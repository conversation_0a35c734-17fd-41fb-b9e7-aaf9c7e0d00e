version: '2.4'

services:
  vue-builder:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - NODE_OPTIONS=--max-old-space-size=6144 # 把 6 GB 真正给到 V8
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
      - SPUG_RELEASE_1=${SPUG_RELEASE_1} # 传入版本号用于base path
    mem_limit: 6g # ← 容器物理内存
    memswap_limit: 6g # ← Swap，同 mem_limit 可避免 OOM
    command: >
      /bin/sh -c "
        set -ex
        # 安装git工具
        apk add --no-cache git
        
        # 检查环境变量
        if [ -z \"$$SPUG_RELEASE_1\" ]; then
          echo 'Error: SPUG_RELEASE_1 environment variable is required'
          exit 1
        fi
        echo \"Building from git tag: $$SPUG_RELEASE_1\"
        
        # 配置git
        git config --global user.name \"Builder\"
        git config --global user.email \"<EMAIL>\"
        
        # 获取最新的tags并切换到指定版本
        git fetch --tags
        git checkout tags/$$SPUG_RELEASE_1
        
        npm install -g pnpm@^9.0.0
        pnpm --version
        echo 'Starting pnpm install...'
        pnpm install --registry=https://registry.npmmirror.com
        
        # 🔍 构建前环境变量确认
        echo '=== 构建环境确认 ==='
        echo \"SPUG_RELEASE_1: $$SPUG_RELEASE_1\"
        echo \"Expected output directory: dist_$$SPUG_RELEASE_1\"
        echo \"Current working directory: \$(pwd)\"
        echo '======================='
        
        echo 'Starting build...'
        # 显式传递环境变量到构建命令（双重保险）
        SPUG_RELEASE_1=\"$$SPUG_RELEASE_1\" pnpm build
        
        # 🎯 构建后验证
        echo '=== 构建结果验证 ==='
        if [ -d \"dist_$$SPUG_RELEASE_1\" ]; then
          echo \"✅ 版本化目录构建成功: dist_$$SPUG_RELEASE_1\"
          ls -la \"dist_$$SPUG_RELEASE_1/\"
        else
          echo \"❌ 版本化目录未找到: dist_$$SPUG_RELEASE_1\"
          echo \"当前目录内容:\"
          ls -la dist*/
          exit 1
        fi
        echo '==================='
      "
    restart: 'no'

  vue-builder-stage:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - NODE_OPTIONS=--max-old-space-size=6144
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
      - SPUG_RELEASE_1=${SPUG_RELEASE_1} # 传入版本号用于base path
    mem_limit: 6g
    memswap_limit: 6g
    command: >
      /bin/sh -c "
        set -ex
        # 安装git工具
        apk add --no-cache git
        
        # 检查环境变量
        if [ -z \"$$SPUG_RELEASE_1\" ]; then
          echo 'Error: SPUG_RELEASE_1 environment variable is required'
          exit 1
        fi
        echo \"Building from git tag: $$SPUG_RELEASE_1\"
        
        # 配置git
        git config --global user.name \"Builder\"
        git config --global user.email \"<EMAIL>\"
        
        # 获取最新的tags并切换到指定版本
        git fetch --tags
        git checkout tags/$$SPUG_RELEASE_1
        
        npm install -g pnpm@^9.0.0
        pnpm --version
        echo 'Starting pnpm install...'
        pnpm install --registry=https://registry.npmmirror.com
        
        # 🔍 构建前环境变量确认
        echo '=== Stage构建环境确认 ==='
        echo \"SPUG_RELEASE_1: $$SPUG_RELEASE_1\"
        echo \"Expected output directory: dist_$$SPUG_RELEASE_1\"
        echo \"Current working directory: \$(pwd)\"
        echo '========================='
        
        echo 'Starting build (stage)...'
        # 显式传递环境变量到构建命令（双重保险）
        SPUG_RELEASE_1=\"$$SPUG_RELEASE_1\" pnpm run build:stage
        
        # 🎯 构建后验证
        echo '=== Stage构建结果验证 ==='
        if [ -d \"dist_$$SPUG_RELEASE_1\" ]; then
          echo \"✅ 版本化目录构建成功: dist_$$SPUG_RELEASE_1\"
          ls -la \"dist_$$SPUG_RELEASE_1/\"
        else
          echo \"❌ 版本化目录未找到: dist_$$SPUG_RELEASE_1\"
          echo \"当前目录内容:\"
          ls -la dist*/
          exit 1
        fi
        echo '======================'
      "
    restart: 'no'

  vue-builder-prod:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - NODE_OPTIONS=--max-old-space-size=6144
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
      - SPUG_RELEASE_1=${SPUG_RELEASE_1} # 传入版本号用于base path
    mem_limit: 6g
    memswap_limit: 6g
    command: >
      /bin/sh -c "
        set -ex
        # 安装git工具
        apk add --no-cache git
        
        # 检查环境变量
        if [ -z \"$$SPUG_RELEASE_1\" ]; then
          echo 'Error: SPUG_RELEASE_1 environment variable is required'
          exit 1
        fi
        echo \"Building from git tag: $$SPUG_RELEASE_1\"
        
        # 配置git
        git config --global user.name \"Builder\"
        git config --global user.email \"<EMAIL>\"
        
        # 获取最新的tags并切换到指定版本
        git fetch --tags
        git checkout tags/$$SPUG_RELEASE_1
        
        npm install -g pnpm@^9.0.0
        pnpm --version
        echo 'Starting pnpm install...'
        pnpm install --registry=https://registry.npmmirror.com
        
        # 🔍 构建前环境变量确认
        echo '=== Prod构建环境确认 ==='
        echo \"SPUG_RELEASE_1: $$SPUG_RELEASE_1\"
        echo \"Expected output directory: dist_$$SPUG_RELEASE_1\"
        echo \"Current working directory: \$(pwd)\"
        echo '========================='
        
        echo 'Starting build (production)...'
        # 显式传递环境变量到构建命令（双重保险）
        SPUG_RELEASE_1=\"$$SPUG_RELEASE_1\" pnpm run build:prod
        
        # 🎯 构建后验证
        echo '=== Prod构建结果验证 ==='
        if [ -d \"dist_$$SPUG_RELEASE_1\" ]; then
          echo \"✅ 版本化目录构建成功: dist_$$SPUG_RELEASE_1\"
          ls -la \"dist_$$SPUG_RELEASE_1/\"
        else
          echo \"❌ 版本化目录未找到: dist_$$SPUG_RELEASE_1\"
          echo \"当前目录内容:\"
          ls -la dist*/
          exit 1
        fi
        echo '======================'
      "
    restart: 'no'

  pad-builder:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - NODE_OPTIONS=--max-old-space-size=6144
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
      - SPUG_RELEASE_1=${SPUG_RELEASE_1} # 传入版本号用于base path
    mem_limit: 6g
    memswap_limit: 6g
    command: >
      /bin/sh -c "
        set -ex
        # 安装git工具
        apk add --no-cache git
        
        # 检查环境变量
        if [ -z \"$$SPUG_RELEASE_1\" ]; then
          echo 'Error: SPUG_RELEASE_1 environment variable is required'
          exit 1
        fi
        echo \"Building from git tag: $$SPUG_RELEASE_1\"
        
        # 配置git
        git config --global user.name \"Builder\"
        git config --global user.email \"<EMAIL>\"
        
        # 获取最新的tags并切换到指定版本
        git fetch --tags
        git checkout tags/$$SPUG_RELEASE_1
        
        npm install -g pnpm@^9.0.0
        pnpm --version
        echo 'Starting pnpm install...'
        pnpm install --registry=https://registry.npmmirror.com
        
        # 🔍 构建前环境变量确认
        echo '=== Pad构建环境确认 ==='
        echo \"SPUG_RELEASE_1: $$SPUG_RELEASE_1\"
        echo \"Expected output directory: dist-client-pad_$$SPUG_RELEASE_1\"
        echo \"Current working directory: \$(pwd)\"
        echo '======================='
        
        echo 'Starting pad build (production)...'
        # 显式传递环境变量到构建命令（双重保险）
        SPUG_RELEASE_1=\"$$SPUG_RELEASE_1\" pnpm run build:client-pad
        
        # 🎯 构建后验证
        echo '=== Pad构建结果验证 ==='
        if [ -d \"dist-client-pad_$$SPUG_RELEASE_1\" ]; then
          echo \"✅ Pad版本化目录构建成功: dist-client-pad_$$SPUG_RELEASE_1\"
          ls -la \"dist-client-pad_$$SPUG_RELEASE_1/\"
        else
          echo \"❌ Pad版本化目录未找到: dist-client-pad_$$SPUG_RELEASE_1\"
          echo \"当前目录内容:\"
          ls -la dist*/
          exit 1
        fi
        echo '==================='
      "
    restart: 'no'

  pad-builder-stage:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - NODE_OPTIONS=--max-old-space-size=6144 # 提升内存限制支持大型构建
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
      - SPUG_RELEASE_1=${SPUG_RELEASE_1} # 传入版本号用于base path
    mem_limit: 6g
    memswap_limit: 6g
    command: >
      /bin/sh -c "
        set -ex
        # 安装git工具
        apk add --no-cache git
        
        # 检查环境变量
        if [ -z \"$$SPUG_RELEASE_1\" ]; then
          echo 'Error: SPUG_RELEASE_1 environment variable is required'
          exit 1
        fi
        echo \"Building from git tag: $$SPUG_RELEASE_1\"
        
        # 配置git
        git config --global user.name \"Builder\"
        git config --global user.email \"<EMAIL>\"
        
        # 获取最新的tags并切换到指定版本
        git fetch --tags
        git checkout tags/$$SPUG_RELEASE_1
        
        npm install -g pnpm@^9.0.0
        pnpm --version
        echo 'Starting pnpm install...'
        pnpm install --registry=https://registry.npmmirror.com
        
        # 🔍 构建前环境变量确认
        echo '=== Pad Stage构建环境确认 ==='
        echo \"SPUG_RELEASE_1: $$SPUG_RELEASE_1\"
        echo \"Expected output directory: dist-client-pad_$$SPUG_RELEASE_1\"
        echo \"Current working directory: \$(pwd)\"
        echo '============================'
        
        echo 'Starting pad build (stage) with performance optimizations...'
        # 显式传递环境变量到构建命令（双重保险）
        SPUG_RELEASE_1=\"$$SPUG_RELEASE_1\" pnpm run build:client-pad:stage
        
        # 🎯 构建后验证
        echo '=== Pad Stage构建结果验证 ==='
        if [ -d \"dist-client-pad_$$SPUG_RELEASE_1\" ]; then
          echo \"✅ Pad Stage版本化目录构建成功: dist-client-pad_$$SPUG_RELEASE_1\"
          echo 'Build completed! Output: dist-client-pad_$$SPUG_RELEASE_1/client-pad.html'
          ls -la \"dist-client-pad_$$SPUG_RELEASE_1/\"
        else
          echo \"❌ Pad Stage版本化目录未找到: dist-client-pad_$$SPUG_RELEASE_1\"
          echo \"当前目录内容:\"
          ls -la dist*/
          exit 1
        fi
        echo '=========================='
      "
    restart: 'no'

  pad-builder-prod:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - NODE_OPTIONS=--max-old-space-size=6144 # 提升内存限制支持大型构建
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
      - SPUG_RELEASE_1=${SPUG_RELEASE_1} # 传入版本号用于base path
    mem_limit: 6g
    memswap_limit: 6g
    command: >
      /bin/sh -c "
        set -ex
        # 安装git工具
        apk add --no-cache git
        
        # 检查环境变量
        if [ -z \"$$SPUG_RELEASE_1\" ]; then
          echo 'Error: SPUG_RELEASE_1 environment variable is required'
          exit 1
        fi
        echo \"Building from git tag: $$SPUG_RELEASE_1\"
        
        # 配置git
        git config --global user.name \"Builder\"
        git config --global user.email \"<EMAIL>\"
        
        # 获取最新的tags并切换到指定版本
        git fetch --tags
        git checkout tags/$$SPUG_RELEASE_1
        
        npm install -g pnpm@^9.0.0
        pnpm --version
        echo 'Starting pnpm install...'
        pnpm install --registry=https://registry.npmmirror.com
        
        # 🔍 构建前环境变量确认
        echo '=== Pad Prod构建环境确认 ==='
        echo \"SPUG_RELEASE_1: $$SPUG_RELEASE_1\"
        echo \"Expected output directory: dist-client-pad_$$SPUG_RELEASE_1\"
        echo \"Current working directory: \$(pwd)\"
        echo '============================'
        
        echo 'Starting pad build (production) with full optimizations...'
        # 显式传递环境变量到构建命令（双重保险）
        SPUG_RELEASE_1=\"$$SPUG_RELEASE_1\" pnpm run build:client-pad:prod
        
        # 🎯 构建后验证
        echo '=== Pad Prod构建结果验证 ==='
        if [ -d \"dist-client-pad_$$SPUG_RELEASE_1\" ]; then
          echo \"✅ Pad Prod版本化目录构建成功: dist-client-pad_$$SPUG_RELEASE_1\"
          echo 'Build completed! Output: dist-client-pad_$$SPUG_RELEASE_1/client-pad.html'
          ls -la \"dist-client-pad_$$SPUG_RELEASE_1/\"
        else
          echo \"❌ Pad Prod版本化目录未找到: dist-client-pad_$$SPUG_RELEASE_1\"
          echo \"当前目录内容:\"
          ls -la dist*/
          exit 1
        fi
        echo '=========================='
      "
    restart: 'no'
