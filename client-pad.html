<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#1976d2" />
    <meta name="description" content="Thunder ERP Client Pad" />
    <title>Thunder ERP Client Pad</title>
    <script>
      // 确保基本路径正确
      window.BASE_URL = '/';

      // 禁用浏览器缓存(开发模式)
      if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
        document.write('<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />');
        document.write('<meta http-equiv="Pragma" content="no-cache" />');
        document.write('<meta http-equiv="Expires" content="0" />');
      }

      // 解决模块加载问题
      window.addEventListener(
        'error',
        function (e) {
          if (e.target && e.target.tagName === 'SCRIPT' && e.target.src && e.target.src.includes('chunk-')) {
            console.warn('[Pad Fix] 尝试修复模块加载:', e.target.src);
            e.preventDefault();
          }
        },
        true
      );
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/apps/client-pad/main.ts"></script>
  </body>
</html>
