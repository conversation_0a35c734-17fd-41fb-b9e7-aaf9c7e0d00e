#!/bin/bash
# 简单的macOS预览版构建脚本
echo "===== 开始构建Thunder ERP macOS预览版应用 ====="

# 设置错误处理
set -e

# 设置环境变量，强制使用系统which命令
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
echo "系统which命令路径: $(which which)"

mkdir -p dist

# 删除可能存在的preview-build文件，避免混淆
echo "删除旧的预览版标记文件..."
rm -f .preview-build

# 手动创建.preview-build标记文件
echo "创建新的预览版标记文件..."
echo "This is a preview build" > .preview-build
echo "预览版标记文件已创建: $(pwd)/.preview-build"
ls -la .preview-build

# 创建临时脚本替代electron-builder内部可能使用的which命令
mkdir -p ./node_modules/.bin/
cat > ./node_modules/.bin/which << 'EOF'
#!/bin/bash
# 内部which替代脚本 - 使用系统which命令
/usr/bin/which "$@"
EOF
chmod +x ./node_modules/.bin/which

# 确保python可用
if ! /usr/bin/which python &>/dev/null && ! /usr/bin/which python3 &>/dev/null; then
  echo "警告: 未找到python，正在创建python3到python的软链接..."
  # 创建python软链接到python3
  if [ -f "/usr/bin/python3" ]; then
    sudo ln -sf /usr/bin/python3 /usr/local/bin/python
  elif [ -f "/usr/local/bin/python3" ]; then
    sudo ln -sf /usr/local/bin/python3 /usr/local/bin/python
  fi
else
  echo "Python已可用: $(/usr/bin/which python || /usr/bin/which python3)"
fi

# 安装依赖
if [ ! -d "node_modules" ]; then
  echo "正在安装依赖..."
  npm install
fi

# 先构建preload脚本
echo "构建preload脚本..."
NODE_ENV=production ./node_modules/.bin/webpack --config webpack.config.js
echo "preload构建完成"

# 直接运行electron-builder命令，不通过npm脚本
echo "正在构建预览版..."
# 不再使用环境变量APP_ENV，而是依赖.preview-build文件
./node_modules/.bin/electron-builder --mac --config.productName="Thunder ERP Preview" || {
  echo "构建失败，尝试使用备用命令..."
  # 备用方案，尝试使用系统安装的electron-builder
  npx electron-builder --mac --config.productName="Thunder ERP Preview"
}

# 检查构建结果
if [ -d "dist" ] && [ "$(ls -A dist)" ]; then
  echo "===== 构建成功！查看dist目录 ====="
  ls -la dist
else
  echo "===== 构建可能失败，dist目录为空 ====="
  exit 1
fi 