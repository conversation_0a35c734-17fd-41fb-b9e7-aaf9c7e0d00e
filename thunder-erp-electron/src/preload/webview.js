/**
 * WebView接口模块
 * 处理WebView相关的功能，如重新加载和应用退出
 */
const { contextBridge, ipcRenderer } = require('electron')

/**
 * 初始化WebView桥接接口
 */
function initWebViewBridge() {
  contextBridge.exposeInMainWorld('webViewBridge', {
    // 重新加载WebView
    reloadWebView: async () => {
      // 获取设备信息用于URL参数
      const deviceId = await ipcRenderer.invoke('get-device-id')
      const ipAddress = await ipcRenderer.invoke('get-ip-address')
      const macAddress = await ipcRenderer.invoke('get-mac-address')

      // 构建完整URL
      const baseUrl = window.location.origin + window.location.pathname
      const url = `${baseUrl}?deviceId=${deviceId}&ipAddress=${ipAddress}&macAddress=${macAddress}`

      // 加载URL
      window.location.href = url
    },

    // 退出应用程序
    exitApp: () => {
      ipcRenderer.invoke('exit-app')
    },

    // 重启应用程序
    restartApp: () => {
      ipcRenderer.invoke('restart-app')
    }
  })

  console.log('WebView接口初始化完成')
}

module.exports = {
  initWebViewBridge
} 