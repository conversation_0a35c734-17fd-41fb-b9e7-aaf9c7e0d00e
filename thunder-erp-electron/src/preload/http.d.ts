/**
 * HTTP桥接接口类型定义
 */

declare global {
  interface Window {
    HttpBridge: HttpBridgeInterface
  }
}

/**
 * HTTP请求对象接口
 */
export interface HttpRequest {
  /** 请求ID */
  id: string
  /** HTTP方法 */
  method: string
  /** 完整URL */
  url: string
  /** 路径名 */
  pathname: string
  /** 查询字符串 */
  search: string
  /** 查询参数对象 */
  query: Record<string, string>
  /** 请求头 */
  headers: Record<string, string>
  /** 请求体 */
  body: any
  /** 请求时间戳 */
  timestamp: number
}

/**
 * HTTP响应对象接口
 */
export interface HttpResponse {
  /** 设置状态码 */
  status(code: number): HttpResponse
  
  /** 设置单个响应头 */
  header(name: string, value: string): HttpResponse
  
  /** 设置多个响应头 */
  headers(obj: Record<string, string>): HttpResponse
  
  /** 发送JSON响应 */
  json(data: any): HttpResponse
  
  /** 发送文本响应 */
  text(data: string): HttpResponse
  
  /** 发送HTML响应 */
  html(data: string): HttpResponse
  
  /** 发送通用响应 */
  send(data: any): HttpResponse
  
  /** 发送错误响应 */
  error(statusCode: number, message: string, details?: any): HttpResponse
}

/**
 * 路由处理器函数类型
 */
export type RouteHandler = (request: HttpRequest, response: HttpResponse) => Promise<void> | void

/**
 * 中间件函数类型
 */
export type Middleware = (
  request: HttpRequest, 
  response: HttpResponse, 
  next: () => Promise<void>
) => Promise<void> | void

/**
 * HTTP桥接主接口
 */
export interface HttpBridgeInterface {
  /**
   * 注册路由处理器
   * @param method HTTP方法
   * @param path 路径
   * @param handler 处理器函数
   */
  route(method: string, path: string, handler: RouteHandler): void
  
  /**
   * 注册GET路由
   * @param path 路径
   * @param handler 处理器函数
   */
  get(path: string, handler: RouteHandler): void
  
  /**
   * 注册POST路由
   * @param path 路径
   * @param handler 处理器函数
   */
  post(path: string, handler: RouteHandler): void
  
  /**
   * 注册PUT路由
   * @param path 路径
   * @param handler 处理器函数
   */
  put(path: string, handler: RouteHandler): void
  
  /**
   * 注册DELETE路由
   * @param path 路径
   * @param handler 处理器函数
   */
  delete(path: string, handler: RouteHandler): void
  
  /**
   * 注册PATCH路由
   * @param path 路径
   * @param handler 处理器函数
   */
  patch(path: string, handler: RouteHandler): void
  
  /**
   * 注册所有HTTP方法的路由
   * @param path 路径
   * @param handler 处理器函数
   */
  all(path: string, handler: RouteHandler): void
  
  /**
   * 设置默认处理器（404处理器）
   * @param handler 处理器函数
   */
  setDefaultHandler(handler: RouteHandler): void
  
  /**
   * 添加中间件
   * @param middleware 中间件函数
   */
  use(middleware: Middleware): void
  
  /**
   * 移除路由
   * @param method HTTP方法
   * @param path 路径
   * @returns 是否成功移除
   */
  removeRoute(method: string, path: string): boolean
  
  /**
   * 清空所有路由
   */
  clearRoutes(): void
  
  /**
   * 清空所有中间件
   */
  clearMiddlewares(): void
  
  /**
   * 获取已注册的路由列表
   * @returns 路由键数组
   */
  getRoutes(): string[]
}

export default HttpBridgeInterface 