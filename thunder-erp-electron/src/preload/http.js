const { contextBridge, ipc<PERSON><PERSON>er } = require('electron')

/**
 * HTTP桥接模块
 * 
 * 功能职责：
 * - 接收主进程转发的HTTP请求
 * - 管理路由处理器
 * - 发送响应回主进程
 */

// 路由处理器存储
const routeHandlers = new Map()

// 中间件存储
const middlewares = []

// 默认处理器
let defaultHandler = null

/**
 * 路由匹配器
 */
function matchRoute(pathname, method) {
  // 精确匹配
  const exactKey = `${method.toUpperCase()}:${pathname}`
  if (routeHandlers.has(exactKey)) {
    return routeHandlers.get(exactKey)
  }
  
  // 通配符匹配
  const wildcardKey = `${method.toUpperCase()}:*`
  if (routeHandlers.has(wildcardKey)) {
    return routeHandlers.get(wildcardKey)
  }
  
  // 所有方法通配符
  const allMethodKey = `*:${pathname}`
  if (routeHandlers.has(allMethodKey)) {
    return routeHandlers.get(allMethodKey)
  }
  
  // 完全通配符
  if (routeHandlers.has('*:*')) {
    return routeHandlers.get('*:*')
  }
  
  return null
}

/**
 * 执行中间件
 */
async function executeMiddlewares(request, response, next) {
  let index = 0
  
  async function dispatch() {
    if (index >= middlewares.length) {
      return next()
    }
    
    const middleware = middlewares[index++]
    try {
      await middleware(request, response, dispatch)
    } catch (error) {
      response.status(500).json({
        error: 'Middleware error',
        message: error.message
      })
    }
  }
  
  return dispatch()
}

/**
 * 创建响应对象
 */
function createResponse(requestId) {
  const response = {
    _requestId: requestId,
    _statusCode: 200,
    _headers: {},
    _body: null,
    _sent: false,
    
    // 设置状态码
    status(code) {
      this._statusCode = code
      return this
    },
    
    // 设置头信息
    header(name, value) {
      this._headers[name] = value
      return this
    },
    
    // 设置多个头信息
    headers(obj) {
      Object.assign(this._headers, obj)
      return this
    },
    
    // 发送JSON响应
    json(data) {
      this._headers['Content-Type'] = 'application/json'
      this._body = data
      this._send()
      return this
    },
    
    // 发送文本响应
    text(data) {
      this._headers['Content-Type'] = 'text/plain'
      this._body = data
      this._send()
      return this
    },
    
    // 发送HTML响应
    html(data) {
      this._headers['Content-Type'] = 'text/html'
      this._body = data
      this._send()
      return this
    },
    
    // 发送响应
    send(data) {
      this._body = data
      this._send()
      return this
    },
    
    // 发送错误响应
    error(statusCode, message, details = {}) {
      this._statusCode = statusCode
      this._headers['Content-Type'] = 'application/json'
      this._body = {
        error: message,
        code: statusCode,
        ...details
      }
      this._send()
      return this
    },
    
    // 内部发送方法
    _send() {
      if (this._sent) {
        console.warn('[HTTP-BRIDGE] 响应已发送，忽略重复发送')
        return
      }
      
      this._sent = true
      
      // 发送响应到主进程
      ipcRenderer.invoke('http-response', {
        requestId: this._requestId,
        statusCode: this._statusCode,
        headers: this._headers,
        body: this._body
      })
    }
  }
  
  return response
}

/**
 * 处理HTTP请求
 */
async function handleHttpRequest(event, request) {
  const { id: requestId, method, pathname } = request
  
  console.log(`[HTTP-BRIDGE] 收到请求: ${method} ${pathname} [ID: ${requestId}]`)
  
  try {
    const response = createResponse(requestId)
    
    // 查找路由处理器
    const handler = matchRoute(pathname, method) || defaultHandler
    
    if (!handler) {
      response.error(404, 'Route not found', {
        path: pathname,
        method: method
      })
      return
    }
    
    // 执行中间件和处理器
    await executeMiddlewares(request, response, async () => {
      try {
        await handler(request, response)
        
        // 如果处理器没有发送响应，自动发送默认响应
        if (!response._sent) {
          response.status(200).text('OK')
        }
      } catch (error) {
        console.error(`[HTTP-BRIDGE] 处理器执行失败: ${requestId}`, error)
        if (!response._sent) {
          response.error(500, 'Handler error', {
            message: error.message,
            stack: error.stack
          })
        }
      }
    })
    
  } catch (error) {
    console.error(`[HTTP-BRIDGE] 请求处理失败: ${requestId}`, error)
    
    // 发送错误响应
    ipcRenderer.invoke('http-response', {
      requestId: requestId,
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: {
        error: 'Internal server error',
        code: 500,
        message: error.message
      }
    })
  }
}

/**
 * 初始化HTTP桥接
 */
function initHttpBridge() {
  // 监听主进程的HTTP请求
  ipcRenderer.on('http-request', handleHttpRequest)
  
  // 暴露HTTP API到渲染进程
  contextBridge.exposeInMainWorld('HttpBridge', {
    // 注册路由处理器
    route(method, path, handler) {
      const key = `${method.toUpperCase()}:${path}`
      routeHandlers.set(key, handler)
      console.log(`[HTTP-BRIDGE] 注册路由: ${key}`)
    },
    
    // 注册GET路由
    get(path, handler) {
      this.route('GET', path, handler)
    },
    
    // 注册POST路由
    post(path, handler) {
      this.route('POST', path, handler)
    },
    
    // 注册PUT路由
    put(path, handler) {
      this.route('PUT', path, handler)
    },
    
    // 注册DELETE路由
    delete(path, handler) {
      this.route('DELETE', path, handler)
    },
    
    // 注册PATCH路由
    patch(path, handler) {
      this.route('PATCH', path, handler)
    },
    
    // 注册所有方法的路由
    all(path, handler) {
      this.route('*', path, handler)
    },
    
    // 设置默认处理器
    setDefaultHandler(handler) {
      defaultHandler = handler
      console.log('[HTTP-BRIDGE] 设置默认处理器')
    },
    
    // 添加中间件
    use(middleware) {
      middlewares.push(middleware)
      console.log('[HTTP-BRIDGE] 添加中间件')
    },
    
    // 移除路由
    removeRoute(method, path) {
      const key = `${method.toUpperCase()}:${path}`
      const removed = routeHandlers.delete(key)
      if (removed) {
        console.log(`[HTTP-BRIDGE] 移除路由: ${key}`)
      }
      return removed
    },
    
    // 清空所有路由
    clearRoutes() {
      routeHandlers.clear()
      console.log('[HTTP-BRIDGE] 清空所有路由')
    },
    
    // 清空所有中间件
    clearMiddlewares() {
      middlewares.length = 0
      console.log('[HTTP-BRIDGE] 清空所有中间件')
    },
    
    // 获取路由列表
    getRoutes() {
      return Array.from(routeHandlers.keys())
    }
  })
  
  console.log('[HTTP-BRIDGE] HTTP桥接初始化完成')
}

module.exports = {
  initHttpBridge
} 