/**
 * 应用升级接口模块
 * 处理应用更新检查和升级相关功能
 */
const { contextBridge, ipcRender<PERSON> } = require('electron')

/**
 * 初始化升级桥接接口
 */
function initUpgradeBridge() {
  // 升级接口
  contextBridge.exposeInMainWorld('appUpgradeBridge', {
    // 检查更新
    checkUpgrade: (callbackName) => {
      ipcRenderer.invoke('check-upgrade')
        .then(result => {
          if (typeof window[callbackName] === 'function') {
            window[callbackName](result)
          }
        })
    },

    // 执行升级
    upgrade: (callbackName) => {
      ipcRenderer.invoke('start-upgrade')
        .then(result => {
          if (typeof window[callbackName] === 'function') {
            window[callbackName](result)
          }
        })
    }
  })

  // 监听升级事件
  ipcRenderer.on('upgrade-event', (event, data) => {
    if (typeof window.appUpgradeEvent === 'function') {
      window.appUpgradeEvent(data.event, data.data)
    }
  })

  console.log('升级接口初始化完成')
}

module.exports = {
  initUpgradeBridge
} 