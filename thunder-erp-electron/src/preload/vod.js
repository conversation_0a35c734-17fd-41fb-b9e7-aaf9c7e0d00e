/**
 * VOD接口模块
 * 处理视频点播相关的功能
 */
const { contextBridge, ipcRenderer } = require('electron')

// 存储回调函数
let vodCallback = undefined

/**
 * 初始化VOD桥接接口
 */
function initVodBridge() {
  // VOD接口 - 直接在这里实现，确保可用性
  contextBridge.exposeInMainWorld('vodBridge', {
    // 发送请求
    request: (method, url, params, body, callbackId) => {
      console.log(`VOD请求: ${method} ${url}?${params}, callbackId: ${callbackId}`)

      // 调用主进程处理请求，传递callbackId参数
      // 主进程会处理回调，通过vod-response事件返回结果
      ipcRenderer.invoke('vod-request', { method, url, params, body, callbackId })
        .catch(error => {
          // 只处理invoke本身的错误，不处理VOD请求的错误（由主进程处理）
          console.error('调用vod-request失败:', error)
        })
    },

    // 注册回调函数 - 这是解决上下文隔离问题的关键
    registerCallback: (callbackFn) => {
      console.log('注册VOD回调函数')
      vodCallback = callbackFn
      return true
    }
  })

  // 监听VOD响应事件
  ipcRenderer.on('vod-response', (event, { callbackId, responseText }) => {
    console.log(`vod-response, callbackId:${callbackId}, responseText:${responseText}`)

    // 首先尝试使用注册的回调函数
    if (vodCallback) {
      console.log('使用注册的回调函数执行回调')
      try {
        vodCallback(callbackId, responseText)
        return
      } catch (err) {
        console.error('执行注册的回调函数失败:', err)
      }
    } else {
      console.warn('未注册回调函数')
    }
  })

  console.log('VOD接口初始化完成')
}

module.exports = {
  initVodBridge
} 