const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')
const { initPrintBridge } = require('./print')
const { initVodBridge } = require('./vod')
const { initWebViewBridge } = require('./webview')
const { initUpgradeBridge } = require('./upgrade')
const { initHttpBridge } = require('./http')

// 直接尝试导入接口模块
try {
  // 设备信息接口
  contextBridge.exposeInMainWorld('deviceInfo', {
    getDeviceId: () => ipcRenderer.invoke('get-device-id'),
    getIpAddress: () => ipcRenderer.invoke('get-ip-address'),
    getMacAddress: () => ipcRenderer.invoke('get-mac-address'),
    getPlatformInfo: () => ipcRenderer.invoke('get-platform-info')
  })

  // AndroidPrinter接口 - 打印机功能模块
  initPrintBridge()

  // WebView接口
  initWebViewBridge()

  // VOD接口
  initVodBridge()

  // 升级接口
  initUpgradeBridge()

  // HTTP桥接接口
  initHttpBridge()

  // JS桥接接口
  contextBridge.exposeInMainWorld('JSBridge', {
    // 执行JavaScript代码
    evaluateJavascript: (script) => {
      try {
        return eval(script)
      } catch (e) {
        console.error('执行JavaScript失败:', e)
        return null
      }
    },

    // 调用JavaScript方法
    callJsMethod: (methodName, ...args) => {
      if (typeof window[methodName] === 'function') {
        try {
          return window[methodName](...args)
        } catch (e) {
          console.error(`调用 ${methodName} 方法失败:`, e)
          return null
        }
      } else {
        console.warn(`${methodName} 不是一个函数`)
        return null
      }
    }
  })

  // 日志管理器接口
  contextBridge.exposeInMainWorld('Logger', {
    // 获取日志
    getLogs: (filter = {}) => {
      return ipcRenderer.invoke('logger-get-logs', filter)
    },

    // 清空日志
    clearLogs: () => {
      return ipcRenderer.invoke('logger-clear-logs')
    },

    // 获取日志统计信息
    getStats: () => {
      return ipcRenderer.invoke('logger-get-stats')
    },

    // 设置日志级别
    setLogLevel: (level) => {
      return ipcRenderer.invoke('logger-set-level', level)
    },

    // 导出日志
    exportLogs: () => {
      return ipcRenderer.invoke('logger-export-logs')
    }
  })

  console.log('预加载脚本初始化完成')
} catch (error) {
  console.error('预加载脚本初始化失败:', error)
}