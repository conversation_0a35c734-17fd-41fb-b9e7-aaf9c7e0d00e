// 打印机功能模块
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

const PRINT_SCANNER_METHODS = {
  SCAN_PRINTERS: 'scan-printers'
}

const PRINT_TASK_METHODS = {
  RAW_PRINT: 'raw-print',
  PRINT_TEST: 'print-test',
  GET_QUEUE_STATUS: 'get-print-queue-status',
  GET_TASKS: 'get-print-tasks',
  RETRY_TASK: 'retry-print-task',
  RETRY_ALL_FAILED: 'retry-all-failed-print-tasks',
  CLEAR_QUEUE: 'clear-print-queue',
  OPEN_MONITOR: 'open-print-queue-monitor'
}

const DEVELOPMENT_MODE = 'development'

// 参数验证函数
const validatePrintParams = (params) => {
  const { ip, port, commandsJson } = params
  return ip && port && commandsJson
}

// 打印参数验证
const logPrintParamsError = (params) => {
  const { ip, port, commandsJson } = params
  console.error('打印参数无效:', { 
    ip, 
    port, 
    commandsJson: commandsJson ? '有数据' : '无数据' 
  })
}

// 执行打印任务
const executePrintTask = async (ipcRenderer, method, params) => {
  try {
    const result = await ipcRenderer.invoke(method, params)
    
    if (result && !result.success) {
      console.error('打印失败:', result.error)
      return false
    }
    
    return result || true
  } catch (error) {
    console.error('打印过程中发生错误:', error)
    return false
  }
}

// 检查开发模式
const isDevelopmentMode = () => {
  return process.env.NODE_ENV === DEVELOPMENT_MODE
}

// 处理开发模式功能
const handleDevelopmentFeature = (ipcRenderer, featureName, invokeMethod) => {
  if (isDevelopmentMode()) {
    return ipcRenderer.invoke(invokeMethod)
  } else {
    console.log(`${featureName}功能仅在开发模式下可用`)
    return Promise.resolve(false)
  }
}

// 扫描打印机
const scanPrinters = (ipcRenderer) => {
  return ipcRenderer.invoke(PRINT_SCANNER_METHODS.SCAN_PRINTERS)
}

// 原始打印 - 使用队列模式
const rawPrint = async (ipcRenderer, ip, port, commandsJson) => {
  const params = { ip, port, commandsJson }
  
  if (!validatePrintParams(params)) {
    logPrintParamsError(params)
    return false
  }

  return await executePrintTask(ipcRenderer, PRINT_TASK_METHODS.RAW_PRINT, params)
}

// 测试打印 - 使用队列模式
const printTest = (ipcRenderer, ip, port, testContent) => {
  return ipcRenderer.invoke(PRINT_TASK_METHODS.PRINT_TEST, { ip, port, testContent })
}

// 获取打印队列状态
const getPrintQueueStatus = (ipcRenderer) => {
  return ipcRenderer.invoke(PRINT_TASK_METHODS.GET_QUEUE_STATUS)
}

// 获取所有打印任务
const getPrintTasks = (ipcRenderer) => {
  return ipcRenderer.invoke(PRINT_TASK_METHODS.GET_TASKS)
}

// 重试失败的打印任务
const retryPrintTask = (ipcRenderer, taskId) => {
  return ipcRenderer.invoke(PRINT_TASK_METHODS.RETRY_TASK, { taskId })
}

// 重试所有失败的打印任务
const retryAllFailedPrintTasks = (ipcRenderer) => {
  return ipcRenderer.invoke(PRINT_TASK_METHODS.RETRY_ALL_FAILED)
}

// 清空打印队列
const clearPrintQueue = (ipcRenderer) => {
  return ipcRenderer.invoke(PRINT_TASK_METHODS.CLEAR_QUEUE)
}

// 打开打印队列监控窗口 (仅开发模式)
const openPrintQueueMonitor = (ipcRenderer) => {
  return handleDevelopmentFeature(ipcRenderer, '打印队列监控', PRINT_TASK_METHODS.OPEN_MONITOR)
}

// 初始化打印机接口
const initPrintBridge = () => {
  try {
    contextBridge.exposeInMainWorld('AndroidPrinter', {
      // 扫描打印机
      scanPrinters: () => {
        return scanPrinters(ipcRenderer)
      },

      // 原始打印 - 使用队列模式
      rawPrint: async (ip, port, commandsJson) => {
        return await rawPrint(ipcRenderer, ip, port, commandsJson)
      },

      // 测试打印 - 使用队列模式
      printTest: (ip, port, testContent) => {
        return printTest(ipcRenderer, ip, port, testContent)
      },

      // 获取打印队列状态
      getPrintQueueStatus: () => {
        return getPrintQueueStatus(ipcRenderer)
      },

      // 获取所有打印任务
      getPrintTasks: () => {
        return getPrintTasks(ipcRenderer)
      },

      // 重试失败的打印任务
      retryPrintTask: (taskId) => {
        return retryPrintTask(ipcRenderer, taskId)
      },

      // 重试所有失败的打印任务
      retryAllFailedPrintTasks: () => {
        return retryAllFailedPrintTasks(ipcRenderer)
      },

      // 清空打印队列
      clearPrintQueue: () => {
        return clearPrintQueue(ipcRenderer)
      },

      // 打开打印队列监控窗口 (仅开发模式)
      openPrintQueueMonitor: () => {
        return openPrintQueueMonitor(ipcRenderer)
      }
    })
    
    console.log('AndroidPrinter 接口已成功注册')
  } catch (error) {
    console.error('注册 AndroidPrinter 接口失败:', error)
  }
}

module.exports = {
  initPrintBridge
}
