/**
 * 核心窗口管理模块
 * 负责创建和管理应用的主要窗口和托盘
 */

const { BrowserWindow, Menu, Tray, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { getVersionInfo } = require('../utils/version');

// 全局窗口引用
let mainWindow = null;
let tray = null;

/**
 * 创建主窗口
 * @param {Object} options 窗口创建选项
 * @param {string} options.clientUrl 客户端URL
 * @param {boolean} options.isPreviewBuild 是否为预览构建
 * @param {Function} options.createLogViewerWindow 创建日志查看器窗口的函数
 * @param {Function} options.createPrintQueueWindow 创建打印队列监控窗口的函数
 * @param {Function} options.openDevTools 开发者工具函数
 * @param {string} options.h5versionCode H5版本号
 * @param {boolean} options.hasUpgrade 是否检测到App升级
 * @param {boolean} options.forceUpgrade 是否强制升级
 * @param {string} options.h5versionName H5版本名称
 * @returns {BrowserWindow} 主窗口实例
 */
function createMainWindow(options) {
  const { clientUrl, isPreviewBuild, h5versionCode = 'latest', hasUpgrade = false, h5versionName = '', forceUpgrade = false } = options;

  // 强制升级处理：若 forceUpgrade 为 true，则显示阻塞对话框提示用户升级
  if (forceUpgrade) {
    dialog.showMessageBoxSync({
      type: 'warning',
      title: '需要更新',
      message: '检测到新的强制版本，请关闭应用并安装更新后继续使用。',
      buttons: ['退出']
    });
    const { app } = require('electron');
    app.quit();
    return null;
  }

  // 创建主窗口
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 720,
    title: isPreviewBuild ? 'Thunder ERP Preview' : 'Thunder ERP',
    fullscreen: true, // 全屏显示
    frame: false, // 移除窗口框架以隐藏菜单栏
    resizable: false, // 禁止调整窗口大小
    minimizable: false, // 禁止最小化
    maximizable: false, // 禁止最大化
    webPreferences: {
      // 使用webpack打包的dist/preload/index.js文件支持沙盒环境
      preload: path.join(__dirname, '../../../dist/preload/index.js'),
      nodeIntegration: false,
      contextIsolation: true,
      // 沙盒模式默认启用，使用webpack打包的文件支持沙盒环境
      webSecurity: false, // 允许跨域请求
      allowRunningInsecureContent: true // 允许加载混合内容
    }
  });

  // 获取设备信息用于URL参数
  const deviceId = getMachineCode();
  const ipAddress = getIpAddress();
  const macAddress = getMacAddress();

  // 获取应用版本信息（使用package.json中的自定义thunderErp字段）
  const { app } = require('electron');
  const { versionCode: appVersionCode, versionName: appVersionName } = getVersionInfo();

  // 开发模式下使用Console调试
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
    console.log('[核心窗口] 设备信息', { deviceId, ipAddress, macAddress, appVersionCode, appVersionName });
  }

  // 加载远程URL并传递设备信息参数（仿照Android MainActivity.java的实现）
  // 根据需求: 路径加入h5versionCode, 其余参数附带
  const basePath = clientUrl.replace(/\/+$|\/+$/g, ''); // remove trailing slash
  const urlWithPath = `${basePath}/${h5versionCode}/auth`;
  const remoteUrl = `${urlWithPath}?deviceId=${deviceId}&macAddress=${macAddress}&ipAddress=${ipAddress}&deviceType=CASHIER_WINDOWS&clientType=CASHIER_WINDOWS&appVersionCode=${appVersionCode}&appVersionName=${encodeURIComponent(appVersionName)}&h5versionCode=${h5versionCode}&h5versionName=${encodeURIComponent(h5versionName)}&hasUpgrade=${hasUpgrade}`;
  console.log('[核心窗口] 加载URL:', remoteUrl);
  mainWindow.loadURL(remoteUrl);

  // 添加事件监听，防止窗口退出全屏状态
  mainWindow.on('leave-full-screen', () => {
    // 如果窗口试图退出全屏，将其设置回全屏状态
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.setFullScreen(true);
    }
  });

  // 窗口关闭时清除引用
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // 创建系统托盘
  createSystemTray(options);

  return mainWindow;
}

/**
 * 创建系统托盘
 * @param {Object} options 配置选项
 */
function createSystemTray(options) {
  const {
    isPreviewBuild,
    createLogViewerWindow,
    createPrintQueueWindow,
    openDevTools,
    getConfiguredUrl,
    saveUrlConfig,
    previewClientUrl,
    productionClientUrl
  } = options;

  // 检查图标文件是否存在
  const iconPath = path.join(__dirname, '../../../build/icon.png');
  const defaultIconPath = path.join(__dirname, '../../assets/logo.png');

  let trayIconPath;

  // 检查图标文件是否存在
  if (fs.existsSync(iconPath)) {
    trayIconPath = iconPath;
  } else if (fs.existsSync(defaultIconPath)) {
    trayIconPath = defaultIconPath;
  } else {
    // 如果找不到图标，使用一个空白图标
    console.warn('[核心窗口] 找不到图标文件，使用默认图标');
    trayIconPath = null;
    // 跳过创建托盘
    return;
  }

  // 创建托盘图标
  try {
    tray = new Tray(trayIconPath);

    // 创建托盘菜单
    const menuTemplate = [
      {
        label: '打开主窗口',
        click: () => {
          if (mainWindow) {
            mainWindow.show();
          } else {
            // 如果主窗口不存在，可以重新创建
            console.warn('[核心窗口] 主窗口不存在，需要重新创建');
          }
        }
      }
    ];

    // 只在预览环境或开发模式下添加环境切换选项
    if (isPreviewBuild || process.env.NODE_ENV === 'development') {
      menuTemplate.push({
        label: '切换环境',
        click: () => {
          const currentUrl = getConfiguredUrl();
          const newUrl = currentUrl === previewClientUrl ? productionClientUrl : previewClientUrl;
          saveUrlConfig(newUrl);

          dialog
            .showMessageBox({
              type: 'info',
              title: '环境已切换',
              message: `环境已切换到: ${newUrl === previewClientUrl ? '预览环境' : '生产环境'}`,
              detail: '请重启应用以应用更改。',
              buttons: ['重启应用', '稍后重启'],
              defaultId: 0
            })
            .then(result => {
              if (result.response === 0) {
                const { app } = require('electron');
                app.relaunch();
                app.exit(0);
              }
            });
        }
      });

      // 添加开发者控制台选项
      if (openDevTools) {
        menuTemplate.push({
          label: '开发者控制台',
          click: openDevTools
        });
      }

      // 添加日志查看器选项
      if (createLogViewerWindow) {
        menuTemplate.push({
          label: '日志查看器',
          click: () => {
            createLogViewerWindow(isPreviewBuild);
          }
        });
      }
    }

    // 只在开发模式下添加打印队列监控选项
    if (process.env.NODE_ENV === 'development' && createPrintQueueWindow) {
      menuTemplate.push({
        label: '打印队列监控 (开发模式)',
        click: createPrintQueueWindow
      });
    }

    // 添加分隔符和退出选项
    menuTemplate.push(
      { type: 'separator' },
      {
        label: '退出',
        click: () => {
          const { app } = require('electron');
          app.quit();
        }
      }
    );

    const contextMenu = Menu.buildFromTemplate(menuTemplate);

    // 设置托盘提示文本
    tray.setToolTip('Thunder ERP');

    // 设置托盘上下文菜单
    tray.setContextMenu(contextMenu);

    // 点击托盘图标时显示主窗口
    tray.on('click', () => {
      if (mainWindow) {
        mainWindow.show();
      }
    });
  } catch (error) {
    console.error('[核心窗口] 创建系统托盘失败:', error);
  }
}

/**
 * 开启开发者控制台
 */
function openDevTools() {
  if (mainWindow && !mainWindow.isDestroyed()) {
    if (mainWindow.webContents.isDevToolsOpened()) {
      mainWindow.webContents.closeDevTools();
      console.log('[核心窗口] 开发者控制台已关闭');
    } else {
      mainWindow.webContents.openDevTools();
      console.log('[核心窗口] 开发者控制台已打开');
    }
  }
}

// ===== 设备信息获取函数 =====

/**
 * 获取IP地址
 * @returns {string} IP地址
 */
function getIpAddress() {
  const nets = os.networkInterfaces();
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // 排除内部接口和非IPv4接口
      if (!net.internal && net.family === 'IPv4') {
        return net.address;
      }
    }
  }
  return '127.0.0.1';
}

/**
 * 获取MAC地址
 * @returns {string} MAC地址
 */
function getMacAddress() {
  try {
    const nets = os.networkInterfaces();
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // 排除内部接口和非IPv4接口
        if (!net.internal && net.family === 'IPv4') {
          return net.mac || '00:00:00:00:00:00';
        }
      }
    }
    return '00:00:00:00:00:00';
  } catch (error) {
    console.error('[核心窗口] 获取MAC地址失败:', error);
    return '00:00:00:00:00:00';
  }
}

/**
 * 获取机器码存储路径
 * @returns {string} 存储路径
 */
function getMachineCodeStoragePath() {
  const { app } = require('electron');
  const userDataPath = app.getPath('userData');
  // 使用不太显眼的文件名
  return path.join(userDataPath, 'system-config.json');
}

/**
 * 生成机器码并存储在本地
 * @returns {string|null} 机器码
 */
function generateMachineCode() {
  try {
    const crypto = require('crypto');

    // 获取设备信息
    const hostname = os.hostname();
    const mac = getMacAddress();
    const cpuInfo = os.cpus()[0]?.model || '';
    const totalMem = os.totalmem();

    console.log('[核心窗口] 设备信息:', { hostname, mac, cpuInfo, totalMem });

    // 组合设备信息生成唯一标识
    const deviceInfo = `${hostname}-${mac}-${cpuInfo}-${totalMem}`;

    // 使用MD5生成32位机器码
    const machineCode = crypto.createHash('md5').update(deviceInfo).digest('hex');

    // 获取存储路径
    const machineCodePath = getMachineCodeStoragePath();

    // 存储机器码到本地文件
    fs.writeFileSync(
      machineCodePath,
      JSON.stringify({
        deviceIdentifier: machineCode,
        updatedAt: new Date().toISOString()
      })
    );

    console.log('[核心窗口] 32位机器码已生成并保存:', machineCode);
    return machineCode;
  } catch (error) {
    console.error('[核心窗口] 生成机器码失败:', error);
    return null;
  }
}

/**
 * 获取存储的机器码
 * @returns {string|null} 机器码
 */
function getMachineCode() {
  try {
    const machineCodePath = getMachineCodeStoragePath();

    if (fs.existsSync(machineCodePath)) {
      const data = JSON.parse(fs.readFileSync(machineCodePath, 'utf8'));
      return data.deviceIdentifier;
    } else {
      // 如果文件不存在，生成新的机器码
      return generateMachineCode();
    }
  } catch (error) {
    console.error('[核心窗口] 获取机器码失败:', error);
    return null;
  }
}

/**
 * 获取主窗口实例
 * @returns {BrowserWindow|null} 主窗口实例
 */
function getMainWindow() {
  return mainWindow;
}

/**
 * 获取托盘实例
 * @returns {Tray|null} 托盘实例
 */
function getTray() {
  return tray;
}

/**
 * 销毁所有窗口和托盘
 */
function destroyAllWindows() {
  if (mainWindow) {
    mainWindow.close();
    mainWindow = null;
  }

  if (tray) {
    tray.destroy();
    tray = null;
  }
}

module.exports = {
  createMainWindow,
  createSystemTray,
  openDevTools,
  getMainWindow,
  getTray,
  destroyAllWindows,
  // 导出设备信息函数供其他模块使用
  getIpAddress,
  getMacAddress,
  getMachineCode,
  generateMachineCode
};
