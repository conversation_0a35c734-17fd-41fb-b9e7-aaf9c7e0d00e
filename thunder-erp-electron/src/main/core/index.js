/**
 * 核心模块统一入口
 * 导出核心IPC和基础功能
 */

const setupIPC = require('./ipc')
const { 
  createMainWindow, 
  createSystemTray, 
  openDevTools,
  getMainWindow,
  getTray,
  destroyAllWindows,
  getIpAddress,
  getMacAddress,
  getMachineCode,
  generateMachineCode
} = require('./windows')

module.exports = {
  // 基础IPC设置函数
  setupIPC,
  
  // 窗口管理
  createMainWindow,
  createSystemTray,
  openDevTools,
  getMainWindow,
  getTray,
  destroyAllWindows,
  
  // 设备信息
  getIpAddress,
  getMacAddress,
  getMachineCode,
  generateMachineCode
} 