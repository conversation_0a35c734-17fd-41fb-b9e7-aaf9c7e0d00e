const os = require('os')
const { app } = require('electron')

module.exports = function setupIPC(ipcMain, mainWindow) {
  // 获取设备 ID（主机名）
  ipcMain.handle('get-device-id', () => {
    return os.hostname()
  })
  
  // 获取 IP 地址
  ipcMain.handle('get-ip-address', () => {
    const nets = os.networkInterfaces()
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // 跳过内部接口和非 IPv4 接口
        if (!net.internal && net.family === 'IPv4') {
          return net.address
        }
      }
    }
    return '127.0.0.1'
  })
  
  // 获取 MAC 地址
  ipcMain.handle('get-mac-address', () => {
    const nets = os.networkInterfaces()
    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // 跳过内部接口
        if (!net.internal) {
          return net.mac
        }
      }
    }
    return '00:00:00:00:00:00'
  })
  
  // 获取平台信息
  ipcMain.handle('get-platform-info', () => {
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      osVersion: os.release(),
      osName: os.type()
    }
  })
  
  // 退出应用
  ipcMain.handle('exit-app', () => {
    app.quit()
  })
} 