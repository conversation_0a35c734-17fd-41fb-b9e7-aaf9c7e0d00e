# Thunder ERP 主进程模块架构

本文档描述了Thunder ERP主进程代码的模块化架构和目录组织。

## 目录结构

```
src/main/
├── index.js            # 主入口文件，应用程序启动和基础功能协调
├── core/               # 核心功能模块
│   ├── index.js        # 核心模块统一入口
│   ├── ipc.js          # 基础IPC处理
│   └── windows.js      # 窗口管理、托盘和设备信息
├── logging/            # 日志管理模块
│   ├── index.js        # 日志模块统一入口
│   ├── logger.js       # 日志管理器主体
│   ├── loggerConfig.js # 日志配置管理
│   └── logViewer.js    # 日志查看器窗口管理
├── printing/           # 打印功能模块
│   ├── index.js        # 打印模块统一入口
│   ├── printer.js      # 打印机管理和IPC处理
│   ├── printQueue.js   # 打印队列管理器
│   ├── printConsumer.js # 打印任务消费者
│   └── printMonitor.js # 打印队列监控窗口管理
├── updater/            # 应用升级模块
│   ├── index.js        # 升级模块统一入口
│   └── updater.js      # 升级功能实现
├── vod/                # VOD视频模块
│   ├── index.js        # VOD模块统一入口
│   └── vod.js          # VOD功能实现
└── http/               # HTTP服务器模块
    ├── index.js        # HTTP模块统一入口
    ├── httpServer.js   # HTTP服务器核心实现
    └── httpIPC.js      # HTTP IPC处理器
```

## 重构改进

### 🎯 单一职责原则

**重构前问题**：
- `index.js` 文件过大（1190行），包含窗口创建、托盘管理、设备信息获取等多种功能
- 功能混杂，违反单一职责原则
- 难以维护和测试

**重构后改进**：
- `index.js` 简化为应用入口和协调器（约300行）
- 各功能模块独立管理自己的窗口和功能
- 每个文件职责明确，便于维护

### 🏗️ 模块化架构

#### 1. Core 模块 - 核心系统功能
```javascript
const { 
  createMainWindow, 
  openDevTools, 
  getMachineCode 
} = require('./core')
```

**功能职责**：
- 主窗口创建和管理
- 系统托盘创建和菜单管理
- 设备信息获取（IP、MAC、机器码）
- 开发者工具控制

#### 2. Logging 模块 - 日志管理
```javascript
const { 
  logger, 
  createLogViewerWindow 
} = require('./logging')
```

**功能职责**：
- 日志记录和管理
- 日志查看器窗口（开发/预览模式）
- 日志配置和级别控制
- 实时日志监控界面

#### 3. Printing 模块 - 打印功能
```javascript
const { 
  setupPrinter, 
  createPrintQueueWindow 
} = require('./printing')
```

**功能职责**：
- 打印队列管理
- 打印任务消费者
- 打印队列监控窗口（开发模式）
- 打印机管理和IPC处理

#### 4. HTTP模块 - HTTP服务器功能
```javascript
const { 
  createHttpServer, 
  stopHttpServer, 
  setupHttpIPC 
} = require('./http')
```

**功能职责**：
- HTTP服务器创建和管理
- HTTP请求转发到渲染进程
- 渲染进程响应处理
- 安全的localhost服务器

#### 5. 其他模块
- **Updater**: 应用自动更新
- **VOD**: 视频点播功能

## 窗口管理架构

### 窗口分离策略

每个功能模块管理自己的窗口：

```javascript
// 日志模块管理日志查看器
logging/logViewer.js → createLogViewerWindow()

// 打印模块管理队列监控器  
printing/printMonitor.js → createPrintQueueWindow()

// 核心模块管理主窗口和托盘
core/windows.js → createMainWindow(), createSystemTray()
```

### 窗口生命周期管理

**统一的窗口管理模式**：
```javascript
let windowInstance = null

function createWindow() {
  if (windowInstance) {
    windowInstance.show()
    return
  }
  
  windowInstance = new BrowserWindow(config)
  
  windowInstance.on('closed', () => {
    windowInstance = null
  })
}
```

## 技术改进

### 1. 代码组织优化

**模块化程度提升**：
- 从单文件1190行 → 多文件分布
- 功能边界清晰
- 易于单元测试

**依赖关系简化**：
```javascript
// 主入口只需要协调各模块
index.js
├── core (窗口、设备信息)
├── logging (日志、查看器)
├── printing (打印、监控)
├── updater (升级)
└── vod (视频)
```

### 2. 可维护性提升

**功能定位**：
- 需要修改日志功能 → 只需关注 `logging/` 目录
- 需要修改打印功能 → 只需关注 `printing/` 目录
- 需要修改窗口逻辑 → 只需关注对应模块的窗口文件

**独立测试**：
```javascript
// 可以独立测试每个窗口功能
describe('LogViewer', () => {
  test('should create log viewer window', () => {
    // 测试日志查看器
  })
})

describe('PrintMonitor', () => {
  test('should create print monitor window', () => {
    // 测试打印监控器
  })
})
```

### 3. 配置统一管理

**环境适配**：
```javascript
// 日志查看器：开发/预览模式可用
if (process.env.NODE_ENV !== 'development' && !isPreviewBuild) {
  return // 不创建窗口
}

// 打印监控器：仅开发模式可用
if (process.env.NODE_ENV !== 'development') {
  return // 不创建窗口
}
```

## 使用示例

### 应用启动流程
```javascript
// 1. 加载模块
const { logger } = require('./logging')
const { setupPrinter } = require('./printing')

// 2. 初始化IPC
setupBasicIPC()
setupPrinter(ipcMain)

// 3. 创建窗口（模块内部管理）
createWindow() // 自动创建主窗口和托盘
```

### 窗口管理调用
```javascript
// 通过托盘菜单调用
tray.contextMenu = [
  {
    label: '日志查看器',
    click: () => createLogViewerWindow(isPreviewBuild)
  },
  {
    label: '打印队列监控',
    click: () => createPrintQueueWindow()
  }
]
```

### 模块独立使用
```javascript
// 日志模块
const { logger, createLogViewerWindow } = require('./logging')
logger.info('测试日志')
createLogViewerWindow()

// 打印模块  
const { addPrintTask, createPrintQueueWindow } = require('./printing')
addPrintTask({ ip: '*************', port: 9100, commands: [] })
createPrintQueueWindow()
```

## 性能优化

### 1. 延迟加载
```javascript
// 只在需要时加载窗口模块
function createWindow() {
  const { createMainWindow } = require('./core')  // 延迟加载
  const { createLogViewerWindow } = require('./logging')
  // ...
}
```

### 2. 内存管理
- 窗口关闭时自动清理引用
- 避免重复创建窗口实例
- 模块化减少内存占用

### 3. 启动优化
- 主入口文件精简，启动更快
- 模块按需初始化
- IPC处理器分模块注册

## 扩展指南

### 添加新窗口功能
1. 在对应模块目录创建窗口管理文件
2. 实现 `createXXXWindow()` 函数
3. 在模块 `index.js` 中导出
4. 在需要的地方调用

**示例 - 添加设置窗口**：
```javascript
// core/settingsWindow.js
function createSettingsWindow() {
  // 实现设置窗口
}

// core/index.js
module.exports = {
  // ... 其他导出
  createSettingsWindow
}

// 在托盘菜单中使用
const { createSettingsWindow } = require('./core')
```

### 模块间通信
- 优先使用模块导出的API
- 通过IPC进行进程间通信
- 使用事件发射器处理异步通信

## 最佳实践

### 1. 窗口命名规范
- 使用描述性名称：`logViewerWindow`, `printQueueWindow`
- 窗口管理文件命名：`xxxWindow.js` 或 `xxxMonitor.js`

### 2. 模块职责边界
- 每个模块只管理自己相关的窗口
- 避免跨模块直接操作窗口实例
- 通过统一入口暴露必要的API

### 3. 错误处理
```javascript
function createWindow() {
  try {
    // 窗口创建逻辑
  } catch (error) {
    console.error('[模块名] 创建窗口失败:', error)
    // 优雅降级
  }
}
```

---

通过这次重构，Thunder ERP 主进程架构更加清晰，代码可维护性大幅提升，符合现代软件开发的最佳实践。 

## 开发脚本使用说明

### dev:url 脚本

从命令行指定客户端URL启动应用程序：

```bash
# 使用自定义URL启动开发模式
npm run dev:url http://localhost:3000

# 使用测试环境URL
npm run dev:url https://test.example.com

# 使用本地开发服务器
npm run dev:url http://*************:8080
```

**URL优先级**：
1. 命令行参数指定的URL（最高优先级）
2. 配置文件中保存的URL
3. 环境选择对话框选择的URL（仅预览版）
4. 默认URL（生产环境或预览环境）

**使用场景**：
- 本地前端开发调试
- 连接不同的测试环境
- 快速切换开发服务器
- CI/CD 流水线中指定特定环境

**注意事项**：
- 通过命令行指定的URL不会保存到配置文件
- 仅在当次启动生效
- URL必须是完整的HTTP/HTTPS地址