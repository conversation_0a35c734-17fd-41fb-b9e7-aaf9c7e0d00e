/**
 * 日志查看器窗口管理模块
 * 负责创建和管理日志查看器窗口
 */

const { BrowserWindow } = require('electron')
const path = require('path')
const fs = require('fs')

let logViewerWindow = null

/**
 * 创建日志查看窗口
 * @param {boolean} isPreviewBuild 是否为预览构建
 */
function createLogViewerWindow(isPreviewBuild = false) {
  // 只在开发模式或预览环境下可用
  if (process.env.NODE_ENV !== 'development' && !isPreviewBuild) {
    console.log('日志查看器仅在开发模式或预览环境下可用')
    return
  }

  // 如果窗口已存在，则显示并返回
  if (logViewerWindow) {
    logViewerWindow.show()
    return
  }

  logViewerWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    title: '日志查看器',
    webPreferences: {
      preload: path.join(__dirname, '../../../dist/preload/index.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 确保renderer目录存在
  const rendererDir = path.join(__dirname, '../../renderer')
  const logViewerHtmlPath = path.join(rendererDir, 'log-viewer.html')

  if (!fs.existsSync(rendererDir)) {
    fs.mkdirSync(rendererDir, { recursive: true })
  }

  // 如果日志查看页面不存在，创建一个功能完整的页面
  if (!fs.existsSync(logViewerHtmlPath)) {
    const logViewerPage = generateLogViewerHTML()
    fs.writeFileSync(logViewerHtmlPath, logViewerPage)
  }

  // 加载日志查看页面
  logViewerWindow.loadFile(logViewerHtmlPath)

  // 开发模式下打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    logViewerWindow.webContents.openDevTools()
  }

  // 窗口关闭时清除引用
  logViewerWindow.on('closed', () => {
    logViewerWindow = null
  })
}

/**
 * 生成日志查看器HTML页面
 * @returns {string} HTML内容
 */
function generateLogViewerHTML() {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>日志查看器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: #fff;
      padding: 16px;
      border-bottom: 1px solid #e0e0e0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }
    
    .controls {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      align-items: center;
    }
    
    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    select, input, button {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    button {
      background: #1890ff;
      color: white;
      border: none;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background: #40a9ff;
    }
    
    button.danger {
      background: #ff4d4f;
    }
    
    button.danger:hover {
      background: #ff7875;
    }
    
    .stats {
      background: #fff;
      padding: 12px 16px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      gap: 24px;
      font-size: 14px;
    }
    
    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .stat-badge {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .stat-badge.error { background: #fff2f0; color: #ff4d4f; }
    .stat-badge.warn { background: #fffbe6; color: #faad14; }
    .stat-badge.info { background: #e6f7ff; color: #1890ff; }
    .stat-badge.debug { background: #f6ffed; color: #52c41a; }
    
    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .log-container {
      flex: 1;
      background: #fff;
      margin: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .log-list {
      flex: 1;
      overflow-y: auto;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
    
    .log-item {
      padding: 8px 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }
    
    .log-item:hover {
      background: #f9f9f9;
    }
    
    .log-time {
      color: #999;
      font-size: 12px;
      width: 180px;
      flex-shrink: 0;
    }
    
    .log-level {
      width: 60px;
      text-align: center;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      flex-shrink: 0;
    }
    
    .log-level.ERROR { background: #fff2f0; color: #ff4d4f; }
    .log-level.WARN { background: #fffbe6; color: #faad14; }
    .log-level.INFO { background: #e6f7ff; color: #1890ff; }
    .log-level.DEBUG { background: #f6ffed; color: #52c41a; }
    
    .log-message {
      flex: 1;
      word-break: break-all;
      white-space: pre-wrap;
    }
    
    .loading {
      text-align: center;
      padding: 40px;
      color: #666;
    }
    
    .empty {
      text-align: center;
      padding: 40px;
      color: #999;
    }
    
    .footer {
      background: #fff;
      padding: 8px 16px;
      border-top: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1 class="title">日志查看器</h1>
    <div class="controls">
      <div class="control-group">
        <label>级别:</label>
        <select id="levelFilter">
          <option value="">所有级别</option>
          <option value="ERROR">错误</option>
          <option value="WARN">警告</option>
          <option value="INFO">信息</option>
          <option value="DEBUG">调试</option>
        </select>
      </div>
      
      <div class="control-group">
        <label>关键词:</label>
        <input type="text" id="keywordFilter" placeholder="搜索关键词...">
      </div>
      
      <div class="control-group">
        <label>数量:</label>
        <select id="limitFilter">
          <option value="100">100条</option>
          <option value="500" selected>500条</option>
          <option value="1000">1000条</option>
          <option value="">全部</option>
        </select>
      </div>
      
      <button onclick="refreshLogs()">刷新</button>
      <button onclick="clearLogs()" class="danger">清空日志</button>
      <button onclick="exportLogs()">导出</button>
    </div>
  </div>
  
  <div class="stats" id="stats">
    <div class="stat-item">
      <span>总计:</span>
      <span class="stat-badge" id="totalCount">0</span>
    </div>
    <div class="stat-item">
      <span>错误:</span>
      <span class="stat-badge error" id="errorCount">0</span>
    </div>
    <div class="stat-item">
      <span>警告:</span>
      <span class="stat-badge warn" id="warnCount">0</span>
    </div>
    <div class="stat-item">
      <span>信息:</span>
      <span class="stat-badge info" id="infoCount">0</span>
    </div>
    <div class="stat-item">
      <span>调试:</span>
      <span class="stat-badge debug" id="debugCount">0</span>
    </div>
  </div>
  
  <div class="content">
    <div class="log-container">
      <div class="log-list" id="logList">
        <div class="loading">正在加载日志...</div>
      </div>
    </div>
  </div>
  
  <div class="footer">
    <span id="statusText">就绪</span>
    <span id="lastUpdate"></span>
  </div>

  <script>
    let currentLogs = [];

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      refreshLogs();
      
      // 绑定事件
      document.getElementById('levelFilter').addEventListener('change', applyFilters);
      document.getElementById('keywordFilter').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('limitFilter').addEventListener('change', applyFilters);
      
      // 自动刷新
      setInterval(refreshLogs, 5000);
    });

    // 防抖函数
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // 刷新日志
    async function refreshLogs() {
      try {
        document.getElementById('statusText').textContent = '正在加载...';
        
        const filter = getFilterOptions();
        const result = await window.Logger.getLogs(filter);
        
        if (result.success) {
          currentLogs = result.data;
          renderLogs(currentLogs);
          updateStats();
          document.getElementById('statusText').textContent = '已加载 ' + currentLogs.length + ' 条日志';
          document.getElementById('lastUpdate').textContent = '最后更新: ' + new Date().toLocaleTimeString();
        } else {
          throw new Error(result.error || '获取日志失败');
        }
      } catch (error) {
        console.error('刷新日志失败:', error);
        document.getElementById('logList').innerHTML = '<div class="empty">加载失败: ' + error.message + '</div>';
        document.getElementById('statusText').textContent = '加载失败';
      }
    }

    // 获取过滤选项
    function getFilterOptions() {
      return {
        level: document.getElementById('levelFilter').value,
        keyword: document.getElementById('keywordFilter').value,
        limit: parseInt(document.getElementById('limitFilter').value) || undefined
      };
    }

    // 应用过滤器
    function applyFilters() {
      const filter = getFilterOptions();
      let filteredLogs = [...currentLogs];
      
      if (filter.level) {
        filteredLogs = filteredLogs.filter(log => log.level === filter.level);
      }
      
      if (filter.keyword) {
        const keyword = filter.keyword.toLowerCase();
        filteredLogs = filteredLogs.filter(log => 
          log.message.toLowerCase().includes(keyword)
        );
      }
      
      renderLogs(filteredLogs);
      document.getElementById('statusText').textContent = '显示 ' + filteredLogs.length + ' 条日志';
    }

    // 渲染日志
    function renderLogs(logs) {
      const logList = document.getElementById('logList');
      
      if (logs.length === 0) {
        logList.innerHTML = '<div class="empty">没有日志数据</div>';
        return;
      }
      
      const html = logs.map(log => {
        const time = new Date(log.timestamp).toLocaleString();
        return \`
          <div class="log-item">
            <div class="log-time">\${time}</div>
            <div class="log-level \${log.level}">\${log.level}</div>
            <div class="log-message">\${escapeHtml(log.message)}</div>
          </div>
        \`;
      }).join('');
      
      logList.innerHTML = html;
      
      // 滚动到底部
      logList.scrollTop = logList.scrollHeight;
    }

    // 更新统计信息
    function updateStats() {
      const stats = {
        total: currentLogs.length,
        ERROR: 0,
        WARN: 0,
        INFO: 0,
        DEBUG: 0
      };
      
      currentLogs.forEach(log => {
        if (stats[log.level] !== undefined) {
          stats[log.level]++;
        }
      });
      
      document.getElementById('totalCount').textContent = stats.total;
      document.getElementById('errorCount').textContent = stats.ERROR;
      document.getElementById('warnCount').textContent = stats.WARN;
      document.getElementById('infoCount').textContent = stats.INFO;
      document.getElementById('debugCount').textContent = stats.DEBUG;
    }

    // 清空日志
    async function clearLogs() {
      if (!confirm('确定要清空所有日志吗？此操作不可恢复。')) {
        return;
      }
      
      try {
        const result = await window.Logger.clearLogs();
        if (result.success) {
          currentLogs = [];
          renderLogs([]);
          updateStats();
          document.getElementById('statusText').textContent = '日志已清空';
        } else {
          alert('清空日志失败: ' + result.error);
        }
      } catch (error) {
        console.error('清空日志失败:', error);
        alert('清空日志失败: ' + error.message);
      }
    }

    // 导出日志
    async function exportLogs() {
      try {
        const result = await window.Logger.exportLogs();
        if (result.success) {
          alert('日志已导出到: ' + result.filePath);
        } else {
          alert('导出日志失败: ' + result.error);
        }
      } catch (error) {
        console.error('导出日志失败:', error);
        alert('导出日志失败: ' + error.message);
      }
    }

    // HTML转义
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }
  </script>
</body>
</html>`
}

/**
 * 获取日志查看器窗口实例
 * @returns {BrowserWindow|null} 窗口实例
 */
function getLogViewerWindow() {
  return logViewerWindow
}

/**
 * 关闭日志查看器窗口
 */
function closeLogViewerWindow() {
  if (logViewerWindow) {
    logViewerWindow.close()
    logViewerWindow = null
  }
}

module.exports = {
  createLogViewerWindow,
  getLogViewerWindow,
  closeLogViewerWindow
} 