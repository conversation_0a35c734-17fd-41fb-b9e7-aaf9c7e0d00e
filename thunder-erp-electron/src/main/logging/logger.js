const fs = require('fs')
const path = require('path')
const { app } = require('electron')
const { getLoggerConfig, sanitizeLogData, formatTimestamp, shouldLog } = require('./loggerConfig')

/**
 * 日志管理器
 * 实现本地日志存储、日志轮转、日志级别管理
 */
class Logger {
  constructor() {
    // 获取配置
    this.config = getLoggerConfig()
    
    this.logDir = path.join(app.getPath('userData'), 'logs')
    this.logFile = path.join(this.logDir, 'main.log')
    this.maxFileSize = this.config.maxFileSize
    this.maxBackupFiles = this.config.maxBackupFiles
    this.logBuffer = [] // 内存中保存最近的日志用于实时查看
    this.maxBufferSize = this.config.maxBufferSize
    
    // 日志级别
    this.levels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    }
    
    this.currentLevel = this.config.logLevel
    
    // 确保日志目录存在
    this.ensureLogDir()
    
    // 保存原始的console方法
    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug
    }
    
    // 重写console方法
    if (this.config.enableConsoleOutput) {
      this.overrideConsole()
    }
    
    // 定期检查日志文件大小
    setInterval(() => this.checkLogRotation(), this.config.rotationCheckInterval)
    
    // 记录日志系统启动
    this.writeLog('INFO', [`日志系统已初始化`, { 
      environment: this.config.environment,
      logLevel: Object.keys(this.levels)[this.currentLevel],
      maxFileSize: `${this.config.maxFileSize / 1024 / 1024}MB`,
      maxBufferSize: this.config.maxBufferSize
    }])
  }

  /**
   * 确保日志目录存在
   */
  ensureLogDir() {
    try {
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true })
      }
    } catch (error) {
      this.originalConsole.error('创建日志目录失败:', error)
    }
  }

  /**
   * 提取模块名从日志消息中
   * @param {Array} args 日志参数
   * @returns {string|null} 模块名
   */
  extractModuleName(args) {
    if (args.length > 0 && typeof args[0] === 'string') {
      const message = args[0]
      const match = message.match(/^\[([^\]]+)\]/)
      return match ? `[${match[1]}]` : null
    }
    return null
  }

  /**
   * 写入日志到文件
   * @param {string} level 日志级别
   * @param {Array} args 日志参数
   */
  writeLog(level, args) {
    try {
      // 检查是否应该记录此日志
      const moduleName = this.extractModuleName(args)
      if (!shouldLog(level, this.currentLevel, moduleName)) {
        return
      }
      
      const timestamp = formatTimestamp(this.config.timestampFormat)
      let message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ')
      
      // 数据净化
      if (this.config.enableDataSanitization) {
        message = sanitizeLogData(message)
      }
      
      const logEntry = {
        timestamp,
        level,
        message,
        pid: process.pid,
        module: moduleName
      }
      
      // 添加到内存缓冲区
      this.logBuffer.push(logEntry)
      if (this.logBuffer.length > this.maxBufferSize) {
        this.logBuffer.shift() // 移除最旧的日志
      }
      
      // 写入文件（如果启用）
      if (this.config.enableFileOutput) {
        const logLine = `[${timestamp}] [${level}] [PID:${process.pid}]${moduleName ? ` ${moduleName}` : ''} ${message}\n`
        fs.appendFileSync(this.logFile, logLine, 'utf8')
        
        // 检查是否需要日志轮转
        this.checkLogRotation()
      }
    } catch (error) {
      this.originalConsole.error('写入日志失败:', error)
    }
  }

  /**
   * 检查并执行日志轮转
   */
  checkLogRotation() {
    try {
      if (!fs.existsSync(this.logFile)) {
        return
      }
      
      const stats = fs.statSync(this.logFile)
      if (stats.size >= this.maxFileSize) {
        this.rotateLog()
      }
    } catch (error) {
      this.originalConsole.error('检查日志轮转失败:', error)
    }
  }

  /**
   * 执行日志轮转
   */
  rotateLog() {
    try {
      // 删除最旧的备份文件
      const oldestBackup = path.join(this.logDir, `main.log.${this.maxBackupFiles}`)
      if (fs.existsSync(oldestBackup)) {
        fs.unlinkSync(oldestBackup)
      }
      
      // 重命名现有备份文件
      for (let i = this.maxBackupFiles - 1; i >= 1; i--) {
        const currentBackup = path.join(this.logDir, `main.log.${i}`)
        const nextBackup = path.join(this.logDir, `main.log.${i + 1}`)
        
        if (fs.existsSync(currentBackup)) {
          fs.renameSync(currentBackup, nextBackup)
        }
      }
      
      // 重命名当前日志文件为第一个备份
      const firstBackup = path.join(this.logDir, 'main.log.1')
      fs.renameSync(this.logFile, firstBackup)
      
      // 记录轮转事件
      this.writeLog('INFO', ['日志轮转完成', { 
        maxFileSize: `${this.maxFileSize / 1024 / 1024}MB`,
        backupFiles: this.maxBackupFiles 
      }])
    } catch (error) {
      this.originalConsole.error('日志轮转失败:', error)
    }
  }

  /**
   * 重写console方法
   */
  overrideConsole() {
    const self = this
    
    console.log = function(...args) {
      self.originalConsole.log(...args)
      if (self.currentLevel >= self.levels.INFO) {
        self.writeLog('INFO', args)
      }
    }
    
    console.error = function(...args) {
      self.originalConsole.error(...args)
      if (self.currentLevel >= self.levels.ERROR) {
        self.writeLog('ERROR', args)
      }
    }
    
    console.warn = function(...args) {
      self.originalConsole.warn(...args)
      if (self.currentLevel >= self.levels.WARN) {
        self.writeLog('WARN', args)
      }
    }
    
    console.info = function(...args) {
      self.originalConsole.info(...args)
      if (self.currentLevel >= self.levels.INFO) {
        self.writeLog('INFO', args)
      }
    }
    
    console.debug = function(...args) {
      self.originalConsole.debug(...args)
      if (self.currentLevel >= self.levels.DEBUG) {
        self.writeLog('DEBUG', args)
      }
    }
    
    // 捕获未处理的异常
    if (this.config.captureUncaughtExceptions) {
      process.on('uncaughtException', (error) => {
        const errorInfo = {
          message: error.message,
          stack: this.config.includeStackTrace ? error.stack : undefined,
          name: error.name,
          timestamp: formatTimestamp()
        }
        self.writeLog('ERROR', ['未捕获异常', errorInfo])
        self.originalConsole.error('Uncaught Exception:', error)
      })
    }
    
    // 捕获未处理的Promise拒绝
    if (this.config.captureUnhandledRejections) {
      process.on('unhandledRejection', (reason, promise) => {
        const rejectionInfo = {
          reason: typeof reason === 'object' ? JSON.stringify(reason) : String(reason),
          promise: promise.toString(),
          timestamp: formatTimestamp()
        }
        self.writeLog('ERROR', ['未处理的Promise拒绝', rejectionInfo])
        self.originalConsole.error('Unhandled Rejection at:', promise, 'reason:', reason)
      })
    }
  }

  /**
   * 获取内存中的日志
   * @param {Object} filter 过滤条件
   * @returns {Array} 过滤后的日志数组
   */
  getMemoryLogs(filter = {}) {
    let logs = [...this.logBuffer]
    
    // 按级别过滤
    if (filter.level) {
      logs = logs.filter(log => log.level === filter.level)
    }
    
    // 按模块过滤
    if (filter.module) {
      logs = logs.filter(log => log.module === filter.module)
    }
    
    // 按关键词过滤
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase()
      logs = logs.filter(log => 
        log.message.toLowerCase().includes(keyword)
      )
    }
    
    // 按时间范围过滤
    if (filter.startTime) {
      logs = logs.filter(log => new Date(log.timestamp) >= new Date(filter.startTime))
    }
    
    if (filter.endTime) {
      logs = logs.filter(log => new Date(log.timestamp) <= new Date(filter.endTime))
    }
    
    // 限制返回数量
    if (filter.limit) {
      logs = logs.slice(-filter.limit)
    }
    
    return logs
  }

  /**
   * 读取日志文件
   * @param {Object} options 读取选项
   * @returns {Array} 日志数组
   */
  readLogFile(options = {}) {
    try {
      const { lines = 1000, filter = {} } = options
      
      if (!fs.existsSync(this.logFile)) {
        return []
      }
      
      const content = fs.readFileSync(this.logFile, 'utf8')
      const logLines = content.split('\n').filter(line => line.trim())
      
      // 获取最后N行
      const recentLines = logLines.slice(-lines)
      
      // 解析日志行
      const logs = recentLines.map(line => {
        const match = line.match(/^\[(.+?)\] \[(.+?)\] \[PID:(\d+)\](?:\s+(\[[^\]]+\]))?\s+(.+)$/)
        if (match) {
          return {
            timestamp: match[1],
            level: match[2],
            pid: parseInt(match[3]),
            module: match[4] || null,
            message: match[5]
          }
        }
        return { timestamp: '', level: 'UNKNOWN', pid: 0, module: null, message: line }
      })
      
      return this.filterLogs(logs, filter)
    } catch (error) {
      this.originalConsole.error('读取日志文件失败:', error)
      return []
    }
  }

  /**
   * 过滤日志
   * @param {Array} logs 日志数组
   * @param {Object} filter 过滤条件
   * @returns {Array} 过滤后的日志数组
   */
  filterLogs(logs, filter) {
    let filteredLogs = [...logs]
    
    if (filter.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filter.level)
    }
    
    if (filter.module) {
      filteredLogs = filteredLogs.filter(log => log.module === filter.module)
    }
    
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase()
      filteredLogs = filteredLogs.filter(log => 
        log.message.toLowerCase().includes(keyword)
      )
    }
    
    return filteredLogs
  }

  /**
   * 清空日志
   */
  clearLogs() {
    try {
      // 清空内存缓冲区
      this.logBuffer = []
      
      // 清空日志文件
      if (fs.existsSync(this.logFile)) {
        fs.writeFileSync(this.logFile, '', 'utf8')
      }
      
      // 删除备份文件
      for (let i = 1; i <= this.maxBackupFiles; i++) {
        const backupFile = path.join(this.logDir, `main.log.${i}`)
        if (fs.existsSync(backupFile)) {
          fs.unlinkSync(backupFile)
        }
      }
      
      this.writeLog('INFO', ['日志已清空', { timestamp: formatTimestamp() }])
    } catch (error) {
      this.originalConsole.error('清空日志失败:', error)
    }
  }

  /**
   * 设置日志级别
   * @param {string} level 日志级别
   */
  setLogLevel(level) {
    if (this.levels.hasOwnProperty(level)) {
      this.currentLevel = this.levels[level]
      this.writeLog('INFO', [`日志级别已设置`, { 
        newLevel: level,
        timestamp: formatTimestamp() 
      }])
    } else {
      this.writeLog('WARN', [`无效的日志级别`, { 
        requestedLevel: level,
        availableLevels: Object.keys(this.levels)
      }])
    }
  }

  /**
   * 获取日志统计信息
   * @returns {Object} 统计信息
   */
  getLogStats() {
    try {
      const stats = {
        totalLogs: this.logBuffer.length,
        logFileSize: 0,
        backupFiles: 0,
        environment: this.config.environment,
        currentLogLevel: Object.keys(this.levels)[this.currentLevel],
        levels: {
          ERROR: 0,
          WARN: 0,
          INFO: 0,
          DEBUG: 0
        },
        modules: {}
      }
      
      // 计算日志文件大小
      if (fs.existsSync(this.logFile)) {
        stats.logFileSize = fs.statSync(this.logFile).size
      }
      
      // 计算备份文件数量
      for (let i = 1; i <= this.maxBackupFiles; i++) {
        const backupFile = path.join(this.logDir, `main.log.${i}`)
        if (fs.existsSync(backupFile)) {
          stats.backupFiles++
        }
      }
      
      // 统计各级别和模块日志数量
      this.logBuffer.forEach(log => {
        if (stats.levels[log.level] !== undefined) {
          stats.levels[log.level]++
        }
        
        if (log.module) {
          if (!stats.modules[log.module]) {
            stats.modules[log.module] = 0
          }
          stats.modules[log.module]++
        }
      })
      
      return stats
    } catch (error) {
      this.originalConsole.error('获取日志统计失败:', error)
      return {}
    }
  }

  /**
   * 更新配置
   * @param {Object} newConfig 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    this.writeLog('INFO', ['日志配置已更新', newConfig])
  }
}

// 导出单例
const logger = new Logger()
module.exports = logger 