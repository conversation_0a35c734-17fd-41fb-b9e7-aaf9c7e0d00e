/**
 * 日志模块统一入口
 * 导出日志管理器和相关配置
 */

const logger = require('./logger')
const config = require('./loggerConfig')
const { createLogViewerWindow, getLogViewerWindow, closeLogViewerWindow } = require('./logViewer')

module.exports = {
  // 主要的日志管理器实例
  logger,
  
  // 日志配置工具
  config,
  
  // 便捷方法
  getLoggerConfig: config.getLoggerConfig,
  getCurrentEnvironment: config.getCurrentEnvironment,
  sanitizeLogData: config.sanitizeLogData,
  formatTimestamp: config.formatTimestamp,
  shouldLog: config.shouldLog,
  
  // 日志级别常量
  LOG_LEVELS: config.logLevels,
  ENVIRONMENTS: config.environments,
  
  // 日志查看器窗口管理
  createLogViewerWindow,
  getLogViewerWindow,
  closeLogViewerWindow
} 