/**
 * 日志配置管理
 * 根据环境和用户设置管理日志级别和行为
 */

const logLevels = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
}

const environments = {
  PRODUCTION: 'production',
  PREVIEW: 'preview',
  DEVELOPMENT: 'development'
}

/**
 * 获取当前环境
 * @returns {string} 环境字符串
 */
function getCurrentEnvironment() {
  if (process.env.NODE_ENV === 'development') {
    return environments.DEVELOPMENT
  }
  
  // 检查是否为预览构建
  const fs = require('fs')
  const path = require('path')
  const isPreviewBuild = fs.existsSync(path.join(__dirname, '../../.preview-build'))
  
  return isPreviewBuild ? environments.PREVIEW : environments.PRODUCTION
}

/**
 * 获取环境默认日志级别
 * @param {string} environment 环境
 * @returns {number} 日志级别
 */
function getDefaultLogLevel(environment) {
  switch (environment) {
    case environments.DEVELOPMENT:
      return logLevels.DEBUG // 开发环境显示所有日志
    case environments.PREVIEW:
      return logLevels.INFO // 预览环境显示信息级别以上日志
    case environments.PRODUCTION:
      return logLevels.WARN // 生产环境只显示警告和错误
    default:
      return logLevels.INFO
  }
}

/**
 * 获取日志配置
 * @returns {Object} 日志配置对象
 */
function getLoggerConfig() {
  const environment = getCurrentEnvironment()
  const defaultLevel = getDefaultLogLevel(environment)
  
  return {
    // 基本配置
    environment,
    logLevel: defaultLevel,
    
    // 文件配置
    maxFileSize: 50 * 1024 * 1024, // 50MB
    maxBackupFiles: 5,
    
    // 内存缓冲配置
    maxBufferSize: environment === environments.DEVELOPMENT ? 2000 : 1000,
    
    // 日志轮转检查间隔（毫秒）
    rotationCheckInterval: 60000, // 1分钟
    
    // 是否启用控制台输出
    enableConsoleOutput: true,
    
    // 是否启用文件输出
    enableFileOutput: true,
    
    // 是否启用日志查看器（仅开发和预览模式）
    enableLogViewer: environment !== environments.PRODUCTION,
    
    // 异常捕获配置
    captureUncaughtExceptions: true,
    captureUnhandledRejections: true,
    
    // 日志格式配置
    timestampFormat: 'ISO', // ISO, LOCAL, TIMESTAMP
    includeStackTrace: environment === environments.DEVELOPMENT,
    
    // 性能监控
    enablePerformanceLogging: environment === environments.DEVELOPMENT,
    
    // 敏感信息过滤
    enableDataSanitization: environment === environments.PRODUCTION,
    
    // 模块特定配置
    moduleConfigs: {
      '[打印模块]': {
        level: environment === environments.DEVELOPMENT ? logLevels.DEBUG : logLevels.INFO
      },
      '[打印队列]': {
        level: environment === environments.DEVELOPMENT ? logLevels.DEBUG : logLevels.INFO
      },
      '[打印消费者]': {
        level: environment === environments.DEVELOPMENT ? logLevels.DEBUG : logLevels.INFO
      },
      '[VOD模块]': {
        level: logLevels.INFO
      },
      '[升级模块]': {
        level: logLevels.INFO
      }
    }
  }
}

/**
 * 数据净化 - 移除或掩码敏感信息
 * @param {string} message 日志消息
 * @returns {string} 净化后的消息
 */
function sanitizeLogData(message) {
  if (typeof message !== 'string') {
    return message
  }
  
  // 移除或掩码常见的敏感信息
  let sanitized = message
  
  // 掩码IP地址（保留最后一段）
  sanitized = sanitized.replace(
    /\b(\d{1,3}\.){3}\d{1,3}\b/g, 
    (match) => {
      const parts = match.split('.')
      return `${parts[0]}.${parts[1]}.${parts[2]}.***`
    }
  )
  
  // 掩码可能的密码字段
  sanitized = sanitized.replace(
    /"password"\s*:\s*"[^"]*"/gi,
    '"password": "***"'
  )
  
  // 掩码可能的token字段
  sanitized = sanitized.replace(
    /"token"\s*:\s*"[^"]*"/gi,
    '"token": "***"'
  )
  
  return sanitized
}

/**
 * 格式化时间戳
 * @param {string} format 格式类型
 * @returns {string} 格式化的时间戳
 */
function formatTimestamp(format = 'ISO') {
  const now = new Date()
  
  switch (format) {
    case 'LOCAL':
      return now.toLocaleString()
    case 'TIMESTAMP':
      return now.getTime().toString()
    case 'ISO':
    default:
      return now.toISOString()
  }
}

/**
 * 检查日志级别是否应该被记录
 * @param {string} messageLevel 消息级别
 * @param {number} currentLevel 当前日志级别
 * @param {string} module 模块名（可选）
 * @returns {boolean} 是否应该记录
 */
function shouldLog(messageLevel, currentLevel, module = null) {
  const config = getLoggerConfig()
  
  // 检查模块特定配置
  if (module && config.moduleConfigs[module]) {
    const moduleLevel = config.moduleConfigs[module].level
    return logLevels[messageLevel] <= moduleLevel
  }
  
  // 使用全局级别
  return logLevels[messageLevel] <= currentLevel
}

module.exports = {
  logLevels,
  environments,
  getCurrentEnvironment,
  getDefaultLogLevel,
  getLoggerConfig,
  sanitizeLogData,
  formatTimestamp,
  shouldLog
} 