const axios = require('axios')

/**
 * 设置VOD模块
 * 完全模拟Android端的VodJSInterface功能
 */
module.exports = function setupVod(ipcMain) {
  // 处理VOD请求
  ipcMain.handle('vod-request', async (event, { method, url, params, body, callbackId }) => {
    try {
      console.info(`[VOD-REQUEST] VOD请求开始: ${url}?${params}, body: ${body || '无'}, callbackId: ${callbackId}`)

      // 参数验证
      if (!url) {
        console.error('[VOD-REQUEST] VOD请求URL为空')
        const errorResponse = {
          errcode: 400,
          errmsg: 'URL不能为空'
        }

        // 如果提供了callbackId，发送响应事件
        if (callbackId) {
          const responseText = JSON.stringify(errorResponse)
          event.sender.send('vod-response', { callbackId, responseText })
        }

        return errorResponse
      }

      // 构建请求配置
      const config = {
        method: method ? method.toLowerCase() : 'get',
        url: `${url}${params ? '?' + params : ''}`,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: 10000 // 10秒超时
      }

      // 如果有请求体，添加到配置中
      if (body && body !== '') {
        try {
          config.data = typeof body === 'string' ? JSON.parse(body) : body
        } catch (e) {
          console.warn('解析请求体失败，使用原始字符串:', e)
          config.data = body
        }
      }

      // 发送请求
      console.info('[VOD-REQUEST] 发送HTTP请求:', config)
      const response = await axios(config)

      // 打印响应信息（限制长度以避免日志过大）
      const responseStr = JSON.stringify(response.data)
      console.info(`[VOD-REQUEST] VOD响应: ${responseStr.length > 200 ? responseStr.substring(0, 200) + '...' : responseStr}`)
      console.info(`[VOD-REQUEST] VOD响应状态码: ${response.status}`)

      // 确保返回的数据格式正确
      let result
      if (!response.data.hasOwnProperty('errcode')) {
        result = {
          errcode: 200,
          errmsg: 'success',
          result: response.data
        }
      } else {
        result = response.data
      }

      // 如果提供了callbackId，发送响应事件
      if (callbackId) {
        const responseText = JSON.stringify(result)
        console.info(`[VOD-REQUEST] 发送VOD响应事件, callbackId: ${callbackId}`)
        event.sender.send('vod-response', { callbackId, responseText })
      }

      return result
    } catch (error) {
      console.error('[VOD-REQUEST] VOD请求失败:', error)

      // 更详细的错误信息
      const errorResponse = {
        errcode: error.response?.status || 500,
        errmsg: `请求失败: ${error.message}`
      }

      // 如果有响应数据，添加到错误信息中
      if (error.response?.data) {
        errorResponse.response = error.response.data
      }

      // 如果提供了callbackId，发送响应事件
      if (callbackId) {
        const errorText = JSON.stringify(errorResponse)
        console.info(`[VOD-REQUEST] 发送VOD错误响应事件, callbackId: ${callbackId}`)
        event.sender.send('vod-response', { callbackId, responseText: errorText })
      }

      return errorResponse
    }
  })
}