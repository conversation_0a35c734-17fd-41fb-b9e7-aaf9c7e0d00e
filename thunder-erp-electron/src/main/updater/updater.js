const { autoUpdater } = require('electron-updater')
const { dialog } = require('electron')

module.exports = function setupUpdater(ipcMain, mainWindow) {
  // 配置更新服务器
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: 'https://your-update-server.com/updates/'
  })
  
  // 检查更新错误
  autoUpdater.on('error', (error) => {
    console.error('更新出错:', error)
    // 发送错误事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeFailed',
        data: error.message
      })
    }
  })
  
  // 检查到更新
  autoUpdater.on('update-available', (info) => {
    console.log('有可用更新:', info)
    // 发送更新事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onCheckResult',
        data: {
          hasUpdate: true,
          upgradeInfo: {
            version: info.version,
            releaseNotes: info.releaseNotes,
            isForceUpgrade: info.releaseNotes?.includes('FORCE') || false
          }
        }
      })
    }
  })
  
  // 没有更新
  autoUpdater.on('update-not-available', () => {
    console.log('无可用更新')
    // 发送无更新事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onCheckResult',
        data: {
          hasUpdate: false,
          upgradeInfo: null
        }
      })
    }
  })
  
  // 更新下载进度
  autoUpdater.on('download-progress', (progressObj) => {
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onDownloadProgress',
        data: progressObj
      })
    }
  })
  
  // 更新下载完成
  autoUpdater.on('update-downloaded', () => {
    console.log('更新下载完成')
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeComplete',
        data: {}
      })
      
      dialog.showMessageBox({
        type: 'info',
        title: '应用更新',
        message: '更新已下载，是否立即安装？',
        buttons: ['是', '否']
      }).then((returnValue) => {
        if (returnValue.response === 0) {
          autoUpdater.quitAndInstall()
        }
      })
    }
  })
  
  // 检查更新
  ipcMain.handle('check-upgrade', async () => {
    try {
      console.log('开始检查更新')
      const result = await autoUpdater.checkForUpdates()
      if (result) {
        return {
          success: true,
          data: {
            hasUpdate: !!result.updateInfo,
            upgradeInfo: result.updateInfo ? {
              version: result.updateInfo.version,
              releaseNotes: result.updateInfo.releaseNotes,
              isForceUpgrade: result.updateInfo.releaseNotes?.includes('FORCE') || false
            } : null
          }
        }
      } else {
        return {
          success: true,
          data: {
            hasUpdate: false,
            upgradeInfo: null
          }
        }
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  })
  
  // 开始升级
  ipcMain.handle('start-upgrade', async () => {
    try {
      console.log('开始下载更新')
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeStarted',
        data: {}
      })
      
      autoUpdater.downloadUpdate()
      return { success: true, message: '开始下载更新' }
    } catch (error) {
      console.error('开始升级失败:', error)
      return { success: false, message: error.message }
    }
  })
  
  // 自动检查更新（应用启动时）
  setTimeout(() => {
    autoUpdater.checkForUpdates()
  }, 3000)
}
