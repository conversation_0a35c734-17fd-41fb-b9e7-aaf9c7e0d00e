const { autoUpdater } = require('electron-updater');
const { dialog } = require('electron');
const axios = require('axios');
const { getVersionInfo } = require('../utils/version');

// 读取 package.json 获取 publish.url 作为更新源
let feedURL;
try {
  const pkg = require('../../../package.json');
  if (pkg.build && pkg.build.publish && pkg.build.publish.url) {
    feedURL = pkg.build.publish.url;
  }
} catch (e) {
  console.error('读取 package.json publish.url 失败', e);
}

// API 基础地址，可通过环境变量覆盖
const BASE_URL = process.env.API_BASE_URL || 'https://api.yourhost.com';

// 新接口路径
const CHECK_URL = BASE_URL + '/api/app-upgrade/version';

module.exports = function setupUpdater(ipcMain, mainWindow) {
  // 升级功能已禁用 - 功能还在开发中
  // 禁用自动更新检查
  // autoUpdater.setFeedURL({
  //   provider: 'generic',
  //   url: 'https://your-update-server.com/updates/'
  // })

  // 设置 FeedURL 并启用自动更新
  if (feedURL) {
    try {
      autoUpdater.setFeedURL({ provider: 'generic', url: feedURL });
      console.log('autoUpdater feedURL 已设置:', feedURL);
      autoUpdater.checkForUpdates();
    } catch (err) {
      console.error('autoUpdater 初始化失败', err);
    }
  } else {
    console.warn('未配置 feedURL，自动更新功能关闭');
  }

  // 检查更新错误处理
  autoUpdater.on('error', error => {
    console.error('更新出错:', error);
    // 发送错误事件
    if (mainWindow && !mainWindow.isDestroyed()) {
      try {
        mainWindow.webContents.send('upgrade-event', {
          event: 'onUpgradeFailed',
          data: error.message || 'unknown'
        });
      } catch (sendError) {
        console.error('发送更新错误事件失败:', sendError);
      }
    }
  });

  // 检查到更新
  autoUpdater.on('update-available', info => {
    console.log('有可用更新:', info);
    
    // 自动开始下载更新
    console.log('[Electron] 开始自动下载更新');
    autoUpdater.downloadUpdate();
    
    // 发送更新事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onCheckResult',
        data: {
          hasUpdate: true,
          upgradeInfo: {
            version: info.version,
            releaseNotes: info.releaseNotes,
            isForceUpgrade: info.releaseNotes?.includes('FORCE') || false
          }
        }
      });
    }
  });

  // 没有更新
  autoUpdater.on('update-not-available', () => {
    console.log('无可用更新');
    // 发送无更新事件
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onCheckResult',
        data: {
          hasUpdate: false,
          upgradeInfo: null
        }
      });
    }
  });

  // 更新下载进度
  autoUpdater.on('download-progress', progressObj => {
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onDownloadProgress',
        data: progressObj
      });
    }
  });

  // 更新下载完成
  autoUpdater.on('update-downloaded', () => {
    console.log('更新下载完成');
    if (mainWindow) {
      mainWindow.webContents.send('upgrade-event', {
        event: 'onUpgradeComplete',
        data: {}
      });

      dialog
        .showMessageBox({
          type: 'info',
          title: '应用更新',
          message: '更新已下载，是否立即安装？',
          buttons: ['是', '否']
        })
        .then(returnValue => {
          if (returnValue.response === 0) {
            autoUpdater.quitAndInstall();
          }
        });
    }
  });

  // 检查更新（已禁用）
  ipcMain.handle('check-upgrade', async () => {
    try {
      // 使用统一的版本工具模块
      const { versionCode, versionName } = getVersionInfo();
      const clientType = process.platform === 'win32' ? 'CASHIER_WINDOWS' : 'CASHIER_MAC';

      console.log('Electron版本信息:', { versionCode, versionName, clientType });

      const res = await axios.post(CHECK_URL, {
        clientType,
        versionCode
      });

      if (res.data && (res.data.code === 200 || res.data.code === 0)) {
        return { success: true, data: res.data.data };
      }

      return { success: false, message: res.data?.message || 'unknown error' };
    } catch (err) {
      console.error('check-upgrade failed', err);
      return { success: false, message: err.message };
    }
  });

  // 开始升级：使用 autoUpdater 或手动下载安装
  ipcMain.handle('start-upgrade', async () => {
    try {
      console.log('[Electron] 开始升级流程');
      
      // 显示升级确认对话框
      const result = await dialog.showMessageBox(mainWindow, {
        type: 'question',
        title: '应用升级',
        message: '发现新版本，是否立即下载升级？',
        detail: '升级将在后台进行，下载完成后会提示安装',
        buttons: ['立即升级', '稍后升级'],
        defaultId: 0,
        cancelId: 1,
        noLink: true
      });

      if (result.response === 0) {
        console.log('[Electron] 用户确认升级，开始下载...');
        
        // 发送升级开始事件
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('upgrade-event', {
            event: 'onUpgradeStarted',
            data: { message: '开始下载更新...' }
          });
        }
        
        // 使用 autoUpdater 检查并下载更新
        try {
          console.log('[Electron] 使用 autoUpdater 检查更新');
          
          // 先检查更新，然后在 update-available 事件中自动下载
          autoUpdater.checkForUpdatesAndNotify();
          
          return { 
            success: true, 
            message: '正在检查更新，请等待...' 
          };
        } catch (downloadError) {
          console.error('[Electron] autoUpdater 检查更新失败:', downloadError);
          
          // 发送升级失败事件
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('upgrade-event', {
              event: 'onUpgradeFailed',
              data: downloadError.message
            });
          }
          
          return { 
            success: false, 
            message: 'autoUpdater 检查更新失败: ' + downloadError.message 
          };
        }
      } else {
        console.log('[Electron] 用户取消升级');
        return { 
          success: false, 
          message: '用户取消升级',
          cancelled: true 
        };
      }
    } catch (err) {
      console.error('[Electron] 升级流程失败:', err);
      return { success: false, message: err.message };
    }
  });


  console.log('autoUpdater 模块初始化完成');
};
