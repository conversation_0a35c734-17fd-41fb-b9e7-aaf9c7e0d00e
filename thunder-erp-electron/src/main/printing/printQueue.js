const EventEmitter = require('events')
const path = require('path')
const fs = require('fs')
const { app } = require('electron')

/**
 * 打印队列管理器
 * 实现生产者消费者模式的打印队列
 */
class PrintQueueManager extends EventEmitter {
  constructor() {
    super()
    this.queue = []
    this.isProcessing = false
    this.maxRetries = 3
    this.queueStoragePath = path.join(app.getPath('userData'), 'print-queue.json')
    
    // 加载持久化的队列
    this.loadQueue()
    
    // 定期保存队列状态
    setInterval(() => this.saveQueue(), 30000)
  }

  /**
   * 添加打印任务到队列
   * @param {Object} task 打印任务对象
   * @param {string} task.ip 打印机IP
   * @param {number} task.port 打印机端口
   * @param {Array} task.commands 打印命令数组
   * @returns {string} 任务ID
   */
  addTask(task) {
    const taskId = `print-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    
    const printTask = {
      id: taskId,
      ip: task.ip,
      port: task.port,
      commands: task.commands,
      status: 'pending', // pending, processing, completed, failed
      createdAt: Date.now(),
      retries: 0,
      error: null
    }
    
    this.queue.push(printTask)
    console.info('[打印队列] 添加任务', { 
      taskId, 
      printer: `${task.ip}:${task.port}`,
      commandCount: task.commands.length,
      queueLength: this.queue.length 
    })
    
    // 保存队列状态
    this.saveQueue()
    
    // 触发任务添加事件
    this.emit('task-added', printTask)
    
    return taskId
  }

  /**
   * 获取下一个待处理的任务
   * @returns {Object|null} 打印任务对象，如果队列为空则返回null
   */
  getNextTask() {
    // 按创建时间排序，优先处理较早的任务
    const pendingTasks = this.queue.filter(task => 
      task.status === 'pending' || 
      (task.status === 'failed' && task.retries < this.maxRetries)
    )
    
    if (pendingTasks.length === 0) {
      return null
    }
    
    // 按创建时间排序
    pendingTasks.sort((a, b) => a.createdAt - b.createdAt)
    return pendingTasks[0]
  }

  /**
   * 更新任务状态
   * @param {string} taskId 任务ID
   * @param {string} status 新状态
   * @param {Error} error 错误信息（可选）
   */
  updateTaskStatus(taskId, status, error = null) {
    const taskIndex = this.queue.findIndex(task => task.id === taskId)
    
    if (taskIndex === -1) {
      console.warn('[打印队列] 未找到任务', { taskId })
      return
    }
    
    const task = this.queue[taskIndex]
    const oldStatus = task.status
    
    // 更新任务状态
    task.status = status
    
    if (status === 'processing') {
      task.startedAt = Date.now()
      console.debug('[打印队列] 任务开始处理', { taskId })
    } else if (status === 'completed') {
      task.completedAt = Date.now()
      const duration = task.completedAt - task.startedAt
      console.info('[打印队列] 任务完成', { 
        taskId, 
        duration: `${duration}ms`,
        printer: `${task.ip}:${task.port}` 
      })
    } else if (status === 'failed') {
      task.retries += 1
      task.error = error ? error.message : '未知错误'
      task.lastFailedAt = Date.now()
      
      // 如果重试次数超过最大值，标记为最终失败
      if (task.retries >= this.maxRetries) {
        task.status = 'failed-final'
        console.error('[打印队列] 任务最终失败', { 
          taskId, 
          retries: task.retries,
          maxRetries: this.maxRetries,
          error: task.error 
        })
      } else {
        console.warn('[打印队列] 任务失败，将重试', { 
          taskId, 
          retries: task.retries,
          maxRetries: this.maxRetries,
          error: task.error 
        })
      }
    }
    
    // 保存队列状态
    this.saveQueue()
    
    // 触发状态变更事件
    this.emit('task-updated', task, oldStatus)
    
    // 如果任务完成或最终失败，从队列中移除
    if (status === 'completed' || task.status === 'failed-final') {
      // 保留一段时间后再清理，方便查询状态
      setTimeout(() => {
        const currentIndex = this.queue.findIndex(t => t.id === taskId)
        if (currentIndex !== -1) {
          this.queue.splice(currentIndex, 1)
          console.debug('[打印队列] 清理已完成任务', { taskId })
          this.saveQueue()
        }
      }, 3600000) // 1小时后清理
    }
  }

  /**
   * 获取队列状态
   * @returns {Object} 队列状态对象
   */
  getQueueStatus() {
    const total = this.queue.length
    const pending = this.queue.filter(task => task.status === 'pending').length
    const processing = this.queue.filter(task => task.status === 'processing').length
    const completed = this.queue.filter(task => task.status === 'completed').length
    const failed = this.queue.filter(task => 
      task.status === 'failed' || task.status === 'failed-final'
    ).length
    
    return {
      total,
      pending,
      processing,
      completed,
      failed,
      isProcessing: this.isProcessing
    }
  }

  /**
   * 获取所有任务
   * @returns {Array} 任务数组
   */
  getAllTasks() {
    return [...this.queue]
  }

  /**
   * 清空队列
   */
  clearQueue() {
    const beforeCount = this.queue.length
    
    // 只清除未开始处理的任务
    this.queue = this.queue.filter(task => 
      task.status !== 'pending'
    )
    
    const clearedCount = beforeCount - this.queue.length
    console.info('[打印队列] 已清空待处理任务', { 
      clearedCount,
      remainingCount: this.queue.length 
    })
    
    this.saveQueue()
    this.emit('queue-cleared')
  }

  /**
   * 重试失败的任务
   * @param {string} taskId 任务ID，如果不提供则重试所有失败任务
   */
  retryFailedTask(taskId = null) {
    if (taskId) {
      // 重试指定任务
      const taskIndex = this.queue.findIndex(task => task.id === taskId)
      
      if (taskIndex === -1) {
        console.warn('[打印队列] 重试失败，未找到任务', { taskId })
        return false
      }
      
      const task = this.queue[taskIndex]
      
      if (task.status !== 'failed' && task.status !== 'failed-final') {
        console.warn('[打印队列] 重试失败，任务不是失败状态', { 
          taskId, 
          currentStatus: task.status 
        })
        return false
      }
      
      // 重置任务状态
      task.status = 'pending'
      task.retries = 0
      task.error = null
      
      console.info('[打印队列] 重试任务', { taskId })
      this.saveQueue()
      this.emit('task-retry', task)
      return true
    } else {
      // 重试所有失败任务
      let retryCount = 0
      
      this.queue.forEach(task => {
        if (task.status === 'failed' || task.status === 'failed-final') {
          task.status = 'pending'
          task.retries = 0
          task.error = null
          retryCount++
        }
      })
      
      if (retryCount > 0) {
        console.info('[打印队列] 重试所有失败任务', { retryCount })
        this.saveQueue()
        this.emit('all-tasks-retry', retryCount)
      }
      
      return retryCount > 0
    }
  }

  /**
   * 保存队列到文件
   */
  saveQueue() {
    try {
      // 创建一个可以序列化的队列副本
      const queueCopy = this.queue.map(task => ({
        ...task,
        // 移除可能包含循环引用的错误对象
        error: task.error ? String(task.error) : null
      }))
      
      fs.writeFileSync(this.queueStoragePath, JSON.stringify(queueCopy, null, 2))
    } catch (error) {
      console.error('[打印队列] 保存队列失败', { 
        error: error.message,
        queueLength: this.queue.length 
      })
    }
  }

  /**
   * 从文件加载队列
   */
  loadQueue() {
    try {
      if (fs.existsSync(this.queueStoragePath)) {
        const data = fs.readFileSync(this.queueStoragePath, 'utf8')
        this.queue = JSON.parse(data)
        
        // 重置处理中的任务状态
        let resetCount = 0
        this.queue.forEach(task => {
          if (task.status === 'processing') {
            task.status = 'pending'
            resetCount++
          }
        })
        
        console.info('[打印队列] 已加载队列', { 
          totalTasks: this.queue.length,
          resetTasks: resetCount 
        })
      } else {
        console.info('[打印队列] 队列文件不存在，创建新队列')
      }
    } catch (error) {
      console.error('[打印队列] 加载队列失败', { error: error.message })
      this.queue = []
    }
  }

  /**
   * 设置处理状态
   * @param {boolean} isProcessing 是否正在处理
   */
  setProcessing(isProcessing) {
    this.isProcessing = isProcessing
  }
}

// 导出单例
const printQueueManager = new PrintQueueManager()
module.exports = printQueueManager
