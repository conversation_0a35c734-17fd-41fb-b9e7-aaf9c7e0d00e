const net = require('net')
const printQueueManager = require('./printQueue')


/**
 * 打印消费者服务
 * 负责从队列中获取打印任务并执行
 */
class PrintConsumer {
  constructor() {
    this.isRunning = false
    this.processingTaskId = null
    this.checkInterval = 1000 // 检查队列间隔（毫秒）
    this.intervalId = null
  }

  /**
   * 启动打印消费者服务
   */
  start() {
    if (this.isRunning) {
      console.info('[打印消费者] 服务已在运行中')
      return
    }

    this.isRunning = true
    console.info('[打印消费者] 服务已启动', { checkInterval: this.checkInterval })

    // 定期检查队列
    this.intervalId = setInterval(() => this.processNextTask(), this.checkInterval)
  }

  /**
   * 停止打印消费者服务
   */
  stop() {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false

    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    console.info('[打印消费者] 服务已停止')
  }

  /**
   * 处理下一个打印任务
   */
  async processNextTask() {
    // 如果已经在处理任务，则跳过
    if (this.processingTaskId) {
      return
    }

    // 获取下一个待处理任务
    const task = printQueueManager.getNextTask()

    if (!task) {
      // 队列为空，无需处理
      return
    }

    // 标记为正在处理
    this.processingTaskId = task.id
    printQueueManager.setProcessing(true)
    printQueueManager.updateTaskStatus(task.id, 'processing')

    try {
      console.info(`[打印消费者] 开始处理任务`, { 
        taskId: task.id, 
        printer: `${task.ip}:${task.port}`,
        commandCount: task.commands.length 
      })

      // 执行打印
      const success = await this.executePrint(task.ip, task.port, task.commands)

      if (success) {
        console.info(`[打印消费者] 任务打印成功`, { taskId: task.id })
        printQueueManager.updateTaskStatus(task.id, 'completed')
      } else {
        console.error(`[打印消费者] 任务打印失败`, { taskId: task.id })
        printQueueManager.updateTaskStatus(task.id, 'failed', new Error('打印失败'))
      }
    } catch (error) {
      console.error(`[打印消费者] 任务处理出错`, { 
        taskId: task.id, 
        error: error.message,
        stack: error.stack 
      })
      printQueueManager.updateTaskStatus(task.id, 'failed', error)
    } finally {
      // 重置处理状态
      this.processingTaskId = null
      printQueueManager.setProcessing(false)
    }
  }

  /**
   * 执行打印操作
   * @param {string} ip 打印机IP
   * @param {number} port 打印机端口
   * @param {Array} commands 打印命令数组
   * @returns {Promise<boolean>} 是否打印成功
   */
  async executePrint(ip, port, commands) {
    return new Promise((resolve, reject) => {
      try {
        const client = new net.Socket()

        // 设置超时
        client.setTimeout(5000)

        client.on('timeout', () => {
          client.end()
          const error = new Error('打印超时')
          console.warn('[打印消费者] 打印超时', { ip, port })
          reject(error)
        })

        client.on('error', (err) => {
          console.error('[打印消费者] Socket连接错误', { 
            ip, 
            port, 
            error: err.message 
          })
          reject(err)
        })

        client.connect(port, ip, () => {
          console.debug(`[打印消费者] 已连接到打印机`, { 
            printer: `${ip}:${port}`,
            commandCount: commands.length 
          })

          // 依次发送命令
          for (const cmd of commands) {
            let data

            switch (cmd.type) {
              case 'text':
                // 对于文本，使用GBK编码而不是UTF-8
                try {
                  // 尝试使用iconv-lite模块
                  let iconv
                  try {
                    iconv = require('iconv-lite')
                  } catch (err) {
                    console.warn('[打印消费者] 未安装iconv-lite模块，将尝试使用内置编码转换')
                  }

                  if (iconv) {
                    // 使用iconv-lite进行编码转换
                    data = iconv.encode(cmd.data, 'gbk')
                  } else {
                    throw new Error('未安装iconv-lite模块')
                  }
                } catch (e) {
                  // 如果编码转换失败，回退到UTF-8
                  console.warn('[打印消费者] 编码转换失败，使用UTF-8编码，中文可能会显示为乱码', { 
                    error: e.message,
                    textLength: cmd.data.length 
                  })
                  data = Buffer.from(cmd.data, 'utf8')
                }
                break
              case 'command':
              case 'raw':
                data = Buffer.from(cmd.data)
                break
            }

            if (data) {
              client.write(data)
            }
          }

          // 关闭连接
          client.end(() => {
            console.debug('[打印消费者] 打印命令发送完成', { printer: `${ip}:${port}` })
            resolve(true)
          })
        })
      } catch (error) {
        console.error('[打印消费者] 打印执行失败', { 
          ip, 
          port, 
          error: error.message,
          stack: error.stack 
        })
        reject(error)
      }
    })
  }
}

// 导出单例
const printConsumer = new PrintConsumer()
module.exports = printConsumer
