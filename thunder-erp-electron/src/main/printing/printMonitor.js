/**
 * 打印队列监控窗口管理模块
 * 负责创建和管理打印队列监控窗口
 */

const { BrowserWindow } = require('electron')
const path = require('path')
const fs = require('fs')

let printQueueWindow = null

/**
 * 创建打印队列监控窗口 - 仅在开发模式下可用
 */
function createPrintQueueWindow() {
  // 在生产模式下不显示打印队列监控窗口
  if (process.env.NODE_ENV !== 'development') {
    console.log('[打印监控] 打印队列监控窗口仅在开发模式下可用')
    return
  }

  // 如果窗口已存在，则显示并返回
  if (printQueueWindow) {
    printQueueWindow.show()
    return
  }

  printQueueWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    title: '打印队列监控 (开发模式)',
    webPreferences: {
      preload: path.join(__dirname, '../../preload/index.js'),
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 确保renderer目录存在
  const rendererDir = path.join(__dirname, '../../renderer')
  const monitorHtmlPath = path.join(rendererDir, 'print-queue-monitor.html')

  if (!fs.existsSync(rendererDir)) {
    fs.mkdirSync(rendererDir, { recursive: true })
  }

  // 如果监控页面不存在，创建一个简单的页面
  if (!fs.existsSync(monitorHtmlPath)) {
    const monitorPage = generatePrintMonitorHTML()
    fs.writeFileSync(monitorHtmlPath, monitorPage)
  }

  // 加载打印队列监控页面
  printQueueWindow.loadFile(monitorHtmlPath)

  // 开发模式下打开开发者工具
  printQueueWindow.webContents.openDevTools()

  // 窗口关闭时清除引用
  printQueueWindow.on('closed', () => {
    printQueueWindow = null
  })
}

/**
 * 生成打印队列监控HTML页面
 * @returns {string} HTML内容
 */
function generatePrintMonitorHTML() {
  return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>打印队列监控 (开发模式)</title>
  <style>
    body { 
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
      padding: 20px; 
      background: #f5f5f5;
    }
    
    .header {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    h1 { 
      color: #1890ff; 
      margin: 0 0 16px 0;
      font-size: 24px;
    }
    
    .dev-badge {
      display: inline-block;
      background: #ff4d4f;
      color: white;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      margin-left: 12px;
      font-weight: 500;
    }
    
    .stats-container {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
    }
    
    .stat-card {
      background: #fafafa;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #e8e8e8;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }
    
    .controls {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    button {
      padding: 8px 16px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }
    
    button:hover {
      background: #f0f0f0;
    }
    
    button.primary {
      background: #1890ff;
      color: white;
      border-color: #1890ff;
    }
    
    button.primary:hover {
      background: #40a9ff;
    }
    
    .tasks-container {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    .tasks-header {
      background: #fafafa;
      padding: 16px;
      border-bottom: 1px solid #e8e8e8;
      font-weight: 600;
    }
    
    .task-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .task-item {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .task-item:last-child {
      border-bottom: none;
    }
    
    .task-info {
      flex: 1;
    }
    
    .task-id {
      font-family: 'Consolas', monospace;
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }
    
    .task-details {
      font-size: 14px;
      color: #333;
    }
    
    .task-status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .status-pending { background: #fff7e6; color: #fa8c16; }
    .status-processing { background: #e6f7ff; color: #1890ff; }
    .status-completed { background: #f6ffed; color: #52c41a; }
    .status-failed { background: #fff2f0; color: #ff4d4f; }
    
    .loading {
      text-align: center;
      padding: 40px;
      color: #666;
    }
    
    .empty {
      text-align: center;
      padding: 40px;
      color: #999;
    }
    
    .note {
      background: #fff7e6;
      border: 1px solid #ffd591;
      border-radius: 4px;
      padding: 12px;
      margin-top: 20px;
      font-size: 14px;
      color: #d48806;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>打印队列监控 <span class="dev-badge">开发模式</span></h1>
    <p>实时监控打印任务队列状态和任务执行情况</p>
  </div>

  <div class="stats-container">
    <div class="stats-grid" id="statsGrid">
      <div class="stat-card">
        <div class="stat-label">总任务数</div>
        <div class="stat-value" id="totalTasks">-</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">待处理</div>
        <div class="stat-value" id="pendingTasks">-</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">处理中</div>
        <div class="stat-value" id="processingTasks">-</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">已完成</div>
        <div class="stat-value" id="completedTasks">-</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">失败</div>
        <div class="stat-value" id="failedTasks">-</div>
      </div>
    </div>
    
    <div class="controls">
      <button class="primary" onclick="refreshData()">刷新数据</button>
      <button onclick="clearQueue()">清空队列</button>
      <button onclick="retryAllFailed()">重试失败任务</button>
    </div>
  </div>

  <div class="tasks-container">
    <div class="tasks-header">
      任务列表
    </div>
    <div class="task-list" id="taskList">
      <div class="loading">正在加载任务数据...</div>
    </div>
  </div>

  <div class="note">
    <strong>注意：</strong> 此功能仅在开发模式下可用，用于调试和监控打印队列状态。
  </div>

  <script>
    let refreshInterval = null;

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      refreshData();
      
      // 自动刷新
      refreshInterval = setInterval(refreshData, 3000);
    });

    // 页面关闭时清理定时器
    window.addEventListener('beforeunload', () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    });

    // 刷新数据
    async function refreshData() {
      try {
        await Promise.all([
          updateQueueStatus(),
          updateTaskList()
        ]);
      } catch (error) {
        console.error('刷新数据失败:', error);
      }
    }

    // 更新队列状态
    async function updateQueueStatus() {
      try {
        if (window.AndroidPrinter && window.AndroidPrinter.getPrintQueueStatus) {
          const status = await window.AndroidPrinter.getPrintQueueStatus();
          
          document.getElementById('totalTasks').textContent = status.total || 0;
          document.getElementById('pendingTasks').textContent = status.pending || 0;
          document.getElementById('processingTasks').textContent = status.processing || 0;
          document.getElementById('completedTasks').textContent = status.completed || 0;
          document.getElementById('failedTasks').textContent = status.failed || 0;
        }
      } catch (error) {
        console.error('获取队列状态失败:', error);
      }
    }

    // 更新任务列表
    async function updateTaskList() {
      try {
        if (window.AndroidPrinter && window.AndroidPrinter.getPrintTasks) {
          const tasks = await window.AndroidPrinter.getPrintTasks();
          renderTaskList(tasks || []);
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
        document.getElementById('taskList').innerHTML = '<div class="empty">获取任务列表失败</div>';
      }
    }

    // 渲染任务列表
    function renderTaskList(tasks) {
      const taskListElement = document.getElementById('taskList');
      
      if (tasks.length === 0) {
        taskListElement.innerHTML = '<div class="empty">暂无任务</div>';
        return;
      }

      const html = tasks.slice(0, 20).map(task => {
        const createdTime = new Date(task.createdAt).toLocaleString();
        const statusClass = 'status-' + task.status.toLowerCase();
        
        return \`
          <div class="task-item">
            <div class="task-info">
              <div class="task-id">ID: \${task.id}</div>
              <div class="task-details">
                打印机: \${task.ip}:\${task.port} | 
                创建时间: \${createdTime} | 
                命令数: \${task.commands ? task.commands.length : 0}
                \${task.retries > 0 ? ' | 重试次数: ' + task.retries : ''}
              </div>
            </div>
            <div class="task-status \${statusClass}">
              \${getStatusText(task.status)}
            </div>
          </div>
        \`;
      }).join('');
      
      taskListElement.innerHTML = html;
    }

    // 获取状态文本
    function getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败',
        'failed-final': '最终失败'
      };
      return statusMap[status] || status;
    }

    // 清空队列
    async function clearQueue() {
      if (!confirm('确定要清空打印队列吗？')) {
        return;
      }
      
      try {
        if (window.AndroidPrinter && window.AndroidPrinter.clearPrintQueue) {
          await window.AndroidPrinter.clearPrintQueue();
          alert('队列已清空');
          refreshData();
        }
      } catch (error) {
        console.error('清空队列失败:', error);
        alert('清空队列失败: ' + error.message);
      }
    }

    // 重试所有失败任务
    async function retryAllFailed() {
      try {
        if (window.AndroidPrinter && window.AndroidPrinter.retryAllFailedPrintTasks) {
          await window.AndroidPrinter.retryAllFailedPrintTasks();
          alert('已重试所有失败任务');
          refreshData();
        }
      } catch (error) {
        console.error('重试失败任务失败:', error);
        alert('重试失败任务失败: ' + error.message);
      }
    }
  </script>
</body>
</html>`
}

/**
 * 获取打印队列监控窗口实例
 * @returns {BrowserWindow|null} 窗口实例
 */
function getPrintQueueWindow() {
  return printQueueWindow
}

/**
 * 关闭打印队列监控窗口
 */
function closePrintQueueWindow() {
  if (printQueueWindow) {
    printQueueWindow.close()
    printQueueWindow = null
  }
}

module.exports = {
  createPrintQueueWindow,
  getPrintQueueWindow,
  closePrintQueueWindow
} 