const net = require('net')
const printQueueManager = require('./printQueue')
const printConsumer = require('./printConsumer')

// 打印机命令常量
const PrintCmd = {
  INIT: [27, 64],                // ESC @ - 初始化打印机
  CHARSET_GBK: [27, 116, 15],    // ESC t 15 - 设置GBK字符集
  CHINESE_MODE: [28, 38],        // FS & - 中文模式
  ALIGN_CENTER: [27, 97, 1],     // ESC a 1 - 居中对齐
  ALIGN_LEFT: [27, 97, 0],       // ESC a 0 - 左对齐
  CUT_PAPER: [29, 86, 49]        // GS V 1 - 切纸
}

module.exports = function setupPrinter(ipcMain) {
  // 启动打印消费者服务
  printConsumer.start()
  console.info('[打印模块] 打印消费者服务已启动')

  // 扫描网络打印机
  ipcMain.handle('scan-printers', async () => {
    try {
      console.debug('[打印模块] 开始扫描打印机')
      // 实现打印机扫描逻辑，或返回预配置的打印机列表
      const printers = [
        // 实际应该通过网络扫描获取
      ]
      console.info('[打印模块] 打印机扫描完成', { printerCount: printers.length })
      return JSON.stringify(printers)
    } catch (error) {
      console.error('[打印模块] 扫描打印机失败', { error: error.message })
      return '[]'
    }
  })

  // 发送原始打印命令 - 使用队列模式
  ipcMain.handle('raw-print', async (event, { ip, port, commandsJson }) => {
    try {
      // 确保参数有效
      if (!ip || !port || !commandsJson) {
        console.error('[打印模块] 打印参数无效', { 
          hasIp: !!ip, 
          hasPort: !!port, 
          hasCommands: !!commandsJson 
        })
        return {
          success: false,
          error: '打印参数无效',
          details: { hasIp: !!ip, hasPort: !!port, hasCommands: !!commandsJson }
        }
      }

      let commands
      try {
        commands = JSON.parse(commandsJson)
      } catch (parseError) {
        console.error('[打印模块] 解析打印命令失败', { error: parseError.message })
        return { success: false, error: '解析打印命令失败: ' + parseError.message }
      }

      console.info('[打印模块] 添加打印任务到队列', { 
        printer: `${ip}:${port}`,
        commandCount: commands.length 
      })

      // 添加到打印队列
      const taskId = printQueueManager.addTask({
        ip,
        port,
        commands
      })

      // 立即返回任务ID，不等待打印完成
      return { success: true, taskId }
    } catch (error) {
      console.error('[打印模块] 添加打印任务失败', { error: error.message })
      return { success: false, error: error.message }
    }
  })

  // 测试打印 - 使用队列模式
  ipcMain.handle('print-test', async (event, { ip, port, testContent }) => {
    try {
      // 使用传入的测试内容，如果没有则使用默认内容
      const content = testContent || '打印机测试页';

      console.info('[打印模块] 创建测试打印任务', { 
        printer: `${ip}:${port}`,
        testContent: content 
      })

      const commands = [
        { type: 'command', data: PrintCmd.INIT }, // 初始化打印机
        { type: 'command', data: PrintCmd.CHARSET_GBK }, // 设置GBK字符集
        { type: 'command', data: PrintCmd.CHINESE_MODE }, // 设置中文模式
        { type: 'command', data: PrintCmd.ALIGN_CENTER }, // 居中对齐
        { type: 'text', data: '\n打印机测试页\n----------------\n' },
        { type: 'text', data: `IP: ${ip}\n` },
        { type: 'text', data: `端口: ${port}\n` },
        { type: 'text', data: `时间: ${new Date().toLocaleString()}\n` },
        { type: 'text', data: `内容: ${content}\n` },
        { type: 'text', data: '----------------\n\n\n' },
        { type: 'command', data: PrintCmd.CUT_PAPER } // 切纸
      ]

      // 添加到打印队列
      const taskId = printQueueManager.addTask({
        ip,
        port,
        commands
      })

      return { success: true, taskId }
    } catch (error) {
      console.error('[打印模块] 测试打印失败', { 
        printer: `${ip}:${port}`,
        error: error.message 
      })
      return { success: false, error: error.message }
    }
  })

  // 获取打印队列状态
  ipcMain.handle('get-print-queue-status', () => {
    console.debug('[打印模块] 获取打印队列状态')
    return printQueueManager.getQueueStatus()
  })

  // 获取所有打印任务
  ipcMain.handle('get-print-tasks', () => {
    console.debug('[打印模块] 获取所有打印任务')
    return printQueueManager.getAllTasks()
  })

  // 重试失败的打印任务
  ipcMain.handle('retry-print-task', (event, { taskId }) => {
    console.info('[打印模块] 重试失败的打印任务', { taskId })
    return printQueueManager.retryFailedTask(taskId)
  })

  // 重试所有失败的打印任务
  ipcMain.handle('retry-all-failed-print-tasks', () => {
    console.info('[打印模块] 重试所有失败的打印任务')
    return printQueueManager.retryFailedTask()
  })

  // 清空打印队列
  ipcMain.handle('clear-print-queue', () => {
    console.info('[打印模块] 清空打印队列')
    printQueueManager.clearQueue()
    return { success: true }
  })
}