/**
 * 打印模块统一入口
 * 导出打印机、队列管理器和消费者
 */

const setupPrinter = require('./printer')
const printQueueManager = require('./printQueue')
const printConsumer = require('./printConsumer')
const { createPrintQueueWindow, getPrintQueueWindow, closePrintQueueWindow } = require('./printMonitor')

module.exports = {
  // 主要的打印设置函数 - 用于IPC注册
  setupPrinter,
  
  // 打印队列管理器实例
  printQueueManager,
  
  // 打印消费者实例
  printConsumer,
  
  // 便捷方法
  addPrintTask: (task) => printQueueManager.addTask(task),
  getQueueStatus: () => printQueueManager.getQueueStatus(),
  getAllTasks: () => printQueueManager.getAllTasks(),
  clearQueue: () => printQueueManager.clearQueue(),
  retryFailedTask: (taskId) => printQueueManager.retryFailedTask(taskId),
  
  // 消费者控制
  startConsumer: () => printConsumer.start(),
  stopConsumer: () => printConsumer.stop(),
  
  // 打印队列监控窗口管理
  createPrintQueueWindow,
  getPrintQueueWindow,
  closePrintQueueWindow
} 