/**
 * HTTP服务器核心实现
 * 
 * 功能职责：
 * - 创建和管理HTTP服务器
 * - 处理HTTP请求
 * - 与渲染进程通信
 * - 请求/响应转发
 */

const http = require('http')
const { URL } = require('url')
const crypto = require('crypto')

// 服务器实例和配置
let httpServer = null
let serverConfig = {
  port: 8080,
  host: 'localhost'
}

// 待处理的请求队列 - 存储等待渲染进程响应的请求
const pendingRequests = new Map()

// 渲染进程实例引用（主窗口的webContents）
let rendererProcess = null

/**
 * 设置渲染进程引用
 * @param {Electron.WebContents} webContents - 主窗口的webContents
 */
function setRendererProcess(webContents) {
  rendererProcess = webContents
  console.log('[HTTP-SERVER] 渲染进程引用已设置')
}

/**
 * 提取请求常量
 */
const REQUEST_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']
const ALLOWED_METHODS = ['GET', 'POST']

/**
 * 生成请求ID
 */
function generateRequestId() {
  return crypto.randomUUID()
}

/**
 * 解析请求体
 */
async function parseRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = ''
    
    req.on('data', chunk => {
      body += chunk.toString()
    })
    
    req.on('end', () => {
      try {
        // 尝试解析JSON，失败则返回原始字符串
        const contentType = req.headers['content-type'] || ''
        if (contentType.includes('application/json') && body) {
          resolve(JSON.parse(body))
        } else {
          resolve(body)
        }
      } catch (error) {
        console.warn('[HTTP-SERVER] 解析请求体失败，返回原始字符串:', error.message)
        resolve(body)
      }
    })
    
    req.on('error', reject)
  })
}

/**
 * 构建请求对象
 */
function buildRequestObject(req, body, requestId) {
  const parsedUrl = new URL(req.url, `http://${req.headers.host}`)
  
  return {
    id: requestId,
    method: req.method,
    url: req.url,
    pathname: parsedUrl.pathname,
    search: parsedUrl.search,
    query: Object.fromEntries(parsedUrl.searchParams),
    headers: req.headers,
    body: body,
    timestamp: Date.now()
  }
}

/**
 * 设置CORS头
 */
function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
}

/**
 * 处理OPTIONS请求
 */
function handleOptionsRequest(res) {
  setCorsHeaders(res)
  res.writeHead(200)
  res.end()
}

/**
 * 处理请求超时
 */
function handleRequestTimeout(requestId, res) {
  if (pendingRequests.has(requestId)) {
    pendingRequests.delete(requestId)
    console.warn(`[HTTP-SERVER] 请求超时: ${requestId}`)
    
    if (!res.headersSent) {
      setCorsHeaders(res)
      res.writeHead(504, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({
        error: 'Request timeout',
        code: 504,
        message: '请求处理超时'
      }))
    }
  }
}

/**
 * HTTP请求处理器
 */
async function requestHandler(req, res) {
  const requestId = generateRequestId()
  
  console.log(`[HTTP-SERVER] 收到请求: ${req.method} ${req.url} [ID: ${requestId}]`)
  
  try {
    // 设置CORS头
    setCorsHeaders(res)
    
    // 处理OPTIONS预检请求
    if (req.method === 'OPTIONS') {
      return handleOptionsRequest(res)
    }
    
    // 方法过滤：只允许GET和POST方法
    if (!ALLOWED_METHODS.includes(req.method)) {
      console.warn(`[HTTP-SERVER] 方法不被允许: ${req.method} ${req.url} [ID: ${requestId}]`)
      res.writeHead(405, { 
        'Content-Type': 'application/json',
        'Allow': ALLOWED_METHODS.join(', ')
      })
      res.end(JSON.stringify({
        error: 'Method not allowed',
        code: 405,
        message: `方法 ${req.method} 不被允许，仅支持: ${ALLOWED_METHODS.join(', ')}`,
        allowedMethods: ALLOWED_METHODS
      }))
      return
    }
    
    // 检查渲染进程是否可用
    if (!rendererProcess || rendererProcess.isDestroyed()) {
      console.error('[HTTP-SERVER] 渲染进程不可用')
      res.writeHead(503, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({
        error: 'Service unavailable',
        code: 503,
        message: '渲染进程不可用'
      }))
      return
    }
    
    // 解析请求体
    const body = await parseRequestBody(req)
    
    // 构建请求对象
    const requestObject = buildRequestObject(req, body, requestId)
    
    // 存储待处理的请求
    pendingRequests.set(requestId, { req, res, timestamp: Date.now() })
    
    // 设置请求超时（30秒）
    const timeout = setTimeout(() => {
      handleRequestTimeout(requestId, res)
    }, 30000)
    
    // 将超时器存储到请求中，以便稍后清除
    pendingRequests.get(requestId).timeout = timeout
    
    // 发送请求到渲染进程
    rendererProcess.send('http-request', requestObject)
    
    console.log(`[HTTP-SERVER] 请求已转发到渲染进程: ${requestId}`)
    
  } catch (error) {
    console.error(`[HTTP-SERVER] 处理请求失败 [ID: ${requestId}]:`, error)
    
    // 清理待处理请求
    if (pendingRequests.has(requestId)) {
      const pending = pendingRequests.get(requestId)
      if (pending.timeout) {
        clearTimeout(pending.timeout)
      }
      pendingRequests.delete(requestId)
    }
    
    if (!res.headersSent) {
      setCorsHeaders(res)
      res.writeHead(500, { 'Content-Type': 'application/json' })
      res.end(JSON.stringify({
        error: 'Internal server error',
        code: 500,
        message: '服务器内部错误'
      }))
    }
  }
}

/**
 * 处理渲染进程返回的响应
 * @param {Object} response - 渲染进程返回的响应对象
 */
function handleRendererResponse(response) {
  const { requestId, statusCode = 200, headers = {}, body = '', error } = response
  
  console.log(`[HTTP-SERVER] 收到渲染进程响应: ${requestId}`)
  
  if (!pendingRequests.has(requestId)) {
    console.warn(`[HTTP-SERVER] 找不到对应的待处理请求: ${requestId}`)
    return
  }
  
  const pending = pendingRequests.get(requestId)
  const { res, timeout } = pending
  
  // 清除超时器
  if (timeout) {
    clearTimeout(timeout)
  }
  
  // 移除待处理请求
  pendingRequests.delete(requestId)
  
  try {
    if (!res.headersSent) {
      // 设置CORS头
      setCorsHeaders(res)
      
      // 设置自定义响应头
      Object.entries(headers).forEach(([key, value]) => {
        res.setHeader(key, value)
      })
      
      // 如果有错误，返回错误响应
      if (error) {
        console.error(`[HTTP-SERVER] 渲染进程返回错误: ${requestId}`, error)
        res.writeHead(error.statusCode || 500, { 'Content-Type': 'application/json' })
        res.end(JSON.stringify({
          error: error.message || 'Internal error',
          code: error.statusCode || 500,
          ...error
        }))
        return
      }
      
      // 设置内容类型
      if (!headers['Content-Type'] && !headers['content-type']) {
        if (typeof body === 'object') {
          res.setHeader('Content-Type', 'application/json')
        } else {
          res.setHeader('Content-Type', 'text/plain')
        }
      }
      
      // 发送响应
      res.writeHead(statusCode)
      res.end(typeof body === 'object' ? JSON.stringify(body) : String(body))
      
      console.log(`[HTTP-SERVER] 响应已发送: ${requestId}, 状态码: ${statusCode}`)
    }
  } catch (error) {
    console.error(`[HTTP-SERVER] 发送响应失败: ${requestId}`, error)
  }
}

/**
 * 创建HTTP服务器
 * @param {Object} options - 服务器配置选项
 * @param {number} options.port - 端口号
 * @param {string} options.host - 主机地址
 * @param {Electron.WebContents} options.rendererProcess - 渲染进程引用
 */
function createHttpServer(options = {}) {
  return new Promise((resolve, reject) => {
    if (httpServer) {
      console.log('[HTTP-SERVER] 服务器已存在，停止现有服务器')
      stopHttpServer()
    }
    
    // 更新配置
    serverConfig = {
      ...serverConfig,
      ...options
    }
    
    // 设置渲染进程引用
    if (options.rendererProcess) {
      setRendererProcess(options.rendererProcess)
    }
    
    try {
      // 创建HTTP服务器
      httpServer = http.createServer(requestHandler)
      
      // 监听端口
      httpServer.listen(serverConfig.port, () => {
        console.log(`[HTTP-SERVER] 服务器启动成功: http://${serverConfig.host}:${serverConfig.port}`)
        resolve({
          port: serverConfig.port,
          url: `http://${serverConfig.host}:${serverConfig.port}`
        })
      })
      
      // 错误处理
      httpServer.on('error', (error) => {
        console.error('[HTTP-SERVER] 服务器错误:', error)
        reject(error)
      })
      
    } catch (error) {
      console.error('[HTTP-SERVER] 创建服务器失败:', error)
      reject(error)
    }
  })
}

/**
 * 停止HTTP服务器
 */
function stopHttpServer() {
  return new Promise((resolve) => {
    if (!httpServer) {
      console.log('[HTTP-SERVER] 服务器未运行')
      resolve()
      return
    }
    
    console.log('[HTTP-SERVER] 正在停止服务器...')
    
    // 清理所有待处理的请求
    for (const [requestId, pending] of pendingRequests) {
      if (pending.timeout) {
        clearTimeout(pending.timeout)
      }
      
      if (pending.res && !pending.res.headersSent) {
        try {
          pending.res.writeHead(503, { 'Content-Type': 'application/json' })
          pending.res.end(JSON.stringify({
            error: 'Service shutting down',
            code: 503,
            message: '服务器正在关闭'
          }))
        } catch (error) {
          console.warn('[HTTP-SERVER] 清理待处理请求时出错:', error.message)
        }
      }
    }
    
    pendingRequests.clear()
    
    // 关闭服务器
    httpServer.close(() => {
      console.log('[HTTP-SERVER] 服务器已停止')
      httpServer = null
      rendererProcess = null
      resolve()
    })
  })
}

/**
 * 获取服务器状态
 */
function getServerStatus() {
  return {
    running: !!httpServer,
    config: serverConfig,
    pendingRequests: pendingRequests.size,
    url: httpServer ? `http://${serverConfig.host}:${serverConfig.port}` : null
  }
}

module.exports = {
  createHttpServer,
  stopHttpServer,
  getServerStatus,
  handleRendererResponse,
  setRendererProcess
} 