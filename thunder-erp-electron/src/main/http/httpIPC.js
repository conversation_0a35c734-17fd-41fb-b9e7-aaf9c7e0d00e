/**
 * HTTP模块IPC处理器
 * 
 * 功能职责：
 * - 注册HTTP相关的IPC处理程序
 * - 处理渲染进程的HTTP服务器控制请求
 * - 处理渲染进程返回的HTTP响应
 */

const { handleRendererResponse, getServerStatus } = require('./httpServer')

// 跟踪IPC处理器是否已经注册
let ipcHandlersRegistered = false

/**
 * 设置HTTP相关的IPC处理程序
 * @param {Electron.IpcMain} ipcMain - IPC主进程实例
 */
function setupHttpIPC(ipcMain) {
  // 防止重复注册
  if (ipcHandlersRegistered) {
    console.log('[HTTP-IPC] IPC处理程序已注册，跳过重复注册')
    return
  }
  
  console.log('[HTTP-IPC] 注册HTTP相关IPC处理程序')
  
  try {
    // 清理可能存在的处理器，防止重复注册错误
    try {
      ipcMain.removeHandler('http-response')
      ipcMain.removeHandler('http-get-server-status')
      ipcMain.removeHandler('http-server-control')
    } catch (cleanupError) {
      // 忽略清理错误，这些处理器可能不存在
    }
    
    // 处理渲染进程返回的HTTP响应
    ipcMain.handle('http-response', async (event, response) => {
      console.log(`[HTTP-IPC] 收到渲染进程HTTP响应: ${response.requestId}`)
      
      try {
        handleRendererResponse(response)
        return { success: true }
      } catch (error) {
        console.error('[HTTP-IPC] 处理HTTP响应失败:', error)
        return { 
          success: false, 
          error: error.message 
        }
      }
    })
    
    // 获取HTTP服务器状态
    ipcMain.handle('http-get-server-status', async () => {
      try {
        const status = getServerStatus()
        console.log('[HTTP-IPC] 返回服务器状态:', status)
        return { success: true, data: status }
      } catch (error) {
        console.error('[HTTP-IPC] 获取服务器状态失败:', error)
        return { 
          success: false, 
          error: error.message 
        }
      }
    })
    
    // 处理HTTP服务器控制命令（预留接口）
    ipcMain.handle('http-server-control', async (event, { action, options = {} }) => {
      console.log(`[HTTP-IPC] 收到服务器控制命令: ${action}`)
      
      try {
        switch (action) {
          case 'status':
            const status = getServerStatus()
            return { success: true, data: status }
            
          case 'restart':
            // 重启功能可以在这里实现
            console.log('[HTTP-IPC] 服务器重启功能待实现')
            return { success: false, error: '重启功能待实现' }
            
          default:
            return { 
              success: false, 
              error: `未知的控制命令: ${action}` 
            }
        }
      } catch (error) {
        console.error(`[HTTP-IPC] 执行控制命令失败 [${action}]:`, error)
        return { 
          success: false, 
          error: error.message 
        }
      }
    })
  
    // 标记为已注册
    ipcHandlersRegistered = true
    console.log('[HTTP-IPC] HTTP IPC处理程序注册完成')
  } catch (error) {
    console.error('[HTTP-IPC] 注册IPC处理程序失败:', error)
    // 重置标志，允许重试
    ipcHandlersRegistered = false
    throw error
  }
}

module.exports = {
  setupHttpIPC
} 