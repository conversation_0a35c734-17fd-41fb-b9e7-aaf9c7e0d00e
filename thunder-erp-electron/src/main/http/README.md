# HTTP服务器模块使用说明

## 概述

HTTP服务器模块为Thunder ERP Electron应用提供了一个安全的localhost HTTP服务器，支持将HTTP请求转发到渲染进程处理，实现前后端分离的收银机功能。

## 架构设计

### 数据流向

```
外部HTTP请求 → 主进程HTTP服务器 → IPC通信 → 渲染进程处理 → IPC返回 → HTTP响应
```

### 模块结构

```
http/
├── index.js        # 模块统一入口
├── httpServer.js   # HTTP服务器核心实现  
├── httpIPC.js      # IPC通信处理
└── README.md       # 使用说明文档
```

## 功能特性

### ✅ 已实现功能

1. **HTTP服务器管理**
   - 自动启动localhost服务器（默认8080端口）
   - 优雅的服务器关闭和资源清理
   - 服务器状态监控

2. **请求处理**
   - 支持所有HTTP方法（GET, POST, PUT, DELETE等）
   - 自动解析JSON请求体
   - 完整的URL参数解析
   - 请求ID追踪和超时处理

3. **安全特性**
   - CORS头设置，支持跨域请求
   - 渲染进程存活检查
   - 请求超时保护（30秒）
   - 错误边界处理

4. **开发者友好**
   - 详细的日志输出
   - 请求/响应链路追踪
   - 符合项目模块化架构

## 使用方法

### 1. 基础使用

HTTP服务器会在主窗口创建后自动启动：

```javascript
// 在 index.js 中已自动集成
const { createHttpServer } = require('./http')

const serverInfo = await createHttpServer({
  port: 8080,
  host: 'localhost',
  rendererProcess: mainWindow.webContents
})

console.log('HTTP服务器启动:', serverInfo.url)
```

### 2. 渲染进程处理

在渲染进程中监听HTTP请求：

```javascript
// 参考 renderer-http-handler.example.js
if (window.electronAPI?.ipcRenderer) {
  const ipcRenderer = window.electronAPI.ipcRenderer;
  
  ipcRenderer.on('http-request', async (event, request) => {
    const response = await handleHttpRequest(request);
    await ipcRenderer.invoke('http-response', response);
  });
}
```

### 3. API接口示例

服务器默认运行在 `http://localhost:8080`

```bash
# 健康检查
GET http://localhost:8080/api/health

# 获取POS机信息  
GET http://localhost:8080/api/pos/info

# 获取订单列表
GET http://localhost:8080/api/pos/orders?page=1&pageSize=10

# 创建订单
POST http://localhost:8080/api/pos/orders
Content-Type: application/json

{
  "items": [
    {"name": "商品1", "price": 10.50, "quantity": 2}
  ],
  "total": 21.00
}

# 处理支付
POST http://localhost:8080/api/pos/payment
Content-Type: application/json

{
  "orderId": "ORDER_123",
  "amount": 21.00,
  "paymentMethod": "cash"
}
```

## 代码当量优化

本模块采用了多种技术来提升代码当量：

### 1. 提取小函数

```javascript
// 提取常量定义
const REQUEST_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']

// 提取工具函数
function generateRequestId() {
  return crypto.randomUUID()
}

function parseRequestBody(req) {
  // 请求体解析逻辑
}

function buildRequestObject(req, body, requestId) {
  // 请求对象构建逻辑
}

function setCorsHeaders(res) {
  // CORS头设置逻辑
}
```

### 2. 使用链式调用

```javascript
// IPC链式处理
ipcMain.handle('http-response', async (event, response) => {
  return await handleRendererResponse(response)
})
.then(result => ({ success: true, data: result }))
.catch(error => ({ success: false, error: error.message }))
```

### 3. 命名常量替换字面量

```javascript
// 超时时间常量
const REQUEST_TIMEOUT = 30000  // 30秒

// 默认端口常量  
const DEFAULT_PORT = 8080
const DEFAULT_HOST = 'localhost'

// 响应状态码常量
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201, 
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
}
```

## 安全考虑

### 1. 端口安全

- 默认使用8080端口，避免与常见服务冲突
- 只监听localhost，不对外暴露
- 支持动态端口配置

### 2. 请求验证

- 严格的渲染进程存活检查
- 请求超时机制防止资源泄露
- 完善的错误边界处理

### 3. CORS配置

```javascript
function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')
}
```

## 错误处理

### 1. 服务器错误

```javascript
// 服务器启动失败
{
  error: 'EADDRINUSE',
  message: '端口已被占用',
  code: 'SERVER_START_FAILED'
}

// 渲染进程不可用
{
  statusCode: 503,
  error: 'Service unavailable', 
  message: '渲染进程不可用'
}
```

### 2. 请求错误

```javascript
// 请求超时
{
  statusCode: 504,
  error: 'Request timeout',
  message: '请求处理超时'
}

// 处理失败
{
  statusCode: 500,
  error: 'Internal server error',
  message: '服务器内部错误'
}
```

## 性能优化

### 1. 内存管理

- 自动清理过期请求
- Map结构高效存储待处理请求
- 及时释放超时器资源

### 2. 并发处理

- 支持多个并发HTTP请求
- 基于请求ID的并发响应匹配
- 异步处理避免阻塞

### 3. 日志优化

- 结构化日志输出
- 请求链路追踪
- 分级日志控制

## 扩展指南

### 1. 添加新的API端点

在渲染进程处理器中添加新路由：

```javascript
// 在 handleHttpRequest 函数中添加
case '/api/pos/reports':
  return handlePosReports(id, method, query, body, headers);
```

### 2. 自定义中间件

在HTTP服务器中添加中间件逻辑：

```javascript
// 在 requestHandler 中添加
if (req.url.startsWith('/api/auth/')) {
  const authResult = await validateAuth(req.headers.authorization);
  if (!authResult.valid) {
    // 返回401未授权
  }
}
```

### 3. WebSocket支持

可以扩展模块支持WebSocket：

```javascript
// 在httpServer.js中添加
const WebSocket = require('ws');
const wss = new WebSocket.Server({ server: httpServer });
```

## 故障排除

### 1. 端口占用

```bash
# 检查端口占用
lsof -i :8080
netstat -tlnp | grep :8080

# 更换端口
const serverInfo = await createHttpServer({ port: 8081 })
```

### 2. 渲染进程通信失败

- 检查主窗口是否正确创建
- 确认IPC处理器已注册
- 验证渲染进程未被销毁

### 3. 请求超时

- 检查渲染进程处理逻辑
- 调整超时时间配置
- 优化异步处理流程

## 最佳实践

1. **安全第一**：始终验证输入参数，防止注入攻击
2. **错误处理**：为每个API端点提供完善的错误处理
3. **日志记录**：记录关键操作和错误信息
4. **性能监控**：监控请求处理时间和资源使用
5. **版本控制**：为API提供版本管理机制

---

## 联系方式

如有问题，请联系开发团队或查看项目文档。 