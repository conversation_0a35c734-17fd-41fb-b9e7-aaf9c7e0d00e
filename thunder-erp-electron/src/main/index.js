const { app, BrowserWindow, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const API_BASE_URL = process.env.API_BASE_URL || 'https://api.yourhost.com';
const CHECK_URL = API_BASE_URL + '/api/app-upgrade/version';

let upgradeMeta = {
  h5versionCode: 'latest',
  h5versionName: 'unknown',
  hasUpgrade: false,
  forceUpgrade: false
};

async function fetchUpgradeInfo() {
  try {
    const pkg = require('../../package.json');
    const versionCode = parseInt((pkg.version || '1').replace(/\D/g, ''), 10) || 1;
    const clientType = process.platform === 'win32' ? 'CASHIER_WINDOWS' : 'CASHIER_MAC';
    const res = await axios.post(CHECK_URL, { clientType, versionCode });
    if (res.data && res.data.code === 200) {
      const data = res.data.data || {};
      upgradeMeta = {
        h5versionCode: data.currentVersion?.h5versionCode || 'latest',
        h5versionName: data.currentVersion?.h5versionName || 'unknown',
        hasUpgrade: data.hasUpgrade || false,
        forceUpgrade: data.forceUpgrade || false
      };
      console.log('升级检查结果:', upgradeMeta);
    }
  } catch (err) {
    console.error('fetchUpgradeInfo error:', err.message);
  }
}

// HTTP服务器模块移除 - 使用模块化的HTTP服务器

// 导入日志管理器 - 必须在其他模块之前导入以确保日志捕获
const { logger } = require('./logging');

// 解析命令行参数获取clientUrl
function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  let clientUrlFromArgs = null;

  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--client-url' && i + 1 < args.length) {
      clientUrlFromArgs = args[i + 1];
      console.log('从命令行参数获取clientUrl:', clientUrlFromArgs);
      break;
    }
  }

  return clientUrlFromArgs;
}

// 从命令行参数获取客户端URL
const clientUrlFromArgs = parseCommandLineArgs();

const previewClientUrl = 'https://merpdev-stage.ktvsky.com';
const productionClientUrl = 'https://merp.ktvsky.com';

// 配置文件路径
const configPath = path.join(app.getPath('userData'), 'app-config.json');

// 检查是否为预览环境构建 - 只依赖.preview-build文件
const isPreviewBuild = fs.existsSync(path.join(__dirname, '../../.preview-build'));
console.log('是否预览版本(通过.preview-build文件检测):', isPreviewBuild);

// 开发模式也使用预览环境
const isDevMode = process.env.NODE_ENV === 'development';
if (isDevMode) {
  console.log('开发模式下使用预览环境');
}

// 全局窗口引用
let mainWindow = null;

// 获取配置的URL
function getConfiguredUrl() {
  try {
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      return config.clientUrl || productionClientUrl;
    }
  } catch (error) {
    console.error('读取配置失败:', error);
  }
  return isPreviewBuild ? previewClientUrl : productionClientUrl;
}

// 保存URL配置
function saveUrlConfig(url) {
  try {
    const config = fs.existsSync(configPath) ? JSON.parse(fs.readFileSync(configPath, 'utf8')) : {};

    config.clientUrl = url;
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    console.log('已保存URL配置:', url);
  } catch (error) {
    console.error('保存配置失败:', error);
  }
}

// 显示环境选择对话框
function showEnvironmentSelector() {
  // 如果不是预览构建，直接使用生产URL
  if (!isPreviewBuild) {
    return productionClientUrl;
  }

  // 为预览版提供环境选择
  const selection = dialog.showMessageBoxSync({
    type: 'question',
    title: 'Thunder ERP - 环境选择',
    message: '请选择要连接的环境:',
    buttons: ['预览环境', '生产环境'],
    defaultId: 0,
    cancelId: 0,
    noLink: true
  });

  // 根据选择返回相应URL
  const selectedUrl = selection === 0 ? previewClientUrl : productionClientUrl;
  saveUrlConfig(selectedUrl);
  return selectedUrl;
}

// 创建窗口
function createWindow() {
  // 导入核心窗口管理模块
  const { createMainWindow, openDevTools } = require('./core');
  const { createLogViewerWindow } = require('./logging');
  const { createPrintQueueWindow } = require('./printing');

  // 优先级：命令行参数 > 配置文件 > 环境选择对话框 > 默认URL
  let clientUrl;

  // 如果命令行有指定clientUrl，直接使用
  if (clientUrlFromArgs) {
    clientUrl = clientUrlFromArgs;
    console.log('使用命令行参数指定的URL:', clientUrl);
  } else if (isPreviewBuild) {
    // 默认使用配置文件中的URL，如果没有配置，则显示选择对话框
    try {
      if (fs.existsSync(configPath)) {
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        if (config.clientUrl) {
          clientUrl = config.clientUrl;
        } else {
          clientUrl = showEnvironmentSelector();
        }
      } else {
        clientUrl = showEnvironmentSelector();
      }
    } catch (error) {
      console.error('读取配置失败，显示选择对话框:', error);
      clientUrl = showEnvironmentSelector();
    }
  } else {
    clientUrl = productionClientUrl;
  }

  // 创建主窗口
  mainWindow = createMainWindow({
    clientUrl,
    isPreviewBuild,
    createLogViewerWindow,
    createPrintQueueWindow,
    openDevTools,
    getConfiguredUrl,
    saveUrlConfig,
    previewClientUrl,
    productionClientUrl,
    h5versionCode: upgradeMeta.h5versionCode,
    h5versionName: upgradeMeta.h5versionName,
    hasUpgrade: upgradeMeta.hasUpgrade,
    forceUpgrade: upgradeMeta.forceUpgrade
  });

  // 启动HTTP服务器，传入渲染进程引用
  if (mainWindow && mainWindow.webContents) {
    console.log('正在启动HTTP服务器...');
    const { createHttpServer } = require('./http');

    // 等待主窗口加载完成后启动HTTP服务器
    mainWindow.webContents.once('dom-ready', async () => {
      try {
        const serverInfo = await createHttpServer({
          port: 8080,
          host: 'localhost',
          rendererProcess: mainWindow.webContents
        });
        console.log('HTTP服务器启动成功:', serverInfo);
      } catch (error) {
        console.error('HTTP服务器启动失败:', error);
      }
    });
  }

  return mainWindow;
}

// 设置基本IPC处理
function setupBasicIPC() {
  // 导入核心模块的设备信息功能
  const { getIpAddress, getMacAddress, getMachineCode } = require('./core');

  // 获取设备ID（主机名）
  ipcMain.handle('get-device-id', () => {
    const os = require('os');
    return os.hostname();
  });

  // 获取IP地址
  ipcMain.handle('get-ip-address', () => {
    return getIpAddress();
  });

  // 获取MAC地址
  ipcMain.handle('get-mac-address', () => {
    return getMacAddress();
  });

  // 获取机器码
  ipcMain.handle('get-machine-code', () => {
    return getMachineCode();
  });

  // 获取平台信息
  ipcMain.handle('get-platform-info', () => {
    const os = require('os');
    return {
      platform: process.platform,
      arch: process.arch,
      version: app.getVersion(),
      osVersion: os.release(),
      osName: os.type()
    };
  });

  // 退出应用
  ipcMain.handle('exit-app', () => {
    app.quit();
  });

  // 重启应用
  ipcMain.handle('restart-app', () => {
    app.relaunch();
    app.exit();
  });

  // 添加日志相关的IPC处理
  setupLoggerIPC();
}

// 设置日志管理器的IPC处理
function setupLoggerIPC() {
  // 获取日志
  ipcMain.handle('logger-get-logs', (event, filter = {}) => {
    try {
      // 优先从内存获取最新日志
      const memoryLogs = logger.getMemoryLogs(filter);

      if (memoryLogs.length > 0) {
        return { success: true, data: memoryLogs };
      }

      // 如果内存中没有日志，从文件读取
      const fileLogs = logger.readLogFile({ filter });
      return { success: true, data: fileLogs };
    } catch (error) {
      console.error('获取日志失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 清空日志
  ipcMain.handle('logger-clear-logs', () => {
    try {
      logger.clearLogs();
      return { success: true };
    } catch (error) {
      console.error('清空日志失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 获取日志统计信息
  ipcMain.handle('logger-get-stats', () => {
    try {
      const stats = logger.getLogStats();
      return { success: true, data: stats };
    } catch (error) {
      console.error('获取日志统计失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 设置日志级别
  ipcMain.handle('logger-set-level', (event, level) => {
    try {
      logger.setLogLevel(level);
      return { success: true };
    } catch (error) {
      console.error('设置日志级别失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 导出日志
  ipcMain.handle('logger-export-logs', async () => {
    try {
      const result = await dialog.showSaveDialog(mainWindow, {
        title: '导出日志',
        defaultPath: `thunder-erp-logs-${new Date().toISOString().slice(0, 10)}.txt`,
        filters: [
          { name: '文本文件', extensions: ['txt'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      });

      if (result.canceled) {
        return { success: false, error: '用户取消' };
      }

      // 读取所有日志并导出
      const logs = logger.readLogFile({ lines: 10000 });
      const content = logs.map(log => `[${log.timestamp}] [${log.level}] [PID:${log.pid}] ${log.message}`).join('\n');

      fs.writeFileSync(result.filePath, content, 'utf8');
      return { success: true, filePath: result.filePath };
    } catch (error) {
      console.error('导出日志失败:', error);
      return { success: false, error: error.message };
    }
  });
}

// 注册全局快捷键
function registerGlobalShortcuts() {
  // 只在开发模式下注册打印队列监控快捷键
  if (process.env.NODE_ENV === 'development') {
    const { createPrintQueueWindow } = require('./printing');

    // 添加打印队列监控快捷键处理
    ipcMain.handle('open-print-queue-monitor', () => {
      createPrintQueueWindow();
      return true;
    });
    console.log('已注册打印队列监控快捷键 (仅开发模式)');
  }
}

// 应用初始化
app.whenReady().then(async () => {
  // 先拉取升级信息
  await fetchUpgradeInfo();
  try {
    // 首先设置基本IPC处理函数
    setupBasicIPC();

    // 加载非核心功能模块 - 在创建窗口之前加载，确保IPC处理程序已注册
    try {
      // 先加载VOD模块，确保VOD相关的IPC处理程序已注册
      console.log('正在加载VOD模块...');
      const { setupVod } = require('./vod');
      if (setupVod) {
        setupVod(ipcMain);
        console.log('VOD模块加载成功');
      } else {
        console.error('VOD模块加载失败: 模块为空');
      }

      // 再加载打印模块
      console.log('正在加载打印模块...');
      const { setupPrinter } = require('./printing');
      if (setupPrinter) {
        setupPrinter(ipcMain);
        console.log('打印模块加载成功');
      }

      // 加载HTTP服务器模块
      console.log('正在加载HTTP服务器模块...');
      const { setupHttpIPC } = require('./http');
      if (setupHttpIPC) {
        setupHttpIPC(ipcMain);
        console.log('HTTP服务器模块加载成功');
      }

      // 最后加载升级模块
      console.log('正在加载升级模块...');
      const { setupUpdater } = require('./updater');
      if (setupUpdater) {
        setupUpdater(ipcMain, mainWindow);
        console.log('升级模块加载成功');
      }

      console.log('所有模块加载完成');
    } catch (err) {
      console.error('加载模块失败:', err.stack || err);
    }

    // 创建窗口
    createWindow();

    // 注册全局快捷键
    registerGlobalShortcuts();

    // macOS 应用激活时创建窗口
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
  } catch (error) {
    console.error('应用初始化失败:', error);
  }
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// 应用退出前清理资源
app.on('before-quit', async () => {
  console.log('应用正在退出，清理资源...');

  try {
    // 停止HTTP服务器
    const { stopHttpServer } = require('./http');
    await stopHttpServer();
    console.log('HTTP服务器已停止');
  } catch (error) {
    console.error('停止HTTP服务器时出错:', error);
  }
});
