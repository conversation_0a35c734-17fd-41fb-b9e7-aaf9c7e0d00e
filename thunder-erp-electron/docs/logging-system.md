# Thunder ERP 日志系统

Thunder ERP 客户端集成了完整的日志收集和管理功能，支持本地存储、日志轮转、级别过滤和实时查看。

## 主要功能

### 🚀 核心特性

- **多级别日志**: ERROR、WARN、INFO、DEBUG 四个级别
- **本地存储**: 日志自动保存到本地文件，支持最大 50MB 存储
- **日志轮转**: 采用 FIFO 模式，自动轮转和清理旧日志
- **实时查看**: 开发/预览模式下提供图形化日志查看器
- **智能过滤**: 支持按级别、关键词、模块、时间范围过滤
- **环境适配**: 根据不同环境自动调整日志级别和行为

### 🛠️ 技术特性

- **性能优化**: 异步写入，内存缓冲机制
- **数据安全**: 生产环境下自动净化敏感信息
- **异常捕获**: 自动捕获未处理异常和 Promise 拒绝
- **模块化**: 支持模块级别的日志控制
- **可配置**: 丰富的配置选项适应不同需求

## 环境配置

### 日志级别策略

```javascript
// 开发环境 (NODE_ENV=development)
logLevel: DEBUG  // 显示所有日志

// 预览环境 (.preview-build 文件存在)
logLevel: INFO   // 显示 INFO 及以上级别

// 生产环境 (默认)
logLevel: WARN   // 仅显示 WARN 和 ERROR
```

### 存储配置

```javascript
{
  maxFileSize: 50MB,        // 单个日志文件最大大小
  maxBackupFiles: 5,        // 保留的备份文件数量
  maxBufferSize: 1000-2000, // 内存缓冲区大小（环境自适应）
  rotationCheckInterval: 60000 // 轮转检查间隔（1分钟）
}
```

## 使用方法

### 1. 基本日志记录

```javascript
// 在主进程中
console.error('严重错误', { error: 'database connection failed' })
console.warn('警告信息', { retries: 3 })
console.info('一般信息', { status: 'ready' })
console.debug('调试信息', { data: someObject })
```

### 2. 模块化日志

推荐在模块中使用统一的前缀格式：

```javascript
// 推荐格式：[模块名] 消息
console.info('[打印模块] 添加打印任务', { 
  taskId: '12345',
  printer: '*************:9100'
})

console.error('[打印队列] 任务处理失败', {
  taskId: '12345',
  error: 'connection timeout'
})
```

### 3. 结构化日志

推荐使用对象传递详细信息：

```javascript
// ✅ 推荐：结构化日志
console.info('[VOD模块] 播放视频', {
  videoId: 'video123',
  resolution: '1080p',
  duration: 120,
  userAgent: 'Thunder ERP Client'
})

// ❌ 不推荐：字符串拼接
console.info(`[VOD模块] 播放视频 ${videoId} 分辨率 ${resolution}`)
```

## 日志查看器

### 访问方式

日志查看器仅在开发模式或预览模式下可用：

1. **托盘菜单**: 右键点击系统托盘图标 → "日志查看器"
2. **快捷方式**: 通过应用内部调用（如有集成）

### 功能特性

- **实时刷新**: 每 5 秒自动刷新最新日志
- **多重过滤**: 级别、关键词、数量限制
- **统计信息**: 实时显示各级别日志数量
- **导出功能**: 支持导出日志到本地文件
- **清空操作**: 一键清空所有历史日志

### 过滤选项

```javascript
// 可用的过滤条件
{
  level: 'ERROR',           // 日志级别
  keyword: '打印',          // 关键词搜索
  module: '[打印模块]',     // 模块名过滤
  limit: 500,              // 显示数量限制
  startTime: '2024-01-01', // 开始时间
  endTime: '2024-01-02'    // 结束时间
}
```

## API 接口

### 前端调用

```javascript
// 获取日志（支持过滤）
const result = await window.Logger.getLogs({
  level: 'ERROR',
  keyword: '打印',
  limit: 100
})

// 清空日志
await window.Logger.clearLogs()

// 获取统计信息
const stats = await window.Logger.getStats()

// 设置日志级别
await window.Logger.setLogLevel('DEBUG')

// 导出日志
const result = await window.Logger.exportLogs()
```

### 主进程调用

```javascript
const logger = require('./src/main/logger')

// 获取内存日志
const logs = logger.getMemoryLogs({ level: 'ERROR' })

// 读取文件日志
const fileLogs = logger.readLogFile({ lines: 1000 })

// 获取统计信息
const stats = logger.getLogStats()

// 动态调整日志级别
logger.setLogLevel('DEBUG')
```

## 配置定制

### 创建自定义配置

```javascript
// src/main/loggerConfig.js 中的配置
const customConfig = {
  // 环境特定配置
  environments: {
    DEVELOPMENT: 'development',
    PREVIEW: 'preview', 
    PRODUCTION: 'production'
  },
  
  // 模块级别配置
  moduleConfigs: {
    '[自定义模块]': {
      level: logLevels.INFO
    }
  },
  
  // 数据净化规则
  enableDataSanitization: true,
  
  // 性能监控
  enablePerformanceLogging: false
}
```

### 运行时配置更新

```javascript
// 动态更新配置
logger.updateConfig({
  enableDataSanitization: false,
  maxBufferSize: 2000
})
```

## 文件位置

### 日志文件路径

```bash
# macOS
~/Library/Application Support/thunder-erp/logs/
├── main.log          # 当前日志文件
├── main.log.1        # 备份文件 1（最新）
├── main.log.2        # 备份文件 2
├── main.log.3        # 备份文件 3
├── main.log.4        # 备份文件 4
└── main.log.5        # 备份文件 5（最旧）

# Windows
%APPDATA%\thunder-erp\logs\

# Linux
~/.config/thunder-erp/logs/
```

### 配置文件路径

```bash
# 应用配置（包含 URL 等）
~/Library/Application Support/thunder-erp/app-config.json

# 系统配置（机器码等）
~/Library/Application Support/thunder-erp/system-config.json

# 打印队列持久化
~/Library/Application Support/thunder-erp/print-queue.json
```

## 故障排除

### 常见问题

#### 1. 日志不显示

**症状**: 日志查看器中没有日志显示

**解决方案**:
```bash
# 检查日志文件是否存在
ls ~/Library/Application\ Support/thunder-erp/logs/

# 检查应用权限
# 确保应用有写入用户目录的权限

# 重启应用并检查控制台输出
```

#### 2. 日志文件过大

**症状**: 磁盘空间被日志文件占用

**解决方案**:
```bash
# 手动清理日志文件
rm ~/Library/Application\ Support/thunder-erp/logs/main.log*

# 或使用应用内功能清空日志
```

#### 3. 性能影响

**症状**: 应用运行缓慢，疑似日志影响

**解决方案**:
```javascript
// 临时禁用文件输出（开发调试用）
logger.updateConfig({
  enableFileOutput: false
})

// 调整日志级别
logger.setLogLevel('WARN') // 减少日志输出

// 减少缓冲区大小
logger.updateConfig({
  maxBufferSize: 100
})
```

### 调试技巧

#### 1. 启用详细日志

```javascript
// 开发环境下启用所有日志
if (process.env.NODE_ENV === 'development') {
  logger.setLogLevel('DEBUG')
}
```

#### 2. 模块特定调试

```javascript
// 只关注特定模块的日志
const printLogs = logger.getMemoryLogs({
  module: '[打印模块]',
  level: 'ERROR'
})
```

#### 3. 性能监控

```javascript
// 启用性能日志记录
logger.updateConfig({
  enablePerformanceLogging: true
})

// 记录操作耗时
const startTime = Date.now()
// ... 执行操作
console.info('[性能] 操作完成', {
  duration: Date.now() - startTime + 'ms'
})
```

## 最佳实践

### 1. 日志级别使用

```javascript
// ERROR: 严重错误，影响功能正常运行
console.error('[模块] 操作失败', { error: 'critical failure' })

// WARN: 警告信息，不影响主要功能但需要关注
console.warn('[模块] 重试操作', { retries: 3, maxRetries: 5 })

// INFO: 一般信息，记录重要操作和状态变化
console.info('[模块] 操作成功', { status: 'completed' })

// DEBUG: 调试信息，详细的内部状态和数据
console.debug('[模块] 内部状态', { state: internalState })
```

### 2. 结构化数据

```javascript
// ✅ 使用对象传递结构化数据
console.info('[打印模块] 任务状态', {
  taskId: 'task-123',
  status: 'processing',
  progress: 75,
  estimatedTime: '2 minutes'
})

// ✅ 包含足够的上下文信息
console.error('[网络] 请求失败', {
  url: 'https://api.example.com/data',
  method: 'POST',
  statusCode: 500,
  retryCount: 2,
  error: 'Internal Server Error'
})
```

### 3. 避免敏感信息

```javascript
// ❌ 避免记录敏感信息
console.info('用户登录', { 
  username: 'admin',
  password: 'secret123'  // 敏感信息
})

// ✅ 正确的做法
console.info('用户登录', {
  username: 'admin',
  loginTime: new Date().toISOString(),
  userAgent: navigator.userAgent
})
```

### 4. 性能考虑

```javascript
// ✅ 避免在循环中大量日志输出
for (let i = 0; i < 1000; i++) {
  // ❌ 避免这样做
  // console.debug('处理项目', { index: i })
}

// ✅ 更好的做法
console.info('开始批量处理', { totalItems: 1000 })
// ... 处理逻辑
console.info('批量处理完成', { 
  totalItems: 1000,
  successCount: 995,
  failureCount: 5
})
```

## 监控和分析

### 日志统计

使用统计接口监控应用健康状态：

```javascript
const stats = await window.Logger.getStats()
console.log('日志统计', {
  totalLogs: stats.totalLogs,
  errorRate: stats.levels.ERROR / stats.totalLogs,
  activeModules: Object.keys(stats.modules)
})
```

### 错误趋势分析

```javascript
// 定期检查错误日志
setInterval(async () => {
  const errorLogs = await window.Logger.getLogs({
    level: 'ERROR',
    limit: 10
  })
  
  if (errorLogs.data.length > 5) {
    console.warn('检测到较多错误，建议检查应用状态')
  }
}, 300000) // 每5分钟检查一次
```

## 版本更新

日志系统会随着应用版本更新而改进，主要更新内容：

- **v1.0.0**: 基础日志功能，文件存储和轮转
- **v1.1.0**: 添加日志查看器和过滤功能
- **v1.2.0**: 增加模块级别配置和数据净化
- **v1.3.0**: 性能优化和环境自适应配置

建议定期关注版本更新，获取最新的日志管理功能。 