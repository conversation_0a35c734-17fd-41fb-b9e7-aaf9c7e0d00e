# 构建脚本更新说明

## 修改背景

由于引入了webpack打包preload脚本，所有构建脚本都需要在执行electron-builder之前先构建preload模块。

## 修改的脚本

### 1. `docker-build-win.sh` (Windows正式版)
- **修改**: 在electron-builder之前添加webpack构建
- **命令**: `NODE_ENV=production ./node_modules/.bin/webpack --config webpack.config.js`

### 2. `docker-build-win-preview.sh` (Windows预览版)  
- **修改**: 在electron-builder之前添加webpack构建
- **命令**: `NODE_ENV=production ./node_modules/.bin/webpack --config webpack.config.js`

### 3. `build-mac.sh` (macOS正式版)
- **修改**: 在electron-builder之前添加webpack构建
- **命令**: `NODE_ENV=production ./node_modules/.bin/webpack --config webpack.config.js`

### 4. `build-mac-preview.sh` (macOS预览版)
- **修改**: 在electron-builder之前添加webpack构建  
- **命令**: `NODE_ENV=production ./node_modules/.bin/webpack --config webpack.config.js`

## 构建流程

```
1. 安装依赖 (npm install)
2. **[新增]** 构建preload脚本 (webpack)
3. 构建Electron应用 (electron-builder)
4. 检查构建结果
```

## 重要说明

- **必须先运行webpack**：确保`dist/preload/index.js`存在
- **生产环境优化**：使用`NODE_ENV=production`进行优化构建
- **Docker兼容**：Docker构建脚本在容器内执行webpack命令
- **错误处理**：保留原有的备用构建命令逻辑

## 验证方法

构建完成后检查：
```bash
# 检查preload文件是否存在
ls -la dist/preload/index.js

# 检查应用包是否正常生成
ls -la dist/*.dmg  # macOS
ls -la dist/*.exe  # Windows
``` 