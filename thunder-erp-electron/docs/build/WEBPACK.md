# Webpack 配置说明

## 概述

为了解决 Electron 20+ 沙盒环境中无法加载自定义模块的问题，项目采用 webpack 打包 preload 脚本的方案。

## 目录结构

```
thunder-erp-electron/
├── src/preload/           # 源码目录（开发时编辑）
│   ├── index.js          # 主入口文件
│   ├── print.js          # 打印机功能模块
│   └── bridge.js         # 其他桥接功能
├── dist/preload/          # 打包输出目录（自动生成）
│   ├── index.js          # 打包后的文件
│   └── index.js.map      # 源码映射文件
├── webpack.config.js      # Webpack 配置文件
└── scripts/
    └── dev-with-watch.sh  # 开发环境启动脚本
```

## 构建命令

### 开发环境
```bash
# 构建开发版本（包含源码映射）
npm run build:preload:dev

# 监听模式（文件变化自动重新打包）
npm run build:preload:watch

# 启动完整开发环境（包含 webpack watch）
./scripts/dev-with-watch.sh
```

### 生产环境
```bash
# 构建生产版本（压缩优化）
npm run build:preload

# 构建完整应用
npm run build
```

## 工作原理

1. **开发时**：编辑 `src/preload/` 目录下的模块化文件
2. **构建时**：webpack 将多个模块打包成单一的 `dist/preload/index.js`
3. **运行时**：Electron 加载打包后的单一文件，符合沙盒要求

## 配置特点

- **目标环境**：`electron-preload`
- **模块格式**：CommonJS（符合 Electron 要求）
- **外部依赖**：`electron` 模块不参与打包
- **代码转换**：使用 Babel 确保兼容性
- **源码映射**：开发环境提供调试支持

## 优势

✅ **完全兼容沙盒**：打包后的单一文件符合 Electron 沙盒要求
✅ **开发体验佳**：保持模块化开发，支持热重载
✅ **性能优化**：生产环境自动压缩优化
✅ **调试友好**：开发环境提供完整的源码映射
✅ **安全性高**：启用沙盒模式，提高应用安全性

## 注意事项

1. **修改源码**：只编辑 `src/preload/` 下的文件，不要直接修改 `dist/` 下的打包文件
2. **构建顺序**：启动应用前必须先构建 preload 脚本
3. **依赖管理**：新增依赖需要在 webpack 配置中正确处理
4. **版本控制**：`dist/preload/` 目录已加入 `.gitignore`，不提交到仓库

## 故障排除

### 问题：模块未找到
**解决**：确保运行了 `npm run build:preload:dev`

### 问题：功能异常
**解决**：检查 webpack 构建是否有错误，查看控制台输出

### 问题：修改不生效
**解决**：使用 watch 模式或手动重新构建 