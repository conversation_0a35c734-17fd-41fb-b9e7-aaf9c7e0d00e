# HTTP桥接系统使用说明

## 概述

HTTP桥接系统允许渲染进程处理从主进程转发的HTTP请求。外部设备可以通过HTTP API与Electron应用的渲染进程进行通信。

## 架构流程

```
外部设备 → HTTP请求 → 主进程HTTP服务器 → 渲染进程处理 → 响应 → 主进程 → 外部设备
```

1. **外部设备**发送HTTP请求到主进程的HTTP服务器（默认端口8080）
2. **主进程HTTP服务器**接收请求并转发给渲染进程
3. **渲染进程**通过HttpBridge接口处理请求
4. **渲染进程**发送响应回主进程
5. **主进程**将响应返回给外部设备

## 文件结构

```
src/preload/
├── http.js                    # HTTP桥接核心实现
├── http.d.ts                 # TypeScript类型定义
├── http-usage-example.js     # 使用示例
└── HTTP_BRIDGE_README.md     # 本说明文档
```

## 基本使用

### 1. 注册路由

```javascript
// GET路由
window.HttpBridge.get('/api/hello', async (request, response) => {
  response.json({
    message: 'Hello World!',
    timestamp: new Date().toISOString()
  })
})

// POST路由
window.HttpBridge.post('/api/users', async (request, response) => {
  const userData = request.body
  
  if (!userData.name) {
    return response.error(400, 'Name is required')
  }
  
  response.status(201).json({
    success: true,
    user: { id: Date.now(), ...userData }
  })
})
```

### 2. 使用中间件

```javascript
// 日志中间件
window.HttpBridge.use(async (request, response, next) => {
  console.log(`${request.method} ${request.pathname}`)
  await next()
})

// 认证中间件
window.HttpBridge.use(async (request, response, next) => {
  const token = request.headers.authorization
  
  if (!token) {
    return response.error(401, 'Unauthorized')
  }
  
  await next()
})
```

### 3. 设置默认处理器

```javascript
window.HttpBridge.setDefaultHandler(async (request, response) => {
  response.error(404, 'API endpoint not found', {
    path: request.pathname,
    method: request.method
  })
})
```

## API接口

### HttpRequest对象

```typescript
interface HttpRequest {
  id: string              // 请求ID
  method: string          // HTTP方法
  url: string            // 完整URL
  pathname: string       // 路径名
  search: string         // 查询字符串
  query: object          // 查询参数对象
  headers: object        // 请求头
  body: any             // 请求体
  timestamp: number      // 请求时间戳
}
```

### HttpResponse对象方法

```typescript
response.status(code: number)                    // 设置状态码
response.header(name: string, value: string)    // 设置响应头
response.headers(obj: object)                    // 设置多个响应头
response.json(data: any)                         // 发送JSON响应
response.text(data: string)                      // 发送文本响应
response.html(data: string)                      // 发送HTML响应
response.send(data: any)                         // 发送通用响应
response.error(statusCode, message, details)    // 发送错误响应
```

### HttpBridge接口方法

```typescript
HttpBridge.get(path, handler)           // 注册GET路由
HttpBridge.post(path, handler)          // 注册POST路由
HttpBridge.put(path, handler)           // 注册PUT路由
HttpBridge.delete(path, handler)        // 注册DELETE路由
HttpBridge.patch(path, handler)         // 注册PATCH路由
HttpBridge.all(path, handler)           // 注册所有方法路由
HttpBridge.route(method, path, handler) // 注册指定方法路由

HttpBridge.use(middleware)              // 添加中间件
HttpBridge.setDefaultHandler(handler)   // 设置默认处理器

HttpBridge.removeRoute(method, path)    // 移除路由
HttpBridge.clearRoutes()               // 清空所有路由
HttpBridge.clearMiddlewares()          // 清空所有中间件
HttpBridge.getRoutes()                 // 获取已注册路由
```

## 路由匹配规则

路由按以下优先级匹配：

1. **精确匹配**: `GET:/api/users`
2. **方法通配符**: `GET:*`
3. **路径通配符**: `*:/api/users`
4. **完全通配符**: `*:*`
5. **默认处理器**

## 示例：完整的API服务

```javascript
// 基础中间件
window.HttpBridge.use(async (request, response, next) => {
  // CORS处理
  response.header('Access-Control-Allow-Origin', '*')
  response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE')
  response.header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  // 请求日志
  console.log(`[API] ${request.method} ${request.pathname}`)
  
  await next()
})

// 健康检查
window.HttpBridge.get('/health', async (request, response) => {
  response.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'Thunder ERP Renderer'
  })
})

// 用户管理API
window.HttpBridge.get('/api/users', async (request, response) => {
  // 获取用户列表
  response.json([
    { id: 1, name: 'User 1' },
    { id: 2, name: 'User 2' }
  ])
})

window.HttpBridge.post('/api/users', async (request, response) => {
  // 创建用户
  const user = request.body
  response.status(201).json({
    success: true,
    user: { id: Date.now(), ...user }
  })
})

// 错误处理
window.HttpBridge.setDefaultHandler(async (request, response) => {
  response.error(404, 'Not Found', {
    path: request.pathname,
    availableRoutes: window.HttpBridge.getRoutes()
  })
})
```

## 测试HTTP API

启动应用后，可以使用curl或其他HTTP客户端测试：

```bash
# 健康检查
curl http://localhost:8080/health

# 获取用户列表
curl http://localhost:8080/api/users

# 创建用户
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{"name":"New User","email":"<EMAIL>"}'

# 测试不存在的路由
curl http://localhost:8080/api/nonexistent
```

## 注意事项

1. **异步处理**: 所有路由处理器和中间件都支持async/await
2. **错误处理**: 未处理的异常会自动返回500错误
3. **响应唯一性**: 每个请求只能发送一次响应
4. **中间件顺序**: 中间件按注册顺序执行
5. **内存管理**: 适当时清理路由和中间件

## 故障排除

### 常见问题

1. **404错误**: 检查路由是否正确注册
2. **500错误**: 检查处理器是否有未捕获的异常
3. **超时错误**: 处理器执行时间超过30秒
4. **重复响应**: 同一个请求多次调用响应方法

### 调试技巧

```javascript
// 启用详细日志
window.HttpBridge.use(async (request, response, next) => {
  console.log('Request:', request)
  const start = Date.now()
  
  await next()
  
  console.log(`Processed in ${Date.now() - start}ms`)
})

// 查看已注册的路由
console.log('Registered routes:', window.HttpBridge.getRoutes())
``` 