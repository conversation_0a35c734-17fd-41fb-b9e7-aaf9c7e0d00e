{"name": "thunder-erp-electron", "version": "1.0.0", "description": "Thunder ERP Windows Desktop Application", "main": "src/main/index.js", "scripts": {"start": "electron .", "dev": "npm run build:preload:dev && NODE_ENV=development electron . --trace-warnings", "dev:url": "npm run build:preload:dev && NODE_ENV=development electron . --trace-warnings --client-url", "build": "npm run build:preload && electron-builder", "build:win": "npm run build:preload && electron-builder --win --x64", "build:win:preview": "npm run build:preload && electron-builder --win --x64 --config.productName=\"Thunder ERP Preview\"", "build:mac": "npm run build:preload && electron-builder --mac", "build:mac:preview": "npm run build:preload && electron-builder --mac --config.productName=\"Thunder ERP Preview\"", "build:dir": "npm run build:preload && electron-builder --dir", "build:preload": "NODE_ENV=production webpack --config webpack.config.js", "build:preload:dev": "NODE_ENV=development webpack --config webpack.config.js", "build:preload:watch": "NODE_ENV=development webpack --config webpack.config.js --watch", "postinstall": "electron-builder install-app-deps", "new:win": "./docker-build-win.sh", "new:win:preview": "./docker-build-win-preview.sh", "new:mac": "./build-mac.sh", "new:mac:preview": "./build-mac-preview.sh"}, "author": "Thunder ERP Team", "license": "ISC", "dependencies": {"axios": "^1.7.7", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "iconv-lite": "^0.6.3"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "babel-loader": "^10.0.0", "electron": "^28.1.0", "electron-builder": "^26.0.12", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "build": {"appId": "com.thunder.erp", "productName": "Thunder ERP", "directories": {"output": "dist"}, "files": ["src/**/*", "dist/preload/**/*", "package.json", ".preview-build"], "extraMetadata": {"main": "src/main/index.js"}, "extraResources": [{"from": "build", "to": "build"}], "buildDependenciesFromSource": true, "npmRebuild": true, "win": {"target": ["nsis"], "icon": "build/logo.png"}, "mac": {"target": ["dmg"], "icon": "build/logo.png", "category": "public.app-category.business", "darkModeSupport": true, "hardenedRuntime": true, "gatekeeperAssess": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Thunder ERP"}, "publish": {"provider": "generic", "url": "https://your-update-server.com/updates/"}}}