#!/bin/bash
# 检查是否存在electronuserland/builder:wine镜像
if ! docker images | grep "electronuserland/builder" | grep "wine"; then
  echo "electronuserland/builder:wine镜像不存在，正在拉取..."
  docker pull electronuserland/builder:wine
else
  echo "electronuserland/builder:wine镜像已存在，跳过拉取"
fi

mkdir -p dist
rm -f .preview-build

# 创建预览版标记文件
echo "创建预览版标记文件..."
echo "This is a preview build" > .preview-build
echo "预览版标记文件已创建: $(pwd)/.preview-build"
ls -la .preview-build

# 为Preview环境构建
echo "正在为Preview环境构建..."
docker run --rm -ti \
  --env ELECTRON_CACHE="/root/.cache/electron" \
  --env ELECTRON_BUILDER_CACHE="/root/.cache/electron-builder" \
  -v $(pwd):/project \
  -v $(pwd)/node_modules:/project/node_modules \
  -v ~/.cache/electron:/root/.cache/electron \
  -v ~/.cache/electron-builder:/root/.cache/electron-builder \
  electronuserland/builder:wine /bin/bash -c "cd /project && echo '构建preload脚本...' && NODE_ENV=production ./node_modules/.bin/webpack --config webpack.config.js && echo 'preload构建完成，开始构建Electron应用...' && ./node_modules/.bin/electron-builder --win --x64 --config.productName=\"Thunder ERP Preview\"" 