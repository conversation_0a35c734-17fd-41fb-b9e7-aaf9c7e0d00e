# Thunder ERP 构建指南

本文档提供了如何构建Thunder ERP桌面应用的详细说明。

## 先决条件

- Node.js 14+
- npm 6+
- 对于macOS构建: macOS系统和Python 3
- 对于Windows构建: Windows系统或使用Docker

## 安装依赖

在构建应用之前，请先安装所有依赖:

```bash
npm install
```

## 推荐构建方法

我们提供了新的构建脚本，它们能够自动处理环境设置、依赖安装和标记文件创建:

### Windows 构建

**正式版:**
```bash
npm run new:win
# 或直接运行脚本
./docker-build-win.sh
```

**预览版:**
```bash
npm run new:win:preview
# 或直接运行脚本
./docker-build-win-preview.sh
```

### macOS 构建

**正式版:**
```bash
npm run new:mac
# 或直接运行脚本
./build-mac.sh
```

**预览版:**
```bash
npm run new:mac:preview
# 或直接运行脚本
./build-mac-preview.sh
```

## 传统构建方法 (不推荐)

以下是传统构建方法，不再推荐使用，因为它们可能无法正确处理环境变量:

### Windows 构建

**正式版:**
```bash
npm run build:win
```

**预览版:**
```bash
npm run build:win:preview
```

### macOS 构建

**正式版:**
```bash
npm run build:mac
```

**预览版:**
```bash
npm run build:mac:preview
```

## 构建输出

所有构建输出将保存在`dist`目录中:

- Windows: `dist/*.exe`
- macOS: `dist/*.dmg`

## 预览版与正式版的区别

预览版应用具有以下特点:

1. 应用名称为"Thunder ERP Preview"
2. 启动时提供环境选择对话框，可选择预览环境或生产环境
3. 系统托盘中包含"切换环境"选项

## 构建机制说明

### 预览版构建的工作原理

预览版构建过程中会创建一个`.preview-build`标记文件，应用运行时会检测该文件以确定是否为预览版。这种方法比依赖环境变量更可靠，因为环境变量在打包过程中可能无法正确传递。

构建脚本会在构建前执行以下操作:

1. 清理`dist`目录，确保clean构建
2. 删除可能存在的`.preview-build`文件(对于正式版)或创建该文件(对于预览版)
3. 直接调用electron-builder，避免通过npm脚本传递环境变量

### 常见问题

#### macOS构建错误

- **问题**: 找不到`which`模块
  **解决方案**: 我们的构建脚本会自动创建which命令的替代品

- **问题**: 无法找到python
  **解决方案**: 确保已安装python3: `brew install python`，脚本会自动创建python3到python的软链接

- **问题**: electron-builder构建时的DMG相关错误
  **解决方案**: 确保XCode命令行工具已安装: `xcode-select --install`

#### Windows构建错误

- **问题**: Docker构建失败
  **解决方案**: 确保Docker已正确安装且`electronuserland/builder:wine`镜像可用

## 进阶构建提示

### 加速macOS构建

如果macOS构建过程中遇到网络问题或下载依赖缓慢的情况，可以尝试以下方法:

1. 使用本地缓存:
   ```bash
   export ELECTRON_CACHE="$HOME/.cache/electron"
   export ELECTRON_BUILDER_CACHE="$HOME/.cache/electron-builder"
   ```

2. 增加npm超时时间:
   ```bash
   npm config set fetch-timeout 300000
   ```

3. 将Electron缓存到本地目录:
   ```bash
   mkdir -p ~/.electron-cache
   export ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/"
   ```

### 调试构建问题

若遇到构建失败，可尝试启用详细日志来调试:

```bash
DEBUG=electron-builder npm run new:mac:preview
``` 