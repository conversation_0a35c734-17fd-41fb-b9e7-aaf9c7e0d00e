const path = require('path');

module.exports = {
  // 开发模式，便于调试
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  
  // 目标环境：Electron preload 脚本
  target: 'electron-preload',
  
  // 入口文件
  entry: './src/preload/index.js',
  
  // 输出配置
  output: {
    path: path.resolve(__dirname, 'dist/preload'),
    filename: 'index.js',
    clean: true // 每次构建前清理输出目录
  },
  
  // 模块解析配置
  resolve: {
    extensions: ['.js', '.json']
  },
  
  // 外部依赖（Electron 内置模块不需要打包）
  externals: {
    'electron': 'commonjs electron'
  },
  
  // 模块规则
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                // 保持 CommonJS 格式，因为 Electron preload 使用 CommonJS
                modules: 'commonjs',
                targets: {
                  electron: '28'
                }
              }]
            ]
          }
        }
      }
    ]
  },
  
  // 开发工具配置
  devtool: process.env.NODE_ENV === 'production' ? false : 'source-map',
  
  // 优化配置
  optimization: {
    // 禁用代码分割，确保输出单一文件
    splitChunks: false,
    // 生产环境压缩代码
    minimize: process.env.NODE_ENV === 'production'
  },
  
  // 性能提示
  performance: {
    hints: false // 禁用性能提示，preload 脚本通常较小
  },
  
  // Node.js polyfills 配置
  node: {
    __dirname: false,
    __filename: false
  }
}; 