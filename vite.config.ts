import { resolve } from 'path';
import { defineConfig, loadEnv, Plugin } from 'vite';
import vue from '@vitejs/plugin-vue';
import { VueMcp } from 'vite-plugin-vue-mcp';
import Inspect from 'vite-plugin-inspect';
// import legacy from '@vitejs/plugin-legacy'; // 已移除 - 直接通过build配置兼容设备
import VueDevTools from 'vite-plugin-vue-devtools';
import { readFileSync } from 'fs';
import { visualizer } from 'rollup-plugin-visualizer';

interface GitInfo {
  commitHash: string;
  commitMessage: string;
  branchName: string;
  author: { name: string; email: string };
  commitDate: string;
  tagName: string;
}
interface VersionInfo {
  version: string;
  versionCode?: number;
  description?: string;
  releaseTimestamp?: string;
  git?: GitInfo;
  buildEnv?: string;
}

const envFlag = process.env.BUILD_ENV || process.env.NODE_ENV || 'development';
const envSuffix = envFlag === 'production' ? 'prod' : envFlag === 'stage' ? 'stage' : envFlag;

const packageJson = JSON.parse(readFileSync('./package.json', 'utf-8'));
let versionInfo: VersionInfo = { version: packageJson.version };
try {
  versionInfo = JSON.parse(readFileSync(`./public/version.${envSuffix}.json`, 'utf-8'));
} catch {
  console.warn(`Warning: version.${envSuffix}.json not found, using package.json version`);
}

// CDN URLs for externalized dependencies
const CDN_CONFIG = {
  externals: {
    vue: 'Vue',
    'vue-router': 'VueRouter',
    pinia: 'Pinia',
    '@vueuse/core': 'VueUse'
  },
  // 在index.html中添加CDN引用 - 生产环境和stage环境使用
  cdnUrls: {
    // 生产环境CDN
    production: {
      vue: 'https://unpkg.com/vue@3.5.12/dist/vue.global.prod.js',
      'vue-router': 'https://unpkg.com/vue-router@4.4.0/dist/vue-router.global.prod.js',
      pinia: 'https://unpkg.com/pinia@3.0.0/dist/pinia.global.prod.js',
      '@vueuse/core': 'https://unpkg.com/@vueuse/core@11.1.0/dist/index.global.js'
    },
    // stage环境CDN (可以使用相同的或者配置不同的CDN)
    stage: {
      vue: 'https://unpkg.com/vue@3.5.12/dist/vue.global.prod.js',
      'vue-router': 'https://unpkg.com/vue-router@4.4.0/dist/vue-router.global.prod.js',
      pinia: 'https://unpkg.com/pinia@3.0.0/dist/pinia.global.prod.js',
      '@vueuse/core': 'https://unpkg.com/@vueuse/core@11.1.0/dist/index.global.js'
    }
  }
};

// 创建CDN注入插件
function createCdnPlugin(mode: string): Plugin {
  return {
    name: 'vite:inject-cdn',
    transformIndexHtml(html) {
      // 完全禁用CDN注入，避免模块解析错误
      return html;
    }
  };
}

export default defineConfig(({ mode }) => {
  loadEnv(mode, process.cwd(), ''); // 若需用 env 变量，可在此读取

  // 同时支持production和stage环境
  const isBuildMode = mode === 'production' || mode === 'stage';
  // build和build:stage都算作生产环境，都需要移除console
  const isProductionBuild = mode === 'production' || mode === 'stage';
  console.log(`Running in ${mode} mode, build optimization: ${isBuildMode ? 'enabled' : 'disabled'}`);

  const plugins = [
    vue(),
    VueMcp({ host: 'localhost', printUrl: true }),
    Inspect(),
    // 🚀 Legacy插件已移除 - 通过build.target直接兼容目标设备
    createCdnPlugin(mode), // 根据当前模式注入CDN
    VueDevTools({
      // 设置默认启动的编辑器为 cursor
      launchEditor: 'cursor',
      // 启用组件检查器，并设置一些高级选项
      componentInspector: {
        toggleComboKey: 'meta-shift', // 使用 command+shift 组合键切换检查器
        toggleButtonVisibility: 'active', // 仅在检查器激活时显示切换按钮
        appendTo: /.+/, // 支持任何入口点
        disableInspectorOnEditorOpen: false, // 打开编辑器时不禁用检查器
        cleanHtml: true // 清理HTML，使开发工具中的代码更整洁
      }
    })
  ];

  // 在生产环境和stage环境都添加分析插件
  if (isBuildMode) {
    plugins.push(
      visualizer({
        filename: `dist/stats-${mode}.html`, // 根据环境生成不同的分析文件
        open: false,
        gzipSize: true,
        brotliSize: true
      }) as Plugin
    );
  }

  return {
    plugins,

    define: {
      'import.meta.env.VITE_APP_VERSION': JSON.stringify(versionInfo.version),
      'import.meta.env.VITE_APP_RELEASE_TIMESTAMP': JSON.stringify(versionInfo.releaseTimestamp),
      'import.meta.env.VITE_APP_GIT_HASH': JSON.stringify(versionInfo.git?.commitHash),
      'import.meta.env.VITE_APP_GIT_BRANCH': JSON.stringify(versionInfo.git?.branchName),
      'import.meta.env.VITE_APP_GIT_MESSAGE': JSON.stringify(versionInfo.git?.commitMessage),
      'import.meta.env.VITE_APP_BUILD_ENV': JSON.stringify(versionInfo.buildEnv)
      // console 的移除已通过 esbuildOptions.drop 配置处理
    },

    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/style/element-plus/index.scss" as *;`
        }
      },
      devSourcemap: false // 禁用 CSS 源码映射以提高打包速度
    },

    build: {
      sourcemap: false,
      // 🎯 直接兼容目标设备，无需legacy插件 (使用esbuild支持的现代浏览器版本)
      target: ['es2018', 'chrome70', 'safari12', 'edge79'],
      minify: 'esbuild',
      cssCodeSplit: false, // 将CSS合并以减少请求和处理开销
      assetsInlineLimit: 4096, // 设置合理的资源内联限制
      chunkSizeWarningLimit: 5000, // 提高块大小警告限制，避免不必要的警告
      esbuildOptions: {
        drop: isProductionBuild ? ['console', 'debugger'] : [], // 在production和stage环境都移除console和debugger
        concurrency: 3, // 降低并发度，减少内存占用
        legalComments: 'none', // 移除法律注释
        treeShaking: true,
        minifySyntax: true,
        minifyWhitespace: true,
        target: ['es2018', 'chrome70', 'safari12', 'edge79'] // 确保目标设备兼容性
      },
      rollupOptions: {
        // 暂时禁用外部化配置，让打包工具处理所有依赖
        // external: isBuildMode ? Object.keys(CDN_CONFIG.externals) : [],
        output: {
          // 为外部化的库生成全局变量
          // globals: CDN_CONFIG.externals,
          // 代码分块策略
          manualChunks: id => {
            // 极简化分块策略 - 只分成三个主要块
            if (id.includes('node_modules')) {
              // 所有第三方依赖打包在一起，避免初始化顺序问题
              return 'vendor';
            }

            // 所有业务代码打包在一起
            return 'app';
          },
          // 优化资源文件命名
          assetFileNames: assetInfo => (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name ?? '') ? 'assets/fonts/[name][extname]' : 'assets/[name]-[hash][extname]')
        }
      }
    },

    resolve: { alias: { '@': resolve(__dirname, 'src') } },

    server: {
      host: '0.0.0.0',
      port: 5173
    },

    // 优化依赖扫描
    optimizeDeps: {
      entries: ['./src/main.ts'],
      include: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'element-plus', 'axios', 'dayjs', 'lodash-es']
      // 确保不外部化任何依赖
    },

    // 添加TypeScript优化 - 修复类型问题
    esbuild: {
      // 使用JSON字符串格式传递tsconfigRaw
      tsconfigRaw: JSON.stringify({
        compilerOptions: {
          skipLibCheck: true,
          ignoreDeprecations: '5.0'
        }
      })
    },

    test: {
      globals: true,
      environment: 'jsdom',
      alias: [{ find: '@', replacement: resolve(__dirname, 'src') }]
    }
  };
});
