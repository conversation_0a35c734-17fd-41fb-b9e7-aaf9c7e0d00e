import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  Receipt,
  ReceiptElementType,
  TextAlignment,
  FontSize,
  ReceiptText,
  ReceiptEmptyLine,
  ReceiptLine,
  ReceiptTable,
  ReceiptTableColumn
} from '@/domains/prints/shared/models/receipt-model';
import { thermoPrinterSimulator } from '@/infrastructure/printing/thermoPrinterSimulator';
import { PrintCommands } from '@/infrastructure/printing/printCommands';
import { OrderDetailData } from '@/domains/prints/order-detail/models/order-detail-data';
import { ProductionOrderData } from '@/domains/prints/production-order/models/production-order-data';
import { SessionOrderData } from '@/domains/prints/session-order/models/session-order-data';
import { receiptBuilder } from '@/domains/prints/receipt-builder';

// 模拟 PrintCommands 类，避免外部依赖
vi.mock('@/infrastructure/printing/printCommands', () => {
  return {
    PrintCommands: {
      TOTAL_WIDTH: 48,
      getStringWidth: (str: string) => {
        let width = 0;
        for (let i = 0; i < str.length; i++) {
          const code = str.charCodeAt(i);
          // 简化的宽度计算逻辑：中文字符算2个宽度，其他算1个
          width += code > 127 ? 2 : 1;
        }
        return width;
      }
    }
  };
});

// 模拟 receiptBuilder
vi.mock('@/domains/prints/receipt-builder', () => {
  return {
    receiptBuilder: {
      buildOrderDetailReceipt: vi.fn((data) => createMockReceipt('消费明细单')),
      buildProductionOrderReceipt: vi.fn((data) => createMockReceipt('出品单')),
      buildSessionOrderReceipt: vi.fn((data) => createMockReceipt('开台单')),
    }
  };
});

// 创建模拟的票据对象
function createMockReceipt(title: string): Receipt {
  return {
    elements: [
      {
        type: ReceiptElementType.TEXT,
        content: title,
        align: TextAlignment.CENTER,
        bold: true,
        fontSize: FontSize.DOUBLE,
      } as ReceiptText,
      {
        type: ReceiptElementType.EMPTY_LINE,
        lines: 1,
      } as ReceiptEmptyLine,
      {
        type: ReceiptElementType.LINE,
        char: '-',
        length: 48,
      } as ReceiptLine,
      {
        type: ReceiptElementType.TEXT,
        content: '商品信息',
        align: TextAlignment.LEFT,
      } as ReceiptText,
      {
        type: ReceiptElementType.TABLE,
        columns: [
          [
            { text: '项目', width: 30, align: TextAlignment.LEFT } as ReceiptTableColumn,
            { text: '价格', width: 20, align: TextAlignment.RIGHT } as ReceiptTableColumn,
          ],
          [
            { text: '商品1', width: 30, align: TextAlignment.LEFT } as ReceiptTableColumn,
            { text: '¥100.00', width: 20, align: TextAlignment.RIGHT } as ReceiptTableColumn,
          ],
        ],
      } as ReceiptTable,
    ],
  };
}

describe('ThermoPrinterSimulator', () => {
  let consoleSpy: any;

  beforeEach(() => {
    // 模拟 console.log，避免测试时输出大量内容
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
    vi.clearAllMocks();
  });

  it('should correctly render a simple receipt with all element types', () => {
    // 创建一个包含各种元素的测试票据
    const testReceipt: Receipt = {
      elements: [
        {
          type: ReceiptElementType.TEXT,
          content: '测试标题',
          align: TextAlignment.CENTER,
          bold: true,
          fontSize: FontSize.DOUBLE,
        } as ReceiptText,
        {
          type: ReceiptElementType.EMPTY_LINE,
          lines: 1,
        } as ReceiptEmptyLine,
        {
          type: ReceiptElementType.LINE,
          char: '-',
          length: PrintCommands.TOTAL_WIDTH,
        } as ReceiptLine,
        {
          type: ReceiptElementType.TEXT,
          content: '左对齐文本',
          align: TextAlignment.LEFT,
        } as ReceiptText,
        {
          type: ReceiptElementType.TEXT,
          content: '右对齐文本',
          align: TextAlignment.RIGHT,
        } as ReceiptText,
        {
          type: ReceiptElementType.TABLE,
          columns: [
            [
              { text: '项目', width: 30, align: TextAlignment.LEFT } as ReceiptTableColumn,
              { text: '价格', width: 20, align: TextAlignment.RIGHT } as ReceiptTableColumn,
            ],
            [
              { text: '啤酒', width: 30, align: TextAlignment.LEFT } as ReceiptTableColumn,
              { text: '¥30.00', width: 20, align: TextAlignment.RIGHT } as ReceiptTableColumn,
            ],
          ],
        } as ReceiptTable,
      ],
    };

    // 不输出到控制台，仅返回结果数组
    const outputLines = thermoPrinterSimulator.simulatePrint(testReceipt, false) as string[];

    // 验证输出行数
    expect(outputLines.length).toBeGreaterThan(10); // 至少有边框和内容行

    // 验证输出内容
    // 1. 标题应该居中
    const titleLineIndex = 6; // 边框之后的第一个内容行
    expect(outputLines[titleLineIndex]).toContain('测试标题');
    // 2. 空行和分隔线应该存在
    expect(outputLines).toContainEqual(expect.stringContaining(' '.repeat(PrintCommands.TOTAL_WIDTH)));
    expect(outputLines).toContainEqual(expect.stringContaining('-'.repeat(PrintCommands.TOTAL_WIDTH)));
    // 3. 表格行应该包含数据
    const tableHeaderIndex = outputLines.findIndex(line => line.includes('项目') && line.includes('价格'));
    expect(tableHeaderIndex).toBeGreaterThan(0);
    expect(outputLines[tableHeaderIndex + 1]).toContain('啤酒');
    expect(outputLines[tableHeaderIndex + 1]).toContain('¥30.00');
    // 4. 应该有完整的边框
    expect(outputLines[0]).toContain('\n\n');
    expect(outputLines[1]).toContain('┌' + '─'.repeat(PrintCommands.TOTAL_WIDTH) + '┐');
    const lastContentIndex = outputLines.length - 1;
    expect(outputLines[lastContentIndex]).toContain('\n\n');
  });

  it('should correctly render center-aligned text', () => {
    const testReceipt: Receipt = {
      elements: [
        {
          type: ReceiptElementType.TEXT,
          content: '居中文本测试',
          align: TextAlignment.CENTER,
        } as ReceiptText,
      ],
    };

    const outputLines = thermoPrinterSimulator.simulatePrint(testReceipt, false) as string[];
    
    // 找到居中文本行
    const textLineIndex = outputLines.findIndex(line => line.includes('居中文本测试'));
    
    // 居中文本应该有适当的左右空白
    const textLine = outputLines[textLineIndex];
    const leftBorderPos = textLine.indexOf('│');
    const textStartPos = textLine.indexOf('居中文本测试');
    const rightPaddingStartPos = textStartPos + '居中文本测试'.length;
    const rightBorderPos = textLine.lastIndexOf('│');
    
    // 验证居中：左右空白应该大致相等（可能有1-2个字符的差异）
    const leftPadding = textStartPos - leftBorderPos - 1;
    const rightPadding = rightBorderPos - rightPaddingStartPos;
    
    expect(Math.abs(leftPadding - rightPadding)).toBeLessThanOrEqual(2);
  });

  it('should correctly render right-aligned text', () => {
    const testReceipt: Receipt = {
      elements: [
        {
          type: ReceiptElementType.TEXT,
          content: '右对齐测试',
          align: TextAlignment.RIGHT,
        } as ReceiptText,
      ],
    };

    const outputLines = thermoPrinterSimulator.simulatePrint(testReceipt, false) as string[];
    
    // 找到右对齐文本行
    const textLineIndex = outputLines.findIndex(line => line.includes('右对齐测试'));
    
    // 右对齐文本应该贴近右边界
    const textLine = outputLines[textLineIndex];
    const textEndPos = textLine.indexOf('右对齐测试') + '右对齐测试'.length;
    const rightBorderPos = textLine.lastIndexOf('│');
    
    // 验证右对齐：文本结束位置应该非常接近右边界
    const rightPadding = rightBorderPos - textEndPos;
    
    expect(rightPadding).toBeLessThanOrEqual(3); // 允许少量空白
  });

  it('should correctly render a table with proper alignment', () => {
    const testReceipt: Receipt = {
      elements: [
        {
          type: ReceiptElementType.TABLE,
          columns: [
            [
              { text: '商品', width: 25, align: TextAlignment.LEFT } as ReceiptTableColumn,
              { text: '数量', width: 10, align: TextAlignment.RIGHT } as ReceiptTableColumn,
              { text: '单价', width: 15, align: TextAlignment.RIGHT } as ReceiptTableColumn,
            ],
            [
              { text: '可乐', width: 25 } as ReceiptTableColumn,
              { text: '2', width: 10, align: TextAlignment.RIGHT } as ReceiptTableColumn,
              { text: '¥3.50', width: 15, align: TextAlignment.RIGHT } as ReceiptTableColumn,
            ],
          ],
        } as ReceiptTable,
      ],
    };

    const outputLines = thermoPrinterSimulator.simulatePrint(testReceipt, false) as string[];
    
    // 找到表格行
    const headerIndex = outputLines.findIndex(line => line.includes('商品') && line.includes('数量') && line.includes('单价'));
    const dataIndex = headerIndex + 1;
    
    expect(headerIndex).toBeGreaterThan(5); // 应该在边框之后
    
    // 验证表格列对齐
    // 1. 商品名称应该左对齐，位置在左侧
    expect(outputLines[dataIndex].indexOf('可乐')).toBeLessThan(PrintCommands.TOTAL_WIDTH / 3);
    // 2. 数量和单价应该右对齐，位于各自区域的右侧
    const price = outputLines[dataIndex].lastIndexOf('¥3.50');
    const quantity = outputLines[dataIndex].lastIndexOf('2');
    
    expect(price).toBeGreaterThan(quantity); // 单价在数量右侧
    expect(price).toBeGreaterThan(PrintCommands.TOTAL_WIDTH / 2); // 单价在右半部分
  });

  // 新增测试：测试消费明细单格式
  it('should correctly render order detail receipt', () => {
    // 创建测试数据
    const orderDetailData: OrderDetailData = {
      shopName: '测试门店',
      payBillId: 'OD202401010001',
      roomInfo: {
        name: 'VIP包房101'
      },
      roomType: '大包',
      duration: 120, // 2小时
      startTime: '2024-01-01 18:00:00',
      endTime: '2024-01-01 20:00:00',
      checkoutTime: '2024-01-01 20:05:00',
      products: [
        {
          productName: '啤酒',
          price: 30,
          payPrice: 30,
          quantity: 6,
          unit: '瓶',
          totalAmount: 180
        }
      ],
      roomFeeTotal: 200,
      productFeeTotal: 180,
      totalReceivable: 380,
      paymentMethods: [
        {
          method: '微信',
          amount: 380
        }
      ],
      paymentAmount: 380,
      cashierName: '收银员小李',
      printTime: '2024-01-01 20:06:00'
    };

    // 获取Receipt对象并模拟打印
    const receipt = receiptBuilder.buildOrderDetailReceipt(orderDetailData);
    const outputLines = thermoPrinterSimulator.simulatePrint(receipt, false) as string[];

    // 验证基本格式
    expect(outputLines.length).toBeGreaterThan(10);
    
    // 找到标题行
    const titleLineIndex = outputLines.findIndex(line => line.includes('消费明细单'));
    expect(titleLineIndex).toBeGreaterThan(5);
    
    // 验证布局结构
    expect(vi.mocked(receiptBuilder.buildOrderDetailReceipt)).toHaveBeenCalledWith(orderDetailData);
    expect(outputLines).toContainEqual(expect.stringContaining('│'));
    expect(outputLines).toContainEqual(expect.stringContaining('└'));
  });

  // 测试出品单格式
  it('should correctly render production order receipt', () => {
    // 创建测试数据
    const productionOrderData: ProductionOrderData = {
      roomInfo: {
        name: 'VIP包房101'
      },
      employeeName: '服务员小红',
      orderTime: '2024-01-01 18:30:00',
      productionOrderNo: 'PO202401010001',
      orderNo: 'OD202401010001',
      sessionId: 'S202401010001',
      products: [
        {
          productName: '啤酒',
          flavors: '',
          price: 30,
          quantity: 6,
          unit: '瓶',
          payPrice: 30,
          totalAmount: 180
        }
      ]
    };

    // 获取Receipt对象并模拟打印
    const receipt = receiptBuilder.buildProductionOrderReceipt(productionOrderData);
    const outputLines = thermoPrinterSimulator.simulatePrint(receipt, false) as string[];

    // 验证基本格式
    expect(outputLines.length).toBeGreaterThan(10);
    
    // 找到标题行
    const titleLineIndex = outputLines.findIndex(line => line.includes('出品单'));
    expect(titleLineIndex).toBeGreaterThan(5);
    
    // 验证布局结构
    expect(vi.mocked(receiptBuilder.buildProductionOrderReceipt)).toHaveBeenCalledWith(productionOrderData);
    expect(outputLines).toContainEqual(expect.stringContaining('│'));
    expect(outputLines).toContainEqual(expect.stringContaining('└'));
  });

  // 测试开台单格式
  it('should correctly render session order receipt', () => {
    // 创建测试数据
    const sessionOrderData: SessionOrderData = {
      shopName: '测试门店',
      roomInfo: {
        name: 'VIP包房101'
      },
      sessionId: 'S202401010001',
      openTime: '2024-01-01 18:00:00',
      startTime: '2024-01-01 18:00:00',
      endTime: '2024-01-01 20:00:00',
      roomPackages: [
        {
          planName: '2小时欢唱套餐',
          duration: 120,
          originalPrice: 200,
          actualPrice: 200
        }
      ],
      roomFeeTotal: 200,
      products: [],
      productFeeTotal: 0,
      cashierName: '前台小王',
      printTime: '2024-01-01 18:01:00'
    };

    // 获取Receipt对象并模拟打印
    const receipt = receiptBuilder.buildSessionOrderReceipt(sessionOrderData, '1234567890');
    const outputLines = thermoPrinterSimulator.simulatePrint(receipt, false) as string[];

    // 验证基本格式
    expect(outputLines.length).toBeGreaterThan(10);
    
    // 找到标题行
    const titleLineIndex = outputLines.findIndex(line => line.includes('开台单'));
    expect(titleLineIndex).toBeGreaterThan(5);
    
    // 验证布局结构
    expect(vi.mocked(receiptBuilder.buildSessionOrderReceipt)).toHaveBeenCalledWith(sessionOrderData, '1234567890');
    expect(outputLines).toContainEqual(expect.stringContaining('│'));
    expect(outputLines).toContainEqual(expect.stringContaining('└'));
  });
}); 