# Thunder ERP 版本维护说明

## 概述
本文档说明了 Thunder ERP 项目中各个平台版本号的维护位置和更新方法。

## 版本号规范

### 版本号格式
- **versionCode**: 数字版本号，用于程序内部比较 (例如: 2)
- **versionName**: 显示版本号，用于用户查看 (例如: v2.0.1-b1.20250705)

### 版本号命名规则
```
v[主版本].[次版本].[修订版本]-[构建类型][构建序号].[构建日期]
```

**示例**: `v2.0.1-b1.20250705`
- `v2.0.1`: 主版本.次版本.修订版本
- `b1`: 构建类型和序号 (b=beta, r=release)
- `20250705`: 构建日期 (yyyyMMdd)

## 各平台版本号配置位置

### 1. Electron 桌面应用

#### 配置文件位置
```
thunder-erp-electron/package.json
```

#### 配置内容
```json
{
  "version": "2.0.1-b1.20250705",
  "thunderErp": {
    "versionCode": 2,
    "versionName": "v2.0.1-b1.20250705"
  }
}
```

#### 说明
- `version`: npm 包版本，用于构建和依赖管理
- `thunderErp.versionCode`: 数字版本号，用于升级检查
- `thunderErp.versionName`: 显示版本号，用于用户界面

### 2. Android 应用

#### 配置文件位置
```
android/app/build.gradle
```

#### 配置内容
```gradle
// 收银台版本 (CASHIER_ANDROID)
cashier {
    dimension 'clientType'
    // ...
    // 版本信息配置
    versionCode = 2
    versionName = "v2.0.1-b1.20250705"
}

// 移动点单版本 (MOBILE_ORDER)  
mobile {
    dimension 'clientType'
    // ...
    // 版本信息配置
    versionCode = 2
    versionName = "v2.0.1-b1.20250705"
}
```

#### 说明
- `versionCode`: Android 应用版本号，用于升级比较
- `versionName`: Android 应用版本名，用户可见

### 3. H5 前端应用

#### 配置文件位置
```
package.json
```

#### 配置内容
```json
{
  "version": "2.0.1-b1.20250705"
}
```

#### 说明
- H5 前端版本主要用于构建标识，升级检查依赖壳应用版本

## 版本更新流程

### 1. 准备新版本
1. 确定新的版本号规则
2. 更新所有平台配置文件
3. 测试各平台版本显示

### 2. 统一更新步骤

#### Step 1: 更新 Electron 版本
```bash
# 编辑 thunder-erp-electron/package.json
{
  "version": "NEW_VERSION",
  "thunderErp": {
    "versionCode": NEW_CODE,
    "versionName": "vNEW_VERSION"
  }
}
```

#### Step 2: 更新 Android 版本
```bash
# 编辑 android/app/build.gradle
# 在 cashier 和 mobile 两个维度中都更新
versionCode = NEW_CODE
versionName = "vNEW_VERSION"
```

#### Step 3: 更新 H5 版本
```bash
# 编辑主项目 package.json
{
  "version": "NEW_VERSION"
}
```

### 3. 验证版本更新
1. **Electron**: 启动应用，检查 关于→应用版本
2. **Android**: 编译应用，检查 设置→关于
3. **H5**: 检查构建产物版本标识

## 环境配置说明

### Electron 环境配置
- **预览环境**: `https://merpdev-stage.ktvsky.com`
- **生产环境**: `https://merp.ktvsky.com`

### Android 环境配置
- **开发环境**: `https://merpdev-stage.ktvsky.com`
- **生产环境**: `https://merp.ktvsky.com`

## 自动化版本管理

### Electron 版本脚本
```bash
# 查看当前版本
npm run version:show

# 自动递增版本
npm run version:increment

# 手动设置版本
npm run version:set
```

### 构建时版本处理
- Electron: 使用 `scripts/version-manager.js`
- Android: 使用 Gradle 构建脚本
- H5: 使用 Vite 构建配置

## 升级检查机制

### API 接口
```
POST /api/app-upgrade/version
```

### 请求参数
```json
{
  "clientType": "CASHIER_ANDROID|MOBILE_ORDER|CASHIER_WINDOWS",
  "environment": "TEST|PREVIEW|PRODUCTION", 
  "versionCode": 2
}
```

### 升级流程
1. 应用启动时检查版本
2. 调用升级接口比较版本号
3. 根据返回结果显示升级提示
4. 下载并安装新版本

## 注意事项

### 版本号同步
- **必须保持 versionCode 在所有平台一致**
- **versionName 格式应当统一**
- **更新时同时更新所有平台**

### 构建环境
- 开发环境构建会添加 `-dev` 后缀
- 生产环境构建使用完整版本号
- 预览环境构建会添加 `Preview` 标识

### 升级兼容性
- versionCode 递增确保升级检查正确
- 跨大版本升级需要特殊处理
- 强制升级版本需要在服务端配置

## 常见问题

### Q: 为什么需要同时维护 versionCode 和 versionName？
A: versionCode 用于程序逻辑比较，versionName 用于用户显示。

### Q: 如何处理多平台版本不一致？
A: 升级检查时会比较 versionCode，建议保持所有平台版本同步。

### Q: 开发环境如何测试升级功能？
A: 可以临时修改 versionCode 为较小值，模拟旧版本测试升级。

---

**最后更新**: 2025-07-06  
**文档版本**: v1.0  
**维护人员**: Thunder ERP Team