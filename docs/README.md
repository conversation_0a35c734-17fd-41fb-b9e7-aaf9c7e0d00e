# Docs 文件夹说明

## 📁 文件夹结构

```
docs/
├── Prompt/          # AI提示词模板集合
├── assets/          # 文档相关的图片和SVG资源
└── README.md        # 本说明文件
```

## 📝 内容说明

### Prompt 文件夹
包含用于AI辅助开发的提示词模板，涵盖：
- 代码生成和重构
- 文档生成和更新
- 设计稿转代码
- API文档转代码
- Storybook相关工具

### Assets 文件夹
包含项目架构图和流程图的SVG文件：
- 架构设计图
- 数据流图
- 对话框架构图
- 前端自适应设计原理图

## 🔄 文档迁移说明

原docs文件夹中的技术文档已迁移到 `wiki/` 目录：

- **项目架构文档** → `wiki/architecture/`
- **核心功能分析** → `wiki/modules/`
- **技术指南** → `wiki/technical/`
- **开发流程** → `wiki/development/`

## 📚 查看完整文档

请访问 [wiki目录](../wiki/README.md) 查看完整的项目文档。

## 🗑️ 已清理的过时文档

以下文档已被删除或整合：
- 系统架构v4.mdx (已被v5替代)
- 短期待开发.md (内容过时)
- 收银台新UI.md (空文件)
- 本地测试指南.md (配置过时)
- 代码发布.md (流程过时)
- 最小改动部署方案.md (已整合到部署指南)
- 各种重复的架构说明文档

## 💡 使用建议

1. **开发者**: 优先查看 `wiki/` 目录下的文档
2. **AI辅助开发**: 使用 `Prompt/` 目录下的模板
3. **架构图表**: 参考 `assets/` 目录下的SVG文件
