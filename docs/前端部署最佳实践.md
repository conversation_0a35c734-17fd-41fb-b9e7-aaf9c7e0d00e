# Thunder ERP 前端部署最佳实践

## 核心目标

确保用户在重新发包期间不受影响，同时优化构建和部署效率。

## 🎯 推荐的部署策略

### 1. 蓝绿部署 (Blue-Green Deployment)

```bash
# 当前版本(蓝)正在服务用户
# 新版本(绿)在后台构建和测试
# 构建完成后一键切换，用户无感知

# 示例 Nginx 配置
upstream app_blue {
    server app-blue:3000;
}

upstream app_green {
    server app-green:3000;
}

# 通过修改这里实现蓝绿切换
upstream active_app {
    server app-blue:3000;  # 或 app-green:3000
}
```

### 2. 滚动更新 (Rolling Update)

```yaml
# Kubernetes Deployment 示例
apiVersion: apps/v1
kind: Deployment
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0 # 确保始终有实例在服务
      maxSurge: 1 # 逐步替换实例
```

### 3. CDN缓存策略

```nginx
# 静态资源缓存策略
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
    # 带hash的文件永久缓存
    if ($uri ~ -[a-f0-9]{8,}\.(js|css)$) {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 其他静态资源短期缓存
    expires 7d;
    add_header Cache-Control "public";
}

# HTML文件不缓存，确保能立即获取新版本
location / {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    try_files $uri $uri/ /index.html;
}
```

## 🚀 优化的构建策略

### 当前采用的策略 (vite.config.fast-build.ts)

1. **简化chunk分离**：

   - `vue-vendor`: Vue生态系统 (变化频率: 低)
   - `ui-vendor`: Element Plus (变化频率: 中)
   - `vendor`: 其他第三方库 (变化频率: 低)
   - `index`: 应用代码 (变化频率: 高)

2. **为什么不过度细分**：

   - ❌ 构建时间变长 (主要问题)
   - ❌ HTTP请求数量增加
   - ❌ 首屏加载变慢
   - ❌ 空chunk导致浪费处理时间

3. **文件命名带hash**：
   ```
   js/vue-vendor-a1b2c3d4.js    # Vue更新时才变化
   js/ui-vendor-e5f6g7h8.js     # Element Plus更新时才变化
   js/vendor-i9j0k1l2.js        # 其他库更新时才变化
   js/index-m3n4o5p6.js         # 业务代码更新时才变化
   ```

## 📋 部署检查清单

### 发布前

- [ ] 运行构建性能测试: `time pnpm run build:stage:fast`
- [ ] 检查bundle分析: `pnpm run build:analyzer`
- [ ] 验证hash变化范围符合预期
- [ ] 在预发环境测试完整功能

### 发布中

- [ ] 使用蓝绿部署或滚动更新
- [ ] 监控构建日志确保无错误
- [ ] 验证新实例健康状态
- [ ] 检查CDN缓存清理(如需要)

### 发布后

- [ ] 验证用户访问正常
- [ ] 检查控制台错误
- [ ] 监控性能指标
- [ ] 确认旧版本chunks可访问(渐进过渡)

## 🔧 故障恢复

### 快速回滚

```bash
# 蓝绿部署回滚 - 秒级恢复
nginx -s reload  # 切换回上一个稳定版本

# 或 Kubernetes 回滚
kubectl rollout undo deployment/thunder-erp-frontend
```

### 缓存问题处理

```bash
# 清理CDN缓存 (如使用)
curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache"

# 强制浏览器刷新 (HTML不缓存会自动生效)
# 用户只需刷新页面即可获取新版本
```

## 💡 最终建议

1. **优先解决构建速度**: 使用 `build:stage:fast` 命令
2. **采用蓝绿部署**: 避免用户感知到更新过程
3. **合理设置缓存**: HTML不缓存，静态资源长缓存
4. **保持简单**: 避免过度工程化的chunk策略

这样既解决了构建速度问题，又确保了用户体验！ 🎯
