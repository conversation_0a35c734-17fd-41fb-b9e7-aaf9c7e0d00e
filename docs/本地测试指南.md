# Thunder ERP 本地测试指南

## 🎯 测试目标

验证构建优化效果、缓存策略和用户体验，确保生产环境部署的成功。

## 📊 1. 构建性能对比测试

### 测试原有构建vs优化构建

```bash
# 测试原有构建配置
echo "=== 原有构建配置 ===" && time pnpm run build:stage

# 测试优化构建配置
echo "=== 优化构建配置 ===" && time pnpm run build:stage:fast

# 对比结果 (记录时间差异)
```

### 预期结果

```bash
# 原有构建: ~7-8秒 (本地) / 3分钟 (生产4核6GB)
# 优化构建: ~5-6秒 (本地) / 预计2分钟内 (生产环境)
# 提升: 25-30%
```

## 🔍 2. 构建产物分析

### 安装分析工具

```bash
# 安装bundle分析器
pnpm add --save-dev rollup-plugin-visualizer

# 在 vite.config.fast-build.ts 中添加分析插件
import { visualizer } from 'rollup-plugin-visualizer';

plugins: [
  // ... 其他插件
  visualizer({
    filename: 'dist/stats.html',
    open: true,
    gzipSize: true
  })
]
```

### 分析chunk分离效果

```bash
# 构建并生成分析报告
pnpm run build:stage:fast

# 查看生成的chunks
ls -la dist/js/ | grep -E "\.(js)$"

# 检查各chunk大小
du -h dist/js/*.js | sort -hr
```

### 预期chunk结构

```bash
dist/js/
├── vue-vendor-[hash].js     # Vue生态 (~200KB)
├── ui-vendor-[hash].js      # Element Plus (~300KB)
├── vendor-[hash].js         # 其他第三方库 (~150KB)
├── index-[hash].js          # 应用代码 (~400KB)
└── polyfills-legacy-[hash].js # Legacy支持 (~35KB)
```

## 🚀 3. 模拟蓝绿部署测试

### 创建简单的蓝绿部署脚本

```bash
# 创建测试脚本
cat > scripts/test-blue-green.sh << 'EOF'
#!/bin/bash

echo "🔵 构建蓝色版本..."
pnpm run build:stage:fast
cp -r dist dist-blue
echo "✅ 蓝色版本就绪"

echo "🟢 模拟修改代码..."
# 模拟代码更改
sed -i '' 's/Thunder ERP/Thunder ERP v2/g' src/App.vue

echo "🟢 构建绿色版本..."
pnpm run build:stage:fast
cp -r dist dist-green
echo "✅ 绿色版本就绪"

echo "📊 对比两个版本的文件hash..."
echo "蓝色版本:"
ls dist-blue/js/*.js | head -3
echo "绿色版本:"
ls dist-green/js/*.js | head -3

echo "🔄 还原代码..."
git checkout -- src/App.vue

echo "🧹 清理..."
rm -rf dist-blue dist-green
EOF

chmod +x scripts/test-blue-green.sh
```

### 运行蓝绿部署测试

```bash
# 执行测试
./scripts/test-blue-green.sh
```

## 🌐 4. 本地HTTP服务器测试

### 启动多版本服务器

```bash
# 安装本地服务器
pnpm add --save-dev serve

# 构建当前版本
pnpm run build:stage:fast

# 创建版本1
cp -r dist dist-v1

# 模拟代码更改并构建版本2
echo "console.log('Version 2')" >> src/main.ts
pnpm run build:stage:fast
cp -r dist dist-v2
git checkout -- src/main.ts

# 启动双版本服务器
npx serve dist-v1 -l 3001 &  # 版本1在3001端口
npx serve dist-v2 -l 3002 &  # 版本2在3002端口
```

### 测试缓存行为

```bash
# 打开浏览器测试
open http://localhost:3001  # 版本1
open http://localhost:3002  # 版本2

# 在开发者工具中检查:
# 1. Network面板 - 查看文件加载和缓存
# 2. Application面板 - 查看缓存策略
# 3. Console面板 - 查看版本信息
```

## 📱 5. 用户体验测试

### 创建切换测试脚本

```bash
cat > scripts/test-user-experience.sh << 'EOF'
#!/bin/bash

echo "🎭 用户体验测试开始..."

# 启动版本1
echo "启动版本1 (端口3001)..."
pnpm run build:stage:fast
cp -r dist dist-v1
(cd dist-v1 && npx serve -l 3001) &
SERVER1_PID=$!
sleep 3

echo "✅ 请在浏览器中打开 http://localhost:3001"
echo "📋 执行以下操作:"
echo "  1. 登录系统"
echo "  2. 浏览各个模块"
echo "  3. 注意加载时间"
echo ""
read -p "按Enter继续部署版本2..."

# 构建版本2
echo "构建版本2..."
echo "console.log('🚀 版本2已加载')" >> src/main.ts
pnpm run build:stage:fast
cp -r dist dist-v2
git checkout -- src/main.ts

# 启动版本2 (模拟蓝绿切换)
echo "启动版本2 (端口3002)..."
(cd dist-v2 && npx serve -l 3002) &
SERVER2_PID=$!
sleep 3

echo "✅ 版本2部署完成！"
echo "🔄 现在可以测试切换:"
echo "  - 版本1: http://localhost:3001"
echo "  - 版本2: http://localhost:3002"
echo ""
echo "📋 测试项目:"
echo "  1. 在版本1中正常使用"
echo "  2. 切换到版本2"
echo "  3. 检查控制台是否显示'版本2已加载'"
echo "  4. 验证功能正常"
echo ""
read -p "测试完成后按Enter清理..."

# 清理
kill $SERVER1_PID $SERVER2_PID 2>/dev/null
rm -rf dist-v1 dist-v2
echo "🧹 清理完成"
EOF

chmod +x scripts/test-user-experience.sh
```

### 运行用户体验测试

```bash
./scripts/test-user-experience.sh
```

## 🧪 6. 缓存策略测试

### 测试HTTP缓存

```bash
# 创建测试服务器配置
cat > scripts/test-cache.js << 'EOF'
const express = require('express');
const path = require('path');
const app = express();

// 静态资源缓存策略
app.use('/js', express.static(path.join(__dirname, '../dist/js'), {
  maxAge: '1y',  // JS文件长期缓存
  etag: false,
  lastModified: false
}));

app.use('/assets', express.static(path.join(__dirname, '../dist/assets'), {
  maxAge: '7d'  // 资源文件7天缓存
}));

// HTML不缓存
app.use('/', express.static(path.join(__dirname, '../dist'), {
  maxAge: 0,
  etag: false,
  lastModified: false,
  setHeaders: (res) => {
    res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  }
}));

app.listen(3000, () => {
  console.log('🌐 缓存测试服务器启动: http://localhost:3000');
});
EOF

# 安装依赖并运行
pnpm add --save-dev express
node scripts/test-cache.js
```

## 📈 7. 性能基准测试

### 创建性能测试脚本

```bash
cat > scripts/performance-test.sh << 'EOF'
#!/bin/bash

echo "⚡ 性能基准测试"
echo "=================="

# 构建时间测试
echo "📊 构建时间对比:"
echo "原有构建:"
time pnpm run build:stage 2>&1 | grep "built in"

echo "优化构建:"
time pnpm run build:stage:fast 2>&1 | grep "built in"

# 产物大小对比
echo ""
echo "📦 产物大小对比:"
echo "原有构建:"
du -sh dist/ | cut -f1

pnpm run build:stage:fast > /dev/null 2>&1
echo "优化构建:"
du -sh dist/ | cut -f1

# chunk数量对比
echo ""
echo "🧩 Chunk数量:"
ls dist/js/*.js | wc -l | xargs echo "JS文件数量:"
EOF

chmod +x scripts/performance-test.sh
```

### 运行性能测试

```bash
./scripts/performance-test.sh
```

## ✅ 8. 测试检查清单

```bash
# 复制这个检查清单，逐项验证:

echo "📋 本地测试检查清单:"
echo "===================="
echo "[ ] 构建时间提升 25%+"
echo "[ ] 无空chunk警告"
echo "[ ] Vendor chunks正确分离"
echo "[ ] 文件hash正常生成"
echo "[ ] 蓝绿部署切换无感知"
echo "[ ] 缓存策略生效"
echo "[ ] 用户体验无影响"
echo "[ ] 控制台无错误"
echo "[ ] 功能完整性确认"
echo "[ ] 不同浏览器兼容性"
```

## 🚀 快速测试命令

最简单的验证方式：

```bash
# 一键性能对比
echo "原有:" && time pnpm run build:stage > /dev/null
echo "优化:" && time pnpm run build:stage:fast > /dev/null

# 一键启动测试服务器
pnpm run build:stage:fast && npx serve dist -l 3000

# 浏览器打开 http://localhost:3000 测试
```

现在你可以按照这个指南逐步验证所有的优化效果！ 🎯
