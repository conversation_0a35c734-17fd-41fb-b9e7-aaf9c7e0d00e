# Thunder ERP Docker本地测试指南

## 🎯 目标

通过Docker容器模拟真实生产环境，测试构建优化效果、蓝绿部署和缓存策略。

## 🏗️ 架构说明

### Docker化架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Nginx负载均衡器                          │
│  端口80: 主服务 │ 端口8081: 蓝色直接访问 │ 端口8082: 绿色直接访问 │
└─────────────────┬───────────────────────┬─────────────────────┘
                  │                       │
                  ▼                       ▼
         ┌─────────────────┐    ┌─────────────────┐
         │   蓝色版本应用   │    │   绿色版本应用   │
         │  thunder-app-blue│    │ thunder-app-green│
         │   (当前版本)     │    │   (新版本)      │
         └─────────────────┘    └─────────────────┘
```

### 端口分配

- **80**: 主服务入口 (蓝绿切换)
- **8080**: 静态资源服务器 (缓存策略测试)
- **8081**: 蓝色版本直接访问
- **8082**: 绿色版本直接访问

## 🚀 快速开始

### 方式1: 一键启动

```bash
# 快速启动测试环境
./scripts/docker-test/quick-start.sh

# 访问测试
open http://localhost        # 主服务
open http://localhost:8081   # 蓝色版本
open http://localhost:8082   # 绿色版本

# 停止服务
cd scripts/docker-test && docker-compose down
```

### 方式2: 完整测试流程

```bash
# 运行完整的蓝绿部署测试
./scripts/docker-test/test-docker-deployment.sh
```

## 📋 详细测试步骤

### 1. 环境准备

```bash
# 确保Docker运行
docker --version
docker-compose --version

# 进入测试目录
cd scripts/docker-test
```

### 2. 手动构建和启动

```bash
# 构建应用
cd ../../
pnpm run build:stage:fast

# 启动Docker服务
cd scripts/docker-test
docker-compose up -d --build

# 检查服务状态
docker-compose ps
```

### 3. 健康检查

```bash
# 检查容器健康状态
docker-compose ps
docker-compose logs

# 测试健康检查端点
curl http://localhost/health
curl http://localhost:8081/health
curl http://localhost:8082/health
```

### 4. 功能测试

```bash
# 测试主要端点
curl -I http://localhost/
curl -I http://localhost:8081/
curl -I http://localhost:8082/

# 测试静态资源
curl -I http://localhost:8080/js/
curl -I http://localhost:8080/assets/
```

## 🔄 蓝绿部署测试

### 手动蓝绿切换

```bash
# 1. 检查当前状态 (应该是蓝色)
curl -I http://localhost/ | grep X-Version

# 2. 模拟代码更新
echo "console.log('绿色版本')" >> ../../src/main.ts

# 3. 重新构建应用
cd ../../
pnpm run build:stage:fast

# 4. 重新构建绿色版本镜像
cd scripts/docker-test
docker-compose build app-green

# 5. 部署绿色版本
docker-compose up -d app-green

# 6. 等待绿色版本启动
sleep 10

# 7. 测试绿色版本
curl http://localhost:8082/health

# 8. 执行蓝绿切换
# 修改nginx.conf中的active_app配置
sed -i.bak 's/server app-blue:80/server app-green:80/' nginx.conf

# 9. 重新加载nginx配置
docker exec thunder-nginx-lb nginx -s reload

# 10. 验证切换结果
curl -I http://localhost/

# 11. 还原代码和配置
cd ../../
git checkout -- src/main.ts
cd scripts/docker-test
mv nginx.conf.bak nginx.conf
```

## 🧪 缓存策略测试

### HTTP缓存头测试

```bash
# 测试JS文件缓存 (应该有长期缓存头)
curl -I http://localhost:8080/js/index-[hash].js

# 测试CSS文件缓存
curl -I http://localhost:8080/assets/index-[hash].css

# 测试HTML文件 (应该无缓存)
curl -I http://localhost:8080/

# 测试图片资源
curl -I http://localhost:8080/assets/images/logo.png
```

### 预期缓存头

```http
# 带hash的JS/CSS文件
Cache-Control: public, immutable
Expires: 1年后

# HTML文件
Cache-Control: no-cache, no-store, must-revalidate
Pragma: no-cache
Expires: 0

# 图片和字体
Cache-Control: public
Expires: 7天后
```

## 📊 性能监控

### 容器资源监控

```bash
# 实时资源使用
docker stats

# 特定容器资源使用
docker stats thunder-nginx-lb thunder-app-blue thunder-app-green

# 容器详细信息
docker inspect thunder-app-blue
```

### 日志监控

```bash
# 查看所有日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f nginx-lb
docker-compose logs -f app-blue
docker-compose logs -f app-green

# 查看nginx访问日志
tail -f scripts/docker-test/logs/access.log
```

## 🧹 清理和维护

### 日常清理

```bash
# 停止所有服务
docker-compose down

# 清理未使用的镜像和容器
docker system prune -f

# 强制重建所有镜像
docker-compose build --no-cache
```

### 故障排查

```bash
# 检查容器状态
docker-compose ps

# 进入容器调试
docker exec -it thunder-nginx-lb sh
docker exec -it thunder-app-blue sh

# 检查网络连接
docker network ls
docker network inspect thunder-network

# 检查端口占用
netstat -tulpn | grep :80
```

## 🔧 配置文件说明

### docker-compose.yml

- 定义nginx负载均衡器和蓝绿两个应用容器
- 配置网络、卷挂载和健康检查
- 端口映射和服务依赖

### nginx.conf

- 负载均衡配置
- 蓝绿部署上游服务器定义
- 缓存策略配置
- 健康检查端点

### Dockerfile.app

- 基于nginx:alpine的应用镜像
- 包含构建产物和nginx配置
- 健康检查定义

### app-nginx.conf

- 应用内部nginx配置
- SPA路由处理
- 静态资源缓存策略
- Gzip压缩配置

## ✅ 测试检查清单

```bash
# 环境检查
[ ] Docker和docker-compose已安装
[ ] 端口80、8080、8081、8082未被占用
[ ] 项目构建正常

# 功能测试
[ ] 容器正常启动
[ ] 健康检查通过
[ ] 主服务正常访问
[ ] 蓝绿版本独立访问正常
[ ] 静态资源服务器正常

# 蓝绿部署测试
[ ] 绿色版本构建成功
[ ] 蓝绿切换无停机时间
[ ] 切换后服务正常
[ ] 版本标识正确

# 缓存策略测试
[ ] JS/CSS文件长期缓存头正确
[ ] HTML文件无缓存头
[ ] 静态资源缓存头正确
[ ] 浏览器缓存行为符合预期

# 性能测试
[ ] 容器资源使用合理
[ ] 响应时间正常
[ ] 构建时间优化明显
[ ] 日志记录正常
```

## 🎯 使用场景

### 开发阶段

- 验证构建优化效果
- 测试容器化配置
- 调试nginx配置

### 预发布测试

- 模拟生产环境部署
- 验证蓝绿切换流程
- 测试缓存策略

### 培训和演示

- 展示部署流程
- 培训运维团队
- 技术方案验证

这个Docker化的测试环境让你能够在本地完整模拟生产环境的部署流程！🚀
