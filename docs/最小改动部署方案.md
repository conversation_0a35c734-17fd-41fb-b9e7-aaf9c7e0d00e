# 🔧 Thunder ERP 最小改动部署方案

## 🎯 现状分析

- ❌ 服务器打包脚本**不由我们维护**
- ❌ 服务器端修改成本高/难度大
- ✅ 需要**最小化改动**解决问题
- ✅ 客户端构建优化在我们控制范围内

## ⚡ 立即可行方案

### 方案1: 仅修改rsync参数（推荐★★★★★）

**服务器端需要的唯一改动**：

```bash
# 🔴 现有命令（有问题）
rsync -a --delete /path/to/dist/ target:/path/to/dist;

# 🟢 修改后命令（安全）
rsync -a /path/to/dist/ target:/path/to/dist;
```

**改动内容**：仅移除 `--delete` 参数

**效果**：

- ✅ 立即解决用户404问题
- ✅ 新旧文件共存，用户体验无缝
- ✅ 对现有流程影响最小

### 方案2: 客户端构建优化（我们控制）

在你们的项目中使用已有的优化配置：

```bash
# 使用快速构建（25-30%性能提升）
pnpm run build:stage:fast

# 原有构建（保持兼容）
pnpm run build:stage
```

**这些完全在我们控制范围内**：

- ✅ `vite.config.fast-build.ts` 已就绪
- ✅ package.json scripts 已配置
- ✅ 不依赖服务器端修改

## 📋 实施计划

### 阶段1: 立即修复（1分钟）

**需要协调的改动**：

```bash
# 服务器端rsync命令中移除 --delete 参数
# 这是唯一需要服务器端配合的改动
```

**我们这边**：

```bash
# 立即使用优化构建
pnpm run build:stage:fast
```

### 阶段2: 客户端优化（已完成）

**已实现的优化**：

- ✅ 快速构建配置（25-30%性能提升）
- ✅ 优化的分块策略
- ✅ 减少重复依赖
- ✅ 移除冲突的手动分块

### 阶段3: 可选进阶（如果服务器端支持）

**如果服务器端愿意后续配合**，可以考虑：

- 智能清理脚本
- 蓝绿部署
- 监控和告警

但这些都是**可选的**，不影响核心问题解决。

## 🔧 技术细节

### 文件共存原理

```bash
# 部署前
dist/
├── index.html
├── js/app-abc123.js     # 旧版本
└── css/main-def456.css  # 旧版本

# 移除--delete后的部署结果
dist/
├── index.html           # 新版本（指向新文件）
├── js/
│   ├── app-abc123.js   # 旧版本 ← 仍可访问
│   └── app-xyz789.js   # 新版本 ← 新用户使用
└── css/
    ├── main-def456.css # 旧版本 ← 仍可访问
    └── main-uvw012.css # 新版本 ← 新用户使用
```

### 为什么这样安全？

1. **旧用户**：浏览器缓存中的文件名（如 app-abc123.js）仍然存在，可以正常访问
2. **新用户**：获取最新的 index.html，使用新的文件名（如 app-xyz789.js）
3. **部署过程**：没有文件被删除，不会出现404

### 磁盘空间管理

**自然清理**：随着时间推移，旧文件的访问会减少
**可选清理**：如果需要，可以后续协调定期清理（非必需）

## 💡 对服务器端的说服理由

### 风险最小

- 只改1个参数：`--delete` → 删除
- 不影响现有逻辑
- 可以随时回滚

### 用户价值巨大

- 彻底解决部署期间的404问题
- 提升用户体验
- 减少用户投诉

### 技术成熟

- rsync不使用--delete是标准做法
- 许多大型网站都采用类似策略
- 风险可控

## 🚀 快速测试验证

### 本地验证

```bash
# 1. 构建优化版本
pnpm run build:stage:fast

# 2. 模拟部署（不使用--delete）
rsync -av dist/ test_deploy/

# 3. 再次构建部署
pnpm run build:stage:fast
rsync -av dist/ test_deploy/

# 4. 检查文件共存
ls -la test_deploy/js/  # 应该看到新旧文件共存
```

### 生产验证建议

1. **低峰期测试**：选择访问量较小的时间段
2. **监控404率**：部署前后对比404错误率
3. **快速回滚**：如有问题，立即恢复--delete参数

## 📊 效果预期

### 立即效果（移除--delete）

| 指标        | 现状     | 改动后     |
| ----------- | -------- | ---------- |
| 部署期间404 | 经常发生 | 几乎消失   |
| 用户体验    | 可能中断 | 无感知更新 |
| 部署风险    | 中等     | 极低       |

### 中期效果（+构建优化）

| 指标     | 现状  | 优化后 |
| -------- | ----- | ------ |
| 构建时间 | ~180s | ~120s  |
| 性能提升 | 基准  | 25-30% |
| 维护成本 | 基准  | 降低   |

## 🎯 推荐行动

### 立即行动（今天）

1. **与服务器端沟通**：说明只需要移除rsync的--delete参数
2. **客户端使用优化构建**：`pnpm run build:stage:fast`

### 验证效果（本周）

1. **监控404错误率**
2. **观察用户反馈**
3. **测量构建时间改善**

### 长期优化（可选）

1. 如果服务器端愿意配合，可以考虑进阶方案
2. 但即使不进阶，现有方案也已经解决核心问题

## 🎉 总结

这个方案的最大优势是：

✅ **服务器端改动最小**：只需要删除1个参数
✅ **客户端优化自主**：25-30%构建性能提升
✅ **用户体验立即改善**：404问题彻底解决
✅ **风险完全可控**：可随时回滚

**关键是说服服务器端移除 `--delete` 参数，这是解决404问题的核心！**
