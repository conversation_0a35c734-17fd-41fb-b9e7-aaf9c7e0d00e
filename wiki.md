# Thunder ERP 前端 - 待完成问题记录

## 📋 当前待解决问题

### 1. 套餐明细显示问题 ⚠️

**问题描述：**

- 用户在套餐弹窗中选择了商品（如apple 2份），但购物车中显示"未选择任何商品"
- 可选组明细没有正确显示tag形式的UI

**当前状态：**

- ✅ 已修复：套餐弹窗默认数据填充问题
- ✅ 已修复：PackageDetailItem组件属性匹配问题
- ✅ 已修复：可选组数据转换逻辑
- ✅ 已修复：by_plan类型筛选逻辑
- ✅ 已修复：数据同步问题（单一数据源方案）
- ❌ **仍存在：套餐明细在购物车中的显示问题**

**技术细节：**

- 文件位置：`src/modules/room/components/dialogs/AddProductDialog.vue`
- 组件：PackageDetailItem
- 数据流：套餐弹窗 → convertToCartItem → createDetailRow → PackageDetailItem显示

**问题表现：**

1. 套餐确认后，购物车中的明细行显示不正确
2. 可选组商品应该显示为tag形式，但可能显示为纯文本或空白
3. 数据在两个阶段处理不一致：
   - 阶段1（convertToCartItem）：正确处理数据
   - 阶段2（createDetailRow/显示）：数据丢失或格式错误

**需要检查的重点：**

- PackageDetailItem组件的props数据结构
- createDetailRow函数的返回数据格式
- 可选组数据的字段映射（name/productName/title, quantity/selected_count/count）
- tag形式UI的实现逻辑

---

## 🔧 技术架构信息

**项目采用 VIPER-VC 架构模式：**

- View层：负责UI展示和用户交互 (\*.vue)
- IViewModel层：定义UI状态和行为 (viewmodel.ts)
- Presenter层：协调视图和业务逻辑 (presenter.ts)
- ViewModelConverter层：处理视图数据和业务数据的转换 (converter.ts)
- Interactor层：处理业务逻辑 (interactor.ts)

**核心技术栈：**

- Vue 3.5+、TypeScript 5.6+、Vite 5.4+
- 状态管理：Pinia 3.0+
- UI组件库：Element Plus 2.8+、Tailwind CSS 4+

**开发规范：**

- 数据处理禁止不清晰的类型，如果不清晰，打log来验证数据类型
- 保持分层职责清晰：View仅负责UI，Presenter负责状态管理和协调

---

## 📝 问题追踪日志

### 最近修复记录：

**2024-01-XX - 套餐弹窗默认填充修复**

- 问题：套餐弹窗打开时显示"还差一件"，无法直接点单
- 解决：改进默认选择逻辑，采用简单策略选择前N个商品各1个直到满足要求
- 文件：`src/modules/production/components/packageDialog/converter.ts`
- 方法：`getOptionalGroups()` 中的默认选择逻辑

**历史修复：**

- PackageDetailItem组件props名称匹配（props.item → props.row）
- 可选组数据解析和字段映射支持
- by_plan类型的正确筛选逻辑（检查selected_count > 0）
- 单一数据源避免同步问题

---

## 🎯 下一步行动计划

1. **调试套餐明细显示问题**

   - 在浏览器中测试套餐选择流程
   - 检查购物车明细行的数据结构
   - 验证PackageDetailItem组件的props接收情况

2. **验证UI显示效果**

   - 确认tag形式UI是否正确实现
   - 检查与设计图的一致性

3. **数据流完整性测试**
   - 从套餐弹窗到购物车显示的完整数据链路测试
   - 确保各种套餐类型（普通套餐、可选组套餐、by_plan类型）都能正确显示

---

## 📞 联系信息

**开发环境启动：**

```bash
cd /Users/<USER>/Documents/Gitlab/thunder-erp-client
npm run dev:client-pad
```

**注意：** 如遇到 `@vitejs/plugin-legacy` 错误，需要先安装相关依赖。

---

_最后更新：2024-01-XX_
_状态：待完成_
