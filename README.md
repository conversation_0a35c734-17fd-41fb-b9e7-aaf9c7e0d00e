# thunder_erp_client

本项目使用 Vue 3 和 Vite 开发。

## 重要说明：包管理器

**本项目强制使用 pnpm 作为包管理器。请勿使用 npm 或 yarn 安装依赖，这将导致错误。**

原因：

1. 本项目使用了 `workspace:` 协议，npm 和 yarn 不支持此协议
2. pnpm 提供更快的安装速度和更高效的磁盘空间利用
3. pnpm 的严格依赖管理可以防止依赖提升导致的问题

如果您尝试使用 npm 或 yarn，安装将会失败并显示错误信息。

## 快速开始

为了简化安装过程，我们提供了自动安装脚本：

### macOS/Linux 用户

```bash
# 给脚本添加执行权限
chmod +x setup.sh

# 运行安装脚本
./setup.sh
```

### Windows 用户

```bash
# 运行安装脚本
setup.bat
```

这些脚本会自动检查 pnpm 是否已安装，如果没有则会安装 pnpm，然后使用 pnpm 安装项目依赖。

## 推荐的 IDE 设置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (禁用 Vetur)。

## 自定义配置

查看 [Vite 配置参考](https://vite.dev/config/)。

## 项目设置

```sh
# 安装 pnpm (如果尚未安装)
npm install -g pnpm

# 安装依赖
pnpm install
```

### 开发环境编译和热重载

```sh
# 主应用开发 (端口5173, 客户端类型: client)
pnpm dev

# Pad应用开发 (端口5174, 客户端类型: pad)
pnpm dev:client-pad
```

**重要说明**：两个应用使用不同的端口和环境变量配置，确保功能隔离：

- 主应用: `http://localhost:5173` - 使用 `.env.development` 配置
- Pad应用: `http://localhost:5174` - 使用 `.env.client-pad.development` 配置

### stage环境开发和构建

```sh
# stage环境开发
pnpm dev:stage

# stage环境构建
pnpm stage
# 或
pnpm build:stage

# Pad应用 stage环境构建
pnpm build:client-pad:stage
```

### Pad应用构建

```sh
# Pad应用开发环境构建
pnpm build:client-pad

# Pad应用生产环境构建
pnpm build:client-pad:prod
```

### 环境配置文件

项目支持多种环境配置，**主应用**和**Client-Pad应用**使用不同的环境文件，实现配置隔离：

#### 主应用环境文件

- `.env`：默认环境配置
- `.env.development`：开发环境配置 (包含 `VITE_CLIENT_TYPE=client`)
- `.env.stage`：测试/预发布环境配置
- `.env.production`：生产环境配置

#### Client-Pad应用环境文件

- `.env.client-pad`：Pad应用默认配置
- `.env.client-pad.development`：Pad应用开发环境配置 (包含 `VITE_CLIENT_TYPE=pad`)
- `.env.client-pad.stage`：Pad应用测试/预发布环境配置
- `.env.client-pad.production`：Pad应用生产环境配置

#### 环境变量隔离机制

**VITE_CLIENT_TYPE** 是一个关键的环境变量，用于区分客户端类型：

| 应用类型   | 端口 | 配置文件                    | 环境变量来源                  | VITE_CLIENT_TYPE值 |
| ---------- | ---- | --------------------------- | ----------------------------- | ------------------ |
| 主应用     | 5173 | `vite.config.ts`            | `.env.development`            | `client`           |
| Client-Pad | 5174 | `vite.client-pad.config.ts` | `.env.client-pad.development` | `pad`              |

**原理说明**：

- 主应用使用标准的Vite环境变量加载机制，从 `.env.development` 读取
- Client-Pad应用通过自定义配置，优先从 `.env.client-pad.development` 读取客户端类型
- 这样确保了两个应用的环境变量完全隔离，互不影响

创建`.env.stage`文件示例：

```
# Stage环境配置
NODE_ENV=production

# API基础路径
VITE_APP_BASE_API=https://stage-api.thunder-erp.com

# NATS服务器配置
VITE_NATS_SERVER=wss://stage-nats.thunder-erp.com
VITE_NATS_TOKEN=stage_token
VITE_NATS_USER=stage_user
VITE_NATS_PASSWORD=stage_password

# 其他配置
VITE_ENABLE_VCONSOLE=false
```

创建`.env.client-pad.production`文件示例：

```
# Client Pad生产环境配置
VITE_APP_TITLE=Thunder ERP Client Pad
VITE_APP_TYPE=client-pad
VITE_ENABLE_PAD_FEATURES=true

# 客户端类型标识 (Pad应用必须设置为pad)
VITE_CLIENT_TYPE=pad

# API配置
VITE_APP_BASE_API=https://me.ktvsky.com

# NATS配置
VITE_NATS_SERVER=wss://nats.ktvsky.com
VITE_NATS_TOKEN=your_token_here
```

#### 验证环境变量配置

启动应用后，可以通过浏览器控制台验证环境变量是否正确加载：

```bash
# 启动主应用 (端口5173)
pnpm run dev
# 控制台应显示: [Request] clientType: client

# 启动Client-Pad应用 (端口5174)
pnpm run dev:client-pad
# 控制台应显示: [Request] clientType: pad
```

### 更新api 对应的接口调用代码

```sh
pnpm run generate-api
```

### 生产环境编译和压缩

```sh
pnpm build
```

### 使用 [Vitest](https://vitest.dev/) 运行单元测试

```sh
pnpm test:unit
```

### 使用 Storybook 运行组件开发环境

```sh
pnpm storybook
```

## 技术栈

- Vue 3.5+
- Vite 5.4+
- TypeScript 5.6+
- Pinia 2.2+
- Element Plus 2.8+
- Storybook 8.6+

## 浏览器兼容性

项目使用 `@vitejs/plugin-legacy` 确保良好的浏览器兼容性。目前支持以下浏览器：

- Chrome >= 49
- Safari >= 10
- Firefox >= 52
- Edge >= 14
- iOS >= 10
- Android >= 6

构建产物针对 ES2015 (ES6) 标准，同时为较旧的浏览器提供兼容性支持。

## 构建配置

### 开发环境

```sh
# 开发环境构建
pnpm dev

# 测试环境构建
pnpm build:dev
```

### 生产环境

```sh
# 标准生产构建
pnpm build

# 完整生产构建（包含压缩）
pnpm build:prod
```

### Pad应用构建配置

Pad应用使用单独的Vite配置文件`vite.client-pad.config.ts`，入口文件为`client-pad.html`：

```sh
# Pad应用预览
pnpm preview:client-pad
```

### 主要构建配置

- **目标平台**: ES2015 (ES6)
- **压缩方式**: Terser
- **代码分割**: 按功能模块（vue-vendor, element-plus, utils, form 等）
- **体积警告限制**: 1000KB

## 代码风格与格式化

项目使用 ESLint 和 Prettier 强制代码风格一致性：

- **缩进**: 4 空格
- **字符串**: 单引号
- **分号**: 使用语句结尾分号
- **行宽**: 最大 160 字符
- **对象格式**: 括号内保留空格

VSCode 用户推荐安装以下扩展并使用项目的 `.vscode` 配置：

- ESLint
- Prettier
- Volar (Vue Language Features)
