# Thunder ERP Client Wiki

欢迎来到 Thunder ERP Client 项目的 Wiki 文档！

## 文档目录

### 1. 项目架构

- [项目整体架构](./architecture/project-architecture.md) - 项目的整体技术架构和设计理念
- [VIPER-VC架构详解](./architecture/viper-vc-architecture.md) - 详细介绍项目采用的VIPER-VC架构模式

### 2. 核心功能模块

- [开台功能深度分析](./modules/open-table-analysis.md) - 开台功能的完整分析和实现细节
- [点单功能深度分析](./modules/ordering-analysis.md) - 点单功能的完整分析和实现细节

### 3. 技术文档

- [环境配置](./technical/environment-setup.md) - 开发环境配置指南
- [代码规范](./technical/coding-standards.md) - 项目代码规范和最佳实践
- [OpenAPI生成器](./technical/openapi-generator.md) - API代码自动生成工具使用指南
- [Electron桌面应用](./technical/electron-desktop-guide.md) - Windows桌面应用开发指南
- [部署指南](./technical/deployment-guide.md) - 前端部署最佳实践和策略

### 4. 开发指南

- [新功能开发流程](./development/feature-development.md) - 如何在项目中开发新功能
- [测试指南](./development/testing-guide.md) - 项目测试策略和方法

## 快速开始

如果您是第一次接触这个项目，建议按以下顺序阅读文档：

1. 首先阅读 [项目整体架构](./architecture/project-architecture.md) 了解项目概况
2. 然后阅读 [VIPER-VC架构详解](./architecture/viper-vc-architecture.md) 理解代码组织方式
3. 接着阅读核心功能模块文档，了解业务实现
4. 最后根据需要查阅技术文档和开发指南

## 贡献指南

欢迎为这个Wiki贡献内容！请确保：

- 文档内容准确、清晰
- 代码示例可以正常运行
- 遵循项目的文档格式规范

## 联系我们

如果您在使用过程中遇到问题，或者有改进建议，请通过以下方式联系我们：

- 提交 Issue
- 发起 Pull Request
- 联系项目维护者
