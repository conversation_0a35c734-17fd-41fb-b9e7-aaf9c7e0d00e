# Thunder ERP Client Wiki

欢迎来到 Thunder ERP Client 项目的 Wiki 文档中心！这里包含了项目的完整技术文档、架构设计和开发指南。

## 📚 文档导航

### 1. 项目架构

- [项目整体架构](./项目架构/项目整体架构.md) - 系统架构概览和技术栈介绍
- [VIPER-VC架构详解](./项目架构/VIPER-VC架构详解.md) - 核心架构模式深度解析

### 2. 核心功能模块

- [开台功能深度分析](./核心功能模块/开台功能深度分析.md) - 开台业务流程和技术实现
- [点单功能深度分析](./核心功能模块/点单功能深度分析.md) - 点单系统的完整分析
- [财务管理功能深度分析](./核心功能模块/财务管理功能深度分析.md) - 收银结算、财务报表、交班管理系统
- [订单管理功能深度分析](./核心功能模块/订单管理功能深度分析.md) - 订单创建、支付处理、退款管理系统
- [会员管理功能深度分析](./核心功能模块/会员管理功能深度分析.md) - 会员注册、充值消费、等级权益系统

### 3. 技术文档

- [环境配置指南](./技术文档/环境配置指南.md) - 开发环境配置指南
- [代码规范和最佳实践](./技术文档/代码规范和最佳实践.md) - 项目代码规范和最佳实践
- [OpenAPI代码生成器使用指南](./技术文档/OpenAPI代码生成器使用指南.md) - API代码自动生成工具使用指南
- [Electron桌面应用开发指南](./技术文档/Electron桌面应用开发指南.md) - Windows桌面应用开发指南
- [前端部署指南](./技术文档/前端部署指南.md) - 前端部署最佳实践和策略

### 4. 开发指南

- [新功能开发流程](./开发指南/新功能开发流程.md) - 标准化的功能开发流程
- [测试指南](./开发指南/测试指南.md) - 完整的测试策略和实践

## 🚀 快速开始

如果您是第一次接触这个项目，建议按以下顺序阅读文档：

1. 首先阅读 [项目整体架构](./项目架构/项目整体架构.md) 了解项目概况
2. 然后阅读 [VIPER-VC架构详解](./项目架构/VIPER-VC架构详解.md) 理解代码组织方式
3. 接着阅读核心功能模块文档，了解业务实现
4. 最后根据需要查阅技术文档和开发指南

## 📖 文档特色

### 中文优先

所有文档均使用中文编写，便于中文开发者理解和使用。

### 实战导向

文档不仅包含理论知识，更注重实际代码示例和最佳实践。

### 架构驱动

基于VIPER-VC架构的完整文档体系，确保代码质量和可维护性。

### 持续更新

文档与代码同步更新，确保内容的准确性和时效性。

## 贡献指南

欢迎为这个Wiki贡献内容！请确保：

- 文档内容准确、清晰
- 代码示例可以正常运行
- 遵循项目的文档格式规范

## 联系我们

如果您在使用过程中遇到问题，或者有改进建议，请通过以下方式联系我们：

- 提交 Issue
- 发起 Pull Request
- 联系项目维护者
