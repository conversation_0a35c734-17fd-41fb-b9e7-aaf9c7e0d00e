# 测试指南

## 测试策略概述

Thunder ERP Client 项目采用多层次的测试策略，确保代码质量和系统稳定性。

### 测试金字塔

```mermaid
graph TD
    A[E2E测试 - 少量] --> B[集成测试 - 适量]
    B --> C[单元测试 - 大量]
    
    style A fill:#ff9999
    style B fill:#ffcc99
    style C fill:#99ff99
```

## 测试环境配置

### 1. 测试工具栈

- **测试框架**: Vitest
- **组件测试**: Vue Test Utils
- **E2E测试**: Playwright
- **覆盖率**: c8
- **Mock工具**: Vitest Mock

### 2. 配置文件

#### vitest.config.js
```javascript
import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'happy-dom',
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  }
});
```

#### playwright.config.ts
```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
});
```

## 单元测试

### 1. Presenter层测试

```typescript
// openTable.presenter.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useOpenTablePresenter } from './presenter';
import { OpenTableInteractor } from './interactor';
import { OpenTableConverter } from './converter';

// Mock依赖
vi.mock('./interactor');
vi.mock('./converter');

describe('OpenTablePresenter', () => {
  let presenter: ReturnType<typeof useOpenTablePresenter>;
  let mockInteractor: vi.Mocked<OpenTableInteractor>;
  let mockConverter: vi.Mocked<OpenTableConverter>;

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks();
    
    // 创建mock实例
    mockInteractor = vi.mocked(new OpenTableInteractor());
    mockConverter = vi.mocked(new OpenTableConverter());
    
    // 创建presenter实例
    presenter = useOpenTablePresenter();
  });

  describe('初始化', () => {
    it('应该正确初始化状态', () => {
      expect(presenter.state.isLoading).toBe(false);
      expect(presenter.state.roomBill).toBeNull();
      expect(presenter.state.marketBill).toBeNull();
      expect(presenter.state.addedProducts).toEqual([]);
    });

    it('应该正确初始化表单数据', () => {
      expect(presenter.state.form.booker).toBe('');
      expect(presenter.state.form.settleImmediately).toBe(true);
      expect(presenter.state.form.minimumCharge).toBe(0);
    });
  });

  describe('数据加载', () => {
    it('应该正确加载开台视图数据', async () => {
      const mockData = {
        roomBill: { amount: 10000, type: 'buyout' },
        marketBill: { products: [] }
      };

      mockInteractor.fetchOpenView.mockResolvedValue(mockData);

      await presenter.actions.fetchOpenView();

      expect(presenter.state.isLoading).toBe(false);
      expect(presenter.state.roomBill).toEqual(mockData.roomBill);
      expect(presenter.state.marketBill).toEqual(mockData.marketBill);
    });

    it('应该正确处理加载错误', async () => {
      const errorMessage = '网络错误';
      mockInteractor.fetchOpenView.mockRejectedValue(new Error(errorMessage));

      await presenter.actions.fetchOpenView();

      expect(presenter.state.isLoading).toBe(false);
      expect(presenter.state.error).toBe(errorMessage);
    });
  });

  describe('商品管理', () => {
    it('应该正确添加商品', () => {
      const product = {
        id: '1',
        name: '测试商品',
        price: 1000,
        quantity: 1
      };

      presenter.actions.handleAddProduct([product]);

      expect(presenter.state.addedProducts).toContain(product);
    });

    it('应该正确计算总金额', () => {
      presenter.state.roomBill = { amount: 10000 };
      presenter.state.addedProducts = [
        { id: '1', price: 1000, quantity: 2 },
        { id: '2', price: 500, quantity: 1 }
      ];

      expect(presenter.computed.totalBill.value).toBe(12500);
    });
  });

  describe('表单验证', () => {
    it('应该验证必填字段', () => {
      presenter.state.form.booker = '';
      expect(presenter.actions.validateCart()).toBe(false);

      presenter.state.form.booker = '张三';
      expect(presenter.actions.validateCart()).toBe(true);
    });

    it('应该验证商品选择', () => {
      presenter.state.marketBill = {
        packageVOs: [{
          optionalGroups: [{
            minSelect: 2,
            selectedCount: 1
          }]
        }]
      };

      expect(presenter.computed.isAllCartItemsValid.value).toBe(false);
    });
  });

  describe('开台操作', () => {
    it('应该正确处理开台成功', async () => {
      const mockResult = {
        success: true,
        data: { sessionId: 'test-session' }
      };

      mockInteractor.openTable.mockResolvedValue(mockResult);

      const result = await presenter.actions.handleImmediatePay();

      expect(result).toEqual(mockResult.data);
      expect(presenter.state.isLoading).toBe(false);
    });

    it('应该正确处理开台失败', async () => {
      const mockError = new Error('开台失败');
      mockInteractor.openTable.mockRejectedValue(mockError);

      const result = await presenter.actions.handleImmediatePay();

      expect(result).toBeNull();
      expect(presenter.state.isLoading).toBe(false);
    });
  });
});
```

### 2. Converter层测试

```typescript
// openTable.converter.test.ts
import { describe, it, expect } from 'vitest';
import { OpenTableConverter } from './converter';

describe('OpenTableConverter', () => {
  describe('buildOpenTableParams', () => {
    it('应该正确构建开台参数', () => {
      const form = {
        booker: '张三',
        roomManager: '李四',
        consumptionMode: 'buyout',
        settleImmediately: true
      };

      const roomBill = {
        type: 'buyout',
        amount: 10000,
        duration: 240
      };

      const marketBill = {
        products: [
          { id: '1', quantity: 2, price: 500 }
        ]
      };

      const addedProducts = [
        { id: '2', quantity: 1, price: 1000 }
      ];

      const result = OpenTableConverter.buildOpenTableParams(
        form,
        roomBill,
        marketBill,
        addedProducts
      );

      expect(result).toEqual({
        booker: '张三',
        roomManager: '李四',
        consumptionMode: 'buyout',
        settleImmediately: true,
        billingType: 'buyout',
        amount: 10000,
        duration: 240,
        products: [
          { id: '1', quantity: 2, price: 500 },
          { id: '2', quantity: 1, price: 1000 }
        ]
      });
    });
  });

  describe('数据转换', () => {
    it('应该正确转换商品数据', () => {
      const rawProduct = {
        productId: '1',
        productName: '测试商品',
        unitPrice: 1000,
        qty: 2
      };

      const result = OpenTableConverter.convertProduct(rawProduct);

      expect(result).toEqual({
        id: '1',
        name: '测试商品',
        price: 1000,
        quantity: 2,
        totalPrice: 2000
      });
    });
  });
});
```

## 组件测试

### 1. Vue组件测试

```typescript
// OpenTable.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import { ElButton, ElTabs } from 'element-plus';
import OpenTable from './index.vue';

// Mock presenter
const mockPresenter = {
  state: {
    isLoading: false,
    activeTab: 'buyout',
    roomBill: { amount: 10000 },
    marketBill: { products: [] },
    addedProducts: [],
    form: {
      booker: '',
      settleImmediately: true
    }
  },
  computed: {
    totalBill: { value: 10000 },
    isAllCartItemsValid: { value: true }
  },
  actions: {
    handleImmediatePay: vi.fn(),
    handleAddProduct: vi.fn(),
    fetchOpenView: vi.fn()
  }
};

vi.mock('./presenter', () => ({
  useOpenTable: () => mockPresenter
}));

describe('OpenTable', () => {
  it('应该正确渲染组件', () => {
    const wrapper = mount(OpenTable, {
      global: {
        components: {
          ElButton,
          ElTabs
        }
      }
    });

    expect(wrapper.find('.open-table-container').exists()).toBe(true);
    expect(wrapper.find('.billing-section').exists()).toBe(true);
    expect(wrapper.find('.product-section').exists()).toBe(true);
  });

  it('应该显示正确的总金额', () => {
    const wrapper = mount(OpenTable);
    
    expect(wrapper.text()).toContain('¥100.00');
  });

  it('应该在点击开台按钮时调用处理函数', async () => {
    const wrapper = mount(OpenTable);
    const button = wrapper.find('[data-test="submit-button"]');

    await button.trigger('click');

    expect(mockPresenter.actions.handleImmediatePay).toHaveBeenCalled();
  });

  it('应该在加载状态时禁用按钮', async () => {
    mockPresenter.state.isLoading = true;
    
    const wrapper = mount(OpenTable);
    const button = wrapper.find('[data-test="submit-button"]');

    expect(button.attributes('disabled')).toBeDefined();
  });

  it('应该在表单无效时禁用按钮', async () => {
    mockPresenter.computed.isAllCartItemsValid.value = false;
    
    const wrapper = mount(OpenTable);
    const button = wrapper.find('[data-test="submit-button"]');

    expect(button.attributes('disabled')).toBeDefined();
  });
});
```

## E2E测试

### 1. 开台流程测试

```typescript
// tests/e2e/open-table.spec.ts
import { test, expect } from '@playwright/test';

test.describe('开台功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/room/1/open');
  });

  test('完整开台流程', async ({ page }) => {
    // 1. 填写开台人
    await page.fill('[data-test="booker-input"]', '张三');

    // 2. 选择计费方案
    await page.click('[data-test="buyout-tab"]');

    // 3. 添加商品
    await page.click('[data-test="add-product-button"]');
    await page.click('[data-test="product-item-1"]');
    await page.click('[data-test="confirm-product"]');

    // 4. 确认开台
    await page.click('[data-test="submit-button"]');

    // 5. 验证成功
    await expect(page.locator('[data-test="success-message"]')).toBeVisible();
  });

  test('表单验证', async ({ page }) => {
    // 不填写开台人直接提交
    await page.click('[data-test="submit-button"]');

    // 验证错误提示
    await expect(page.locator('[data-test="error-message"]')).toContainText('请填写开台人');
  });

  test('商品选择验证', async ({ page }) => {
    await page.fill('[data-test="booker-input"]', '张三');
    
    // 添加套餐但不选择必选项
    await page.click('[data-test="add-product-button"]');
    await page.click('[data-test="package-item-1"]');
    // 不选择必选项直接确认
    await page.click('[data-test="confirm-product"]');

    // 验证套餐验证错误
    await expect(page.locator('[data-test="package-error"]')).toBeVisible();
  });
});
```

### 2. 点单流程测试

```typescript
// tests/e2e/product-order.spec.ts
import { test, expect } from '@playwright/test';

test.describe('点单功能', () => {
  test('加菜点单流程', async ({ page }) => {
    // 进入点单页面
    await page.goto('/production/order?mode=add2room&sessionId=test-session');

    // 选择商品分类
    await page.click('[data-test="category-drinks"]');

    // 添加商品到购物车
    await page.click('[data-test="product-cola"]');
    await page.click('[data-test="add-to-cart"]');

    // 查看购物车
    await page.click('[data-test="cart-button"]');
    await expect(page.locator('[data-test="cart-item"]')).toContainText('可乐');

    // 提交订单
    await page.click('[data-test="submit-order"]');

    // 验证成功
    await expect(page.locator('[data-test="order-success"]')).toBeVisible();
  });

  test('套餐配置', async ({ page }) => {
    await page.goto('/production/order');

    // 选择套餐商品
    await page.click('[data-test="package-combo-a"]');

    // 配置套餐选项
    await page.click('[data-test="option-group-1-item-1"]');
    await page.click('[data-test="option-group-2-item-2"]');

    // 确认套餐配置
    await page.click('[data-test="confirm-package"]');

    // 验证购物车中的套餐
    await expect(page.locator('[data-test="cart-package"]')).toContainText('套餐A');
  });
});
```

## 测试命令

### 运行测试
```bash
# 运行所有单元测试
pnpm test

# 运行特定测试文件
pnpm test openTable.presenter.test.ts

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行E2E测试
pnpm test:e2e

# 运行E2E测试（特定浏览器）
pnpm test:e2e --project=chromium

# 监听模式运行测试
pnpm test:watch
```

### 调试测试
```bash
# 调试模式运行测试
pnpm test --inspect-brk

# 使用UI界面运行测试
pnpm test:ui

# 调试E2E测试
pnpm test:e2e --debug
```

## 测试最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 "应该...当...时" 的格式
- 使用中文描述业务场景

### 2. 测试结构
- 使用 AAA 模式：Arrange, Act, Assert
- 每个测试只验证一个功能点
- 保持测试的独立性

### 3. Mock策略
- Mock外部依赖（API、第三方库）
- 不要Mock被测试的代码
- 使用类型安全的Mock

### 4. 测试数据
- 使用有意义的测试数据
- 避免硬编码，使用工厂函数
- 保持测试数据的简洁性

### 5. 断言
- 使用具体的断言而不是通用断言
- 验证重要的业务逻辑
- 包含错误场景的测试

通过遵循这些测试指南和最佳实践，可以确保项目的代码质量和系统稳定性。
