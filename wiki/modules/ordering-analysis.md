# 点单功能深度分析

## 功能概述

点单功能是KTV收银系统中客户消费的重要环节，支持多种点单模式，包括开台时点单、包厢内加菜、以及独立的点单操作。系统提供了丰富的商品选择、购物车管理、套餐配置等功能。

## 业务流程

### 1. 点单流程图

```mermaid
graph TD
    A[进入点单页面] --> B[选择商品分类]
    B --> C[浏览商品列表]
    C --> D{商品类型}
    D -->|普通商品| E[直接加入购物车]
    D -->|套餐商品| F[配置套餐选项]
    F --> G[确认套餐配置]
    G --> E
    E --> H[管理购物车]
    H --> I{继续选择?}
    I -->|是| C
    I -->|否| J[确认下单]
    J --> K{点单模式}
    K -->|开台点单| L[返回开台页面]
    K -->|加菜点单| M[提交订单]
    K -->|独立点单| N[选择包厢]
    M --> O[打印出品单]
    N --> O
    L --> P[继续开台流程]
    O --> Q[点单完成]
```

### 2. 点单模式

#### 开台点单模式 (normal)

- 在开台过程中选择商品
- 商品信息返回给开台页面
- 与房费一起结算

#### 加菜点单模式 (add2room)

- 为已开台包厢追加商品
- 直接提交订单
- 立即生效并打印出品单

#### 独立点单模式

- 独立的点单操作
- 需要选择目标包厢
- 支持挂单功能

### 3. 商品类型

#### 普通商品

- 单一商品项
- 固定价格
- 直接数量选择

#### 套餐商品

- 包含多个子商品
- 可选组配置
- 复杂价格计算

#### 赠品商品

- 满足条件自动赠送
- 不计入总价
- 特殊标识显示

## 技术实现

### 1. 文件结构

```
src/modules/production/views/OpenTableProduct/
├── index.vue                    # View层 - 点单主界面
├── viewmodel.ts                 # ViewModel接口定义
├── presenter.ts                 # Presenter层 - 业务协调
├── converter.ts                 # 数据转换层
├── interactor.ts                # Interactor层 - 业务逻辑
└── components/                  # 页面组件
    └── PackageDialog/           # 套餐配置弹窗

src/apps/client-pad/views/ProductOrder/
├── index.vue                    # Pad端点单界面
├── presenter.ts                 # Pad端业务逻辑
├── viewmodel.ts                 # Pad端ViewModel
└── components/                  # Pad端专用组件

src/modules/production/components/dialogs/OpenTableProductDialog/
├── index.vue                    # 点单弹窗组件
├── dialogPresenter.ts           # 弹窗业务逻辑
└── dialogViewModel.ts           # 弹窗ViewModel
```

### 2. 核心接口定义

#### IOpenTableProductViewModel

```typescript
export interface IOpenTableProductViewModel {
  state: IOpenTableProductState;
  computed: IOpenTableProductComputed;
  actions: IOpenTableProductActions;
}

export interface IOpenTableProductState {
  cartItems: Ref<ICartItem[]>; // 购物车商品
  loading: Ref<boolean>; // 加载状态
  packageDialogVisible: Ref<boolean>; // 套餐弹窗显示
  currentPackage: Ref<any>; // 当前配置套餐
}

export interface ICartItem {
  id: string; // 商品ID
  name: string; // 商品名称
  currentPrice: number; // 当前价格
  quantity: number; // 数量
  isPackage: boolean; // 是否套餐
  packageUniqueId?: string; // 套餐唯一标识
  selectedOptions?: any[]; // 已选选项
  isGift: boolean; // 是否赠品
}
```

### 3. 购物车管理

#### 添加商品到购物车

```typescript
// presenter.ts
addToCart: (product: any) => {
  // 检查商品状态
  if (product.status === 'sold_out') {
    ElMessage.warning('该商品已沽清');
    return;
  }

  // 处理套餐商品
  if (product.isPackage && hasOptionalGroups(product)) {
    _currentPackage.value = product;
    _packageDialogVisible.value = true;
    return;
  }

  // 普通商品直接添加
  const cartItem = OpenTableProductConverter.productToCartItem(product);

  // 检查是否已存在相同商品
  const existingItem = _cartItems.value.find(item => item.id === cartItem.id && (!item.isPackage || item.packageUniqueId === cartItem.packageUniqueId));

  if (existingItem) {
    existingItem.quantity += cartItem.quantity;
  } else {
    _cartItems.value.push(cartItem);
  }
};
```

#### 购物车数量管理

```typescript
// 增加数量
increaseQuantity: (item: ICartItem) => {
  item.quantity++;
},

// 减少数量
decreaseQuantity: (item: ICartItem) => {
  if (item.quantity > 1) {
    item.quantity--;
  } else {
    // 数量为1时移除商品
    _cartItems.value = _cartItems.value.filter(
      cartItem => cartItem.id !== item.id ||
      cartItem.packageUniqueId !== item.packageUniqueId
    );
  }
}
```

### 4. 套餐配置

#### 套餐选项处理

```typescript
// converter.ts
export class OpenTableProductConverter {
  static packageToCartItem(packageDetail: any): ICartItem {
    return {
      id: packageDetail.id,
      name: packageDetail.name,
      currentPrice: packageDetail.totalPrice,
      quantity: 1,
      isPackage: true,
      packageUniqueId: generateUniqueId(),
      selectedOptions: packageDetail.selectedOptions,
      isGift: false
    };
  }

  static hasOptionalGroups(product: any): boolean {
    return product.packageDetail?.optionalGroups?.some(group => group.products?.length > 0) || false;
  }
}
```

#### 套餐弹窗处理

```typescript
// 套餐确认处理
handlePackageConfirm: (packageData: any) => {
  const cartItem = OpenTableProductConverter.packageToCartItem(packageData);

  // 检查是否为编辑模式
  if (_editingPackageIndex.value !== -1) {
    _cartItems.value[_editingPackageIndex.value] = cartItem;
    _editingPackageIndex.value = -1;
  } else {
    _cartItems.value.push(cartItem);
  }

  _packageDialogVisible.value = false;
  _currentPackage.value = null;
};
```

### 5. 订单提交

#### 加菜点单处理

```typescript
// presenter.ts
async function handleAdditionalOrder() {
  const currentStage = interactor.getCurrentStage();
  interactor.validateStageInfo(currentStage);

  _loading.value = true;
  try {
    // 转换购物车数据为订单商品
    const orderProducts = OpenTableProductConverter.convertCartItemsToOrderProducts(_cartItems.value);

    // 提交加菜订单
    await interactor.submitAdditionalOrder({
      sessionId: currentStage.sessionVO.sessionId,
      venueId: currentStage.roomVO.venueId,
      roomId: currentStage.roomVO.id,
      currentTime: Date.now(),
      orderProductVOs: orderProducts,
      roomVO: currentStage.roomVO
    });

    ElMessage.success('点单成功');
    router.back();
  } finally {
    _loading.value = false;
  }
}
```

#### 数据转换处理

```typescript
// converter.ts
static convertCartItemsToOrderProducts(cartItems: ICartItem[]): Partial<OrderProductVO>[] {
  return cartItems.map(item => ({
    productId: item.id,
    productName: item.name,
    quantity: item.quantity,
    price: item.currentPrice,
    isPackage: item.isPackage,
    packageOptions: item.selectedOptions,
    isGift: item.isGift
  }));
}
```

### 6. Pad端点单

#### Pad端特殊处理

```typescript
// apps/client-pad/views/ProductOrder/presenter.ts
export class ProductOrderPresenter {
  private addToCart(product: ProductItem): void {
    // Pad端商品添加逻辑
    if (product.status === 'sold_out') {
      ElMessage.warning('该商品已沽清');
      return;
    }

    // 处理套餐商品
    if (product.isPackage) {
      if (ProductOrderConverter.hasOptionalGroups(product)) {
        this._state.currentPackage = product;
        this._state.packageDialogVisible = true;
        return;
      }
    }

    // 普通商品或无选项套餐直接添加
    const cartItem = product.isPackage ? ProductOrderConverter.packageToCartItem(product.packageDetail) : ProductOrderConverter.productToCartItem(product);

    this.mergeOrAddCartItem(cartItem);
  }

  private mergeOrAddCartItem(cartItem: CartItem): void {
    const existingItem = this._state.cartItems.find(
      item => item.id === cartItem.id && (!item.isPackage || !cartItem.isPackage || item.packageUniqueId === cartItem.packageUniqueId)
    );

    if (existingItem) {
      existingItem.quantity += cartItem.quantity;
    } else {
      this._state.cartItems.push(cartItem);
    }
  }
}
```

### 7. 购物车抽屉组件

#### CartDrawer组件

```typescript
// apps/client-pad/components/CartDrawer/presenter.ts
export class CartDrawerPresenter {
  private async confirmOrder(): Promise<void> {
    if (this.computed.isEmpty.value) {
      ElMessage.warning('购物车为空，无法下单');
      return;
    }

    try {
      const employeeId = this.userStore.userInfo?.employee?.id || '';

      // 调用立即下单API
      const result = await CartDrawerInteractor.submitOrder(this._state.cartItems, this.roomId, this.sessionId, employeeId);

      if (result) {
        ElMessage.success('下单成功');
        this.productOrderActions.clearCart();
        this.close();
        this.emitEvent('order-confirmed');
      }
    } catch (error) {
      console.error('下单出错:', error);
      ElMessage.error('下单出错，请重试');
    }
  }
}
```

## 关键特性

### 1. 多端支持

- **PC端**: 完整功能，支持复杂操作
- **Pad端**: 触屏优化，简化操作流程
- **弹窗模式**: 嵌入式点单，无需页面跳转

### 2. 实时计算

- 购物车总金额实时更新
- 商品数量实时统计
- 套餐价格动态计算

### 3. 数据验证

- 商品库存验证
- 套餐配置验证
- 订单数据验证

### 4. 状态管理

- 购物车状态持久化
- 页面状态恢复
- 多页面数据同步

### 5. 用户体验

- 流畅的动画效果
- 直观的操作反馈
- 友好的错误提示

## API接口

### 1. 点单相关接口

```typescript
// 加菜点单
POST /api/order/additional-order
{
  sessionId: string;
  venueId: string;
  roomId: string;
  orderProductVOs: OrderProductVO[];
}

// 立即下单
POST /api/order/immediate-order
{
  roomId: string;
  sessionId: string;
  products: ProductItem[];
  employeeId: string;
}
```

### 2. 商品查询接口

```typescript
// 获取商品分类
GET /api/product/categories

// 获取商品列表
GET /api/product/list?categoryId={categoryId}

// 获取套餐详情
GET /api/product/package/{packageId}
```

## 性能优化

### 1. 列表优化

- 虚拟滚动处理大量商品
- 图片懒加载
- 分页加载

### 2. 计算优化

- 计算属性缓存
- 防抖处理用户输入
- 异步计算复杂价格

### 3. 内存管理

- 组件销毁时清理状态
- 避免内存泄漏
- 合理使用响应式数据

## 测试策略

### 1. 单元测试

```typescript
// 购物车功能测试
describe('购物车管理', () => {
  test('添加普通商品', () => {
    const presenter = new ProductOrderPresenter();
    const product = { id: '1', name: '可乐', price: 500 };
    presenter.addToCart(product);
    expect(presenter.state.cartItems.length).toBe(1);
  });

  test('套餐商品处理', () => {
    const presenter = new ProductOrderPresenter();
    const packageProduct = {
      id: '2',
      name: '套餐A',
      isPackage: true,
      packageDetail: { optionalGroups: [] }
    };
    presenter.addToCart(packageProduct);
    expect(presenter.state.packageDialogVisible).toBe(true);
  });
});
```

### 2. 集成测试

- API接口调用测试
- 数据流转测试
- 组件交互测试

### 3. E2E测试

- 完整点单流程测试
- 多端兼容性测试
- 异常情况处理测试

## 常见问题

### 1. 购物车数据丢失

**问题**: 页面刷新后购物车清空
**解决**: 实现购物车数据本地存储和恢复机制

### 2. 套餐配置错误

**问题**: 套餐选项配置不正确
**解决**: 加强套餐数据验证和错误提示

### 3. 价格计算错误

**问题**: 总价计算不准确
**解决**: 检查价格计算逻辑，确保精度处理

### 4. 并发问题

**问题**: 多人同时点单导致数据冲突
**解决**: 实现乐观锁机制和冲突检测

## 扩展功能

### 1. 智能推荐

- 基于历史数据的商品推荐
- 关联商品推荐
- 个性化推荐算法

### 2. 语音点单

- 语音识别商品名称
- 语音数量输入
- 语音确认订单

### 3. 扫码点单

- 二维码扫描添加商品
- 批量商品导入
- 快速点单模式

### 4. 营销集成

- 优惠券自动应用
- 满减活动计算
- 会员价格优惠

## 数据流转详解

### 1. 点单数据流

```mermaid
sequenceDiagram
    participant U as 用户
    participant V as View
    participant P as Presenter
    participant C as Converter
    participant I as Interactor
    participant S as Store
    participant A as API

    U->>V: 选择商品
    V->>P: 触发addToCart
    P->>C: 转换商品数据
    C-->>P: 返回CartItem
    P->>P: 更新购物车状态
    P-->>V: 响应式更新UI

    U->>V: 确认下单
    V->>P: 触发handleConfirm
    P->>I: 验证并提交订单
    I->>C: 转换订单数据
    C-->>I: 返回OrderData
    I->>A: 调用点单API
    A-->>I: 返回结果
    I->>S: 更新本地状态
    I-->>P: 返回处理结果
    P-->>V: 显示结果反馈
```

### 2. 购物车状态管理

```typescript
// 购物车状态管理器
export class CartStateManager {
  private cartItems: Ref<ICartItem[]> = ref([]);
  private listeners: Set<Function> = new Set();

  // 添加商品到购物车
  addItem(item: ICartItem): void {
    const existingIndex = this.findExistingItemIndex(item);

    if (existingIndex >= 0) {
      this.cartItems.value[existingIndex].quantity += item.quantity;
    } else {
      this.cartItems.value.push({ ...item });
    }

    this.notifyListeners('item_added', item);
    this.saveToStorage();
  }

  // 更新商品数量
  updateQuantity(itemId: string, quantity: number): void {
    const item = this.cartItems.value.find(item => item.id === itemId);
    if (item) {
      if (quantity <= 0) {
        this.removeItem(itemId);
      } else {
        item.quantity = quantity;
        this.notifyListeners('quantity_updated', item);
        this.saveToStorage();
      }
    }
  }

  // 移除商品
  removeItem(itemId: string): void {
    const index = this.cartItems.value.findIndex(item => item.id === itemId);
    if (index >= 0) {
      const removedItem = this.cartItems.value.splice(index, 1)[0];
      this.notifyListeners('item_removed', removedItem);
      this.saveToStorage();
    }
  }

  // 清空购物车
  clear(): void {
    this.cartItems.value = [];
    this.notifyListeners('cart_cleared');
    this.clearStorage();
  }

  // 计算总金额
  getTotalAmount(): number {
    return this.cartItems.value.reduce((total, item) => total + item.currentPrice * item.quantity, 0);
  }

  // 计算总数量
  getTotalItems(): number {
    return this.cartItems.value.reduce((total, item) => total + item.quantity, 0);
  }

  // 持久化到本地存储
  private saveToStorage(): void {
    try {
      localStorage.setItem('cart_items', JSON.stringify(this.cartItems.value));
    } catch (error) {
      console.error('保存购物车数据失败:', error);
    }
  }

  // 从本地存储恢复
  restoreFromStorage(): void {
    try {
      const stored = localStorage.getItem('cart_items');
      if (stored) {
        this.cartItems.value = JSON.parse(stored);
      }
    } catch (error) {
      console.error('恢复购物车数据失败:', error);
      this.cartItems.value = [];
    }
  }

  // 查找已存在的商品
  private findExistingItemIndex(item: ICartItem): number {
    return this.cartItems.value.findIndex(
      existing => existing.id === item.id && (!existing.isPackage || !item.isPackage || existing.packageUniqueId === item.packageUniqueId)
    );
  }

  // 通知监听器
  private notifyListeners(event: string, data?: any): void {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('购物车事件监听器执行失败:', error);
      }
    });
  }

  // 添加事件监听器
  addListener(listener: Function): void {
    this.listeners.add(listener);
  }

  // 移除事件监听器
  removeListener(listener: Function): void {
    this.listeners.delete(listener);
  }
}
```

### 3. 套餐配置管理

```typescript
// 套餐配置管理器
export class PackageConfigManager {
  // 验证套餐配置
  static validatePackageConfig(packageData: any): ValidationResult {
    const errors: string[] = [];

    if (!packageData.optionalGroups) {
      return { isValid: true, errors: [] };
    }

    for (const group of packageData.optionalGroups) {
      const selectedCount = group.products?.filter(p => p.isSelected)?.length || 0;

      // 检查最小选择数量
      if (selectedCount < group.minSelect) {
        errors.push(`${group.name} 至少需要选择 ${group.minSelect} 项，当前已选择 ${selectedCount} 项`);
      }

      // 检查最大选择数量
      if (group.maxSelect > 0 && selectedCount > group.maxSelect) {
        errors.push(`${group.name} 最多只能选择 ${group.maxSelect} 项，当前已选择 ${selectedCount} 项`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 计算套餐总价
  static calculatePackagePrice(packageData: any): number {
    let totalPrice = packageData.basePrice || 0;

    if (packageData.optionalGroups) {
      for (const group of packageData.optionalGroups) {
        if (group.products) {
          for (const product of group.products) {
            if (product.isSelected) {
              totalPrice += (product.price || 0) * (product.quantity || 1);
            }
          }
        }
      }
    }

    return totalPrice;
  }

  // 生成套餐唯一标识
  static generatePackageUniqueId(packageData: any): string {
    const selectedItems = [];

    if (packageData.optionalGroups) {
      for (const group of packageData.optionalGroups) {
        if (group.products) {
          for (const product of group.products) {
            if (product.isSelected) {
              selectedItems.push(`${product.id}:${product.quantity || 1}`);
            }
          }
        }
      }
    }

    return `${packageData.id}_${selectedItems.sort().join('_')}_${Date.now()}`;
  }

  // 自动选择默认选项
  static applyDefaultSelections(packageData: any): void {
    if (!packageData.optionalGroups) return;

    for (const group of packageData.optionalGroups) {
      if (group.autoSelect && group.products) {
        const unselectedProducts = group.products.filter(p => !p.isSelected);
        const needToSelect = Math.max(0, group.minSelect - group.selectedCount);

        // 自动选择前N个未选择的商品
        unselectedProducts.slice(0, needToSelect).forEach(product => {
          product.isSelected = true;
          product.quantity = 1;
        });

        // 更新选择计数
        group.selectedCount = group.products.filter(p => p.isSelected).length;
      }
    }
  }
}
```

## 错误处理机制

### 1. 网络错误处理

```typescript
// 网络错误处理器
export class NetworkErrorHandler {
  private static retryCount = 0;
  private static maxRetries = 3;
  private static retryDelay = 1000;

  static async handleApiCall<T>(apiCall: () => Promise<T>, errorMessage: string = '操作失败'): Promise<T> {
    try {
      const result = await apiCall();
      this.retryCount = 0; // 重置重试计数
      return result;
    } catch (error) {
      return this.handleError(error, apiCall, errorMessage);
    }
  }

  private static async handleError<T>(error: any, apiCall: () => Promise<T>, errorMessage: string): Promise<T> {
    console.error('API调用失败:', error);

    // 网络错误重试
    if (this.isNetworkError(error) && this.retryCount < this.maxRetries) {
      this.retryCount++;
      console.log(`网络错误，正在重试 (${this.retryCount}/${this.maxRetries})...`);

      await this.delay(this.retryDelay * this.retryCount);
      return this.handleApiCall(apiCall, errorMessage);
    }

    // 显示用户友好的错误信息
    const userMessage = this.getUserFriendlyMessage(error, errorMessage);
    ElMessage.error(userMessage);

    throw error;
  }

  private static isNetworkError(error: any): boolean {
    return error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error') || error.message?.includes('timeout');
  }

  private static getUserFriendlyMessage(error: any, defaultMessage: string): string {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    if (error.code === 'NETWORK_ERROR') {
      return '网络连接失败，请检查网络设置';
    }

    if (error.message?.includes('timeout')) {
      return '请求超时，请稍后重试';
    }

    return defaultMessage;
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 2. 业务错误处理

```typescript
// 业务错误处理器
export class BusinessErrorHandler {
  // 处理商品库存不足
  static handleOutOfStock(product: any): void {
    ElMessage.warning(`商品 "${product.name}" 库存不足`);
    // 可以添加自动推荐替代商品的逻辑
  }

  // 处理套餐配置错误
  static handlePackageConfigError(errors: string[]): void {
    const message = errors.join('\n');
    ElMessage.error({
      message: `套餐配置有误：\n${message}`,
      duration: 5000
    });
  }

  // 处理支付失败
  static handlePaymentError(error: any): void {
    let message = '支付失败';

    if (error.code === 'INSUFFICIENT_BALANCE') {
      message = '余额不足，请充值后重试';
    } else if (error.code === 'PAYMENT_TIMEOUT') {
      message = '支付超时，请重新尝试';
    } else if (error.code === 'PAYMENT_CANCELLED') {
      message = '支付已取消';
    }

    ElMessage.error(message);
  }

  // 处理订单提交失败
  static handleOrderSubmitError(error: any): void {
    console.error('订单提交失败:', error);

    if (error.code === 'DUPLICATE_ORDER') {
      ElMessage.warning('订单已存在，请勿重复提交');
    } else if (error.code === 'INVALID_SESSION') {
      ElMessage.error('会话已过期，请重新登录');
    } else {
      ElMessage.error('订单提交失败，请重试');
    }
  }
}
```

## 性能监控

### 1. 性能指标收集

```typescript
// 性能监控器
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();

  // 记录操作耗时
  static recordTiming(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }

    const timings = this.metrics.get(operation)!;
    timings.push(duration);

    // 保持最近100次记录
    if (timings.length > 100) {
      timings.shift();
    }
  }

  // 获取平均耗时
  static getAverageTiming(operation: string): number {
    const timings = this.metrics.get(operation);
    if (!timings || timings.length === 0) return 0;

    return timings.reduce((sum, time) => sum + time, 0) / timings.length;
  }

  // 监控API调用
  static async monitorApiCall<T>(operation: string, apiCall: () => Promise<T>): Promise<T> {
    const startTime = performance.now();

    try {
      const result = await apiCall();
      const duration = performance.now() - startTime;
      this.recordTiming(operation, duration);

      // 如果耗时过长，记录警告
      if (duration > 3000) {
        console.warn(`API调用 ${operation} 耗时过长: ${duration.toFixed(2)}ms`);
      }

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTiming(`${operation}_error`, duration);
      throw error;
    }
  }

  // 生成性能报告
  static generateReport(): PerformanceReport {
    const report: PerformanceReport = {
      timestamp: new Date().toISOString(),
      operations: {}
    };

    for (const [operation, timings] of this.metrics.entries()) {
      if (timings.length > 0) {
        report.operations[operation] = {
          count: timings.length,
          average: this.getAverageTiming(operation),
          min: Math.min(...timings),
          max: Math.max(...timings)
        };
      }
    }

    return report;
  }
}

interface PerformanceReport {
  timestamp: string;
  operations: {
    [key: string]: {
      count: number;
      average: number;
      min: number;
      max: number;
    };
  };
}
```

点单功能作为客户消费的核心环节，其用户体验直接影响客户满意度。通过精心设计的架构和丰富的功能特性，为用户提供了流畅、便捷的点单体验。
