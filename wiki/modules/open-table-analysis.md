# 开台功能深度分析

## 功能概述

开台功能是KTV收银系统的核心功能之一，负责为客户开启包厢服务，包括计费方案选择、商品配置、最低消费设置等关键业务流程。

## 业务流程

### 1. 开台流程图

```mermaid
graph TD
    A[选择包厢] --> B[选择计费方案]
    B --> C[配置商品]
    C --> D[设置最低消费]
    D --> E[填写客户信息]
    E --> F[确认开台]
    F --> G{立即结算?}
    G -->|是| H[立即支付]
    G -->|否| I[稍后结算]
    H --> J[打印小票]
    I --> J
    J --> K[开台完成]
```

### 2. 核心业务规则

#### 计费方案

- **买断模式**: 固定时长，固定价格
- **买钟模式**: 按小时计费，最大8小时限制
- **核销模式**: 使用预付费或会员卡

#### 商品管理

- **标准商品**: 必选商品，数量固定
- **可选商品**: 客户可选择的商品
- **套餐商品**: 组合商品，包含多个子商品
- **赠品管理**: 满足条件自动赠送

#### 价格计算

- 基础房费计算
- 商品费用累加
- 最低消费差额计算
- 节假日价格调整
- 区域差价处理

## 技术实现

### 1. 文件结构

```
src/modules/room/views/OpenTable/
├── index.vue                    # View层 - UI展示
├── viewmodel.ts                 # ViewModel接口定义
├── presenter.ts                 # Presenter层 - 业务协调
├── converter.ts                 # 数据转换层
├── interactor.ts                # Interactor层 - 业务逻辑
├── billUtils.ts                 # 账单工具函数
└── components/                  # 页面组件
    ├── ProductBill/             # 商品账单组件
    ├── HourlyBilling/           # 买钟计费组件
    ├── BuyoutBilling/           # 买断计费组件
    └── RoomTypeAreaSelector.vue # 房型区域选择器
```

### 2. 核心类和接口

#### IOpenTableViewModel 接口

```typescript
export interface IOpenTableViewModel {
  state: IOpenTableState; // UI状态
  computed: IOpenTableComputed; // 计算属性
  actions: IOpenTableActions; // 操作方法
  initialize(): void; // 初始化方法
}
```

#### 状态管理

```typescript
export interface IOpenTableState {
  isLoading: boolean; // 加载状态
  activeTab: string; // 当前计费方案
  bookingInfo: any; // 预订信息
  roomBill: any; // 房费账单
  marketBill: any; // 商品账单
  addedProducts: ProductEntity[]; // 已添加商品
  form: OpenTableForm; // 表单数据
  stageInfo: any; // 场次信息
  // ... 其他状态
}
```

### 3. 核心业务逻辑

#### 开台处理流程

```typescript
// presenter.ts
async handleOpenTable(): Promise<any> {
  try {
    this._state.isLoading = true;

    // 1. 数据验证
    this.validateCart();

    // 2. 构建开台参数
    const params = OpenTableConverter.buildOpenTableParams(
      this._state.form,
      this._state.roomBill,
      this._state.marketBill,
      this._state.addedProducts
    );

    // 3. 调用业务逻辑
    const result = this._state.billSessionId
      ? await OpenTableInteractor.continueBill(params)
      : await OpenTableInteractor.openTable(params);

    // 4. 处理结果
    if (result.success) {
      this.handleOpenSuccess(result.data);
      return result.data;
    } else {
      this.handleOpenError(result.error);
      return null;
    }
  } finally {
    this._state.isLoading = false;
  }
}
```

#### 数据转换处理

```typescript
// converter.ts
export class OpenTableConverter {
  static buildOpenTableParams(form, roomBill, marketBill, products) {
    return {
      // 基础信息
      roomId: form.roomId,
      booker: form.booker,
      consumptionMode: form.consumptionMode,

      // 计费信息
      billingType: roomBill.type,
      duration: roomBill.duration,
      baseAmount: roomBill.amount,

      // 商品信息
      products: products.map(product => ({
        productId: product.id,
        quantity: product.quantity,
        price: product.price,
        isGift: product.isGift
      }))

      // 其他参数...
    };
  }
}
```

### 4. 商品管理

#### 商品验证逻辑

```typescript
// billUtils.ts
export function validateOptionalGroup(group: any): boolean {
  const { selectedCount, minSelect, maxSelect } = group;

  // 检查最小选择数量
  if (selectedCount < minSelect) {
    return false;
  }

  // 检查最大选择数量
  if (maxSelect > 0 && selectedCount > maxSelect) {
    return false;
  }

  return true;
}

export function validateAllPackageProducts(marketBill: any): boolean {
  if (!marketBill?.packageVOs) return true;

  return marketBill.packageVOs.every(pkg => {
    return pkg.optionalGroups?.every(group => validateOptionalGroup(group)) ?? true;
  });
}
```

#### 自动选择逻辑

```typescript
export function applyAutoSelectionsToMarketBill(marketBill: any): void {
  marketBill?.packageVOs?.forEach(pkg => {
    pkg.optionalGroups?.forEach(group => {
      if (group.autoSelect && group.selectedCount < group.minSelect) {
        const needSelect = group.minSelect - group.selectedCount;
        const availableProducts = group.products.filter(p => !p.isSelected && p.status !== 'sold_out');

        // 自动选择前N个可用商品
        availableProducts.slice(0, needSelect).forEach(product => {
          product.isSelected = true;
          product.quantity = 1;
        });

        group.selectedCount += needSelect;
      }
    });
  });
}
```

### 5. 价格计算

#### 总金额计算

```typescript
// 计算总账单金额
get totalBill(): ComputedRef<number> {
  return computed(() => {
    const roomAmount = this._state.roomBill?.totalAmount || 0;
    const productAmount = this.processedMarketBill.value.inOrderProducts
      .reduce((sum, product) => sum + (product.totalPrice || 0), 0);
    return roomAmount + productAmount;
  });
}

// 最低消费差额计算
get minimumConsumptionDifference(): ComputedRef<number> {
  return computed(() => {
    const minimumCharge = this._state.form.minimumCharge || 0;
    const currentTotal = this.totalBill.value;
    return Math.max(0, minimumCharge - currentTotal);
  });
}
```

### 6. 打印功能

#### 统一打印处理

```typescript
// interactor.ts
static async printOpenTableDocuments(sessionId: string, orderNos?: string[]): Promise<void> {
  try {
    const printingService = usePrintingService();

    // 1. 打印开台小票
    console.log('打印开台小票, 会话ID:', sessionId);
    printingService.printOpenTableReceipt(sessionId);

    // 2. 打印出品单 (如果有商品)
    if (orderNos && orderNos.length > 0) {
      console.log('打印出品单, 订单号:', orderNos);
      printingService.printProductOutBySessionId(sessionId, orderNos);
    }

    console.log('开台相关单据打印完成');
  } catch (error) {
    console.error('打印开台单据时出错:', error);
  }
}
```

## 关键特性

### 1. 响应式数据管理

- 使用Vue 3的响应式系统
- 实时计算价格和验证状态
- 自动更新UI显示

### 2. 数据验证

- 表单数据验证
- 商品选择验证
- 业务规则验证
- 实时错误提示

### 3. 状态持久化

- 表单数据本地存储
- 页面刷新数据恢复
- 异常情况数据保护

### 4. 错误处理

- 网络错误重试机制
- 用户友好错误提示
- 异常状态恢复

### 5. 性能优化

- 计算属性缓存
- 防抖处理
- 懒加载组件

## API接口

### 1. 开台接口

```typescript
// 开台
POST /api/order/open
{
  roomId: string;
  booker: string;
  consumptionMode: string;
  billingType: string;
  duration: number;
  products: ProductItem[];
  // ... 其他参数
}

// 续房
POST /api/order/open-continue
{
  sessionId: string;
  // ... 续房参数
}
```

### 2. 数据查询接口

```typescript
// 获取房间信息
GET / api / room / info / { roomId };

// 获取价格方案
GET / api / price - plan / list;

// 获取商品信息
GET / api / product / list;
```

## 测试策略

### 1. 单元测试

- Presenter层逻辑测试
- Converter转换逻辑测试
- 工具函数测试
- 计算属性测试

### 2. 集成测试

- API接口测试
- 业务流程测试
- 数据流测试

### 3. E2E测试

- 完整开台流程测试
- 异常情况处理测试
- 用户交互测试

## 常见问题

### 1. 商品验证失败

**问题**: 套餐商品选择不满足要求
**解决**: 检查可选组的最小/最大选择数量，启用自动选择功能

### 2. 价格计算错误

**问题**: 总金额计算不正确
**解决**: 检查商品价格、节假日价格、区域差价设置

### 3. 开台失败

**问题**: 开台接口调用失败
**解决**: 检查网络连接、参数验证、服务器状态

### 4. 打印异常

**问题**: 小票或出品单打印失败
**解决**: 检查打印机连接、打印服务状态、模板配置

## 扩展功能

### 1. 预订集成

- 预订信息自动填充
- 预订状态同步
- 预订变更处理

### 2. 会员集成

- 会员信息识别
- 会员价格应用
- 积分计算

### 3. 营销活动

- 优惠券应用
- 促销活动计算
- 满减优惠

### 4. 数据分析

- 开台数据统计
- 商品销售分析
- 客户行为分析

## 数据模型

### 1. 核心实体

#### RoomInfoEntity (房间信息实体)

```typescript
export interface RoomInfoEntity {
  id: string; // 房间ID
  name: string; // 房间名称
  roomTypeId: string; // 房型ID
  areaId: string; // 区域ID
  status: RoomStatus; // 房间状态
  capacity: number; // 容纳人数
  features: string[]; // 房间特色
  equipment: Equipment[]; // 设备信息
}
```

#### ProductEntity (商品实体)

```typescript
export interface ProductEntity {
  id: string; // 商品ID
  name: string; // 商品名称
  price: number; // 商品价格
  category: string; // 商品分类
  status: ProductStatus; // 商品状态
  isPackage: boolean; // 是否套餐
  packageDetail?: PackageDetail; // 套餐详情
  quantity: number; // 数量
  totalPrice: number; // 总价
  isGift: boolean; // 是否赠品
}
```

#### BillingPlan (计费方案实体)

```typescript
export interface BillingPlan {
  id: string; // 方案ID
  type: 'buyout' | 'hourly'; // 计费类型
  name: string; // 方案名称
  basePrice: number; // 基础价格
  duration: number; // 时长(分钟)
  validTimeRanges: TimeRange[]; // 有效时间段
  holidayPrice?: number; // 节假日价格
  areaMultiplier?: number; // 区域倍数
}
```

### 2. 状态管理

#### OpenTableStore

```typescript
export const useOpenTableStore = defineStore('openTable', {
  state: () => ({
    currentRoom: null as RoomInfoEntity | null,
    billingPlans: [] as BillingPlan[],
    selectedProducts: [] as ProductEntity[],
    formData: {
      booker: '',
      roomManager: '',
      queueNumber: '',
      customerSource: '',
      customerTag: '',
      consumptionMode: '',
      consumptionTime: '',
      settleImmediately: true,
      duration: 0,
      minimumCharge: 0
    }
  }),

  getters: {
    totalAmount: state => {
      const roomAmount = state.currentBilling?.amount || 0;
      const productAmount = state.selectedProducts.reduce((sum, product) => sum + product.totalPrice, 0);
      return roomAmount + productAmount;
    },

    isFormValid: state => {
      return state.formData.booker.trim() !== '' && state.formData.consumptionMode !== '';
    }
  },

  actions: {
    async loadRoomInfo(roomId: string) {
      const response = await RoomApi.getRoomInfo(roomId);
      this.currentRoom = response.data;
    },

    async loadBillingPlans(roomTypeId: string, areaId: string) {
      const response = await BillingApi.getPlans(roomTypeId, areaId);
      this.billingPlans = response.data;
    },

    addProduct(product: ProductEntity) {
      const existingIndex = this.selectedProducts.findIndex(p => p.id === product.id);

      if (existingIndex >= 0) {
        this.selectedProducts[existingIndex].quantity += product.quantity;
        this.selectedProducts[existingIndex].totalPrice = this.selectedProducts[existingIndex].quantity * this.selectedProducts[existingIndex].price;
      } else {
        this.selectedProducts.push({
          ...product,
          totalPrice: product.price * product.quantity
        });
      }
    }
  }
});
```

## 组件架构

### 1. 主组件结构

#### OpenTable/index.vue

```vue
<template>
  <div class="open-table-container">
    <!-- 头部信息 -->
    <div class="header-section">
      <RoomInfoDisplay :room="vm.state.stageInfo?.roomVO" />
      <CustomerInfoForm v-model="vm.state.form" />
    </div>

    <!-- 计费方案选择 -->
    <div class="billing-section">
      <el-tabs v-model="vm.state.activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="买断" name="buyout">
          <BuyoutBilling :room-bill="vm.state.roomBill" :stage-info="vm.state.stageInfo" @update="handleBillingUpdate" />
        </el-tab-pane>
        <el-tab-pane label="买钟" name="hourly">
          <HourlyBilling :room-bill="vm.state.roomBill" :stage-info="vm.state.stageInfo" @update="handleBillingUpdate" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 商品账单 -->
    <div class="product-section">
      <ProductBill
        :market-bill="vm.state.marketBill"
        :added-products="vm.state.addedProducts"
        @add-product="vm.actions.handleAddProduct"
        @update-product="handleProductUpdate" />
    </div>

    <!-- 底部操作 -->
    <div class="footer-actions">
      <div class="total-amount">总计: ¥{{ (vm.computed.totalBill.value / 100).toFixed(2) }}</div>
      <el-button type="primary" :loading="vm.state.isLoading" :disabled="!vm.computed.isAllCartItemsValid.value" @click="vm.actions.handleImmediatePay">
        {{ vm.state.form.settleImmediately ? '开台立结' : '开台' }}
      </el-button>
    </div>
  </div>
</template>
```

### 2. 子组件设计

#### BuyoutBilling (买断计费组件)

```vue
<template>
  <div class="buyout-billing">
    <div class="time-selector">
      <el-select v-model="selectedTimeSlot" @change="handleTimeSlotChange">
        <el-option v-for="slot in availableTimeSlots" :key="slot.id" :label="slot.label" :value="slot.id" />
      </el-select>
    </div>

    <div class="price-display">
      <div class="base-price">基础价格: ¥{{ basePrice }}</div>
      <div class="holiday-price" v-if="isHoliday">节假日加价: ¥{{ holidayExtra }}</div>
      <div class="area-price" v-if="areaMultiplier !== 1">区域调价: ×{{ areaMultiplier }}</div>
      <div class="total-price">总计: ¥{{ totalPrice }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

const props = defineProps<{
  roomBill: any;
  stageInfo: any;
}>();

const emit = defineEmits<{
  update: [billing: BillingInfo];
}>();

const selectedTimeSlot = ref('');

const basePrice = computed(() => {
  // 基础价格计算逻辑
  return props.roomBill?.basePrice || 0;
});

const holidayExtra = computed(() => {
  // 节假日加价计算
  return props.roomBill?.holidayExtra || 0;
});

const areaMultiplier = computed(() => {
  // 区域倍数计算
  return props.roomBill?.areaMultiplier || 1;
});

const totalPrice = computed(() => {
  return (basePrice.value + holidayExtra.value) * areaMultiplier.value;
});

watch(totalPrice, newPrice => {
  emit('update', {
    type: 'buyout',
    amount: newPrice,
    duration: selectedTimeSlot.value
  });
});
</script>
```

#### ProductBill (商品账单组件)

```vue
<template>
  <div class="product-bill">
    <div class="bill-header">
      <h3>商品账单</h3>
      <el-button type="primary" @click="handleAddProduct"> 添加商品 </el-button>
    </div>

    <div class="product-list">
      <!-- 套餐商品 -->
      <div v-if="packageProducts.length > 0" class="package-section">
        <h4>套餐商品</h4>
        <div v-for="pkg in packageProducts" :key="pkg.id" class="package-item">
          <PackageDetailItem :package="pkg" @update="handlePackageUpdate" />
        </div>
      </div>

      <!-- 标准商品 -->
      <div v-if="standardProducts.length > 0" class="standard-section">
        <h4>标准商品</h4>
        <div v-for="product in standardProducts" :key="product.id" class="product-item">
          <ProductItem :product="product" @update-quantity="handleQuantityUpdate" />
        </div>
      </div>

      <!-- 已添加商品 -->
      <div v-if="addedProducts.length > 0" class="added-section">
        <h4>已添加商品</h4>
        <div v-for="product in addedProducts" :key="product.id" class="added-item">
          <AddedProductItem :product="product" @remove="handleRemoveProduct" />
        </div>
      </div>
    </div>

    <div class="bill-summary">
      <div class="summary-line">
        <span>商品小计:</span>
        <span>¥{{ productSubtotal }}</span>
      </div>
      <div class="summary-line" v-if="giftAmount > 0">
        <span>赠品价值:</span>
        <span>¥{{ giftAmount }}</span>
      </div>
    </div>
  </div>
</template>
```

## 业务规则引擎

### 1. 价格计算引擎

```typescript
export class PriceCalculationEngine {
  static calculateRoomPrice(basePlan: BillingPlan, room: RoomInfoEntity, timeSlot: TimeSlot, isHoliday: boolean): number {
    let price = basePlan.basePrice;

    // 节假日加价
    if (isHoliday && basePlan.holidayPrice) {
      price = basePlan.holidayPrice;
    }

    // 区域调价
    if (room.areaMultiplier) {
      price *= room.areaMultiplier;
    }

    // 时段调价
    if (timeSlot.priceMultiplier) {
      price *= timeSlot.priceMultiplier;
    }

    return Math.round(price);
  }

  static calculateProductTotal(products: ProductEntity[]): number {
    return products.reduce((total, product) => {
      if (product.isGift) return total;
      return total + product.price * product.quantity;
    }, 0);
  }

  static calculateMinimumConsumptionDifference(totalAmount: number, minimumCharge: number): number {
    return Math.max(0, minimumCharge - totalAmount);
  }
}
```

### 2. 验证规则引擎

```typescript
export class ValidationEngine {
  static validateOpenTableForm(form: OpenTableForm): ValidationResult {
    const errors: string[] = [];

    if (!form.booker.trim()) {
      errors.push('请填写开台人');
    }

    if (!form.consumptionMode) {
      errors.push('请选择消费方式');
    }

    if (form.duration <= 0) {
      errors.push('请选择有效的消费时长');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateProductSelection(marketBill: any): ValidationResult {
    const errors: string[] = [];

    // 验证套餐商品
    if (marketBill?.packageVOs) {
      for (const pkg of marketBill.packageVOs) {
        if (pkg.optionalGroups) {
          for (const group of pkg.optionalGroups) {
            if (group.selectedCount < group.minSelect) {
              errors.push(`${pkg.name} 的 ${group.name} 至少需要选择 ${group.minSelect} 项`);
            }
            if (group.maxSelect > 0 && group.selectedCount > group.maxSelect) {
              errors.push(`${pkg.name} 的 ${group.name} 最多只能选择 ${group.maxSelect} 项`);
            }
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

## 状态机设计

### 1. 开台状态机

```typescript
export enum OpenTableState {
  INITIALIZING = 'initializing',
  ROOM_SELECTED = 'room_selected',
  BILLING_CONFIGURED = 'billing_configured',
  PRODUCTS_SELECTED = 'products_selected',
  FORM_COMPLETED = 'form_completed',
  SUBMITTING = 'submitting',
  SUCCESS = 'success',
  ERROR = 'error'
}

export class OpenTableStateMachine {
  private currentState: OpenTableState = OpenTableState.INITIALIZING;
  private context: OpenTableContext;

  constructor(context: OpenTableContext) {
    this.context = context;
  }

  transition(action: string, payload?: any): boolean {
    const transitions = this.getValidTransitions();

    if (transitions.includes(action)) {
      this.executeTransition(action, payload);
      return true;
    }

    return false;
  }

  private getValidTransitions(): string[] {
    switch (this.currentState) {
      case OpenTableState.INITIALIZING:
        return ['SELECT_ROOM'];
      case OpenTableState.ROOM_SELECTED:
        return ['CONFIGURE_BILLING'];
      case OpenTableState.BILLING_CONFIGURED:
        return ['SELECT_PRODUCTS', 'COMPLETE_FORM'];
      case OpenTableState.PRODUCTS_SELECTED:
        return ['COMPLETE_FORM'];
      case OpenTableState.FORM_COMPLETED:
        return ['SUBMIT'];
      case OpenTableState.SUBMITTING:
        return ['SUCCESS', 'ERROR'];
      default:
        return [];
    }
  }

  private executeTransition(action: string, payload?: any): void {
    switch (action) {
      case 'SELECT_ROOM':
        this.currentState = OpenTableState.ROOM_SELECTED;
        this.context.selectedRoom = payload;
        break;
      case 'CONFIGURE_BILLING':
        this.currentState = OpenTableState.BILLING_CONFIGURED;
        this.context.billingConfig = payload;
        break;
      case 'SELECT_PRODUCTS':
        this.currentState = OpenTableState.PRODUCTS_SELECTED;
        this.context.selectedProducts = payload;
        break;
      case 'COMPLETE_FORM':
        this.currentState = OpenTableState.FORM_COMPLETED;
        this.context.formData = payload;
        break;
      case 'SUBMIT':
        this.currentState = OpenTableState.SUBMITTING;
        break;
      case 'SUCCESS':
        this.currentState = OpenTableState.SUCCESS;
        this.context.result = payload;
        break;
      case 'ERROR':
        this.currentState = OpenTableState.ERROR;
        this.context.error = payload;
        break;
    }
  }
}
```

开台功能作为系统的核心模块，其设计和实现直接影响到整个系统的稳定性和用户体验。通过VIPER-VC架构的合理应用，确保了功能的可维护性和可扩展性。
