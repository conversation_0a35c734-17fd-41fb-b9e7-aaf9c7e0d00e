# 开台功能深度分析

## 功能概述

开台功能是KTV收银系统的核心功能之一，负责为客户开启包厢服务，包括计费方案选择、商品配置、最低消费设置等关键业务流程。

## 业务流程

### 1. 开台流程图

```mermaid
graph TD
    A[选择包厢] --> B[选择计费方案]
    B --> C[配置商品]
    C --> D[设置最低消费]
    D --> E[填写客户信息]
    E --> F[确认开台]
    F --> G{立即结算?}
    G -->|是| H[立即支付]
    G -->|否| I[稍后结算]
    H --> J[打印小票]
    I --> J
    J --> K[开台完成]
```

### 2. 核心业务规则

#### 计费方案
- **买断模式**: 固定时长，固定价格
- **买钟模式**: 按小时计费，最大8小时限制
- **核销模式**: 使用预付费或会员卡

#### 商品管理
- **标准商品**: 必选商品，数量固定
- **可选商品**: 客户可选择的商品
- **套餐商品**: 组合商品，包含多个子商品
- **赠品管理**: 满足条件自动赠送

#### 价格计算
- 基础房费计算
- 商品费用累加
- 最低消费差额计算
- 节假日价格调整
- 区域差价处理

## 技术实现

### 1. 文件结构

```
src/modules/room/views/OpenTable/
├── index.vue                    # View层 - UI展示
├── viewmodel.ts                 # ViewModel接口定义
├── presenter.ts                 # Presenter层 - 业务协调
├── converter.ts                 # 数据转换层
├── interactor.ts                # Interactor层 - 业务逻辑
├── billUtils.ts                 # 账单工具函数
└── components/                  # 页面组件
    ├── ProductBill/             # 商品账单组件
    ├── HourlyBilling/           # 买钟计费组件
    ├── BuyoutBilling/           # 买断计费组件
    └── RoomTypeAreaSelector.vue # 房型区域选择器
```

### 2. 核心类和接口

#### IOpenTableViewModel 接口
```typescript
export interface IOpenTableViewModel {
  state: IOpenTableState;           // UI状态
  computed: IOpenTableComputed;     // 计算属性
  actions: IOpenTableActions;       // 操作方法
  initialize(): void;               // 初始化方法
}
```

#### 状态管理
```typescript
export interface IOpenTableState {
  isLoading: boolean;               // 加载状态
  activeTab: string;                // 当前计费方案
  bookingInfo: any;                 // 预订信息
  roomBill: any;                    // 房费账单
  marketBill: any;                  // 商品账单
  addedProducts: ProductEntity[];   // 已添加商品
  form: OpenTableForm;              // 表单数据
  stageInfo: any;                   // 场次信息
  // ... 其他状态
}
```

### 3. 核心业务逻辑

#### 开台处理流程
```typescript
// presenter.ts
async handleOpenTable(): Promise<any> {
  try {
    this._state.isLoading = true;
    
    // 1. 数据验证
    this.validateCart();
    
    // 2. 构建开台参数
    const params = OpenTableConverter.buildOpenTableParams(
      this._state.form,
      this._state.roomBill,
      this._state.marketBill,
      this._state.addedProducts
    );
    
    // 3. 调用业务逻辑
    const result = this._state.billSessionId 
      ? await OpenTableInteractor.continueBill(params)
      : await OpenTableInteractor.openTable(params);
    
    // 4. 处理结果
    if (result.success) {
      this.handleOpenSuccess(result.data);
      return result.data;
    } else {
      this.handleOpenError(result.error);
      return null;
    }
  } finally {
    this._state.isLoading = false;
  }
}
```

#### 数据转换处理
```typescript
// converter.ts
export class OpenTableConverter {
  static buildOpenTableParams(form, roomBill, marketBill, products) {
    return {
      // 基础信息
      roomId: form.roomId,
      booker: form.booker,
      consumptionMode: form.consumptionMode,
      
      // 计费信息
      billingType: roomBill.type,
      duration: roomBill.duration,
      baseAmount: roomBill.amount,
      
      // 商品信息
      products: products.map(product => ({
        productId: product.id,
        quantity: product.quantity,
        price: product.price,
        isGift: product.isGift
      })),
      
      // 其他参数...
    };
  }
}
```

### 4. 商品管理

#### 商品验证逻辑
```typescript
// billUtils.ts
export function validateOptionalGroup(group: any): boolean {
  const { selectedCount, minSelect, maxSelect } = group;
  
  // 检查最小选择数量
  if (selectedCount < minSelect) {
    return false;
  }
  
  // 检查最大选择数量
  if (maxSelect > 0 && selectedCount > maxSelect) {
    return false;
  }
  
  return true;
}

export function validateAllPackageProducts(marketBill: any): boolean {
  if (!marketBill?.packageVOs) return true;
  
  return marketBill.packageVOs.every(pkg => {
    return pkg.optionalGroups?.every(group => 
      validateOptionalGroup(group)
    ) ?? true;
  });
}
```

#### 自动选择逻辑
```typescript
export function applyAutoSelectionsToMarketBill(marketBill: any): void {
  marketBill?.packageVOs?.forEach(pkg => {
    pkg.optionalGroups?.forEach(group => {
      if (group.autoSelect && group.selectedCount < group.minSelect) {
        const needSelect = group.minSelect - group.selectedCount;
        const availableProducts = group.products.filter(p => 
          !p.isSelected && p.status !== 'sold_out'
        );
        
        // 自动选择前N个可用商品
        availableProducts.slice(0, needSelect).forEach(product => {
          product.isSelected = true;
          product.quantity = 1;
        });
        
        group.selectedCount += needSelect;
      }
    });
  });
}
```

### 5. 价格计算

#### 总金额计算
```typescript
// 计算总账单金额
get totalBill(): ComputedRef<number> {
  return computed(() => {
    const roomAmount = this._state.roomBill?.totalAmount || 0;
    const productAmount = this.processedMarketBill.value.inOrderProducts
      .reduce((sum, product) => sum + (product.totalPrice || 0), 0);
    return roomAmount + productAmount;
  });
}

// 最低消费差额计算
get minimumConsumptionDifference(): ComputedRef<number> {
  return computed(() => {
    const minimumCharge = this._state.form.minimumCharge || 0;
    const currentTotal = this.totalBill.value;
    return Math.max(0, minimumCharge - currentTotal);
  });
}
```

### 6. 打印功能

#### 统一打印处理
```typescript
// interactor.ts
static async printOpenTableDocuments(sessionId: string, orderNos?: string[]): Promise<void> {
  try {
    const printingService = usePrintingService();
    
    // 1. 打印开台小票
    console.log('打印开台小票, 会话ID:', sessionId);
    printingService.printOpenTableReceipt(sessionId);
    
    // 2. 打印出品单 (如果有商品)
    if (orderNos && orderNos.length > 0) {
      console.log('打印出品单, 订单号:', orderNos);
      printingService.printProductOutBySessionId(sessionId, orderNos);
    }
    
    console.log('开台相关单据打印完成');
  } catch (error) {
    console.error('打印开台单据时出错:', error);
  }
}
```

## 关键特性

### 1. 响应式数据管理
- 使用Vue 3的响应式系统
- 实时计算价格和验证状态
- 自动更新UI显示

### 2. 数据验证
- 表单数据验证
- 商品选择验证
- 业务规则验证
- 实时错误提示

### 3. 状态持久化
- 表单数据本地存储
- 页面刷新数据恢复
- 异常情况数据保护

### 4. 错误处理
- 网络错误重试机制
- 用户友好错误提示
- 异常状态恢复

### 5. 性能优化
- 计算属性缓存
- 防抖处理
- 懒加载组件

## API接口

### 1. 开台接口
```typescript
// 开台
POST /api/order/open
{
  roomId: string;
  booker: string;
  consumptionMode: string;
  billingType: string;
  duration: number;
  products: ProductItem[];
  // ... 其他参数
}

// 续房
POST /api/order/open-continue
{
  sessionId: string;
  // ... 续房参数
}
```

### 2. 数据查询接口
```typescript
// 获取房间信息
GET /api/room/info/{roomId}

// 获取价格方案
GET /api/price-plan/list

// 获取商品信息
GET /api/product/list
```

## 测试策略

### 1. 单元测试
- Presenter层逻辑测试
- Converter转换逻辑测试
- 工具函数测试
- 计算属性测试

### 2. 集成测试
- API接口测试
- 业务流程测试
- 数据流测试

### 3. E2E测试
- 完整开台流程测试
- 异常情况处理测试
- 用户交互测试

## 常见问题

### 1. 商品验证失败
**问题**: 套餐商品选择不满足要求
**解决**: 检查可选组的最小/最大选择数量，启用自动选择功能

### 2. 价格计算错误
**问题**: 总金额计算不正确
**解决**: 检查商品价格、节假日价格、区域差价设置

### 3. 开台失败
**问题**: 开台接口调用失败
**解决**: 检查网络连接、参数验证、服务器状态

### 4. 打印异常
**问题**: 小票或出品单打印失败
**解决**: 检查打印机连接、打印服务状态、模板配置

## 扩展功能

### 1. 预订集成
- 预订信息自动填充
- 预订状态同步
- 预订变更处理

### 2. 会员集成
- 会员信息识别
- 会员价格应用
- 积分计算

### 3. 营销活动
- 优惠券应用
- 促销活动计算
- 满减优惠

### 4. 数据分析
- 开台数据统计
- 商品销售分析
- 客户行为分析

开台功能作为系统的核心模块，其设计和实现直接影响到整个系统的稳定性和用户体验。通过VIPER-VC架构的合理应用，确保了功能的可维护性和可扩展性。
