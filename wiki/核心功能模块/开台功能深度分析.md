# 开台功能深度分析

## 功能概述

开台功能是KTV收银系统的核心功能之一，负责为客户开启包厢服务，包括计费方案选择、商品配置、最低消费设置等关键业务流程。

## 业务流程

### 1. 开台流程图

```mermaid
graph TD
    A[选择包厢] --> B[选择计费方案]
    B --> C[配置商品]
    C --> D[设置最低消费]
    D --> E[填写客户信息]
    E --> F[确认开台]
    F --> G{立即结算?}
    G -->|是| H[立即支付]
    G -->|否| I[稍后结算]
    H --> J[打印小票]
    I --> J
    J --> K[开台完成]
```

### 2. 核心业务规则

#### 计费方案

- **买断模式**: 固定时长，固定价格
- **买钟模式**: 按小时计费，最大8小时限制
- **核销模式**: 使用预付费或会员卡

#### 商品管理

- **标准商品**: 必选商品，数量固定
- **可选商品**: 客户可选择的商品
- **套餐商品**: 组合商品，包含多个子商品
- **赠品管理**: 满足条件自动赠送

#### 价格计算

- 基础房费计算
- 商品费用累加
- 最低消费差额计算
- 节假日价格调整
- 区域差价处理

## 技术实现

### 1. 文件结构

```
src/modules/room/views/OpenTable/
├── index.vue                    # View层 - UI展示
├── viewmodel.ts                 # ViewModel接口定义
├── presenter.ts                 # Presenter层 - 业务协调
├── converter.ts                 # 数据转换层
├── interactor.ts                # Interactor层 - 业务逻辑
├── billUtils.ts                 # 账单工具函数
└── components/                  # 页面组件
    ├── ProductBill/             # 商品账单组件
    ├── HourlyBilling/           # 买钟计费组件
    │   ├── presenter.ts         # 买钟计费业务逻辑
    │   ├── interactor.ts        # 买钟计费交互层
    │   ├── converter.ts         # 买钟数据转换
    │   ├── PriceModel.ts        # 价格计算模型
    │   ├── PriceCalculator.ts   # 价格计算器
    │   ├── PricingRules.ts      # 价格规则引擎
    │   └── TimeModel.ts         # 时间处理模型
    ├── BuyoutBilling/           # 买断计费组件
    │   ├── presenter.ts         # 买断计费业务逻辑
    │   ├── interactor.ts        # 买断计费交互层
    │   └── converter.ts         # 买断数据转换
    └── RoomTypeAreaSelector.vue # 房型区域选择器
```

### 2. 核心类和接口

#### IOpenTableViewModel 接口

```typescript
export interface IOpenTableViewModel {
  state: IOpenTableState; // UI状态
  computed: IOpenTableComputed; // 计算属性
  actions: IOpenTableActions; // 操作方法
  initialize(): void; // 初始化方法
}
```

#### 状态管理

```typescript
export interface IOpenTableState {
  isLoading: boolean; // 加载状态
  activeTab: string; // 当前计费方案
  bookingInfo: any; // 预订信息
  roomBill: any; // 房费账单
  marketBill: any; // 商品账单
  addedProducts: ProductEntity[]; // 已添加商品
  form: OpenTableForm; // 表单数据
  stageInfo: any; // 场次信息
  // ... 其他状态
}
```

### 3. 买断方案核心逻辑

买断方案是KTV最常用的计费模式，客户选择固定时长的包厢使用权，价格根据时段、区域、节假日等因素动态计算。

#### 买断方案筛选和选择逻辑

```typescript
// BuyoutBilling/presenter.ts - 买断方案筛选
buyoutOptions: computed((): BuyoutOption[] => {
  const filteredOptions = this._pricePlans.value
    .filter(plan => {
      // 1. 方案必须启用
      const enabled = plan.isEnabled !== false;

      // 2. 检查日期或星期是否在范围内
      const dateRangeValid = BuyoutBillingConverter.isWithinDateRange(plan, this.currentTime);

      // 3. 检查房间类型是否匹配
      const roomTypeValid = this.isRoomTypeMatched(plan);

      // 4. 检查区域是否匹配
      const areaValid = this.isAreaMatched(plan);

      return enabled && dateRangeValid && roomTypeValid && areaValid;
    })
    .map(plan => {
      // 计算基础房费
      const { baseRoomFee } = this.interactor.calculateRoomPrice(plan, this.areaId, this.holidayVO);

      // 检查方案是否在可用时间段内
      const isTimeAvailable = BuyoutBillingConverter.isWithinAvailableTime(plan, this.currentTime);

      // 检查方案是否可点击（考虑提前禁用时间）
      const isClickable = BuyoutBillingConverter.isPlanClickable(plan, this.currentTime);

      // 方案是否禁用
      const isDisabled = !isTimeAvailable || !isClickable;

      const displayInfo = BuyoutBillingConverter.formatDisplayLabel(plan, baseRoomFee);

      return {
        label: displayInfo.fullLabel,
        name: displayInfo.name,
        priceDisplay: displayInfo.priceDisplay,
        timeRange: BuyoutBillingConverter.formatTimeRange(plan),
        selected: plan.id === this.state.selectedPlan?.id,
        plan: plan,
        disabled: isDisabled,
        disabledReason: isDisabled ? `可用时间 ${plan.timeConfig?.availableTimeStart || '00:00'}-${plan.timeConfig?.availableTimeEnd || '23:59'}` : ''
      };
    })
    .sort((a, b) => {
      // 可点击的排在前面，按价格排序
      if (!a.disabled && b.disabled) return -1;
      if (a.disabled && !b.disabled) return 1;

      const getBasePrice = (plan: BuyoutPricePlanVO) => {
        const baseConfig = plan.priceConfigList?.find(c => c.type === 'base');
        return baseConfig ? baseConfig.price : 0;
      };
      return getBasePrice(b.plan) - getBasePrice(a.plan);
    });

  return filteredOptions;
});
```

#### 买断方案价格计算引擎

```typescript
// BuyoutBilling/interactor.ts - 价格计算核心逻辑
calculateRoomPrice(selectedPlan: BuyoutPricePlanVO, areaId: string, holidayVO?: any): {
  baseRoomFee: number;
  minimumCharge: number
} {
  if (!selectedPlan || !selectedPlan.priceConfigList || selectedPlan.priceConfigList.length === 0) {
    return { baseRoomFee: 0, minimumCharge: 0 };
  }

  // 获取基础价格配置
  const baseConfig = selectedPlan.priceConfigList.find(config => config.type === 'base');
  if (!baseConfig) {
    return { baseRoomFee: 0, minimumCharge: 0 };
  }

  let baseRoomFee = baseConfig.price;
  let minimumCharge = selectedPlan.hasMinimumCharge ? selectedPlan.minimumCharge || 0 : 0;

  // 判断是否是节假日
  const isHoliday = holidayVO?.id && baseConfig.holidayPrice && baseConfig.holidayPrice.length > 0;

  // 价格计算优先级：节假日+区域 > 节假日 > 区域 > 基础价格
  if (isHoliday && areaId) {
    // 1. 节假日 + 区域价格（最高优先级）
    const holidayPrice = baseConfig.holidayPrice.find(hp => hp.holidayId === holidayVO.id);

    if (holidayPrice) {
      // 查找匹配的区域价格
      const areaPrice = holidayPrice.areaPrice?.find(ap => ap.areaId === areaId);

      if (areaPrice) {
        baseRoomFee = areaPrice.price;
      } else {
        // 没有特定区域价格，使用假日基础价格
        baseRoomFee = holidayPrice.price;
      }
    }
  }
  else if (isHoliday) {
    // 2. 节假日价格
    const holidayPrice = baseConfig.holidayPrice.find(hp => hp.holidayId === holidayVO.id);
    if (holidayPrice) {
      baseRoomFee = holidayPrice.price;
    }
  }
  else if (areaId) {
    // 3. 区域价格
    const areaPrice = baseConfig.areaPrice?.find(ap => ap.areaId === areaId);
    if (areaPrice) {
      baseRoomFee = areaPrice.price;
    }
  }
  // 4. 基础价格（默认已设置）

  return { baseRoomFee, minimumCharge };
}
```

#### 房间类型和区域匹配规则

```typescript
// 检查房间类型是否匹配
private isRoomTypeMatched(plan: BuyoutPricePlanVO): boolean {
  // 如果方案没有指定房间类型，默认匹配所有
  if (!plan.roomTypeConfig || !plan.roomTypeConfig.roomTypes || plan.roomTypeConfig.roomTypes.length === 0) {
    return true;
  }

  // 获取当前房间类型ID（支持多种字段名）
  let roomTypeId = this.roomInfo?.roomTypeId || this.roomInfo?.typeId;
  if (!roomTypeId) return false;

  // 检查当前房间类型是否在方案支持的房间类型列表中
  return plan.roomTypeConfig.roomTypes.some(type => type.id === roomTypeId);
}

// 检查区域是否匹配
private isAreaMatched(plan: BuyoutPricePlanVO): boolean {
  // 如果方案没有指定区域，默认匹配所有
  if (!plan.areaConfig || !plan.areaConfig.areas || plan.areaConfig.areas.length === 0) {
    return true;
  }

  // 检查当前区域是否在方案支持的区域列表中
  return plan.areaConfig.areas.some(area => area.id === this.areaId);
}
```

### 4. 买钟方案核心逻辑

买钟方案提供灵活的计费模式，支持按时长、按金额、按结束时间和计时等多种计费方式。

#### 买钟方案价格选项生成

```typescript
// HourlyBilling/interactor.ts - 价格选项生成
getValidPriceOptions(
  pricePlans: TimePricePlanVO[],
  areaId: string,
  holidayVO: any,
  roomTypeId: string,
  startTime: string,
  selectedDuration: number,
  businessHours: any,
  currentTime: number
): PriceOption[] {
  // 1. 筛选符合条件的价格方案
  const filteredPlans = pricePlans.filter(plan => {
    return plan.isEnabled &&
           PricingRules.isRoomTypeInPlan(plan, roomTypeId) &&
           PricingRules.isAreaInPlan(plan, areaId);
  });

  // 2. 选择目标价格方案（优先级排序）
  let targetPlan: TimePricePlanVO | undefined;

  // 优先级1：同时匹配房间类型、区域和当前时间的方案
  targetPlan = filteredPlans.find(
    plan => PricingRules.isRoomTypeInPlan(plan, roomTypeId) &&
            PricingRules.isAreaInPlan(plan, areaId) &&
            isPlanTimeContainingCurrentTime(plan)
  );

  // 优先级2：匹配房间类型和区域的方案
  if (!targetPlan) {
    targetPlan = filteredPlans.find(plan =>
      PricingRules.isRoomTypeInPlan(plan, roomTypeId) &&
      PricingRules.isAreaInPlan(plan, areaId)
    );
  }

  // 优先级3：匹配房间类型的方案
  if (!targetPlan) {
    targetPlan = filteredPlans.find(plan =>
      PricingRules.isRoomTypeInPlan(plan, roomTypeId)
    );
  }

  // 优先级4：使用第一个有效方案
  if (!targetPlan) {
    targetPlan = filteredPlans[0];
  }

  if (!targetPlan || !targetPlan.priceConfigList) {
    return [];
  }

  // 3. 转换为价格选项格式
  const priceOptions: PriceOption[] = [];

  // 获取方案时间范围
  const timeDetails = PricingRules.getPlanTimeDetails('00:00', currentTime, targetPlan);
  if (!timeDetails) {
    return [];
  }
  const { timeStart, timeEnd } = timeDetails;

  // 4. 处理各种价格类型
  // 基础价格
  const baseConfig = targetPlan.priceConfigList.find(item => item.type === 'base');
  if (baseConfig) {
    const finalBasePrice = PricingRules.getFinalPrice(baseConfig, holidayVO, areaId);

    priceOptions.push({
      id: 'base',
      label: '基础房费',
      value: 'baseRoomFee',
      prices: {
        baseRoomFee: finalBasePrice,
        birthdayFee: 0,
        activityFee: 0,
        groupBuyFee: 0
      },
      timeRanges: [{ start: timeStart, end: timeEnd }]
    });
  }

  // 生日价格
  const birthdayConfig = targetPlan.priceConfigList.find(item => item.type === 'birthday');
  if (birthdayConfig) {
    const finalBirthdayPrice = PricingRules.getFinalPrice(birthdayConfig, holidayVO, areaId);

    priceOptions.push({
      id: 'birthday',
      label: '生日价格',
      value: 'birthdayFee',
      prices: {
        baseRoomFee: 0,
        birthdayFee: finalBirthdayPrice,
        activityFee: 0,
        groupBuyFee: 0
      },
      timeRanges: [{ start: timeStart, end: timeEnd }]
    });
  }

  // 活动价格
  const activityConfig = targetPlan.priceConfigList.find(item => item.type === 'activity');
  if (activityConfig) {
    const finalActivityPrice = PricingRules.getFinalPrice(activityConfig, holidayVO, areaId);

    priceOptions.push({
      id: 'activity',
      label: '活动价格',
      value: 'activityFee',
      prices: {
        baseRoomFee: 0,
        birthdayFee: 0,
        activityFee: finalActivityPrice,
        groupBuyFee: 0
      },
      timeRanges: [{ start: timeStart, end: timeEnd }]
    });
  }

  // 团购价格
  const groupConfig = targetPlan.priceConfigList.find(item => item.type === 'group');
  if (groupConfig) {
    const finalGroupPrice = PricingRules.getFinalPrice(groupConfig, holidayVO, areaId);

    priceOptions.push({
      id: 'group',
      label: '团购价格',
      value: 'groupBuyFee',
      prices: {
        baseRoomFee: 0,
        birthdayFee: 0,
        activityFee: 0,
        groupBuyFee: finalGroupPrice
      },
      timeRanges: [{ start: timeStart, end: timeEnd }]
    });
  }

  return priceOptions;
}
```

#### 价格规则引擎核心逻辑

```typescript
// PricingRules.ts - 价格规则处理
export class PricingRules {
  /**
   * 根据价格配置和区域/节假日信息获取最终价格
   */
  static getFinalPrice(priceConfigItem: any, holidayVO: any, areaId: string): number {
    if (!priceConfigItem) return 0;

    let finalPrice = priceConfigItem.price || 0;

    // 1. 处理节假日价格（优先级最高）
    if (holidayVO && priceConfigItem.holidayPrice?.length) {
      const holidayPrice = this.getHolidayPrice(priceConfigItem, holidayVO, areaId);
      if (holidayPrice > 0) {
        finalPrice = holidayPrice;
      }
    }

    // 2. 处理区域价格（如果没有节假日价格）
    if (priceConfigItem.areaPrice?.length) {
      const areaPrice = this.getAreaPrice(priceConfigItem, areaId);
      if (areaPrice > 0) {
        finalPrice = areaPrice;
      }
    }

    // 从分转为元
    return finalPrice / 100;
  }

  /**
   * 获取节假日价格
   */
  static getHolidayPrice(priceConfigItem: any, holidayVO: any, areaId: string): number {
    if (!holidayVO || !priceConfigItem) return 0;

    // 在节假日价格列表中查找匹配项
    const holidayConfig = priceConfigItem.holidayPrice?.find((hp: any) => hp.holidayId === holidayVO.id);

    if (!holidayConfig) return 0;

    // 优先使用区域的节假日价格
    const areaHolidayPrice = holidayConfig.areaPrice?.find((ap: any) => ap.areaId === areaId);

    return areaHolidayPrice?.price || holidayConfig.price || 0;
  }

  /**
   * 获取区域价格
   */
  static getAreaPrice(priceConfigItem: any, areaId: string): number {
    if (!priceConfigItem) return 0;

    // 在区域价格列表中查找匹配项
    const areaPrice = priceConfigItem.areaPrice?.find((ap: any) => ap.areaId === areaId);

    return areaPrice?.price || priceConfigItem.price || 0;
  }

  /**
   * 检查房间类型是否在计划中
   */
  static isRoomTypeInPlan(plan: TimePricePlanVO, roomTypeId: string): boolean {
    if (!plan.roomTypeConfig || !plan.roomTypeConfig.roomTypes || !roomTypeId) {
      return false;
    }

    return plan.roomTypeConfig.roomTypes.some((rt: any) => rt.id === roomTypeId);
  }

  /**
   * 检查区域是否在计划中
   */
  static isAreaInPlan(plan: TimePricePlanVO, areaId: string): boolean {
    // 如果没有areaId参数，返回false
    if (!areaId) {
      return false;
    }

    // 如果方案没有区域配置，或者没有启用区域限制，则匹配所有区域
    if (!plan.areaConfig || !plan.areaConfig.areas || plan.areaConfig.areas.length === 0 || !plan.isAreaSpecified) {
      return true;
    }

    // 如果方案明确指定了区域列表，则检查具体匹配
    return plan.areaConfig.areas.some((area: any) => area.id === areaId);
  }
}
```

#### 买钟方案价格计算模型

```typescript
// PriceModel.ts - 价格计算核心模型
export function calculatePrice(
  bookingStart: string,
  bookingEnd: string,
  priceOptions: PriceOption[],
  fallbackHourlyPrice: number = 100
): PriceCalculationResult {
  // 转换时间为分钟
  const startMinute = TimeModel.timeToMinutes(bookingStart);
  const endMinute = TimeModel.timeToMinutes(bookingEnd);

  // 使用共享逻辑生成时间段和价格段
  const { priceSegments } = generateTimeAndPriceSegments(startMinute, endMinute, priceOptions, null, fallbackHourlyPrice);

  // 计算总价格
  let totalPrice = 0;
  const details: PriceDetail[] = [];

  priceSegments.forEach(segment => {
    const segmentPrice = segment.price * (segment.duration / 60); // 按小时计费
    totalPrice += segmentPrice;

    details.push({
      timeRange: `${TimeModel.minutesToTime(segment.startMinute)}-${TimeModel.minutesToTime(segment.endMinute)}`,
      duration: segment.duration,
      hourlyRate: segment.price,
      totalPrice: segmentPrice,
      priceType: segment.priceType
    });
  });

  return {
    totalPrice: Math.round(totalPrice * 100) / 100, // 保留两位小数
    details
  };
}

// 生成时间段和价格段
function generateTimeAndPriceSegments(
  startMinute: number,
  endMinute: number,
  priceOptions: PriceOption[],
  selectedPriceType: PriceTypeKey | null,
  fallbackPrice: number
): { priceSegments: PriceSegment[] } {
  const segments: PriceSegment[] = [];

  // 处理跨天情况
  if (endMinute <= startMinute) {
    endMinute += 24 * 60; // 加一天
  }

  let currentMinute = startMinute;

  while (currentMinute < endMinute) {
    // 找到当前时间点适用的价格选项
    const applicableOption = findApplicablePriceOption(currentMinute, priceOptions);

    if (applicableOption) {
      // 计算这个价格选项的结束时间
      const optionEndMinute = Math.min(endMinute, getOptionEndMinute(applicableOption, currentMinute));

      // 获取价格
      const price = selectedPriceType ? applicableOption.prices[selectedPriceType] || 0 : getHighestPrice(applicableOption.prices);

      segments.push({
        startMinute: currentMinute,
        endMinute: optionEndMinute,
        duration: optionEndMinute - currentMinute,
        price: price || fallbackPrice,
        priceType: selectedPriceType || 'baseRoomFee',
        planName: applicableOption.label
      });

      currentMinute = optionEndMinute;
    } else {
      // 没有找到适用的价格选项，使用全时段价格
      segments.push({
        startMinute: currentMinute,
        endMinute: endMinute,
        duration: endMinute - currentMinute,
        price: fallbackPrice,
        priceType: 'baseRoomFee',
        planName: '默认价格'
      });
      break;
    }
  }

  return { priceSegments: segments };
}
```

#### 多种计费模式支持

```typescript
// HourlyBilling/presenter.ts - 计费模式处理
bill: computed(() => {
  if (this.state.billingType === 'amount') {
    // 根据金额反算时长和价格
    const amount = parseFloat(this.state.inputAmount || '0');
    if (amount <= 0) {
      return { totalPrice: 0, details: [] };
    }

    const validPrices = this.getProcessedPrices();

    return this.interactor.calculatePriceDetailsFromAmount(
      amount,
      this.state.startTime,
      this.getStartTimestamp(),
      [],
      this.state.selectedPriceOption,
      this.computed.basePriceForArea.value,
      this.props.baseTimePriceFee || 0,
      this.props.pricePlans || [],
      this.props.areaId,
      this.props.holidayVO,
      this.props.roomTypeVO?.id || '',
      validPrices
    );
  } else if (this.state.billingType === 'endTime') {
    // 根据结束时间计算价格
    if (!this.state.endTime || this.state.selectedDuration <= 0) {
      return { totalPrice: 0, details: [] };
    }

    const endTime = this.interactor.addMinutesToTime(this.state.startTime, this.state.selectedDuration);
    const validPrices = this.getProcessedPrices();

    return PriceCalculator.calculatePriceByDuration(this.state.startTime, endTime, validPrices, this.props.baseTimePriceFee || 0);
  } else if (this.state.billingType === 'timer') {
    // 计时模式 - 实时计算
    const validPrices = this.getProcessedPrices();
    if (validPrices && validPrices.length > 0) {
      return { totalPrice: 0, details: [] };
    }
  } else {
    // 根据时长计算价格（默认模式）
    const duration = this.state.selectedDuration;
    if (duration <= 0) {
      return { totalPrice: 0, details: [] };
    }

    const endTime = this.interactor.addMinutesToTime(this.state.startTime, duration);
    const validPrices = this.getProcessedPrices();

    return PriceCalculator.calculatePriceByDuration(this.state.startTime, endTime, validPrices, this.props.baseTimePriceFee || 0);
  }
});
```

### 5. 开台处理流程

#### 开台业务逻辑协调

```typescript
// presenter.ts - 开台处理流程
async handleOpenTable(): Promise<any> {
  try {
    this._state.isLoading = true;

    // 1. 数据验证
    if (!this.validateCart()) {
      return null;
    }

    // 2. 构建开台参数
    const params = OpenTableConverter.convertToOpenOrderRequest({
      roomVO: this._state.stageInfo!.roomVO,
      form: this._state.form,
      roomBill: this._state.roomBill!,
      inOrderProducts: this.computed.processedMarketBill.value.inOrderProducts,
      outOrderProducts: this.computed.processedMarketBill.value.outOrderProducts,
      billSessionId: this._state.billSessionId,
      billStartTime: this._state.billStartTime!,
      billRoomTypeVO: this._state.billRoomTypeVO,
      billRoomAreaVO: this._state.billRoomAreaVO,
      customMinimum: this._state.customMinimum,
      isOpenEnd: this._state.form.settleImmediately,
      totalBill: this.computed.totalBill.value,
      bookingId: this._state.stageInfo?.bookingId
    });

    // 3. 调用业务逻辑
    const result = this._state.billSessionId
      ? await OpenTableInteractor.continueBill(params as any)
      : await OpenTableInteractor.openTable(params as any);

    // 4. 处理结果
    if (result.success && result.data && result.data.data) {
      this._state.addedProducts = [];
      this.handleOpenSuccess(result.data.data);
      return result.data;
    } else {
      this.handleOpenError(result.error);
      return null;
    }
  } finally {
    this._state.isLoading = false;
  }
}

// 开台成功处理
private handleOpenSuccess(sessionData: SessionVO): void {
  console.log('开台成功:', sessionData);

  // 1. 打印相关单据
  if (sessionData.sessionId) {
    const orderNos = sessionData.orderNos || [];
    OpenTableInteractor.printOpenTableDocuments(
      sessionData.sessionId,
      orderNos,
      this._state.billSessionId ? true : false // 是否为续台操作
    );
  }

  // 2. 显示成功消息
  toast({
    title: this._state.billSessionId ? '续台成功' : '开台成功',
    description: `房间: ${sessionData.roomName || this._state.billRoomName}`,
    duration: 2000
  });

  // 3. 跳转到订单详情页面
  this.router.push({
    path: '/order/detail',
    query: {
      orderId: sessionData.sessionId,
      roomId: sessionData.roomId
    }
  });
}
```

#### 数据转换处理

```typescript
// converter.ts - 开台参数转换
export class OpenTableConverter {
  static convertToOpenOrderRequest(params: {
    roomVO: any;
    form: any;
    roomBill: any;
    inOrderProducts: any[];
    outOrderProducts: any[];
    billSessionId: string;
    billStartTime: number;
    billRoomTypeVO: any;
    billRoomAreaVO: any;
    customMinimum: string | null;
    isOpenEnd: boolean;
    totalBill: number;
    bookingId?: string;
  }): OpenOrderRequest {
    const {
      roomVO,
      form,
      roomBill,
      inOrderProducts,
      outOrderProducts,
      billSessionId,
      billStartTime,
      billRoomTypeVO,
      billRoomAreaVO,
      customMinimum,
      isOpenEnd,
      totalBill,
      bookingId
    } = params;

    // 构建基础订单信息
    const baseRequest = {
      venueId: roomVO.venueId,
      roomId: roomVO.id,
      bookingId: bookingId || undefined,
      startTime: billStartTime,
      endTime: roomBill?.timeRange?.endTime
        ? this.convertTimeToTimestamp(roomBill.timeRange.endTime, billStartTime)
        : billStartTime + (roomBill?.duration || 0) * 60,

      // 价格方案信息
      pricePlanId: roomBill?.details?.[0]?.id || '',
      consumptionMode: form.consumptionMode,
      selectedAreaId: billRoomAreaVO?.id || '',
      selectedRoomTypeId: billRoomTypeVO?.id || '',
      timeChargeMode: roomBill?.isTimeConsume ? 'time' : 'fixed',
      buyMinute: roomBill?.duration || 0,
      timeChargeAmount: roomBill?.totalPrice || 0,
      totalAmount: totalBill,
      isOpenTableSettled: isOpenEnd,
      minimumCharge: customMinimum ? convertToFen(parseFloat(customMinimum)) : 0,

      // 房费方案
      orderRoomPlanVOS: this.convertRoomPlans(roomBill),

      // 商品信息
      inOrderProductInfos: this.convertProducts(inOrderProducts, 'in'),
      outOrderProductInfos: this.convertProducts(outOrderProducts, 'out'),

      // 房间信息
      roomVO: roomVO
    };

    return baseRequest;
  }

  // 转换房费方案
  private static convertRoomPlans(roomBill: any): any[] {
    if (!roomBill || !roomBill.details) return [];

    return roomBill.details.map(detail => ({
      id: detail.id,
      planName: detail.planName,
      startTime: detail.startTime,
      endTime: detail.endTime,
      duration: detail.duration,
      amount: detail.price,
      timeRange: roomBill.timeRange
    }));
  }

  // 转换商品信息
  private static convertProducts(products: any[], type: 'in' | 'out'): any[] {
    if (!products || products.length === 0) return [];

    return products.map(product => ({
      productId: product.id,
      productName: product.productName,
      quantity: product.quantity,
      price: product.price,
      totalPrice: product.totalAmount,
      isGift: product.isFree || false,
      type: type,
      categoryId: product.categoryId,
      categoryName: product.categoryName
    }));
  }

  // 时间转换工具
  private static convertTimeToTimestamp(timeStr: string, baseTimestamp: number): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const baseDate = new Date(baseTimestamp * 1000);
    const targetDate = new Date(baseDate);
    targetDate.setHours(hours, minutes, 0, 0);

    // 如果目标时间早于基础时间，说明是第二天
    if (targetDate.getTime() < baseDate.getTime()) {
      targetDate.setDate(targetDate.getDate() + 1);
    }

    return Math.floor(targetDate.getTime() / 1000);
  }
}
```

## 核心价格计算总结

### 1. 买断方案价格计算流程

1. **方案筛选**: 根据房间类型、区域、时间范围筛选可用方案
2. **价格计算**: 按优先级应用节假日价格、区域价格、基础价格
3. **方案排序**: 可用方案优先，按价格高低排序
4. **用户选择**: 用户选择具体方案，系统计算最终价格

### 2. 买钟方案价格计算流程

1. **价格选项生成**: 根据时段生成基础、生日、活动、团购等价格选项
2. **时段价格计算**: 按时间段应用不同价格，支持跨时段计费
3. **多种计费模式**: 支持按时长、按金额、按结束时间、计时等模式
4. **实时价格更新**: 根据选择的计费模式实时计算价格

### 3. 价格规则引擎

- **优先级规则**: 节假日价格 > 区域价格 > 基础价格
- **匹配规则**: 房间类型匹配 + 区域匹配 + 时间匹配
- **兜底机制**: 当没有匹配的价格方案时，使用系统默认价格
- **精度处理**: 价格计算精确到分，显示时转换为元

### 4. 商品管理

#### 商品验证逻辑

```typescript
// billUtils.ts
export function validateOptionalGroup(group: any): boolean {
  const { selectedCount, minSelect, maxSelect } = group;

  // 检查最小选择数量
  if (selectedCount < minSelect) {
    return false;
  }

  // 检查最大选择数量
  if (maxSelect > 0 && selectedCount > maxSelect) {
    return false;
  }

  return true;
}

export function validateAllPackageProducts(marketBill: any): boolean {
  if (!marketBill?.packageVOs) return true;

  return marketBill.packageVOs.every(pkg => {
    return pkg.optionalGroups?.every(group => validateOptionalGroup(group)) ?? true;
  });
}
```

#### 自动选择逻辑

```typescript
export function applyAutoSelectionsToMarketBill(marketBill: any): void {
  marketBill?.packageVOs?.forEach(pkg => {
    pkg.optionalGroups?.forEach(group => {
      if (group.autoSelect && group.selectedCount < group.minSelect) {
        const needSelect = group.minSelect - group.selectedCount;
        const availableProducts = group.products.filter(p => !p.isSelected && p.status !== 'sold_out');

        // 自动选择前N个可用商品
        availableProducts.slice(0, needSelect).forEach(product => {
          product.isSelected = true;
          product.quantity = 1;
        });

        group.selectedCount += needSelect;
      }
    });
  });
}
```

### 5. 价格计算

#### 总金额计算

```typescript
// 计算总账单金额
get totalBill(): ComputedRef<number> {
  return computed(() => {
    const roomAmount = this._state.roomBill?.totalAmount || 0;
    const productAmount = this.processedMarketBill.value.inOrderProducts
      .reduce((sum, product) => sum + (product.totalPrice || 0), 0);
    return roomAmount + productAmount;
  });
}

// 最低消费差额计算
get minimumConsumptionDifference(): ComputedRef<number> {
  return computed(() => {
    const minimumCharge = this._state.form.minimumCharge || 0;
    const currentTotal = this.totalBill.value;
    return Math.max(0, minimumCharge - currentTotal);
  });
}
```

### 6. 打印功能

#### 统一打印处理

```typescript
// interactor.ts
static async printOpenTableDocuments(sessionId: string, orderNos?: string[]): Promise<void> {
  try {
    const printingService = usePrintingService();

    // 1. 打印开台小票
    console.log('打印开台小票, 会话ID:', sessionId);
    printingService.printOpenTableReceipt(sessionId);

    // 2. 打印出品单 (如果有商品)
    if (orderNos && orderNos.length > 0) {
      console.log('打印出品单, 订单号:', orderNos);
      printingService.printProductOutBySessionId(sessionId, orderNos);
    }

    console.log('开台相关单据打印完成');
  } catch (error) {
    console.error('打印开台单据时出错:', error);
  }
}
```

## 关键特性

### 1. 响应式数据管理

- 使用Vue 3的响应式系统
- 实时计算价格和验证状态
- 自动更新UI显示

### 2. 数据验证

- 表单数据验证
- 商品选择验证
- 业务规则验证
- 实时错误提示

### 3. 状态持久化

- 表单数据本地存储
- 页面刷新数据恢复
- 异常情况数据保护

### 4. 错误处理

- 网络错误重试机制
- 用户友好错误提示
- 异常状态恢复

### 5. 性能优化

- 计算属性缓存
- 防抖处理
- 懒加载组件

## API接口

### 1. 开台接口

```typescript
// 开台
POST /api/order/open
{
  roomId: string;
  booker: string;
  consumptionMode: string;
  billingType: string;
  duration: number;
  products: ProductItem[];
  // ... 其他参数
}

// 续房
POST /api/order/open-continue
{
  sessionId: string;
  // ... 续房参数
}
```

### 2. 数据查询接口

```typescript
// 获取房间信息
GET / api / room / info / { roomId };

// 获取价格方案
GET / api / price - plan / list;

// 获取商品信息
GET / api / product / list;
```

## 测试策略

### 1. 单元测试

- Presenter层逻辑测试
- Converter转换逻辑测试
- 工具函数测试
- 计算属性测试

### 2. 集成测试

- API接口测试
- 业务流程测试
- 数据流测试

### 3. E2E测试

- 完整开台流程测试
- 异常情况处理测试
- 用户交互测试

## 常见问题

### 1. 商品验证失败

**问题**: 套餐商品选择不满足要求
**解决**: 检查可选组的最小/最大选择数量，启用自动选择功能

### 2. 价格计算错误

**问题**: 总金额计算不正确
**解决**: 检查商品价格、节假日价格、区域差价设置

### 3. 开台失败

**问题**: 开台接口调用失败
**解决**: 检查网络连接、参数验证、服务器状态

### 4. 打印异常

**问题**: 小票或出品单打印失败
**解决**: 检查打印机连接、打印服务状态、模板配置

## 扩展功能

### 1. 预订集成

- 预订信息自动填充
- 预订状态同步
- 预订变更处理

### 2. 会员集成

- 会员信息识别
- 会员价格应用
- 积分计算

### 3. 营销活动

- 优惠券应用
- 促销活动计算
- 满减优惠

### 4. 数据分析

- 开台数据统计
- 商品销售分析
- 客户行为分析

开台功能作为系统的核心模块，其设计和实现直接影响到整个系统的稳定性和用户体验。通过VIPER-VC架构的合理应用，确保了功能的可维护性和可扩展性。
