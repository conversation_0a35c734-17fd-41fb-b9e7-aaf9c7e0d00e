# VIPER-VC 架构详解

## 架构概述

VIPER-VC (VIPER with Vue and Convert) 是本项目采用的核心架构模式，它是在经典VIPER架构基础上，结合Vue 3特性和业务需求进行的优化设计。

### 架构名称解释
- **V**iew: 视图层，负责UI展示
- **I**nteractor: 交互层，处理业务逻辑
- **P**resenter: 展示层，协调视图和业务
- **E**ntity: 实体层，数据模型定义
- **R**outer: 路由层，页面导航管理
- **VC**: Vue + Convert，Vue框架集成和数据转换

## 架构层级详解

### 1. View层 (视图层)

#### 职责
- UI结构定义和渲染
- 用户交互事件处理
- 数据绑定和展示
- 组件生命周期管理

#### 组成部分
```typescript
// [pageName].vue
<template>
  <!-- UI结构 -->
</template>

<script setup lang="ts">
// 组件逻辑
import { usePresenter } from './presenter'
const vm = usePresenter()
</script>
```

#### 特点
- 纯展示逻辑，不包含业务规则
- 通过Presenter获取数据和方法
- 响应式数据绑定
- 组件化设计

### 2. IViewModel (视图模型接口)

#### 职责
- 定义UI状态结构
- 声明UI操作接口
- 作为View和Presenter的契约

#### 文件结构
```typescript
// [pageName].viewModel.ts
export interface IPageViewModel {
  state: IPageState;
  computed: IPageComputed;
  actions: IPageActions;
}

export interface IPageState {
  loading: Ref<boolean>;
  data: Ref<DataType>;
  // ... 其他状态
}

export interface IPageActions {
  loadData(): Promise<void>;
  handleSubmit(): void;
  // ... 其他操作
}
```

### 3. Presenter层 (展示层)

#### 职责
- 实现IViewModel接口
- 协调View和Interactor
- 管理UI状态
- 处理用户交互事件
- 调用数据转换器

#### 文件结构
```typescript
// [pageName].presenter.ts
export function usePresenter(): IPageViewModel {
  const interactor = new PageInteractor()
  const converter = new PageConverter()
  
  // 状态管理
  const state = reactive({
    loading: false,
    data: null
  })
  
  // 操作方法
  const actions = {
    async loadData() {
      state.loading = true
      try {
        const rawData = await interactor.fetchData()
        state.data = converter.toViewModel(rawData)
      } finally {
        state.loading = false
      }
    }
  }
  
  return { state, actions }
}
```

#### 核心特性
- 继承IViewModel接口
- 响应式状态管理
- 事件处理协调
- **数据转换核心**
- 路由管理

### 4. ViewModelConverter (数据转换层)

#### 职责
- **核心数据转换功能**
- Entity到ViewModel的转换
- ViewModel到Entity的转换
- 数据格式化和处理

#### 文件结构
```typescript
// [pageName].converter.ts
export class PageConverter {
  // Entity转ViewModel
  toViewModel(entity: EntityType): ViewModelType {
    return {
      displayName: entity.name,
      formattedPrice: formatPrice(entity.price),
      // ... 转换逻辑
    }
  }
  
  // ViewModel转Entity
  toEntity(viewModel: ViewModelType): EntityType {
    return {
      name: viewModel.displayName,
      price: parsePrice(viewModel.formattedPrice),
      // ... 转换逻辑
    }
  }
}
```

### 5. Interactor层 (交互层)

#### 职责
- 业务逻辑实现
- 数据获取和处理
- 业务规则验证
- 与Store和API交互

#### 文件结构
```typescript
// [pageName].interactor.ts
export class PageInteractor {
  private store = usePageStore()
  
  async fetchData(): Promise<EntityType> {
    // 业务逻辑处理
    const result = await this.store.getData()
    return result
  }
  
  async saveData(data: EntityType): Promise<void> {
    // 业务验证和保存
    this.validateData(data)
    await this.store.saveData(data)
  }
}
```

### 6. Router层 (路由层)

#### 职责
- 页面导航管理
- 路由参数处理
- 导航守卫
- 页面切换逻辑

#### 文件结构
```typescript
// router/modules/[module].routes.ts
export const moduleRoutes = [
  {
    path: '/module/page',
    component: () => import('@/modules/module/views/Page/index.vue'),
    meta: { title: '页面标题' }
  }
]
```

### 7. Store层 (状态管理层)

#### 职责
- 全局状态管理
- 数据持久化
- API服务封装
- 缓存策略

#### 文件结构
```typescript
// store/[module]Store.ts
export const useModuleStore = defineStore('module', {
  state: () => ({
    data: null,
    loading: false
  }),
  
  actions: {
    async fetchData() {
      this.loading = true
      try {
        const response = await ModuleApi.getData()
        this.data = response.data
      } finally {
        this.loading = false
      }
    }
  }
})
```

### 8. Entity层 (实体层)

#### 职责
- 数据模型定义
- 类型约束
- 模型关系定义
- 数据验证规则

#### 文件结构
```typescript
// entity/[module].ts
export interface ModuleEntity {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ModuleRelation {
  moduleId: string;
  relatedId: string;
  relationType: string;
}
```

## 数据流向

### 1. 用户操作流
```mermaid
graph TD
    A[用户操作] --> B[View事件]
    B --> C[Presenter处理]
    C --> D[Interactor业务逻辑]
    D --> E[Store数据操作]
    E --> F[API调用]
```

### 2. 数据展示流
```mermaid
graph TD
    A[API响应] --> B[Store存储]
    B --> C[Interactor处理]
    C --> D[Converter转换]
    D --> E[Presenter状态更新]
    E --> F[View响应式更新]
```

### 3. 核心转换流
```mermaid
graph LR
    A[Entity数据] --> B[ViewModelConverter]
    B --> C[ViewModel数据]
    C --> D[View展示]
    
    D --> E[用户修改]
    E --> F[ViewModelConverter]
    F --> G[Entity数据]
    G --> H[业务处理]
```

## 目录结构规范

```
src/modules/[business_module]/
├── views/[page_name]/
│   ├── index.vue                 # View层
│   ├── viewModel.ts             # UI状态和行为定义
│   ├── presenter.ts             # Presenter层
│   ├── converter.ts             # 数据转换层
│   ├── interactor.ts            # 业务交互层
│   └── components/              # 页面专用组件
├── api/
│   ├── [module]Api.ts           # API接口
│   └── index.ts                 # 统一导出
├── entity/
│   ├── [module]Entity.ts        # 实体模型
│   └── index.ts                 # 统一导出
├── store/
│   ├── [module]Store.ts         # 状态管理
│   └── index.ts                 # 统一导出
└── components/                  # 模块共享组件
```

## 架构优势

### 1. 职责分离
- 每层职责明确，降低耦合
- 易于单元测试
- 代码可读性强

### 2. 可维护性
- 模块化设计
- 统一的代码结构
- 清晰的依赖关系

### 3. 可扩展性
- 新功能易于添加
- 现有功能易于修改
- 支持团队协作开发

### 4. 可测试性
- 每层可独立测试
- 依赖注入支持
- Mock友好设计

## 最佳实践

### 1. 命名规范
- 文件命名：`[pageName].[layerName].ts`
- 接口命名：`I[PageName][LayerName]`
- 类命名：`[PageName][LayerName]`

### 2. 依赖管理
- 单向依赖，避免循环引用
- 通过接口定义依赖
- 使用依赖注入

### 3. 错误处理
- 统一的错误处理机制
- 错误边界设计
- 用户友好的错误提示

### 4. 性能优化
- 懒加载和代码分割
- 响应式数据优化
- 内存泄漏防护

这个架构确保了代码的高质量、高可维护性和高可扩展性，为复杂业务系统的开发提供了坚实的基础。
