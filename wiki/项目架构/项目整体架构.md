# Thunder ERP Client 项目整体架构

## 项目概述

Thunder ERP Client 是一个基于 Vue 3 + TypeScript 的现代化企业级收银系统前端应用，专为KTV等娱乐场所设计。项目采用先进的 VIPER-VC 架构模式，确保代码的可维护性、可测试性和可扩展性。

## 技术栈

### 核心技术
- **前端框架**: Vue 3.5+ (Composition API)
- **开发语言**: TypeScript 5.6+
- **构建工具**: Vite 5.4+
- **状态管理**: Pinia 2.2+
- **UI组件库**: Element Plus 2.8+
- **路由管理**: Vue Router 4.x

### 开发工具
- **包管理器**: pnpm (强制使用)
- **代码规范**: ESLint + Prettier
- **组件开发**: Storybook 8.6+
- **单元测试**: Vitest
- **类型检查**: TypeScript

### 浏览器兼容性
- Chrome >= 49
- Safari >= 10
- Firefox >= 52
- Edge >= 14
- iOS >= 10
- Android >= 6

## 项目结构

```
thunder-erp-client/
├── src/                          # 源代码目录
│   ├── apps/                     # 应用入口
│   │   └── client-pad/           # Pad端应用
│   ├── modules/                  # 业务模块
│   │   ├── room/                 # 包厢管理模块
│   │   ├── order/                # 订单管理模块
│   │   ├── production/           # 生产管理模块
│   │   ├── finance/              # 财务管理模块
│   │   ├── member/               # 会员管理模块
│   │   └── ...                   # 其他业务模块
│   ├── components/               # 全局共享组件
│   ├── stores/                   # 全局状态管理
│   ├── router/                   # 路由配置
│   ├── utils/                    # 工具函数
│   ├── types/                    # 类型定义
│   └── api/                      # API接口
├── docs/                         # 项目文档
├── wiki/                         # Wiki文档
├── tests/                        # 测试文件
└── public/                       # 静态资源
```

## 架构特点

### 1. 模块化设计
项目采用模块化设计，每个业务模块独立管理：
- **独立的API接口**: 每个模块有自己的API文件
- **独立的状态管理**: 模块级别的store管理
- **独立的实体定义**: 模块内的数据模型
- **独立的组件库**: 模块特有的组件

### 2. 多端支持
项目支持多种客户端类型：
- **主应用** (client): 收银台主界面，端口5173
- **Pad应用** (pad): 平板点餐界面，端口5174

### 3. 环境隔离
通过环境变量实现不同环境的配置隔离：
- 开发环境 (development)
- 测试环境 (stage)  
- 生产环境 (production)

### 4. 代码分层
严格的代码分层架构：
- **View层**: 视图展示和用户交互
- **Presenter层**: 业务逻辑协调
- **Interactor层**: 业务用例处理
- **Entity层**: 数据模型定义

## 核心功能模块

### 1. 包厢管理 (Room)
- 开台功能
- 包厢状态管理
- 计费方案选择
- 商品管理

### 2. 订单管理 (Order)
- 订单创建和管理
- 支付处理
- 退单处理
- 订单查询

### 3. 生产管理 (Production)
- 点单功能
- 商品选择
- 购物车管理
- 套餐配置

### 4. 财务管理 (Finance)
- 收银结算
- 财务报表
- 交班管理
- 资金流水

## 数据流架构

### 1. 状态管理流
```
用户操作 → View → Presenter → Interactor → Store → API
```

### 2. 数据转换流
```
API数据 → Entity → Converter → ViewModel → View
```

### 3. 事件通信流
```
组件事件 → EventBus → 全局状态更新 → 界面响应
```

## 开发规范

### 1. 命名规范
- **文件命名**: kebab-case (如: open-table.vue)
- **组件命名**: PascalCase (如: OpenTable)
- **变量命名**: camelCase (如: userName)
- **常量命名**: UPPER_SNAKE_CASE (如: API_BASE_URL)

### 2. 目录规范
- 每个页面功能独立目录
- 按VIPER-VC架构组织文件
- 统一的导出文件 (index.ts)

### 3. 代码规范
- 使用TypeScript严格模式
- 遵循ESLint和Prettier配置
- 编写单元测试
- 添加详细的代码注释

## 部署架构

### 1. 构建配置
- **开发构建**: 快速编译，包含调试信息
- **生产构建**: 代码压缩，性能优化
- **分包策略**: 按功能模块分包

### 2. 部署方式
- **Docker容器化部署**
- **静态资源CDN加速**
- **多环境自动化部署**

## 性能优化

### 1. 构建优化
- Tree-shaking 去除无用代码
- 代码分割减少首屏加载
- 资源压缩和缓存策略

### 2. 运行时优化
- 组件懒加载
- 虚拟滚动
- 防抖节流
- 内存泄漏防护

## 安全考虑

### 1. 数据安全
- API接口鉴权
- 敏感数据加密
- XSS防护
- CSRF防护

### 2. 业务安全
- 操作权限控制
- 数据校验
- 异常处理
- 日志记录

## 扩展性设计

### 1. 插件化架构
- 模块热插拔
- 功能扩展点
- 自定义组件注册

### 2. 国际化支持
- 多语言切换
- 本地化配置
- 时区处理

### 3. 主题定制
- CSS变量系统
- 动态主题切换
- 品牌定制化

## 监控和维护

### 1. 错误监控
- 前端错误收集
- 性能监控
- 用户行为分析

### 2. 日志系统
- 操作日志记录
- 错误日志追踪
- 性能日志分析

### 3. 版本管理
- 自动版本检测
- 热更新机制
- 回滚策略

这个架构设计确保了项目的高质量、高性能和高可维护性，为业务的快速发展提供了坚实的技术基础。
