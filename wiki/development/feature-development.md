# 新功能开发流程

## 开发流程概述

本文档描述了在 Thunder ERP Client 项目中开发新功能的标准流程，确保代码质量和架构一致性。

## 开发流程图

```mermaid
graph TD
    A[需求分析] --> B[技术设计]
    B --> C[创建分支]
    C --> D[搭建架构]
    D --> E[实现功能]
    E --> F[编写测试]
    F --> G[代码审查]
    G --> H[集成测试]
    H --> I[部署验证]
    I --> J[合并主分支]
```

## 详细开发步骤

### 1. 需求分析

#### 需求文档模板
```markdown
# 功能需求文档

## 功能概述
- **功能名称**: [功能名称]
- **功能描述**: [详细描述功能的目的和价值]
- **优先级**: [高/中/低]
- **预估工期**: [开发时间估算]

## 业务需求
- **用户故事**: 作为[用户角色]，我希望[功能描述]，以便[业务价值]
- **验收标准**: [明确的验收条件]
- **业务规则**: [相关的业务逻辑和约束]

## 技术需求
- **性能要求**: [响应时间、并发量等]
- **兼容性要求**: [浏览器、设备兼容性]
- **安全要求**: [数据安全、权限控制等]

## 界面设计
- **UI设计稿**: [设计稿链接或描述]
- **交互流程**: [用户操作流程]
- **响应式要求**: [不同屏幕尺寸的适配]
```

#### 需求评审检查清单
- [ ] 需求描述清晰明确
- [ ] 验收标准可测试
- [ ] 技术可行性确认
- [ ] 与现有功能的兼容性
- [ ] 性能影响评估
- [ ] 安全风险评估

### 2. 技术设计

#### 架构设计文档模板
```markdown
# 技术设计文档

## 架构设计
- **模块位置**: src/modules/[module-name]
- **页面路由**: /[route-path]
- **VIPER-VC层级**: [各层职责分配]

## 数据模型
```typescript
// 实体定义
interface FeatureEntity {
  id: string;
  name: string;
  // ... 其他字段
}

// ViewModel定义
interface IFeatureViewModel {
  state: IFeatureState;
  computed: IFeatureComputed;
  actions: IFeatureActions;
}
```

## API接口设计
- **接口列表**: [所需的API接口]
- **数据格式**: [请求和响应格式]
- **错误处理**: [错误码和处理方式]

## 组件设计
- **组件层级**: [组件结构图]
- **组件复用**: [可复用的组件]
- **状态管理**: [状态流转设计]
```

### 3. 创建开发分支

```bash
# 从主分支创建功能分支
git checkout main
git pull origin main
git checkout -b feature/[feature-name]

# 分支命名规范
# feature/[功能名称]     - 新功能开发
# bugfix/[问题描述]     - 问题修复
# hotfix/[紧急修复]     - 紧急修复
# refactor/[重构内容]   - 代码重构
```

### 4. 搭建功能架构

#### 创建目录结构
```bash
# 创建功能模块目录
mkdir -p src/modules/[module]/views/[feature]
mkdir -p src/modules/[module]/views/[feature]/components
mkdir -p src/modules/[module]/api
mkdir -p src/modules/[module]/entity
mkdir -p src/modules/[module]/store

# 创建VIPER-VC架构文件
touch src/modules/[module]/views/[feature]/index.vue
touch src/modules/[module]/views/[feature]/viewModel.ts
touch src/modules/[module]/views/[feature]/presenter.ts
touch src/modules/[module]/views/[feature]/converter.ts
touch src/modules/[module]/views/[feature]/interactor.ts
```

#### 架构文件模板

##### viewModel.ts 模板
```typescript
import { Ref, ComputedRef } from 'vue';

// 状态接口
export interface IFeatureState {
  isLoading: Ref<boolean>;
  data: Ref<FeatureData | null>;
  error: Ref<string | null>;
}

// 计算属性接口
export interface IFeatureComputed {
  isDataValid: ComputedRef<boolean>;
  displayText: ComputedRef<string>;
}

// 操作接口
export interface IFeatureActions {
  loadData(): Promise<void>;
  saveData(data: FeatureData): Promise<void>;
  resetForm(): void;
}

// 主ViewModel接口
export interface IFeatureViewModel {
  state: IFeatureState;
  computed: IFeatureComputed;
  actions: IFeatureActions;
}
```

##### presenter.ts 模板
```typescript
import { reactive, computed, readonly } from 'vue';
import type { IFeatureViewModel, IFeatureState } from './viewModel';
import { FeatureInteractor } from './interactor';
import { FeatureConverter } from './converter';

export function useFeaturePresenter(): IFeatureViewModel {
  // 创建Interactor和Converter实例
  const interactor = new FeatureInteractor();
  const converter = new FeatureConverter();

  // 内部状态
  const _state = reactive<IFeatureState>({
    isLoading: false,
    data: null,
    error: null
  });

  // 计算属性
  const computed = {
    isDataValid: computed(() => {
      return _state.data !== null && _state.error === null;
    }),

    displayText: computed(() => {
      return _state.data ? converter.toDisplayText(_state.data) : '';
    })
  };

  // 操作方法
  const actions = {
    async loadData() {
      _state.isLoading = true;
      _state.error = null;
      
      try {
        const rawData = await interactor.fetchData();
        _state.data = converter.toViewModel(rawData);
      } catch (error) {
        _state.error = error.message;
        console.error('加载数据失败:', error);
      } finally {
        _state.isLoading = false;
      }
    },

    async saveData(data: FeatureData) {
      _state.isLoading = true;
      _state.error = null;

      try {
        const entityData = converter.toEntity(data);
        await interactor.saveData(entityData);
        _state.data = data;
      } catch (error) {
        _state.error = error.message;
        console.error('保存数据失败:', error);
        throw error;
      } finally {
        _state.isLoading = false;
      }
    },

    resetForm() {
      _state.data = null;
      _state.error = null;
    }
  };

  return {
    state: readonly(_state),
    computed,
    actions
  };
}
```

##### interactor.ts 模板
```typescript
import { FeatureApi } from '../api/featureApi';
import { useFeatureStore } from '../store/featureStore';
import type { FeatureEntity } from '../entity/featureEntity';

export class FeatureInteractor {
  private featureStore = useFeatureStore();

  /**
   * 获取功能数据
   */
  async fetchData(): Promise<FeatureEntity> {
    try {
      const response = await FeatureApi.getData();
      
      if (response.code === 0) {
        // 更新本地存储
        this.featureStore.setData(response.data);
        return response.data;
      } else {
        throw new Error(response.message || '获取数据失败');
      }
    } catch (error) {
      console.error('FeatureInteractor.fetchData 失败:', error);
      throw error;
    }
  }

  /**
   * 保存功能数据
   */
  async saveData(data: FeatureEntity): Promise<void> {
    try {
      const response = await FeatureApi.saveData(data);
      
      if (response.code === 0) {
        // 更新本地存储
        this.featureStore.setData(response.data);
      } else {
        throw new Error(response.message || '保存数据失败');
      }
    } catch (error) {
      console.error('FeatureInteractor.saveData 失败:', error);
      throw error;
    }
  }

  /**
   * 验证数据
   */
  validateData(data: FeatureEntity): boolean {
    // 实现数据验证逻辑
    return true;
  }
}
```

##### converter.ts 模板
```typescript
import type { FeatureEntity } from '../entity/featureEntity';
import type { FeatureViewModel } from './viewModel';

export class FeatureConverter {
  /**
   * 将实体数据转换为视图模型数据
   */
  toViewModel(entity: FeatureEntity): FeatureViewModel {
    return {
      id: entity.id,
      displayName: entity.name,
      formattedDate: this.formatDate(entity.createdAt),
      // ... 其他转换逻辑
    };
  }

  /**
   * 将视图模型数据转换为实体数据
   */
  toEntity(viewModel: FeatureViewModel): FeatureEntity {
    return {
      id: viewModel.id,
      name: viewModel.displayName,
      createdAt: this.parseDate(viewModel.formattedDate),
      // ... 其他转换逻辑
    };
  }

  /**
   * 转换为显示文本
   */
  toDisplayText(data: FeatureViewModel): string {
    return `${data.displayName} - ${data.formattedDate}`;
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date): string {
    return date.toLocaleDateString('zh-CN');
  }

  /**
   * 解析日期
   */
  private parseDate(dateString: string): Date {
    return new Date(dateString);
  }
}
```

### 5. 实现功能

#### Vue组件实现
```vue
<template>
  <div class="feature-container">
    <!-- 加载状态 -->
    <div v-if="vm.state.isLoading" class="loading">
      加载中...
    </div>

    <!-- 错误状态 -->
    <div v-else-if="vm.state.error" class="error">
      {{ vm.state.error }}
      <el-button @click="vm.actions.loadData">重试</el-button>
    </div>

    <!-- 正常内容 -->
    <div v-else class="content">
      <h1>{{ vm.computed.displayText }}</h1>
      
      <!-- 功能内容 -->
      <div class="feature-content">
        <!-- 实现具体功能 -->
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button @click="vm.actions.resetForm">重置</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useFeaturePresenter } from './presenter';

defineOptions({
  name: 'FeaturePage'
});

// 使用Presenter
const vm = useFeaturePresenter();

// 处理保存
const handleSave = async () => {
  try {
    await vm.actions.saveData(vm.state.data);
    ElMessage.success('保存成功');
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  vm.actions.loadData();
});
</script>

<style scoped>
.feature-container {
  padding: 20px;
}

.loading, .error {
  text-align: center;
  padding: 40px;
}

.content {
  max-width: 800px;
  margin: 0 auto;
}

.actions {
  margin-top: 20px;
  text-align: right;
}
</style>
```

### 6. 编写测试

#### 单元测试
```typescript
// feature.presenter.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useFeaturePresenter } from './presenter';

// Mock依赖
vi.mock('./interactor');
vi.mock('./converter');

describe('FeaturePresenter', () => {
  let presenter: ReturnType<typeof useFeaturePresenter>;

  beforeEach(() => {
    presenter = useFeaturePresenter();
  });

  it('应该正确初始化状态', () => {
    expect(presenter.state.isLoading).toBe(false);
    expect(presenter.state.data).toBeNull();
    expect(presenter.state.error).toBeNull();
  });

  it('应该正确加载数据', async () => {
    const mockData = { id: '1', name: 'test' };
    
    // Mock interactor
    vi.mocked(FeatureInteractor.prototype.fetchData).mockResolvedValue(mockData);
    
    await presenter.actions.loadData();
    
    expect(presenter.state.isLoading).toBe(false);
    expect(presenter.state.data).toEqual(mockData);
    expect(presenter.state.error).toBeNull();
  });

  it('应该正确处理错误', async () => {
    const errorMessage = '网络错误';
    
    vi.mocked(FeatureInteractor.prototype.fetchData).mockRejectedValue(
      new Error(errorMessage)
    );
    
    await presenter.actions.loadData();
    
    expect(presenter.state.isLoading).toBe(false);
    expect(presenter.state.error).toBe(errorMessage);
  });
});
```

#### 组件测试
```typescript
// Feature.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import Feature from './index.vue';

// Mock presenter
vi.mock('./presenter', () => ({
  useFeaturePresenter: () => ({
    state: {
      isLoading: false,
      data: { id: '1', name: 'test' },
      error: null
    },
    computed: {
      displayText: 'Test Feature'
    },
    actions: {
      loadData: vi.fn(),
      saveData: vi.fn(),
      resetForm: vi.fn()
    }
  })
}));

describe('Feature', () => {
  it('应该正确渲染', () => {
    const wrapper = mount(Feature);
    
    expect(wrapper.find('.feature-container').exists()).toBe(true);
    expect(wrapper.text()).toContain('Test Feature');
  });

  it('应该在挂载时加载数据', () => {
    const mockLoadData = vi.fn();
    
    vi.mocked(useFeaturePresenter).mockReturnValue({
      // ... mock返回值
      actions: { loadData: mockLoadData }
    });
    
    mount(Feature);
    
    expect(mockLoadData).toHaveBeenCalled();
  });
});
```

### 7. 代码审查

#### 审查检查清单
- [ ] 代码符合项目规范
- [ ] VIPER-VC架构正确实现
- [ ] 错误处理完善
- [ ] 性能考虑充分
- [ ] 测试覆盖率达标
- [ ] 文档更新完整
- [ ] 安全性检查通过

#### 提交Pull Request
```bash
# 提交代码
git add .
git commit -m "feat: 添加[功能名称]功能

- 实现[具体功能点1]
- 实现[具体功能点2]
- 添加相关测试
- 更新文档

Closes #[issue-number]"

# 推送分支
git push origin feature/[feature-name]

# 创建Pull Request
# 在GitHub/GitLab上创建PR，填写详细描述
```

### 8. 部署和验证

#### 部署前检查
```bash
# 运行所有测试
pnpm test

# 类型检查
pnpm type-check

# 代码质量检查
pnpm lint

# 构建检查
pnpm build
```

#### 功能验证
- [ ] 功能正常运行
- [ ] 性能指标达标
- [ ] 兼容性测试通过
- [ ] 用户体验良好
- [ ] 错误处理正确

### 9. 文档更新

#### 更新项目文档
- [ ] API文档更新
- [ ] 用户手册更新
- [ ] 开发文档更新
- [ ] 变更日志更新

#### 知识分享
- [ ] 技术分享会
- [ ] 代码走读
- [ ] 最佳实践总结

通过遵循这个开发流程，可以确保新功能的高质量交付和项目的长期可维护性。
