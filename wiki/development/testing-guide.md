# 测试指南

## 测试策略概述

Thunder ERP Client 项目采用多层次的测试策略，确保代码质量和系统稳定性。

### 测试金字塔

```mermaid
graph TD
    A[E2E测试 - 少量] --> B[集成测试 - 适量]
    B --> C[单元测试 - 大量]
    
    style A fill:#ff9999
    style B fill:#ffcc99
    style C fill:#99ff99
```

## 测试环境配置

### 1. 测试工具栈

- **测试框架**: Vitest
- **组件测试**: Vue Test Utils
- **E2E测试**: Playwright
- **覆盖率**: c8
- **Mock工具**: Vitest Mock

### 2. 配置文件

#### vitest.config.js
```javascript
import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'happy-dom',
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  }
});
```

#### playwright.config.ts
```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
});
```

## 单元测试

### 1. Presenter层测试

```typescript
// openTable.presenter.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useOpenTablePresenter } from './presenter';
import { OpenTableInteractor } from './interactor';
import { OpenTableConverter } from './converter';

// Mock依赖
vi.mock('./interactor');
vi.mock('./converter');

describe('OpenTablePresenter', () => {
  let presenter: ReturnType<typeof useOpenTablePresenter>;
  let mockInteractor: vi.Mocked<OpenTableInteractor>;
  let mockConverter: vi.Mocked<OpenTableConverter>;

  beforeEach(() => {
    // 重置所有mock
    vi.clearAllMocks();
    
    // 创建mock实例
    mockInteractor = vi.mocked(new OpenTableInteractor());
    mockConverter = vi.mocked(new OpenTableConverter());
    
    // 创建presenter实例
    presenter = useOpenTablePresenter();
  });

  describe('初始化', () => {
    it('应该正确初始化状态', () => {
      expect(presenter.state.isLoading).toBe(false);
      expect(presenter.state.roomBill).toBeNull();
      expect(presenter.state.marketBill).toBeNull();
      expect(presenter.state.addedProducts).toEqual([]);
    });

    it('应该正确初始化表单数据', () => {
      expect(presenter.state.form.booker).toBe('');
      expect(presenter.state.form.settleImmediately).toBe(true);
      expect(presenter.state.form.minimumCharge).toBe(0);
    });
  });

  describe('数据加载', () => {
    it('应该正确加载开台视图数据', async () => {
      const mockData = {
        roomBill: { amount: 10000, type: 'buyout' },
        marketBill: { products: [] }
      };

      mockInteractor.fetchOpenView.mockResolvedValue(mockData);

      await presenter.actions.fetchOpenView();

      expect(presenter.state.isLoading).toBe(false);
      expect(presenter.state.roomBill).toEqual(mockData.roomBill);
      expect(presenter.state.marketBill).toEqual(mockData.marketBill);
    });

    it('应该正确处理加载错误', async () => {
      const errorMessage = '网络错误';
      mockInteractor.fetchOpenView.mockRejectedValue(new Error(errorMessage));

      await presenter.actions.fetchOpenView();

      expect(presenter.state.isLoading).toBe(false);
      expect(presenter.state.error).toBe(errorMessage);
    });
  });

  describe('商品管理', () => {
    it('应该正确添加商品', () => {
      const product = {
        id: '1',
        name: '测试商品',
        price: 1000,
        quantity: 1
      };

      presenter.actions.handleAddProduct([product]);

      expect(presenter.state.addedProducts).toContain(product);
    });

    it('应该正确计算总金额', () => {
      presenter.state.roomBill = { amount: 10000 };
      presenter.state.addedProducts = [
        { id: '1', price: 1000, quantity: 2 },
        { id: '2', price: 500, quantity: 1 }
      ];

      expect(presenter.computed.totalBill.value).toBe(12500);
    });
  });

  describe('表单验证', () => {
    it('应该验证必填字段', () => {
      presenter.state.form.booker = '';
      expect(presenter.actions.validateCart()).toBe(false);

      presenter.state.form.booker = '张三';
      expect(presenter.actions.validateCart()).toBe(true);
    });

    it('应该验证商品选择', () => {
      presenter.state.marketBill = {
        packageVOs: [{
          optionalGroups: [{
            minSelect: 2,
            selectedCount: 1
          }]
        }]
      };

      expect(presenter.computed.isAllCartItemsValid.value).toBe(false);
    });
  });

  describe('开台操作', () => {
    it('应该正确处理开台成功', async () => {
      const mockResult = {
        success: true,
        data: { sessionId: 'test-session' }
      };

      mockInteractor.openTable.mockResolvedValue(mockResult);

      const result = await presenter.actions.handleImmediatePay();

      expect(result).toEqual(mockResult.data);
      expect(presenter.state.isLoading).toBe(false);
    });

    it('应该正确处理开台失败', async () => {
      const mockError = new Error('开台失败');
      mockInteractor.openTable.mockRejectedValue(mockError);

      const result = await presenter.actions.handleImmediatePay();

      expect(result).toBeNull();
      expect(presenter.state.isLoading).toBe(false);
    });
  });
});
```

### 2. Converter层测试

```typescript
// openTable.converter.test.ts
import { describe, it, expect } from 'vitest';
import { OpenTableConverter } from './converter';

describe('OpenTableConverter', () => {
  describe('buildOpenTableParams', () => {
    it('应该正确构建开台参数', () => {
      const form = {
        booker: '张三',
        roomManager: '李四',
        consumptionMode: 'buyout',
        settleImmediately: true
      };

      const roomBill = {
        type: 'buyout',
        amount: 10000,
        duration: 240
      };

      const marketBill = {
        products: [
          { id: '1', quantity: 2, price: 500 }
        ]
      };

      const addedProducts = [
        { id: '2', quantity: 1, price: 1000 }
      ];

      const result = OpenTableConverter.buildOpenTableParams(
        form,
        roomBill,
        marketBill,
        addedProducts
      );

      expect(result).toEqual({
        booker: '张三',
        roomManager: '李四',
        consumptionMode: 'buyout',
        settleImmediately: true,
        billingType: 'buyout',
        amount: 10000,
        duration: 240,
        products: [
          { id: '1', quantity: 2, price: 500 },
          { id: '2', quantity: 1, price: 1000 }
        ]
      });
    });
  });

  describe('数据转换', () => {
    it('应该正确转换商品数据', () => {
      const rawProduct = {
        productId: '1',
        productName: '测试商品',
        unitPrice: 1000,
        qty: 2
      };

      const result = OpenTableConverter.convertProduct(rawProduct);

      expect(result).toEqual({
        id: '1',
        name: '测试商品',
        price: 1000,
        quantity: 2,
        totalPrice: 2000
      });
    });
  });
});
```

### 3. Interactor层测试

```typescript
// openTable.interactor.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { OpenTableInteractor } from './interactor';
import { OrderApi } from '../api/orderApi';

// Mock API
vi.mock('../api/orderApi');

describe('OpenTableInteractor', () => {
  let interactor: OpenTableInteractor;

  beforeEach(() => {
    vi.clearAllMocks();
    interactor = new OpenTableInteractor();
  });

  describe('openTable', () => {
    it('应该正确调用开台API', async () => {
      const params = {
        booker: '张三',
        roomId: 'room-1',
        amount: 10000
      };

      const mockResponse = {
        success: true,
        data: { sessionId: 'test-session' }
      };

      vi.mocked(OrderApi.openOrder).mockResolvedValue(mockResponse);

      const result = await interactor.openTable(params);

      expect(OrderApi.openOrder).toHaveBeenCalledWith(params);
      expect(result).toEqual(mockResponse);
    });

    it('应该正确处理API错误', async () => {
      const params = { booker: '张三' };
      const error = new Error('API错误');

      vi.mocked(OrderApi.openOrder).mockRejectedValue(error);

      await expect(interactor.openTable(params)).rejects.toThrow('API错误');
    });
  });

  describe('fetchOpenView', () => {
    it('应该正确获取开台视图数据', async () => {
      const roomId = 'room-1';
      const mockData = {
        roomBill: { amount: 10000 },
        marketBill: { products: [] }
      };

      vi.mocked(OrderApi.getOpenView).mockResolvedValue({
        code: 0,
        data: mockData
      });

      const result = await interactor.fetchOpenView(roomId);

      expect(result).toEqual(mockData);
    });
  });
});
```

## 组件测试

### 1. Vue组件测试

```typescript
// OpenTable.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import { ElButton, ElTabs } from 'element-plus';
import OpenTable from './index.vue';

// Mock presenter
const mockPresenter = {
  state: {
    isLoading: false,
    activeTab: 'buyout',
    roomBill: { amount: 10000 },
    marketBill: { products: [] },
    addedProducts: [],
    form: {
      booker: '',
      settleImmediately: true
    }
  },
  computed: {
    totalBill: { value: 10000 },
    isAllCartItemsValid: { value: true }
  },
  actions: {
    handleImmediatePay: vi.fn(),
    handleAddProduct: vi.fn(),
    fetchOpenView: vi.fn()
  }
};

vi.mock('./presenter', () => ({
  useOpenTable: () => mockPresenter
}));

describe('OpenTable', () => {
  it('应该正确渲染组件', () => {
    const wrapper = mount(OpenTable, {
      global: {
        components: {
          ElButton,
          ElTabs
        }
      }
    });

    expect(wrapper.find('.open-table-container').exists()).toBe(true);
    expect(wrapper.find('.billing-section').exists()).toBe(true);
    expect(wrapper.find('.product-section').exists()).toBe(true);
  });

  it('应该显示正确的总金额', () => {
    const wrapper = mount(OpenTable);
    
    expect(wrapper.text()).toContain('¥100.00');
  });

  it('应该在点击开台按钮时调用处理函数', async () => {
    const wrapper = mount(OpenTable);
    const button = wrapper.find('[data-test="submit-button"]');

    await button.trigger('click');

    expect(mockPresenter.actions.handleImmediatePay).toHaveBeenCalled();
  });

  it('应该在加载状态时禁用按钮', async () => {
    mockPresenter.state.isLoading = true;
    
    const wrapper = mount(OpenTable);
    const button = wrapper.find('[data-test="submit-button"]');

    expect(button.attributes('disabled')).toBeDefined();
  });

  it('应该在表单无效时禁用按钮', async () => {
    mockPresenter.computed.isAllCartItemsValid.value = false;
    
    const wrapper = mount(OpenTable);
    const button = wrapper.find('[data-test="submit-button"]');

    expect(button.attributes('disabled')).toBeDefined();
  });
});
```

### 2. 组件交互测试

```typescript
// ProductBill.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import ProductBill from './ProductBill.vue';

describe('ProductBill', () => {
  const defaultProps = {
    marketBill: {
      packageVOs: [],
      standardProducts: []
    },
    addedProducts: []
  };

  it('应该正确显示商品列表', () => {
    const props = {
      ...defaultProps,
      addedProducts: [
        { id: '1', name: '可乐', price: 500, quantity: 2 },
        { id: '2', name: '薯片', price: 800, quantity: 1 }
      ]
    };

    const wrapper = mount(ProductBill, { props });

    expect(wrapper.text()).toContain('可乐');
    expect(wrapper.text()).toContain('薯片');
  });

  it('应该正确计算商品小计', () => {
    const props = {
      ...defaultProps,
      addedProducts: [
        { id: '1', price: 500, quantity: 2 },
        { id: '2', price: 800, quantity: 1 }
      ]
    };

    const wrapper = mount(ProductBill, { props });

    expect(wrapper.text()).toContain('¥18.00'); // (500*2 + 800*1) / 100
  });

  it('应该在点击添加商品时触发事件', async () => {
    const wrapper = mount(ProductBill, { props: defaultProps });
    const button = wrapper.find('[data-test="add-product-button"]');

    await button.trigger('click');

    expect(wrapper.emitted('add-product')).toBeTruthy();
  });
});
```

## 集成测试

### 1. API集成测试

```typescript
// api.integration.test.ts
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { OrderApi } from '@/modules/order/api/orderApi';

// 创建mock服务器
const server = setupServer(
  rest.post('/api/order/open', (req, res, ctx) => {
    return res(
      ctx.json({
        code: 0,
        message: 'success',
        data: {
          sessionId: 'test-session-id',
          orderNo: 'ORDER-001'
        }
      })
    );
  }),

  rest.get('/api/room/open-view/:roomId', (req, res, ctx) => {
    return res(
      ctx.json({
        code: 0,
        data: {
          roomBill: { amount: 10000, type: 'buyout' },
          marketBill: { products: [] }
        }
      })
    );
  })
);

beforeAll(() => server.listen());
afterAll(() => server.close());

describe('API集成测试', () => {
  it('应该正确调用开台API', async () => {
    const params = {
      booker: '张三',
      roomId: 'room-1',
      amount: 10000
    };

    const result = await OrderApi.openOrder(params);

    expect(result.code).toBe(0);
    expect(result.data.sessionId).toBe('test-session-id');
  });

  it('应该正确获取开台视图数据', async () => {
    const result = await OrderApi.getOpenView('room-1');

    expect(result.code).toBe(0);
    expect(result.data.roomBill.amount).toBe(10000);
  });
});
```

### 2. 状态管理集成测试

```typescript
// store.integration.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useOpenTableStore } from '@/modules/room/store/openTableStore';

describe('OpenTableStore集成测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('应该正确管理开台状态', () => {
    const store = useOpenTableStore();

    // 初始状态
    expect(store.currentRoom).toBeNull();
    expect(store.selectedProducts).toEqual([]);

    // 设置房间信息
    const room = { id: 'room-1', name: '包厢A' };
    store.setCurrentRoom(room);
    expect(store.currentRoom).toEqual(room);

    // 添加商品
    const product = { id: '1', name: '可乐', price: 500 };
    store.addProduct(product);
    expect(store.selectedProducts).toContain(product);

    // 计算总金额
    expect(store.totalAmount).toBe(500);
  });
});
```

## E2E测试

### 1. 完整业务流程测试

```typescript
// openTable.e2e.test.ts
import { test, expect } from '@playwright/test';

test.describe('开台功能E2E测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/room');
  });

  test('完整开台流程', async ({ page }) => {
    // 1. 选择包厢
    await page.click('[data-test="room-card-1"]');
    await expect(page).toHaveURL(/.*open-table/);

    // 2. 填写开台信息
    await page.fill('[data-test="booker-input"]', '张三');
    await page.selectOption('[data-test="consumption-mode"]', 'buyout');

    // 3. 选择计费方案
    await page.click('[data-test="billing-tab-buyout"]');
    await page.click('[data-test="time-slot-evening"]');

    // 4. 添加商品
    await page.click('[data-test="add-product-button"]');
    await page.click('[data-test="product-item-1"]');
    await page.click('[data-test="confirm-product-button"]');

    // 5. 确认开台
    await page.click('[data-test="submit-button"]');

    // 6. 验证结果
    await expect(page.locator('[data-test="success-message"]')).toBeVisible();
    await expect(page).toHaveURL(/.*room/);
  });

  test('表单验证', async ({ page }) => {
    await page.click('[data-test="room-card-1"]');
    
    // 不填写必填字段直接提交
    await page.click('[data-test="submit-button"]');
    
    // 应该显示验证错误
    await expect(page.locator('[data-test="error-message"]')).toBeVisible();
    await expect(page.locator('[data-test="error-message"]')).toContainText('请填写开台人');
  });

  test('商品选择验证', async ({ page }) => {
    await page.click('[data-test="room-card-1"]');
    await page.fill('[data-test="booker-input"]', '张三');
    
    // 添加套餐但不完成配置
    await page.click('[data-test="add-product-button"]');
    await page.click('[data-test="package-item-1"]');
    // 不选择必选项，直接确认
    await page.click('[data-test="confirm-package-button"]');
    
    // 应该显示套餐配置错误
    await expect(page.locator('[data-test="package-error"]')).toBeVisible();
  });
});
```

### 2. 跨浏览器兼容性测试

```typescript
// compatibility.e2e.test.ts
import { test, expect, devices } from '@playwright/test';

const browsers = ['chromium', 'firefox', 'webkit'];

browsers.forEach(browserName => {
  test.describe(`${browserName} 兼容性测试`, () => {
    test.use({ ...devices[`Desktop ${browserName}`] });

    test('基本功能正常', async ({ page }) => {
      await page.goto('/open-table');
      
      // 检查页面加载
      await expect(page.locator('[data-test="open-table-container"]')).toBeVisible();
      
      // 检查交互功能
      await page.click('[data-test="billing-tab-hourly"]');
      await expect(page.locator('[data-test="hourly-billing"]')).toBeVisible();
    });
  });
});
```

## 测试运行和报告

### 1. 运行测试命令

```bash
# 运行所有单元测试
pnpm test

# 运行特定文件测试
pnpm test openTable.presenter.test.ts

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行E2E测试
pnpm test:e2e

# 运行测试并监听文件变化
pnpm test:watch
```

### 2. 持续集成配置

```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install pnpm
        run: npm install -g pnpm
        
      - name: Install dependencies
        run: pnpm install
        
      - name: Run unit tests
        run: pnpm test:coverage
        
      - name: Run E2E tests
        run: pnpm test:e2e
        
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### 3. 测试覆盖率要求

- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **E2E测试覆盖率**: ≥ 40%
- **关键业务流程**: 100%

通过完善的测试策略，确保项目的高质量和稳定性。
