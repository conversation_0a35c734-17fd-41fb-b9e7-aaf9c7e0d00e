# 环境配置指南

## 系统要求

### 开发环境要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 18.0+ (推荐使用 LTS 版本)
- **pnpm**: 8.0+ (必须使用，不支持 npm/yarn)
- **Git**: 2.20+
- **IDE**: VSCode (推荐) 或其他支持 TypeScript 的编辑器

### 浏览器支持
- Chrome 49+
- Safari 10+
- Firefox 52+
- Edge 14+

## 安装步骤

### 1. 安装 Node.js
```bash
# 使用 nvm 安装 (推荐)
nvm install 18
nvm use 18

# 或直接从官网下载安装
# https://nodejs.org/
```

### 2. 安装 pnpm
```bash
# 使用 npm 安装 pnpm
npm install -g pnpm

# 或使用 curl 安装
curl -fsSL https://get.pnpm.io/install.sh | sh -

# 验证安装
pnpm --version
```

### 3. 克隆项目
```bash
git clone <repository-url>
cd thunder-erp-client
```

### 4. 安装依赖
```bash
# 使用项目提供的安装脚本 (推荐)
# macOS/Linux
chmod +x setup.sh
./setup.sh

# Windows
setup.bat

# 或手动安装
pnpm install
```

## 环境配置

### 1. 环境变量配置

项目支持多环境配置，通过不同的 `.env` 文件管理：

#### 主应用环境文件
```bash
# .env.development (开发环境)
NODE_ENV=development
VITE_CLIENT_TYPE=client
VITE_APP_BASE_API=http://localhost:8080
VITE_NATS_SERVER=ws://localhost:4222
VITE_ENABLE_VCONSOLE=true

# .env.stage (测试环境)
NODE_ENV=production
VITE_CLIENT_TYPE=client
VITE_APP_BASE_API=https://stage-api.example.com
VITE_NATS_SERVER=wss://stage-nats.example.com
VITE_ENABLE_VCONSOLE=false

# .env.production (生产环境)
NODE_ENV=production
VITE_CLIENT_TYPE=client
VITE_APP_BASE_API=https://api.example.com
VITE_NATS_SERVER=wss://nats.example.com
VITE_ENABLE_VCONSOLE=false
```

#### Pad应用环境文件
```bash
# .env.client-pad.development (Pad开发环境)
NODE_ENV=development
VITE_CLIENT_TYPE=pad
VITE_APP_BASE_API=http://localhost:8080
VITE_NATS_SERVER=ws://localhost:4222
VITE_ENABLE_PAD_FEATURES=true

# .env.client-pad.production (Pad生产环境)
NODE_ENV=production
VITE_CLIENT_TYPE=pad
VITE_APP_BASE_API=https://api.example.com
VITE_NATS_SERVER=wss://nats.example.com
VITE_ENABLE_PAD_FEATURES=true
```

### 2. 环境变量说明

| 变量名 | 说明 | 可选值 | 默认值 |
|--------|------|--------|--------|
| `NODE_ENV` | Node.js 环境 | development/production | development |
| `VITE_CLIENT_TYPE` | 客户端类型 | client/pad | client |
| `VITE_APP_BASE_API` | API 基础地址 | URL | - |
| `VITE_NATS_SERVER` | NATS 服务器地址 | WebSocket URL | - |
| `VITE_NATS_TOKEN` | NATS 认证令牌 | string | - |
| `VITE_NATS_USER` | NATS 用户名 | string | - |
| `VITE_NATS_PASSWORD` | NATS 密码 | string | - |
| `VITE_ENABLE_VCONSOLE` | 启用移动端调试 | true/false | false |
| `VITE_ENABLE_PAD_FEATURES` | 启用Pad特性 | true/false | false |

### 3. 本地开发配置

#### 创建本地配置文件
```bash
# 复制示例配置文件
cp .env.example .env.development.local
cp .env.client-pad.example .env.client-pad.development.local

# 编辑本地配置 (不会被提交到版本控制)
vim .env.development.local
```

#### 本地配置示例
```bash
# .env.development.local
VITE_APP_BASE_API=http://*************:8080
VITE_NATS_SERVER=ws://*************:4222
VITE_NATS_TOKEN=your_local_token
VITE_DEBUG_MODE=true
```

## 开发工具配置

### 1. VSCode 配置

#### 推荐扩展
```json
{
  "recommendations": [
    "vue.volar",
    "vue.typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

#### 工作区设置
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "vue.codeActions.enabled": true,
  "tailwindCSS.includeLanguages": {
    "vue": "html"
  }
}
```

### 2. Git 配置

#### Git Hooks 设置
```bash
# 安装 husky (如果项目中没有)
pnpm add -D husky

# 设置 Git hooks
npx husky install

# 添加 pre-commit hook
npx husky add .husky/pre-commit "pnpm lint-staged"
```

#### .gitignore 配置
```gitignore
# 环境配置
.env.local
.env.development.local
.env.stage.local
.env.production.local
.env.client-pad.local
.env.client-pad.development.local
.env.client-pad.stage.local
.env.client-pad.production.local

# 依赖
node_modules/
.pnpm-store/

# 构建产物
dist/
dist-client-pad/

# 日志
*.log
logs/

# IDE
.vscode/settings.json
.idea/

# 系统文件
.DS_Store
Thumbs.db
```

## 启动项目

### 1. 开发模式启动

```bash
# 启动主应用 (端口 5173)
pnpm dev

# 启动 Pad 应用 (端口 5174)
pnpm dev:client-pad

# 启动测试环境
pnpm dev:stage
```

### 2. 构建项目

```bash
# 开发环境构建
pnpm build:dev

# 测试环境构建
pnpm build:stage

# 生产环境构建
pnpm build

# Pad 应用构建
pnpm build:client-pad
pnpm build:client-pad:prod
```

### 3. 预览构建结果

```bash
# 预览主应用
pnpm preview

# 预览 Pad 应用
pnpm preview:client-pad
```

## 常见问题

### 1. pnpm 安装失败
**问题**: 使用 npm 或 yarn 安装依赖失败
**解决**: 必须使用 pnpm，项目使用了 workspace: 协议

```bash
# 清理 node_modules
rm -rf node_modules
rm package-lock.json  # 如果存在
rm yarn.lock          # 如果存在

# 使用 pnpm 重新安装
pnpm install
```

### 2. 端口冲突
**问题**: 默认端口被占用
**解决**: 修改 vite 配置或使用其他端口

```bash
# 使用其他端口启动
pnpm dev --port 3000
pnpm dev:client-pad --port 3001
```

### 3. 环境变量不生效
**问题**: 配置的环境变量在应用中获取不到
**解决**: 检查变量名前缀和文件名

- 环境变量必须以 `VITE_` 开头
- 文件名必须匹配当前环境
- 重启开发服务器

### 4. TypeScript 类型错误
**问题**: 类型检查失败
**解决**: 更新类型定义和配置

```bash
# 重新生成类型定义
pnpm generate-api

# 检查 TypeScript 配置
npx tsc --noEmit
```

### 5. 热更新不工作
**问题**: 代码修改后页面不自动刷新
**解决**: 检查文件监听和代理配置

```bash
# 增加文件监听限制
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 性能优化

### 1. 开发环境优化
```bash
# 启用 SWC 编译器 (更快的 TypeScript 编译)
pnpm add -D @swc/core

# 使用 esbuild 进行依赖预构建
# 在 vite.config.ts 中配置
optimizeDeps: {
  include: ['vue', 'vue-router', 'pinia']
}
```

### 2. 构建优化
```bash
# 分析构建产物
pnpm build --analyze

# 启用 gzip 压缩
pnpm build && gzip -9 dist/**/*.js
```

## 部署准备

### 1. 环境检查
```bash
# 检查代码质量
pnpm lint
pnpm type-check

# 运行测试
pnpm test

# 构建检查
pnpm build
```

### 2. Docker 部署
```dockerfile
# Dockerfile 示例
FROM node:18-alpine
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile
COPY . .
RUN pnpm build
EXPOSE 80
CMD ["pnpm", "preview", "--host", "0.0.0.0", "--port", "80"]
```

通过以上配置，您可以快速搭建完整的开发环境，开始 Thunder ERP Client 项目的开发工作。
