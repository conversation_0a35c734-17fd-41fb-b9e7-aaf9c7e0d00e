# Thunder ERP Windows 桌面应用开发指南

## 项目概述

本指南介绍如何将 Thunder ERP Web 应用封装为 Windows 桌面应用，通过 Electron 框架实现与 Android 版本功能的完全兼容。

## 技术选型

### 核心技术
- **框架**: Electron 28+
- **打包工具**: Electron Builder
- **加载方式**: 远程 URL 加载
- **原生功能**: 通过 Electron 主进程和 IPC 通信实现

### 功能要点
1. 远程 URL 加载与参数传递
2. JSBridge 等效实现
3. 打印功能支持
4. 应用升级机制
5. 设备信息获取
6. VOD 服务代理实现

## 项目结构

```
thunder-erp-electron/
├── src/
│   ├── main/              # 主进程代码
│   │   ├── index.js       # 主进程入口文件
│   │   ├── updater.js     # 应用更新模块
│   │   ├── printer.js     # 打印模块
│   │   ├── vod.js         # VOD服务模块
│   │   └── ipc.js         # IPC 通信处理
│   ├── preload/           # 预加载脚本
│   │   ├── index.js       # 预加载主文件
│   │   ├── bridge.js      # JS 桥接实现
│   │   └── interfaces/    # 接口实现目录
│   │       ├── webview.js  # WebView接口
│   │       ├── printer.js  # 打印接口
│   │       ├── vod.js      # VOD接口
│   │       └── upgrade.js  # 升级接口
│   └── assets/            # 静态资源
├── build/                 # 构建资源
├── package.json           # 项目配置
└── README.md              # 项目说明
```

## 核心功能实现

### 1. 主进程入口文件

```javascript
// src/main/index.js
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const os = require('os');

let mainWindow = null;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    title: 'Thunder ERP',
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
      allowRunningInsecureContent: true
    }
  });

  // 获取设备信息用于URL参数
  const deviceId = os.hostname();
  const ipAddress = getIpAddress();

  // 加载远程URL并传递设备信息参数
  const remoteUrl = `https://your-server.com/erp-web?deviceId=${deviceId}&ipAddress=${ipAddress}&macAddress=${deviceId}`;
  mainWindow.loadURL(remoteUrl);

  // 开发环境打开开发者工具
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

function getIpAddress() {
  const nets = os.networkInterfaces();
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      if (!net.internal && net.family === 'IPv4') {
        return net.address;
      }
    }
  }
  return '127.0.0.1';
}

app.whenReady().then(createWindow);
```

### 2. 预加载脚本

```javascript
// src/preload/index.js
const { contextBridge, ipcRenderer } = require('electron');

// 设备信息 - 与Android提供的格式保持一致
contextBridge.exposeInMainWorld('deviceInfo', {
  getDeviceId: () => ipcRenderer.invoke('get-device-id'),
  getIpAddress: () => ipcRenderer.invoke('get-ip-address'),
  getMacAddress: () => ipcRenderer.invoke('get-mac-address'),
  getPlatformInfo: () => ipcRenderer.invoke('get-platform-info')
});

// JSBridge实现
contextBridge.exposeInMainWorld('JSBridge', {
  evaluateJavascript: script => {
    try {
      return eval(script);
    } catch (e) {
      console.error('执行JavaScript失败:', e);
      return null;
    }
  },

  callJsMethod: (methodName, ...args) => {
    if (typeof window[methodName] === 'function') {
      try {
        return window[methodName](...args);
      } catch (e) {
        console.error(`调用 ${methodName} 方法失败:`, e);
        return null;
      }
    }
    return null;
  }
});
```

### 3. WebView接口实现

```javascript
// src/preload/interfaces/webview.js
contextBridge.exposeInMainWorld('webViewBridge', {
  // 重新加载WebView
  reloadWebView: async () => {
    const deviceId = await ipcRenderer.invoke('get-device-id');
    const ipAddress = await ipcRenderer.invoke('get-ip-address');
    
    const baseUrl = window.location.origin + window.location.pathname;
    const url = `${baseUrl}?deviceId=${deviceId}&ipAddress=${ipAddress}&macAddress=${deviceId}`;
    
    window.location.href = url;
  },

  // 退出应用程序
  exitApp: () => {
    ipcRenderer.invoke('exit-app');
  }
});
```

### 4. 打印接口实现

```javascript
// src/preload/interfaces/printer.js
contextBridge.exposeInMainWorld('printerBridge', {
  // 扫描打印机
  scanPrinters: async () => {
    return await ipcRenderer.invoke('scan-printers');
  },

  // 原始打印
  rawPrint: async (ip, port, commandsJson) => {
    return await ipcRenderer.invoke('raw-print', { ip, port, commandsJson });
  },

  // 测试打印
  printTest: async (ip, port) => {
    return await ipcRenderer.invoke('print-test', { ip, port });
  }
});
```

### 5. VOD接口实现

```javascript
// src/preload/interfaces/vod.js
contextBridge.exposeInMainWorld('vodBridge', {
  request: (method, url, params, body, callbackId) => {
    ipcRenderer
      .invoke('vod-request', { method, url, params, body })
      .then(result => {
        if (typeof window.vodBridgeCallback === 'function') {
          window.vodBridgeCallback(callbackId, JSON.stringify(result));
        }
      })
      .catch(error => {
        const errorObj = {
          errcode: 500,
          errmsg: `请求失败: ${error.message}`
        };
        if (typeof window.vodBridgeCallback === 'function') {
          window.vodBridgeCallback(callbackId, JSON.stringify(errorObj));
        }
      });
  }
});
```

### 6. 打印模块

```javascript
// src/main/printer.js
const net = require('net');

module.exports = function setupPrinter(ipcMain) {
  // 扫描网络打印机
  ipcMain.handle('scan-printers', async () => {
    try {
      const printers = [
        { ip: '*************', port: 9100 },
        { ip: '*************', port: 9100 }
      ];
      return JSON.stringify(printers);
    } catch (error) {
      console.error('扫描打印机失败:', error);
      return '[]';
    }
  });

  // 发送原始打印命令
  ipcMain.handle('raw-print', async (event, { ip, port, commandsJson }) => {
    try {
      const commands = JSON.parse(commandsJson);

      return await new Promise((resolve, reject) => {
        const client = new net.Socket();
        client.setTimeout(5000);

        client.on('timeout', () => {
          client.end();
          reject(new Error('打印超时'));
        });

        client.on('error', err => {
          reject(err);
        });

        client.connect(port, ip, () => {
          console.log(`已连接到打印机 ${ip}:${port}`);

          for (const cmd of commands) {
            let data;
            switch (cmd.type) {
              case 'text':
                data = Buffer.from(cmd.data, 'utf8');
                break;
              case 'command':
              case 'raw':
                data = Buffer.from(cmd.data);
                break;
            }

            if (data) {
              client.write(data);
            }
          }

          client.end(() => {
            resolve(true);
          });
        });
      });
    } catch (error) {
      console.error('打印失败:', error);
      return false;
    }
  });
};
```

## 构建配置

### package.json 配置

```json
{
  "name": "thunder-erp-electron",
  "version": "1.0.0",
  "description": "Thunder ERP Windows Desktop Application",
  "main": "src/main/index.js",
  "scripts": {
    "start": "electron .",
    "dev": "NODE_ENV=development electron .",
    "build": "electron-builder",
    "build:win": "electron-builder --win --x64"
  },
  "dependencies": {
    "axios": "^1.7.7",
    "electron-updater": "^6.1.7",
    "electron-store": "^8.1.0"
  },
  "devDependencies": {
    "electron": "^28.1.0",
    "electron-builder": "^24.9.1"
  },
  "build": {
    "appId": "com.thunder.erp",
    "productName": "Thunder ERP",
    "directories": {
      "output": "dist"
    },
    "files": ["src/**/*", "package.json"],
    "win": {
      "target": ["nsis"],
      "icon": "build/icon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "Thunder ERP"
    }
  }
}
```

## 开发和构建

### 开发环境
```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev
```

### 生产构建
```bash
# 构建 Windows 安装包
npm run build:win
```

## 最佳实践

### 1. 安全性考虑
- 设置 `nodeIntegration: false`
- 使用 `contextIsolation: true`
- 通过预加载脚本提供安全的API

### 2. 性能优化
- 合理设置窗口大小和位置
- 优化远程URL加载速度
- 实现资源缓存策略

### 3. 错误处理
- 完善网络错误处理
- 添加用户友好的错误提示
- 实现自动重试机制

### 4. 用户体验
- 添加启动画面
- 实现自动更新
- 提供离线使用支持

## 常见问题

### 1. 远程URL加载失败
- 检查网络连接
- 确认URL是否正确
- 检查跨域问题

### 2. JS接口无法调用
- 确认接口名称与Android版本一致
- 检查预加载脚本是否正确加载
- 验证远程页面的接口调用

### 3. 打印功能不工作
- 检查网络打印机连接
- 确认打印命令格式正确
- 验证网络端口是否开放

通过这个指南，可以快速搭建一个功能完整的 Thunder ERP Windows 桌面应用，确保与现有 Android 版本的完全兼容。
