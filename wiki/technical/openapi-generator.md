# OpenAPI TypeScript 代码生成器

## 概述

OpenAPI TypeScript Request Generator CLI 是一个命令行工具，用于将 Swagger/OpenAPI 文档转换为 TypeScript 请求函数，支持命名空间隔离、版本管理和自定义配置。

## 功能特点

### 1. 命名空间隔离
- 根据API路径自动分组：`/api/abnormal-payment-order/query` → `abnormalPaymentOrder` 命名空间
- 版本化API分离：`/api/v3/order/additional-order` → `v3/order` 目录结构

### 2. 灵活配置
- 支持本地和远程 Swagger 文档
- 可配置的路径处理策略
- 自定义命名空间映射
- 模板自定义支持

### 3. 代码质量
- TypeScript 类型安全
- 自动生成类型定义
- 代码格式化和验证
- 错误处理和重试机制

## 安装和使用

### 安装
```bash
npm install -g openapi-ts-gen
```

### 基本使用
```bash
# 基本用法
openapi-ts-gen -i swagger.json -o ./src/api

# 使用配置文件
openapi-ts-gen -c openapi-ts-gen.config.js

# 指定远程URL
openapi-ts-gen -i https://api.example.com/swagger.json -o ./src/api
```

### 命令行选项
```
usage: openapi-ts-gen [options]

options:
  -c, --config <path>      配置文件路径
  -i, --input <path>       OpenAPI/Swagger文件路径
  -o, --output <dir>       输出目录，默认为./src/api
  -b, --base-url <url>     API基础URL，可选
  -p, --path-pattern <p>   路径匹配模式，默认为/api/
  --request-module <path>  请求模块路径，默认为@/utils/request
  -h, --help               显示帮助信息
  -v, --version            显示版本信息
```

## 配置文件

### 完整配置示例
```javascript
// openapi-ts-gen.config.js
module.exports = {
  // 输入配置
  input: './swagger.json',
  
  // Swagger获取配置（优先级高于input）
  swagger: {
    remoteUrl: 'https://api.example.com/swagger/doc.json',
    localPath: './src/api/swagger.json',
    preferRemote: true,
    shouldSaveLocal: true,
    timeout: 10000
  },
  
  // 输出配置
  output: './src/api',
  baseUrl: 'https://api.example.com',
  requestModule: '@/utils/request',
  
  // 路径处理配置
  pathHandling: {
    prefix: '/api',
    versionPatterns: [
      {
        pattern: '^/api/v([0-9]+)/',
        dirName: 'v$1-api',
        removeFromNamespace: false
      }
    ],
    namespaceMap: {
      '/api/abnormal-payment-order': 'payment/abnormalOrder',
    },
    segmentStrategy: 'second'
  },
  
  // 生成选项
  generation: {
    generateTypes: true,
    addComments: true,
    generateIndex: true,
  },
  
  // 自定义钩子
  hooks: {
    processParameter: (parameter) => parameter,
    processResponse: (response) => response,
    processFunctionName: (path, method, operation) => {
      // 自定义函数名生成逻辑
      return '';
    }
  }
};
```

### 配置说明

#### 输入配置
- `input`: 本地 Swagger 文件路径
- `swagger.remoteUrl`: 远程 Swagger URL
- `swagger.preferRemote`: 是否优先使用远程URL
- `swagger.timeout`: 请求超时时间

#### 路径处理
- `pathHandling.prefix`: API路径前缀
- `pathHandling.versionPatterns`: 版本匹配规则
- `pathHandling.namespaceMap`: 自定义命名空间映射
- `pathHandling.segmentStrategy`: 路径段分组策略

#### 生成选项
- `generation.generateTypes`: 是否生成类型定义
- `generation.addComments`: 是否添加注释
- `generation.generateIndex`: 是否生成索引文件

## 生成规则

### 命名空间确定逻辑
```
输入: /api/abnormal-payment-order/query
处理:
1. 移除前缀 "/api/"
2. 提取第二段作为主命名空间 "abnormal-payment-order"
3. 转换为驼峰命名: abnormalPaymentOrder
4. 确定输出目录: ./src/api/abnormalPaymentOrder/
5. 生成函数名: query

输入: /api/v3/order/additional-order
处理:
1. 检测版本号 "v3"
2. 确定目录: ./src/api/v3-api/order/
3. 命名空间: order
4. 生成函数名: additionalOrder
```

### 函数名生成规则
1. 优先使用 operationId（如果存在）
2. 根据 HTTP 方法和路径段生成函数名
3. 对于多级路径，使用父级路径段作为前缀避免冲突

## 生成示例

### 普通API生成
对于路径 `/api/abnormal-payment-order/add`：

```typescript
// src/api/abnormalPaymentOrder/index.ts
import request from '@/utils/request';
import type { AddAbnormalPaymentOrderReqDto, AbnormalPaymentOrderVO, Result } from './types';

/**
 * 添加异常支付订单
 * @param data - 请求参数
 */
export function add(data: AddAbnormalPaymentOrderReqDto) {
  return request<Result<AbnormalPaymentOrderVO>>('/api/abnormal-payment-order/add', {
    method: 'post',
    data,
  });
}
```

### 版本化API生成
对于路径 `/api/v3/order/additional-order`：

```typescript
// src/api/v3-api/order/index.ts
import request from '@/utils/request';
import type { AdditionalOrderReqDto, AdditionalOrderResultVO, Result } from './types';

/**
 * 附加订单
 * @param data - 请求参数
 */
export function additionalOrder(data: AdditionalOrderReqDto) {
  return request<Result<AdditionalOrderResultVO>>('/api/v3/order/additional-order', {
    method: 'post',
    data,
  });
}
```

### 类型定义生成
```typescript
// src/api/abnormalPaymentOrder/types.ts
/**
 * 自动生成的类型定义文件
 */

export namespace Req {
  export interface AddAbnormalPaymentOrderReqDto {
    orderId: string;
    amount: number;
    reason: string;
  }
}

export namespace Res {
  export interface AbnormalPaymentOrderVO {
    id: string;
    orderId: string;
    amount: number;
    status: string;
    createdAt: string;
  }
}

export interface Result<T> {
  code: number;
  message: string;
  data: T;
}
```

## 最佳实践

### 1. 项目集成
```json
// package.json
{
  "scripts": {
    "api:generate": "openapi-ts-gen -c openapi-ts-gen.config.js",
    "api:update": "npm run api:generate && npm run lint:fix"
  }
}
```

### 2. CI/CD 集成
```yaml
# .github/workflows/api-update.yml
name: Update API
on:
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点更新
  workflow_dispatch:

jobs:
  update-api:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
      - name: Generate API
        run: npm run api:generate
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v4
        with:
          title: 'chore: update API definitions'
          body: 'Auto-generated API updates from Swagger documentation'
```

### 3. 错误处理
```typescript
// 在生成的API中添加错误处理
export async function addWithErrorHandling(data: AddReqDto) {
  try {
    return await add(data);
  } catch (error) {
    console.error('API调用失败:', error);
    throw new Error(`添加操作失败: ${error.message}`);
  }
}
```

## 常见问题

### 1. 重复导入问题
**问题**: 生成的文件中存在重复的 request 导入
**解决**: 修改模板，统一使用默认导入方式

### 2. 类型引用错误
**问题**: types.ts 文件引用出错
**解决**: 确保类型文件正确生成并使用命名空间组织

### 3. 路径冲突
**问题**: 不同API生成相同的函数名
**解决**: 使用路径段作为前缀，或自定义函数名生成规则

### 4. 版本管理
**问题**: 不同版本API混合在一起
**解决**: 配置版本匹配规则，分离不同版本的API

## 扩展功能

### 1. 自定义模板
```javascript
// 自定义API函数模板
const customTemplate = `
import request from '{{requestModule}}';
{{#each imports}}
import type { {{this}} } from './types';
{{/each}}

{{#each functions}}
/**
 * {{description}}
 * @param {{#each parameters}}{{name}} - {{description}}{{/each}}
 */
export function {{name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) {
  return request<{{returnType}}>('{{path}}', {
    method: '{{method}}',
    {{#if hasData}}data,{{/if}}
    {{#if hasParams}}params,{{/if}}
  });
}
{{/each}}
`;
```

### 2. Mock数据生成
```javascript
// 配置Mock数据生成
module.exports = {
  // ... 其他配置
  mock: {
    enabled: true,
    outputDir: './src/mock',
    generateMockData: true
  }
};
```

通过这个工具，可以大大提高API客户端代码的生成效率，确保与后端API的同步，减少手动维护的工作量。
