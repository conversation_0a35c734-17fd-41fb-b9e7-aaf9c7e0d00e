# Thunder ERP 前端部署指南

## 部署策略概述

本指南介绍 Thunder ERP Client 项目的部署最佳实践，确保用户在重新发包期间不受影响，同时优化构建和部署效率。

## 🎯 推荐的部署策略

### 1. 蓝绿部署 (Blue-Green Deployment)

蓝绿部署是最推荐的部署策略，可以实现零停机时间的部署。

#### 工作原理
```bash
# 当前版本(蓝)正在服务用户
# 新版本(绿)在后台构建和测试
# 构建完成后一键切换，用户无感知
```

#### Nginx 配置示例
```nginx
upstream app_blue {
    server app-blue:3000;
}

upstream app_green {
    server app-green:3000;
}

# 通过修改这里实现蓝绿切换
upstream active_app {
    server app-blue:3000;  # 或 app-green:3000
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://active_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 部署脚本示例
```bash
#!/bin/bash
# blue-green-deploy.sh

CURRENT_COLOR=$(cat /etc/nginx/current_color)
NEW_COLOR="blue"

if [ "$CURRENT_COLOR" = "blue" ]; then
    NEW_COLOR="green"
fi

echo "当前版本: $CURRENT_COLOR, 部署版本: $NEW_COLOR"

# 1. 构建新版本
echo "构建新版本..."
pnpm run build:stage:fast

# 2. 部署到新环境
echo "部署到 $NEW_COLOR 环境..."
rsync -av dist/ /var/www/app-$NEW_COLOR/

# 3. 健康检查
echo "健康检查..."
if curl -f http://app-$NEW_COLOR:3000/health; then
    # 4. 切换流量
    echo "切换流量到 $NEW_COLOR..."
    sed -i "s/app-$CURRENT_COLOR/app-$NEW_COLOR/g" /etc/nginx/nginx.conf
    nginx -s reload
    echo "$NEW_COLOR" > /etc/nginx/current_color
    echo "部署成功！"
else
    echo "健康检查失败，回滚部署"
    exit 1
fi
```

### 2. 滚动更新 (Rolling Update)

适用于 Kubernetes 环境的部署策略。

```yaml
# kubernetes-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thunder-erp-frontend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0  # 确保始终有实例在服务
      maxSurge: 1        # 逐步替换实例
  template:
    spec:
      containers:
      - name: frontend
        image: thunder-erp-frontend:latest
        ports:
        - containerPort: 80
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 3. CDN缓存策略

合理的缓存策略是确保部署成功的关键。

```nginx
# 静态资源缓存策略
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
    # 带hash的文件永久缓存
    if ($uri ~ -[a-f0-9]{8,}\.(js|css)$) {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 其他静态资源短期缓存
    expires 7d;
    add_header Cache-Control "public";
}

# HTML文件不缓存，确保能立即获取新版本
location / {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
    try_files $uri $uri/ /index.html;
}
```

## 🚀 构建优化策略

### 快速构建配置

项目提供了优化的构建配置 `vite.config.fast-build.ts`：

#### 1. 简化chunk分离
```javascript
// 优化的分包策略
build: {
  rollupOptions: {
    output: {
      manualChunks: {
        'vue-vendor': ['vue', 'vue-router', 'pinia'],
        'ui-vendor': ['element-plus'],
        'vendor': ['axios', 'lodash-es']
      }
    }
  }
}
```

#### 2. 分包策略说明
- **vue-vendor**: Vue生态系统 (变化频率: 低)
- **ui-vendor**: Element Plus (变化频率: 中)
- **vendor**: 其他第三方库 (变化频率: 低)
- **index**: 应用代码 (变化频率: 高)

#### 3. 为什么不过度细分
- ❌ 构建时间变长 (主要问题)
- ❌ HTTP请求数量增加
- ❌ 首屏加载变慢
- ❌ 空chunk导致浪费处理时间

#### 4. 文件命名带hash
```
js/vue-vendor-a1b2c3d4.js    # Vue更新时才变化
js/ui-vendor-e5f6g7h8.js     # Element Plus更新时才变化
js/vendor-i9j0k1l2.js        # 其他库更新时才变化
js/index-m3n4o5p6.js         # 业务代码更新时才变化
```

### 构建命令
```bash
# 快速构建 (推荐)
pnpm run build:stage:fast

# 标准构建
pnpm run build:stage

# 生产构建
pnpm run build

# 构建分析
pnpm run build:analyzer
```

## 📋 部署检查清单

### 发布前检查
- [ ] 运行构建性能测试: `time pnpm run build:stage:fast`
- [ ] 检查bundle分析: `pnpm run build:analyzer`
- [ ] 验证hash变化范围符合预期
- [ ] 在预发环境测试完整功能
- [ ] 确认环境变量配置正确
- [ ] 检查API接口连通性

### 发布中监控
- [ ] 使用蓝绿部署或滚动更新
- [ ] 监控构建日志确保无错误
- [ ] 验证新实例健康状态
- [ ] 检查CDN缓存清理(如需要)
- [ ] 监控服务器资源使用情况

### 发布后验证
- [ ] 验证用户访问正常
- [ ] 检查浏览器控制台错误
- [ ] 监控性能指标
- [ ] 确认旧版本chunks可访问(渐进过渡)
- [ ] 验证关键功能正常工作
- [ ] 检查用户反馈

## 🔧 故障恢复

### 快速回滚

#### 蓝绿部署回滚
```bash
# 秒级恢复 - 切换回上一个稳定版本
PREVIOUS_COLOR=$(cat /etc/nginx/previous_color)
sed -i "s/app-green/app-$PREVIOUS_COLOR/g" /etc/nginx/nginx.conf
nginx -s reload
echo "已回滚到 $PREVIOUS_COLOR 版本"
```

#### Kubernetes 回滚
```bash
# 查看部署历史
kubectl rollout history deployment/thunder-erp-frontend

# 回滚到上一个版本
kubectl rollout undo deployment/thunder-erp-frontend

# 回滚到指定版本
kubectl rollout undo deployment/thunder-erp-frontend --to-revision=2
```

### 缓存问题处理

#### CDN缓存清理
```bash
# Cloudflare 缓存清理
curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache" \
  -H "Authorization: Bearer {api_token}" \
  -H "Content-Type: application/json" \
  --data '{"purge_everything":true}'

# 阿里云CDN缓存清理
aliyun cdn RefreshObjectCaches --ObjectPath https://your-domain.com/
```

#### 浏览器缓存处理
```javascript
// 在应用中检测版本更新
function checkForUpdates() {
  fetch('/version.json?' + Date.now())
    .then(response => response.json())
    .then(data => {
      if (data.version !== currentVersion) {
        // 提示用户刷新页面
        showUpdateNotification();
      }
    });
}
```

## 🔍 监控和告警

### 部署监控
```bash
# 监控脚本示例
#!/bin/bash
# monitor-deployment.sh

HEALTH_URL="https://your-domain.com/health"
EXPECTED_VERSION="v1.2.3"

while true; do
    RESPONSE=$(curl -s $HEALTH_URL)
    VERSION=$(echo $RESPONSE | jq -r '.version')
    
    if [ "$VERSION" = "$EXPECTED_VERSION" ]; then
        echo "✅ 部署成功，版本: $VERSION"
        break
    else
        echo "⏳ 等待部署完成，当前版本: $VERSION"
        sleep 10
    fi
done
```

### 性能监控
```javascript
// 前端性能监控
window.addEventListener('load', () => {
  const perfData = performance.getEntriesByType('navigation')[0];
  
  // 发送性能数据到监控系统
  fetch('/api/metrics', {
    method: 'POST',
    body: JSON.stringify({
      loadTime: perfData.loadEventEnd - perfData.fetchStart,
      domContentLoaded: perfData.domContentLoadedEventEnd - perfData.fetchStart,
      version: window.APP_VERSION
    })
  });
});
```

## 💡 最佳实践建议

### 1. 构建优化
- 优先使用 `build:stage:fast` 命令提升构建速度
- 定期分析bundle大小，避免不必要的依赖
- 使用合理的chunk分割策略

### 2. 部署策略
- 采用蓝绿部署避免用户感知到更新过程
- 设置合理的健康检查和超时时间
- 保持部署脚本的简单性和可维护性

### 3. 缓存策略
- HTML文件不缓存，确保能立即获取新版本
- 静态资源使用长期缓存配合文件hash
- 合理设置CDN缓存策略

### 4. 监控告警
- 设置部署成功/失败的通知机制
- 监控关键性能指标
- 建立快速回滚机制

### 5. 团队协作
- 建立标准的部署流程文档
- 设置部署权限和审批流程
- 定期进行部署演练

通过遵循这些最佳实践，可以确保 Thunder ERP Client 的稳定、高效部署，为用户提供无缝的服务体验。
