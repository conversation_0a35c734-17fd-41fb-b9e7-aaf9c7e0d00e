# 代码规范和最佳实践

## 概述

本文档定义了 Thunder ERP Client 项目的代码规范和最佳实践，旨在确保代码的一致性、可读性和可维护性。

## 命名规范

### 1. 文件和目录命名

#### 文件命名

- **Vue 组件**: PascalCase (如: `OpenTable.vue`, `ProductList.vue`)
- **TypeScript 文件**: camelCase (如: `userService.ts`, `apiClient.ts`)
- **VIPER-VC 架构文件**: `[pageName].[layerName].ts`
  - `openTable.presenter.ts`
  - `openTable.interactor.ts`
  - `openTable.converter.ts`
  - `openTable.viewModel.ts`
- **样式文件**: kebab-case (如: `main-layout.scss`, `button-styles.css`)
- **配置文件**: kebab-case (如: `vite.config.ts`, `tailwind.config.js`)

#### 目录命名

- **业务模块**: kebab-case (如: `open-table`, `product-order`)
- **通用目录**: kebab-case (如: `components`, `utils`, `stores`)

### 2. 变量和函数命名

#### 变量命名

```typescript
// ✅ 正确
const userName = 'john';
const isLoading = false;
const userList = [];
const API_BASE_URL = 'https://api.example.com';

// ❌ 错误
const user_name = 'john';
const IsLoading = false;
const userlist = [];
const apiBaseUrl = 'https://api.example.com'; // 常量应使用大写
```

#### 函数命名

```typescript
// ✅ 正确 - 动词开头，描述性强
function getUserInfo() {}
function handleSubmit() {}
function validateForm() {}
function calculateTotalPrice() {}

// ❌ 错误
function user() {}
function submit() {}
function check() {}
function total() {}
```

#### 类和接口命名

```typescript
// ✅ 正确
class UserService {}
class OpenTablePresenter {}
interface IUserInfo {}
interface IOpenTableViewModel {}
type UserStatus = 'active' | 'inactive';

// ❌ 错误
class userService {}
class openTablePresenter {}
interface UserInfo {} // 接口应以 I 开头
interface openTableViewModel {}
```

### 3. 组件命名

#### Vue 组件

```vue
<!-- ✅ 正确 -->
<template>
  <div class="open-table">
    <UserInfoCard :user="user" />
    <ProductList :products="products" />
  </div>
</template>

<script setup lang="ts">
// 组件名使用 PascalCase
defineOptions({
  name: 'OpenTable'
});
</script>
```

#### 组件属性

```typescript
// ✅ 正确
interface Props {
  userName: string;
  isVisible: boolean;
  productList: Product[];
  onSubmit: (data: FormData) => void;
}

// ❌ 错误
interface Props {
  user_name: string;
  IsVisible: boolean;
  productlist: Product[];
  onsubmit: (data: FormData) => void;
}
```

## 代码结构规范

### 1. VIPER-VC 架构规范

#### 文件组织

```
src/modules/[module]/views/[page]/
├── index.vue                 # View 层
├── viewModel.ts             # ViewModel 接口定义
├── presenter.ts             # Presenter 层
├── converter.ts             # 数据转换层
├── interactor.ts            # Interactor 层
└── components/              # 页面专用组件
```

#### 接口定义规范

```typescript
// ✅ 正确的 ViewModel 接口定义
export interface IOpenTableViewModel {
  state: IOpenTableState;
  computed: IOpenTableComputed;
  actions: IOpenTableActions;
}

export interface IOpenTableState {
  isLoading: Ref<boolean>;
  formData: Ref<FormData>;
  selectedProducts: Ref<Product[]>;
}

export interface IOpenTableActions {
  handleSubmit(): Promise<void>;
  addProduct(product: Product): void;
  validateForm(): boolean;
}
```

#### Presenter 实现规范

```typescript
// ✅ 正确的 Presenter 实现
export function useOpenTablePresenter(): IOpenTableViewModel {
  // 内部状态
  const _state = reactive<IOpenTableState>({
    isLoading: false,
    formData: {},
    selectedProducts: []
  });

  // 计算属性
  const computed = {
    totalAmount: computed(() => _state.selectedProducts.reduce((sum, p) => sum + p.price, 0))
  };

  // 操作方法
  const actions = {
    async handleSubmit() {
      _state.isLoading = true;
      try {
        // 业务逻辑
      } finally {
        _state.isLoading = false;
      }
    }
  };

  return {
    state: readonly(_state),
    computed,
    actions
  };
}
```

### 2. Vue 组件规范

#### 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入语句
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 2. 类型定义
interface Props {
  title: string;
  visible: boolean;
}

interface Emits {
  close: [];
  submit: [data: FormData];
}

// 3. 组件配置
defineOptions({
  name: 'ComponentName'
});

// 4. Props 和 Emits
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 5. 响应式数据
const isLoading = ref(false);
const formData = ref({});

// 6. 计算属性
const isFormValid = computed(() => {
  return formData.value.name && formData.value.email;
});

// 7. 方法定义
const handleSubmit = () => {
  emit('submit', formData.value);
};

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped>
/* 样式定义 */
</style>
```

#### 模板规范

```vue
<template>
  <!-- ✅ 正确 -->
  <div class="container">
    <h1 class="title">{{ title }}</h1>
    <button type="button" class="btn btn-primary" :disabled="isLoading" @click="handleSubmit">
      {{ isLoading ? '提交中...' : '提交' }}
    </button>
  </div>

  <!-- ❌ 错误 -->
  <div class="container">
    <h1 class="title">{{ title }}</h1>
    <button type="button" class="btn btn-primary" :disabled="isLoading" @click="handleSubmit">{{ isLoading ? '提交中...' : '提交' }}</button>
  </div>
</template>
```

### 3. TypeScript 规范

#### 类型定义

```typescript
// ✅ 正确
interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive';
  createdAt: Date;
}

type UserStatus = 'active' | 'inactive' | 'pending';

// 泛型使用
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// ❌ 错误
interface User {
  id: any; // 避免使用 any
  name: string;
  email: string;
  status: string; // 应使用联合类型
  createdAt: any; // 应使用具体类型
}
```

#### 函数定义

```typescript
// ✅ 正确
async function fetchUserData(userId: string): Promise<User> {
  const response = await api.get(`/users/${userId}`);
  return response.data;
}

function calculateTotal(items: CartItem[]): number {
  return items.reduce((sum, item) => sum + item.price * item.quantity, 0);
}

// ❌ 错误
async function fetchUserData(userId) {
  // 缺少类型注解
  const response = await api.get(`/users/${userId}`);
  return response.data;
}

function calculateTotal(items) {
  // 缺少类型注解
  return items.reduce((sum, item) => sum + item.price * item.quantity, 0);
}
```

## 代码质量规范

### 1. ESLint 配置

```javascript
// .eslintrc.js
module.exports = {
  extends: ['@vue/typescript/recommended', 'plugin:vue/vue3-recommended', 'prettier'],
  rules: {
    // Vue 规则
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/no-unused-vars': 'error',
    'vue/require-default-prop': 'off',

    // TypeScript 规则
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',

    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};
```

### 2. Prettier 配置

```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  trailingComma: 'es5',
  tabWidth: 2,
  useTabs: false,
  printWidth: 100,
  bracketSpacing: true,
  arrowParens: 'avoid',
  endOfLine: 'lf',
  vueIndentScriptAndStyle: false
};
```

### 3. 注释规范

#### 函数注释

````typescript
/**
 * 计算订单总金额
 * @param items 订单项列表
 * @param discountRate 折扣率 (0-1)
 * @returns 总金额（分为单位）
 * @example
 * ```typescript
 * const total = calculateOrderTotal([
 *   { price: 1000, quantity: 2 }
 * ], 0.1);
 * console.log(total); // 1800
 * ```
 */
function calculateOrderTotal(items: OrderItem[], discountRate: number = 0): number {
  const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  return Math.round(subtotal * (1 - discountRate));
}
````

#### 复杂逻辑注释

```typescript
// ✅ 正确
function processPackageSelection(packageData: PackageData): ProcessedPackage {
  // 1. 验证套餐配置的完整性
  if (!packageData.optionalGroups) {
    throw new Error('套餐缺少可选组配置');
  }

  // 2. 计算每个可选组的选择状态
  const processedGroups = packageData.optionalGroups.map(group => {
    // 统计已选择的商品数量
    const selectedCount = group.products.filter(p => p.isSelected).length;

    // 检查是否满足最小选择要求
    const isValid = selectedCount >= group.minSelect;

    return { ...group, selectedCount, isValid };
  });

  // 3. 计算套餐总价
  const totalPrice = calculatePackagePrice(packageData);

  return {
    ...packageData,
    optionalGroups: processedGroups,
    totalPrice,
    isValid: processedGroups.every(g => g.isValid)
  };
}
```

### 4. 错误处理规范

```typescript
// ✅ 正确的错误处理
async function submitOrder(orderData: OrderData): Promise<OrderResult> {
  try {
    // 1. 数据验证
    validateOrderData(orderData);

    // 2. 提交订单
    const response = await OrderApi.submit(orderData);

    // 3. 处理响应
    if (response.code !== 0) {
      throw new BusinessError(response.message, response.code);
    }

    return response.data;
  } catch (error) {
    // 4. 错误分类处理
    if (error instanceof ValidationError) {
      ElMessage.error(`数据验证失败: ${error.message}`);
    } else if (error instanceof NetworkError) {
      ElMessage.error('网络连接失败，请检查网络设置');
    } else {
      console.error('订单提交失败:', error);
      ElMessage.error('订单提交失败，请重试');
    }

    throw error;
  }
}

// 自定义错误类
class BusinessError extends Error {
  constructor(
    message: string,
    public code: string
  ) {
    super(message);
    this.name = 'BusinessError';
  }
}
```

## 性能优化规范

### 1. 响应式数据优化

```typescript
// ✅ 正确 - 合理使用响应式
const state = reactive({
  // 需要响应式的数据
  userInfo: null,
  isLoading: false,
  formData: {}
});

// 不需要响应式的数据使用普通变量
const API_CONFIG = {
  baseURL: 'https://api.example.com',
  timeout: 5000
};

// ❌ 错误 - 过度使用响应式
const state = reactive({
  userInfo: null,
  isLoading: false,
  formData: {},
  // 这些不需要响应式
  apiConfig: { baseURL: '...', timeout: 5000 },
  constants: { MAX_ITEMS: 100, DEFAULT_PAGE_SIZE: 20 }
});
```

### 2. 计算属性优化

```typescript
// ✅ 正确 - 使用计算属性缓存
const expensiveComputed = computed(() => {
  return items.value
    .filter(item => item.status === 'active')
    .map(item => processItem(item))
    .sort((a, b) => a.priority - b.priority);
});

// ❌ 错误 - 在模板中进行复杂计算
// <div v-for="item in items.filter(i => i.status === 'active').map(i => processItem(i)).sort((a, b) => a.priority - b.priority)">
```

### 3. 组件懒加载

```typescript
// ✅ 正确 - 路由懒加载
const routes = [
  {
    path: '/open-table',
    component: () => import('@/modules/room/views/OpenTable/index.vue')
  },
  {
    path: '/product-order',
    component: () => import('@/modules/production/views/ProductOrder/index.vue')
  }
];

// ✅ 正确 - 组件懒加载
const HeavyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'));
```

## 测试规范

### 1. 单元测试

```typescript
// openTable.presenter.test.ts
import { describe, it, expect, vi } from 'vitest';
import { useOpenTablePresenter } from './presenter';

describe('OpenTablePresenter', () => {
  it('应该正确初始化状态', () => {
    const presenter = useOpenTablePresenter();

    expect(presenter.state.isLoading).toBe(false);
    expect(presenter.state.selectedProducts).toEqual([]);
  });

  it('应该正确添加商品', () => {
    const presenter = useOpenTablePresenter();
    const product = { id: '1', name: '测试商品', price: 1000 };

    presenter.actions.addProduct(product);

    expect(presenter.state.selectedProducts).toContain(product);
  });

  it('应该正确计算总金额', () => {
    const presenter = useOpenTablePresenter();
    presenter.actions.addProduct({ id: '1', price: 1000 });
    presenter.actions.addProduct({ id: '2', price: 2000 });

    expect(presenter.computed.totalAmount.value).toBe(3000);
  });
});
```

### 2. 组件测试

```typescript
// OpenTable.test.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import OpenTable from './index.vue';

describe('OpenTable', () => {
  it('应该正确渲染', () => {
    const wrapper = mount(OpenTable);
    expect(wrapper.find('.open-table').exists()).toBe(true);
  });

  it('应该响应用户交互', async () => {
    const wrapper = mount(OpenTable);
    const button = wrapper.find('.submit-button');

    await button.trigger('click');

    expect(wrapper.emitted('submit')).toBeTruthy();
  });
});
```

## Dialog架构规范

### 1. 对话框组件设计原则

项目采用分层架构的对话框系统，基于 Element Plus 的 Dialog 组件进行扩展和封装。

#### 架构分层

```
业务组件 → DialogManager → DialogStore → DialogContainer → 具体对话框组件 → AppDialog → Element Plus Dialog
```

#### 核心组件

- **AppDialog.vue**: 基础对话框组件，封装 Element Plus Dialog
- **DialogContainer.vue**: 对话框容器，管理多个对话框实例
- **系统对话框**: AlertDialog、ConfirmDialog、MessageDialog
- **DialogManager**: 对话框管理器，提供统一调用接口

### 2. 对话框使用规范

#### 基本使用

```typescript
// 打开确认对话框
const dialogId = dialogManager.open({
  type: 'confirm',
  props: {
    title: '确认操作',
    message: '确定要执行此操作吗？',
    type: MessageType.WARNING
  },
  events: {
    confirm: () => {
      console.log('用户确认操作');
    },
    cancel: () => {
      console.log('用户取消操作');
    }
  }
});
```

#### 异步加载对话框

```typescript
// 注册异步对话框
{
  id: 'ComplexFormDialog',
  type: DialogType.PAGE,  // 异步加载
  config: {
    title: '复杂表单',
    width: '80%'
  },
  component: () => import('@/modules/forms/ComplexFormDialog.vue')
}

// 使用异步对话框
try {
  const result = await DialogManager.open('ComplexFormDialog', {
    initialData: { /* ... */ }
  });
} catch (error) {
  DialogManager.error('操作失败: ' + error.message);
}
```

### 3. 自定义对话框开发规范

#### 创建步骤

1. **定义类型** (src/types/dialog.ts)

```typescript
export interface INewDialogProps {
  title: string;
  // ... 其他属性
}
```

2. **创建组件** (src/components/Dialog/system/)

```vue
<template>
  <AppDialog v-model="visible" :title="title">
    <!-- 对话框内容 -->
  </AppDialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import AppDialog from '../core/AppDialog.vue';

interface Props {
  title: string;
  visible: boolean;
}

interface Emits {
  'update:visible': [value: boolean];
  confirm: [];
  cancel: [];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
</script>
```

3. **注册组件** (src/registry/dialog/)

```typescript
import { DialogRegistry } from '@/registry/dialog';

DialogRegistry.push({
  id: 'NewDialog',
  type: DialogType.GLOBAL,
  component: NewDialog
});
```

### 4. 对话框最佳实践

#### 性能优化

- 关键对话框使用同步加载 (DialogType.GLOBAL)
- 复杂对话框使用异步加载 (DialogType.PAGE)
- 正确设置 keepAlive 和 destroyOnClose 选项

#### 错误处理

- 完善异步加载失败的处理
- 提供友好的错误提示
- 实现自动重试机制

#### 用户体验

- 添加加载指示器
- 使用骨架屏
- 实现预加载策略

通过遵循这些代码规范和最佳实践，可以确保项目代码的高质量、一致性和可维护性。
