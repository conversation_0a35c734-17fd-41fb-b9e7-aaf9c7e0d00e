#!/bin/bash

# 快速启动Docker测试环境
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "🚀 快速启动Thunder ERP Docker测试环境"
echo "=================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 构建应用
echo "📦 构建优化版本..."
cd "$PROJECT_ROOT"
pnpm run build:stage:fast

# 启动Docker服务
echo "🐳 启动Docker服务..."
cd "$SCRIPT_DIR"
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "✅ 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 环境已启动！可以访问："
echo "  🌐 主服务:           http://localhost"
echo "  🔵 蓝色版本:         http://localhost:8081"
echo "  🟢 绿色版本:         http://localhost:8082"
echo "  📦 静态资源服务器:   http://localhost:8080"
echo ""
echo "📋 测试项目："
echo "  1. 在浏览器中访问上述地址"
echo "  2. 验证页面正常加载"
echo "  3. 检查开发者工具中的网络请求"
echo "  4. 验证缓存策略"
echo ""
echo "🛑 停止服务: cd scripts/docker-test && docker-compose down"
echo "📊 查看日志: cd scripts/docker-test && docker-compose logs -f" 