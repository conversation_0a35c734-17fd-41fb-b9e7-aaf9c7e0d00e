events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    
    # 上游服务器定义 - 蓝绿部署
    upstream app_blue {
        server app-blue:80;
    }
    
    upstream app_green {
        server app-green:80;
    }
    
    # 当前活跃服务器 (蓝绿切换通过修改这里实现)
    upstream active_app {
        server app-blue:80;  # 默认指向蓝色版本
    }
    
    # 主服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 根路径 - 代理到活跃应用
        location / {
            proxy_pass http://active_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # HTML文件不缓存，确保用户始终获取最新版本
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # 蓝色版本直接访问 (测试用)
    server {
        listen 8081;
        server_name localhost;
        
        location / {
            proxy_pass http://app-blue;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            add_header X-Version "Blue" always;
        }
    }
    
    # 绿色版本直接访问 (测试用)
    server {
        listen 8082;
        server_name localhost;
        
        location / {
            proxy_pass http://app-green;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            add_header X-Version "Green" always;
        }
    }
    
    # 静态资源服务器 (带完整缓存策略)
    server {
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # JS/CSS文件 - 长期缓存 (文件名带hash)
        location ~* \.(js|css)$ {
            # 检查是否为带hash的文件
            if ($uri ~ -[a-f0-9]{8,}\.(js|css)$) {
                expires 1y;
                add_header Cache-Control "public, immutable";
                break;
            }
            
            # 普通JS/CSS文件短期缓存
            expires 1d;
            add_header Cache-Control "public";
        }
        
        # 图片和字体文件
        location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 7d;
            add_header Cache-Control "public";
        }
        
        # HTML文件不缓存
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # 健康检查
        location /health {
            return 200 "OK";
            add_header Content-Type text/plain;
        }
    }
} 