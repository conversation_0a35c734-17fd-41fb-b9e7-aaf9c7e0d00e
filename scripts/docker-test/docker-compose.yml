version: '3.8'

services:
  # Nginx负载均衡器
  nginx-lb:
    image: nginx:alpine
    container_name: thunder-nginx-lb
    ports:
      - '80:80' # 主服务入口
      - '8080:8080' # 静态资源服务器
      - '8081:8081' # 蓝色版本直接访问
      - '8082:8082' # 绿色版本直接访问
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./logs:/var/log/nginx
      - ../../dist:/usr/share/nginx/html:ro # 静态资源
    depends_on:
      - app-blue
      - app-green
    networks:
      - thunder-network
    restart: unless-stopped

  # 蓝色版本应用
  app-blue:
    build:
      context: ../../
      dockerfile: scripts/docker-test/Dockerfile.app
    container_name: thunder-app-blue
    environment:
      - VERSION=blue
    volumes:
      - ./logs:/var/log/nginx
    networks:
      - thunder-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # 绿色版本应用
  app-green:
    build:
      context: ../../
      dockerfile: scripts/docker-test/Dockerfile.app
    container_name: thunder-app-green
    environment:
      - VERSION=green
    volumes:
      - ./logs:/var/log/nginx
    networks:
      - thunder-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  thunder-network:
    driver: bridge

volumes:
  nginx-logs:
