server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # JS/CSS文件 - 根据文件名hash优化缓存
    location ~* \.(js|css)$ {
        # 带hash的文件长期缓存
        if ($uri ~ -[a-f0-9]{8,}\.(js|css)$) {
            expires 1y;
            add_header Cache-Control "public, immutable";
            break;
        }
        
        # 普通文件短期缓存
        expires 1d;
        add_header Cache-Control "public";
    }

    # 静态资源
    location ~* \.(png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 7d;
        add_header Cache-Control "public";
    }

    # SPA路由处理
    location / {
        try_files $uri $uri/ /index.html;
        
        # HTML文件不缓存
        if ($uri ~ \.html$) {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }

    # 健康检查端点
    location /health {
        return 200 "healthy";
        add_header Content-Type text/plain;
    }

    # API代理 (如果需要)
    location /api/ {
        # 这里可以配置到后端API的代理
        return 404;
    }
} 