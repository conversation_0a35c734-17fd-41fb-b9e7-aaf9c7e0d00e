#!/bin/bash

# Docker化蓝绿部署测试脚本
set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}🐳 Thunder ERP Docker化部署测试${NC}"
echo "=================================="

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🧹 清理Docker环境...${NC}"
    cd "$SCRIPT_DIR"
    docker-compose down --remove-orphans
    docker system prune -f
    rm -rf logs
}

# 错误处理
trap cleanup EXIT

# 进入脚本目录
cd "$SCRIPT_DIR"

# 创建日志目录
mkdir -p logs

echo -e "${BLUE}📊 步骤1: 构建性能测试${NC}"
echo "-------------------------"

# 原有构建
echo -e "${YELLOW}测试原有构建性能...${NC}"
cd "$PROJECT_ROOT"
time pnpm run build:stage > /dev/null 2>&1

# 优化构建
echo -e "${YELLOW}测试优化构建性能...${NC}"
time pnpm run build:stage:fast > /dev/null 2>&1

echo -e "${GREEN}✅ 构建性能测试完成${NC}"

echo -e "\n${BLUE}🔵 步骤2: 构建蓝色版本${NC}"
echo "-------------------------"

# 构建蓝色版本
cd "$SCRIPT_DIR"
docker-compose build app-blue
echo -e "${GREEN}✅ 蓝色版本构建完成${NC}"

echo -e "\n${BLUE}🌐 步骤3: 启动初始环境${NC}"
echo "-------------------------"

# 启动服务
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo -e "${YELLOW}检查服务健康状态...${NC}"
docker-compose ps

# 等待健康检查通过
echo -e "${YELLOW}等待健康检查通过...${NC}"
for i in {1..30}; do
    if curl -f http://localhost/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务健康检查通过${NC}"
        break
    fi
    echo -n "."
    sleep 2
done

echo -e "\n${BLUE}📱 步骤4: 用户体验测试${NC}"
echo "-------------------------"

echo -e "${GREEN}🎯 测试环境已就绪！${NC}"
echo ""
echo "可用的测试端点："
echo "  🌐 主服务 (当前指向蓝色):  http://localhost"
echo "  🔵 蓝色版本直接访问:      http://localhost:8081"
echo "  🟢 绿色版本直接访问:      http://localhost:8082"
echo "  📦 静态资源服务器:        http://localhost:8080"
echo ""
echo "请在浏览器中测试以上端点，验证："
echo "  1. 页面正常加载"
echo "  2. 功能完整性"
echo "  3. 控制台无错误"
echo ""

read -p "按Enter继续绿色版本部署测试..."

echo -e "\n${BLUE}🟢 步骤5: 模拟代码更新和绿色版本部署${NC}"
echo "----------------------------------------"

# 模拟代码更改
cd "$PROJECT_ROOT"
echo -e "${YELLOW}模拟代码更改...${NC}"

# 创建标识文件，标记为绿色版本
echo "console.log('🟢 绿色版本已加载 - ' + new Date().toISOString());" >> src/main.ts

# 重新构建应用
echo -e "${YELLOW}重新构建应用...${NC}"
pnpm run build:stage:fast > /dev/null

# 重新构建绿色版本镜像
cd "$SCRIPT_DIR"
echo -e "${YELLOW}重新构建绿色版本镜像...${NC}"
docker-compose build app-green

# 重启绿色版本容器
echo -e "${YELLOW}部署绿色版本...${NC}"
docker-compose up -d app-green

# 等待绿色版本启动
echo -e "${YELLOW}等待绿色版本启动...${NC}"
sleep 5

# 检查绿色版本健康状态
for i in {1..15}; do
    if curl -f http://localhost:8082/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 绿色版本部署成功${NC}"
        break
    fi
    echo -n "."
    sleep 2
done

echo -e "\n${BLUE}🔄 步骤6: 蓝绿切换测试${NC}"
echo "-------------------------"

echo -e "${GREEN}🎯 绿色版本部署完成！${NC}"
echo ""
echo "现在可以测试蓝绿切换："
echo "  🔵 蓝色版本: http://localhost:8081 (旧版本)"
echo "  🟢 绿色版本: http://localhost:8082 (新版本，控制台会显示绿色版本日志)"
echo "  🌐 主服务:   http://localhost (当前还是蓝色，即将切换)"
echo ""

read -p "按Enter执行蓝绿切换..."

# 执行蓝绿切换
echo -e "${YELLOW}执行蓝绿切换...${NC}"

# 修改nginx配置，将active_app指向绿色版本
sed -i.bak 's/server app-blue:80;  # 默认指向蓝色版本/server app-green:80;  # 切换到绿色版本/' nginx.conf

# 重新加载nginx配置
docker exec thunder-nginx-lb nginx -s reload

echo -e "${GREEN}✅ 蓝绿切换完成！${NC}"
echo ""
echo "验证切换结果："
echo "  🌐 主服务: http://localhost (现在指向绿色版本)"
echo "  打开浏览器控制台，应该能看到 '🟢 绿色版本已加载' 的日志"

echo -e "\n${BLUE}🧪 步骤7: 缓存策略测试${NC}"
echo "-------------------------"

echo -e "${YELLOW}测试缓存策略...${NC}"

# 测试各种缓存头
echo "测试静态资源缓存头："
curl -I http://localhost:8080/js/ 2>/dev/null | grep -i cache-control || echo "无缓存头"

echo -e "\n${BLUE}📊 步骤8: 性能和监控${NC}"
echo "-------------------------"

# 显示Docker资源使用
echo -e "${YELLOW}Docker容器资源使用:${NC}"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

# 显示nginx访问日志
echo -e "\n${YELLOW}Nginx访问日志 (最近10条):${NC}"
if [ -f logs/access.log ]; then
    tail -10 logs/access.log
else
    echo "日志文件还未生成"
fi

echo -e "\n${GREEN}🎉 Docker化部署测试完成！${NC}"
echo ""
echo "总结："
echo "  ✅ 构建性能已优化"
echo "  ✅ 蓝绿部署机制正常"
echo "  ✅ 缓存策略生效"
echo "  ✅ 零停机时间切换"
echo "  ✅ 容器健康检查正常"
echo ""
echo "清理提示："
echo "  - 容器将在脚本结束时自动清理"
echo "  - 如需保持运行，请 Ctrl+C 取消清理"
echo ""

# 还原代码更改
cd "$PROJECT_ROOT"
git checkout -- src/main.ts

# 还原nginx配置
cd "$SCRIPT_DIR"
if [ -f nginx.conf.bak ]; then
    mv nginx.conf.bak nginx.conf
fi

read -p "按Enter开始清理，或Ctrl+C取消清理保持环境运行..."

echo -e "${YELLOW}开始清理...${NC}" 