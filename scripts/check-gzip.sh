#!/bin/bash

# Thunder ERP Gzip压缩验证脚本
# 使用方法: ./scripts/check-gzip.sh https://你的域名

if [ -z "$1" ]; then
    echo "❌ 请提供域名"
    echo "用法: $0 https://你的域名"
    exit 1
fi

DOMAIN=$1
echo "🔍 检查 $DOMAIN 的Gzip压缩状态..."
echo "================================"

# 基于实际构建输出的文件名
FILES=(
    "/dist/element-plus-lvWgvwwa.js"
    "/dist/vendor-m0qtMtAL.js"
    "/dist/index-BVQszHry.js"
    "/dist/css/index-C9es1gtF.css"
)

for FILE in "${FILES[@]}"; do
    echo ""
    echo "📄 检查文件: $FILE"
    echo "-----------------------------------"
    
    URL="${DOMAIN}${FILE}"
    
    # 检查是否返回gzip头
    RESPONSE=$(curl -H "Accept-Encoding: gzip" -I -s "$URL" 2>/dev/null)
    
    if echo "$RESPONSE" | grep -qi "content-encoding: gzip"; then
        echo "✅ Gzip已启用"
        
        # 计算压缩效果
        if command -v bc >/dev/null 2>&1; then
            COMPRESSED=$(curl -H "Accept-Encoding: gzip" -s "$URL" 2>/dev/null | wc -c)
            ORIGINAL=$(curl -s "$URL" 2>/dev/null | wc -c)
            
            if [ "$ORIGINAL" -gt 0 ]; then
                RATIO=$(echo "scale=1; (1 - $COMPRESSED/$ORIGINAL) * 100" | bc)
                echo "📊 原始: $(numfmt --to=iec $ORIGINAL) → 压缩: $(numfmt --to=iec $COMPRESSED) (节省${RATIO}%)"
            fi
        fi
    elif echo "$RESPONSE" | grep -qi "200 OK"; then
        echo "❌ Gzip未启用 (文件存在但未压缩)"
    else
        echo "⚠️  文件可能不存在或无法访问"
    fi
done

echo ""
echo "🎯 快速浏览器验证:"
echo "1. 打开浏览器开发者工具 (F12)"
echo "2. 切换到Network标签"
echo "3. 刷新页面"
echo "4. 查看JS文件的Response Headers是否包含 'content-encoding: gzip'" 