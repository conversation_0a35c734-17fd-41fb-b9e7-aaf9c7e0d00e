import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// 获取环境变量
const env = process.env.BUILD_ENV || process.env.NODE_ENV || 'development';
const envSuffix = env === 'production' ? 'prod' : env === 'stage' ? 'stage' : env;
const isDev = env === 'development';

// 开发模式下跳过版本信息生成
if (isDev) {
  console.log('Development mode: skipping version info generation.');
  process.exit(0);
}

// 检查git命令是否可用
function isGitAvailable() {
  try {
    execSync('git --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    console.warn('Warning: Git command not available in this environment. Version info will be set to default values.');
    return false;
  }
}

// 获取Git信息的函数
function getGitInfo() {
  // 如果git命令不可用，直接返回默认值
  if (!isGitAvailable()) {
    return {
      commitHash: 'unknown',
      commitMessage: 'unknown',
      branchName: 'unknown',
      author: {
        name: 'unknown',
        email: 'unknown'
      },
      commitDate: 'unknown',
      tagName: 'unknown'
    };
  }

  try {
    const commitHash = execSync('git rev-parse HEAD').toString().trim();
    const commitMessage = execSync('git log -1 --pretty=%B').toString().trim();
    const branchName = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
    const authorName = execSync('git log -1 --pretty=%an').toString().trim();
    const authorEmail = execSync('git log -1 --pretty=%ae').toString().trim();
    const commitDate = execSync('git log -1 --pretty=%cd --date=iso').toString().trim();
    const tagName = execSync('git describe --tags --abbrev=0 2>/dev/null || echo ""').toString().trim();

    return {
      commitHash,
      commitMessage,
      branchName,
      author: {
        name: authorName,
        email: authorEmail
      },
      commitDate,
      tagName
    };
  } catch (error) {
    console.warn('Warning: Unable to get complete git info:', error.message);
    return {
      commitHash: 'unknown',
      commitMessage: 'unknown',
      branchName: 'unknown',
      author: {
        name: 'unknown',
        email: 'unknown'
      },
      commitDate: 'unknown',
      tagName: 'unknown'
    };
  }
}

try {
  // 在 ES 模块中获取当前文件的目录
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = dirname(__filename);

  const versionConfigPath = resolve(__dirname, '../version.config.json');
  const publicDirPath = resolve(__dirname, '../public');
  const versionFilePath = resolve(publicDirPath, `version.${envSuffix}.json`);

  // 读取版本配置文件
  let versionConfig;
  try {
    versionConfig = JSON.parse(readFileSync(versionConfigPath, 'utf-8'));
  } catch (error) {
    console.warn('Warning: version.config.json not found or invalid, using default version');
    versionConfig = {
      version: '1.0.0',
      description: '初始版本'
    };
  }

  // 获取当前发布时间戳（中国时区，UTC+8）
  function getChinaTimeString() {
    const now = new Date();
    const utcTime = now.getTime();
    const chinaTime = new Date(utcTime + 8 * 60 * 60 * 1000);
    return chinaTime.toISOString().replace('Z', '+08:00');
  }
  const releaseTimestamp = getChinaTimeString();

  // 获取Git信息
  const gitInfo = getGitInfo();

  // 构建版本信息对象
  const versionInfo = {
    version: versionConfig.version,
    versionCode: versionConfig.versionCode,
    description: versionConfig.description,
    releaseTimestamp,
    git: gitInfo,
    buildEnv: env
  };

  // 确保 public 目录存在
  if (!existsSync(publicDirPath)) {
    mkdirSync(publicDirPath, { recursive: true });
  }

  // 写入 version.{env}.json
  writeFileSync(versionFilePath, JSON.stringify(versionInfo, null, 2));

  console.log(`Successfully generated ${versionFilePath}`);
  console.log(JSON.stringify(versionInfo, null, 2));

  // 如果存在 Git tag，检查是否与配置版本号匹配
  if (gitInfo.tagName && gitInfo.tagName !== 'unknown') {
    const tagVersion = gitInfo.tagName.replace(/^v/, ''); // 移除可能的 'v' 前缀
    if (tagVersion !== versionConfig.version) {
      console.warn(`Warning: Git tag version (${tagVersion}) does not match config version (${versionConfig.version})`);
    }
  }
} catch (error) {
  console.error('Error generating version.json:', error);
  process.exit(1);
}
