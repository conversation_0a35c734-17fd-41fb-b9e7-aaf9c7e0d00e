# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 依赖目录
node_modules

public/version.*


# 构建输出
dist
dist-ssr
dist-client-pad

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器目录和文件
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 环境变量文件
.env
.env.local
.env.*.local
.env.development

# TypeScript 生成的文件
*.tsbuildinfo

# Vue 开发服务器缓存
.vite

# 测试覆盖率报告
coverage
*.local

# Cypress 端到端测试
/cypress/videos/
/cypress/screenshots/

# 可选: 如果您使用 yarn 的零安装特性
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

*storybook.log
.temp
dist.zip

.gradle
!/thunder-erp-electron/build/logo.png
/android/*/build
android/.gradle/
*.bin
*.lock

*.apk
*.temp.sh