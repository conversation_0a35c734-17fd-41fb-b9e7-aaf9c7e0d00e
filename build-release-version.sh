#!/bin/bash

# Thunder ERP 版本构建脚本
# 用于基于指定的git tag进行构建

set -e

# 检查参数
if [ -z "$1" ]; then
    echo "错误: 请提供版本号参数"
    echo "用法: $0 <版本号/git tag> [构建类型]"
    echo "示例: $0 v1.0.6-test vue-prod"
    echo "示例: $0 v1.0.6-test pad-stage"
    exit 1
fi

VERSION=$1
BUILD_TYPE=${2:-vue-builder}  # 默认为vue构建

echo "开始构建 Thunder ERP 版本: $VERSION"
echo "构建类型: $BUILD_TYPE"

# 设置环境变量 - 启用版本路径部署
export SPUG_RELEASE_1=$VERSION
echo "已设置版本环境变量: SPUG_RELEASE_1=$VERSION"
echo "版本化构建将输出到: dist_$VERSION/"

# 可用的构建类型
case $BUILD_TYPE in
    "vue" | "vue-builder")
        echo "执行 Vue 主应用构建..."
        docker-compose -f docker-compose-release.yml up --build vue-builder
        ;;
    "vue-stage" | "vue-builder-stage")
        echo "执行 Vue 主应用构建 (stage)..."
        docker-compose -f docker-compose-release.yml up --build vue-builder-stage
        ;;
    "vue-prod" | "vue-builder-prod")
        echo "执行 Vue 主应用构建 (production)..."
        docker-compose -f docker-compose-release.yml up --build vue-builder-prod
        ;;
    "pad" | "pad-builder")
        echo "执行 Pad 应用构建..."
        docker-compose -f docker-compose-release.yml up --build pad-builder
        ;;
    "pad-stage" | "pad-builder-stage")
        echo "执行 Pad 应用构建 (stage)..."
        docker-compose -f docker-compose-release.yml up --build pad-builder-stage
        ;;
    "pad-prod" | "pad-builder-prod")
        echo "执行 Pad 应用构建 (production)..."
        docker-compose -f docker-compose-release.yml up --build pad-builder-prod
        ;;
    "all")
        echo "执行所有构建任务..."
        docker-compose -f docker-compose-release.yml up --build
        ;;
    *)
        echo "错误: 不支持的构建类型 '$BUILD_TYPE'"
        echo "支持的构建类型:"
        echo "  vue | vue-builder        - Vue 主应用构建"
        echo "  vue-stage                - Vue 主应用构建 (stage)"
        echo "  vue-prod                 - Vue 主应用构建 (production)"
        echo "  pad | pad-builder        - Pad 应用构建"
        echo "  pad-stage                - Pad 应用构建 (stage)"
        echo "  pad-prod                 - Pad 应用构建 (production)"
        echo "  all                      - 所有构建任务"
        exit 1
        ;;
esac

echo "构建完成！版本: $VERSION" 