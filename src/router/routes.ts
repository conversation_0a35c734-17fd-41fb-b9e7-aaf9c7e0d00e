import { AppRouteRecordRaw } from './types';
import { Router } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import NProgress from 'nprogress';
import '@/styles/nprogress.css';
// 导入通知路由模块
import notificationRoutes from './modules/notification';

// NProgress配置
NProgress.configure({
  easing: 'ease',
  speed: 500,
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.2
});

/**
 * 应用的所有路由配置
 */
export const routes: AppRouteRecordRaw[] = [
  // 基础路由
  {
    path: '/auth',
    name: 'auth',
    meta: {
      componentName: 'AuthView',
      title: '认证',
      requiresAuth: false
    },
    component: () => import('@/views/AuthView.vue')
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      componentName: 'LoginView',
      title: '登录',
      requiresAuth: false
    },
    component: () => import('@/views/Login/index.vue')
  },

  {
    path: '/',
    name: 'home',
    meta: {
      componentName: 'GlobalLayout',
      title: '首页',
      requiresAuth: true
    },
    component: () => import('@/layouts/GlobalLayout.vue'),
    redirect: '/room',
    children: [
      // 包厢管理
      {
        path: '/room',
        component: () => import('@/layouts/BasicLayout.vue'),
        children: [
          {
            path: '',
            redirect: '/room/realtimetable'
          },
          {
            path: 'realtimetable',
            name: 'RealTimeTable',
            component: () => import('@/modules/room/views/RealTimeTable/index.vue'),
            meta: { title: '实时包厢', keepAlive: true }
          },
          {
            path: 'opentable',
            name: 'room-opentable',
            meta: {
              showNav: true,
              componentName: 'OpenTable'
            },
            component: () => import('@/modules/room/views/OpenTable/index.vue')
          },
          {
            path: 'booking',
            name: 'room-booking',
            component: () => import('@/modules/room/views/BookTable/index.vue'),
            meta: { title: '预定管理' }
          },
          {
            path: 'booking-detail',
            name: 'booking-detail',
            meta: {
              title: '预定详情',
              showNav: true,
              componentName: 'BookingDetail'
            },
            component: () => import('@/modules/room/views/BookDetial/BookingDetail.vue')
          }
        ]
      },

      // 订单管理
      {
        path: '/order',
        redirect: '/order/room-list',
        children: [
          {
            path: 'room-list',
            name: 'order-room-list',
            meta: {
              title: '包厢订单',
              keepAlive: true,
              showNav: true,
              componentName: 'TableOrder'
            },
            component: () => import('@/modules/order/views/TableOrder/index.vue')
          },
          {
            path: 'room-detail',
            name: 'order-room-detail',
            meta: {
              title: '消费明细',
              showNav: true,
              componentName: 'RoomOrderDetail'
            },
            component: () => import('@/modules/order/views/RoomOrderDetail/index.vue')
          }
        ]
      },

      // 商品管理
      {
        path: 'production',
        name: 'production',
        redirect: '/production/index',
        meta: {
          title: '商品管理'
        },
        children: [
          {
            path: 'index',
            name: 'production-index',
            meta: {
              title: '商品管理',
              componentName: 'ProductIndex'
            },
            component: () => import('@/modules/production/views/ProductIndex.vue')
          },
          {
            path: 'refunddetail',
            name: 'production-refunddetail',
            meta: {
              title: '退单详情',
              showNav: true,
              componentName: 'RefundDetail'
            },
            component: () => import('@/modules/production/views/refunddetail/index.vue')
          },
          {
            path: 'addorder',
            name: 'production-addorder',
            meta: {
              title: '添加商品',
              componentName: 'OpenTableProduct'
            },
            component: () => import('@/modules/production/views/OpenTableProduct/index.vue')
          },
          // 商品沽清管理
          {
            path: 'soldout',
            name: 'production-soldout',
            meta: {
              title: '沽清商品',
              componentName: 'SoldOutView'
            },
            component: () => import('@/modules/production/views/SoldOutView.vue')
          }
        ]
      },

      // 交班管理
      {
        path: 'shift',
        name: 'shift',
        redirect: '/shift/handover',
        meta: {
          title: '交班管理'
        },
        children: [
          {
            path: 'handover',
            name: 'shift-handover',
            meta: {
              title: '交班',
              componentName: 'EmployeeShift',
              keepAlive: true
            },
            component: () => import('@/modules/shift/views/employeeShift/index.vue')
          },
          {
            path: 'history',
            name: 'shift-history',
            meta: {
              title: '交班历史',
              componentName: 'EmployeeShiftHistory',
              keepAlive: true
            },
            component: () => import('@/modules/shift/views/employeeShiftList/index.vue')
          },
          {
            path: 'history/detail/:id',
            name: 'shift-history-detail',
            meta: {
              title: '交班历史详情',
              componentName: 'EmployeeShift',
              showNav: true
            },
            component: () => import('@/modules/shift/views/employeeShift/index.vue'),
            props: (route: any) => ({
              shiftId: route.params.id,
              handNo: route.query.handNo || '',
              mode: 'history'
            })
          }
        ]
      },

      // 会员管理
      {
        path: 'member',
        name: 'member',
        redirect: '/member/index',
        meta: {
          title: '会员管理',
          componentName: 'MemberLayout'
        },
        component: () => import('@/modules/member/views/MemberLayout.vue'),
        children: [
          {
            path: 'index',
            name: 'member-index',
            meta: {
              title: '会员管理',
              componentName: 'MemberIndex'
            },
            component: () => import('@/modules/member/views/MemberIndex.vue')
          },
          {
            path: 'transfer',
            name: 'member-transfer',
            meta: {
              title: '会员转账',
              componentName: 'MemberTransfer'
            },
            component: () => import('@/modules/member/views/MemberTransfer.vue')
          },
          {
            path: 'payment',
            name: 'member-payment',
            meta: {
              title: '会员充值',
              componentName: 'MemberPayment'
            },
            component: () => import('@/modules/member/views/memberPayment/index.vue')
          },
          {
            path: 'detail/:id',
            name: 'member-detail',
            meta: {
              title: '会员详情',
              componentName: 'MemberDetail'
            },
            component: () => import('@/modules/member/views/memberDetail/index.vue')
          },
          {
            path: 'operation-list',
            name: 'member-operation-list',
            meta: {
              title: '会员办理记录',
              componentName: 'MemberOptrationList'
            },
            component: () => import('@/modules/member/views/MemberOperationList/index.vue')
          },
          {
            path: 'transfer-record',
            name: 'member-transfer-record',
            meta: {
              title: '会员转账记录',
              componentName: 'MemberTransferRecord'
            },
            component: () => import('@/modules/member/views/transfer-records/index.vue')
          },
          {
            path: 'times-card',
            name: 'times-card',
            meta: {
              title: '次卡管理',
              componentName: 'TimesCard'
            },
            component: () => import('@/modules/member/views/TimesCard.vue')
          },
          {
            path: 'times-card/open',
            name: 'times-card-open',
            meta: {
              title: '次卡开卡',
              componentName: 'TimesCardOpen'
            },
            component: () => import('@/modules/member/views/TimesCardOpen.vue')
          },
          {
            path: 'points-exchange',
            name: 'points-exchange',
            meta: {
              title: '积分兑换',
              componentName: 'PointsExchange'
            },
            component: () => import('@/modules/member/views/PointsExchange.vue')
          }
        ]
      },

      // 存酒管理 - 作为普通路由整合到主配置
      {
        path: 'wine',
        name: 'wine',
        redirect: '/wine/index',
        meta: {
          title: '存酒管理'
        },
        children: [
          {
            path: 'index',
            name: 'wine-index',
            meta: {
              title: '存酒管理',
              componentName: 'WineIndex'
            },
            component: () => import('@/modules/wine/views/index.vue')
          }
        ]
      },

      // 财务报表
      {
        path: 'finance',
        name: 'finance',
        redirect: '/finance/report',
        meta: {
          title: '财务报表'
        },
        children: [
          {
            path: 'report',
            name: 'finance-report',
            meta: {
              title: '财务报表',
              componentName: 'ReportIndex'
            },
            component: () => import('@/modules/finance/views/ReportIndex.vue')
          }
        ]
      },

      // 库存报表
      {
        path: 'inventory',
        name: 'inventory',
        redirect: '/inventory/index',
        meta: {
          title: '库存报表'
        },
        children: [
          {
            path: 'index',
            name: 'inventory-index',
            meta: {
              title: '库存报表',
              componentName: 'InventoryIndex'
            },
            component: () => import('@/modules/inventory/views/InventoryIndex.vue')
          }
        ]
      },

      // 系统设置
      {
        path: 'settings',
        name: 'settings',
        meta: {
          title: '系统设置',
          keepAlive: true,
          componentName: 'Settings'
        },
        component: () => import('@/modules/settings/views/Settings.vue')
      },

      // 通知管理 - 添加通知模块
      {
        path: '/notifications',
        component: () => import('@/layouts/BasicLayout.vue'),
        redirect: '/notifications/list',
        meta: {
          title: '呼叫通知',
          componentName: 'NotificationsLayout'
        },
        children: [
          {
            path: 'list',
            name: 'notifications-list',
            meta: {
              title: '通知列表',
              componentName: 'NotificationListPage'
            },
            component: () => import('@/modules/notification/list/index.vue')
          }
        ]
      }
    ]
  },
  // 添加产品模块路由
  {
    path: '/product',
    component: () => import('@/layouts/BasicLayout.vue'),
    children: [
      {
        path: '',
        redirect: '/product/list'
      },
      {
        path: 'list',
        name: 'ProductList',
        meta: {
          componentName: 'ProductList',
          title: '产品列表',
          requiresAuth: true
        },
        component: () => import('@/modules/product/views/ProductList.vue')
      }
    ]
  },
  // 404路由 - 使用重定向而不是不存在的组件
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    redirect: '/'
  }
];

/**
 * 获取所有路由
 * @returns 所有路由配置
 */
export function getRoutes(): AppRouteRecordRaw[] {
  return routes;
}

/**
 * 根据角色获取路由
 * @param roles 角色列表
 * @returns 过滤后的路由配置
 */
export function getRoutesByRoles(roles: string[]): AppRouteRecordRaw[] {
  // 如果没有角色限制，返回所有路由
  if (!roles || roles.length === 0) {
    return routes;
  }

  // 过滤路由
  const filterRoutes = (routes: AppRouteRecordRaw[]): AppRouteRecordRaw[] => {
    return routes.filter(route => {
      // 检查当前路由是否有角色限制
      if (route.meta?.roles) {
        // 检查用户角色是否有权限访问
        const hasRole = roles.some(role => route.meta?.roles?.includes(role));
        if (!hasRole) {
          return false;
        }
      }

      // 递归处理子路由
      if (route.children) {
        route.children = filterRoutes(route.children);
      }

      return true;
    });
  };

  return filterRoutes(routes);
}

/**
 * 根据权限获取路由
 * @param permissions 权限列表
 * @returns 过滤后的路由配置
 */
export function getRoutesByPermissions(permissions: string[]): AppRouteRecordRaw[] {
  // 如果没有权限限制，返回所有路由
  if (!permissions || permissions.length === 0) {
    return routes;
  }

  // 过滤路由
  const filterRoutes = (routes: AppRouteRecordRaw[]): AppRouteRecordRaw[] => {
    return routes.filter(route => {
      // 检查当前路由是否有权限限制
      if (route.meta?.permissions) {
        // 检查用户权限是否有权限访问
        const hasPermission = permissions.some(permission => route.meta?.permissions?.includes(permission));
        if (!hasPermission) {
          return false;
        }
      }

      // 递归处理子路由
      if (route.children) {
        route.children = filterRoutes(route.children);
      }

      return true;
    });
  };

  return filterRoutes(routes);
}

/**
 * 设置路由守卫
 * @param router 路由实例
 */
export function setupRouterGuards(router: Router): void {
  router.beforeEach(async (to, from, next) => {
    // 开始加载进度条
    NProgress.start();

    console.log('路由变化:', {
      from: { path: from.path, name: from.name },
      to: { path: to.path, name: to.name }
    });

    const userStore = useUserStore();
    await userStore.initUserState();
    const deviceStore = useDeviceStore();
    const venueStore = useVenueStore();

    const token = userStore.token;
    console.log('----token:', token);

    // 白名单路由，不需要验证token
    const whiteList = ['/login', '/auth'];

    // 第一个条件检查 - venue相关
    const condition1 = !venueStore.venueId && to.path !== '/auth' && !whiteList.includes(to.path);
    if (condition1) {
      // 如果没有venueId，且不是前往auth页面，重定向到auth页面
      console.log('路由守卫执行：跳转到 /auth (无venue)');
      next('/auth');
      return;
    }

    // 新增：检查IP不一致的情况
    const ipMismatch = venueStore.venueId && deviceStore.cashierMachine?.grantIp && deviceStore.cashierMachine.grantIp !== deviceStore.ipAddress;

    if (ipMismatch && to.path !== '/auth' && !whiteList.includes(to.path)) {
      console.log('路由守卫执行：跳转到 /auth (IP不一致)');
      next('/auth');
      return;
    }

    // 第二个条件检查 - token相关
    const condition2 = !token && !whiteList.includes(to.path);
    if (condition2) {
      // 如果没有token且不是白名单路由，重定向到登录页
      console.log('路由守卫执行：跳转到 /login');
      next('/login');
      return;
    }

    next();
  });

  // 路由加载完成后
  router.afterEach(() => {
    // 完成进度条
    NProgress.done();
  });

  // 路由加载出错时
  router.onError(() => {
    // 出错时完成进度条
    NProgress.done();
  });
}
