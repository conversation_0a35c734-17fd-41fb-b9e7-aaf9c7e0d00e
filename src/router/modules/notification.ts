import type { RouteRecordRaw } from 'vue-router';
import Layout from '@/layout/index.vue'; // 假设你有主布局组件

/**
 * 通知模块路由
 */
const notificationRoutes: Array<RouteRecordRaw> = [
  {
    path: '/notifications',
    component: Layout,
    redirect: '/notifications/list',
    name: 'NotificationsModule',
    meta: {
      title: '呼叫通知'
    },
    children: [
      {
        path: 'list',
        name: 'NotificationListPage',
        component: () => import('@/modules/notification/list/index.vue'),
        meta: {
          title: '通知列表'
        }
      }
    ]
  }
];

export default notificationRoutes;
