import { receiptBuilder } from '@/domains/prints/receipt-builder';
import { escPosCommandGenerator } from '@/infrastructure/printing/escPosCommandGenerator';
import { printerService, configurePrinter } from '@/infrastructure/printing/printerService';
import { toast } from '@/components/customer/toast';
import { OrderDetailData } from '@/domains/prints/order-detail/models/order-detail-data';
import { ProductionOrderData, ProductionOrderItem } from '@/domains/prints/production-order/models/production-order-data';
import { ShiftReportData } from '@/domains/prints/shift-report/models/shift-report-data';
import { SessionOrderData } from '@/domains/prints/session-order/models/session-order-data';
import { useDeviceStore } from '@/stores/deviceStore';
import { postApiPrintRecordOpenTableCreate, postApiPrintRecordProductOutCreate, postApiPrintRecordCheckoutCreate, postApiPrintRecordShiftChangeCreate, postApiPrintRecordRoomExtensionCreate, postApiPrintRecordCheckoutPayBillIds } from '@/api/autoGenerated';
import type { CreateOpenTablePrintRecordReqDto, CreateProductOutPrintRecordReqDto, CreateCheckoutPrintRecordReqDto, CreateShiftChangePrintRecordReq, CreateRoomExtensionPrintRecordReqDto, GetCheckoutPrintRecordsByPayBillIdsReqDto, CheckoutPrintRecordVO } from '@/api/autoGenerated';
import { formatDateTime } from '@/utils/dateUtils';
import { useUserStore } from '@/stores/userStore';
import { useVenueStore } from '@/stores/venueStore';

/**
 * 打印服务配置常量
 */
const PRINTING_CONFIG = {
  DEFAULT_PORT: 9100,
  SIMULATE_PRINT: false,
  DEFAULT_HOST: 'localhost',
  UNKNOWN_DEVICE: '未知设备'
} as const;

/**
 * 打印消息常量
 */
const PRINT_MESSAGES = {
  SUCCESS: {
    TITLE: '成功',
    PRODUCTION_ORDER: '打印成功',
    SHIFT_REPORT: '打印成功',
    SESSION_ORDER: '打印开台单成功',
    ROOM_EXTENSION: '打印续房单成功',
    CHECKOUT_BILL: '打印结账单成功'
  },
  ERROR: {
    TITLE: '错误',
    GENERAL_FAILURE: '打印失败',
    EMPTY_DATA: '数据不能为空',
    INVALID_PRINTER_CONFIG: '未提供有效的打印机配置',
    ALL_TASKS_FAILED: '所有打印任务均失败',
    VENUE_ID_MISSING: '无法获取场馆ID',
    BILL_IDS_EMPTY: '账单ID列表不能为空',
    SESSION_ID_EMPTY: '会话ID不能为空'
  },
  WARNINGS: {
    NO_SESSION_ID: '没有可打印出品单的sessionId',
    NO_OPEN_TABLE_SESSION_ID: '没有可打印开台单的sessionId',
    NO_ROOM_EXTENSION_SESSION_ID: '没有可打印续房单的sessionId',
    NO_PAY_BILL_ID: '没有可打印结账单的payBillId',
    NO_HAND_NOS: '没有可打印交班单的交班单号',
    EMPTY_PRINT_DATA: '获取的数据为空',
    MISSING_CHECKOUT_DATA: '缺少账单数据'
  },
  SIMULATE_MODE: {
    PRODUCTION_ORDER: '模拟打印模式：无需实际打印',
    SESSION_ORDER: '模拟打印模式：无需实际打印开台单',
    ROOM_EXTENSION: '模拟打印模式：无需实际打印续房单',
    CHECKOUT_BILL: '模拟打印模式：无需实际打印结账单',
    SHIFT_REPORT: '模拟打印模式：无需实际打印交班单'
  }
} as const;

/**
 * 打印任务类型枚举
 */
enum PrintTaskType {
  PRODUCTION_ORDER = 'production_order',
  SESSION_ORDER = 'session_order',
  ROOM_EXTENSION = 'room_extension',
  CHECKOUT_BILL = 'checkout_bill',
  SHIFT_REPORT = 'shift_report'
}

/**
 * 打印结果接口
 */
interface PrintResult {
  success: boolean;
  error?: string;
}

/**
 * 批量打印结果接口
 */
interface BatchPrintResult {
  total: number;
  success: number;
  failed: number;
  results: Array<{ billId: string; success: boolean; error?: string }>;
}

/**
 * 打印机配置接口
 */
interface PrinterConfig {
  ip: string;
  port: number;
}

/**
 * 打印应用服务 - 负责编排打印流程
 * 接收领域实体作为输入
 */
export class PrintingService {
  private readonly port = PRINTING_CONFIG.DEFAULT_PORT;
  private readonly simulatePrint = PRINTING_CONFIG.SIMULATE_PRINT;

  /**
   * 验证必要参数
   * @private
   */
  private validateRequiredParam<T>(param: T | undefined, errorMessage: string): T {
    if (!param) {
      throw new Error(errorMessage);
    }
    return param;
  }

  /**
   * 获取场馆ID
   * @private
   */
  private getVenueId(): string {
    const venueStore = useVenueStore();
    return this.validateRequiredParam(venueStore.venueId, PRINT_MESSAGES.ERROR.VENUE_ID_MISSING);
  }

  /**
   * 获取用户信息
   * @private
   */
  private getUserInfo() {
    const userStore = useUserStore();
    return {
      employeeName: userStore.userInfo?.name || '',
      operatorId: userStore.userInfo?.employee?.id || ''
    };
  }

  /**
   * 获取设备信息
   * @private
   */
  private getDeviceInfo() {
    const deviceStore = useDeviceStore();
    return {
      deviceStore,
      deviceName: deviceStore.cashierMachine?.name || PRINTING_CONFIG.UNKNOWN_DEVICE
    };
  }

  /**
   * 从deviceStore获取打印机配置
   * @private
   */
  private getPrinterConfig(deviceStore: ReturnType<typeof useDeviceStore>): PrinterConfig {
    const cashierMachine = deviceStore.cashierMachine;
    const useNetworkPrinter = cashierMachine?.useNetworkPrinter;
    const printerIp = cashierMachine?.printerIp;

    console.log('cashierMachine', cashierMachine);
    
    if (useNetworkPrinter && printerIp) {
      return { ip: printerIp, port: this.port };
    }

    return { ip: PRINTING_CONFIG.DEFAULT_HOST, port: this.port };
  }

  /**
   * 配置打印机
   * @private
   */
  private configurePrinterIfNeeded(config: PrinterConfig): void {
    if (!this.simulatePrint) {
      console.log('打印服务: 配置打印机:', config);
      configurePrinter(config);
    }
  }

  /**
   * 显示成功提示
   * @private
   */
  private showSuccessToast(message: string): void {
    if (!this.simulatePrint) {
      toast({
        title: PRINT_MESSAGES.SUCCESS.TITLE,
        description: message
      });
    }
  }

  /**
   * 显示错误提示
   * @private
   */
  private showErrorToast(message: string): void {
    if (!this.simulatePrint) {
      toast({
        title: PRINT_MESSAGES.ERROR.TITLE,
        description: message,
        duration: 1000
      });
    }
  }

  /**
   * 显示部分成功提示
   * @private
   */
  private showPartialSuccessToast(successCount: number, totalCount: number, taskName: string): void {
    if (!this.simulatePrint) {
      if (successCount === totalCount) {
        toast({
          title: PRINT_MESSAGES.SUCCESS.TITLE,
          description: `成功打印 ${successCount} 张${taskName}`
        });
      } else {
        toast({
          title: '部分成功',
          description: `成功打印 ${successCount}/${totalCount} 张${taskName}`
        });
      }
    }
  }

  /**
   * 执行打印任务的核心逻辑
   * @private
   */
  private async executePrintTask<T>(
    data: T,
    taskType: PrintTaskType,
    buildReceiptFn: (data: T, venueId: string) => any,
    printerConfig?: PrinterConfig
  ): Promise<boolean> {
    try {
      const venueId = this.getVenueId();
      
      if (printerConfig) {
        this.configurePrinterIfNeeded(printerConfig);
      }

      const receipt = buildReceiptFn(data, venueId);
      const commands = escPosCommandGenerator.generate(receipt, this.simulatePrint);

      if (!this.simulatePrint) {
        const success = await printerService.print(commands);
        if (!success) {
          throw new Error(PRINT_MESSAGES.ERROR.GENERAL_FAILURE);
        }
      } else {
        this.logSimulateMode(taskType);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 记录模拟模式日志
   * @private
   */
  private logSimulateMode(taskType: PrintTaskType): void {
    const messages = {
      [PrintTaskType.PRODUCTION_ORDER]: PRINT_MESSAGES.SIMULATE_MODE.PRODUCTION_ORDER,
      [PrintTaskType.SESSION_ORDER]: PRINT_MESSAGES.SIMULATE_MODE.SESSION_ORDER,
      [PrintTaskType.ROOM_EXTENSION]: PRINT_MESSAGES.SIMULATE_MODE.ROOM_EXTENSION,
      [PrintTaskType.CHECKOUT_BILL]: PRINT_MESSAGES.SIMULATE_MODE.CHECKOUT_BILL,
      [PrintTaskType.SHIFT_REPORT]: PRINT_MESSAGES.SIMULATE_MODE.SHIFT_REPORT
    };
    console.log(messages[taskType]);
  }

  /**
   * 处理打印错误
   * @private
   */
  private handlePrintError(error: unknown, taskName: string): boolean {
    console.error(`打印${taskName}失败:`, error);
    const errorMessage = error instanceof Error ? error.message : PRINT_MESSAGES.ERROR.GENERAL_FAILURE;
    this.showErrorToast(errorMessage);
    return false;
  }

  /**
   * 创建API请求参数
   * @private
   */
  private createApiParams<T extends Record<string, any>>(baseParams: T): T {
    return { ...baseParams };
  }

  /**
   * 验证并获取会话ID
   * @private
   */
  private validateSessionId(sessionId: string | undefined, warningMessage: string): string {
    if (!sessionId) {
      console.warn(`打印服务: ${warningMessage}`);
      throw new Error('会话ID不能为空');
    }
    return sessionId;
  }

  /**
   * 打印出品单
   * @param productionOrderData 转换后的出品单领域实体
   * @param printerConfig 打印机配置 - 用于特定出品点的网络打印机
   */
  private async printProductionOrder(productionOrderData: ProductionOrderData, venueId: string, printerConfig?: PrinterConfig): Promise<boolean> {
    try {
      this.validateRequiredParam(productionOrderData, '出品单' + PRINT_MESSAGES.ERROR.EMPTY_DATA);

      if (!this.simulatePrint && (!printerConfig || !printerConfig.ip)) {
        throw new Error(PRINT_MESSAGES.ERROR.INVALID_PRINTER_CONFIG);
      }

      const success = await this.executePrintTask(
        productionOrderData,
        PrintTaskType.PRODUCTION_ORDER,
        (data, vId) => receiptBuilder.buildProductionOrderReceipt(data, vId),
        printerConfig
      );

      if (success) {
        this.showSuccessToast(PRINT_MESSAGES.SUCCESS.PRODUCTION_ORDER);
      }

      return success;
    } catch (error) {
      return this.handlePrintError(error, '出品单');
    }
  }

  /**
   * 根据会话ID打印出品单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印是否成功
   */
  async printProductOutBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      const validSessionId = this.validateSessionId(sessionId, PRINT_MESSAGES.WARNINGS.NO_SESSION_ID);
      const venueId = this.getVenueId();
      const { employeeName } = this.getUserInfo();

      const params = this.createApiParams<CreateProductOutPrintRecordReqDto>({
        orderNos,
        sessionId: validSessionId,
        venueId,
        employeeName
      });

      console.log('打印服务: 请求打印出品单数据，参数:', params);
      const response = await postApiPrintRecordProductOutCreate(params);
      const printTasks = response.data;

      if (!printTasks || printTasks.length === 0) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.EMPTY_PRINT_DATA);
        return false;
      }

      console.log('打印服务: 获取到出品单数据:', printTasks);

      const printResults = await this.processPrintOutTasks(printTasks, venueId);
      const isSuccess = printResults.some(result => result === true);

      if (isSuccess) {
        this.showSuccessToast(PRINT_MESSAGES.SUCCESS.PRODUCTION_ORDER);
      } else {
        throw new Error(PRINT_MESSAGES.ERROR.ALL_TASKS_FAILED);
      }

      return isSuccess;
    } catch (error) {
      return this.handlePrintError(error, '出品单');
    }
  }

  /**
   * 处理打印任务列表
   * @private
   */
  private async processPrintOutTasks(printTasks: any[], venueId: string): Promise<boolean[]> {
    const printResults: boolean[] = [];

    for (const task of printTasks) {
      const printerConfig = {
        ip: task.printerIp || PRINTING_CONFIG.DEFAULT_HOST,
        port: this.port
      };

      if (!task.data) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.EMPTY_PRINT_DATA);
        continue;
      }

      const productionOrderData = this.convertToProductionOrderData(task.data);
      const result = await this.printProductionOrder(productionOrderData, venueId, printerConfig);
      printResults.push(result);
    }

    return printResults;
  }

  /**
   * 转换为出品单领域实体
   * @private
   */
  private convertToProductionOrderData(data: any): ProductionOrderData {
    return {
      roomInfo: data.roomInfo,
      employeeName: data.employeeName,
      orderTime: data.orderTime || formatDateTime(new Date().toISOString()),
      productionOrderNo: data.productionOrderNo || '',
      orderNo: data.orderNo,
      sessionId: data.sessionId,
      products: (data.products || []) as unknown as ProductionOrderItem[],
      producerName: data.producerName,
      productionTime: data.productionTime
    };
  }

  /**
   * 根据会话ID打印开台单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印是否成功
   */
  async printSessionOrderBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      const validSessionId = this.validateSessionId(sessionId, PRINT_MESSAGES.WARNINGS.NO_OPEN_TABLE_SESSION_ID);
      const venueId = this.getVenueId();

      const params = this.createApiParams<CreateOpenTablePrintRecordReqDto>({
        orderNos,
        sessionId: validSessionId,
        venueId
      });

      console.log('打印服务: 请求打印开台单数据，参数:', params);
      const response = await postApiPrintRecordOpenTableCreate(params);
      const printData = response.data.sessionOrderData;

      if (!printData) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.EMPTY_PRINT_DATA);
        return false;
      }

      console.log('打印服务: 获取到开台单数据:', printData);

      const { deviceStore } = this.getDeviceInfo();
      const printerConfig = this.getPrinterConfig(deviceStore);

      const success = await this.executePrintTask(
        printData as SessionOrderData,
        PrintTaskType.SESSION_ORDER,
        (data, vId) => receiptBuilder.buildSessionOrderReceipt(data, vId),
        printerConfig
      );

      if (success) {
        this.showSuccessToast(PRINT_MESSAGES.SUCCESS.SESSION_ORDER);
      }

      return success;
    } catch (error) {
      return this.handlePrintError(error, '开台单');
    }
  }

  /**
   * 根据会话ID打印续房单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印是否成功
   */
  async printRoomExtensionBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      const validSessionId = this.validateSessionId(sessionId, PRINT_MESSAGES.WARNINGS.NO_ROOM_EXTENSION_SESSION_ID);
      const venueId = this.getVenueId();

      const params = this.createApiParams<CreateRoomExtensionPrintRecordReqDto>({
        orderNos,
        sessionId: validSessionId,
        venueId
      });

      console.log('打印服务: 请求打印续房单数据，参数:', params);
      const response = await postApiPrintRecordRoomExtensionCreate(params);
      const printData = response.data.sessionOrderData;

      if (!printData) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.EMPTY_PRINT_DATA);
        return false;
      }

      console.log('打印服务: 获取到续房单数据:', printData);

      const { deviceStore } = this.getDeviceInfo();
      const printerConfig = this.getPrinterConfig(deviceStore);

      const success = await this.executePrintTask(
        printData as SessionOrderData,
        PrintTaskType.ROOM_EXTENSION,
        (data, vId) => receiptBuilder.buildRoomExtensionReceipt(data, vId),
        printerConfig
      );

      if (success) {
        this.showSuccessToast(PRINT_MESSAGES.SUCCESS.ROOM_EXTENSION);
      }

      return success;
    } catch (error) {
      return this.handlePrintError(error, '续房单');
    }
  }

  /**
   * 根据结账单ID打印结账单
   * @param payBillId 结账单ID
   * @param sessionId 会话ID
   * @returns 打印是否成功
   */
  async printCheckoutBillByPayBillId(payBillId: string | undefined, sessionId: string | undefined, orderNos: string[] | undefined): Promise<boolean> {
    try {
      const validPayBillId = this.validateRequiredParam(payBillId, PRINT_MESSAGES.WARNINGS.NO_PAY_BILL_ID);
      const venueId = this.getVenueId();

      const params = this.createApiParams<CreateCheckoutPrintRecordReqDto>({
        payBillId: validPayBillId,
        sessionId: sessionId || '',
        venueId,
        orderNos: orderNos || []
      });

      console.log('打印服务: 请求打印结账单数据，参数:', params);
      const response = await postApiPrintRecordCheckoutCreate(params);
      const printData = response.data.checkoutBillData;

      if (!printData) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.EMPTY_PRINT_DATA);
        return false;
      }

      console.log('打印服务: 获取到结账单数据:', printData);

      const { deviceStore } = this.getDeviceInfo();
      const printerConfig = this.getPrinterConfig(deviceStore);

      const success = await this.executePrintTask(
        printData as OrderDetailData,
        PrintTaskType.CHECKOUT_BILL,
        (data, vId) => receiptBuilder.buildOrderDetailReceipt(data, vId),
        printerConfig
      );

      if (success) {
        this.showSuccessToast(PRINT_MESSAGES.SUCCESS.CHECKOUT_BILL);
      }

      return success;
    } catch (error) {
      return this.handlePrintError(error, '结账单');
    }
  }

  /**
   * 根据交班单号数组打印交班单
   * @param handNos 交班单号数组
   * @param employeeId 员工ID（可选）
   * @returns 打印是否成功
   */
  async printShiftChangeByHandNos(
    handNos: string[] | undefined,
    employeeId?: string
  ): Promise<boolean> {
    try {
      if (!handNos || handNos.length === 0) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.NO_HAND_NOS);
        return false;
      }

      const venueId = this.getVenueId();
      const { operatorId } = this.getUserInfo();
      const { deviceStore, deviceName } = this.getDeviceInfo();

      const params = this.createApiParams<CreateShiftChangePrintRecordReq>({
        handNos,
        venueId,
        operatorId,
        employeeId,
        deviceName
      });

      console.log('打印服务: 请求打印交班单数据，参数:', params);
      const response = await postApiPrintRecordShiftChangeCreate(params);
      const printRecords = response.data;

      if (!printRecords || printRecords.length === 0) {
        console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.EMPTY_PRINT_DATA);
        return false;
      }

      console.log('打印服务: 获取到交班单数据:', printRecords);

      const printerConfig = this.getPrinterConfig(deviceStore);
      this.configurePrinterIfNeeded(printerConfig);

      const printResults = await this.processShiftReportPrintTasks(printRecords, venueId);
      const successCount = printResults.filter(result => result === true).length;
      const totalCount = printResults.length;

      if (successCount === 0) {
        throw new Error('所有交班单打印失败');
      }

      this.showPartialSuccessToast(successCount, totalCount, '交班单');

      return successCount > 0;
    } catch (error) {
      return this.handlePrintError(error, '交班单');
    }
  }

  /**
   * 处理交班单打印任务
   * @private
   */
  private async processShiftReportPrintTasks(printRecords: any[], venueId: string): Promise<boolean[]> {
    const printResults: boolean[] = [];
    
    for (const record of printRecords) {
      const printData = record.shiftChangeBillData;
      
      if (!printData) {
        console.warn(`打印服务: 交班单 ${record.handNo} 数据为空`);
        printResults.push(false);
        continue;
      }

      try {
        const shiftReportData: ShiftReportData = printData as ShiftReportData;
        
        const success = await this.executePrintTask(
          shiftReportData,
          PrintTaskType.SHIFT_REPORT,
          (data, vId) => receiptBuilder.buildShiftReportReceipt(data, vId)
        );

        if (!this.simulatePrint) {
          console.log(`打印服务: 执行打印交班单 ${record.handNo}`);
        } else {
          console.log(`模拟打印模式：无需实际打印交班单 ${record.handNo}`);
        }

        printResults.push(success);
      } catch (error) {
        console.error(`打印交班单 ${record.handNo} 失败:`, error);
        printResults.push(false);
      }
    }

    return printResults;
  }

  /**
   * 根据交班单号打印交班单 (兼容旧接口)
   * @param handNo 交班单号
   * @param employeeId 员工ID（可选）
   * @returns 打印是否成功
   */
  async printShiftChangeByHandNo(
    handNo: string | undefined,
    employeeId?: string
  ): Promise<boolean> {
    if (!handNo) {
      console.warn('打印服务: ' + PRINT_MESSAGES.WARNINGS.NO_HAND_NOS);
      return false;
    }
    
    return this.printShiftChangeByHandNos([handNo], employeeId);
  }

  /**
   * 批量查询并打印结账单
   * @param billIds 账单ID数组
   * @param sessionId 会话ID
   * @returns 打印结果
   */
  async printCheckoutBillsByBillIds(billIds: string[], sessionId: string): Promise<BatchPrintResult> {
    try {
      this.validateBatchPrintParams(billIds, sessionId);

      console.log('[PrintingService] 开始批量打印结账单:', {
        billIds,
        sessionId,
        count: billIds.length
      });

      const venueId = this.getVenueId();
      const existingRecords = await this.batchQueryCheckoutPrintRecords(billIds, venueId);
      const { needCreateBillIds } = this.analyzeCheckoutPrintRecords(billIds, existingRecords);
      const newRecords = await this.batchCreateCheckoutPrintRecords(needCreateBillIds, sessionId, venueId);
      const allRecords = [...existingRecords, ...newRecords];
      const printResults = await this.batchExecuteCheckoutPrint(allRecords);

      const result = this.createBatchPrintResult(printResults);

      console.log('[PrintingService] 批量打印结账单完成:', result);

      return result;
    } catch (error) {
      console.error('[PrintingService] 批量打印结账单失败:', error);
      throw error;
    }
  }

  // -------------------------------------------------------------
  /**
   * 验证批量打印参数
   * @private
   */
  private validateBatchPrintParams(billIds: string[], sessionId: string): void {
    this.validateRequiredParam(billIds?.length, PRINT_MESSAGES.ERROR.BILL_IDS_EMPTY);
    this.validateRequiredParam(sessionId, PRINT_MESSAGES.ERROR.SESSION_ID_EMPTY);
  }

  /**
   * 创建批量打印结果
   * @private
   */
  private createBatchPrintResult(printResults: Array<{ billId: string; success: boolean; error?: string }>): BatchPrintResult {
    const successCount = printResults.filter(r => r.success).length;
    const failedCount = printResults.length - successCount;

    return {
      total: printResults.length,
      success: successCount,
      failed: failedCount,
      results: printResults
    };
  }

  /**
   * 批量查询结账单打印记录
   * @private
   */
  private async batchQueryCheckoutPrintRecords(billIds: string[], venueId: string): Promise<CheckoutPrintRecordVO[]> {
    try {
      const params = this.createApiParams<GetCheckoutPrintRecordsByPayBillIdsReqDto>({
        payBillIds: billIds,
        venueId
      });

      console.log('[PrintingService] 批量查询结账单打印记录:', params);
      const response = await postApiPrintRecordCheckoutPayBillIds(params);

      if (response?.data) {
        console.log(`[PrintingService] 查询到 ${response.data.length} 条已有打印记录`);
        return response.data;
      } else {
        console.log('[PrintingService] 未查询到已有打印记录');
        return [];
      }
    } catch (error) {
      console.error('[PrintingService] 批量查询打印记录失败:', error);
      return [];
    }
  }

  /**
   * 分析打印记录，确定哪些账单需要创建新记录
   * @private
   */
  private analyzeCheckoutPrintRecords(billIds: string[], existingRecords: CheckoutPrintRecordVO[]): {
    needCreateBillIds: string[];
    existingRecordsMap: Map<string, CheckoutPrintRecordVO>;
  } {
    const existingRecordsMap = this.createExistingRecordsMap(existingRecords);
    const needCreateBillIds = this.findBillIdsNeedingCreation(billIds, existingRecordsMap);

    console.log('[PrintingService] 打印记录分析:', {
      totalBills: billIds.length,
      existingRecords: existingRecords.length,
      needCreate: needCreateBillIds.length,
      needCreateBillIds
    });

    return { needCreateBillIds, existingRecordsMap };
  }

  /**
   * 创建已有记录映射
   * @private
   */
  private createExistingRecordsMap(existingRecords: CheckoutPrintRecordVO[]): Map<string, CheckoutPrintRecordVO> {
    const existingRecordsMap = new Map<string, CheckoutPrintRecordVO>();
    existingRecords.forEach(record => {
      if (record.payBillId) {
        existingRecordsMap.set(record.payBillId, record);
      }
    });
    return existingRecordsMap;
  }

  /**
   * 找出需要创建记录的账单ID
   * @private
   */
  private findBillIdsNeedingCreation(billIds: string[], existingRecordsMap: Map<string, CheckoutPrintRecordVO>): string[] {
    return billIds.filter(billId => !existingRecordsMap.has(billId));
  }

  /**
   * 批量创建结账单打印记录
   * @private
   */
  private async batchCreateCheckoutPrintRecords(billIds: string[], sessionId: string, venueId: string): Promise<CheckoutPrintRecordVO[]> {
    if (billIds.length === 0) {
      console.log('[PrintingService] 无需创建新的打印记录');
      return [];
    }

    console.log(`[PrintingService] 开始批量创建 ${billIds.length} 条打印记录`);

    const createPromises = billIds.map(billId => this.createSingleCheckoutPrintRecord(billId, sessionId, venueId));
    const newRecords = await Promise.all(createPromises);
    
    console.log(`[PrintingService] 成功创建 ${newRecords.length} 条打印记录`);

    return newRecords;
  }

  /**
   * 创建单个结账单打印记录
   * @private
   */
  private async createSingleCheckoutPrintRecord(billId: string, sessionId: string, venueId: string): Promise<CheckoutPrintRecordVO> {
    try {
      const params = this.createApiParams<CreateCheckoutPrintRecordReqDto>({
        payBillId: billId,
        sessionId,
        venueId,
        orderNos: []
      });

      console.log(`[PrintingService] 创建账单 ${billId} 的打印记录`);
      const response = await postApiPrintRecordCheckoutCreate(params);

      if (response?.data) {
        console.log(`[PrintingService] 账单 ${billId} 创建打印记录成功:`, response.data.id);
        return response.data;
      } else {
        throw new Error(`创建打印记录失败: ${response?.statusText || '未知错误'}`);
      }
    } catch (error) {
      console.error(`[PrintingService] 创建账单 ${billId} 打印记录失败:`, error);
      throw error;
    }
  }

  /**
   * 批量执行结账单打印
   * @private
   */
  private async batchExecuteCheckoutPrint(records: CheckoutPrintRecordVO[]): Promise<Array<{ billId: string; success: boolean; error?: string }>> {
    console.log(`[PrintingService] 开始批量执行 ${records.length} 条打印任务`);

    const printPromises = records.map(record => this.executeSingleCheckoutPrint(record));
    return await Promise.all(printPromises);
  }

  /**
   * 执行单个结账单打印任务
   * @private
   */
  private async executeSingleCheckoutPrint(record: CheckoutPrintRecordVO): Promise<{ billId: string; success: boolean; error?: string }> {
    try {
      const billId = record.payBillId || 'unknown';

      if (!record.checkoutBillData) {
        console.warn(`[PrintingService] 账单 ${billId} 打印记录缺少账单数据`);
        return { billId, success: false, error: PRINT_MESSAGES.WARNINGS.MISSING_CHECKOUT_DATA };
      }

      console.log(`[PrintingService] 执行打印账单: ${billId}，记录ID: ${record.id}`);

      const success = await this.executeCheckoutPrint(record);

      if (success) {
        console.log(`[PrintingService] 账单 ${billId} 打印成功`);
        return { billId, success: true };
      } else {
        console.warn(`[PrintingService] 账单 ${billId} 打印失败`);
        return { billId, success: false, error: PRINT_MESSAGES.ERROR.GENERAL_FAILURE };
      }

    } catch (error) {
      const billId = record.payBillId || 'unknown';
      console.error(`[PrintingService] 账单 ${billId} 打印异常:`, error);
      return {
        billId,
        success: false,
        error: error instanceof Error ? error.message : '打印异常'
      };
    }
  }

  /**
   * 执行单个结账单打印
   * @private
   */
  private async executeCheckoutPrint(record: CheckoutPrintRecordVO): Promise<boolean> {
    try {
      if (!record.checkoutBillData) {
        return false;
      }

      const { deviceStore } = this.getDeviceInfo();
      const printerConfig = this.getPrinterConfig(deviceStore);

      const success = await this.executePrintTask(
        record.checkoutBillData as OrderDetailData,
        PrintTaskType.CHECKOUT_BILL,
        (data, venueId) => receiptBuilder.buildOrderDetailReceipt(data, venueId),
        printerConfig
      );

      return success;
    } catch (error) {
      console.error('[PrintingService] 执行结账单打印失败:', error);
      return false;
    }
  }

  /**
   * 测试打印开台单 - 直接使用提供的测试数据
   * @param testData 测试开台单数据
   * @returns 打印是否成功
   */
  async printTestSessionOrder(testData: SessionOrderData): Promise<boolean> {
    try {
      this.validateRequiredParam(testData, '测试数据' + PRINT_MESSAGES.ERROR.EMPTY_DATA);
      
      const venueId = this.getVenueId();
      const { deviceStore } = this.getDeviceInfo();
      const printerConfig = this.getPrinterConfig(deviceStore);

      console.log('打印服务: 开始测试打印开台单:', testData);

      const success = await this.executePrintTask(
        testData,
        PrintTaskType.SESSION_ORDER,
        (data, vId) => receiptBuilder.buildSessionOrderReceipt(data, vId),
        printerConfig
      );

      if (success) {
        this.showSuccessToast('测试打印开台单成功');
      }

      return success;
    } catch (error) {
      return this.handlePrintError(error, '测试开台单');
    }
  }
}

// 导出服务实例
export const printingService = new PrintingService();
