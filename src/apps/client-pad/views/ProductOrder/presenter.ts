import { computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { IProductOrderViewModel, ProductCategory, ProductItem, CartItem } from './viewmodel';
import { ProductOrderConverter } from './converter';
import { ProductOrderInteractor } from './interactor';

/**
 * ProductOrder展示器类
 * 处理视图逻辑和状态管理
 */
export class ProductOrderPresenter implements IProductOrderViewModel {
  private _state;
  public computed;
  public actions;

  constructor(props: { roomId: string; roomName: string; sessionId: string }) {
    // 初始化状态
    this._state = reactive({
      // 房间信息
      roomId: props.roomId || '',
      roomName: props.roomName || '',
      sessionId: props.sessionId || '',

      // 分类相关
      categories: [] as ProductCategory[],
      selectedCategory: 'all',

      // 商品相关
      products: [] as ProductItem[],
      loading: false,

      // 数据缓存和本地过滤
      allProducts: [] as ProductItem[], // 所有商品的完整列表
      isSearchMode: false,
      searchKeyword: '',

      // 购物车相关
      cartItems: [] as CartItem[],

      // 套餐相关
      currentPackage: null as any,
      packageDialogVisible: false
    });

    // 初始化计算属性
    this.computed = {
      totalAmount: computed(() => {
        return ProductOrderConverter.calculateTotalAmount(this._state.cartItems);
      }),

      totalItems: computed(() => {
        return this._state.cartItems.reduce((total, item) => total + item.quantity, 0);
      })
    };

    // 初始化动作方法
    this.actions = {
      // 加载分类和商品
      loadCategories: this.loadCategories.bind(this),
      loadProducts: this.loadProducts.bind(this),

      // 分类操作
      selectCategory: this.selectCategory.bind(this),

      // 商品操作
      addToCart: this.addToCart.bind(this),
      handlePackageConfirm: this.handlePackageConfirm.bind(this),
      closePackageDialog: this.closePackageDialog.bind(this),

      // 购物车操作
      clearCart: this.clearCart.bind(this),
      increaseQuantity: this.increaseQuantity.bind(this),
      decreaseQuantity: this.decreaseQuantity.bind(this),
      updateQuantity: this.updateQuantity.bind(this),

      // 套餐编辑
      editPackage: this.editPackage.bind(this),

      // 搜索商品
      searchProducts: this.searchProducts.bind(this)
    };

    // 自动加载分类数据，然后加载所有商品
    this.loadAllData();
  }

  /**
   * 获取状态
   */
  public get state() {
    return this._state;
  }

  /**
   * 加载所有数据（分类和商品）
   */
  private async loadAllData(): Promise<void> {
    this._state.loading = true;

    try {
      // 1. 加载分类数据
      const categoriesData = await ProductOrderInteractor.getCategories();

      if (categoriesData) {
        this._state.categories = ProductOrderConverter.apiCategoriesToViewCategories(
          categoriesData.productTypeVOs,
          categoriesData.productPackageTypeVOs,
          this._state.selectedCategory
        );

        // 2. 加载所有商品数据
        // 使用'all'参数一次性获取所有商品
        const productsData = await ProductOrderInteractor.getProducts('all', false);

        if (productsData) {
          // 保存所有商品
          let allProducts = ProductOrderConverter.apiProductsToViewProducts(productsData);

          // 优先展示套餐，然后是单品
          allProducts.sort((a, b) => {
            if (a.isPackage && !b.isPackage) return -1; // 套餐排在前面
            if (!a.isPackage && b.isPackage) return 1; // 单品排在后面
            return 0; // 保持原有顺序
          });

          this._state.allProducts = allProducts;

          // 初始显示所有商品
          this._state.products = [...this._state.allProducts];
        } else {
          ElMessage.error('获取商品列表失败');
        }
      } else {
        ElMessage.error('获取分类列表失败');
      }
    } catch (error) {
      console.error('加载数据出错:', error);
      ElMessage.error('加载数据失败');
    } finally {
      this._state.loading = false;
    }
  }

  /**
   * 加载分类列表 - 已被loadAllData替代，保持API兼容
   */
  private async loadCategories(): Promise<void> {
    // 此方法已被loadAllData替代，但保留以兼容现有接口
    if (this._state.categories.length === 0) {
      await this.loadAllData();
    }
  }

  /**
   * 加载商品列表 - 已被loadAllData替代，保持API兼容
   * @param categoryId 分类ID
   */
  private async loadProducts(categoryId: string): Promise<void> {
    // 此方法已被loadAllData替代，改为本地过滤
    this.filterProductsByCategory(categoryId);
  }

  /**
   * 根据分类在本地过滤商品
   * @param categoryId 分类ID
   */
  private filterProductsByCategory(categoryId: string): void {
    // 如果是搜索模式，先进行搜索过滤，再按分类过滤
    if (this._state.isSearchMode && this._state.searchKeyword) {
      const searchResults = this.filterProductsByKeyword(this._state.searchKeyword);

      if (categoryId === 'all') {
        // 显示所有搜索结果
        this._state.products = searchResults;
      } else {
        // 分类ID对应的是否为套餐
        const isPackageCategory = this._state.categories.find(cat => cat.id === categoryId)?.isPackage || false;

        // 过滤符合当前分类的搜索结果
        this._state.products = searchResults.filter(product => {
          // 根据商品的categoryId与选中的分类ID进行匹配
          return product.categoryId === categoryId;
        });
      }
    } else {
      // 非搜索模式，直接按分类过滤
      if (categoryId === 'all') {
        // 显示所有商品
        this._state.products = [...this._state.allProducts];
      } else {
        // 分类ID对应的是否为套餐
        const isPackageCategory = this._state.categories.find(cat => cat.id === categoryId)?.isPackage || false;

        // 过滤符合当前分类的商品
        this._state.products = this._state.allProducts.filter(product => {
          // 直接比较商品的categoryId与选中的分类ID
          return product.categoryId === categoryId;
        });
      }
    }

    // 优先展示套餐，然后是单品
    this._state.products.sort((a, b) => {
      if (a.isPackage && !b.isPackage) return -1; // 套餐排在前面
      if (!a.isPackage && b.isPackage) return 1; // 单品排在后面
      return 0; // 保持原有顺序
    });

    // 如果过滤后没有商品，显示提示信息
    if (this._state.products.length === 0) {
      ElMessage.info(`该分类下暂无商品`);
    }
  }

  /**
   * 根据关键字在本地过滤商品
   * @param keyword 搜索关键字
   * @returns 过滤后的商品列表
   */
  private filterProductsByKeyword(keyword: string): ProductItem[] {
    if (!keyword.trim()) return [...this._state.allProducts];

    const lowerKeyword = keyword.toLowerCase();

    // 搜索商品名称或分类名称
    const filteredProducts = this._state.allProducts.filter(product => {
      // 先检查商品名称
      if (product.name.toLowerCase().includes(lowerKeyword)) {
        return true;
      }

      // 检查价格是否匹配（当关键字是纯数字时）
      const numericKeyword = parseFloat(lowerKeyword);
      if (!isNaN(numericKeyword) && product.price === numericKeyword) {
        return true;
      }

      // 检查商品分类名称
      const category = this._state.categories.find(cat => cat.id === product.categoryId);
      if (category && category.name.toLowerCase().includes(lowerKeyword)) {
        return true;
      }

      return false;
    });

    // 优先展示套餐，然后是单品
    return filteredProducts.sort((a, b) => {
      if (a.isPackage && !b.isPackage) return -1; // 套餐排在前面
      if (!a.isPackage && b.isPackage) return 1; // 单品排在后面
      return 0; // 保持原有顺序
    });
  }

  /**
   * 选择分类
   * @param categoryId 分类ID
   */
  private selectCategory(categoryId: string): void {
    if (this._state.selectedCategory === categoryId) {
      return;
    }

    this._state.selectedCategory = categoryId;

    // 更新分类选中状态
    this._state.categories.forEach(category => {
      category.isSelected = category.id === categoryId;
    });

    // 本地过滤显示对应分类的商品
    this.filterProductsByCategory(categoryId);
  }

  /**
   * 添加商品到购物车
   * @param product 商品
   */
  private addToCart(product: ProductItem): void {
    if (product.status === 'sold_out') {
      ElMessage.warning('该商品已沽清');
      return;
    }

    // 处理套餐商品
    if (product.isPackage) {
      if (ProductOrderConverter.hasOptionalGroups(product)) {
        // 打开套餐选择
        this._state.currentPackage = product;
        this._state.packageDialogVisible = true;
        return;
      }
    }

    // 普通商品或无选项套餐直接添加
    const cartItem = product.isPackage ? ProductOrderConverter.packageToCartItem(product.packageDetail) : ProductOrderConverter.productToCartItem(product);

    // 尝试合并购物车中已有的相同商品
    const existingItem = this._state.cartItems.find(
      item => item.id === cartItem.id && (!item.isPackage || !cartItem.isPackage || item.packageUniqueId === cartItem.packageUniqueId)
    );

    if (existingItem) {
      existingItem.quantity += cartItem.quantity;
    } else {
      this._state.cartItems.push(cartItem);
    }

    ElMessage.success('已添加到购物车');
  }

  /**
   * 处理套餐确认
   * @param packageData 套餐数据
   */
  private handlePackageConfirm(packageData: any): void {
    const cartItem = ProductOrderConverter.packageToCartItem(packageData);

    // 处理编辑模式
    if (this._state.currentPackage?.isEditing) {
      const originalItem = this._state.cartItems.find(
        item => item.isPackage && item.id === cartItem.id && this._state.currentPackage.packageUniqueId === item.packageUniqueId
      );

      if (originalItem) {
        cartItem.quantity = originalItem.quantity;
        // 删除原项
        this._state.cartItems = this._state.cartItems.filter(item => item !== originalItem);
      }
    }

    // 添加新项
    this._state.cartItems.push(cartItem);

    // 关闭套餐对话框
    this._state.packageDialogVisible = false;
    this._state.currentPackage = null;

    ElMessage.success('已添加到购物车');
  }

  /**
   * 关闭套餐对话框
   */
  private closePackageDialog(): void {
    this._state.packageDialogVisible = false;
    this._state.currentPackage = null;
  }

  /**
   * 清空购物车
   */
  private clearCart(): void {
    this._state.cartItems = [];
  }

  /**
   * 增加商品数量
   * @param item 购物车项
   */
  private increaseQuantity(item: CartItem): void {
    const cartItem = this._state.cartItems.find(i => i === item);
    if (cartItem) {
      cartItem.quantity++;
    }
  }

  /**
   * 减少商品数量
   * @param item 购物车项
   */
  private decreaseQuantity(item: CartItem): void {
    const cartItem = this._state.cartItems.find(i => i === item);
    if (cartItem) {
      if (cartItem.quantity > 1) {
        cartItem.quantity--;
      } else {
        this._state.cartItems = this._state.cartItems.filter(i => i !== item);
      }
    }
  }

  /**
   * 更新商品数量
   * @param item 购物车项
   * @param value 新数量
   */
  private updateQuantity(item: CartItem, value: number | undefined): void {
    if (!value || value < 1) {
      this._state.cartItems = this._state.cartItems.filter(i => i !== item);
    } else {
      const cartItem = this._state.cartItems.find(i => i === item);
      if (cartItem) {
        cartItem.quantity = value;
      }
    }
  }

  /**
   * 编辑套餐
   * @param item 购物车项
   */
  private editPackage(item: CartItem): void {
    if (!item.isPackage) return;

    // 创建套餐编辑对象
    const packageToEdit = JSON.parse(JSON.stringify(item));
    packageToEdit.isEditing = true;

    this._state.currentPackage = packageToEdit;
    this._state.packageDialogVisible = true;
  }

  /**
   * 搜索商品
   * @param keyword 搜索关键字
   */
  private searchProducts(keyword: string): void {
    this._state.searchKeyword = keyword.trim();

    // 如果关键字为空，恢复当前分类下的商品列表
    if (!this._state.searchKeyword) {
      this._state.isSearchMode = false;
      this.filterProductsByCategory(this._state.selectedCategory);
      return;
    }

    // 设置为搜索模式
    this._state.isSearchMode = true;

    // 在所有商品中搜索，然后按当前分类过滤
    if (this._state.selectedCategory === 'all') {
      // 如果当前是全部分类，则在所有商品中搜索
      this._state.products = this.filterProductsByKeyword(this._state.searchKeyword);
    } else {
      // 如果选择了特定分类，则先按分类过滤，再搜索
      this.filterProductsByCategory(this._state.selectedCategory);
    }
  }
}

/**
 * ProductOrder组合函数
 * @param props 组件属性
 * @returns ProductOrder视图模型
 */
export function useProductOrder(props: { roomId: string; roomName: string; sessionId: string }): IProductOrderViewModel {
  return new ProductOrderPresenter(props);
}
