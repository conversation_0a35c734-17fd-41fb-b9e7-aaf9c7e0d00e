import { postApiProductQueryDetailBytype, postApiProductListTypes } from '@/api/autoGenerated/defaultVersion/product';
import { ProductOrPackageRVO, ProductTypeAndPackageVO } from '@/api/autoGenerated/shared/types';

/**
 * ProductOrder交互器
 * 处理业务逻辑和API调用
 */
export class ProductOrderInteractor {
  /**
   * 获取分类列表
   * @returns 分类API响应
   */
  static async getCategories(): Promise<ProductTypeAndPackageVO | null> {
    try {
      const response = await postApiProductListTypes();
      if (response && response.code === 0) {
        return response.data || null;
      }
      return null;
    } catch (error) {
      console.error('获取商品分类失败:', error);
      return null;
    }
  }

  /**
   * 获取商品列表
   * @param categoryId 分类ID
   * @param isPackage 是否为套餐分类
   * @returns 商品API响应
   */
  static async getProducts(categoryId: string, isPackage: boolean): Promise<ProductOrPackageRVO | null> {
    try {
      // 构建请求参数
      const params: any = {
        pageSize: 1000, // 设置足够大的页面大小，确保一次获取所有商品
        pageNum: 1
      };

      // 如果不是"全部"分类，则按分类筛选
      if (categoryId !== 'all') {
        if (isPackage) {
          params.productPackageTypeId = categoryId;
        } else {
          params.productTypeId = categoryId;
        }
      }

      const response = await postApiProductQueryDetailBytype(params);
      if (response && response.code === 0) {
        return response.data || null;
      }
      return null;
    } catch (error) {
      console.error('获取商品列表失败:', error);
      return null;
    }
  }
}
