import { ProductOrPackageRVO, ProductTypeVO, ProductPackageTypeVO, ProductVO, ProductPackageVO } from '@/api/autoGenerated/shared/types';
import { ComputedRef } from 'vue';

// 商品分类类型
export interface ProductCategory {
  id: string;
  name: string;
  isPackage: boolean; // 是否是套餐分类
  isSelected: boolean; // 是否被选中
}

// 商品项类型
export interface ProductItem {
  id: string;
  name: string;
  price: number;
  unit: string;
  imageUrl: string;
  status: 'normal' | 'sold_out';
  isPackage: boolean;
  packageDetail?: any;
  promotionTag?: string; // 促销标签
  isPromotion?: boolean; // 是否是推广商品
  categoryId?: string; // 分类ID，用于本地过滤
}

// 购物车商品类型
export interface CartItem {
  id: string;
  name: string;
  currentPrice: number;
  quantity: number;
  unit: string;
  imageUrl: string; // 商品图片URL
  isPackage: boolean;
  packageUniqueId?: string;
  packageDetail?: any;
  packageSelectedItems?: string; // 套餐已选商品描述
  isEditing?: boolean;
}

// 状态接口
export interface IProductOrderState {
  // 房间信息
  roomId: string;
  roomName: string;
  sessionId: string;

  // 分类相关
  categories: ProductCategory[];
  selectedCategory: string;

  // 商品相关
  products: ProductItem[];
  loading: boolean;

  // 本地过滤相关
  allProducts: ProductItem[]; // 所有商品的完整缓存
  isSearchMode: boolean;
  searchKeyword: string;

  // 购物车相关
  cartItems: CartItem[];

  // 套餐相关
  currentPackage: any;
  packageDialogVisible: boolean;
}

// 计算属性接口
export interface IProductOrderComputed {
  totalAmount: ComputedRef<number>;
  totalItems: ComputedRef<number>;
}

// 动作接口
export interface IProductOrderActions {
  // 加载分类和商品
  loadCategories(): Promise<void>;
  loadProducts(categoryId: string): Promise<void>;

  // 分类操作
  selectCategory(categoryId: string): void;

  // 商品操作
  addToCart(product: ProductItem): void;
  handlePackageConfirm(packageData: any): void;
  closePackageDialog(): void;

  // 购物车操作
  clearCart(): void;
  increaseQuantity(item: CartItem): void;
  decreaseQuantity(item: CartItem): void;
  updateQuantity(item: CartItem, value: number | undefined): void;

  // 套餐编辑
  editPackage(item: CartItem): void;

  // 搜索商品
  searchProducts(keyword: string): void;
}

// 总的ViewModel接口
export interface IProductOrderViewModel {
  state: IProductOrderState;
  computed: IProductOrderComputed;
  actions: IProductOrderActions;
}
