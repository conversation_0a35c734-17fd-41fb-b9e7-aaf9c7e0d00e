import { RoomStageVO } from '@/types/projectobj';
import { IRealTimeTableState } from './viewmodel';
import { ExtendedStageVO } from '@/modules/room/types/extendedStageVO';
// 导入统一的状态常量
import { ROOM_STATUS, SESSION_PAY_STATUS, SESSION_TAGS } from '@/modules/room/constants/stageStatus';

export class RealTimeTableConverter {
  // 创建初始状态
  static createInitialState(): IRealTimeTableState {
    return {
      loading: false,
      stages: [],
      currentStageId: null,
      searchText: '',
      sortType: 'name',
      filters: [],
      displayMode: 'grid',
      refreshTimers: [],
      tempFilters: [],
      filterPopoverVisible: false,
      selectedButton: 'grid',
      retryCount: 0,
      activeTab: 'all'
    };
  }

  // 处理从API获取的舞台数据
  static processStageData(rawData: any[]): ExtendedStageVO[] {
    return rawData.map(stage => {
      // 构建ExtendedStageVO对象
      const result = {
        ...stage,
        isHighlighted: false,
        isSelected: false
      } as ExtendedStageVO;

      // 确保sessionVO中的数值字段是正确的数字类型
      if (result.sessionVO) {
        // 转换totalFee为数字类型
        if (result.sessionVO.totalFee !== undefined && result.sessionVO.totalFee !== null) {
          result.sessionVO.totalFee = Number(result.sessionVO.totalFee);
        }
        // 转换其他可能的数值字段
        if (result.sessionVO.roomFee !== undefined && result.sessionVO.roomFee !== null) {
          result.sessionVO.roomFee = Number(result.sessionVO.roomFee);
        }
        if (result.sessionVO.supermarketFee !== undefined && result.sessionVO.supermarketFee !== null) {
          result.sessionVO.supermarketFee = Number(result.sessionVO.supermarketFee);
        }
        if (result.sessionVO.unpaidAmount !== undefined && result.sessionVO.unpaidAmount !== null) {
          result.sessionVO.unpaidAmount = Number(result.sessionVO.unpaidAmount);
        }
        if (result.sessionVO.minConsume !== undefined && result.sessionVO.minConsume !== null) {
          result.sessionVO.minConsume = Number(result.sessionVO.minConsume);
        }
      }

      return result;
    });
  }
}
