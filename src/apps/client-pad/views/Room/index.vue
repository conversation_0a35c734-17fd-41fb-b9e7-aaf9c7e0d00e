<template>
  <div class="flex flex-col h-full bg-[#f5f5f5]">
    <!-- 顶部操作区域 -->
    <div class="p-[24px] border-b flex justify-between items-center">
      <!-- 左侧标签页 -->
      <div class="flex bg-white rounded-[10px] h-[68px] w-[224px] px-[8px] items-center">
        <div
          class="flex-1 h-[52px] w-[100px] font-medium rounded-[6px] flex items-center justify-center cursor-pointer"
          :class="vm.state.activeTab === 'all' ? 'bg-btn-focus text-white' : 'text-gray-500'"
          @click="vm.actions.handleTabChange('all')">
          全部
        </div>
        <div
          class="flex-1 h-[52px] w-[100px] font-medium rounded-[6px] flex items-center justify-center cursor-pointer"
          :class="vm.state.activeTab === 'inUse' ? 'bg-btn-focus text-white' : 'text-gray-500'"
          @click="vm.actions.handleTabChange('inUse')">
          使用中
        </div>
      </div>

      <!-- 中间搜索区域 -->
      <div class="flex-1 flex justify-center px-[24px]">
        <el-input v-model="vm.state.searchText" placeholder="搜索包厢" class="custom-search-input" @input="vm.actions.handleSearch">
          <template #prefix>
            <el-icon class="ml-4">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 右侧排序区域 -->
      <div class="flex items-center gap-[12px]">
        <!-- 排序下拉菜单 -->
        <el-dropdown @command="vm.actions.handleSortCommand" trigger="click" class="customer-dropdown sort-dropdown w-[152px] h-[68px]">
          <div class="flex flex-col justify-center items-center bg-white rounded-[10px] h-full w-full cursor-pointer shadow-sm">
            <div class="text-xs text-gray-400">可选择</div>
            <div class="flex items-center text-sm">
              {{ vm.computed.currentSortLabel.value }}
              <el-icon class="ml-1">
                <ArrowDown />
              </el-icon>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="sort in vm.sortOptions" :key="sort.value" :command="sort.value">
                <el-icon v-if="vm.state.sortType === sort.value" class="mr-1">
                  <Check />
                </el-icon>
                {{ sort.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 通知图标 - 使用新组件 -->
        <NotificationButton @openNotificationCenter="showNotificationCenter" />

        <!-- 用户图标 -->
        <div class="flex items-center gap-[8px] h-[68px] pl-[16px] pr-[12px] bg-white rounded-[10px]">
          <img :src="currentUserAvatar" alt="用户头像" class="w-[36px] h-[36px] rounded-full cursor-pointer" @click="vm.actions.handleAvatarClick" />
          <el-dropdown @command="vm.actions.handleUserCommand" trigger="click" size="large">
            <span class="flex items-center cursor-pointer">
              <span class="text-sm mr-1">{{ currentUserName }}</span>
              <el-icon>
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- RoomGrid 区域 -->
    <div class="flex-1 overflow-auto p-5">
      <RoomGrid
        ref="roomGridRef"
        :stages="vm.computed.filteredStages.value"
        :loading="vm.state.loading"
        :display-mode="'grid'"
        :search-text="vm.state.searchText"
        :sort-type="vm.state.sortType"
        :filters="vm.state.filters"
        @select-stage="handleStageSelect" />
    </div>

    <!-- 底部状态标签栏 -->
    <div class="h-[52px] bg-white px-[24px] flex flex-row items-center justify-between border-t border-[#E0E0E0]">
      <div class="flex flex-row items-center gap-[12px]">
        <div class="flex flex-row items-center gap-[4px]">
          <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#FFFFFF]"></span>
          <span class="text-[14px] font-[500]">空闲中</span>
        </div>
        <div class="flex flex-row items-center gap-[4px]">
          <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#D5E7F2]"></span>
          <span class="text-[14px] font-[500]">使用中</span>
        </div>
        <div class="flex flex-row items-center gap-[4px]">
          <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#F8DEA7]"></span>
          <span class="text-[14px] font-[500]">超时</span>
        </div>
        <div class="flex flex-row items-center gap-[4px]">
          <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#DFDEDD]"></span>
          <span class="text-[14px] font-[500]">故障</span>
        </div>
        <div class="flex flex-row items-center gap-[4px]">
          <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#FFFFFF]"></span>
          <span class="text-[14px] font-[500]">清洁中</span>
        </div>
      </div>
      <!-- 加入计时器 -->
      <div class="flex flex-row items-center gap-[8px]">
        <el-tooltip content="NATS连接状态 - 点击重连" placement="top">
          <div class="relative cursor-pointer" @click="handleNatsReconnect">
            <div
              class="w-[12px] h-[12px] rounded-full"
              :class="{
                'bg-green-500': natsStore.isConnected,
                'bg-red-500': !natsStore.isConnected,
                'animate-pulse': !natsStore.isConnected
              }"></div>
          </div>
        </el-tooltip>
        <span class="text-[14px] font-[500] min-w-[140px]">{{ timeStore.formattedTime }}</span>
      </div>
    </div>

    <!-- 消息中心组件 - 移到最外层，避免嵌套问题 -->
    <NotificationCenter ref="notificationCenterRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
// 忽略TypeScript错误，继续使用原有导入方式
// @ts-ignore
import RoomGrid from '@/modules/room/components/RoomGrid/index.vue';
import { useRealTimeTablePresenter } from './presenter';
import type { IRealTimeTableViewModel } from './viewmodel';
import { useTimeStore } from '@/stores/timeStore';
import { useNatsStore } from '@/stores/natsStore';
import NatsService from '@/services/nats-service';
import { Search, ArrowDown, Check } from '@element-plus/icons-vue';
import userAvatar from '@/assets/v2/login/avater.png';
// 忽略TypeScript错误，继续使用原有导入方式
// @ts-ignore
import NotificationCenter from '@/apps/client-pad/components/NotificationCenter/index.vue';
import { useNotificationStore } from '@/modules/notification/stores/notificationStore';
import NotificationButton from '@/apps/client-pad/components/NotificationButton/index.vue';
import { ElMessage } from 'element-plus';
import { ROOM_STATUS, SESSION_TAGS } from '@/modules/room/constants/stageStatus';
import { useUserStore } from '@/stores/userStore';

const router = useRouter();
const timeStore = useTimeStore();
const natsStore = useNatsStore();
const natsService = NatsService.getInstance();
const vm: IRealTimeTableViewModel = useRealTimeTablePresenter();
const roomGridRef = ref();
const userStore = useUserStore();

// 计算当前用户头像 - 优先使用 userStore 中的头像，没有则使用默认头像
const currentUserAvatar = computed(() => {
  return userStore.userInfo.avatar || userAvatar;
});

// 计算当前用户姓名 - 优先使用 userStore 中的姓名，没有则显示默认值
const currentUserName = computed(() => {
  return userStore.userInfo.name || '管理员';
});

// 通知中心相关
const notificationStore = useNotificationStore();
const notificationCenterRef = ref<any>(null);

// 显示通知中心
const showNotificationCenter = () => {
  console.log('Room: 点击通知图标，notificationCenterRef:', notificationCenterRef.value);
  if (notificationCenterRef.value) {
    console.log('Room: 主动调用showDrawer方法');
    // 确保传递参数表明这是用户手动点击，显式传入true
    notificationCenterRef.value.showDrawer(true);
  } else {
    console.error('Room: 消息中心引用为空，无法调用showDrawer方法');
  }
};

const handleStageSelect = (stage: any) => {
  console.log('包厢已选择:', stage);

  // 检查包厢状态是否为"使用中"
  if (stage?.roomStatus !== ROOM_STATUS.IN_USE) {
    ElMessage.warning('只有使用中的包厢才能进行点单操作');
    return;
  }
  if (stage?.tags?.includes(SESSION_TAGS.TIMEOUT)) {
    ElMessage.warning('包厢已超时，无法进行点单操作');
    return;
  }
  if (stage?.tags?.includes(SESSION_TAGS.LOCKED)) {
    ElMessage.warning('包厢已锁定，无法进行点单操作');
    return;
  }

  const roomId = stage?.roomVO?.id;
  const roomName = stage?.roomVO?.name;
  const sessionId = stage?.sessionVO?.sessionId;
  if (roomId) {
    router.push({ path: '/product-order', query: { roomId, roomName, sessionId } });
  } else {
    router.push('/product-order');
    console.warn('包厢信息不完整，已跳转到默认商品点单页');
  }
};

// NATS重连处理方法
const handleNatsReconnect = async () => {
  try {
    if (!natsStore.isConnected) {
      const NatsListenerService = (await import('@/application/natsListenerService')).default;
      await NatsListenerService.triggerReconnect();
      console.log('NATS重连请求已发送');
    }
  } catch (error) {
    console.error('NATS重连失败:', error);
  }
};

// 组件挂载时的处理逻辑
(() => {
  if (typeof vm.setRoomGridRef === 'function') {
    vm.setRoomGridRef(roomGridRef);
  }

  timeStore.startTimer();

  setTimeout(async () => {
    if (!natsStore.isConnected) {
      try {
        const NatsListenerService = (await import('@/application/natsListenerService')).default;
        await NatsListenerService.triggerReconnect();
      } catch (error) {
        console.error('NATS重连出错:', error);
      }
    }
  }, 1000);
})();
</script>

<style scoped>
/* 自定义搜索框样式 */
:deep(.custom-search-input) {
  height: 68px;
  border-radius: 10px;
}

:deep(.custom-search-input .el-input__wrapper) {
  border-radius: 10px;
  height: 68px;
  padding-left: 20px;
  box-shadow: 0 0 0 1px #e0e0e0 inset;
}

:deep(.custom-search-input .el-input__inner) {
  height: 68px;
  font-size: 16px;
}

/* 筛选面板内的按钮样式 */
:deep(.filter-panel .el-button) {
  padding: 8px 4px;
  height: 56px !important;
  white-space: normal;
  line-height: 1.2;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  margin: 0;
}

/* 筛选面板内的按钮文字样式 */
:deep(.filter-panel .el-button span) {
  display: inline-block;
  text-align: center;
  width: 100%;
  word-break: break-word;
}

/* 确保选中的按钮文字颜色可见 */
:deep(.filter-panel .el-button--primary) {
  color: #333 !important;
}

/* 通知徽章样式 */
:deep(.el-badge__content) {
  background-color: #e23939;
}

:deep(.filter-popover) {
  padding: 16px;
  max-width: 100%;
  width: 400px !important;
}

/* 排序下拉菜单样式 */
.sort-dropdown {
  position: relative;
}

/* 排序下拉菜单样式 */
:deep(.customer-dropdown .el-dropdown-menu) {
  padding: 8px;
}

:deep(.customer-dropdown .el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 8px 12px;
}

:deep(.customer-dropdown .el-dropdown-menu__item:hover) {
  background-color: #f5f7fa;
}

:deep(.customer-dropdown .el-dropdown-menu__item i) {
  margin-right: 6px;
}

:deep(.customer-dropdown .el-dropdown__popper) {
  font-size: 32px !important;
}
</style>
