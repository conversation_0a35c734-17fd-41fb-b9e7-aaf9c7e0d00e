import { ComputedRef } from 'vue';
import { ExtendedStageVO } from '@/modules/room/types/extendedStageVO';
import { RoomVO } from '@/types/projectobj';

// 定义UI状态接口
export interface IRealTimeTableState {
  loading: boolean;
  stages: ExtendedStageVO[];
  currentStageId: string | null;
  searchText: string;
  sortType: string;
  filters: string[];
  displayMode: 'grid' | 'list';
  refreshTimers: number[];
  tempFilters: string[]; // 临时筛选条件
  filterPopoverVisible: boolean; // 筛选弹窗可见状态
  selectedButton: string; // 当前选中的视图模式按钮
  retryCount: number; // 添加重试计数
  activeTab: string; // 当前选中的标签页（全部/使用中）
}

// 定义UI计算属性接口
export interface IRealTimeTableComputed {
  stages: ComputedRef<ExtendedStageVO[]>; // 原始stages
  currentStage: ComputedRef<ExtendedStageVO | null>;
  filteredStages: ComputedRef<ExtendedStageVO[]>; // 过滤后的stages
  currentSortLabel: ComputedRef<string>; // 当前排序方式显示文本
  filterGroups: ComputedRef<any[]>; // 筛选选项组
  statusCounts: ComputedRef<{
    idle: number;
    inUse: number;
    timeout: number;
    fault: number;
    cleaning: number;
    withGuest: number;
  }>; // 房间状态计数
}

// 定义UI动作接口
export interface IRealTimeTableActions {
  manualRefresh(): Promise<void>; // 添加手动刷新方法
  fetchStages(): Promise<void>;
  refreshRoomData(): void;
  refreshCurrentStage(stage: ExtendedStageVO): void;
  setupRoomTimers(): void;
  clearAllTimers(): void;

  // 房间操作
  handleStageSelect(stage: ExtendedStageVO): void;
  handleSelectedRoomUpdate(newRoom: RoomVO): void;

  // 筛选、排序与视图切换
  handleSearch(value: string): void;
  handleSortCommand(command: string): void;
  handleFilterCommand(command: string): void;
  clearFilters(): void;
  applyFilters(): void;
  toggleDisplayMode(mode: string): void;

  // Tab切换和用户操作
  handleTabChange(tab: string): void;
  handleAvatarClick(): void;
  handleUserCommand(command: string): void;

  // NATS操作
  handleNatsReconnect(): Promise<void>;
}

// 定义辅助方法接口
export interface IRealTimeTableHelpers {
  processStageData(rawData: any[]): ExtendedStageVO[];
}

// 组合接口
export interface IRealTimeTableViewModel {
  state: IRealTimeTableState;
  computed: IRealTimeTableComputed;
  actions: IRealTimeTableActions;
  helpers: IRealTimeTableHelpers;
  sortOptions: { label: string; value: string }[]; // 排序选项
  setProps?: (props: any) => void;
  setRoomGridRef?: (ref: any) => void;
}
