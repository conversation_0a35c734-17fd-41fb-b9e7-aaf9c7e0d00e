import { RoomApi } from '@/modules/room/api/room';
import { TimeoutType } from '@/application/vodService';
import { useUserStore } from '@/stores/userStore';

export class RealTimeTableInteractor {
  // 获取房间舞台数据
  async fetchStages(): Promise<any[]> {
    try {
      const response = await RoomApi.stageRoom({
        roomId: '',
        typeId: '',
        areaId: '',
        sessionId: ''
      });

      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error('获取房间数据失败: 返回的数据不是数组');
        return [];
      }
    } catch (error) {
      console.error('获取房间数据失败:', error);
      throw error;
    }
  }

  // VOD超时设置
  async vodTimeout(deviceIp: string, type: TimeoutType, time: string): Promise<void> {
    if (!deviceIp) return;
    // try {
    //   await vodService.timeout(deviceIp, type, time);
    // } catch (error) {
    //   console.error('VOD超时设置失败:', error);
    // }
  }

  // 退出登录
  async logout(): Promise<boolean> {
    try {
      const userStore = useUserStore();
      await userStore.logoutAction();
      return true;
    } catch (error) {
      console.error('退出登录失败:', error);
      throw error;
    }
  }
}

export function useRealTimeTableInteractor() {
  return new RealTimeTableInteractor();
}
