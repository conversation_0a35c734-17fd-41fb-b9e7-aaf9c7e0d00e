import { phoneLogin, qrAutoLogin } from '@/api/user';
import { getWxLoginQrcode, checkWxLoginStatus } from '@/api/wechat';
import { queryCashierMachineBindInfo } from '@/api/cashierMachine';
import { postApiVodSettingsQuery } from '@/api/autoGenerated/defaultVersion/vodSettings';
import { UserInfo, useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { setToken, setUserInfoStorage } from '@/utils/authUtils';
import NatsService from '@/services/nats-service';
import { ElMessage } from 'element-plus';
import { useVodService } from '@/application/vodService';
export interface LoginParams {
  phone: string;
  password: string;
  mac: string;
}

export interface QrLoginParams {
  login_key: string;
  user_id: string;
  scene_str: string;
}

export interface WxQrcodeParams {
  venue_id: string;
  mac: string;
}

export interface CheckQrStatusParams {
  scene_str: string;
  login_key: string;
}

export class LoginInteractor {
  private userStore = useUserStore();
  private venueStore = useVenueStore();
  private natsService = NatsService.getInstance();

  // 手机号密码登录
  async phoneLogin(params: LoginParams) {
    return await phoneLogin(params);
  }

  // 获取微信登录二维码
  async getWxLoginQrcode(params: WxQrcodeParams) {
    return await getWxLoginQrcode(params);
  }

  // 检查二维码状态
  async checkWxLoginStatus(params: CheckQrStatusParams) {
    return await checkWxLoginStatus(params);
  }

  // 二维码扫码登录
  async qrAutoLogin(params: QrLoginParams) {
    return await qrAutoLogin(params);
  }

  // 查询收银机绑定信息
  async queryCashierMachineBindInfo(mac: string) {
    return await queryCashierMachineBindInfo({ mac });
  }

  // 处理登录成功
  async handleLoginSuccess(loginResult: any) {
    try {
      const userInfo = {
        name: loginResult.data.erpUserVO.name,
        phone: loginResult.data.erpUserVO.phone,
        employee: loginResult.data.venues[0].loginEmployeeVO,
        venues: loginResult.data.venues[0],
        avatar: loginResult.data.erpUserVO.avatar
      };
      const token = loginResult.data?.token;

      // 保存到 store
      this.userStore.setUserInfo(userInfo);
      this.userStore.setUserToken(token);

      // 保存到 localStorage
      setToken(token);
      setUserInfoStorage(userInfo);

      // 查询VOD设置
      try {
        if (this.venueStore.venueId) {
          const vodSettingsRes = await postApiVodSettingsQuery({
            venueId: this.venueStore.venueId
          });

          if (vodSettingsRes.data && vodSettingsRes.data.length > 0) {
            // 存储VOD设置
            this.venueStore.setVodSettings(vodSettingsRes.data[0]);

            // 更新vodService配置
            if (vodSettingsRes.data[0].vodServerIP) {
              const vodService = useVodService();
              vodService.updateVodConfig({
                appid: this.venueStore.venue?.appId || '',
                appkey: this.venueStore.venue?.appKey || '',
                host: vodSettingsRes.data[0].vodServerIP,
                port: 8008
              });
            }
          }
        }
      } catch (error) {
        console.error('获取VOD设置失败:', error);
      }

      ElMessage.success('登录成功');
      return true;
    } catch (error) {
      console.error('处理登录结果失败:', error);
      ElMessage.error('登录失败，请重试');
      throw error;
    }
  }

  // 连接NATS
  async connectNats() {
    if (this.natsService.getStatus() !== 'connected') {
      return await this.natsService.connect();
    }
  }

  // 订阅NATS主题
  async subscribeNats(topic: string, callback: (data: any) => void) {
    return await this.natsService.subscribe(topic, callback);
  }

  // 取消NATS订阅
  async unsubscribeNats(topic: string) {
    return await this.natsService.unsubscribe(topic);
  }
}

export function useLoginInteractor() {
  return new LoginInteractor();
}
