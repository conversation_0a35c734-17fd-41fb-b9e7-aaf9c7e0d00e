import { FormRules } from 'element-plus'
import { ILoginState } from './viewmodel'

// 常量
const STORAGE_KEY = {
  REMEMBER_PASSWORD: 'remember_password',
  LOGIN_INFO: 'login_info'
}

export class LoginConverter {
  // 创建初始状态
  static createInitialState(): ILoginState {
    const savedLoginInfo = localStorage.getItem(STORAGE_KEY.LOGIN_INFO)
      ? JSON.parse(localStorage.getItem(STORAGE_KEY.LOGIN_INFO)!)
      : null

    return {
      loginForm: {
        phone: savedLoginInfo?.phone || '',
        password: savedLoginInfo?.password || ''
      },
      loginType: 'password',
      loading: false,
      rememberPassword: localStorage.getItem(STORAGE_KEY.REMEMBER_PASSWORD) === 'true',
      qrcodeUrl: '',
      qrcodeExpired: false,
      qrcodeScanned: false,
      sceneStr: '',
      loginKey: '',
      venueCode: '',
      venueName: '',
      ipAddress: ''
    }
  }

  // 验证规则
  static createValidationRules(): FormRules {
    return {
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码不能少于6位', trigger: 'blur' }
      ]
    }
  }

  // 存储记住密码信息
  static saveRememberPassword(state: ILoginState): void {
    if (state.rememberPassword) {
      localStorage.setItem(STORAGE_KEY.LOGIN_INFO, JSON.stringify({
        phone: state.loginForm.phone,
        password: state.loginForm.password
      }))
      localStorage.setItem(STORAGE_KEY.REMEMBER_PASSWORD, 'true')
    } else {
      localStorage.removeItem(STORAGE_KEY.LOGIN_INFO)
      localStorage.setItem(STORAGE_KEY.REMEMBER_PASSWORD, 'false')
    }
  }

  // 将设备信息转换到视图状态
  static updateDeviceInfo(state: ILoginState, deviceInfo: any): void {
    if (deviceInfo.venue) {
      state.venueCode = deviceInfo.venue.id || ''
      state.venueName = deviceInfo.venue.name || ''
    }
    
    state.ipAddress = deviceInfo.ipAddress || ''
  }
} 