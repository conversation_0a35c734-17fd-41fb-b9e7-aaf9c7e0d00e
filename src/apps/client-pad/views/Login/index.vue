<template>
    <div class="relative min-h-screen w-full overflow-hidden bg-[#F5F5F5]">
        <!-- 背景图片 -->
        <div class="absolute inset-0">
            <img src="@/assets/v2/login/bg.webp" alt="背景" class="w-full h-full object-cover"
                style="transform: scaleX(-1);">
        </div>

        <!-- 更新按钮 - 添加在右上角 -->
        <div class="absolute top-4 right-4 z-10">
            <UpdateButton />
        </div>

        <!-- 登录卡片 -->
        <div class="relative min-h-screen flex items-center justify-end">
            <div class="login-card bg-white rounded-[24px] w-[540px] min-h-[720px] max-h-[85vh] shadow-lg flex flex-col mr-[10%] my-auto">
                <!-- 登录表单区域 -->
                <div class="px-[60px] pt-[60px] pb-[40px] flex-1 flex flex-col">
                    <h1 class="text-[28px] font-medium text-[#1D2129] mb-[40px]">登录</h1>

                    <!-- 密码登录表单 -->
                    <div v-if="vm.state.loginType === 'password'" class="flex flex-col flex-1">
                        <div class="flex-1">
                            <el-form ref="loginFormEl" :model="vm.state.loginForm" :rules="vm.loginRules">
                                <el-form-item prop="phone">
                                    <el-input v-model="vm.state.loginForm.phone" placeholder="请输入手机号" class="!h-[60px] !w-[420px]"/>
                                </el-form-item>

                                <el-form-item prop="password" class="mt-[24px]">
                                    <el-input v-model="vm.state.loginForm.password" type="password" placeholder="请输入密码" show-password
                                        class="!h-[60px] !w-[420px]" />
                                </el-form-item>

                                <div class="flex items-center my-[24px]">
                                    <el-checkbox v-model="vm.state.rememberPassword" class="text-[16px]">记住密码</el-checkbox>
                                </div>

                                <el-button type="primary"
                                    class="!w-[420px] !h-[60px] !text-[16px] !font-normal !rounded-[10px]"
                                    :class="{ 'opacity-20 cursor-not-allowed': !vm.computed.isFormValid.value, 'opacity-100': vm.computed.isFormValid.value }"
                                    :loading="vm.state.loading" @click="vm.actions.handleLogin"
                                    :disabled="!vm.computed.isFormValid.value">
                                    登录
                                </el-button>
                            </el-form>
                        </div>

                        <!-- 切换到微信登录 - 位于底部 -->
                        <div class="mb-[40px] mt-auto">
                            <button
                                class="flex items-center justify-center w-[420px] h-[60px] border border-[#E5E6E8] rounded-[10px] hover:bg-[#F7F8FA] transition-colors"
                                @click="vm.actions.toggleLoginType">
                                <img src="@/assets/v2/login/wechat.png" alt="微信" class="w-[24px] h-[24px] mr-[8px]">
                                <span class="text-[16px] text-[#1D2129]">
                                    微信扫码登录
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- 微信扫码登录 -->
                    <div v-else class="flex flex-col flex-1">
                        <div class="flex-1 flex flex-col items-center">
                            <div class="qrcode-wrapper inline-block mb-[32px] relative">
                                <!-- 二维码显示 -->
                                <img v-if="vm.state.qrcodeUrl" :src="vm.state.qrcodeUrl" alt="微信二维码" class="w-[240px] h-[240px]">
                                <div v-else class="w-[240px] h-[240px] flex items-center justify-center">
                                    <el-icon class="animate-spin text-[#C9CDD4] text-[24px]">
                                        <Loading />
                                    </el-icon>
                                </div>

                                <!-- 二维码过期遮罩 -->
                                <div v-if="vm.state.qrcodeExpired"
                                    class="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center">
                                    <div class="text-white text-center">
                                        <p class="mb-[16px] text-[16px]">二维码已过期</p>
                                        <el-button type="primary" size="large" @click="vm.actions.refreshQrcode"
                                            class="!rounded-[10px]">
                                            刷新二维码
                                        </el-button>
                                    </div>
                                </div>
                                <!-- 已扫码遮罩 -->
                                <div v-else-if="vm.state.qrcodeScanned"
                                    class="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center">
                                    <p class="text-white text-[16px]">请在手机上确认登录</p>
                                </div>
                            </div>
                            <p class="text-[16px] text-[#86909C] mb-[32px]">请使用微信扫码登录</p>
                        </div>

                        <!-- 切换到账号登录 - 位于底部 -->
                        <div class="mb-[40px] mt-auto">
                            <button
                                class="flex items-center justify-center w-[420px] h-[60px] border border-[#E5E6E8] rounded-[10px] hover:bg-[#F7F8FA] transition-colors"
                                @click="vm.actions.toggleLoginType">
                                <span class="text-[16px] text-[#1D2129]">
                                    手机号登录
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备信息区域 -->
        <div class="device-info-container absolute bottom-[30px] right-[30px] z-10">
            <div class="device-info-frame flex flex-col items-end p-2">
                <div v-if="vm.state.venueCode" class="device-info-item flex items-center">
                    <div class="device-info-value">{{ vm.state.venueCode }}</div>
                    <div class="device-info-label">：门店编号</div>
                </div>
                <div v-if="vm.state.venueName" class="device-info-item flex items-center">
                    <div class="device-info-value">{{ vm.state.venueName }}</div>
                    <div class="device-info-label">：门店名称</div>
                </div>
                <div v-if="vm.state.ipAddress" class="device-info-item flex items-center">
                    <div class="device-info-value">{{ vm.state.ipAddress }}</div>
                    <div class="device-info-label">:&nbsp;&nbsp;&nbsp;&nbsp; IP 地址</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Phone, Lock, Loading } from '@element-plus/icons-vue'
import UpdateButton from '@/components/UpdateButton.vue'
import { useLoginPresenter } from './presenter'
import type { ILoginViewModel } from './viewmodel'
import type { FormInstance } from 'element-plus'
import { onMounted, ref } from 'vue'

// 使用VIPER-VueC架构的Presenter作为ViewModel
const vm: ILoginViewModel = useLoginPresenter()
const loginFormEl = ref<FormInstance>()

// 在组件挂载后设置表单引用
onMounted(() => {
  vm.refs.loginFormRef = loginFormEl.value || undefined
})
</script>

<style scoped>
:deep(.el-input__wrapper) {
    @apply mb-0 border border-[#E5E6E8] shadow-none bg-white rounded-[8px] !h-[60px];
}

:deep(.el-input__wrapper.is-focus) {
    @apply ring-1 ring-[#165DFF] border-[#165DFF];
}

:deep(.el-input__wrapper:hover) {
    @apply border-[#165DFF];
}

:deep(.el-checkbox__label) {
    @apply text-[#4E5969] text-[16px];
}

:deep(.el-button--primary) {
    @apply bg-[#165DFF] hover:bg-[#0E42D2] border-none text-white;
}

:deep(.el-form-item) {
    @apply mb-0;
}

:deep(.el-input__inner) {
    @apply text-[16px] text-[#1D2129] h-[58px] leading-[58px];
}

:deep(.el-input__inner::placeholder) {
    @apply text-[#86909C];
}

:deep(.el-checkbox__inner) {
    @apply w-[16px] h-[16px] border-[#C9CDD4];
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    @apply bg-[#165DFF] border-[#165DFF];
}

:deep(.el-input__prefix-inner) {
    @apply text-[20px];
}

/* 登录卡片响应式样式 */
.login-card {
    max-width: calc(100% - 120px);
    transition: all 0.3s ease;
}

/* 设备信息样式 */
.device-info-container {
    max-width: 300px;
    transition: all 0.3s ease;
}

.device-info-frame {
    @apply flex flex-col;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.device-info-item {
    @apply flex items-center my-[4px];
}

.device-info-value {
    @apply text-[15px] text-[#fb7d6a] font-medium text-right max-w-[200px] truncate;
}

.device-info-label {
    @apply text-[15px] text-[#fb7d6a] font-medium whitespace-nowrap;
}

/* 响应式样式 */
@media (max-height: 900px) {
    .device-info-value, .device-info-label {
        @apply text-[13px];
    }
    
    .device-info-item {
        @apply my-[3px];
    }
}

@media (max-width: 1366px) {
    .login-card {
        @apply mr-[8%];
    }
    
    .device-info-container {
        @apply bottom-[15px] right-[15px];
    }
}

@media (max-width: 1200px) {
    .login-card {
        @apply mr-[60px];
    }
    
    .device-info-value {
        @apply max-w-[150px];
    }
}

@media (max-width: 992px) {
    .login-card {
        @apply mr-[40px] w-[480px];
    }
    
    .device-info-container {
        @apply bottom-[10px] right-[10px];
    }
    
    .device-info-value, .device-info-label {
        @apply text-[12px];
    }
}

@media (max-height: 768px) {
    .device-info-container {
        @apply bottom-[10px];
    }
    
    .login-card {
        @apply max-h-[90vh];
    }
}

@media (max-height: 700px) {
    .device-info-value, .device-info-label {
        @apply text-[11px];
    }
    
    .device-info-item {
        @apply my-[2px];
    }
}
</style> 