import { postApiVenueShouyinAuth, VenueCashMachineAuthResultVO, AddVenueShouyinAuthReqDto } from '@/api/autoGenerated';

/**
 * AuthView 业务交互层
 *
 * 负责处理与后端服务的交互和业务逻辑
 */
export class AuthViewInteractor {
  /**
   * 发送设备授权请求到服务器
   * @param params 授权请求参数, 类型为 AddVenueShouyinAuthReqDto
   * @returns 授权响应
   */
  static async authorize(params: AddVenueShouyinAuthReqDto): Promise<VenueCashMachineAuthResultVO> {
    try {
      // 调用后端授权接口
      const response = await postApiVenueShouyinAuth(params); // Pass params directly
      // response.data is already VenueCashMachineAuthResultVO as per postApiVenueShouyinAuth return type
      return response.data;
    } catch (error) {
      console.error('[AuthViewInteractor] 授权请求失败:', error);
      throw error; // 将错误抛出，由Presenter处理
    }
  }
}
