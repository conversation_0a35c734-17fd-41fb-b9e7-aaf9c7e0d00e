import { Ref, ComputedRef } from 'vue';

/**
 * 设备授权页面视图模型接口定义
 */
export interface IAuthViewModel {
  // 状态 (Refs for mutable state, ComputedRefs for derived state)
  authChecking: Ref<boolean>;
  loading: Ref<boolean>; // For QR code loading or other general loading states within the view
  loginLoading: Ref<boolean>; // Specifically for the login button's loading state
  qrCodeData: Ref<string>;
  isAuthorized: Ref<boolean>;

  // 计算属性 (Computed Properties)
  ipAddress: ComputedRef<string>; // Typically derived from a store, hence ComputedRef
  macAddress: ComputedRef<string>; // Add macAddress here
  venueName: ComputedRef<string>; // Derived from a store

  // 方法
  checkAuth: () => Promise<boolean>;
  handleLogin: () => Promise<void>;
  // initializeDeviceInfoFromUrl: () => void; // Private method in presenter, not part of view model interface
  // checkInitialAuth: () => Promise<void>;    // Private method in presenter
}
