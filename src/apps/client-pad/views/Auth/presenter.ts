import { ref, Ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { IAuthViewModel } from './viewmodel';
import { AuthViewInteractor } from './interactor';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { AddVenueShouyinAuthReqDto, VenueCashMachineAuthResultVO } from '@/api/autoGenerated';

/**
 * AuthView Presenter 类
 *
 * 处理业务逻辑并协调视图与业务层的交互
 */
export class AuthViewPresenter implements IAuthViewModel {
  // 状态
  authChecking: Ref<boolean> = ref(true);
  loading: Ref<boolean> = ref(false);
  loginLoading: Ref<boolean> = ref(false);
  qrCodeData: Ref<string> = ref('');
  isAuthorized: Ref<boolean> = ref(false); // 授权状态追踪

  // 设备信息
  private deviceStore = useDeviceStore();
  private venueStore = useVenueStore();
  ipAddress = computed(() => this.deviceStore.ipAddress);
  macAddress = computed(() => this.deviceStore.macAddress);
  venueName = computed(() => this.venueStore.venue?.name || '未授权门店');

  // 手动授权相关
  venueCode: Ref<string> = ref('');
  deviceCode: Ref<string> = ref('');

  // 注入路由器
  private router = useRouter();

  constructor() {
    this.initializeDeviceInfoFromUrl();
    this.checkInitialAuth();

    // 监听 deviceStore 的 macAddress 和 ipAddress 初始化完成
    // electron环境下，这些值可能是异步获取的
    watch(
      () => [this.deviceStore.macAddress, this.deviceStore.ipAddress],
      ([newMac, newIp], [oldMac, oldIp]) => {
        console.log('[AuthViewPresenter] Device info changed in store:', { newMac, newIp, oldMac, oldIp });
        // 确保只在从未初始化到初始化时触发一次关键的授权检查
        // 或者当它们从无效值变更为有效值时
        if ((!oldMac && newMac && newIp && newMac !== '00:00:00:00:00:00') || (!oldIp && newIp && newMac && newMac !== '00:00:00:00:00:00')) {
          if (!this.venueStore.venueId) {
            console.log('[AuthViewPresenter] IP/MAC initialized, re-checking auth.');
            this.checkAuth(); // IP或MAC地址首次有效时，重新检查授权
          }
        }
      },
      { immediate: false } // constructor中已调用checkInitialAuth，避免立即执行
    );
  }

  private initializeDeviceInfoFromUrl(): void {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('deviceId')) {
      this.deviceStore.setDeviceId(urlParams.get('deviceId') || '');
    }
    if (urlParams.has('macAddress')) {
      this.deviceStore.setMacAddress(urlParams.get('macAddress') || '');
    }
    if (urlParams.has('ipAddress')) {
      this.deviceStore.setIpAddress(urlParams.get('ipAddress') || '');
    }
    if (urlParams.has('location')) {
      this.deviceStore.setLocation(urlParams.get('location') || '');
    }
    if (urlParams.has('deviceType')) {
      this.deviceStore.setDeviceType(urlParams.get('deviceType') || '');
    }
    if (urlParams.has('appVersion')) {
      this.deviceStore.setAppVersion(urlParams.get('appVersion') || '');
    }
    if (urlParams.has('appVersionCode')) {
      this.deviceStore.setAppVersionCode(urlParams.get('appVersionCode') || '');
    }
    // venueId 从URL获取的优先级较低，checkAuth会从API获取并覆盖
    // if (urlParams.has('venueId')) {
    //   this.deviceStore.setVenueId(urlParams.get('venueId') || '');
    // }
  }

  /**
   * 初始检查设备授权状态
   */
  private async checkInitialAuth(): Promise<void> {
    console.log('[AuthViewPresenter] Initializing auth check...');
    console.log('[AuthViewPresenter] Initial device info:', {
      ip: this.deviceStore.ipAddress,
      mac: this.deviceStore.macAddress,
      venueId: this.venueStore.venueId
    });

    // 如果 store 中已经有 venueId，我们认为之前可能已授权，验证一下
    if (this.venueStore.venueId) {
      console.log('[AuthViewPresenter] VenueId exists, assuming authorized, verifying...');
      this.isAuthorized.value = true; // 初步假设
      await this.checkAuth(); // 验证此授权
    } else {
      // 如果没有venueId，并且IP和MAC地址已经存在，则尝试获取授权
      if (this.deviceStore.ipAddress && this.deviceStore.macAddress && this.deviceStore.macAddress !== '00:00:00:00:00:00') {
        console.log('[AuthViewPresenter] No VenueId, but IP/MAC exist. Checking auth...');
        await this.checkAuth();
      } else {
        console.log('[AuthViewPresenter] No VenueId, IP/MAC missing. Waiting for IP/MAC from store watch...');
        this.authChecking.value = true; // 持续检查状态，直到IP/MAC获取到
        // 等待 watch 回调来触发 checkAuth
      }
    }
  }

  private storeAuthResponse(responseData: VenueCashMachineAuthResultVO): void {
    // Determine the final venueId, preferring the top-level one if available.
    const finalVenueId = responseData.venueId ?? responseData.venue?.id ?? '';
    if (finalVenueId) {
      // Ensure finalVenueId is not empty if it's critical, or rely on store validation
      this.venueStore.setVenueId(finalVenueId);
    }

    if (responseData.cashierMachine) {
      // Construct machineData using properties from responseData.cashierMachine.
      // Provide defaults for fields that are optional in API but potentially required by setCashierMachine.
      const machineData = {
        id: responseData.cashierMachine.id ?? '', // Assuming id is expected as string by the store
        grantIp: responseData.cashierMachine.grantIp,
        mac: responseData.cashierMachine.mac,
        name: responseData.cashierMachine.name
        // Other properties from responseData.cashierMachine can be added if setCashierMachine expects them.
        // Example: status: responseData.cashierMachine.status ?? 0, (if status is a number)
      };
      this.deviceStore.setCashierMachine(machineData);
    }

    if (responseData.venue) {
      // Construct venueData similarly.
      const venueData = {
        id: responseData.venue.id ?? finalVenueId, // Use finalVenueId as fallback if venue.id is missing
        name: responseData.venue.name
        // Example: address: responseData.venue.address ?? '',
      };
      // Ensure venueData.id is not an empty string if setVenue has strict requirements for a valid ID.
      // If finalVenueId was determined to be '' and responseData.venue.id is also null/undefined,
      // venueData.id will be ''. This might need further validation based on store logic.
      if (venueData.id) {
        // Optional: check if ID is non-empty before setting
        this.venueStore.setVenue(venueData);
      }
    }
  }

  /**
   * 检查设备授权状态 - 与PC端保持一致的实现
   */
  async checkAuth(): Promise<boolean> {
    this.authChecking.value = true;
    this.loading.value = true; // 二维码区域加载状态

    const currentIp = this.deviceStore.ipAddress;
    const currentMac = this.deviceStore.macAddress;

    if (!currentIp || currentIp === '0.0.0.0' || !currentMac || currentMac === '00:00:00:00:00:00') {
      console.warn('[AuthViewPresenter] Invalid IP or MAC address for auth check:', { currentIp, currentMac });
      ElMessage.warning('设备IP或MAC地址无效，无法检查授权。请确保网络连接正常或稍后重试。');
      this.authChecking.value = false;
      this.loading.value = false;
      this.isAuthorized.value = false; // 明确未授权
      this.qrCodeData.value = ''; // 清空二维码
      return false;
    }

    console.log('[AuthViewPresenter] Checking auth with IP:', currentIp, 'MAC:', 'pad:' + currentMac);

    try {
      const authRequest: AddVenueShouyinAuthReqDto = {
        grantIp: currentIp,
        mac: 'pad:' + currentMac,
        clientType: 'pad'
      };
      const responseData = await AuthViewInteractor.authorize(authRequest);

      console.log('[AuthViewPresenter] Auth response data:', responseData);

      if (responseData.venueId) {
        if (responseData.lastIP && responseData.cashierMachine?.grantIp && responseData.lastIP !== responseData.cashierMachine.grantIp) {
          const confirmed = await ElMessageBox.confirm(
            `检测到IP地址已变更<br/>原IP: ${responseData.lastIP}<br/>当前IP: ${responseData.cashierMachine.grantIp}<br/>是否确认使用当前IP继续？`,
            'IP地址变更提示',
            {
              confirmButtonText: '确认使用当前IP',
              cancelButtonText: '取消操作',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }
          ).catch(() => false);

          if (!confirmed) {
            ElMessage.info('操作已取消。请检查网络配置或联系管理员。');
            this.isAuthorized.value = false; // 因为IP变更未确认，视为未授权
            this.qrCodeData.value = ''; // 清空二维码，避免误导
            return false;
          }
        }
        // IP一致或用户确认变更后，处理授权信息
        this.storeAuthResponse(responseData);
        this.isAuthorized.value = true;
        this.qrCodeData.value = ''; // 已授权，清除二维码
        console.log('[AuthViewPresenter] Device authorized. Venue ID:', this.venueStore.venueId, 'Name:', this.venueStore.venue?.name);
        ElMessage.success(`设备已授权：${this.venueStore.venue?.name || '门店'}`);
        return true;
      } else if (responseData.qrCodeUrl) {
        this.qrCodeData.value = responseData.qrCodeUrl;
        this.isAuthorized.value = false;
        this.storeAuthResponse(responseData); // 即使只有二维码，也尝试更新store中的其他可能信息（如lastIP）
        console.log('[AuthViewPresenter] Authorization required. QR code received.');
        ElMessage.info('设备未授权，请使用微信扫描二维码完成配置。');
        return false;
      } else {
        // 未知情况，既没有venueId也没有qrCodeUrl
        console.warn('[AuthViewPresenter] Auth response invalid:', responseData);
        ElMessage.error('授权响应无效，请稍后重试或联系技术支持。');
        this.isAuthorized.value = false;
        this.qrCodeData.value = '';
        return false;
      }
    } catch (error: any) {
      console.error('[AuthViewPresenter] checkAuth error:', error);
      ElMessage.error(error.message || '检查授权失败，请检查网络连接或联系技术支持。');
      this.isAuthorized.value = false;
      this.qrCodeData.value = '';
      return false;
    } finally {
      this.authChecking.value = false;
      this.loading.value = false;
    }
  }

  /**
   * 跳转到登录页面
   */
  handleLogin = async (): Promise<void> => {
    try {
      // 防止重复点击和处理
      if (this.loginLoading?.value) {
        return;
      }

      // 增加安全检查
      if (!this.loginLoading) {
        console.error('[AuthViewPresenter] loginLoading is undefined, re-initializing...');
        this.loginLoading = ref(false);
      }

      this.loginLoading.value = true;
      console.log('[AuthViewPresenter] Handle login. Current authorization status:', this.isAuthorized?.value);

      // 再次检查授权状态，以防万一
      if (!this.isAuthorized?.value || !this.venueStore?.venueId) {
        console.log('[AuthViewPresenter] Not authorized or no venueId. Re-checking auth before login attempt...');
        const authSuccess = await this.checkAuth();
        if (!authSuccess) {
          ElMessage.warning('设备授权失败或未完成，请先完成授权再登录。');
          return;
        }
      }

      // 确保在跳转前 deviceStore 中有 venueId
      if (this.venueStore?.venueId) {
        console.log('[AuthViewPresenter] Authorized and venueId present. Navigating to /login.');
        this.router.push('/login');
      } else {
        // 这种情况理论上在上面的 checkAuth 后不应该发生，但作为保险
        console.error('[AuthViewPresenter] Critical: Authorized but venueId is missing before login navigation!');
        ElMessage.error('授权状态异常，无法登录。请重试或联系技术支持。');
      }
    } catch (error: any) {
      console.error('[AuthViewPresenter] Login navigation error:', error);
      ElMessage.error(error.message || '登录跳转失败，请稍后重试。');
    } finally {
      // 安全地重置loading状态
      if (this.loginLoading) {
        this.loginLoading.value = false;
      }
    }
  };
}
