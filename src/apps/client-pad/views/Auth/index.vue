<script setup lang="ts">
import { onMounted, onBeforeUnmount, provide } from 'vue';
import { AuthViewPresenter } from './presenter';
// import { IAuthViewModel } from './viewmodel'; // IAuthViewModel 通常由 Presenter 实现，在provide时不需要显式导入类型
import { Loading } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

// 创建Presenter实例
const presenter = new AuthViewPresenter();

// 安全的方法调用，确保this上下文正确
const safeHandleLogin = () => {
  try {
    console.log('[AuthView/index.vue] Safe handle login invoked');
    // 增加额外的安全检查
    if (presenter && typeof presenter.handleLogin === 'function') {
      presenter.handleLogin();
    } else {
      console.error('[AuthView/index.vue] presenter.handleLogin is not a function');
      ElMessage.error('系统错误，请刷新页面后重试');
    }
  } catch (error) {
    console.error('[AuthView/index.vue] Safe handle login error:', error);
    ElMessage.error('登录跳转失败，请刷新页面后重试');
  }
};

// 初始化页面
onMounted(async () => {
  console.log('[AuthView/index.vue] Mounted');
  // Presenter的constructor中已经调用了初始化逻辑 (checkInitialAuth)
  // 如果需要，可以在这里调用 presenter 上的特定方法，但当前设计中，presenter 自管理其初始化
});

onBeforeUnmount(() => {
  console.log('[AuthView/index.vue] Before unmount');
  // 如果 presenter 需要清理资源，可以在这里调用 presenter.dispose() 或类似方法
});

// 修改组件名称
defineOptions({
  name: 'AuthView' // 与文件名一致，或更具体的业务名称
});

// 暴露presenter的属性和方法给模板使用
// 解构时使用 toRefs 或者直接访问 presenter.property 以保持响应性
// const { authChecking, loading, loginLoading, qrCodeData, ipAddress, isAuthorized, handleLogin, venueName } = toRefs(presenter); // 如果presenter的属性是ref
// 或者，如果presenter的属性本身就是ref，则可以直接解构，Vue会自动处理
const { authChecking, loading, loginLoading, qrCodeData, ipAddress, isAuthorized, venueName, macAddress } = presenter;
// 注意：不再解构handleLogin方法，而是直接使用presenter.handleLogin

// 用于依赖注入，允许子组件访问视图模型 (presenter 实例)
provide('authViewModel', presenter);
</script>

<template>
  <div class="relative min-h-screen w-full overflow-hidden bg-[#F5F5F5]">
    <!-- 背景图片 -->
    <div class="absolute inset-0 z-0">
      <img src="@/assets/v2/login/bg.webp" alt="背景" class="w-full h-full object-cover opacity-50" style="transform: scaleX(-1)" />
    </div>

    <!-- 授权检查中的遮罩 -->
    <div v-if="authChecking" class="fixed inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center flex-col">
      <el-icon class="animate-spin text-[#000] text-4xl mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 text-lg">正在检查授权状态，请稍候...</p>
    </div>

    <!-- 顶部IP显示 -->
    <div class="bg-[#B37A51] text-white px-4 py-2 rounded-br-lg absolute top-0 left-0 z-10">
      当前IP：{{ ipAddress }} <span v-if="venueName && venueName !== '未授权门店'"> | {{ venueName }}</span>
    </div>

    <!-- 主要内容区域 -->
    <div class="relative z-10 flex-1 flex flex-col items-center justify-center p-8 w-full h-screen">
      <div class="text-center mb-8">
        <span class="text-[60px] font-[400]">欢迎使用</span>
        <span class="text-[60px] font-[600] text-[#B37A51] ml-[20px]">汇金Pro</span>
        <span class="text-[60px] font-[400] ml-[20px]">移动点单系统</span>
      </div>

      <!-- 已授权状态 -->
      <div v-if="isAuthorized" class="mt-[52px] text-center">
        <el-result icon="success" title="设备已授权成功" :sub-title="`门店：${venueName}`">
          <template #extra>
            <el-button type="primary" class="mt-4 w-[400px] h-[80px] text-[24px] text-[#fff]" @click="safeHandleLogin" :loading="loginLoading">
              进入登录页面
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 未授权状态，显示二维码 -->
      <template v-else-if="!authChecking">
        <!-- 仅在非authChecking时显示二维码或提示 -->
        <div class="text-center">
          <span class="text-[32px] text-[#000] mt-[28px]">先完成门店创建，再绑定此设备</span>
        </div>
        <!-- 二维码区域 -->
        <div class="mt-[52px]">
          <div class="flex flex-col items-center text-center rounded-lg shadow-md w-[330px] h-[330px] bg-white justify-center">
            <div v-if="loading" class="w-[252px] h-[252px] flex items-center justify-center">
              <el-icon class="animate-spin text-blue-500 text-3xl">
                <Loading />
              </el-icon>
              <span class="ml-2 text-gray-500">二维码生成中...</span>
            </div>
            <img v-else-if="qrCodeData" :src="qrCodeData" alt="QR Code" class="w-[252px] h-[252px] mx-auto" />
            <div v-else class="w-[252px] h-[252px] mx-auto flex items-center justify-center text-gray-400 flex-col">
              <p>无法加载二维码</p>
              <el-button type="text" @click="presenter.checkAuth()" class="mt-2">点击重试</el-button>
            </div>
            <p class="text-gray-600 mt-2">打开微信扫一扫完成设备授权</p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <el-button
          type="primary"
          class="mt-[52px] w-[400px] h-[80px] text-[24px] text-[#fff]"
          @click="safeHandleLogin"
          :loading="loginLoading"
          :disabled="!qrCodeData && !isAuthorized">
          <!-- 如果没有二维码且未授权，则禁用 -->
          门店配置完成，前往登录
        </el-button>
        <p v-if="!qrCodeData && !isAuthorized && !loading" class="text-sm text-gray-500 mt-2">如二维码未显示，请检查网络或尝试刷新授权。</p>
        <el-button link type="primary" @click="presenter.checkAuth()" class="mt-4">刷新授权状态</el-button>
      </template>

      <!-- 初始加载，IP/MAC 未获取时，可以给个友好提示 -->
      <div
        v-if="
          !ipAddress ||
          ipAddress === '' ||
          ipAddress === '0.0.0.0' ||
          macAddress === '' ||
          (macAddress === '00:00:00:00:00:00' && !authChecking && !isAuthorized && !qrCodeData)
        "
        class="mt-8 text-center text-gray-500">
        正在获取设备信息，请稍候... 如果长时间未响应，请检查网络连接。
      </div>
    </div>
  </div>
</template>

<style scoped>
/* .auth-view class was present in the original file but seemed unused after migration to Tailwind, so removed. */
/* Add any component-specific styles here if absolutely necessary, prefer Tailwind utility classes. */
</style>
