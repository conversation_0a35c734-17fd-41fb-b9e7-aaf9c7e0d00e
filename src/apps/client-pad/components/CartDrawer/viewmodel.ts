import { ComputedRef } from 'vue';
import { CartItem } from '@/apps/client-pad/views/ProductOrder/viewmodel';

// 购物车状态接口
export interface ICartDrawerState {
  visible: boolean;
  title: string;
  cartItems: CartItem[];
}

// 计算属性接口
export interface ICartDrawerComputed {
  totalAmount: ComputedRef<number>;
  totalItems: ComputedRef<number>;
  isEmpty: ComputedRef<boolean>;
}

// 动作接口
export interface ICartDrawerActions {
  open(): void;
  close(): void;
  increaseQuantity(item: CartItem): void;
  decreaseQuantity(item: CartItem): void;
  clearCart(): void;
  confirmOrder(): void;
  closeDialog?(): void;
}

// 总的ViewModel接口
export interface ICartDrawerViewModel {
  state: ICartDrawerState;
  computed: ICartDrawerComputed;
  actions: ICartDrawerActions;
}
