import { CartItem } from '@/apps/client-pad/views/ProductOrder/viewmodel';
import { postApiV3OrderAdditionalOrder } from '@/api/autoGenerated/v3Version/order';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { ElMessage } from 'element-plus';
import { CartDrawerConverter } from './converter';
import { printingService } from '@/application/printingService';
import { OrderApi } from '@/modules/order/api/order';

/**
 * 购物车交互器类
 * 处理与业务数据的交互
 */
export class CartDrawerInteractor {
  /**
   * 提交订单
   * @param cartItems 购物车商品列表
   * @param roomId 房间ID
   * @param sessionId 会话ID
   * @param employeeId 员工ID
   * @returns 提交结果
   */
  public static async submitOrder(cartItems: CartItem[], roomId: string, sessionId: string, employeeId: string = ''): Promise<boolean> {
    try {
      console.log('调用立即下单API', { cartItems, roomId, sessionId, employeeId });

      // 获取设备门店信息
      const venueStore = useVenueStore();
      const venueId = venueStore.venueId;

      if (!venueId) {
        console.error('未找到门店ID');
        return false;
      }

      // 计算订单金额
      const totalAmount = CartDrawerConverter.calculateTotalAmount(cartItems);

      // 转换商品数据为API参数格式
      const orderProductVOs = cartItems.map(item => {
        let packageProductInfo: string | undefined = undefined;

        // 如果是套餐，需要将packageDetail转换为符合API要求的格式
        if (item.isPackage && item.packageDetail) {
          const baseProductInfoArray = CartDrawerConverter.convertPackageDetailToBaseProductInfo(item.packageDetail);
          if (baseProductInfoArray.length > 0) {
            packageProductInfo = JSON.stringify(baseProductInfoArray);
            console.log(`[CartDrawerInteractor] 套餐商品 ${item.name} 的 packageProductInfo:`, baseProductInfoArray);
            console.log(`[CartDrawerInteractor] 序列化后的 packageProductInfo:`, packageProductInfo);
          }
        }

        const orderProduct: any = {
          id: '',
          venueId: item.isPackage ? '' : venueId,
          roomId: item.isPackage ? '' : roomId,
          sessionId: item.isPackage ? '' : sessionId,
          productName: item.name,
          flavors: '',
          quantity: item.quantity,
          unit: item.isPackage ? '套餐' : item.unit || '份',
          payPrice: item.currentPrice,
          originalPrice: item.currentPrice,
          payAmount: item.currentPrice * item.quantity,
          originalAmount: item.currentPrice * item.quantity,
          payStatus: 'unpaid',
          inPackageTag: item.isPackage ? 'yes' : 'no'
        };

        // 根据是否是套餐决定使用 packageId 还是 productId
        if (item.isPackage) {
          orderProduct.packageId = item.id;
          orderProduct.packageProductInfo = packageProductInfo;
        } else {
          orderProduct.productId = item.id;
        }

        return orderProduct;
      });

      // 构建请求参数
      const params = {
        sessionId,
        venueId,
        roomId,
        employeeId,
        payAmount: totalAmount,
        originalAmount: totalAmount,
        orderProductVOs
      };

      console.log('订单立结参数:', JSON.stringify(params, null, 2));

      // 调用订单立结API
      const response = await OrderApi.additionalOrderV3(params);

      if (response && response.code === 0) {
        console.log('下单成功:', response);

        // 下单成功后打印出品单
        try {
          console.log('下单成功，准备打印出品单: ', response.data);
          // 根据API响应结构获取订单号
          // postApiV3OrderAdditionalOrder 返回的是 OrderVO，只有单个 orderNo
          // 但这里我们需要构建数组格式以兼容打印方法
          const orderNos = response.data.orderNos;

          if (orderNos && orderNos.length > 0) {
            console.log('移动端准备打印出品单，订单号:', orderNos, '会话ID:', sessionId);
            // 调用打印方法
            this.printProductionOrder(orderNos, sessionId);
          } else {
            console.log('移动端没有订单号，跳过出品单打印');
          }
        } catch (printError) {
          console.error('移动端打印出品单失败:', printError);
          // 打印失败不影响下单流程
        }

        return true;
      } else {
        console.error('下单失败:', response);
        const errorMsg = typeof response === 'object' && 'message' in response ? String(response.message) : '下单失败';
        ElMessage.error(errorMsg);
        return false;
      }
    } catch (error) {
      console.error('下单出错:', error);
      ElMessage.error('下单出错，请重试');
      return false;
    }
  }

  /**
   * 打印出品单 (异步，不阻塞调用者)
   * 用于下单成功后调用，打印出品单
   * @param orderNos 订单号列表
   * @param sessionId 会话ID（可选）
   */
  private static async printProductionOrder(orderNos: string[] | undefined, sessionId?: string): Promise<void> {
    try {
      if (!orderNos || orderNos.length === 0) {
        console.warn('CartDrawerInteractor: 没有可打印出品单的订单号');
        return;
      }

      console.log(`CartDrawerInteractor: 准备打印出品单 (异步)，订单号: ${orderNos.join(', ')}, 会话ID: ${sessionId || '无'}`);

      // 调用printingService的printProductOutBySessionId方法，不等待结果
      printingService
        .printProductOutBySessionId(sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('CartDrawerInteractor: 出品单打印任务已发送');
          } else {
            console.error('CartDrawerInteractor: 出品单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('CartDrawerInteractor: 打印出品单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('CartDrawerInteractor: 准备打印出品单时出错:', error);
      // 即使内部出错，也不阻塞外部调用
    }
  }
}
