<!-- 
  CartDrawer/index.vue
  购物车侧边栏组件
-->
<template>
  <el-drawer v-model="visible" :title="title" direction="rtl" :with-header="false" size="43%" :destroy-on-close="false" @close="close">
    <div class="cart-drawer justify-between">
      <!-- 调整头部区域布局 -->
      <div class="drawer-header">
        <div class="left-section">
          <span class="cart-count">购物车</span>
          <span class="cart-clear text-[26px] flex items-center ml-[4px]" @click="clearCart">
            <DashIcon class="cart-clear text-[32px]" />
            清空
          </span>
        </div>
        <div class="drawer-close">
          <el-icon class="close-icon" @click="close"><Close /></el-icon>
        </div>
      </div>

      <!-- 购物车为空状态 -->
      <el-empty v-if="isEmpty" description="购物车为空">
        <template #description>
          <p>您的购物车还是空的，去选购商品吧~</p>
        </template>
      </el-empty>

      <!-- 购物车商品列表 -->
      <div v-else class="cart-items flex-1 overflow-y-auto pt-[24px]">
        <div class="cart-list">
          <div v-for="item in cartItems" :key="item.id" class="cart-item">
            <!-- 商品图片 -->
            <div class="item-image">
              <img :src="item.imageUrl" :alt="item.name" @error="handleImageError" />
            </div>

            <div class="item-content">
              <div class="item-info">
                <div class="item-name">{{ item.name }}</div>
                <!-- 套餐明细 -->
                <div v-if="item.isPackage && item.packageSelectedItems" class="item-package-detail">已选: {{ item.packageSelectedItems }}</div>
              </div>

              <div class="item-actions">
                <div class="item-price">¥{{ (item.currentPrice / 100).toFixed(2) }}/{{ item.unit }}</div>
                <div class="item-quantity">
                  <div class="quantity-control">
                    <div class="minus-btn" @click="decreaseQuantity(item)"></div>
                    <div class="quantity-display">{{ item.quantity }}</div>
                    <div class="plus-btn" @click="increaseQuantity(item)"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部结算区域 -->
      <div class="cart-footer">
        <div class="total-amount">
          <span class="label">待结总计:</span>
          <span class="amount">{{ (totalAmount / 100).toFixed(2) }}</span>
        </div>

        <el-button type="primary" class="checkout-btn" :disabled="isEmpty" @click="confirmOrder"> 立即下单 </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { useCartDrawer } from './presenter';
import { computed, reactive, toRefs } from 'vue';
import { CartDrawerConverter } from './converter';
import { handleImageError } from '@/utils/imageUtils';
import DashIcon from '@/assets/v3/dash.vue';
import CloseIcon from '@/assets/v3/close.vue';

// 辅助函数：将分转为元并格式化
const fenToYuanFormatted = priceInFen => {
  return (priceInFen / 100).toFixed(2);
};

export default {
  name: 'CartDrawer',
  components: {
    CloseIcon,
    DashIcon
  },
  props: {
    productOrderActions: {
      type: Object,
      required: true
    },
    roomId: {
      type: String,
      required: true
    },
    sessionId: {
      type: String,
      required: true
    }
  },
  emits: ['close', 'order-confirmed'],
  setup(props, { emit, expose }) {
    const viewModel = useCartDrawer(emit, props.productOrderActions, props.roomId, props.sessionId);

    // 暴露方法
    expose({
      open: viewModel.actions.open,
      close: viewModel.actions.close
    });

    // 提取计算属性
    const totalAmount = computed(() => viewModel.computed.totalAmount.value);
    const totalItems = computed(() => viewModel.computed.totalItems.value);
    const isEmpty = computed(() => viewModel.computed.isEmpty.value);

    // 提取方法
    const increaseQuantity = viewModel.actions.increaseQuantity;
    const decreaseQuantity = viewModel.actions.decreaseQuantity;
    const clearCart = viewModel.actions.clearCart;
    const confirmOrder = viewModel.actions.confirmOrder;
    const close = viewModel.actions.close;

    // 提取状态
    const { cartItems, title, visible } = toRefs(viewModel.state);

    return {
      // 状态
      cartItems,
      title,
      visible,

      // 计算属性
      totalAmount,
      totalItems,
      isEmpty,

      // 方法
      increaseQuantity,
      decreaseQuantity,
      clearCart,
      confirmOrder,
      close,
      handleImageError
    };
  }
};
</script>

<style scoped>
.cart-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-section {
  display: flex;
  align-items: center;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.cart-count {
  font-size: 32px;
  font-weight: 450;
  color: #666;
}

.cart-subtitle {
  font-size: 24px;
  color: #666;
  margin-left: 10px;
}

.cart-clear {
  color: #999;
  cursor: pointer;
}

.cart-list {
  flex: 1;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  padding: 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

.item-image {
  width: 130px;
  height: 130px;
  border-radius: 12px;
  background-color: #f3f3f3;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20px;
  flex: 1;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.8);
}

.item-package-detail {
  font-size: 24px;
  color: #666;
  margin: 10px 0;
  max-width: 614px;
}

.item-price {
  font-size: 24px;
  color: #ff3333;
  font-weight: 500;
  font-family: PingFangSC-Semibold, sans-serif;
}

.item-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.item-quantity {
  margin-right: 20px;
}

.quantity-control {
  display: flex;
  align-items: center;
  background-color: #f3f3f3;
  border-radius: 80px;
  width: 184px;
  height: 72px;
}

.minus-btn,
.plus-btn {
  width: 72px;
  height: 72px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.minus-btn::before {
  content: '';
  position: absolute;
  width: 24px;
  height: 4px;
  background-color: #666;
  border-radius: 1px;
}

.plus-btn::before,
.plus-btn::after {
  content: '';
  position: absolute;
  background-color: #666;
  border-radius: 1px;
}

.plus-btn::before {
  width: 24px;
  height: 4px;
}

.plus-btn::after {
  width: 4px;
  height: 24px;
}

.quantity-display {
  flex: 1;
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: #000;
  font-family: 'MiSans-Demibold', sans-serif;
}

.item-subtotal {
  font-size: 32px;
  font-weight: bold;
  color: #ff3333;
  min-width: 100px;
  text-align: right;
  font-family: PingFangSC-Semibold, sans-serif;
}

.cart-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 28px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.total-amount {
  display: flex;
  align-items: baseline;
}

.total-amount .label {
  font-size: 24px;
  margin-right: 10px;
  font-weight: bold;
  color: #000;
}

.total-amount .amount {
  font-size: 52px;
  font-weight: bold;
  color: #000;
  font-family: 'MiSans-Demibold', sans-serif;
}

.total-amount .decimal {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.4);
  margin-left: 2px;
  align-self: flex-end;
  margin-bottom: 8px;
}

.checkout-btn {
  width: 278px;
  height: 100px;
  font-size: 32px;
  background-color: #e23939;
  border-color: #e23939;
  border-radius: 6px;
}

.checkout-btn:hover {
  background-color: #d62c2c;
  border-color: #d62c2c;
}

/* 空购物车样式 */
:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-empty__description p) {
  font-size: 16px;
  color: #999;
  margin-top: 10px;
}

.close-icon {
  font-size: 32px;
  cursor: pointer;
  color: #666;
}
</style>
