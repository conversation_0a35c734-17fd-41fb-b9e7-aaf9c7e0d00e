import { CartItem } from '@/apps/client-pad/views/ProductOrder/viewmodel';

/**
 * 购物车转换器类
 * 用于处理购物车数据的转换
 */
export class CartDrawerConverter {
  /**
   * 计算购物车总金额
   * @param cartItems 购物车商品列表
   * @returns 总金额（分）
   */
  public static calculateTotalAmount(cartItems: CartItem[]): number {
    return cartItems.reduce((total, item) => {
      return total + item.currentPrice * item.quantity;
    }, 0);
  }

  /**
   * 将分转换为元
   * @param priceInFen 以分为单位的价格
   * @returns 以元为单位的价格
   */
  public static fenToYuan(priceInFen: number): number {
    return priceInFen / 100;
  }

  /**
   * 格式化价格显示（单位：元）
   * @param priceInFen 以分为单位的价格
   * @returns 格式化后的价格字符串（元）
   */
  public static formatPrice(priceInFen: number): string {
    return this.fenToYuan(priceInFen).toFixed(2);
  }

  /**
   * 将套餐详情转换为API要求的BaseProductInfo格式
   * @param packageDetail 套餐详情
   * @returns BaseProductInfo数组
   */
  public static convertPackageDetailToBaseProductInfo(packageDetail: any): { id: string; count: number; name: string; price: number }[] {
    if (!packageDetail) {
      return [];
    }

    const result: { id: string; count: number; name: string; price: number }[] = [];

    try {
      // 处理selectedProducts格式
      if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts)) {
        packageDetail.selectedProducts.forEach((item: any) => {
          if (item.id && item.name && (item.quantity || item.count)) {
            result.push({
              id: item.id,
              count: item.quantity || item.count || 1,
              name: item.name,
              price: item.price || item.currentPrice || 0
            });
          }
        });
        return result;
      }

      // 处理optionalGroups格式 - 支持PackageDialog的新结构
      if (packageDetail.optionalGroups && Array.isArray(packageDetail.optionalGroups)) {
        packageDetail.optionalGroups.forEach((group: any) => {
          if (group.products && Array.isArray(group.products)) {
            group.products.forEach((product: any) => {
              if (product.id && product.name && product.quantity > 0) {
                result.push({
                  id: product.id,
                  count: product.quantity,
                  name: product.name,
                  price: product.price || product.currentPrice || 0
                });
              }
            });
          }
        });

        // 处理固定商品
        if (packageDetail.fixedProducts && Array.isArray(packageDetail.fixedProducts)) {
          packageDetail.fixedProducts.forEach((product: any) => {
            if (product.id && product.name && product.quantity > 0) {
              result.push({
                id: product.id,
                count: product.quantity,
                name: product.name,
                price: product.price || product.currentPrice || 0
              });
            }
          });
        }

        return result;
      }

      // 处理productVOList格式
      if (packageDetail.productVOList && Array.isArray(packageDetail.productVOList)) {
        packageDetail.productVOList.forEach((product: any) => {
          if (product.id && product.name) {
            result.push({
              id: product.id,
              count: product.quantity || 1,
              name: product.name,
              price: product.price || product.currentPrice || 0
            });
          }
        });
        return result;
      }

      // 处理字符串格式的packageProducts
      if (typeof packageDetail === 'string') {
        const parsed = JSON.parse(packageDetail);
        return this.convertPackageDetailToBaseProductInfo(parsed);
      }

      // 处理packageProducts格式
      if (packageDetail.packageProducts) {
        if (typeof packageDetail.packageProducts === 'string') {
          const parsed = JSON.parse(packageDetail.packageProducts);
          return this.convertPackageDetailToBaseProductInfo({ selectedProducts: parsed });
        } else if (Array.isArray(packageDetail.packageProducts)) {
          return this.convertPackageDetailToBaseProductInfo({ selectedProducts: packageDetail.packageProducts });
        }
      }

      console.log('[CartDrawerConverter] 未能识别的套餐格式:', packageDetail);
      return [];
    } catch (error) {
      console.error('[CartDrawerConverter] 转换套餐信息出错:', error, packageDetail);
      return [];
    }
  }
}
