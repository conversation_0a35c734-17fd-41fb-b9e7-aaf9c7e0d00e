import { computed, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ICartDrawerViewModel, ICartDrawerState } from './viewmodel';
import { CartDrawerConverter } from './converter';
import { CartDrawerInteractor } from './interactor';
import { CartItem } from '@/apps/client-pad/views/ProductOrder/viewmodel';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';

/**
 * 购物车展示器类
 * 处理视图逻辑和状态管理
 */
export class CartDrawerPresenter implements ICartDrawerViewModel {
  private _state: ICartDrawerState;
  public computed;
  public actions;
  private router;
  private emitEvent: Function;
  private productOrderActions: any;
  private roomId: string;
  private sessionId: string;
  private userStore;

  constructor(emit: Function, productOrderActions: any, roomId: string, sessionId: string) {
    this.emitEvent = emit;
    this.productOrderActions = productOrderActions;
    this.roomId = roomId;
    this.sessionId = sessionId;
    this.router = useRouter();
    this.userStore = useUserStore();

    // 初始化状态
    this._state = reactive({
      visible: false,
      title: '购物车',
      cartItems: [] as CartItem[]
    });

    // 初始化计算属性
    this.computed = {
      totalAmount: computed(() => {
        return CartDrawerConverter.calculateTotalAmount(this._state.cartItems);
      }),
      totalItems: computed(() => {
        return this._state.cartItems.reduce((total, item) => total + item.quantity, 0);
      }),
      isEmpty: computed(() => {
        return this._state.cartItems.length === 0;
      })
    };

    // 初始化动作方法
    this.actions = {
      open: this.open.bind(this),
      close: this.close.bind(this),
      increaseQuantity: this.increaseQuantity.bind(this),
      decreaseQuantity: this.decreaseQuantity.bind(this),
      clearCart: this.clearCart.bind(this),
      confirmOrder: this.confirmOrder.bind(this)
    };
  }

  /**
   * 获取状态
   */
  public get state() {
    return this._state;
  }

  /**
   * 打开购物车抽屉
   */
  private open(): void {
    // 同步购物车数据
    this.syncCartItems();
    this._state.visible = true;
  }

  /**
   * 关闭购物车抽屉
   */
  private close(): void {
    this._state.visible = false;
    this.emitEvent('close');
  }

  /**
   * 同步购物车数据
   */
  private syncCartItems(): void {
    // 从ProductOrder中同步购物车数据
    if (this.productOrderActions && this.productOrderActions.state) {
      this._state.cartItems = [...this.productOrderActions.state.cartItems];
    }
  }

  /**
   * 增加商品数量
   * @param item 购物车商品
   */
  private increaseQuantity(item: CartItem): void {
    // 调用ProductOrder的增加数量方法
    this.productOrderActions.increaseQuantity(item);
    // 同步购物车数据
    this.syncCartItems();
  }

  /**
   * 减少商品数量
   * @param item 购物车商品
   */
  private decreaseQuantity(item: CartItem): void {
    // 调用ProductOrder的减少数量方法
    this.productOrderActions.decreaseQuantity(item);
    // 同步购物车数据
    this.syncCartItems();
  }

  /**
   * 清空购物车
   */
  private clearCart(): void {
    ElMessageBox.confirm('确定要清空购物车吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        // 调用ProductOrder的清空购物车方法
        this.productOrderActions.clearCart();
        // 同步购物车数据
        this.syncCartItems();
        ElMessage.success('购物车已清空');
      })
      .catch(() => {
        // 用户取消操作
      });
  }

  /**
   * 确认下单
   */
  private async confirmOrder(): Promise<void> {
    if (this.computed.isEmpty.value) {
      ElMessage.warning('购物车为空，无法下单');
      return;
    }

    try {
      // 获取员工ID
      const employeeId = this.userStore.userInfo?.employee?.id || '';

      // 调用立即下单API
      const result = await CartDrawerInteractor.submitOrder(this._state.cartItems, this.roomId, this.sessionId, employeeId);

      if (result) {
        ElMessage.success('下单成功');
        // 清空购物车
        this.productOrderActions.clearCart();
        // 关闭抽屉
        this.close();
        // 触发下单成功事件
        this.emitEvent('order-confirmed');
      }
    } catch (error) {
      console.error('下单出错:', error);
      ElMessage.error('下单出错，请重试');
    }
  }
}

/**
 * 购物车抽屉组合函数
 * @param emit 事件发射函数
 * @param productOrderActions ProductOrder的actions
 * @param roomId 房间ID
 * @param sessionId 会话ID
 * @returns 购物车ViewModel
 */
export function useCartDrawer(emit: Function, productOrderActions: any, roomId: string, sessionId: string): ICartDrawerViewModel {
  return new CartDrawerPresenter(emit, productOrderActions, roomId, sessionId);
}
