<template>
  <div
    class="notification-btn cursor-pointer relative h-[68px] w-[68px] flex items-center justify-center bg-white rounded-[10px]"
    @click="showNotificationCenter">
    <el-badge is-dot :hidden="notificationCount <= 0" class="item">
      <NotifyIcon class="text-[28px] icon" color="#00000099" />
    </el-badge>
  </div>
</template>

<script>
import { ref } from 'vue';
import NotifyIcon from '@/assets/v3/notify.vue';
import { useNotificationStore } from '@/modules/notification/stores/notificationStore';

export default {
  name: 'NotificationButton',
  components: {
    NotifyIcon
  },
  emits: ['openNotificationCenter'],
  setup(props, { emit }) {
    // 获取通知仓库
    const notificationStore = useNotificationStore();
    const notificationCount = ref(0);

    // 显示通知中心的方法
    const showNotificationCenter = async () => {
      // 发出事件，通知父组件打开抽屉
      emit('openNotificationCenter');

      // 更新未读消息数量
      await updateNotificationCount();
    };

    // 更新未读消息数量
    const updateNotificationCount = async () => {
      await notificationStore.fetchAndUpdateUnreadNotifications();
      notificationCount.value = notificationStore.getUnreadCount;
      console.log('NotificationButton: 已更新消息计数，数量:', notificationCount.value);
    };

    // 组件挂载时初始化
    (() => {
      // 初始化加载通知数据（只更新计数，不显示抽屉）
      updateNotificationCount();
    })();

    return {
      notificationCount,
      showNotificationCenter
    };
  }
};
</script>

<style scoped>
/* 通知徽章样式 */
:deep(.el-badge__content) {
  background-color: #e23939;
}

/* 笔记本风格 */
.notification-btn {
  border: 1px solid #eee;
}

/* 平板风格 */
@media (min-width: 768px) {
  .notification-btn {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
    border: none;
  }
}
</style>
