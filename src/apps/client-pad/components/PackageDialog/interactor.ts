/**
 * 套餐选择侧边栏组件的交互器
 */

export class PackageDialogInteractor {
  /**
   * 创建订单项
   * @param packageData 套餐数据
   * @param fixedProducts 固定商品列表
   * @param optionalGroups 可选组列表
   * @param packageQuantity 套餐数量
   */
  static async createOrderItem(packageData: any, fixedProducts: any[], optionalGroups: any[], packageQuantity: number) {
    // 构建已选商品列表，用于显示
    const allSelectedProducts = [] as Array<{ name: string; quantity: number }>;

    // 添加固定商品
    for (const product of fixedProducts) {
      if (product.quantity > 0) {
        allSelectedProducts.push({
          name: product.name,
          quantity: product.quantity
        });
      }
    }

    // 添加所选可选组商品
    for (const group of optionalGroups) {
      for (const product of group.products) {
        if (product.quantity > 0) {
          allSelectedProducts.push({
            name: product.name,
            quantity: product.quantity
          });
        }
      }
    }

    // 确保价格数据有效
    const price = packageData.price || packageData.currentPrice || 0;

    // 构建选择的商品数据
    const packageSelections = {
      // 套餐基本信息
      id: packageData.id,
      packageId: packageData.id,
      name: packageData.name,
      image: packageData.imageUrl || packageData.image || '',
      imageUrl: packageData.imageUrl || packageData.image || '', // 兼容两种图片字段名
      price: price,
      currentPrice: price, // 添加currentPrice字段以保持一致
      unit: packageData.unit || '套',

      // 数量信息
      quantity: packageQuantity,

      // 已选商品清单
      selectedItems: allSelectedProducts,

      // 固定商品和可选组信息
      fixedProducts: fixedProducts.map(product => ({
        id: product.id,
        name: product.name,
        quantity: product.quantity,
        imageUrl: product.imageUrl,
        price: product.price || 0
      })),
      optionalGroups: optionalGroups.map(group => ({
        name: group.name,
        optionType: group.optionType,
        optionCount: group.optionCount,
        products: group.products
          .filter((product: any) => product.quantity > 0)
          .map((product: any) => ({
            id: product.id,
            name: product.name,
            quantity: product.quantity,
            imageUrl: product.imageUrl,
            price: product.price || 0
          }))
      }))
    };

    // 添加调试日志，检查生成的数据结构
    console.log('[PackageDialog确认] 套餐选择数据:', {
      id: packageSelections.id,
      name: packageSelections.name,
      price: packageSelections.price,
      selectedItems: packageSelections.selectedItems.length,
      imageUrl: packageSelections.imageUrl,
      fixedProducts: packageSelections.fixedProducts.map(p => ({
        id: p.id,
        name: p.name,
        quantity: p.quantity,
        price: p.price
      })),
      optionalGroups: packageSelections.optionalGroups.map(g => ({
        name: g.name,
        products: g.products.map(p => ({
          id: p.id,
          name: p.name,
          quantity: p.quantity,
          price: p.price
        }))
      }))
    });

    // 实际项目中这里可能需要调用API将套餐添加到购物车
    // 当前仅返回构建好的数据结构
    return packageSelections;
  }
}
