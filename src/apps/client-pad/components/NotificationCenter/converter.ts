import type { RoomNotifyPayload } from '@/modules/notification/types/notification';
import type { INotificationListItem } from '@/modules/notification/list/viewmodel';
import dayjs from 'dayjs';

/**
 * 通知中心数据转换器
 */
export class NotificationCenterConverter {
  /**
   * 将API响应转换为视图模型格式
   * @param notifications API返回的通知列表
   * @returns 转换后的通知列表
   */
  convertNotificationsToViewModel(notifications: RoomNotifyPayload[]): INotificationListItem[] {
    return notifications.map(notification => {
      return {
        ...notification,
        // 格式化时间戳为可读格式
        formattedTimestamp: this.formatTimestamp(notification.timestamp),
        // 如果有处理时间，则格式化
        formattedOptTime: notification.optTime ? this.formatTimestamp(notification.optTime) : undefined
      };
    });
  }

  /**
   * 格式化时间戳
   * @param timestamp 时间戳（毫秒）
   * @returns 格式化后的时间字符串
   */
  private formatTimestamp(timestamp: number): string {
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
  }

  /**
   * 获取通知类型的图标
   * @param type 通知类型
   * @returns 图标class名称
   */
  getNotificationTypeIcon(type: string): string {
    const iconMap: Record<string, string> = {
      CALL_SERVICE: 'el-icon-Bell',
      CALL_FOOD: 'el-icon-Food',
      CALL_MANAGER: 'el-icon-UserFilled',
      TECHNICAL_ISSUE: 'el-icon-Warning',
      EMERGENCY: 'el-icon-AlarmClock'
    };

    return iconMap[type] || 'el-icon-Bell';
  }
}
