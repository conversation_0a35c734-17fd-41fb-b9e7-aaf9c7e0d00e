import type { INotificationListItem } from '@/modules/notification/list/viewmodel';
import type { Ref } from 'vue';

/**
 * 消息中心视图模型接口
 */
export interface INotificationCenterViewModel {
  // 状态
  state: {
    visible: Ref<boolean>; // 侧边栏是否可见
    notifications: Ref<INotificationListItem[]>; // 通知列表
    loading: Ref<boolean>; // 加载状态
    notificationCount: Ref<number>; // 通知数量
  };

  // 计算属性
  computed: {
    hasNotifications: () => boolean; // 是否有通知
  };

  // 操作
  actions: {
    showDrawer: (fromUserClick?: boolean) => void; // 显示侧边栏
    hideDrawer: () => void; // 隐藏侧边栏
    loadNotifications: () => Promise<void>; // 加载通知
    markAsDeal: (notificationId: string) => Promise<any>; // 标记通知为已处理
    markAllAsRead: () => Promise<boolean>; // 标记所有通知为已读
  };
}
