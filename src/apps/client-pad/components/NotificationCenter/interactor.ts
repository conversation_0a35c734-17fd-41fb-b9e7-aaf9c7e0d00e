import { notificationApiService } from '@/modules/notification/api/notificationApiService';
import type { RoomNotifyPayload } from '@/modules/notification/types/notification';

/**
 * 通知中心交互器类
 */
export class NotificationCenterInteractor {
  /**
   * 获取未处理的通知列表
   * @returns 通知列表和数量
   */
  async getUnprocessedNotifications(): Promise<{ notifications: RoomNotifyPayload[]; count: number }> {
    try {
      const response = await notificationApiService.getUnprocessedList();
      return {
        notifications: response.data || [],
        count: response.data?.length || 0
      };
    } catch (error) {
      console.error('获取未处理通知列表失败:', error);
      return {
        notifications: [],
        count: 0
      };
    }
  }

  /**
   * 标记通知为已处理
   * @param notificationId 通知ID
   * @returns 是否处理成功
   */
  async markNotificationAsDeal(notificationId: string): Promise<any> {
    try {
      const result = await notificationApiService.markAsDeal({
        id: notificationId,
        remark: ''
      });
      return { success: result.code === 0, message: result.message };
    } catch (error) {
      console.error('标记通知已处理失败:', error);
      return { success: false, message: error };
    }
  }

  /**
   * 标记所有通知为已读
   * @returns 是否处理成功
   */
  async markAllNotificationsAsRead(): Promise<boolean> {
    try {
      const result = await notificationApiService.markAllAsRead();
      return result.success;
    } catch (error) {
      console.error('标记所有通知已读失败:', error);
      return false;
    }
  }
}
