import { ref, computed } from 'vue';
import type { INotificationCenterViewModel } from './viewmodel';
import { NotificationCenterInteractor } from './interactor';
import { NotificationCenterConverter } from './converter';
import type { INotificationListItem } from '@/modules/notification/list/viewmodel';
import { ElMessage } from 'element-plus';
/**
 * 创建消息中心Presenter
 */
export function useNotificationCenterPresenter(): INotificationCenterViewModel {
  // 创建交互器和转换器
  const interactor = new NotificationCenterInteractor();
  const converter = new NotificationCenterConverter();

  // 定义状态
  const visible = ref(false);
  const notifications = ref<INotificationListItem[]>([]);
  const loading = ref(false);
  const notificationCount = ref(0);
  // 标记是否是自动初始化或用户手动点击
  const isUserClick = ref(false);

  /**
   * 加载未处理通知列表
   */
  const loadNotifications = async () => {
    try {
      loading.value = true;
      const { notifications: notifyList, count } = await interactor.getUnprocessedNotifications();

      // 使用转换器处理数据
      notifications.value = converter.convertNotificationsToViewModel(notifyList);
      notificationCount.value = count;
    } catch (error) {
      console.error('加载通知列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 显示侧边栏
   * @param fromUserClick 是否由用户点击触发
   */
  const showDrawer = (fromUserClick = false) => {
    console.log('NotificationCenter Presenter: showDrawer被调用', { fromUserClick });

    // 记录用户点击状态
    isUserClick.value = fromUserClick;

    // 如果是用户点击，则必须显示抽屉
    if (fromUserClick) {
      visible.value = true;
      loadNotifications();
      return;
    }

    // 如果不是用户点击（自动初始化），则只加载数据，不显示抽屉
    loadNotifications();
  };

  /**
   * 隐藏侧边栏
   */
  const hideDrawer = () => {
    visible.value = false;
  };

  /**
   * 标记通知为已处理
   * @param notificationId 通知ID
   */
  const markAsDeal = async (notificationId: string) => {
    const dealResult = await interactor.markNotificationAsDeal(notificationId);
    if (dealResult.success) {
      ElMessage.success('消息处理成功');
    } else {
      ElMessage.error('消息处理失败:' + dealResult.message);
    }

    await loadNotifications();
  };

  /**
   * 标记所有通知为已读
   */
  const markAllAsRead = async () => {
    const success = await interactor.markAllNotificationsAsRead();
    if (success) {
      await loadNotifications();
    }
    return success;
  };

  // 计算属性：是否有通知
  const hasNotifications = computed(() => notificationCount.value > 0);

  // 返回视图模型
  return {
    state: {
      visible: visible,
      notifications: notifications,
      loading: loading,
      notificationCount: notificationCount
    },
    computed: {
      hasNotifications: () => hasNotifications.value
    },
    actions: {
      showDrawer,
      hideDrawer,
      loadNotifications,
      markAsDeal,
      markAllAsRead
    }
  };
}
