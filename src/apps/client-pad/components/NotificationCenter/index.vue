<template>
  <div>
    <!-- 抽屉组件 -->
    <el-drawer
      :model-value="vm.state.visible.value"
      @update:model-value="vm.state.visible.value = $event"
      title="消息中心"
      direction="rtl"
      size="43%"
      :destroy-on-close="false"
      :show-close="true"
      :with-header="true"
      custom-class="notification-drawer">
      <!-- 标题栏 -->
      <template #header>
        <div class="flex items-center justify-between w-full px-8">
          <h3 class="text-2xl font-medium text-gray-600">通知中心</h3>
        </div>
      </template>

      <!-- 内容区域 -->
      <el-scrollbar height="calc(100vh - 120px)" class="px-8">
        <!-- 加载中 -->
        <div v-if="vm.state.loading.value" class="w-full h-32 flex items-center justify-center">
          <el-skeleton animated :rows="3" :loading="true" />
        </div>

        <!-- 无数据展示 -->
        <el-empty v-else-if="vm.state.notifications.value.length === 0" description="暂无未处理消息" />

        <!-- 通知列表 -->
        <div v-else class="notification-list">
          <div
            v-for="notification in vm.state.notifications.value"
            :key="notification.id"
            class="notification-item bg-white mb-6 rounded-xl border border-gray-200">
            <div class="flex items-stretch">
              <!-- 左侧图标区域 -->
              <div class="w-[90px] h-[90px] m-5 bg-gray-100 rounded-lg flex items-center justify-center">
                <!-- <i class="text-4xl" :class="converter.getNotificationTypeIcon(notification.type)"></i> -->
                <NotifyIcon class="text-[32px]" />
              </div>

              <!-- 中间内容区域 -->
              <div class="flex flex-col justify-center py-5">
                <div class="text-2xl font-medium">{{ notification.roomNumber }}</div>
                <div class="text-lg text-gray-600 mt-1">{{ getNotificationTypeText(notification.type) }}</div>
              </div>

              <!-- 右侧操作区域 -->
              <div class="ml-auto flex items-center mr-5">
                <button v-if="notification.status === 'pending'" class="btn-default" @click="vm.actions.markAsDeal(notification.id)">处理</button>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { defineExpose, ref } from 'vue';
import { useNotificationCenterPresenter } from './presenter';
import { NotificationCenterConverter } from './converter';
import type { INotificationCenterViewModel } from './viewmodel';
import NotifyIcon from '@/assets/v3/notify.vue';
// 创建视图模型
const vm: INotificationCenterViewModel = useNotificationCenterPresenter();
const converter = new NotificationCenterConverter();

// 获取通知类型文本
const getNotificationTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    CALL_SERVICE: '呼叫服务',
    CALL_FOOD: '呼叫点餐',
    CALL_MANAGER: '呼叫经理',
    TECHNICAL_ISSUE: '技术故障',
    EMERGENCY: '紧急情况'
  };

  return typeMap[type] || '未知类型';
};

// 自定义showDrawer方法，添加日志
const showNotificationDrawer = () => {
  console.log('NotificationCenter Component: showDrawer被手动调用');
  // 手动调用时传递 fromUserClick=true 参数，确保一定打开抽屉
  vm.actions.showDrawer(true);
  console.log('NotificationCenter Component: 抽屉状态已更新');
};

// 对外暴露方法
defineExpose({
  showDrawer: (fromUserClick = true) => {
    console.log('NotificationCenter Component: showDrawer被外部调用', { fromUserClick });
    // 传递参数，表示是否由用户手动点击触发
    vm.actions.showDrawer(fromUserClick);
  }
});

// 暂时去掉defineOptions，通过引用使用组件
</script>

<style scoped>
.notification-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notification-drawer :deep(.el-drawer__body) {
  padding: 20px 0;
}

.notification-drawer :deep(.el-drawer) {
  background-color: #f3f3f3;
}

.notification-item {
  transition: all 0.3s ease;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.notification-drawer :deep(.el-button--danger) {
  background-color: #e23939;
  border-color: #e23939;
  font-weight: 600;
}
</style>
