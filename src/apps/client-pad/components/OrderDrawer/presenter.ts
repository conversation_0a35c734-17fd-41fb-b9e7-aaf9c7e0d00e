import { computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { IOrderDrawerViewModel, IOrderDrawerState } from './viewmodel';
import { OrderDrawerInteractor } from './interactor';
import { OrderDrawerConverter } from './converter';

/**
 * 订单抽屉展示器类
 * 处理视图逻辑和状态管理
 */
export class OrderDrawerPresenter implements IOrderDrawerViewModel {
  private _state: IOrderDrawerState;
  public computed;
  public actions;
  private router;
  private emitEvent: Function;
  private roomId: string;
  private sessionId: string;

  constructor(emit: Function, roomId: string, sessionId: string) {
    this.emitEvent = emit;
    this.roomId = roomId;
    this.sessionId = sessionId;
    this.router = useRouter();

    // 初始化状态
    this._state = reactive({
      visible: false,
      title: '全部订单',
      orders: [],
      loading: false
    });

    // 初始化计算属性
    this.computed = {
      paidOrders: computed(() => {
        return this._state.orders.filter(order => order.status === 'paid');
      }),
      unpaidOrders: computed(() => {
        return this._state.orders.filter(order => order.status !== 'paid');
      }),
      isEmpty: computed(() => {
        return this._state.orders.length === 0;
      })
    };

    // 初始化动作方法
    this.actions = {
      open: this.open.bind(this),
      close: this.close.bind(this),
      viewOrderDetail: this.viewOrderDetail.bind(this),
      refreshOrders: this.refreshOrders.bind(this)
    };
  }

  /**
   * 获取状态
   */
  public get state() {
    return this._state;
  }

  /**
   * 打开订单抽屉
   */
  private async open(): Promise<void> {
    this._state.visible = true;
    await this.refreshOrders();
  }

  /**
   * 关闭订单抽屉
   */
  private close(): void {
    this._state.visible = false;
    this.emitEvent('close');
  }

  /**
   * 查看订单详情
   * @param orderNo 订单号
   */
  private viewOrderDetail(orderNo: string): void {
    // 跳转到订单详情页面
    this.router.push({
      path: '/order-detail',
      query: {
        orderId: orderNo,
        roomId: this.roomId,
        sessionId: this.sessionId
      }
    });
    // 关闭抽屉
    this.close();
  }

  /**
   * 刷新订单列表
   */
  private async refreshOrders(): Promise<void> {
    if (!this.roomId || !this.sessionId) {
      ElMessage.warning('缺少房间ID或会话ID，无法获取订单');
      return;
    }

    this._state.loading = true;
    try {
      // 获取订单数据
      const orders = await OrderDrawerInteractor.fetchOrders(this.roomId, this.sessionId);

      // 暂存订单数据，稍后更新
      this._state.orders = orders;

      // 如果订单数据为空，直接返回
      if (orders.length === 0) {
        return;
      }

      // 提取所有产品ID
      const productIds = OrderDrawerConverter.extractProductIds(orders);

      // 如果有产品ID，查询产品信息
      if (productIds.length > 0) {
        // 查询产品详情
        const productInfos = await OrderDrawerInteractor.queryProductsByIds(productIds);

        if (productInfos && productInfos.length > 0) {
          // 更新产品图片
          const updatedOrders = OrderDrawerConverter.updateProductImages(orders, productInfos);
          // 更新状态
          this._state.orders = updatedOrders;
        }
      }
    } catch (error) {
      console.error('刷新订单失败:', error);
      ElMessage.error('获取订单失败，请重试');
    } finally {
      this._state.loading = false;
    }
  }
}

/**
 * 订单抽屉组合函数
 * @param emit 事件发射函数
 * @param roomId 房间ID
 * @param sessionId 会话ID
 * @returns 订单ViewModel
 */
export function useOrderDrawer(emit: Function, roomId: string, sessionId: string): IOrderDrawerViewModel {
  return new OrderDrawerPresenter(emit, roomId, sessionId);
}
