<!-- 
  OrderDrawer/index.vue
  订单侧边栏组件
-->
<template>
  <el-drawer v-model="visible" :title="title" direction="rtl" size="43%" :show-close="false" :with-header="false" :destroy-on-close="false" @close="close">
    <div class="order-drawer">
      <!-- 自定义标题栏 -->
      <div class="drawer-header">
        <div class="left-section">
          <span class="drawer-title">{{ title }}</span>
        </div>
        <div class="drawer-close">
          <el-icon class="close-icon" @click="close"><Close /></el-icon>
        </div>
      </div>

      <!-- 订单为空状态 -->
      <el-empty v-if="isEmpty" description="暂无订单">
        <template #description>
          <p>您还没有订单记录~</p>
        </template>
      </el-empty>

      <!-- 订单列表 -->
      <div v-else class="order-content">
        <!-- 未结订单列表 -->
        <template v-if="unpaidOrders.length > 0">
          <div v-for="order in unpaidOrders" :key="order.id" class="order-item">
            <!-- 订单头部 -->
            <div class="order-header">
              <div class="order-info">
                <div class="order-status unpaid">{{ order.statusText }}</div>
                <div class="order-number">订单编号: {{ order.orderNo }}</div>
                <div v-if="order.isPartialRefund" class="order-refund-tag">(部分退款)</div>
              </div>
              <div class="order-time">{{ order.time }}</div>
            </div>

            <!-- 订单商品列表 -->
            <div class="order-products">
              <div v-for="product in order.products" :key="product.id" class="product-item">
                <!-- 商品图片 -->
                <div class="product-image">
                  <img :src="product.imageUrl" :alt="product.name" @error="handleImageError" />
                </div>

                <div class="product-content">
                  <div class="product-info">
                    <div class="product-name">
                      <span v-if="product.isGift" class="package-tag">[赠品]</span>
                      <span v-else-if="product.isFree" class="package-tag">[免费]</span>
                      {{ product.name }}
                    </div>
                    <!-- 套餐明细 -->
                    <div v-if="product.isPackage && product.packageSelectedItems" class="product-package-detail">已选: {{ product.packageSelectedItems }}</div>
                  </div>

                  <div class="product-price-info">
                    <div>
                      <div class="quantity-info">数量: {{ product.quantity }}{{ product.unit }}</div>
                      <!-- 退款信息 -->
                      <div v-if="product.isRefunded" class="product-refund-info">
                        已退: {{ product.refundQuantity || 0 }} {{ product.unit }} 退款金额: -¥{{ ((product.refundAmount || 0) / 100).toFixed(2) }}
                      </div>
                    </div>

                    <div class="product-price">
                      <span class="price-symbol">¥</span>
                      <span class="price-value">{{ ((product.payAmount || 0) / 100).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 已结订单列表 -->
        <template v-if="paidOrders.length > 0">
          <div v-for="order in paidOrders" :key="order.id" class="order-item">
            <!-- 订单头部 -->
            <div class="order-header">
              <div class="order-info">
                <div class="order-status paid">{{ order.statusText }}</div>
                <div class="order-number">订单编号: {{ order.orderNo }}</div>
                <div v-if="order.isPartialRefund" class="order-refund-tag">(部分退款)</div>
              </div>
              <div class="order-time">{{ order.time }}</div>
            </div>

            <!-- 订单商品列表 -->
            <div class="order-products">
              <div v-for="product in order.products" :key="product.id" class="product-item">
                <!-- 商品图片 -->
                <div class="product-image">
                  <img :src="product.imageUrl" :alt="product.name" @error="handleImageError" />
                </div>

                <div class="product-content">
                  <div class="product-info">
                    <div class="product-name">
                      <span v-if="product.isGift" class="package-tag">[赠品]</span>
                      <span v-else-if="product.isFree" class="package-tag">[免费]</span>
                      {{ product.name }}
                    </div>
                    <!-- 套餐明细 -->
                    <div v-if="product.isPackage && product.packageSelectedItems" class="product-package-detail">已选: {{ product.packageSelectedItems }}</div>
                  </div>

                  <div class="product-price-info">
                    <div>
                      <div class="quantity-info">数量: {{ product.quantity }}{{ product.unit }}</div>
                      <!-- 退款信息 -->
                      <div v-if="product.isRefunded" class="product-refund-info">
                        已退: {{ product.refundQuantity || 0 }} {{ product.unit }} 退款金额: -¥{{ ((product.refundAmount || 0) / 100).toFixed(2) }}
                      </div>
                    </div>
                    <div class="product-price">
                      <span class="price-symbol">¥</span>
                      <span class="price-value">{{ ((product.payAmount || 0) / 100).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 加载中 -->
      <div v-if="state.loading" class="loading-container">
        <el-loading :fullscreen="false" />
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { useOrderDrawer } from './presenter';
import { computed, toRefs } from 'vue';
import { handleImageError } from '@/utils/imageUtils';
import { PropType } from 'vue';
import CloseIcon from '@/assets/v3/close.vue';

export default {
  name: 'OrderDrawer',
  components: {
    CloseIcon
  },
  props: {
    roomId: {
      type: String as PropType<string>,
      required: true
    },
    sessionId: {
      type: String as PropType<string>,
      required: true
    }
  },
  emits: ['close'],
  setup(props: { roomId: string; sessionId: string }, { emit, expose }: { emit: Function; expose: Function }) {
    const viewModel = useOrderDrawer(emit, props.roomId, props.sessionId);

    // 暴露方法
    expose({
      open: viewModel.actions.open,
      close: viewModel.actions.close
    });

    // 提取计算属性
    const paidOrders = computed(() => viewModel.computed.paidOrders.value);
    const unpaidOrders = computed(() => viewModel.computed.unpaidOrders.value);
    const isEmpty = computed(() => viewModel.computed.isEmpty.value);

    // 提取方法
    const refreshOrders = viewModel.actions.refreshOrders;
    const close = viewModel.actions.close;

    // 提取状态
    const { title, visible } = toRefs(viewModel.state);

    return {
      // 状态
      state: viewModel.state,
      title,
      visible,

      // 计算属性
      paidOrders,
      unpaidOrders,
      isEmpty,

      // 方法
      refreshOrders,
      close,
      handleImageError
    };
  }
};
</script>

<style scoped>
.drawer-close {
  position: absolute;
  right: 24px;
}

.close-icon {
  font-size: 48px;
  cursor: pointer;
  color: #666;
}

.close-icon:hover {
  color: #333;
}

.order-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  padding: 0;
}

.order-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 30px;
}

.order-item {
  margin-bottom: 36px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 30px 0;
}

.order-info {
  display: flex;
  flex-direction: column;
}

.order-status {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  font-family: 'MiSans-Demibold', sans-serif;
}

.order-status.unpaid {
  color: #e23939;
}

.order-status.paid {
  color: #12aa58;
}

.order-number {
  font-size: 24px;
  color: #999;
  font-family: 'MiSans-Demibold', sans-serif;
}

.order-refund-tag {
  font-size: 20px;
  color: #f56c6c;
  margin-top: 5px;
}

.order-time {
  font-size: 32px;
  color: #999;
  font-family: 'MiSans-Medium', sans-serif;
}

.order-products {
  padding-bottom: 30px;
}

.product-item {
  display: flex;
  margin-bottom: 30px;
}

.product-image {
  width: 130px;
  height: 130px;
  border-radius: 12px;
  background-color: #f3f3f3;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20px;
  flex: 1;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28px;
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.8);
}

.package-tag {
  font-size: 20px;
  color: #409eff;
  margin-right: 5px;
}

.product-package-detail {
  font-size: 24px;
  color: #666;
  margin: 10px 0;
  max-width: 614px;
}

.product-refund-info {
  font-size: 16px;
  color: #f56c6c;
}

.product-price-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.quantity-info {
  font-size: 24px;
  color: #666;
}

.product-price {
  font-size: 32px;
  font-weight: bold;
  color: #000;
  font-family: 'MiSans-Demibold', sans-serif;
}

.price-symbol {
  font-size: 24px;
  margin-right: 4px;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
}

/* 空订单样式 */
:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-empty__description p) {
  font-size: 16px;
  color: #999;
  margin-top: 10px;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.left-section {
  display: flex;
  align-items: center;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.drawer-title {
  font-size: 32px;
  font-weight: 450;
  color: #666;
  font-family: 'MiSans-Demibold', sans-serif;
}

.close-icon {
  font-size: 32px;
  cursor: pointer;
  color: #666;
}
</style>
