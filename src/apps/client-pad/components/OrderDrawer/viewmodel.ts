import { ComputedRef } from 'vue';

// 订单项接口
export interface OrderItem {
  id: string;
  orderNo: string;
  status: string; // 'paid' | 'unpaid'
  statusText: string;
  time: string;
  timestamp: number;
  products: OrderProductItem[];
  totalAmount: number;
  isPartialRefund?: boolean; // 是否部分退款
}

// 订单商品项接口
export interface OrderProductItem {
  id: string;
  name: string;
  imageUrl: string;
  price: number;
  quantity: number;
  unit: string;
  isPackage?: boolean;
  isInPackage?: boolean; // 是否是套餐内商品
  packageSelectedItems?: string;
  isRefunded?: boolean;
  refundQuantity?: number;
  refundAmount?: number;
  originalData?: any; // 保存原始数据，便于调试
  payAmount?: number;
  isFree?: boolean;
  isGift?: boolean;
}

// 状态接口
export interface IOrderDrawerState {
  visible: boolean;
  title: string;
  orders: OrderItem[];
  loading: boolean;
}

// 计算属性接口
export interface IOrderDrawerComputed {
  paidOrders: ComputedRef<OrderItem[]>;
  unpaidOrders: ComputedRef<OrderItem[]>;
  isEmpty: ComputedRef<boolean>;
}

// 动作接口
export interface IOrderDrawerActions {
  open(): void;
  close(): void;
  viewOrderDetail(orderNo: string): void;
  refreshOrders(): Promise<void>;
}

// 总的ViewModel接口
export interface IOrderDrawerViewModel {
  state: IOrderDrawerState;
  computed: IOrderDrawerComputed;
  actions: IOrderDrawerActions;
}
