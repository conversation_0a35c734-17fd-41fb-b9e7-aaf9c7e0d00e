import { OrderItem } from './viewmodel';
import { OrderDrawerConverter } from './converter';
import request from '@/utils/request';

// 请求参数接口
interface BaseSessionRequest {
  sessionId: string;
}

// 响应接口
interface BaseResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 开台订单响应数据
interface OrderOpenVO {
  orderVOS: any[];
  inOrderProductInfos: any[];
  outOrderProductInfos: any[];
  sessionVO: any;
  // 其他字段...
}

// 产品信息接口
interface ProductVO {
  id: string;
  name: string;
  image: string;
  // 其他产品字段...
}

/**
 * 订单抽屉交互器类
 * 处理与订单业务数据的交互
 */
export class OrderDrawerInteractor {
  /**
   * 获取用户订单列表
   * @param roomId 房间ID
   * @param sessionId 会话ID
   * @returns 订单列表
   */
  public static async fetchOrders(roomId: string, sessionId: string): Promise<OrderItem[]> {
    try {
      // 调用真实API获取订单数据
      const response = await this.queryOpenOrder({ sessionId });

      if (response.code !== 0 || !response.data) {
        console.error('获取订单失败:', response.message);
        return [];
      }

      const orderData = response.data;

      // 过滤出商品订单（type为product的订单）
      const productOrders = orderData.orderVOS?.filter(order => order.type === 'product') || [];

      // 转换数据
      return OrderDrawerConverter.toOrderItems(productOrders, orderData.inOrderProductInfos || [], orderData.outOrderProductInfos || []);
    } catch (error) {
      console.error('获取订单列表失败', error);
      return [];
    }
  }

  /**
   * 查询开台订单
   * @param params 请求参数，包含sessionId
   * @returns 订单数据
   */
  private static async queryOpenOrder(params: BaseSessionRequest): Promise<BaseResponse<OrderOpenVO>> {
    return request.post('/api/order/query-open', params);
  }

  /**
   * 获取订单详情
   * @param orderNo 订单号
   * @returns 订单详情
   */
  public static async getOrderDetail(orderNo: string): Promise<any> {
    try {
      console.log('获取订单详情', orderNo);
      // 实际项目中应该有独立的获取订单详情的API
      // 这里暂时返回空对象
      return {};
    } catch (error) {
      console.error('获取订单详情失败', error);
      return null;
    }
  }

  /**
   * 根据产品ID列表查询产品详情
   * @param productIds 产品ID数组
   * @returns 产品信息数组
   */
  public static async queryProductsByIds(productIds: string[]): Promise<ProductVO[]> {
    try {
      if (!productIds || productIds.length === 0) {
        return [];
      }

      // 查看 request.ts 的实现，其响应拦截器已经处理了 response.data 的提取
      const response: any = await request.post('/api/v3/product/query', {
        ids: productIds
      });

      if (!response || !response.data) {
        console.error('获取产品详情失败:', response?.message || '未知错误');
        return [];
      }

      return response.data;
    } catch (error) {
      console.error('查询产品信息失败', error);
      return [];
    }
  }
}
