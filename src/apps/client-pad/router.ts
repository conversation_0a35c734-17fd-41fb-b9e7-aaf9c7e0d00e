import { createRouter, createWebHistory, RouteRecordRaw, Router } from 'vue-router';
import { AppRouteRecordRaw } from '@/router/types';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import NProgress from 'nprogress';
import '@/styles/nprogress.css';
import PadLayout from '@/layouts/PadLayout.vue';

// NProgress配置
NProgress.configure({
  easing: 'ease',
  speed: 500,
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.2
});

// 定义 Pad 应用的路由
const routes: AppRouteRecordRaw[] = [
  // 基础路由 - 认证和登录
  {
    path: '/auth',
    name: 'auth',
    meta: {
      title: '设备授权',
      requiresAuth: false
    },
    component: () => import('./views/Auth/index.vue')
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      requiresAuth: false
    },
    component: () => import('./views/Login/index.vue')
  },

  // 使用 PadLayout 作为需要认证的路由的布局
  {
    path: '/',
    component: PadLayout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'room',
        meta: {
          title: '包厢首页'
        },
        component: () => import('./views/Room/index.vue')
      },
      {
        path: 'product-order',
        name: 'product-order',
        meta: {
          title: '商品点单'
        },
        component: () => import('./views/ProductOrder/index.vue')
      }
    ]
  },

  // 通配符路由 - 捕获所有未匹配的路由，直接重定向到首页
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    redirect: '/'
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes as RouteRecordRaw[]
});

/**
 * 获取所有路由
 * @returns 所有路由配置
 */
export function getRoutes(): AppRouteRecordRaw[] {
  return routes;
}

/**
 * 根据角色获取路由
 * @param roles 角色列表
 * @returns 过滤后的路由配置
 */
export function getRoutesByRoles(roles: string[]): AppRouteRecordRaw[] {
  // 如果没有角色限制，返回所有路由
  if (!roles || roles.length === 0) {
    return routes;
  }

  // 过滤路由
  const filterRoutes = (routes: AppRouteRecordRaw[]): AppRouteRecordRaw[] => {
    return routes.filter(route => {
      // 检查当前路由是否有角色限制
      if (route.meta?.roles) {
        // 检查用户角色是否有权限访问
        const hasRole = roles.some(role => route.meta?.roles?.includes(role));
        if (!hasRole) {
          return false;
        }
      }

      // 递归处理子路由
      if (route.children) {
        route.children = filterRoutes(route.children);
      }

      return true;
    });
  };

  return filterRoutes(routes);
}

/**
 * 根据权限获取路由
 * @param permissions 权限列表
 * @returns 过滤后的路由配置
 */
export function getRoutesByPermissions(permissions: string[]): AppRouteRecordRaw[] {
  // 如果没有权限限制，返回所有路由
  if (!permissions || permissions.length === 0) {
    return routes;
  }

  // 过滤路由
  const filterRoutes = (routes: AppRouteRecordRaw[]): AppRouteRecordRaw[] => {
    return routes.filter(route => {
      // 检查当前路由是否有权限限制
      if (route.meta?.permissions) {
        // 检查用户权限是否有权限访问
        const hasPermission = permissions.some(permission => route.meta?.permissions?.includes(permission));
        if (!hasPermission) {
          return false;
        }
      }

      // 递归处理子路由
      if (route.children) {
        route.children = filterRoutes(route.children);
      }

      return true;
    });
  };

  return filterRoutes(routes);
}

/**
 * 设置路由守卫
 * @param router 路由实例
 */
export function setupRouterGuards(router: Router): void {
  router.beforeEach(async (to, from, next) => {
    // 开始加载进度条
    NProgress.start();

    console.log('路由变化:', {
      from: { path: from.path, name: from.name },
      to: { path: to.path, name: to.name }
    });

    const userStore = useUserStore();
    await userStore.initUserState();
    const deviceStore = useDeviceStore();
    const venueStore = useVenueStore();

    const token = userStore.token;
    console.log('----token:', token);

    // 白名单路由，不需要验证token
    const whiteList = ['/login', '/auth'];

    // 第一个条件检查 - venue相关
    const condition1 = !venueStore.venueId && to.path !== '/auth' && !whiteList.includes(to.path);
    if (condition1) {
      // 如果没有venueId，且不是前往auth页面，重定向到auth页面
      console.log('路由守卫执行：跳转到 /auth (无venue)');
      next('/auth');
      return;
    }

    // 新增：检查IP不一致的情况
    const ipMismatch = venueStore.venueId && deviceStore.cashierMachine?.grantIp && deviceStore.cashierMachine.grantIp !== deviceStore.ipAddress;

    if (ipMismatch && to.path !== '/auth' && !whiteList.includes(to.path)) {
      console.log('路由守卫执行：跳转到 /auth (IP不一致)');
      next('/auth');
      return;
    }

    // 第二个条件检查 - token相关
    const condition2 = !token && !whiteList.includes(to.path);
    if (condition2) {
      // 如果没有token且不是白名单路由，重定向到登录页
      console.log('路由守卫执行：跳转到 /login');
      next('/login');
      return;
    }

    next();
  });

  // 路由加载完成后
  router.afterEach(() => {
    // 完成进度条
    NProgress.done();
  });

  // 路由加载出错时
  router.onError(() => {
    // 出错时完成进度条
    NProgress.done();
  });
}

// 设置路由守卫
setupRouterGuards(router);

export default router;
