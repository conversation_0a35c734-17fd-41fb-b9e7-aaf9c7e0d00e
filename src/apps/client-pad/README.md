# Thunder ERP 平板客户端

## 文件夹结构与命名规范

### 命名规则

为避免在不同操作系统间的大小写敏感性问题，请遵循以下命名规则：

1. **视图目录命名**：视图目录必须使用 **大驼峰命名法(PascalCase)**，例如 `DeviceAuth`、`Login`
2. **组件文件命名**：组件文件必须使用 **大驼峰命名法(PascalCase)**，例如 `ProductList.vue`
3. **工具文件命名**：非组件文件使用 **小驼峰命名法(camelCase)**，例如 `utils.ts`、`helper.ts`

### 导入路径注意事项

项目中的导入路径对大小写敏感。macOS和Windows的文件系统默认不区分大小写，但Vite和TypeScript是区分大小写的。这可能导致本地开发时正常，但部署后出现问题。

**常见问题**：

- 路径大小写不一致：`import { Something } from './component'` vs `import { Something } from './Component'`
- 自引用错误：`import { SomeThing } from '../ThisDirectory/thing'` 而不是 `import { SomeThing } from './thing'`

### 相关问题的临时解决方案

如遇到模块导入错误，可以：

1. 清除IDE和TypeScript的缓存
2. 添加 `// @ts-ignore` 注释暂时忽略错误
3. 重新启动开发服务器
4. 确保所有import路径与真实的文件系统路径完全匹配（包括大小写）

## VIPER架构说明

客户端使用VIPER架构，每个视图由以下文件组成：

- `index.vue` - 视图层
- `viewmodel.ts` - 视图模型接口
- `presenter.ts` - 展示层
- `interactor.ts` - 业务交互层
- `converter.ts` - 数据转换层

请确保遵循架构规范，保持各层职责清晰。
