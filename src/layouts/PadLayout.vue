<template>
  <div class="client-pad-layout">
    <!-- 当视口宽度小于最小宽度时，这个容器会保持固定宽度 -->
    <div class="min-width-container">
      <RouterView />
    </div>
  </div>
</template>

<script setup lang="ts">
// Script setup content if any
</script>

<style scoped>
.client-pad-layout {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f7fa; /* Added from App.vue global styles */
}

.min-width-container {
  min-width: var(--app-min-width, 320px); /* Updated to be more flexible */
  width: 100%; /* Added from App.vue global styles */
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.pad-header {
  /* Header styles */
}

/* Ensure RouterView takes remaining height */
.min-width-container > :deep(> div:last-child),
.min-width-container > :deep(> main:last-child),
.min-width-container > :deep(> section:last-child) {
  flex-grow: 1;
  overflow-y: auto;
}
</style>
