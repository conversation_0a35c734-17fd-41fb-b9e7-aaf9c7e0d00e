<template>
  <div class="global-layout">
    <!-- 左侧边栏 -->
    <Sidebar
      mode="compact"
      :menu-items="menuItems"
      :user-name="userName"
      :user-avatar="userAvatar"
      :active-path="activePath"
      @menu-click="handleMenuClick"
      @update:active-path="updateActivePath" />

    <!-- 右侧内容区 -->
    <div class="content-area">
      <!-- 主内容区 -->
      <div class="main-content">
        <router-view v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
import { useVenueStore } from '@/stores/venueStore';
import Sidebar from '@/components/Sidebar/index.vue';
import { menuItems as defaultMenuItems } from '@/utils/constant/menuConfig';
import NProgress from 'nprogress';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const venueStore = useVenueStore();

// 用户信息 - 使用计算属性确保响应式更新
const userName = computed(() => userStore.userInfo?.employee?.name || '未登录');

// 用户头像 - 空字符串，让 Sidebar 组件自己处理
const userAvatar = computed(() => {
  return '';
});

const venueName = computed(() => venueStore.venue?.name || '');
const isLoggedIn = computed(() => userStore.isLoggedIn());

// 当前活动路径
const activePath = ref('');

// 使用导入的菜单项
const menuItems = ref(defaultMenuItems);

// 检查登录状态和场馆信息
function checkAuth() {
  // 初始化用户状态，确保从本地存储加载最新状态
  userStore.initUserState();

  if (!userStore.isLoggedIn()) {
    console.log('用户未登录，重定向到登录页面');
    router.push('/login');
    return false;
  }

  // 检查是否有场馆信息
  if (!venueStore.venueId) {
    console.log('用户未选择场馆，重定向到授权页面');
    router.push('/auth');
    return false;
  }

  console.log('----checkAuth:', userStore.userInfo, venueStore.venue);
  return true;
}

// 更新当前活动路径函数
function updateActivePath(path: string) {
  // 提取一级路径
  const firstLevelPath = '/' + path.split('/')[1];

  // 找到匹配的菜单项
  const matchedItem = menuItems.value.find((item: any) => item.path === firstLevelPath);

  if (matchedItem) {
    activePath.value = matchedItem.path;
  } else {
    activePath.value = '';
  }
}

// 处理菜单点击
function handleMenuClick(path: string) {
  // 检查登录状态
  if (!checkAuth()) return;

  try {
    // 如果点击的是当前一级路径，强制刷新
    const currentFirstLevelPath = '/' + route.path.split('/')[1];
    if (path === currentFirstLevelPath) {
      router.replace({ path: route.path, query: { _t: Date.now() } });
    } else {
      router.push(path);
    }
  } catch (error) {
    console.error(`导航到 ${path} 失败:`, error);
  }
}

// 组件挂载后的处理
onMounted(() => {
  // 更新活动路径
  updateActivePath(route.path);

  // 确保进度条完成
  NProgress.done();
});

// 组件卸载前处理
onUnmounted(() => {
  // 确保组件卸载时进度条重置
  NProgress.remove();
});

// 监听路由变化
watch(
  () => route.path,
  newPath => {
    updateActivePath(newPath);
  },
  { immediate: true }
);
</script>

<style scoped>
.global-layout {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* 右侧内容区样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  /* padding: 20px; */
}
</style>
