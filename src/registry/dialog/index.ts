// =====================================================================
// 对话框注册中心 - 系统核心文件
// =====================================================================
/**
 * 对话框注册中心
 *
 * 本模块负责：
 * 1. 提供对话框组件的查找服务
 * 2. 集成系统对话框和业务对话框
 * 3. 区分全局对话框（同步加载）和页面对话框（异步加载）
 */

import { markRaw, defineAsyncComponent } from 'vue';
import { DialogLevel, DialogUIType } from '@/types/dialog';
import type { Component } from 'vue';
import type { DialogConfig } from '@/types/dialog';

// 导入系统对话框（全局同步加载）
import MessageDialog from '@/components/Dialog/system/MessageDialog.vue';
import ConfirmDialog from '@/components/Dialog/system/ConfirmDialog.vue';
import AlertDialog from '@/components/Dialog/system/AlertDialog.vue';
import ErrorDialog from '@/components/Dialog/system/ErrorDialog.vue';

// 账单相关对话框
import BillRestoreDetailDialog from '@/modules/order/components/dialogs/BillRestoreDetailDialog.vue';
import BillRestoreConfirmDialog from '@/modules/order/components/dialogs/BillRestoreConfirmDialog.vue';

export enum DialogType {
  GLOBAL = 'global', // 全局对话框
  PAGE = 'page' // 页面级对话框
}

/**
 * 统一的对话框配置接口
 */
interface DialogRegistration {
  id: string; // 对话框唯一标识符，与组件名称保持一致
  type: DialogType; // 对话框类型（全局/页面）
  config: DialogConfig; // 对话框配置
  component: Component | (() => Promise<Component>); // 组件或组件加载函数
}

/**
 * 对话框注册表
 */
export const DialogRegistry: DialogRegistration[] = [
  // 系统对话框（全局同步加载）
  {
    id: 'MessageDialog',
    type: DialogType.GLOBAL,
    config: {
      title: '消息',
      width: '400px',
      closeOnClickModal: true,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        message: '',
        type: 'info'
      }
    },
    component: markRaw(MessageDialog)
  },
  {
    id: 'ConfirmDialog',
    type: DialogType.GLOBAL,
    config: {
      title: '确认',
      width: '400px',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        message: '',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    },
    component: markRaw(ConfirmDialog)
  },
  {
    id: 'AlertDialog',
    type: DialogType.GLOBAL,
    config: {
      title: '提示',
      width: '400px',
      closeOnClickModal: true,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        message: '',
        buttonText: '确定',
        type: 'info'
      }
    },
    component: markRaw(AlertDialog)
  },
  {
    id: 'ErrorDialog',
    type: DialogType.GLOBAL,
    config: {
      title: '错误',
      width: '400px',
      closeOnClickModal: true,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        message: '',
        buttonText: '确定'
      }
    },
    component: markRaw(ErrorDialog)
  },

  // 产品模块对话框（页面异步加载）
  {
    id: 'AddProductDialog',
    type: DialogType.PAGE,
    config: {
      title: '包厢点单',
      customClass: '',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      showHeader: false,
      showFooter: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.LARGE,
      defaultProps: {}
    },
    component: () => import('@/modules/room/components/dialogs/AddProductDialog.vue').then(module => module.default)
  },
  {
    id: 'ProductDetailDialog',
    type: DialogType.PAGE,
    config: {
      title: '商品详情',
      width: '80%',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.LARGE,
      defaultProps: {
        productId: '',
        initialData: {}
      }
    },
    component: () => import('@/modules/product/components/dialogs/ProductDetailDialog.vue')
  },

  // 房间模块对话框（页面异步加载）
  {
    id: 'RoomSelectorDialog',
    type: DialogType.PAGE,
    config: {
      title: '选择包厢',
      width: '60%',
      closeOnClickModal: true,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {}
    },
    component: () => import('@/modules/room/components/RoomSelectorDialog.vue')
  },

  // 预订包厢对话框
  {
    id: 'PreTableDialog',
    type: DialogType.PAGE,
    config: {
      title: '包厢预订',
      width: '80%',
      closeOnClickModal: false,
      showClose: true,
      showHeader: false,
      showFooter: false,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.LARGE,
      customClass: 'pre-table-dialog',
      defaultProps: {
        roomId: '',
        mode: 'add',
        bookingId: ''
      }
    },
    component: () => import('@/modules/room/views/PreTable/index.vue')
  },

  // 订单支付对话框
  {
    id: 'OrderPayDialog',
    type: DialogType.PAGE,
    config: {
      title: '结账',
      width: '90%',
      closeOnClickModal: false,
      showClose: true,
      showHeader: false,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.LARGE,
      defaultProps: {
        sessionId: '',
        orderData: null,
        payType: 'later'
      }
    },
    component: () => import('@/modules/order/components/dialogs/OrderPayDialog/index.vue')
  },

  // 会员卡支付对话框
  {
    id: 'MemberCardPayDialog',
    type: DialogType.PAGE,
    config: {
      title: '会员卡支付',
      width: '880px',
      closeOnClickModal: false,
      showClose: true,
      showHeader: true,
      showFooter: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        memberCardId: '378671d4624e4a79a0f95fc418482444',
        roomAmount: 0,
        goodsAmount: 0
      }
    },
    component: () => import('@/modules/member/components/dialogs/MemberCardPayDialog/index.vue')
  },

  // 支付详情对话框
  {
    id: 'PayDetailDialog',
    type: DialogType.PAGE,
    config: {
      title: '结账详情',
      width: '650px',
      closeOnClickModal: false,
      showClose: true,
      showHeader: true,
      showFooter: false,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        payBillVOs: [],
        payRecordVOs: []
      }
    },
    component: () => import('@/modules/order/components/dialogs/PayDetailDialog/index.vue')
  },

  // 商家调整对话框
  {
    id: 'MerchantAdjustDialog',
    type: DialogType.PAGE,
    config: {
      title: '商家调整',
      width: '800px',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        originalAmount: 0,
        roomAmount: 0,
        productAmount: 0
      }
    },
    component: () => import('@/modules/order/components/MerchantAdjustDialog.vue')
  },

  // 添加退单对话框
  {
    id: 'RoomRefundDialog',
    type: DialogType.PAGE,
    config: {
      title: '包厢退单',
      width: '85%',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        sessionId: '',
        roomId: ''
      }
    },
    component: () => import('@/modules/production/views/refunddetail/index.vue')
  },

  // 结束计时对话框
  {
    id: 'FinishTimingDialog',
    type: DialogType.PAGE,
    config: {
      title: '结束计时',
      width: '85%',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        sessionId: '',
        roomId: '',
        roomTypeId: '',
        areaId: '',
        roomName: ''
      }
    },
    component: () => import('@/modules/order/components/dialogs/FinishTimingDialog/index.vue').then(module => module.default)
  },

  // 关房对话框
  {
    id: 'CloseRoomDialog',
    type: DialogType.PAGE,
    config: {
      title: '关房',
      width: '80%',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        sessionId: '',
        roomId: ''
      }
    },
    component: () => import('@/modules/room/components/CloseRoomDialog.vue')
  },

  // 存酒对话框
  {
    id: 'StoreWineDialog',
    type: DialogType.PAGE,
    config: {
      title: '存酒',
      width: '1680px',
      closeOnClickModal: false,
      showClose: true,
      showHeader: false,
      showFooter: false,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.LARGE,
      defaultProps: {
        roomId: ''
      }
    },
    component: () => import('@/modules/wine/components/StoreWineDialog/index.vue')
  },

  // 账单相关对话框
  {
    id: 'BillPayDialog',
    type: DialogType.PAGE,
    component: () => import('@/modules/shift/views/employeeShift/components/BillDetailDialog.vue'),
    config: {
      title: '结账详情',
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      showClose: true,
      closeOnClickModal: false,
      defaultProps: {}
    }
  },
  {
    id: 'BillRestoreDetailDialog',
    type: DialogType.PAGE,
    component: BillRestoreDetailDialog,
    config: {
      title: '结账详情',
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      showClose: true,
      closeOnClickModal: false,
      defaultProps: {}
    }
  },
  {
    id: 'BillRestoreConfirmDialog',
    type: DialogType.PAGE,
    component: BillRestoreConfirmDialog,
    config: {
      title: '账单还原',
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      showClose: true,
      closeOnClickModal: false,
      defaultProps: {}
    }
  },

  // 可以添加更多对话框...
  {
    id: 'MemberOpenDialog',
    type: DialogType.PAGE,
    config: {
      title: '开卡',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        data: {}
      }
    },
    component: () => import('@/modules/member/views/memberOpen/MemberOpenDialog.vue')
  },
  //会员卡充值
  {
    id: 'MemberPayDialog',
    type: DialogType.PAGE,
    config: {
      title: '会员卡充值',
      width: '800px',
      closeOnClickModal: false,
      showClose: true,
      keepAlive: false,
      destroyOnClose: true,
      level: DialogLevel.PRIMARY,
      uiType: DialogUIType.DEFAULT,
      defaultProps: {
        memberInfo: {}
      }
    },
    component: () => import('@/modules/member/views/memberPayDialog/index.vue')
  }
];

/**
 * 获取对话框组件（支持同步和异步）
 * @param id 对话框ID
 * @returns 对话框组件（同步）或Promise<对话框组件>（异步）
 */
export function getDialogComponent(id: string): Component | Promise<Component> | null {
  // 查找对话框注册信息
  const dialog = DialogRegistry.find(d => d.id === id);
  if (!dialog) {
    return null;
  }

  // 根据类型返回组件或组件加载函数
  if (dialog.type === DialogType.GLOBAL) {
    return dialog.component as Component;
  } else {
    try {
      // 检查组件是否为函数
      if (typeof dialog.component === 'function') {
        // 异步加载组件并确保正确处理默认导出
        return (dialog.component as () => Promise<any>)()
          .then(module => {
            // console.log(`[DialogRegistry] 加载组件 ${id} 成功:`, module);
            // 处理 ES 模块的默认导出
            return module.default || module;
          })
          .catch(error => {
            console.error(`[DialogRegistry] 加载组件 ${id} 失败:`, error);
            throw error;
          });
      } else {
        // 如果组件不是函数，直接返回
        // console.log(`[DialogRegistry] 组件 ${id} 不是函数，直接返回:`, dialog.component);
        return Promise.resolve(dialog.component as Component);
      }
    } catch (error) {
      console.error(`[DialogRegistry] 获取组件 ${id} 时发生错误:`, error);
      throw error;
    }
  }
}

/**
 * 获取对话框配置
 * @param id 对话框ID
 * @returns 对话框配置
 */
export function getDialogConfig(id: string): DialogConfig | null {
  // 查找对话框注册信息
  const dialog = DialogRegistry.find(d => d.id === id);
  if (!dialog) {
    return null;
  }

  // 如果配置中没有 component 属性，则添加 id 作为 component
  const config = { ...dialog.config };
  if (!config.component) {
    config.component = dialog.id;
  }

  return config;
}

// 注册全局对话框注册函数
// @ts-ignore
window.registerDialog = (registration: DialogRegistration) => {
  try {
    console.log(`[DialogRegistry] 尝试注册对话框 ${registration.id}`);

    // 规范化component属性
    if (registration.type === DialogType.PAGE && registration.component) {
      if (typeof registration.component !== 'function') {
        console.log(`[DialogRegistry] 将页面对话框 ${registration.id} 的组件转换为函数`);
        const originalComponent = registration.component;
        registration.component = () => Promise.resolve(originalComponent);
      }
    }

    // 检查是否已存在
    const existingDialogIndex = DialogRegistry.findIndex(d => d.id === registration.id);
    if (existingDialogIndex !== -1) {
      // 更新
      DialogRegistry[existingDialogIndex] = registration;
      console.log(`[DialogRegistry] 对话框 ${registration.id} 已更新`);
    } else {
      // 添加
      DialogRegistry.push(registration);
      console.log(`[DialogRegistry] 对话框 ${registration.id} 已添加`);
    }
  } catch (error) {
    console.error(`[DialogRegistry] 注册对话框 ${registration.id} 失败:`, error);
  }
};
