import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type {
  IStoreWineDetailViewModel,
  IStoreWineDetailState,
  IStoreWineDetailComputed,
  IStoreWineDetailActions,
  ExtendedProductStorageVO,
  OperationLog,
  OperationHistoryItem
} from './viewmodel';
import { formatUnixTimestamp } from '@/utils/dateUtils';
// 导入API接口
import { getApiProductStorageDetailId } from '@/api/autoGenerated/defaultVersion/product';

export class StoreWineDetailPresenter implements IStoreWineDetailViewModel {
  // 状态
  public state: IStoreWineDetailState = reactive({
    dialogVisible: false,
    detail: null
  });

  // 计算属性
  public computed: IStoreWineDetailComputed = {
    // 操作日志数据
    operationLogs: computed(() => {
      // 如果没有详情数据，返回空数组
      if (!this.state.detail) {
        return [];
      }

      let logItems: OperationLog[] = [];

      // 使用API返回的操作历史记录（如果有）
      if (this.state.detail.operationHistory && Array.isArray(this.state.detail.operationHistory)) {
        // 将API返回的操作历史记录标准化为统一格式
        logItems = this.state.detail.operationHistory.map(history => ({
          operation: history.typeName || history.type || '',
          operator: history.operatorId || '',
          operatorName: history.operatorName || '',
          time: history.time || 0,
          quantity: history.quantity || 0,
          balance: history.balanceQty || 0,
          remark: history.remark || '',
          type: history.type || '',
          typeName: history.typeName || '',
          deliveryRoomName: history.deliveryRoomName || '', // 取酒送达包厢字段
          roomName: history.roomName || '', // 存酒寄存包厢字段
          roomId: history.roomId || ''
        }));
      }
      // 如果没有操作历史但有operations数据
      else if (this.state.detail.operations && Array.isArray(this.state.detail.operations)) {
        // 确保operations数据的字段都是标准化的
        logItems = this.state.detail.operations.map(op => ({
          operation: op.operation || op.typeName || '',
          operator: op.operator || '',
          operatorName: op.operatorName || '',
          time: op.time || 0,
          quantity: op.quantity || 0,
          balance: op.balance || 0,
          remark: op.remark || '',
          type: op.type || '',
          typeName: op.typeName || op.operation || '',
          deliveryRoomName: op.deliveryRoomName || '', // 取酒送达包厢字段
          roomName: op.roomName || '', // 存酒寄存包厢字段
          roomId: op.roomId || ''
        }));
      }
      // 如果既没有operationHistory也没有operations，创建一条初始记录
      else {
        logItems = [
          {
            operation: '存酒',
            operator: this.state.detail.operatorId || '',
            operatorName: this.state.detail.operatorName || '',
            time: this.state.detail.storageTime || Date.now(),
            quantity: this.state.detail.quantity || 0,
            balance: this.state.detail.remainingQty || 0,
            remark: this.state.detail.remark || '',
            type: 'storage',
            typeName: '',
            deliveryRoomName: '', // 存酒操作没有送达包厢
            roomName: this.state.detail.storageRoomName || '', // 存酒寄存包厢字段
            roomId: this.state.detail.storageRoomId || ''
          }
        ];
      }

      return logItems;
    })
  };

  // 动作实现
  public actions: IStoreWineDetailActions = {
    // 关闭对话框
    closeDialog: () => {
      this.state.dialogVisible = false;
    },

    // 报废操作
    handleReport: () => {
      ElMessage.info('报废功能开发中');
    },

    // 续存操作
    handleExtend: () => {
      ElMessage.info('续存功能开发中');
    },

    // 格式化日期
    formatDate: (timestamp?: number) => {
      if (!timestamp) return '';
      return formatUnixTimestamp(timestamp);
    },

    // 获取剩余天数
    getRemainingDays: (timestamp?: number) => {
      if (!timestamp) return '';
      const now = new Date();
      const expireDate = new Date(timestamp * 1000);
      const diffTime = expireDate.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays > 0 ? `${diffDays}天后到期` : '已过期';
    },

    // 格式化短日期（带时间）
    formatShortDate: (timestamp?: number) => {
      if (!timestamp) return '';
      return formatUnixTimestamp(timestamp);
    },

    // 设置对话框可见性
    setDialogVisible: (visible: boolean) => {
      this.state.dialogVisible = visible;
    },

    // 设置详情数据
    setDetail: (detail: ExtendedProductStorageVO | null) => {
      this.state.detail = detail;

      // 移除自动调用逻辑，改为手动调用模式
    },

    // 获取操作历史记录
    fetchOperationHistory: async (detailId: string) => {
      try {
        // 调用API获取详情，包含操作历史
        const response = await getApiProductStorageDetailId({
          id: detailId
        });

        // API返回格式: { code: 0, message: 'success', data: {...} }
        if (response && response.code === 0 && response.data) {
          // 更新整个详情对象，包含操作历史
          if (this.state.detail) {
            // 保留原有对象的引用，更新其属性
            Object.assign(this.state.detail, response.data);
          }
        }
      } catch (error) {
        console.error('获取操作历史失败:', error);
        ElMessage.error('获取操作历史失败');
      }
    }
  };
}

// 导出组合式函数
export function useStoreWineDetailPresenter(): IStoreWineDetailViewModel {
  return new StoreWineDetailPresenter();
}
