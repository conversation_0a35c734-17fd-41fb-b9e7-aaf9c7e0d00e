<template>
  <app-dialog
    v-model="vm.state.dialogVisible"
    :width="1680"
    title="详情"
    :close-on-click-modal="false"
    :show-header="false"
    :show-footer="false"
    :ui-type="DialogUIType.LARGE">
    <LeftRightLayout :leftClass="'!w-[344px] !flex-none !min-w-[344px]'" :rightClass="'!flex-1 !bg-white'" :leftTitle="'基础信息'">
      <!-- 左侧：商品单基础信息 -->
      <template #left>
        <div class="h-full bg-gray-50 overflow-auto pb-[60px]">
          <div class="p-5">
            <div class="grid grid-cols-1 gap-4">
              <div class="flex">
                <div class="w-[80px] text-gray-500">单号：</div>
                <div class="font-medium">{{ vm.state.detail?.parentOrderNo || '' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">手机号：</div>
                <div class="font-medium">{{ vm.state.detail?.phoneNumber || '' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">客户姓名：</div>
                <div class="font-medium">{{ vm.state.detail?.customerName || '--' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">客户卡号：</div>
                <div class="font-medium">{{ vm.state.detail?.memberCardNo || vm.state.detail?.customerId || '' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">状态：</div>
                <div class="font-medium">{{ vm.state.detail?.statusName || '' }}</div>
              </div>
            </div>
          </div>

          <div class="p-3 border-b border-t">
            <div class="text-base font-medium">寄存信息</div>
          </div>

          <div class="p-5">
            <div class="grid grid-cols-1 gap-4">
              <div class="flex">
                <div class="w-[80px] text-gray-500">商品名称：</div>
                <div class="font-medium">{{ vm.state.detail?.productName || '' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">寄存总数：</div>
                <div class="font-medium">{{ vm.state.detail?.quantity || '' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">寄存仓库：</div>
                <div class="font-medium">{{ getWarehouseLabel(vm.state.detail?.storageLocation) }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">寄存包厢：</div>
                <div class="font-medium">{{ vm.state.detail?.storageRoomName || vm.state.detail?.storageRoomId || '--' }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">存酒日期：</div>
                <div class="font-medium">{{ vm.actions.formatDate(vm.state.detail?.storageTime) }}</div>
              </div>
              <div class="flex">
                <div class="w-[80px] text-gray-500">过期日期：</div>
                <div class="font-medium">
                  {{ vm.actions.formatDate(vm.state.detail?.expireTime) }} ({{ vm.actions.getRemainingDays(vm.state.detail?.expireTime) || '白酒晚' }})
                </div>
              </div>
              <!-- <div class="flex">
                <div class="w-[80px] text-gray-500">存酒类型：</div>
                <div class="font-medium">{{ vm.state.detail?.offlineOnly ? '仅线下存酒' : '线上线下' }}</div>
              </div> -->
            </div>
          </div>

          <div class="p-3 border-b border-t">
            <div class="text-base font-medium">备注信息</div>
          </div>

          <div class="p-5">
            <div class="text-gray-700">{{ vm.state.detail?.remark || '--' }}</div>
          </div>
        </div>
      </template>

      <!-- 右侧：商品明细列表 -->
      <template #right>
        <div class="h-full flex flex-col relative pt-[54px]">
          <div class="overflow-auto pb-[60px]">
            <el-table :data="vm.computed.operationLogs.value" stripe :highlight-current-row="true">
              <el-table-column prop="operation" label="操作" min-width="120">
                <template #default="scope">
                  <span>{{ scope.row.operation }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="operatorName" label="操作人" min-width="120">
                <template #default="scope">
                  <span>{{ scope.row.operatorName || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="time" label="操作时间" min-width="180">
                <template #default="scope">
                  <span>{{ formatUnixTimestamp(scope.row.time) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="deliveryRoomName" label="送达包厢" min-width="120">
                <template #default="scope">
                  <span>{{ scope.row.deliveryRoomName || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="操作数量" min-width="120">
                <template #default="scope">
                  <span>{{ scope.row.quantity }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="balance" label="剩余数量">
                <template #default="scope">
                  <span>{{ scope.row.balance }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </template>

      <!-- 右侧底部操作 -->
      <template #right-footer>
        <div class="border-t border-[#e5e5e5] py-4 px-5">
          <div class="flex justify-end">
            <div class="flex gap-3">
              <el-button type="plain" plain @click="handleDiscard" class="w-[240px] h-[80px]" :disabled="isButtonDisabled">报废</el-button>
              <el-button
                class="w-[240px] h-[80px] !bg-black !border-black !text-white hover:!bg-gray-800 hover:!border-gray-800"
                @click="handleExtend"
                :disabled="isExtendDisabled"
                >续存</el-button
              >
            </div>
          </div>
        </div>
      </template>
    </LeftRightLayout>
  </app-dialog>

  <!-- 续存对话框 -->
  <el-dialog
    v-model="showExtendDialog"
    align-center
    append-to-body
    :title="'续存'"
    width="500px"
    :close-on-click-modal="false"
    custom-class="extend-wine-dialog">
    <div class="p-4 text-center">
      <p class="text-xl mb-6">续存</p>
      <p class="text-lg mb-8">续存至 {{ extendDateFormatted }}</p>

      <div class="bg-gray-50 p-4 rounded mb-6">
        <p class="mb-2"><strong>商品名称：</strong>{{ vm.state.detail?.productName }}</p>
        <p class="mb-2"><strong>续存天数：</strong>{{ storageStore.renewalDays }}天</p>
        <p class="mb-0"><strong>续存次数：</strong>{{ renewalInfo.current + 1 }}/{{ renewalInfo.limit }}</p>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-center gap-6">
        <el-button @click="showExtendDialog = false" size="large" class="w-40">取消</el-button>
        <el-button type="primary" @click="confirmExtend" :loading="operationLoading" size="large" class="w-40"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 报废对话框 -->
  <el-dialog
    v-model="showDiscardDialog"
    align-center
    append-to-body
    :title="'报废'"
    width="480px"
    :close-on-click-modal="false"
    custom-class="discard-wine-dialog">
    <div class="text-center p-6">
      <div class="text-[#E23939] text-xl font-medium mb-6">报废</div>
    </div>
    <template #footer>
      <div class="flex justify-center pb-6">
        <el-button type="danger" @click="confirmDiscard" :loading="operationLoading" size="large" class="w-40 !h-[46px]"> 确认报废 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { watch, ref, computed, reactive, onMounted } from 'vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import LeftRightLayout from '@/components/Dialog/layout/LeftRightLayout.vue';
import { DialogUIType } from '@/types/dialog';
import { useStoreWineDetailPresenter } from './presenter';
import type { ExtendedProductStorageVO } from './viewmodel';
import { ElMessage } from 'element-plus';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import { getWarehouseLabel } from '@/modules/wine/constants/moduleTypes';
import { useUserStore } from '@/stores/userStore';
import { wineOperationService } from '@/modules/wine/services/wineOperationService';
import { StorageStatusCode, INOPERABLE_STATUS_CODES } from '@/modules/wine/constants/storageStatus';
import { useProductStorageStore } from '@/stores/productStorageStore';

// Props定义
const props = defineProps<{
  modelValue: boolean;
  detail: ExtendedProductStorageVO | null;
}>();

// Emits定义
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'refresh-list'): void;
}>();

// 使用Presenter
const vm = useStoreWineDetailPresenter();

// 监听props.modelValue变化，同步到presenter state
watch(
  () => props.modelValue,
  newVal => {
    vm.actions.setDialogVisible(newVal);

    // 移除重复的操作历史获取逻辑，因为父组件已经获取了完整数据
    // 包括操作历史，不需要再次调用API
  },
  { immediate: true }
);

// 监听presenter state中dialogVisible变化，同步回父组件
watch(
  () => vm.state.dialogVisible,
  newVal => {
    emit('update:modelValue', newVal);
  }
);

// 监听props.detail变化，同步到presenter state
watch(
  () => props.detail,
  newVal => {
    vm.actions.setDetail(newVal);
  },
  { immediate: true }
);

// 移除loadOperationHistory方法，因为父组件已经获取了完整的数据

// 获取当前用户信息
const userStore = useUserStore();
const currentOperatorId = computed(() => userStore.userInfo?.employee?.id || '');
const currentOperatorName = computed(() => userStore.userInfo?.employee?.name || '');

// 引入存酒配置store
const storageStore = useProductStorageStore();

// 确保配置已加载
onMounted(async () => {
  await storageStore.ensureSettings();
});

// 续存对话框状态
const showExtendDialog = ref(false);
const operationLoading = ref(false);
const extendForm = reactive({
  expireTime: new Date() // 将在handleExtend中计算正确的续存时间
});

// 报废对话框状态
const showDiscardDialog = ref(false);
const discardForm = reactive({
  remark: ''
});

// 计算续存后的日期格式化显示
const extendDateFormatted = computed(() => {
  if (!extendForm.expireTime) return '';

  const date = new Date(extendForm.expireTime);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year} - ${month} - ${day}`;
});

// 检查是否为续存操作 - 只根据标准字段判断，不依赖文案
const isRenewalOperation = (op: any) => {
  // 方式1: 检查操作类型字段
  if (op.type === 'extend') {
    return true;
  }

  // 方式2: 检查操作类型字段 (可能的其他命名)
  if (op.operationType === 'extend') {
    return true;
  }

  // 方式3: 检查操作动作字段
  if (op.action === 'extend') {
    return true;
  }

  return false;
};

// 检查是否可以续存
const canExtend = computed(() => {
  // 确保配置已加载
  if (!storageStore.settings) return false;

  if (!vm.state.detail?.operationHistory) return true;

  // 计算当前已续存次数
  const extendCount = vm.state.detail.operationHistory.filter(isRenewalOperation).length;

  // 调试日志 - 强制输出
  console.log('=== 续存检查调试 ===');
  console.log('配置已加载:', !!storageStore.settings);
  console.log('续存天数配置:', storageStore.renewalDays);
  console.log('续存次数上限:', storageStore.renewalTimes);
  console.log('操作历史:', vm.state.detail.operationHistory);

  // 详细分析每个操作
  if (vm.state.detail.operationHistory) {
    vm.state.detail.operationHistory.forEach((op: any, index: number) => {
      const isRenewal = isRenewalOperation(op);
      console.log(`操作${index + 1}:`, {
        操作: op.typeName || op.operation || op.type,
        类型: op.type,
        是否为续存: isRenewal,
        操作数据: op
      });
    });
  }

  console.log('续存操作数量:', extendCount);
  console.log('是否可以续存:', storageStore.canRenew(extendCount));
  console.log('==================');

  return storageStore.canRenew(extendCount);
});

// 获取续存次数信息
const renewalInfo = computed(() => {
  if (!vm.state.detail?.operationHistory) return { current: 0, limit: storageStore.renewalTimes };

  const currentCount = vm.state.detail.operationHistory.filter(isRenewalOperation).length;

  return {
    current: currentCount,
    limit: storageStore.renewalTimes
  };
});

// 处理续存按钮点击
const handleExtend = async () => {
  console.log('=== 点击续存按钮 ===');
  console.log('当前详情数据:', vm.state.detail);
  console.log('续存检查结果:', canExtend.value);
  console.log('续存信息:', renewalInfo.value);

  // 确保配置已加载
  await storageStore.ensureSettings();

  // 检查是否可以续存
  if (!canExtend.value) {
    console.log('续存被拒绝，原因: 超过续存上限');
    ElMessage.warning(`已达到续存上限（${renewalInfo.value.limit}次），无法继续续存`);
    return;
  }

  if (!vm.state.detail?.expireTime) {
    ElMessage.warning('无法获取原过期时间');
    return;
  }

  // 在原过期时间基础上 + 续存天数
  const newExpireTime = storageStore.calculateRenewExpireTime(vm.state.detail.expireTime);
  extendForm.expireTime = new Date(newExpireTime * 1000);

  showExtendDialog.value = true;
};

// 处理报废按钮点击
const handleDiscard = () => {
  showDiscardDialog.value = true;
  discardForm.remark = '';
};

// 确认续存
const confirmExtend = async () => {
  if (!vm.state.detail || !vm.state.detail.id) return;

  operationLoading.value = true;
  try {
    // 使用计算后的到期时间戳
    const expireTimestamp = Math.floor(extendForm.expireTime.getTime() / 1000);

    await wineOperationService.extendWineStorage(
      vm.state.detail.id,
      expireTimestamp,
      currentOperatorId.value,
      currentOperatorName.value,
      `续存${storageStore.renewalDays}天` // 使用配置中的续存天数
    );

    showExtendDialog.value = false;
    ElMessage.success('续存成功');

    // 续存成功后重新获取详情数据，包括更新的操作历史
    if (vm.state.detail?.id) {
      await vm.actions.fetchOperationHistory(vm.state.detail.id);

      // 强制触发响应式更新
      vm.actions.setDetail({ ...vm.state.detail });
    }

    // 刷新列表数据
    emit('refresh-list');
  } catch (error) {
    // 错误已在service中处理
  } finally {
    operationLoading.value = false;
  }
};

// 确认报废
const confirmDiscard = async () => {
  if (!vm.state.detail || !vm.state.detail.id) return;

  operationLoading.value = true;
  try {
    await wineOperationService.discardWineStorage(
      vm.state.detail.id,
      currentOperatorId.value,
      currentOperatorName.value,
      '系统报废' // 固定报废原因
    );

    showDiscardDialog.value = false;
    ElMessage.success('报废成功');

    // 报废成功后重新获取详情数据，包括更新的操作历史
    if (vm.state.detail?.id) {
      await vm.actions.fetchOperationHistory(vm.state.detail.id);

      // 强制触发响应式更新
      vm.actions.setDetail({ ...vm.state.detail });
    }

    // 刷新列表数据
    emit('refresh-list');
  } catch (error) {
    // 错误已在service中处理
  } finally {
    operationLoading.value = false;
  }
};

// 计算禁用按钮的条件
const isButtonDisabled = computed(() => {
  // 获取当前状态
  const status = vm.state.detail?.statusCode;
  // 如果状态未定义，默认不禁用
  if (!status) return false;
  // 检查是否在不可操作的状态列表中
  return INOPERABLE_STATUS_CODES.includes(status as StorageStatusCode);
});

// 计算续存按钮是否禁用 - 只检查续存次数和完全不可操作状态
const isExtendDisabled = computed(() => {
  // 获取当前状态
  const status = vm.state.detail?.statusCode;

  // 检查是否为完全不可操作状态（已取完、已撤销、已报废）
  const isCompletelyInoperable = status && INOPERABLE_STATUS_CODES.includes(status as StorageStatusCode);

  // 检查续存次数限制
  const canExtendValue = canExtend.value;

  const result = isCompletelyInoperable || !canExtendValue;

  console.log('=== 续存按钮禁用检查 ===');
  console.log('存酒状态码:', status);
  console.log('完全不可操作:', isCompletelyInoperable);
  console.log('续存次数检查:', canExtendValue);
  console.log('最终禁用状态:', result);
  console.log('========================');

  return result;
});
</script>

<style lang="scss" scoped>
// 对话框样式
:deep(.extend-wine-dialog),
:deep(.discard-wine-dialog) {
  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 0;
    border-top: 1px solid #e4e7ed;
  }

  .el-input__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }

  .el-button {
    &.is-loading {
      opacity: 0.8;
      cursor: not-allowed;
    }
  }
}

// 特殊处理报废对话框
:deep(.discard-wine-dialog) {
  .el-dialog__title {
    display: none;
  }

  .el-alert {
    border-radius: 4px;

    .el-alert__icon {
      color: #f56c6c;
    }

    .el-alert__content {
      padding: 0 8px;
    }
  }

  .el-form-item__label {
    font-weight: 500;
  }

  .el-textarea__inner {
    min-height: 80px;
    border-color: #dcdfe6;

    &:focus {
      border-color: #409eff;
    }

    &::placeholder {
      color: #c0c4cc;
    }
  }
}

// 操作按钮
.el-button {
  &.w-\[240px\] {
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.4 !important;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: none !important;
    }
  }
}
</style>
