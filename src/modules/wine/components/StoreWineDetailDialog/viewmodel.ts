import { ComputedRef } from 'vue';
import type { ProductStorageVO } from '@/api/autoGenerated/shared/types/storage';

// 操作记录历史类型（API返回结构）
export interface OperationHistoryItem {
  type?: string;
  typeName?: string;
  operatorId?: string;
  operatorName?: string;
  time?: number;
  quantity?: number;
  balanceQty?: number;
  remark?: string;
  deliveryRoomName?: string; // 取酒送达包厢字段
  roomName?: string; // 存酒寄存包厢字段
  roomId?: string; // 包厢ID
}

// 操作记录类型
export interface OperationLog {
  operation: string;
  operator: string;
  operatorName?: string;
  time: number;
  quantity: number;
  balance: number;
  remark: string;
  type?: string;
  typeName?: string;
  deliveryRoomName?: string; // 取酒送达包厢字段
  roomName?: string; // 存酒寄存包厢字段
  roomId?: string; // 包厢ID
}

// 扩展的ProductStorageVO类型，包含操作记录和新增字段
export interface ExtendedProductStorageVO extends ProductStorageVO {
  operations?: OperationLog[];
  operationHistory?: OperationHistoryItem[];
  operatorId?: string;
  operatorName?: string;
  // 新增字段
  memberCardNo?: string; // 会员卡号
  storageRoomId?: string; // 存储包厢ID
  storageRoomName?: string; // 存储包厢名称
  offlineOnly?: boolean; // 是否仅线下存酒
}

// UI状态接口
export interface IStoreWineDetailState {
  dialogVisible: boolean;
  detail: ExtendedProductStorageVO | null;
}

// UI计算属性接口
export interface IStoreWineDetailComputed {
  // 操作日志数据
  operationLogs: ComputedRef<OperationLog[]>;
  // 其他计算属性
}

// UI动作接口
export interface IStoreWineDetailActions {
  // 关闭对话框
  closeDialog(): void;
  // 报废操作
  handleReport(): void;
  // 续存操作
  handleExtend(): void;
  // 格式化日期
  formatDate(timestamp?: number): string;
  // 获取剩余天数
  getRemainingDays(timestamp?: number): string;
  // 格式化短日期（带时间）
  formatShortDate(timestamp?: number): string;
  // 设置对话框可见性
  setDialogVisible(visible: boolean): void;
  // 设置详情数据
  setDetail(detail: ExtendedProductStorageVO | null): void;
  // 获取操作历史记录
  fetchOperationHistory(detailId: string): Promise<void>;
}

// 总的ViewModel接口
export interface IStoreWineDetailViewModel {
  state: IStoreWineDetailState;
  computed: IStoreWineDetailComputed;
  actions: IStoreWineDetailActions;
}
