<template>
  <app-dialog
    v-model="dialogVisible"
    :width="1680"
    :close-on-click-modal="false"
    :show-header="false"
    :show-footer="false"
    :ui-type="DialogUIType.LARGE"
    class="take-wine-dialog">
    <!-- 使用LeftRightLayout布局 -->
    <LeftRightLayout :leftClass="'!w-[640px] !flex-none !min-w-[640px]'" :rightClass="'!flex-1'" leftTitle="取酒">
      <!-- 左侧搜索和寄存列表区域 -->
      <template #left>
        <div class="h-full flex flex-col pb-[24px] border-r">
          <!-- 搜索区域 -->
          <div class="p-5">
            <div class="w-full h-[60px] rounded-lg border border-black/10 flex items-center px-6">
              <el-input
                ref="searchInputRef"
                v-model="vm.state.searchKeyword"
                placeholder="手机号 / 姓名"
                class="text-lg !border-none no-border-input"
                :prefix-icon="Search"
                clearable
                @input="handleSearchInput"
                @keyup.enter="vm.actions.handleSearch" />
            </div>
          </div>
          <!-- 寄存酒列表区域 -->
          <div class="flex-1 px-5 flex flex-col relative">
            <!-- 独立的loading蒙层，更清晰展示loading状态 -->
            <div v-if="vm.state.loading" class="absolute z-10 inset-0 bg-white/70 flex items-center justify-center">
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mb-2"></div>
                <span class="text-primary">搜索中...</span>
              </div>
            </div>

            <div class="h-[600px] overflow-hidden">
              <el-table
                ref="storedWinesTable"
                :data="vm.state.storedWines"
                height="100%"
                :empty-text="vm.state.searchKeyword ? '未找到匹配的寄存记录' : '请输入手机号/姓名'"
                v-loading="vm.state.loading"
                :border="false"
                fixed="right"
                stripe>
                <el-table-column prop="name" label="商品名称" min-width="100">
                  <template #default="{ row }">
                    <div class="flex flex-col">
                      <span class="font-medium">{{ row.name }}</span>
                      <span class="text-xs text-gray-500 mt-1"> 寄存时间：{{ formatUnixTimestamp(row.storageTime) }} </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="remainQuantity" label="数量" align="center" width="60">
                  <template #default="{ row }">
                    <span class="font-medium">{{ row.remainQuantity }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="phoneNumber" label="手机号" align="center" min-width="120">
                  <template #default="{ row }">
                    <span>{{ row.phoneNumber || '-' }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作" min-width="90" align="center">
                  <template #default="{ row }">
                    <div class="flex items-center justify-center">
                      <erp-input-number v-model="row.takeQuantity" simple-mode size="small" />
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>

      <!-- 右侧取酒表单区域 -->
      <template #right>
        <div class="flex flex-col h-full bg-gray-50">
          <!-- 头部区域 -->
          <div class="pt-5">
            <div class="bg-white rounded-md shadow border p-5 mb-6 mx-5">
              <div class="text-lg font-medium mb-4">取酒信息</div>

              <div class="grid gap-y-4 mb-5">
                <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                  <div class="flex flex-col">
                    <div class="selector-wrapper h-[64px] border border-gray-200 rounded-md">
                      <RoomSelector
                        v-model="vm.state.roomId"
                        placeholder="请选择"
                        @change="(roomId, roomNameObj) => vm.actions.updateRoomId(roomId, roomNameObj?.name || '')"
                        label="送达包厢" />
                    </div>
                  </div>
                  <div class="flex flex-col">
                    <div class="selector-wrapper h-[64px] border border-gray-200 rounded-md">
                      <EmployeeSelector
                        v-model="vm.state.operatorId"
                        placeholder="请选择"
                        @change="handleEmployeeChange"
                        @options-loaded="handleEmployeesLoaded"
                        label="操作人" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间取酒列表 -->
          <div class="flex-1 overflow-hidden mb-6 px-5">
            <div class="bg-white rounded-md shadow border h-full flex flex-col">
              <div class="p-5 border-b">
                <div class="text-lg font-medium">取酒列表</div>
              </div>
              <div class="p-5 overflow-auto flex-1">
                <el-table :data="selectedWines" class="take-items-table" size="small" stripe :border="false" :row-style="{ height: '64px' }" height="100%">
                  <el-table-column label="商品名称" prop="name" width="130">
                    <template #default="{ row }">
                      <span class="font-medium">{{ row.name }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column prop="warehouse" label="寄存仓库" width="120" align="center">
                    <template #default="{ row }">
                      <div class="flex items-center justify-center">
                        <span>{{ getWarehouseLabel(row.warehouse) || '暂无' }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="数量" min-width="120" align="center">
                    <template #default="{ row, $index }">
                      <div class="flex items-center justify-center">
                        <erp-input-number
                          v-model="row.takeQuantity"
                          class="bordered-number-input"
                          size="small"
                          :min="0"
                          enable-decimal
                          :max="row.remainQuantity"
                          @change="
                            value => {
                              // 确保原始数据同步更新
                              const originalIndex = vm.state.storedWines.findIndex(wine => wine.id === row.id);
                              if (originalIndex !== -1) {
                                vm.state.storedWines[originalIndex].takeQuantity = value;
                              }
                            }
                          " />
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="过期日期" align="center">
                    <template #default="{ row }">
                      <span>{{ row.expireDate ? formatUnixTimestamp(row.expireDate) : '-' }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" align="center" fixed="right">
                    <template #default="{ row, $index }">
                      <div class="flex items-center justify-center">
                        <el-button circle size="small" @click="removeFromSelectedWines($index)" class="delete-btn-circle">
                          <el-icon><Minus /></el-icon>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>

                  <!-- 添加空数据提示 -->
                  <template #empty>
                    <div class="py-16 text-center">
                      <div class="text-gray-400 mb-3 text-lg">取酒列表为空</div>
                      <div class="text-gray-400 text-sm">请从左侧选择要取出的寄存酒</div>
                    </div>
                  </template>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 右侧底部按钮区域 -->
      <template #right-footer>
        <div class="border-t flex items-center justify-end !h-[120px] px-5">
          <el-button @click="dialogVisible = false" class="w-[240px] !h-[80px]">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="vm.state.isSubmitting" :disabled="!vm.computed.canSubmit.value" class="w-[240px] !h-[80px]"
            >确认支取</el-button
          >
        </div>
      </template>
    </LeftRightLayout>

    <!-- 编辑备注对话框 -->
    <el-dialog v-model="remarkDialogVisible" title="编辑备注" width="500px" :close-on-click-modal="false" append-to-body>
      <el-form>
        <el-form-item>
          <el-input v-model="currentRemark" type="textarea" :rows="4" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="remarkDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRemark">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Search, Delete, ArrowDown, Minus } from '@element-plus/icons-vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import LeftRightLayout from '@/components/Dialog/layout';
import { DialogUIType } from '@/types/dialog';
import { useTakeWinePresenter } from './presenter';
import type { ITakeWineViewModel, StoredWine } from './viewmodel';
import { useUserStore } from '@/stores/userStore';
import { formatUnixTimestamp } from '@/utils/date';
import { getWarehouseLabel } from '@/modules/wine/constants/moduleTypes';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import RoomSelector from '@/modules/wine/components/RoomSelector.vue';
import EmployeeSelector from '@/modules/wine/components/EmployeeSelector.vue';
import eventBus from '@/utils/eventBus';
import { debounce } from 'lodash-es';

// Props定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

// Emits定义
const emit = defineEmits(['update:modelValue', 'confirm', 'refresh-list']);

// 使用Presenter
const vm: ITakeWineViewModel = useTakeWinePresenter();

// 搜索输入框引用
const searchInputRef = ref<InstanceType<(typeof import('element-plus'))['ElInput']> | null>(null);

// 双向绑定modelValue
const dialogVisible = computed({
  get: () => {
    console.log('TakeWineDialog读取modelValue:', props.modelValue);
    return props.modelValue;
  },
  set: value => {
    console.log('TakeWineDialog设置modelValue:', value);
    emit('update:modelValue', value);
    // 如果对话框关闭，触发closed事件
    if (!value) {
      console.log('取酒对话框关闭');
    }
  }
});

// 直接监听props.modelValue而不是计算属性
watch(
  () => props.modelValue,
  newValue => {
    console.log('props.modelValue变化:', newValue);
    if (newValue) {
      // 对话框显示时可以执行一些初始化逻辑
      vm.actions.resetForm();
      // 如果没有数据，自动聚焦到搜索框
      nextTick(() => {
        if (!vm.state.storedWines.length && searchInputRef.value) {
          searchInputRef.value.focus();
        }
      });
    } else {
      // 对话框关闭时，确保状态清理
      console.log('取酒对话框由props触发关闭');
      vm.actions.resetForm();
    }
  },
  { immediate: true }
);

// 获取当前用户名
const userStore = useUserStore();
const currentUserName = computed(() => userStore.userInfo?.employee?.name || '');

// 表格引用
const storedWinesTable = ref();

// 已选择的酒水列表
const selectedWines = computed(() => {
  // 直接返回原始对象的引用，不创建副本
  return vm.state.storedWines.filter(wine => {
    // 使用类型断言添加临时属性
    const wineWithSelected = wine as StoredWine & { _selected?: boolean };

    // 如果已经添加到选中列表，则保持显示，即使暂时数量为0
    // 这样用户可以完成编辑而不会导致项目消失
    if (typeof wineWithSelected._selected === 'undefined') {
      // 首次筛选，根据数量判断
      wineWithSelected._selected = wine.takeQuantity > 0;
    } else if (wine.takeQuantity > 0) {
      // 数量大于0，标记为已选中
      wineWithSelected._selected = true;
    }
    return wineWithSelected._selected;
  });
  // 移除了 .map() 操作，直接返回原始对象引用
});

// 处理员工选择变更
const handleEmployeeChange = (operatorId: string, employee: any) => {
  // 确保获取正确的员工ID和名称
  if (employee && employee.name) {
    vm.actions.updateOperatorId(operatorId, employee.name);
  } else if (userStore.userInfo?.employee?.id === operatorId) {
    // 如果选择的是当前登录用户但没获取到name，使用当前用户名
    vm.actions.updateOperatorId(operatorId, currentUserName.value);
  }
};

// 处理员工选项加载完成
const handleEmployeesLoaded = (options: any[]) => {
  // 如果已经选择了员工（自动选择的情况），需要设置对应的员工名称
  if (vm.state.operatorId && !vm.state.operatorName) {
    const selectedEmployee = options.find(option => option.value === vm.state.operatorId);
    if (selectedEmployee) {
      vm.actions.updateOperatorId(vm.state.operatorId, selectedEmployee.label);
      console.log('自动选择员工:', vm.state.operatorId, selectedEmployee.label);
    }
  }
};

// 从选中列表中移除商品
const removeFromSelectedWines = (index: number) => {
  const itemToRemove = selectedWines.value[index];
  const storeIndex = vm.state.storedWines.findIndex(item => item.id === itemToRemove.id);
  if (storeIndex !== -1) {
    // 将数量设为0
    vm.state.storedWines[storeIndex].takeQuantity = 0;
    // 同时清除选中标记
    (vm.state.storedWines[storeIndex] as any)._selected = false;
  }
};

// 处理提交
const handleSubmit = async () => {
  const success = await vm.actions.handleConfirm();
  if (success) {
    console.log('取酒成功，触发wine-list-refresh事件');
    // 使用事件总线发送刷新事件
    eventBus.emit('wine-list-refresh');
    dialogVisible.value = false;
  }
};

// 处理搜索输入内容变化
const handleSearchInput = debounce(value => {
  if (!value) {
    vm.state.storedWines = []; // 当输入框为空时清空结果列表
    return;
  }

  // 判断是否为纯数字
  const isNumericOnly = /^\d+$/.test(value);

  // 根据条件自动触发搜索
  if ((isNumericOnly && value.length >= 4) || (!isNumericOnly && value.length >= 1)) {
    vm.actions.handleSearch();
  }
}, 300);

// 备注编辑相关
const remarkDialogVisible = ref(false);
const currentRemark = ref('');
const currentEditRow = ref<StoredWine | null>(null);

// 处理编辑备注
const handleEditRemark = (row: StoredWine) => {
  currentEditRow.value = row;
  currentRemark.value = row.remark || '';
  remarkDialogVisible.value = true;
};

// 保存备注
const saveRemark = () => {
  if (currentEditRow.value) {
    // 使用类型断言添加remark属性
    (currentEditRow.value as any).remark = currentRemark.value;
    remarkDialogVisible.value = false;
  }
};
</script>

<style scoped>
.text-primary {
  color: var(--el-color-primary);
}

.border-primary {
  border-color: var(--el-color-primary);
}

.bg-primary {
  background-color: var(--el-color-primary);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 新增样式 */
.bordered-number-input :deep(.el-input-number__decrease),
.bordered-number-input :deep(.el-input-number__increase) {
  border: 1px dashed rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  top: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bordered-number-input :deep(.el-input__wrapper) {
  padding: 0 6px;
  box-shadow: none;
}

.bordered-number-input :deep(.el-input__inner) {
  text-align: center;
}

.delete-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.delete-btn-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* 表格样式调整 */
:deep(.el-table) {
  --el-table-tr-bg-color: transparent;
  --el-table-header-bg-color: white;
  --el-table-row-hover-bg-color: rgba(0, 0, 0, 0.02);
}

:deep(.el-table .el-table__row.row-odd) {
  background-color: #f3f3f3;
}

:deep(.el-table .cell) {
  padding: 8px;
}
</style>
