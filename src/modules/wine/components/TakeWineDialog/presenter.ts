import { reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { postApiProductWithdrawQueryWithdrawableItems, postApiProductWithdrawBatchAdd } from '@/api/autoGenerated/defaultVersion/product';
import type { ITakeWineViewModel, ITakeWineState, ITakeWineComputed, ITakeWineActions, StoredWine } from './viewmodel';
import { BatchAddProductWithdrawReqDto } from '@/api/autoGenerated/shared/types/product';
import { useVenueStore } from '@/stores/venueStore';

export class TakeWinePresenter implements ITakeWineViewModel {
  // 内部状态
  public state: ITakeWineState = reactive({
    // 搜索相关
    searchKeyword: '',
    loading: false,

    // 客户和酒水信息
    customerInfo: null,
    storedWines: [],

    // 表单数据
    remark: '',
    isSubmitting: false,

    // 新增字段
    roomId: '',
    roomName: '',
    operatorId: '',
    operatorName: ''
  });

  // 计算属性
  public computed: ITakeWineComputed = {
    // 是否有选择的酒水
    hasSelectedWines: computed(() => {
      return this.state.storedWines.some(wine => wine.takeQuantity > 0);
    }),

    // 选择的酒水数量总计
    totalSelectedQuantity: computed(() => {
      return this.state.storedWines.reduce((total, wine) => total + wine.takeQuantity, 0);
    }),

    // 是否可以提交
    canSubmit: computed(() => {
      return this.computed.hasSelectedWines.value && Boolean(this.state.operatorId);
    })
  };

  // 动作实现
  public actions: ITakeWineActions = {
    // 搜索客户
    handleSearch: async () => {
      if (!this.state.searchKeyword) {
        ElMessage.warning('请输入搜索关键词');
        return;
      }

      console.log('搜索开始，设置loading=true');
      this.state.loading = true;

      try {
        console.log('正在发送API请求...');
        const { data } = await postApiProductWithdrawQueryWithdrawableItems({
          searchValue: this.state.searchKeyword,
          venueId: useVenueStore().venueId!,
          includeExpiringSoon: true,
          fuzzyMatch: true,
          searchType: 'all' // 可根据实际情况选择搜索类型：name/phone/id/card
        });
        console.log('API请求完成，获取到数据');

        if (data) {
          // 更新客户信息
          this.state.customerInfo = data.customerInfo
            ? {
                id: data.customerInfo.customerId || '',
                name: data.customerInfo.customerName || '',
                mobile: data.customerInfo.phoneNumber || '',
                cardNo: '', // 暂不使用卡号
                orderPerson: ''
              }
            : null;

          // 更新存酒列表
          this.state.storedWines = (data.withdrawableItems || []).map(item => ({
            id: item.id || '',
            name: item.productName || '',
            warehouse: item.storageLocation || '存酒仓库',
            remainQuantity: item.remainingQty || 0,
            unit: item.productUnit || '',
            takeQuantity: 0,
            productId: item.productId || '',
            storageTime: item.storageTime || 0,
            parentOrderNo: item.parentOrderNo || '',
            orderNo: item.orderNo || '',
            remark: '', // 默认为空备注
            expireDate: item.expireTime || 0,
            // 存酒人手机号，直接使用服务端字段名
            phoneNumber: item.phoneNumber || ''
          }));
          console.log(`搜索结果: ${this.state.storedWines.length}条数据`);
        } else {
          ElMessage.warning('未找到相关客户或寄存记录');
          this.state.customerInfo = null;
          this.state.storedWines = [];
        }
      } catch (error) {
        console.error('搜索出错:', error);
        ElMessage.error('搜索客户信息失败');
        this.state.customerInfo = null;
        this.state.storedWines = [];
      } finally {
        // 强制延迟一段时间再关闭loading状态，确保用户能看到loading效果
        setTimeout(() => {
          console.log('搜索结束，设置loading=false');
          this.state.loading = false;
        }, 300);
      }
    },

    // 减少数量
    decreaseQuantity: (index: number) => {
      const wine = this.state.storedWines[index];
      if (wine.takeQuantity > 0) {
        wine.takeQuantity--;
      }
    },

    // 增加数量
    increaseQuantity: (index: number) => {
      const wine = this.state.storedWines[index];
      if (wine.takeQuantity < wine.remainQuantity) {
        wine.takeQuantity++;
      }
    },

    // 验证输入
    validateInput: () => {
      if (!this.computed.hasSelectedWines.value) {
        ElMessage.warning('请选择需要取出的酒');
        return false;
      }

      if (!this.state.operatorId) {
        ElMessage.warning('请选择操作人');
        return false;
      }

      return true;
    },

    // 重置表单
    resetForm: () => {
      this.state.searchKeyword = '';
      this.state.customerInfo = null;
      this.state.storedWines = [];
      this.state.remark = '';
      this.state.roomId = '';
      this.state.roomName = '';
      this.state.operatorId = '';
      this.state.operatorName = '';
    },

    // 确认取酒
    handleConfirm: async () => {
      if (!this.actions.validateInput()) {
        return false;
      }

      this.state.isSubmitting = true;

      try {
        // 筛选出要取的商品
        const selectedItems = this.state.storedWines.filter(wine => wine.takeQuantity > 0);

        // 准备API请求参数
        const payload: BatchAddProductWithdrawReqDto = {
          operatorId: this.state.operatorId,
          operatorName: this.state.operatorName,
          remark: this.state.remark,
          venueId: useVenueStore().venueId!,
          withdrawTime: Math.floor(Date.now() / 1000),
          deliveryRoomId: this.state.roomId,
          deliveryRoomName: this.state.roomName,
          withdrawItems: selectedItems.map(item => ({
            storageId: item.id,
            storageOrderNo: item.orderNo || '',
            quantity: item.takeQuantity,
            productSpec: '',
            productUnit: item.unit
          }))
        };

        // 发送请求
        await postApiProductWithdrawBatchAdd(payload);

        ElMessage.success('取酒成功');
        this.actions.resetForm();
        return true;
      } catch (error: any) {
        ElMessage.error(error?.message || '取酒失败');
        console.error(error);
        return false;
      } finally {
        this.state.isSubmitting = false;
      }
    },

    // 数量变更处理方法
    handleQuantityChange: (value: number, row: StoredWine) => {
      const index = this.state.storedWines.findIndex(wine => wine.id === row.id);
      if (index !== -1) {
        // 确保数量在有效范围内
        const validValue = Math.min(Math.max(value, 0), row.remainQuantity);
        this.state.storedWines[index].takeQuantity = validValue;
      }
    },

    // 更新送达包厢
    updateRoomId: (roomId: string, roomName: string) => {
      this.state.roomId = roomId;
      this.state.roomName = roomName;
    },

    // 更新操作人
    updateOperatorId: (operatorId: string, operatorName: string) => {
      this.state.operatorId = operatorId;
      this.state.operatorName = operatorName;
    }
  };
}

// 导出组合式函数
export function useTakeWinePresenter(): ITakeWineViewModel {
  return new TakeWinePresenter();
}
