import { ComputedRef } from 'vue';

// 客户信息接口
export interface CustomerInfo {
  id: string;
  name: string;
  mobile: string;
  cardNo: string;
  orderPerson: string;
}

// 存酒信息接口
export interface StoredWine {
  id: string;
  name: string;
  warehouse: string;
  remainQuantity: number;
  unit: string;
  takeQuantity: number;
  productId?: string;
  storageTime?: number;
  parentOrderNo?: string;
  orderNo?: string;
  flavor?: string;
  remark?: string;
  expireDate?: number;
  // 存酒人手机号字段，与服务端字段名保持一致
  phoneNumber?: string;
}

// 取酒API请求参数
export interface TakeWineApiPayload {
  customerId: string;
  items: {
    storageRecordId: string;
    quantity: number;
  }[];
  remark?: string;
  roomId?: string;
  operatorId?: string;
}

// UI状态接口
export interface ITakeWineState {
  // 搜索相关
  searchKeyword: string;
  loading: boolean;

  // 客户和酒水信息
  customerInfo: CustomerInfo | null;
  storedWines: StoredWine[];

  // 表单数据
  remark: string;
  isSubmitting: boolean;

  // 新增字段
  roomId: string; // 送达包厢ID
  roomName: string; // 送达包厢名称
  operatorId: string; // 操作人ID
  operatorName: string; // 操作人名称
}

// UI计算属性接口
export interface ITakeWineComputed {
  // 是否有选择的酒
  hasSelectedWines: ComputedRef<boolean>;

  // 选择的酒总数量
  totalSelectedQuantity: ComputedRef<number>;

  // 是否可以提交
  canSubmit: ComputedRef<boolean>;
}

// UI动作接口
export interface ITakeWineActions {
  // 搜索客户
  handleSearch(): Promise<void>;

  // 增加数量
  increaseQuantity(index: number): void;

  // 减少数量
  decreaseQuantity(index: number): void;

  // 数量变更处理
  handleQuantityChange: (value: number, row: StoredWine) => void;

  // 验证输入
  validateInput(): boolean;

  // 重置表单
  resetForm(): void;

  // 确认取酒
  handleConfirm(): Promise<boolean>;

  // 更新送达包厢
  updateRoomId(roomId: string, roomName: string): void;

  // 更新操作人
  updateOperatorId(operatorId: string, operatorName: string): void;
}

// 总的ViewModel接口
export interface ITakeWineViewModel {
  state: ITakeWineState;
  computed: ITakeWineComputed;
  actions: ITakeWineActions;
}
