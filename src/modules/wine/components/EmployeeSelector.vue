<template>
  <div class="employee-selector" :class="{ 'is-disabled': disabled }">
    <div class="selector-label">{{ label }}</div>
    <el-select
      v-model="innerValue"
      class="selector-input no-border-selecter"
      :placeholder="placeholder"
      :loading="internalLoading"
      :disabled="disabled"
      popper-class="employee-selector-dropdown"
      @focus="handleFocus"
      @change="handleChange">
      <el-option v-for="option in internalOptions" :key="option.value" :label="option.label" :value="option.value" />
      <template #empty>
        <div class="py-2 px-4 text-center text-gray-500">暂无可用员工</div>
      </template>
    </el-select>
    <div v-if="error" class="error-message text-red-500 text-xs mt-1">{{ error }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import type { SelectOption } from '../components/StoreWineDialog/viewmodel';
import { useUserStore } from '@/stores/userStore';
import { useVenueStore } from '@/stores/venueStore';
import { postApiEmployeeQuery } from '@/api/autoGenerated/defaultVersion/employee';
import { EmployeeVO } from '@/api/autoGenerated/shared/types/employee';

// Props定义
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Array as () => SelectOption[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: '代订人'
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  autoLoad: {
    type: Boolean,
    default: true
  },
  selectCurrentUser: {
    type: Boolean,
    default: true
  },
  error: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

// Emits定义
const emit = defineEmits(['update:modelValue', 'focus', 'change', 'options-loaded', 'clear']);

// 内部状态
const internalLoading = ref(false);
const internalOptions = ref<SelectOption[]>([]);

// 使用外部options或内部options - 修复逻辑
const useInternalOptions = computed(() => {
  // 如果外部没有传递有效的employee数据（只有"全部"这样的默认选项），则使用内部加载
  const hasValidEmployeeOptions = props.options.some(option => option.value && option.value !== '');
  return !hasValidEmployeeOptions;
});

// 监听外部options变化
watch(
  () => props.options,
  newOptions => {
    if (newOptions.length > 0) {
      internalOptions.value = newOptions;
    }
  },
  { immediate: true }
);

// 内部值处理
const innerValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val)
});

// 处理焦点事件，触发数据加载
const handleFocus = () => {
  emit('focus');
  if (useInternalOptions.value) {
    loadEmployees();
  }
};

// 处理变更事件
const handleChange = (value: string) => {
  // 查找选中的员工对象并传递
  if (value) {
    const selectedEmployee = internalOptions.value.find(option => option.value === value);
    if (selectedEmployee) {
      // 传递完整的员工信息，包含id和name
      emit('change', value, {
        id: selectedEmployee.value,
        name: selectedEmployee.label,
        ...selectedEmployee
      });
    } else {
      emit('change', value);
    }
  } else {
    // 如果值为空，触发清除事件
    emit('clear');
    emit('change', '');
  }
};

// 加载员工数据
const loadEmployees = async () => {
  console.log('🔄 EmployeeSelector: 开始加载员工数据');
  console.log('🔄 当前状态:', {
    internalLoading: internalLoading.value,
    optionsLength: internalOptions.value.length,
    useInternalOptions: useInternalOptions.value
  });

  if (internalLoading.value || internalOptions.value.length > 0) {
    console.log('⚠️ EmployeeSelector: 跳过加载 - 正在加载中或已有数据');
    return; // 避免重复加载
  }

  internalLoading.value = true;
  try {
    const userStore = useUserStore();
    const venueStore = useVenueStore();
    const currentEmployeeId = userStore.userInfo?.employee?.id || '';
    const currentVenueId = venueStore.venueId;

    console.log('👤 当前用户员工ID:', currentEmployeeId);
    console.log('🏢 当前门店ID:', currentVenueId);

    if (!currentVenueId) {
      console.error('❌ EmployeeSelector: 门店ID为空，无法查询员工列表');
      setupDefaultEmployeeData();
      return;
    }

    console.log('📡 EmployeeSelector: 调用API - postApiEmployeeQuery');

    // 调用员工查询API - 添加门店ID参数
    const response = await postApiEmployeeQuery({
      // 确保员工处于正常状态
      reviewStatus: 1,
      // 指定门店ID
      venueId: currentVenueId
    });

    console.log('📡 EmployeeSelector: API响应:', {
      code: response.code,
      dataLength: response.data?.length || 0,
      data: response.data
    });

    if (response.code === 0 && response.data && response.data.length > 0) {
      // 转换员工数据为下拉选项格式
      const options = response.data.map((employee: EmployeeVO) => ({
        label: employee.name || '',
        value: employee.id || '',
        employeeData: employee
      }));

      console.log('✅ EmployeeSelector: 员工数据转换完成:', options);

      internalOptions.value = options;

      // 通知父组件选项已加载完成
      emit('options-loaded', options);
      console.log('📤 EmployeeSelector: 已发送options-loaded事件');

      // 处理默认选择逻辑
      if (!innerValue.value) {
        if (props.selectCurrentUser && currentEmployeeId) {
          // 如果配置了自动选择当前用户，并且当前没有选中值，则自动选择
          // 检查当前用户是否在返回的列表中
          const currentUserInList = options.some(option => option.value === currentEmployeeId);
          if (currentUserInList) {
            // 先设置值
            emit('update:modelValue', currentEmployeeId);
            // 立即触发change事件，传递完整员工信息
            const selectedEmployee = options.find(option => option.value === currentEmployeeId);
            if (selectedEmployee) {
              emit('change', currentEmployeeId, {
                id: selectedEmployee.value,
                name: selectedEmployee.label,
                ...selectedEmployee
              });
            }
            console.log('✅ EmployeeSelector: 自动选择当前用户:', currentEmployeeId);
          } else if (options.length > 0) {
            // 如果当前用户不在列表中，选择第一个员工
            const firstEmployee = options[0]; // 选择第一个员工
            if (firstEmployee) {
              // 先设置值
              emit('update:modelValue', firstEmployee.value);
              // 立即触发change事件，传递完整员工信息
              emit('change', firstEmployee.value, {
                id: firstEmployee.value,
                name: firstEmployee.label,
                ...firstEmployee
              });
            }
            console.log('✅ EmployeeSelector: 自动选择第一个员工:', firstEmployee);
          }
        } else if (!props.selectCurrentUser) {
          console.log('ℹ️ EmployeeSelector: 不自动选择当前用户，保持空值');
          // 如果不自动选择当前用户，默认保持空值（对应"全部"）
          // 这样父组件可以通过监听options-loaded事件来设置默认值
        }
      }
    } else {
      console.error('❌ EmployeeSelector: 获取员工列表失败:', response);
      // 加载失败时使用默认数据
      setupDefaultEmployeeData();
    }
  } catch (error) {
    console.error('❌ EmployeeSelector: 获取员工列表异常:', error);
    // 加载失败时使用默认数据
    setupDefaultEmployeeData();
  } finally {
    internalLoading.value = false;
    console.log('🏁 EmployeeSelector: 员工数据加载完成');
  }
};

// 设置默认员工数据
const setupDefaultEmployeeData = () => {
  const userStore = useUserStore();
  const currentEmployeeId = userStore.userInfo?.employee?.id || '';
  const currentEmployeeName = userStore.userInfo?.employee?.name || '当前用户';

  // 如果有当前用户，至少添加当前用户作为选项
  if (currentEmployeeId) {
    const options = [
      {
        label: currentEmployeeName,
        value: currentEmployeeId,
        employeeData: {
          id: currentEmployeeId,
          name: currentEmployeeName
        }
      }
    ];

    internalOptions.value = options;

    // 通知父组件选项已加载完成
    emit('options-loaded', options);

    // 如果配置了自动选择当前用户，并且当前没有选中值
    if (props.selectCurrentUser && !innerValue.value) {
      // 先设置值
      emit('update:modelValue', currentEmployeeId);
      // 再触发change事件，确保名称被正确传递
      emit('change', currentEmployeeId, {
        id: currentEmployeeId,
        name: currentEmployeeName,
        employeeData: {
          id: currentEmployeeId,
          name: currentEmployeeName
        }
      });
    }
  }
};

// 组件挂载时的初始化逻辑
onMounted(() => {
  console.log('🚀 EmployeeSelector: 组件挂载');
  console.log('🚀 初始状态:', {
    autoLoad: props.autoLoad,
    useInternalOptions: useInternalOptions.value,
    optionsLength: props.options.length,
    hasValidOptions: props.options.some(option => option.value && option.value !== '')
  });

  if (props.autoLoad && useInternalOptions.value) {
    console.log('🚀 EmployeeSelector: 开始自动加载员工数据');
    loadEmployees();
  }
});
</script>

<style scoped>
.employee-selector {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  height: 100%;
}

.employee-selector.is-disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
}

.employee-selector.is-disabled .selector-label {
  color: #c0c4cc;
}

.selector-label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  padding: 0 10px;
  width: 70px;
  color: #606266;
  font-size: 14px;
  background-color: #fff;
  flex-shrink: 0;
  white-space: nowrap;
}
</style>
