import { ref, reactive, computed, nextTick } from 'vue';
import { debounce } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { ProductApi } from '@/modules/production/api/product';
import { useUserStore } from '@/stores/userStore';
import { useProductStorageStore } from '@/stores/productStorageStore';
import { validateMobile } from '@/utils/validator';
import type {
  IStoreWineViewModel,
  IStoreWineState,
  IStoreWineComputed,
  IStoreWineActions,
  ProductCategory,
  Product,
  WineItem,
  SelectOption
} from './viewmodel';
import { convertToApiPayload } from './converter';
import { postApiProductStorageAdd } from '@/api/autoGenerated/defaultVersion/product';
import type { ResultVoProductStorageAddResultVO } from '@/api/autoGenerated/shared/types/result';
import { UnifiedProductStorageReqDto } from '@/api/autoGenerated/shared/types/product';

// 类型常量
const CATEGORY_TYPE = {
  ALL: 'all',
  PACKAGE: 'package',
  PRODUCT: 'product'
};

// API调用函数
const storeWineToApi = async (data: UnifiedProductStorageReqDto) => {
  try {
    const response: ResultVoProductStorageAddResultVO = await postApiProductStorageAdd(data);
    return {
      code: response.code || 0,
      data: response.data,
      message: response.message || '操作成功'
    };
  } catch (error: any) {
    console.error('存酒API调用失败:', error);
    return {
      code: -1,
      data: null,
      message: error?.message || '存酒失败'
    };
  }
};

export class StoreWinePresenter implements IStoreWineViewModel {
  // 存酒配置store
  private storageStore = useProductStorageStore();

  // 内部状态
  public state: IStoreWineState = reactive({
    // 商品列表状态
    categories: [],
    products: [],
    loading: false,
    searchKeyword: '',
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    isLoadingMore: false,

    // 存酒单状态
    customerInfo: {
      name: '',
      mobile: '',
      cardNo: '',
      orderPerson: ''
    },
    wineItems: [],
    onlyStoreWine: false,
    isSubmitting: false,
    employee: '',

    // 房间选择相关
    roomOptions: [],
    loadingRooms: false,
    storeRoomId: '',

    // 员工选择相关
    employeeOptions: [],
    loadingEmployees: false,
    selectedEmployee: ''
  });

  constructor() {
    // 初始化代订人为当前登录用户
    const userStore = useUserStore();
    if (userStore.userInfo?.employee?.name) {
      this.state.employee = userStore.userInfo.employee.name;
    }
  }

  // 计算属性
  public computed: IStoreWineComputed = {
    // 计算总金额（分）
    totalAmountInFen: computed(() => {
      return this.state.wineItems.reduce((total, item) => {
        return total + item.currentPrice * item.quantity;
      }, 0);
    }),

    // 计算商品总数
    totalItems: computed(() => {
      return this.state.wineItems.reduce((total, item) => {
        return total + item.quantity;
      }, 0);
    }),

    // 客户信息是否有效
    isCustomerInfoValid: computed(() => {
      const { mobile } = this.state.customerInfo;
      if (!mobile.trim()) {
        return false;
      }
      // 验证手机号格式
      const mobileValidation = validateMobile(mobile);
      return mobileValidation.isValid;
    }),

    // 存酒单是否有效
    isWineListValid: computed(() => {
      return this.state.wineItems.length > 0;
    }),

    // 整个表单是否有效
    isFormValid: computed(() => {
      return this.computed.isCustomerInfoValid.value && this.computed.isWineListValid.value;
    })
  };

  // 取消请求控制器
  private abortControllers: AbortController[] = [];

  // 初始化加载数据
  private async fetchCategories() {
    if (this.state.categories.length > 0) {
      return; // 已有数据，无需重复加载
    }

    try {
      const controller = new AbortController();
      this.abortControllers.push(controller);

      const response = await ProductApi.listProductTypes({});

      if (response.code === 0) {
        // 普通商品分类
        const productTypes = response.data.productTypeVOs.map((type: any) => ({
          id: type.id,
          name: type.name,
          count: type.count,
          isActive: false,
          type: CATEGORY_TYPE.PRODUCT,
          isPackage: false
        }));

        // 所有分类（只包含全部分类和普通商品分类，不包含套餐分类）
        const allCategories: ProductCategory[] = [
          {
            id: null,
            name: '全部分类',
            count: '',
            isActive: true,
            type: CATEGORY_TYPE.ALL,
            isPackage: false
          },
          ...productTypes
        ];

        this.state.categories = allCategories;
      }
    } catch (error) {
      console.error('获取商品分类失败:', error);
    } finally {
      // 移除已完成的控制器
      this.abortControllers = this.abortControllers.filter(controller => controller.signal.aborted);
    }
  }

  // 加载商品列表
  private async fetchProducts(isLoadingMore = false) {
    if (this.state.loading && !isLoadingMore) {
      return; // 防止重复加载
    }

    try {
      const controller = new AbortController();
      this.abortControllers.push(controller);

      if (!isLoadingMore) {
        this.state.loading = true;
      } else {
        this.state.isLoadingMore = true;
      }

      // 找到选中的分类
      const selectedCategory = this.state.categories.find(c => c.isActive) ?? null;

      // 创建API参数对象
      const apiParams: any = {
        pageNum: this.state.currentPage,
        pageSize: this.state.pageSize
      };

      // 只针对普通商品的分类进行查询，忽略套餐分类
      if (selectedCategory?.isPackage) {
        // 如果是套餐分类，不进行查询，直接返回空结果
        this.state.products = [];
        this.state.hasMore = false;
        this.state.loading = false;
        this.state.isLoadingMore = false;
        return;
      } else {
        // 普通商品分类或全部分类
        apiParams.category = selectedCategory?.id || '';
      }

      // 添加搜索关键字参数
      if (this.state.searchKeyword.trim()) {
        apiParams.name = this.state.searchKeyword.trim();
      }

      console.log('API参数:', apiParams);

      // 调用API
      const response = await ProductApi.queryDetailByType(apiParams);

      if (response.code === 0) {
        // 只处理普通商品数据，忽略套餐
        const productList = response.data.productVOs || [];
        const normalProducts = productList.map((item: any) => ({
          id: item.id,
          name: item.name,
          stock: item.calculateInventory ? item.lowStockThreshold : -1,
          tag: item.isPromotion ? '促销' : '',
          currentPrice: item.currentPrice,
          unit: item.unit,
          isPackage: false,
          isSoldOut: item.isSoldOut,
          quantity: 0
        }));

        // 更新商品列表 - 只包含普通商品
        if (isLoadingMore) {
          this.state.products = [...this.state.products, ...normalProducts];
        } else {
          this.state.products = normalProducts;
        }

        // 更新是否有更多数据
        this.state.hasMore = normalProducts.length === this.state.pageSize;
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
    } finally {
      this.state.loading = false;
      this.state.isLoadingMore = false;

      // 移除已完成的控制器
      this.abortControllers = this.abortControllers.filter(controller => controller.signal.aborted);
    }
  }

  // 动作实现
  public actions: IStoreWineActions = {
    // 初始化加载数据
    loadInitialData: () => {
      this.fetchCategories();
      this.fetchProducts();
    },

    // 切换分类
    changeCategory: (categoryId: string | null) => {
      // 如果选择的是相同分类，不做处理
      const currentCategory = this.state.categories.find(c => c.isActive);
      if (currentCategory?.id === categoryId) {
        return;
      }

      // 更新分类选中状态
      this.state.categories.forEach(category => {
        category.isActive = category.id === categoryId;
      });

      // 重置页码并重新加载数据
      this.state.currentPage = 1;
      this.state.hasMore = true;
      this.fetchProducts();
    },

    // 处理搜索 - 使用debounce
    handleSearch: debounce(() => {
      this.state.currentPage = 1;
      this.state.hasMore = true;
      this.fetchProducts();
    }, 300),

    // 清除搜索
    clearSearch: () => {
      this.state.searchKeyword = '';
      this.state.currentPage = 1;
      this.state.hasMore = true;
      this.fetchProducts();
    },

    // 设置表格行的类名
    tableRowClassName: ({ row }: { row: Product }) => {
      if (row.isSoldOut) {
        return 'sold-out-row';
      }
      return '';
    },

    // 处理表格滚动加载更多
    handleTableScroll: (e: any) => {
      if (!e.target) return;

      const { scrollTop, scrollHeight, clientHeight } = e.target;

      // 判断是否滚动到底部 (距离底部小于50px)
      if (scrollHeight - scrollTop - clientHeight < 50) {
        if (!this.state.isLoadingMore && this.state.hasMore) {
          // 加载更多数据
          this.state.currentPage++;
          this.fetchProducts(true);
        }
      }
    },

    // 添加商品到存酒清单
    handleAddToCart: (product: Product) => {
      if (!product.quantity || product.quantity <= 0) {
        return;
      }

      // 查找清单中是否已存在该商品
      const existingItem = this.state.wineItems.find(item => item.id === product.id);

      if (existingItem) {
        // 如果已存在，增加数量
        existingItem.quantity += product.quantity;
      } else {
        // 如果不存在，添加到清单
        this.state.wineItems.push({
          id: product.id,
          name: product.name,
          warehouse: 'main', // 默认仓库
          quantity: product.quantity,
          unit: product.unit || '份',
          currentPrice: product.currentPrice
        });
      }

      // 重置商品数量为0
      product.quantity = 0;
    },

    // 处理存酒项数量变更
    handleWineItemQuantityChange: (quantity: number, item: WineItem) => {
      item.quantity = quantity;
    },

    // 移除存酒项
    removeWineItem: (index: number) => {
      this.state.wineItems.splice(index, 1);
    },

    // 清空存酒清单
    clearWineItems: () => {
      this.state.wineItems = [];
    },

    // 确认存酒
    handleConfirm: async () => {
      if (this.state.isSubmitting) {
        return; // 防止重复提交
      }

      try {
        this.state.isSubmitting = true;

        // 使用计算属性验证表单
        if (!this.computed.isFormValid.value) {
          if (!this.computed.isCustomerInfoValid.value) {
            // 如果客户信息无效
            if (!this.state.customerInfo.mobile.trim()) {
              ElMessage.warning('请输入手机号');
            }
          } else if (!this.computed.isWineListValid.value) {
            // 如果存酒列表为空
            ElMessage.warning('存酒清单为空，请添加商品');
          }

          this.state.isSubmitting = false;
          return;
        }

        // 确保存酒配置已加载
        await this.storageStore.ensureSettings();

        // 使用converter处理数据，传入存储天数配置
        const apiPayload = convertToApiPayload({
          customerInfo: this.state.customerInfo,
          wineItems: this.state.wineItems,
          onlyStoreWine: this.state.onlyStoreWine,
          employeeId: this.state.selectedEmployee,
          employeeName: this.state.employee,
          roomId: this.state.storeRoomId,
          storageDays: this.storageStore.storageDays // 使用配置中的存储天数
        });

        console.log('提交数据:', apiPayload);

        // 移除调试代码，恢复API调用
        const response = await storeWineToApi(apiPayload);

        if (response && response.code === 0) {
          // 成功提示
          ElMessage.success('存酒成功');
          // 保存当前代订人
          const currentEmployee = this.state.employee;

          // 清空表单
          this.state.customerInfo = {
            name: '',
            mobile: '',
            cardNo: '',
            orderPerson: ''
          };
          this.state.wineItems = [];
          this.state.onlyStoreWine = false;

          // 恢复代订人
          this.state.employee = currentEmployee;

          // 返回成功结果
          return {
            success: true,
            data: response.data
          };
        } else {
          // 错误提示
          ElMessage.error(response?.message || '存酒失败，请重试');
          return {
            success: false,
            error: response?.message || '存酒失败'
          };
        }
      } catch (error: any) {
        ElMessage.error('存酒失败，请重试');
        return {
          success: false,
          error: error?.message || '存酒失败'
        };
      } finally {
        this.state.isSubmitting = false;
      }
    },

    // 加载包厢列表
    loadRooms: () => {},

    // 加载员工列表
    loadEmployees: async () => {},

    // 处理房间选择
    handleRoomSelect: (room: any) => {
      console.log('handleRoomSelect被调用，参数：', room);
      if (room && room.id) {
        console.log(`设置包厢信息: ID=${room.id}, 名称=${room.name}`);
        this.state.storeRoomId = room.id;
        this.state.customerInfo.storeRoom = room.name;
        // 打印更新后的状态
        console.log('更新后的包厢信息:', {
          storeRoomId: this.state.storeRoomId,
          storeRoom: this.state.customerInfo.storeRoom
        });
      } else {
        console.warn('handleRoomSelect被调用，但room对象无效或缺少id属性');
      }
    },

    // 清除房间选择
    clearRoomSelection: () => {
      console.log('clearRoomSelection被调用');
      this.state.storeRoomId = '';
      this.state.customerInfo.storeRoom = '';
      console.log('清除后的包厢信息:', {
        storeRoomId: this.state.storeRoomId,
        storeRoom: this.state.customerInfo.storeRoom
      });
    }
  };

  // 取消所有请求
  public cancelAllRequests() {
    this.abortControllers.forEach(controller => {
      if (!controller.signal.aborted) {
        controller.abort();
      }
    });
    this.abortControllers = [];
  }
}

// 导出组合式函数
export function useStoreWinePresenter(): IStoreWineViewModel {
  return new StoreWinePresenter();
}
