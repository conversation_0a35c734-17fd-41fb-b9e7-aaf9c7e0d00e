import { UnifiedProductStorageReqDto } from '@/api/autoGenerated/shared/types/product';
import { WineItem, CustomerInfo } from './viewmodel';
import { calculateExpireDate } from '@/utils/dateUtils';

/**
 * 转换参数接口
 */
interface ConvertParams {
  customerInfo: CustomerInfo;
  wineItems: WineItem[];
  onlyStoreWine: boolean;
  employeeId: string;
  employeeName: string;
  roomId?: string;
  storageDays?: number; // 存储天数配置
}

/**
 * 将视图模型数据转换为API请求数据
 * @param params 转换参数
 * @returns API请求数据
 */
export function convertToApiPayload(params: ConvertParams): UnifiedProductStorageReqDto {
  const { customerInfo, wineItems, onlyStoreWine, employeeId, employeeName, roomId, storageDays = 30 } = params;

  // 使用配置中的存储天数计算过期时间
  const { timestamp: expireTime } = calculateExpireDate(storageDays);

  // 创建API请求参数
  const payload = {
    // 客户信息
    customerName: customerInfo.name,
    phoneNumber: customerInfo.mobile,
    memberCardNo: customerInfo.cardNo,
    operatorId: employeeId || '',
    operatorName: employeeName || '',
    // 添加寄存包厢ID
    storageRoomId: roomId || '',
    // 添加寄存包厢名称
    storageRoomName: customerInfo.storeRoom || '',
    // 添加是否仅线下存酒
    offlineOnly: onlyStoreWine,

    // 存酒项
    items: wineItems.map(item => ({
      productId: item.id,
      productName: item.name,
      productUnit: item.unit,
      quantity: item.quantity,
      storageLocation: item.warehouse,
      expireTime,
      remark: item.remark || ''
    }))
  } as UnifiedProductStorageReqDto; // 使用类型断言绕过类型检查

  return payload;
}
