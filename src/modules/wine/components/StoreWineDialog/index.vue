<template>
  <app-dialog
    v-model="dialogVisible"
    :width="1680"
    :close-on-click-modal="false"
    :show-header="false"
    :show-footer="false"
    :ui-type="DialogUIType.LARGE"
    class="store-wine-dialog">
    <!-- 使用LeftRightLayout布局 -->
    <LeftRightLayout :leftClass="'!w-[640px] !flex-none !min-w-[640px]'" :rightClass="'!flex-1'" leftTitle="商品列表">
      <!-- 左侧商品列表区域 -->
      <template #left>
        <div class="h-full flex flex-col pb-[24px] border-r">
          <!-- 商品内容区域 - 使用flex和子元素定位 -->
          <div class="flex flex-1 relative overflow-hidden">
            <!-- 分类侧边栏 -->
            <div class="w-[160px] h-full overflow-y-auto border-r flex-shrink-0">
              <div class="w-[120px] mx-auto mt-5 flex flex-col gap-4">
                <div
                  v-for="category in vm.state.categories"
                  :key="category.id || 'all'"
                  class="h-[60px] rounded-lg flex items-center justify-center cursor-pointer"
                  :class="{
                    'bg-red-500 text-white': category.isActive,
                    'bg-gray-100 text-gray-800 hover:bg-gray-200': !category.isActive
                  }"
                  @click="vm.actions.changeCategory(category.id)">
                  <span class="truncate text-base font-medium">{{ category.name }}</span>
                </div>
              </div>
            </div>

            <!-- 商品区域 - 使用flex和el-scrollbar -->
            <div class="flex-1 flex flex-col overflow-hidden">
              <!-- 搜索框 -->
              <div class="p-5 flex-shrink-0">
                <div class="w-full h-[60px] rounded-lg border border-black/10 flex items-center px-6">
                  <el-input
                    v-model="vm.state.searchKeyword"
                    placeholder="搜索商品"
                    class="text-lg !border-none no-border-input"
                    :prefix-icon="Search"
                    clearable
                    @input="vm.actions.handleSearch"
                    @clear="vm.actions.clearSearch" />
                </div>
              </div>

              <!-- 商品列表 - 使用简单div结构 -->
              <div class="flex-1 flex flex-col overflow-hidden">
                <!-- 表头 -->
                <div class="flex bg-gray-50 border-b text-sm text-gray-600 font-medium py-3">
                  <div class="flex-1 px-4">商品名称</div>
                  <div class="w-[100px] text-center">操作</div>
                </div>

                <!-- 商品列表内容 -->
                <div class="flex-1 overflow-y-auto" v-loading="vm.state.loading">
                  <!-- 商品行 -->
                  <div
                    v-for="(product, index) in vm.state.products"
                    :key="product.id || index"
                    class="flex items-center border-b py-3 hover:bg-gray-50 transition-colors"
                    :class="{ 'bg-gray-50': index % 2 === 1 }">
                    <div class="flex-1 px-4">
                      <span class="text-sm text-gray-900" :title="product.name">{{ product.name }}</span>
                    </div>
                    <div class="w-[100px] flex justify-center">
                      <erp-input-number v-model="product.quantity" simple-mode size="small" @update:modelValue="vm.actions.handleAddToCart(product)" />
                    </div>
                  </div>

                  <!-- 空数据提示 -->
                  <div v-if="!vm.state.loading && !vm.state.products.length" class="flex flex-col items-center justify-center py-20 text-gray-400">
                    <div class="text-lg mb-2">{{ vm.state.searchKeyword ? '没有找到匹配的商品' : '暂无商品数据' }}</div>
                    <div class="text-sm">
                      {{ vm.state.searchKeyword ? `没有找到包含"${vm.state.searchKeyword}"的商品，请尝试其他关键词` : '请选择其他分类或刷新页面' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 右侧存酒表单区域 -->
      <template #right>
        <div class="flex flex-col h-full bg-gray-50 overflow-hidden">
          <!-- 头部表单区域 -->
          <div class="pt-5 flex-shrink-0">
            <div class="bg-white rounded-md shadow border p-5 mb-6 customer-info-section">
              <div class="text-lg font-medium mb-4">客户信息</div>
              <div class="grid grid-cols-3 gap-x-4 gap-y-6">
                <div class="relative">
                  <div class="absolute left-4 top-0 bottom-0 flex items-center text-gray-500 text-sm z-10">姓名</div>
                  <input
                    type="text"
                    v-model="vm.state.customerInfo.name"
                    class="w-full h-[64px] pl-[72px] border border-gray-200 rounded-md focus:border-blue-500 focus:outline-none text-base"
                    placeholder="请输入姓名" />
                </div>

                <div class="relative">
                  <div class="absolute left-4 top-0 bottom-0 flex items-center text-gray-500 text-sm z-10"><span class="text-red-500 mr-1">*</span>手机号</div>
                  <input
                    type="text"
                    :value="vm.state.customerInfo.mobile"
                    @input="handleMobileInput(($event.target as HTMLInputElement).value)"
                    @blur="handleMobileBlur"
                    :class="[
                      'w-full h-[64px] pl-[88px] border rounded-md focus:outline-none text-base transition-colors',
                      mobileValidation.isValid ? 'border-gray-200 focus:border-blue-500' : 'border-red-400 focus:border-red-500'
                    ]"
                    placeholder="请输入手机号"
                    maxlength="11" />
                  <!-- 验证错误提示 -->
                  <div v-if="!mobileValidation.isValid && mobileValidation.message" class="absolute top-full left-0 mt-1 text-red-500 text-xs">
                    {{ mobileValidation.message }}
                  </div>
                </div>

                <div class="relative">
                  <div class="absolute left-4 top-0 bottom-0 flex items-center text-gray-500 text-sm z-10">会员卡</div>
                  <input
                    type="text"
                    v-model="vm.state.customerInfo.cardNo"
                    class="w-full h-[64px] pl-[78px] border border-gray-200 rounded-md focus:border-blue-500 focus:outline-none text-base"
                    placeholder="请输入会员卡" />
                </div>

                <div class="selector-wrapper h-[64px] border border-gray-200 rounded-md">
                  <RoomSelector
                    v-model="vm.state.storeRoomId"
                    placeholder="请选择"
                    @change="handleRoomChange"
                    @options-loaded="handleRoomsLoaded"
                    label="寄存包厢" />
                </div>

                <div class="selector-wrapper h-[64px] border border-gray-200 rounded-md">
                  <EmployeeSelector
                    v-model="vm.state.selectedEmployee"
                    placeholder="请选择"
                    disabled
                    @change="handleEmployeeChange"
                    @options-loaded="handleEmployeesLoaded"
                    label="代订人" />
                </div>

                <!-- <div class="flex items-center h-[64px] pl-3 border border-gray-200 rounded-md">
                  <el-checkbox v-model="vm.state.onlyStoreWine" class="text-sm">仅线下存酒</el-checkbox>
                </div> -->
              </div>
            </div>
          </div>

          <!-- 中间存酒列表 -->
          <div class="flex-1 overflow-auto">
            <div class="bg-white rounded-md shadow border mb-6">
              <div class="p-5 border-b">
                <div class="text-lg font-medium">寄存列表</div>
              </div>
              <div class="px-5 py-3">
                <el-table :data="vm.state.wineItems" class="wine-items-table" size="small" :border="false" :row-style="{ height: '64px' }" stripe>
                  <el-table-column label="商品名称" min-width="90">
                    <template #default="{ row }">
                      <span>{{ row.name }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="寄存仓库" min-width="80" align="center">
                    <template #default="{ row }">
                      <el-select v-model="row.warehouse" placeholder="请选择" size="small" class="w-full">
                        <el-option label="存酒仓库" value="wine" />
                        <el-option label="总仓库" value="main" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="数量" align="center">
                    <template #default="{ row, $index }">
                      <erp-input-number v-model="row.quantity" type="default" size="small" mode="cart" @delete-item="handleDeleteWineItem($index)" />
                    </template>
                  </el-table-column>

                  <el-table-column label="单位" align="center">
                    <template #default="{ row }">
                      <el-select v-model="row.unit" placeholder="单位" size="small" class="w-full">
                        <el-option label="瓶" value="瓶" />
                        <el-option label="箱" value="箱" />
                        <el-option label="杯" value="杯" />
                        <el-option label="份" value="份" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="备注" width="100" align="center">
                    <template #default="{ row }">
                      <div class="cursor-pointer text-blue-500 hover:underline" @click="openRemarkDialog(row)">编辑</div>
                    </template>
                  </el-table-column>

                  <el-table-column label="过期日期" min-width="100" align="center">
                    <template #default="{ row }">
                      <div class="text-center">{{ getExpireDate() }}</div>
                    </template>
                  </el-table-column>

                  <!-- 添加空数据提示 -->
                  <template #empty>
                    <div class="py-10 text-center">
                      <div class="text-gray-400 mb-2 text-lg">寄存列表为空</div>
                      <div class="text-gray-400 text-sm">请从左侧商品列表选择商品添加到寄存列表</div>
                    </div>
                  </template>
                </el-table>

                <!-- 备注编辑对话框 -->
                <el-dialog v-model="remarkDialogVisible" title="编辑备注" width="400px" :close-on-click-modal="false" align-center append-to-body>
                  <el-input v-model="currentRemark" type="textarea" placeholder="请输入备注内容" :rows="4" :maxlength="30" />
                  <template #footer>
                    <div class="flex justify-end gap-2">
                      <el-button @click="remarkDialogVisible = false">取消</el-button>
                      <el-button type="primary" @click="saveRemark">确认</el-button>
                    </div>
                  </template>
                </el-dialog>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 右侧底部按钮区域 -->
      <template #right-footer>
        <div class="border-t flex items-center justify-end !h-[120px] px-5 relative z-[9999] bg-white">
          <button
            @click="vm.actions.clearWineItems"
            class="w-[240px] h-[80px] bg-white text-gray-800 border border-gray-300 rounded-md text-lg mr-4 hover:bg-gray-50 transition-colors"
            :disabled="vm.state.isSubmitting">
            清空
          </button>
          <button
            @click="handleSubmit"
            class="w-[240px] h-[80px] bg-red-500 text-white rounded-md text-lg hover:bg-red-600 transition-colors disabled:opacity-60 disabled:cursor-not-allowed disabled:bg-red-300"
            :disabled="!vm.computed.isFormValid.value || vm.state.isSubmitting">
            <div v-if="vm.state.isSubmitting" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              提交中...
            </div>
            <span v-else>确认</span>
          </button>
        </div>
      </template>
    </LeftRightLayout>
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { Close, Plus, Search, Minus } from '@element-plus/icons-vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import LeftRightLayout from '@/components/Dialog/layout';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import { DialogUIType } from '@/types/dialog';
import { useStoreWinePresenter } from './presenter';
import type { IStoreWineViewModel } from './viewmodel';
import { useUserStore } from '@/stores/userStore';
import { useProductStorageStore } from '@/stores/productStorageStore';
import RoomSelector from '../RoomSelector.vue';
import EmployeeSelector from '../EmployeeSelector.vue';
import { calculateExpireDate } from '@/utils/dateUtils';
import eventBus from '@/utils/eventBus';
import { validateMobile } from '@/utils/validator';

// Props定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roomId: {
    type: String,
    default: ''
  },
  searchKeyword: {
    type: String,
    default: ''
  }
});

// Emits定义
const emit = defineEmits(['update:modelValue', 'confirm', 'refresh-list']);

// 使用Presenter和存酒配置Store
const vm: IStoreWineViewModel = useStoreWinePresenter();
const storageStore = useProductStorageStore();

// 双向绑定modelValue
const dialogVisible = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value);
    // 如果对话框关闭，触发closed事件
    if (!value) {
      console.log('存酒对话框关闭');
    }
  }
});

// 备注编辑对话框状态
const remarkDialogVisible = ref(false);
const currentRemark = ref('');
const currentEditItem = ref<any>(null);

// 手机号验证状态
const mobileValidation = ref({ isValid: true, message: '' });

// 打开备注编辑对话框
const openRemarkDialog = (row: any) => {
  currentEditItem.value = row;
  currentRemark.value = row.remark || '';
  remarkDialogVisible.value = true;
};

// 保存备注
const saveRemark = () => {
  if (currentEditItem.value) {
    currentEditItem.value.remark = currentRemark.value;
  }
  remarkDialogVisible.value = false;
};

// 处理删除存酒项（当数量变为0时）
const handleDeleteWineItem = (index: number) => {
  console.log('🗑️ 删除存酒项，索引:', index);
  vm.actions.removeWineItem(index);
};

// 处理手机号输入
const handleMobileInput = (value: string) => {
  // 只允许输入数字
  const numericValue = value.replace(/\D/g, '');
  vm.state.customerInfo.mobile = numericValue;
  mobileValidation.value = validateMobile(numericValue);
};

// 处理手机号失焦验证
const handleMobileBlur = () => {
  mobileValidation.value = validateMobile(vm.state.customerInfo.mobile);
};

// 处理提交
const handleSubmit = () => {
  vm.actions.handleConfirm();
};

// 监听提交状态
watch(
  () => vm.state.isSubmitting,
  (newValue, oldValue) => {
    // 如果从提交状态(true)变为非提交状态(false)，说明提交已完成
    if (oldValue === true && newValue === false) {
      if (!vm.state.wineItems.length) {
        // 当提交完成且列表为空时（提交成功），触发刷新事件
        console.log('存酒成功，触发wine-list-refresh事件');
        // 使用事件总线发送刷新事件
        eventBus.emit('wine-list-refresh');
        dialogVisible.value = false;
      }
    }
  }
);

// 获取当前用户名
const userStore = useUserStore();
const currentUserName = computed(() => userStore.userInfo?.employee?.name || '');

// 监听currentUserName变化并更新employee字段
watch(
  currentUserName,
  newName => {
    if (newName) {
      vm.state.employee = newName;
    }
  },
  { immediate: true }
);

// 组件挂载后初始化数据
onMounted(async () => {
  // 并行加载存酒配置和分类商品数据
  await Promise.all([
    storageStore.ensureSettings(), // 确保存酒配置已加载
    vm.actions.loadInitialData() // 初始化加载分类和商品数据
  ]);

  // 如果传入了roomId，设置寄存包厢
  if (props.roomId) {
    vm.state.storeRoomId = props.roomId;
    console.log('从外部传入寄存包厢ID:', props.roomId);
  }

  // 如果传入了searchKeyword，设置搜索关键词
  if (props.searchKeyword) {
    vm.state.searchKeyword = props.searchKeyword;
    // 自动触发搜索
    nextTick(() => {
      vm.actions.handleSearch();
    });
    console.log('从外部传入搜索关键词:', props.searchKeyword);
  }
});

// 处理房间选择变化
const handleRoomChange = (roomId: string, roomData?: any) => {
  if (roomData) {
    console.log('选择包厢:', { id: roomId, name: roomData.name });
    vm.actions.handleRoomSelect(roomData);
  } else if (!roomId) {
    console.log('清除包厢选择');
    vm.actions.clearRoomSelection();
  }
};

// 处理员工选择变化
const handleEmployeeChange = (employeeId: string, employee: any) => {
  // 确保获取正确的员工ID和名称
  if (employee && employee.name) {
    // 设置员工ID
    vm.state.selectedEmployee = employeeId;
    // 设置员工名称
    vm.state.employee = employee.name;
    console.log('选择员工:', employeeId, employee.name);
  } else {
    vm.state.selectedEmployee = employeeId;
    vm.state.employee = '';
    console.log('选择员工:', employeeId);
  }
};

// 处理房间选项加载完成
const handleRoomsLoaded = (options: any[]) => {
  // 可以将选项同步到Presenter中，或进行其他处理
  vm.state.roomOptions = options;
};

// 处理员工选项加载完成
const handleEmployeesLoaded = (options: any[]) => {
  // 可以将选项同步到Presenter中，或进行其他处理
  vm.state.employeeOptions = options;
};

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  visible => {
    if (visible) {
      console.log('存酒对话框已显示');
      // 重置验证状态
      mobileValidation.value = { isValid: true, message: '' };
    } else {
      // 对话框关闭时的清理工作
      console.log('存酒对话框关闭，执行清理');
      // 重置验证状态
      mobileValidation.value = { isValid: true, message: '' };
    }
  }
);

// 计算过期日期
const getExpireDate = () => {
  // 使用存酒配置中的存储天数
  const storageDays = storageStore.storageDays || 30;
  const { formatted } = calculateExpireDate(storageDays);
  return formatted;
};
</script>
