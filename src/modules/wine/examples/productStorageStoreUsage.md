# 存酒配置Store使用指南

## 简介

`useProductStorageStore` 是一个 Pinia store，用于管理门店存酒相关的配置信息。它通过调用 `/api/wineStorageSetting/query` 接口获取配置，取代了之前写死的配置值。

## 主要功能

### 1. 配置管理

- 自动获取和缓存门店存酒配置
- 提供默认配置fallback
- 支持强制刷新配置

### 2. 提供的配置项

- `storageDays`: 默认存储天数
- `renewalTimes`: 可续存次数
- `renewalDays`: 每次续存天数
- `customerNotificationDays`: 客户通知天数
- `merchantNotificationDays`: 商户通知天数
- `autoConfiscate`: 是否自动报废
- `overdueWithdrawalLimit`: 过期取酒限制天数
- `autoConfiscateDays`: 自动报废天数

### 3. 实用方法

- `calculateExpireTime()`: 根据配置计算过期时间
- `canRenew()`: 检查是否可以续存
- `isOverWithdrawalLimit()`: 检查是否超过取酒限制

## 使用示例

### 基本使用

```typescript
import { useProductStorageStore } from '@/stores/productStorageStore';

// 在组件中使用
const storageStore = useProductStorageStore();

// 确保配置已加载
await storageStore.ensureSettings();

// 获取存储天数
const days = storageStore.storageDays; // 默认30天

// 计算过期时间
const storageTime = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
const expireTime = storageStore.calculateExpireTime(storageTime);
```

### 在存酒对话框中使用

```typescript
// StoreWineDialog/presenter.ts
export class StoreWinePresenter {
  private storageStore = useProductStorageStore();

  async submit() {
    // 确保配置已加载
    await this.storageStore.ensureSettings();

    // 使用配置中的存储天数
    const apiPayload = convertToApiPayload({
      // ... 其他参数
      storageDays: this.storageStore.storageDays
    });
  }
}
```

### 在续存功能中使用

```typescript
// 检查是否可以续存
const canRenew = storageStore.canRenew(currentRenewalCount);

if (canRenew) {
  // 计算续存后的过期时间
  const newExpireTime = storageStore.calculateRenewExpireTime(currentExpireTime);
}
```

### 在过期检查中使用

```typescript
// 检查是否超过取酒限制
const isOverLimit = storageStore.isOverWithdrawalLimit(expireTime);

if (isOverLimit) {
  console.log('已超过取酒限制时间');
}
```

## 配置缓存策略

- 缓存有效期：5分钟
- 自动失效：超过有效期后下次访问会重新获取
- 手动刷新：调用 `refreshSettings()` 强制刷新

## 错误处理

- 网络请求失败时自动使用默认配置
- 提供友好的错误提示
- 配置加载失败不影响业务流程

## 在哪些地方已经集成

### 1. 存酒对话框 (`StoreWineDialog`)

- 自动使用配置中的存储天数计算过期时间
- 显示正确的过期日期

### 2. 存酒列表 (`StoreWine`)

- 使用配置进行续存操作
- 根据配置检查过期状态

### 3. 转换器 (`converter.ts`)

- 使用配置计算API请求中的过期时间参数

## 注意事项

1. 首次使用时会自动获取配置，建议在应用启动时预加载
2. 配置获取失败时会使用默认值，不会影响正常业务
3. 所有时间计算都基于Unix时间戳（秒）
4. 续存次数检查需要业务层传入当前已续存次数

## 未来扩展

- 支持按不同商品类型设置不同的存储配置
- 增加配置变更通知功能
- 支持批量配置管理
