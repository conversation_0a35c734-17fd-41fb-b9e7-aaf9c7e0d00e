import { postApiProductStorageOperate } from '@/api/autoGenerated/defaultVersion/product';

/**
 * 存酒操作类型枚举
 */
export enum StorageOperationType {
  EXTEND = 'extend', // 续存
  DISCARD = 'discard', // 报废
  UPDATE = 'update', // 更新(可用于部分撤销)
  ADD_ITEMS = 'addItems', // 添加商品
  CANCEL = 'cancel' // 撤销
}

/**
 * 存酒操作服务
 */
export const wineOperationService = {
  /**
   * 续存操作 - 延长到期时间
   * @param id 存酒记录ID
   * @param expireTime 新的到期时间戳(秒)
   * @param operatorId 操作人ID
   * @param operatorName 操作人姓名
   * @param remark 备注信息
   * @returns Promise
   */
  extendWineStorage: async (id: string, expireTime: number, operatorId: string, operatorName: string, remark?: string) => {
    return postApiProductStorageOperate({
      id,
      operationType: StorageOperationType.EXTEND,
      expireTime,
      operatorId,
      operatorName,
      remark
    });
  },

  /**
   * 报废操作
   * @param id 存酒记录ID
   * @param operatorId 操作人ID
   * @param operatorName 操作人姓名
   * @param remark 报废原因
   * @returns Promise
   */
  discardWineStorage: async (id: string, operatorId: string, operatorName: string, remark?: string) => {
    return postApiProductStorageOperate({
      id,
      operationType: StorageOperationType.DISCARD,
      operatorId,
      operatorName,
      remark
    });
  },

  /**
   * 添加商品到已有存酒单
   * @param id 存酒记录ID
   * @param orderNo 存酒单号
   * @param operatorId 操作人ID
   * @param operatorName 操作人姓名
   * @param items 添加的商品项
   * @returns Promise
   */
  addItemsToWineStorage: async (
    id: string,
    orderNo: string,
    operatorId: string,
    operatorName: string,
    items: Array<{
      productId: string;
      productName: string;
      productType?: string;
      productUnit: string;
      productSpec?: string;
      quantity: number;
      storageLocation?: string;
      expireTime: number;
      remark?: string;
    }>
  ) => {
    return postApiProductStorageOperate({
      id,
      operationType: StorageOperationType.ADD_ITEMS,
      orderNo,
      operatorId,
      operatorName,
      items
    });
  },

  /**
   * 撤销存酒(完全撤销整单)
   * @param id 存酒记录ID
   * @param operatorId 操作人ID
   * @param operatorName 操作人姓名
   * @param remark 操作备注
   * @returns Promise
   */
  cancelWineStorage: async (id: string, operatorId: string, operatorName: string, remark?: string) => {
    return postApiProductStorageOperate({
      id,
      operationType: StorageOperationType.CANCEL,
      operatorId,
      operatorName,
      remark
    });
  }
};
