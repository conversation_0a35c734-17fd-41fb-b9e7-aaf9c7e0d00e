<template>
  <div class="h-full flex flex-col relative">
    <!-- 表格内容区域 (可滚动) -->
    <div class="flex-1 overflow-auto px-[20px] pt-[20px] pb-[8px]">
      <el-table
        v-loading="vm.state.loading"
        :data="vm.computed.filteredTableData.value"
        size="large"
        height="calc(100vh - 120px)"
        stripe
        :row-class-name="tableRowClassName">
        <el-table-column prop="productName" label="商品名称" min-width="200"> </el-table-column>

        <el-table-column prop="storageLocation" label="存酒仓库" width="160" align="center">
          <template #header>
            <!-- 仓库过滤器 -->
            <el-popover v-model:visible="showWarehouseFilter" placement="bottom" :width="120" :offset="10" trigger="manual" popper-class="filter-popover">
              <template #reference>
                <div class="flex items-center justify-center gap-1 cursor-pointer" @click="showWarehouseFilter = !showWarehouseFilter">
                  <span class="">存酒仓库</span>
                  <el-icon class="text-gray-600 text-xs"><ArrowDown /></el-icon>
                </div>
              </template>

              <div class="p-2">
                <div
                  class="p-2 hover:bg-gray-100 cursor-pointer rounded"
                  :class="{ 'bg-gray-100': vm.state.selectedWarehouse === ModuleType.MAIN }"
                  @click="
                    vm.actions.setWarehouseFilter(vm.state.selectedWarehouse === ModuleType.MAIN ? null : ModuleType.MAIN);
                    showWarehouseFilter = false;
                  ">
                  仓库
                </div>
                <div
                  class="p-2 hover:bg-gray-100 cursor-pointer rounded"
                  :class="{ 'bg-gray-100': vm.state.selectedWarehouse === ModuleType.WINE }"
                  @click="
                    vm.actions.setWarehouseFilter(vm.state.selectedWarehouse === ModuleType.WINE ? null : ModuleType.WINE);
                    showWarehouseFilter = false;
                  ">
                  存酒仓库
                </div>
                <div
                  class="p-2 hover:bg-gray-100 cursor-pointer rounded"
                  @click="
                    vm.actions.setWarehouseFilter(null);
                    showWarehouseFilter = false;
                  ">
                  全部
                </div>
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <span class="">
              {{ getWarehouseLabel(scope.row.storageLocation) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="unit" label="单位" min-width="160" align="center">
          <template #header>
            <!-- 单位过滤器 -->
            <el-popover v-model:visible="showUnitFilter" placement="bottom" :width="120" :offset="10" trigger="manual" popper-class="filter-popover">
              <template #reference>
                <div class="flex items-center justify-center gap-1 cursor-pointer" @click="showUnitFilter = !showUnitFilter">
                  <span class="">单位</span>
                  <el-icon class="text-gray-600 text-xs"><ArrowDown /></el-icon>
                </div>
              </template>

              <div class="p-2">
                <div
                  v-for="option in vm.computed.filterOptions.value.unit"
                  :key="option"
                  class="p-2 hover:bg-gray-100 cursor-pointer rounded"
                  :class="{ 'bg-gray-100': vm.state.selectedUnit === option }"
                  @click="
                    vm.actions.setUnitFilter(option === vm.state.selectedUnit ? null : option);
                    showUnitFilter = false;
                  ">
                  {{ option }}
                </div>
                <div
                  class="p-2 hover:bg-gray-100 cursor-pointer rounded"
                  @click="
                    vm.actions.setUnitFilter(null);
                    showUnitFilter = false;
                  ">
                  全部
                </div>
              </div>
            </el-popover>
          </template>
          <template #default="scope">
            <span class="">{{ scope.row.unit }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="totalQuantity" label="存酒数量" min-width="160" align="center">
          <template #header>
            <div class="">存酒数量</div>
          </template>
          <template #default="scope">
            <span class="">{{ scope.row.totalQuantity }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="discardedQuantity" label="报废数量" min-width="160" align="center"> </el-table-column>

        <el-table-column prop="currentQuantity" label="取酒数量" min-width="160" align="center">
          <template #header>
            <div class="">取酒数量</div>
          </template>
          <template #default="scope">
            <span class="">{{ scope.row.takenQuantity }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="remainingQuantity" label="当前剩余数量" min-width="160" align="center">
          <template #header>
            <div class="">当前剩余数量</div>
          </template>
          <template #default="scope">
            <span class="">{{ scope.row.remainingQuantity }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 使用通用底部操作栏组件 -->
    <!-- TODO: 打印功能暂未实现，后期需要实现 -->
    <!-- <PageFooterActions>
      <el-button 
        type="default" 
        size="large"
        @click="vm.actions.printData"
        class="bg-[#F3F3F3] border border-[rgba(0,0,0,0.1)] text-[18px]"
      >
        打印
      </el-button>
    </PageFooterActions> -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, onActivated } from 'vue';
import { ArrowDown, Warning } from '@element-plus/icons-vue';
import { useWineStatisticsPresenter } from './presenter';
import PageFooterActions from '@/components/PageFooterActions.vue';
import { useRoute } from 'vue-router';
import { ModuleType, getWarehouseLabel } from '@/modules/wine/constants';
import dayjs from 'dayjs';

// 定义props接收父组件传递的搜索参数和模块类型
const props = defineProps<{
  searchParams?: {
    keyword: string;
    dateRange: [Date | null, Date | null];
    dateRangeText: string;
    staffId: string | null;
    staffName: string;
  };
  moduleType?: string; // 模块类型：main或wine
}>();

// 根据模块确定仓库标题
const route = useRoute();
// 确保currentModule始终返回字符串值
const currentModule = computed<string>(() => props.moduleType || (route.meta.module as string) || ModuleType.WINE);

// 定义事件，向父组件传递搜索参数
const emit = defineEmits<{
  (e: 'search', params: any): void;
}>();

// 使用Presenter组件，传入模块类型，但不在此处初始化(避免在useWineStatisticsPresenter自动调用refreshData)
const vm = useWineStatisticsPresenter(currentModule.value, false);

// 过滤器弹窗控制
const showTypeFilter = ref(false);
const showWarehouseFilter = ref(false);
const showUnitFilter = ref(false);

// 表格行样式函数
const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
  // 如果是总计行，添加固定样式
  if (row.productId === 'summary-row') {
    return 'summary-row-fixed';
  }
  return '';
};

// 初始化标记，防止重复加载
const isInitialized = ref(false);

// 初始化数据加载函数
const initializeData = () => {
  if (isInitialized.value) return;

  // 确保设置了正确的模块类型
  vm.actions.setModuleType(currentModule.value, false); // 不自动刷新

  // 如果有搜索参数，应用搜索参数
  if (props.searchParams) {
    // 更新本地状态
    vm.state.searchKeyword = props.searchParams.keyword;
    vm.state.dateRange = props.searchParams.dateRange;
    vm.state.dateRangeText = props.searchParams.dateRangeText;
    vm.state.selectedStaff = props.searchParams.staffId;
    vm.state.selectedStaffName = props.searchParams.staffName;
  }

  // 只调用一次刷新数据
  vm.actions.refreshData();
  isInitialized.value = true;
};

// 初始化加载数据
onMounted(() => {
  // 不在这里初始化数据，统一在 onActivated 中处理
});

// 组件激活时自动刷新统计数据（包括首次进入）
onActivated(() => {
  console.log('[WineStatistics] 页面激活，刷新统计数据');
  // 如果是首次进入，先初始化
  if (!isInitialized.value) {
    initializeData();
  } else {
    // 非首次进入，直接刷新数据
    vm.actions.refreshData();
  }
});

// 监听模块类型变化 - 模块变化时需要重新获取数据
watch(
  () => currentModule.value,
  (newModule, oldModule) => {
    if (newModule !== oldModule) {
      vm.actions.setModuleType(newModule);
    }
  }
);

// 监听父组件传递的搜索参数 - 仅当已初始化后且参数有变化时才触发刷新
watch(
  () => props.searchParams,
  (newParams, oldParams) => {
    if (!newParams || !isInitialized.value) return;

    // 检查参数是否有实质性变化
    const hasChanged =
      !oldParams || newParams.keyword !== oldParams.keyword || newParams.dateRangeText !== oldParams.dateRangeText || newParams.staffId !== oldParams.staffId;

    if (hasChanged) {
      // 更新本地状态
      vm.state.searchKeyword = newParams.keyword;
      vm.state.dateRange = newParams.dateRange;
      vm.state.dateRangeText = newParams.dateRangeText;
      vm.state.selectedStaff = newParams.staffId;
      vm.state.selectedStaffName = newParams.staffName;

      // 触发搜索
      vm.actions.refreshData();
    }
  },
  { deep: true }
);
</script>

<style scoped>
/* 固定合计行样式 */
:deep(.el-table .summary-row-fixed) {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f5f7fa;
  font-weight: 600;
}

:deep(.el-table .summary-row-fixed td) {
  position: sticky;
  top: 0;
  background-color: #f5f7fa !important;
  font-weight: 600;
  border-bottom: 1px solid #dcdfe6;
}

:deep(.el-table .summary-row-fixed:hover td) {
  background-color: #f5f7fa !important;
}

/* 确保表格容器支持sticky定位 */
:deep(.el-table__body-wrapper) {
  position: relative;
}
</style>
