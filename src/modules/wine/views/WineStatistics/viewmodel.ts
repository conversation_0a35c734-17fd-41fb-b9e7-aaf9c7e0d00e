import { ComputedRef } from 'vue';
import { ProductStorageItemStatVO } from '@/api/autoGenerated/shared/types/product';

// 存取酒统计数据记录
export interface WineStatisticsRecord extends ProductStorageItemStatVO {
  // 添加取酒数量字段，这是通过计算得到的，不在原始API数据中
  takenQuantity?: number;
}

// 统计数据项
export interface StatisticsData {
  type: string;
  count: number;
  percentage: number;
}

// 统计模式
export type StatisticsMode = 'daily' | 'monthly' | 'yearly';

// 过滤选项
export interface FilterOptions {
  type: string[];
  warehouse: string[];
  unit: string[];
}

// UI状态
export interface IWineStatisticsState {
  // 搜索参数
  searchKeyword: string;
  dateRange: [Date | null, Date | null];
  dateRangeText: string;
  selectedStaff: string | null;
  selectedStaffName: string;

  // 过滤条件
  selectedType: string | null;
  selectedWarehouse: string | null;
  selectedUnit: string | null;

  // 统计数据
  statisticsData: StatisticsData[];
  tableData: WineStatisticsRecord[];
  loading: boolean;

  // 统计模式
  statisticsMode: StatisticsMode;

  // 分页
  currentPage: number;
  pageSize: number;
  total: number;
}

// UI计算属性
export interface IWineStatisticsComputed {
  // 总数
  totalCount: ComputedRef<number>;

  // 过滤选项
  filterOptions: ComputedRef<FilterOptions>;

  // 过滤后的表格数据
  filteredTableData: ComputedRef<WineStatisticsRecord[]>;

  // 过滤后的明细数据（不包含总计行）
  filteredDetailData: ComputedRef<WineStatisticsRecord[]>;
}

// UI动作
export interface IWineStatisticsActions {
  // 设置模块类型
  setModuleType(moduleType: string, autoRefresh?: boolean): void;

  // 搜索
  search(params: any): void;

  // 搜索框输入
  setSearchKeyword(keyword: string): void;

  // 选择日期范围
  setDateRange(dateRange: [Date | null, Date | null], dateRangeText: string): void;

  // 选择员工
  setStaff(staffId: string | null, staffName: string): void;

  // 刷新数据
  refreshData(): void;

  // 切换统计模式
  setStatisticsMode(mode: StatisticsMode): void;

  // 页面变化
  handlePageChange(page: number): void;

  // 每页数量变化
  handleSizeChange(size: number): void;

  // 设置过滤条件
  setTypeFilter(type: string | null): void;
  setWarehouseFilter(warehouse: string | null): void;
  setUnitFilter(unit: string | null): void;

  // 打印数据
  printData(): void;
}

// 总的ViewModel接口
export interface IWineStatisticsViewModel {
  state: IWineStatisticsState;
  computed: IWineStatisticsComputed;
  actions: IWineStatisticsActions;
}
