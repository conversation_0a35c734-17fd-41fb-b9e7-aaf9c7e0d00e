import { ref, reactive, computed } from 'vue';
import type {
  IWineStatisticsViewModel,
  IWineStatisticsState,
  IWineStatisticsComputed,
  IWineStatisticsActions,
  WineStatisticsRecord,
  StatisticsMode,
  FilterOptions
} from './viewmodel';
import { useRequest } from 'vue-hooks-plus';
import { postApiProductStorageStatistics } from '@/api/autoGenerated/defaultVersion/product';
import { ModuleType } from '@/modules/wine/constants';

// 从API获取统计数据
const fetchStatisticsData = async (
  queryParams: any
): Promise<{
  list: WineStatisticsRecord[];
  total: number;
  totalStoreCount: number;
  totalTakeCount: number;
  totalRemainCount: number;
}> => {
  try {
    // 使用统计API
    const response = await postApiProductStorageStatistics({
      // 使用正确的API参数名称
      startTime: queryParams.dateRange && queryParams.dateRange[0] ? Math.floor(queryParams.dateRange[0].getTime() / 1000) : undefined,
      endTime: queryParams.dateRange && queryParams.dateRange[1] ? Math.floor(queryParams.dateRange[1].getTime() / 1000) : undefined
    });

    // 直接使用API返回的数据
    const responseData = response.data;

    // 从data属性中获取列表数据
    const list = Array.isArray(responseData.data) ? responseData.data : [];

    // 为每个列表项添加取酒数量字段
    const processedList = list.map(item => ({
      ...item,
      // 计算取酒数量：总存酒数量 - 剩余数量
      takenQuantity: (item.totalQuantity || 0) - (item.remainingQuantity || 0)
    }));

    // 计算统计数据总和 - 使用API字段对应含义：totalQuantity(存酒总数量)、remainingQuantity(剩余数量)
    const totalStorageQuantity = processedList.reduce((sum, item) => sum + (item.totalQuantity || 0), 0);
    const totalRemainingQuantity = processedList.reduce((sum, item) => sum + (item.remainingQuantity || 0), 0);
    const totalDiscardedQuantity = processedList.reduce((sum, item) => sum + (item.discardedQuantity || 0), 0);
    const totalTakenQuantity = processedList.reduce((sum, item) => sum + (item.takenQuantity || 0), 0);

    // 创建合计行数据 - 使用正确的字段
    const summaryRow = {
      productId: 'summary-row',
      productName: '合计',
      productType: '',
      storageLocation: '',
      unit: '',
      totalQuantity: totalStorageQuantity, // 存酒总数量
      discardedQuantity: totalDiscardedQuantity, // 报废数量
      remainingQuantity: totalRemainingQuantity, // 剩余数量
      takenQuantity: totalTakenQuantity // 取酒数量
    };

    // 将合计行作为第一行数据
    const finalData = [summaryRow, ...processedList];

    return {
      list: finalData,
      total: responseData.total || 0,
      totalStoreCount: totalStorageQuantity,
      totalTakeCount: totalTakenQuantity, // 修改为计算后的取酒数量
      totalRemainCount: totalRemainingQuantity
    };
  } catch (error) {
    // 即使失败也创建一个合计行 - 使用API数据结构
    const summaryRow: WineStatisticsRecord = {
      productId: 'summary-row',
      productName: '合计',
      productType: '',
      storageLocation: '',
      unit: '',
      totalQuantity: 0,
      remainingQuantity: 0,
      discardedQuantity: 0,
      takenQuantity: 0
    };

    // 失败时返回空数据，但包含合计行
    return {
      list: [summaryRow],
      total: 0,
      totalStoreCount: 0,
      totalTakeCount: 0,
      totalRemainCount: 0
    };
  }
};

// 导出独立的获取统计数据函数供外部使用
export const fetchWineStatistics = async (params: any): Promise<WineStatisticsRecord[]> => {
  try {
    const result = await fetchStatisticsData({
      ...params,
      module: params.module || ModuleType.WINE
    });
    // 按产品名称排序 - 使用原始API字段
    result.list.sort((a: WineStatisticsRecord, b: WineStatisticsRecord) => (a.productName || '').localeCompare(b.productName || ''));
    return result.list;
  } catch (error) {
    return [];
  }
};

export class WineStatisticsPresenter implements IWineStatisticsViewModel {
  // 模块类型
  private moduleType: string = ModuleType.WINE;

  // 状态
  public state: IWineStatisticsState = reactive({
    // 搜索参数
    searchKeyword: '',
    dateRange: [null, null],
    dateRangeText: '',
    selectedStaff: null,
    selectedStaffName: '',

    // 过滤条件
    selectedType: null,
    selectedWarehouse: null,
    selectedUnit: null,

    // 表格数据
    statisticsData: [],
    tableData: [],
    loading: false,

    // 统计模式
    statisticsMode: 'monthly' as StatisticsMode,

    // 分页
    currentPage: 1,
    pageSize: 10000, // 设置为很大的值，实现不分页效果
    total: 0
  });

  // 使用useRequest钩子加载数据
  private requestService = useRequest(fetchStatisticsData, {
    manual: true, // 改为手动触发
    onSuccess: result => {
      this.state.tableData = result.list || [];
      console.log('tableData', result.list);
      this.state.total = result.total || 0;
      this.state.loading = false;
    },
    onError: () => {
      this.state.loading = false;
      this.state.tableData = [];
      this.state.total = 0;
    }
  });

  // 计算属性
  public computed: IWineStatisticsComputed = {
    // 总数
    totalCount: computed(() => {
      return this.state.tableData.length;
    }),

    // 过滤选项（根据表格数据动态生成）
    filterOptions: computed(() => {
      const options: FilterOptions = {
        type: [],
        warehouse: [],
        unit: []
      };

      // 从表格数据中提取唯一的类型、仓库和单位 - 使用原始API字段
      this.state.tableData.forEach(item => {
        if (item.productType && !options.type.includes(item.productType)) {
          options.type.push(item.productType);
        }
        if (item.storageLocation && !options.warehouse.includes(item.storageLocation)) {
          options.warehouse.push(item.storageLocation);
        }
        if (item.unit && !options.unit.includes(item.unit)) {
          options.unit.push(item.unit);
        }
      });

      return options;
    }),

    // 过滤后的表格数据
    filteredTableData: computed(() => {
      // 保存为局部变量以便于引用
      const searchKeyword = this.state.searchKeyword.toLowerCase();
      const selectedType = this.state.selectedType;
      const selectedWarehouse = this.state.selectedWarehouse;
      const selectedUnit = this.state.selectedUnit;

      // 分离总计行和明细数据
      let summaryRow: WineStatisticsRecord | null = null;
      let detailData: WineStatisticsRecord[] = [];

      this.state.tableData.forEach(item => {
        if (item.productId === 'summary-row') {
          summaryRow = item;
        } else {
          detailData.push(item);
        }
      });

      // 对明细数据进行过滤
      let filteredDetails = detailData
        // 搜索关键字过滤 - 使用原始API字段
        .filter(item => {
          if (!searchKeyword) return true;
          return (
            (item.productName || '').toLowerCase().includes(searchKeyword) ||
            (item.productType || '').toLowerCase().includes(searchKeyword) ||
            (item.storageLocation || '').toLowerCase().includes(searchKeyword) ||
            (item.unit || '').toLowerCase().includes(searchKeyword)
          );
        })
        // 类型过滤 - 使用原始API字段
        .filter(item => {
          if (!selectedType) return true;
          return item.productType === selectedType;
        })
        // 仓库过滤 - 使用原始API字段
        .filter(item => {
          if (!selectedWarehouse) return true;
          return item.storageLocation === selectedWarehouse;
        })
        // 单位过滤
        .filter(item => {
          if (!selectedUnit) return true;
          return item.unit === selectedUnit;
        });

      // 根据过滤后的明细数据重新计算总计行
      if (summaryRow && filteredDetails.length > 0) {
        const summary = summaryRow as WineStatisticsRecord;
        summary.totalQuantity = filteredDetails.reduce((sum, item) => sum + (item.totalQuantity || 0), 0);
        summary.discardedQuantity = filteredDetails.reduce((sum, item) => sum + (item.discardedQuantity || 0), 0);
        summary.remainingQuantity = filteredDetails.reduce((sum, item) => sum + (item.remainingQuantity || 0), 0);
        summary.takenQuantity = filteredDetails.reduce((sum, item) => sum + (item.takenQuantity || 0), 0);
      }

      // 合并总计行和明细数据
      const result = summaryRow ? [summaryRow, ...filteredDetails] : filteredDetails;

      // 计算分页相关（只计算明细数据的总数）
      this.state.total = filteredDetails.length;

      // 分页处理
      const start = (this.state.currentPage - 1) * this.state.pageSize;
      const end = start + this.state.pageSize;

      // 对于分页，总计行始终在第一页显示，其他页面只显示明细数据
      if (this.state.currentPage === 1) {
        const pagedDetails = filteredDetails.slice(start, end);
        return summaryRow ? [summaryRow, ...pagedDetails] : pagedDetails;
      } else {
        // 非第一页，调整start和end以正确分页明细数据
        const adjustedStart = start - 1; // 减去总计行的位置
        const adjustedEnd = end - 1;
        return filteredDetails.slice(adjustedStart, adjustedEnd);
      }
    }),

    // 过滤后的明细数据（不包含总计行）
    filteredDetailData: computed(() => {
      const filteredData = this.computed.filteredTableData.value;
      // 过滤掉总计行数据
      return filteredData.filter(item => item.productId !== 'summary-row');
    })
  };

  // 动作实现
  public actions: IWineStatisticsActions = {
    // 设置模块类型
    setModuleType: (moduleType, autoRefresh = true) => {
      this.moduleType = moduleType || ModuleType.WINE;
      if (autoRefresh) {
        this.actions.refreshData();
      }
    },

    // 搜索
    search: params => {
      Object.assign(this.state, params);
      this.actions.refreshData();
    },

    // 设置搜索关键字
    setSearchKeyword: keyword => {
      this.state.searchKeyword = keyword;
    },

    // 设置日期范围
    setDateRange: (dateRange, dateRangeText) => {
      this.state.dateRange = dateRange;
      this.state.dateRangeText = dateRangeText;
      this.actions.refreshData();
    },

    // 设置员工
    setStaff: (staffId, staffName) => {
      this.state.selectedStaff = staffId;
      this.state.selectedStaffName = staffName;
      this.actions.refreshData();
    },

    // 刷新数据
    refreshData: () => {
      this.state.loading = true;

      this.requestService.run({
        module: this.moduleType,
        dateRange: this.state.dateRange,
        searchKeyword: this.state.searchKeyword,
        pageNum: this.state.currentPage,
        pageSize: this.state.pageSize
      });
    },

    // 切换统计模式
    setStatisticsMode: (mode: StatisticsMode) => {
      this.state.statisticsMode = mode;
      this.actions.refreshData();
    },

    // 处理页面变化
    handlePageChange: (page: number) => {
      this.state.currentPage = page;
      this.actions.refreshData();
    },

    // 处理每页数量变化
    handleSizeChange: (size: number) => {
      this.state.pageSize = size;
      this.state.currentPage = 1;
      this.actions.refreshData();
    },

    // 设置类型过滤 - productType
    setTypeFilter: type => {
      this.state.selectedType = type;
    },

    // 设置仓库过滤 - storageLocation
    setWarehouseFilter: warehouse => {
      this.state.selectedWarehouse = warehouse;
    },

    // 设置单位过滤
    setUnitFilter: unit => {
      this.state.selectedUnit = unit;
    },

    // 打印数据
    printData: () => {
      console.log('打印数据', this.state.tableData);
      // TODO: 实现打印功能
    }
  };
}

// 导出组合式函数
export function useWineStatisticsPresenter(moduleType: string = ModuleType.WINE, autoInitialize: boolean = true): IWineStatisticsViewModel {
  const presenter = new WineStatisticsPresenter();
  if (autoInitialize) {
    presenter.actions.setModuleType(moduleType);
  }
  return presenter;
}
