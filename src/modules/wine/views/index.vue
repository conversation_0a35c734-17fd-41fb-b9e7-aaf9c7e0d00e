<template>
  <div class="flex flex-col h-full">
    <!-- 标签页切换区域 (左侧) 和 搜索筛选区域 (右侧) -->
    <nav class="p-4 border-b">
      <div class="flex items-center justify-between">
        <!-- 左侧标签页切换 -->
        <div class="flex items-center">
          <div class="flex bg-[#F3F3F3] rounded-[10px] h-[68px] items-center gap-[8px] px-[8px]">
            <div
              class="h-[52px] w-[120px] font-medium flex items-center justify-center cursor-pointer text-[16px] rounded-[8px]"
              :class="{ 'bg-[#E23939] text-white': activeTab === 'storeWine', 'text-gray-500': activeTab !== 'storeWine' }"
              @click="switchTab('storeWine')">
              存取酒
            </div>
            <div
              class="h-[52px] w-[120px] font-medium flex items-center justify-center cursor-pointer text-[16px] rounded-[8px]"
              :class="{ 'bg-[#E23939] text-white': activeTab === 'wineStatistics', 'text-gray-500': activeTab !== 'wineStatistics' }"
              @click="switchTab('wineStatistics')">
              存取统计
            </div>
          </div>
        </div>
        <!-- 中间搜索区域 -->
        <div class="flex-1 flex justify-center ml-4" v-if="activeTab === 'storeWine'">
          <div class="relative w-full">
            <el-input v-model="searchKeyword" placeholder="手机号 / 姓名" class="custom-search-input" @keyup.enter="handleSearch" clearable>
              <template #prefix>
                <el-icon class="ml-4">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>
        <!-- 筛选区域 -->
        <div class="flex items-center gap-4" v-if="activeTab === 'storeWine'">
          <!-- 筛选条件：起止时间 -->
          <div class="flex items-center ml-[24px]">
            <el-date-picker
              class="customer-date-picker"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY/MM/DD"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              @change="handleDateChange" />
          </div>

          <!-- 存酒按钮 -->
          <div>
            <el-button class="btn-default" @click="showStoreWineDialog"> 存酒 </el-button>
          </div>

          <!-- 取酒按钮 -->
          <div>
            <el-button class="btn-default" @click="showTakeWineDialog"> 取酒 </el-button>
          </div>
        </div>

        <!-- 统计页面筛选区域 -->
        <div class="flex items-center gap-4" v-if="activeTab === 'wineStatistics'">
          <!-- 筛选条件：起止时间 -->
          <div class="flex items-center ml-[24px]">
            <el-date-picker
              class="customer-date-picker"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY/MM/DD"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              @change="handleDateChange" />
          </div>
        </div>
      </div>
    </nav>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-hidden">
      <keep-alive>
        <component
          :is="currentComponent"
          :search-params="searchParams"
          @search="handleSearch"
          @open-store-dialog="showStoreWineDialog"
          @open-take-dialog="showTakeWineDialog" />
      </keep-alive>
    </div>

    <!-- 存酒弹窗组件 -->
    <StoreWineDialog v-model="storeWineDialogVisible" @confirm="handleStoreWine" />

    <!-- 取酒弹窗组件 -->
    <TakeWineDialog v-model="takeWineDialogVisible" @confirm="handleTakeWine" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, markRaw, watch, onMounted, nextTick } from 'vue';
import { Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import StoreWine from './StoreWine/index.vue';
import WineStatistics from './WineStatistics/index.vue';
import TakeWineDialog from '../components/TakeWineDialog/index.vue';
import StoreWineDialog from '../components/StoreWineDialog/index.vue';
import { useUserStore } from '@/stores/userStore';

// 使用markRaw以避免不必要的组件代理
const StoreWineComponent = markRaw(StoreWine);
const WineStatisticsComponent = markRaw(WineStatistics);

// 声明 emit
const emit = defineEmits(['search']);

// 当前激活的标签页
const activeTab = ref<'storeWine' | 'wineStatistics'>('storeWine');

// 切换标签页
const switchTab = (tab: 'storeWine' | 'wineStatistics') => {
  activeTab.value = tab;
};

// 当前组件
const currentComponent = computed(() => {
  return activeTab.value === 'storeWine' ? StoreWineComponent : WineStatisticsComponent;
});

// 计算默认日期范围：从今天往前倒推一周
const getDefaultDateRange = (): [Date, Date] => {
  const today = dayjs().endOf('day').toDate(); // 今天结束时间
  const weekAgo = dayjs().subtract(6, 'days').startOf('day').toDate(); // 7天前开始时间（包含今天共7天）

  return [weekAgo, today];
};

// 搜索和筛选状态
const searchKeyword = ref('');
const dateRange = ref<[Date | null, Date | null]>(getDefaultDateRange());
const dateRangeText = ref('');

// 获取用户信息
const userStore = useUserStore();

// 防抖计时器
let searchTimer: number | null = null;

// 搜索
const handleSearch = () => {
  console.log('执行搜索');
  emit('search', {
    keyword: searchKeyword.value,
    dateRange: dateRange.value,
    dateRangeText: dateRangeText.value,
    staffId: null,
    staffName: '全部'
  });
};

// 实时搜索（防抖）
const debouncedSearch = ref<number | null>(null);
watch(
  searchKeyword,
  newValue => {
    if (debouncedSearch.value) {
      clearTimeout(debouncedSearch.value);
    }
    debouncedSearch.value = setTimeout(() => {
      emit('search', {
        keyword: newValue,
        dateRange: dateRange.value,
        dateRangeText: dateRangeText.value,
        staffId: null,
        staffName: '全部'
      });
    }, 300) as unknown as number;
  },
  { immediate: false }
);

// 日期范围变化处理
const handleDateChange = (range: [Date | null, Date | null] | null) => {
  dateRange.value = range || [null, null];
  dateRangeText.value = range ? `${dayjs(range[0]).format('YYYY/MM/DD')} 至 ${dayjs(range[1]).format('YYYY/MM/DD')}` : '';

  emit('search', {
    keyword: searchKeyword.value,
    dateRange: range || [null, null],
    dateRangeText: dateRangeText.value,
    staffId: null,
    staffName: '全部'
  });
};

// 搜索参数，传递给子组件
const searchParams = computed(() => {
  return {
    keyword: searchKeyword.value,
    dateRange: dateRange.value,
    dateRangeText: dateRangeText.value,
    staffId: null, // 不再使用操作人筛选，设为null
    staffName: '全部'
  };
});

// 组件挂载后初始化数据
onMounted(() => {
  // 确保组件完全挂载后再触发数据加载
  nextTick(() => {
    console.log('初始化组件');

    // 初始化默认日期范围的显示文本
    const defaultRange = getDefaultDateRange();
    const formatDate = (date: Date) => dayjs(date).format('MM/DD');
    dateRangeText.value = `${formatDate(defaultRange[0])} -- ${formatDate(defaultRange[1])}`;
  });
});

// 控制对话框显示
const storeWineDialogVisible = ref(false);
const takeWineDialogVisible = ref(false);

// 跟踪对话框状态，避免频繁点击导致的问题
const dialogProcessing = ref(false);

// 显示存酒弹窗
const showStoreWineDialog = () => {
  // 如果对话框已经在处理中，直接返回
  if (dialogProcessing.value) return;

  // 标记为处理中
  dialogProcessing.value = true;

  // 确保关闭其他对话框
  takeWineDialogVisible.value = false;

  // 使用nextTick代替setTimeout，确保DOM更新
  nextTick(() => {
    // 先设置为false再设置为true，确保Vue能检测到变化
    storeWineDialogVisible.value = false;

    // 强制Vue重新渲染
    setTimeout(() => {
      storeWineDialogVisible.value = true;
      console.log('存酒对话框状态已设置为:', storeWineDialogVisible.value);

      // 延迟重置处理状态，防止短时间内重复点击
      setTimeout(() => {
        dialogProcessing.value = false;
      }, 500);
    }, 100);
  });
};

// 显示取酒弹窗
const showTakeWineDialog = () => {
  // 如果对话框已经在处理中，直接返回
  if (dialogProcessing.value) return;

  // 标记为处理中
  dialogProcessing.value = true;

  // 确保关闭其他对话框
  storeWineDialogVisible.value = false;

  console.log('触发显示取酒对话框');

  // 强制使用nextTick确保DOM更新
  nextTick(() => {
    // 先设置为false再设置为true，确保Vue能够检测到变化
    takeWineDialogVisible.value = false;

    // 然后用延时设置为true
    setTimeout(() => {
      takeWineDialogVisible.value = true;
      console.log('取酒对话框状态已设置为:', takeWineDialogVisible.value);

      // 延迟重置处理状态，防止短时间内重复点击
      setTimeout(() => {
        dialogProcessing.value = false;
      }, 500);
    }, 100);
  });
};

// 处理存酒确认
const handleStoreWine = (data: any) => {
  console.log('存酒数据:', data);
  // 调用API存储数据
};

// 处理取酒确认
const handleTakeWine = (data: any) => {
  console.log('取酒数据:', data);
  // 调用API存储数据
};
</script>
