<template>
  <div class="flex flex-col h-full">
    <!-- 标签页切换区域 (左侧) 和 搜索筛选区域 (右侧) -->
    <nav class="p-4 border-b">
      <div class="flex items-center justify-between">
        <!-- 左侧标签页切换 -->
        <div class="flex items-center">
          <div class="flex bg-[#F3F3F3] rounded-[10px] h-[68px] items-center gap-[8px] px-[8px]">
            <div
              class="h-[52px] w-[120px] font-medium flex items-center justify-center cursor-pointer text-[16px] rounded-[8px]"
              :class="{ 'bg-[#E23939] text-white': activeTab === 'storeWine', 'text-gray-500': activeTab !== 'storeWine' }"
              @click="switchTab('storeWine')">
              存取酒
            </div>
            <div
              class="h-[52px] w-[120px] font-medium flex items-center justify-center cursor-pointer text-[16px] rounded-[8px]"
              :class="{ 'bg-[#E23939] text-white': activeTab === 'wineStatistics', 'text-gray-500': activeTab !== 'wineStatistics' }"
              @click="switchTab('wineStatistics')">
              存取统计
            </div>
          </div>
        </div>
        <!-- 中间搜索区域 -->
        <div class="flex-1 flex justify-center ml-4" v-if="activeTab === 'storeWine'">
          <div class="relative w-full">
            <el-input v-model="searchKeyword" placeholder="手机号 / 姓名" class="custom-search-input" @keyup.enter="handleSearch" clearable>
              <template #prefix>
                <el-icon class="ml-4">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>
        <!-- 筛选区域 -->
        <div class="flex items-center gap-4" v-if="activeTab === 'storeWine'">
          <!-- 筛选条件：起止时间 -->
          <div class="flex items-center ml-[24px]">
            <el-date-picker
              class="customer-date-picker"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY/MM/DD"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              @change="handleDateChange" />
          </div>

          <!-- 筛选条件：负责人 -->
          <div>
            <div class="selector-wrapper h-[64px] w-[200px] border border-gray-200 rounded-md">
              <EmployeeSelector
                v-model="selectedStaff"
                placeholder="全部"
                :select-current-user="false"
                :options="employeeOptions"
                @change="handleEmployeeChange"
                @options-loaded="handleEmployeesLoaded"
                label="操作人" />
            </div>
          </div>
        </div>

        <!-- 统计页面筛选区域 -->
        <div class="flex items-center gap-4" v-if="activeTab === 'wineStatistics'">
          <!-- 筛选条件：起止时间 -->
          <div class="flex items-center ml-[24px]">
            <el-date-picker
              class="customer-date-picker"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY/MM/DD"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              @change="handleDateChange" />
          </div>
        </div>
      </div>
    </nav>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-hidden">
      <keep-alive>
        <component
          :is="currentComponent"
          :search-params="searchParams"
          @search="handleSearch"
          @open-store-dialog="showStoreWineDialog"
          @open-take-dialog="showTakeWineDialog" />
      </keep-alive>
    </div>

    <!-- 存酒弹窗组件 -->
    <StoreWineDialog v-model="storeWineDialogVisible" @confirm="handleStoreWine" />

    <!-- 取酒弹窗组件 -->
    <TakeWineDialog v-model="takeWineDialogVisible" @confirm="handleTakeWine" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, markRaw, watch, onMounted, nextTick } from 'vue';
import { Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import StoreWine from './StoreWine/index.vue';
import WineStatistics from './WineStatistics/index.vue';
import TakeWineDialog from '../components/TakeWineDialog/index.vue';
import StoreWineDialog from '../components/StoreWineDialog/index.vue';
import EmployeeSelector from '../components/EmployeeSelector.vue';
import { useUserStore } from '@/stores/userStore';

// 使用markRaw以避免不必要的组件代理
const StoreWineComponent = markRaw(StoreWine);
const WineStatisticsComponent = markRaw(WineStatistics);

// 声明 emit
const emit = defineEmits(['search']);

// 当前激活的标签页
const activeTab = ref<'storeWine' | 'wineStatistics'>('storeWine');

// 切换标签页
const switchTab = (tab: 'storeWine' | 'wineStatistics') => {
  activeTab.value = tab;
};

// 当前组件
const currentComponent = computed(() => {
  return activeTab.value === 'storeWine' ? StoreWineComponent : WineStatisticsComponent;
});

// 计算默认日期范围：从今天往前倒推一周
const getDefaultDateRange = (): [Date, Date] => {
  const today = dayjs().endOf('day').toDate(); // 今天结束时间
  const weekAgo = dayjs().subtract(6, 'days').startOf('day').toDate(); // 7天前开始时间（包含今天共7天）

  return [weekAgo, today];
};

// 搜索和筛选状态
const searchKeyword = ref('');
const dateRange = ref<[Date | null, Date | null]>(getDefaultDateRange());
const dateRangeText = ref('');
const selectedStaff = ref<string>('');
const selectedStaffName = ref('全部');
const employeeOptions = ref<any[]>([]); // 初始化为空数组，让EmployeeSelector自动加载

// 获取用户信息
const userStore = useUserStore();

// 防抖计时器
let searchTimer: number | null = null;

// 处理搜索
const handleSearch = () => {
  // 取消之前的延迟搜索
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 触发搜索
  emit('search', {
    keyword: searchKeyword.value,
    dateRange: dateRange.value,
    staffId: selectedStaff.value,
    staffName: selectedStaffName.value
  });
};

// 监听搜索关键词变化，使用防抖
watch(searchKeyword, newValue => {
  // 取消之前的延迟搜索
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的延迟搜索（300ms延迟）
  searchTimer = setTimeout(() => {
    // 触发搜索
    emit('search', {
      keyword: newValue,
      dateRange: dateRange.value,
      staffId: selectedStaff.value,
      staffName: selectedStaffName.value
    });
  }, 300) as unknown as number;
});

// 处理日期变化
const handleDateChange = (range: [Date, Date] | null) => {
  if (range && range[0] && range[1]) {
    const formatDate = (date: Date) => dayjs(date).format('MM/DD');
    dateRangeText.value = `${formatDate(range[0])} -- ${formatDate(range[1])}`;
  } else {
    dateRangeText.value = '';
  }

  // 触发搜索
  emit('search', {
    keyword: searchKeyword.value,
    dateRange: range,
    staffId: selectedStaff.value,
    staffName: selectedStaffName.value
  });
};

// 处理员工选择变化
const handleEmployeeChange = (employeeId: string, employeeData?: any) => {
  console.log('员工选择变化:', { employeeId, employeeData });

  selectedStaff.value = employeeId;

  // 如果选择的是空值，表示选择了"全部"
  if (employeeId === '') {
    selectedStaffName.value = '全部';
  } else {
    selectedStaffName.value = employeeData?.name || '';
  }

  // 触发搜索
  emit('search', {
    keyword: searchKeyword.value,
    dateRange: dateRange.value,
    staffId: employeeId,
    staffName: selectedStaffName.value
  });
};

// 处理员工选项加载完成
const handleEmployeesLoaded = (options: any[]) => {
  console.log('📥 存酒列表页面: 员工选项加载完成:', options);
  console.log('📥 选项数量:', options?.length || 0);

  if (!options || options.length === 0) {
    console.warn('⚠️ 存酒列表页面: 员工选项为空，可能是API调用失败');
    return;
  }

  // 在选项列表开头添加"全部"选项
  const allOptions = [
    {
      label: '全部',
      value: '',
      employeeData: null
    },
    ...options
  ];

  console.log('✅ 存酒列表页面: 添加"全部"选项后的完整列表:', allOptions);

  // 更新组件的选项列表
  employeeOptions.value = allOptions;

  // 设置默认选择为"全部"
  if (!selectedStaff.value) {
    selectedStaff.value = '';
    selectedStaffName.value = '全部';

    console.log('🎯 存酒列表页面: 设置默认选择为"全部"');

    // 触发一次搜索以应用"全部"筛选
    emit('search', {
      keyword: searchKeyword.value,
      dateRange: dateRange.value,
      staffId: '',
      staffName: '全部'
    });
  }
};

// 搜索参数，传递给子组件
const searchParams = computed(() => {
  return {
    keyword: searchKeyword.value,
    dateRange: dateRange.value,
    dateRangeText: dateRangeText.value,
    staffId: selectedStaff.value,
    staffName: selectedStaffName.value
  };
});

// 组件挂载后初始化数据
onMounted(() => {
  // 确保组件完全挂载后再触发数据加载
  nextTick(() => {
    console.log('初始化组件');

    // 初始化为"全部"选项
    if (!selectedStaff.value) {
      selectedStaff.value = '';
      selectedStaffName.value = '全部';
    }

    // 初始化默认日期范围的显示文本
    const defaultRange = getDefaultDateRange();
    const formatDate = (date: Date) => dayjs(date).format('MM/DD');
    dateRangeText.value = `${formatDate(defaultRange[0])} -- ${formatDate(defaultRange[1])}`;
  });
});

// 控制对话框显示
const storeWineDialogVisible = ref(false);
const takeWineDialogVisible = ref(false);

// 跟踪对话框状态，避免频繁点击导致的问题
const dialogProcessing = ref(false);

// 显示存酒弹窗
const showStoreWineDialog = () => {
  // 如果对话框已经在处理中，直接返回
  if (dialogProcessing.value) return;

  // 标记为处理中
  dialogProcessing.value = true;

  // 确保关闭其他对话框
  takeWineDialogVisible.value = false;

  // 使用nextTick代替setTimeout，确保DOM更新
  nextTick(() => {
    // 先设置为false再设置为true，确保Vue能检测到变化
    storeWineDialogVisible.value = false;

    // 强制Vue重新渲染
    setTimeout(() => {
      storeWineDialogVisible.value = true;
      console.log('存酒对话框状态已设置为:', storeWineDialogVisible.value);

      // 延迟重置处理状态，防止短时间内重复点击
      setTimeout(() => {
        dialogProcessing.value = false;
      }, 500);
    }, 100);
  });
};

// 显示取酒弹窗
const showTakeWineDialog = () => {
  // 如果对话框已经在处理中，直接返回
  if (dialogProcessing.value) return;

  // 标记为处理中
  dialogProcessing.value = true;

  // 确保关闭其他对话框
  storeWineDialogVisible.value = false;

  console.log('触发显示取酒对话框');

  // 强制使用nextTick确保DOM更新
  nextTick(() => {
    // 先设置为false再设置为true，确保Vue能够检测到变化
    takeWineDialogVisible.value = false;

    // 然后用延时设置为true
    setTimeout(() => {
      takeWineDialogVisible.value = true;
      console.log('取酒对话框状态已设置为:', takeWineDialogVisible.value);

      // 延迟重置处理状态，防止短时间内重复点击
      setTimeout(() => {
        dialogProcessing.value = false;
      }, 500);
    }, 100);
  });
};

// 处理存酒确认
const handleStoreWine = (data: any) => {
  console.log('存酒数据:', data);
  // 调用API存储数据
};

// 处理取酒确认
const handleTakeWine = (data: any) => {
  console.log('取酒数据:', data);
  // 调用API存储数据
};
</script>
