import { ref, reactive, computed } from 'vue';
import type { IStoreWineViewModel } from './viewmodel';
import { useRequest } from 'vue-hooks-plus';
import { useProductStorageStore } from '@/stores/productStorageStore';
import dayjs from 'dayjs';
import {
  postApiProductStorageAdd,
  postApiProductStorageQuery,
  postApiProductWithdrawAdd,
  getApiProductStorageDetailId
} from '@/api/autoGenerated/defaultVersion/product';

import { ElMessage } from 'element-plus';
import { wineOperationService } from '@/modules/wine/services/wineOperationService';

// 从API获取数据的函数
const fetchWineRecords = async (queryParams: any) => {
  try {
    const response = await postApiProductStorageQuery({
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的值来获取所有数据，不分页
      searchText: queryParams.keyword || undefined,
      storageTimeStart: queryParams.dateRange && queryParams.dateRange[0] ? Math.floor(queryParams.dateRange[0].getTime() / 1000) : undefined,
      storageTimeEnd: queryParams.dateRange && queryParams.dateRange[1] ? Math.floor(queryParams.dateRange[1].getTime() / 1000) : undefined,
      operatorId: queryParams.staffId || undefined
    });
    const responseData = response.data;

    return {
      list: responseData?.data || [],
      total: responseData?.total || 0,
      pageNum: responseData?.pageNum,
      pageSize: responseData?.pageSize
    };
  } catch (error) {
    console.error('获取存取酒记录失败:', error);
    // 失败时返回空数据
    return {
      list: [],
      total: 0,
      pageNum: 1,
      pageSize: 20
    };
  }
};

// 存酒API调用
const storeWineToApi = async (data: any) => {
  try {
    const currentTime = dayjs().unix();

    // 确保操作人信息存在
    const operatorId = data.operatorId;
    const operatorName = data.operatorName;

    const response = await postApiProductStorageAdd({
      productId: data.productId,
      productName: data.productName,
      productUnit: data.productUnit || '',
      customerName: data.customerName,
      phoneNumber: data.phoneNumber,
      memberCardNo: data.memberCard || undefined,
      operatorId: operatorId, // 使用变量确保字段存在
      operatorName: operatorName, // 使用变量确保字段存在
      quantity: data.quantity,
      remark: data.remark,
      storageLocation: data.storageLocation,
      expireTime: data.expireTime ? dayjs(data.expireTime).unix() : dayjs().add(1, 'year').unix(), // 默认一年有效期
      storageTime: currentTime
    });
    return response;
  } catch (error) {
    console.error('存酒操作失败:', error);
    throw error;
  }
};

// 取酒API调用
const takeWineFromApi = async (data: any) => {
  try {
    const currentTime = dayjs().unix();
    const response = await postApiProductWithdrawAdd({
      storageId: data.recordId, // 存储记录ID
      productId: data.productId,
      productName: data.productName,
      customerId: data.customerId,
      customerName: data.customerName,
      quantity: data.quantity,
      remark: data.remark,
      operatorId: data.operatorId,
      operatorName: data.operatorName,
      withdrawTime: currentTime,
      storageOrderNo: data.orderNo || '' // 存酒单号
    } as any);
    return response;
  } catch (error) {
    console.error('取酒操作失败:', error);
    throw error;
  }
};

// 获取存酒详情
const getWineDetailFromApi = async (id: string) => {
  try {
    const response = await getApiProductStorageDetailId({ id });
    return response;
  } catch (error) {
    console.error('获取存酒详情失败:', error);
    throw error;
  }
};

export class StoreWinePresenter implements IStoreWineViewModel {
  // 存酒配置store
  private storageStore = useProductStorageStore();

  // 状态
  public state = reactive({
    searchKeyword: '',
    dateRange: [null, null] as [Date | null, Date | null],
    dateRangeText: '',
    selectedStaff: null as string | null,
    selectedStaffName: '',
    records: [] as any[],
    currentMode: 'store' as 'store' | 'take',

    // 加载状态
    loading: false,

    // 对话框控制
    showStoreForm: false,
    showTakeForm: false,
    showDetailDialog: false,
    currentDetail: null as any,

    // 分页相关已移除
    total: 0
  });

  // 使用useRequest钩子加载数据
  private requestService = useRequest(fetchWineRecords, {
    manual: true,
    onSuccess: result => {
      this.state.records = result.list;
      this.state.total = result.total;
      this.state.loading = false;
    },
    onError: () => {
      this.state.loading = false;
    }
  });

  // 计算属性
  public computed = {
    filteredRecords: computed(() => {
      return this.state.records;
    })
  };

  // 动作
  public actions = {
    // 搜索相关
    onSearchChange: () => {
      this.requestService.run({
        mode: this.state.currentMode,
        keyword: this.state.searchKeyword,
        dateRange: this.state.dateRange,
        staffId: this.state.selectedStaff
      });
    },

    selectDateRange: (range: [Date, Date]) => {
      this.state.dateRange = range;

      // 格式化日期显示
      if (range && range[0] && range[1]) {
        const formatDate = (date: Date) => dayjs(date).format('MM/DD');
        this.state.dateRangeText = `${formatDate(range[0])} -- ${formatDate(range[1])}`;
      } else {
        this.state.dateRangeText = '';
      }

      this.requestService.run({
        mode: this.state.currentMode,
        keyword: this.state.searchKeyword,
        dateRange: this.state.dateRange,
        staffId: this.state.selectedStaff
      });
    },

    selectStaff: (staffId: string, staffName: string) => {
      this.state.selectedStaff = staffId;
      this.state.selectedStaffName = staffName;

      this.requestService.run({
        mode: this.state.currentMode,
        keyword: this.state.searchKeyword,
        dateRange: this.state.dateRange,
        staffId: this.state.selectedStaff
      });
    },

    // 存酒/取酒模式切换
    switchToStoreMode: () => {
      this.state.currentMode = 'store';
      this.requestService.run({
        mode: this.state.currentMode,
        keyword: this.state.searchKeyword,
        dateRange: this.state.dateRange,
        staffId: this.state.selectedStaff
      });
    },

    switchToTakeMode: () => {
      this.state.currentMode = 'take';
      this.requestService.run({
        mode: this.state.currentMode,
        keyword: this.state.searchKeyword,
        dateRange: this.state.dateRange,
        staffId: this.state.selectedStaff
      });
    },

    // 刷新数据
    refreshData: () => {
      this.state.loading = true;
      this.requestService.run({
        mode: this.state.currentMode,
        keyword: this.state.searchKeyword,
        dateRange: this.state.dateRange,
        staffId: this.state.selectedStaff
      });
    },

    // 以下方法已移除，因为不再使用分页
    // handleCurrentChange 和 handleSizeChange

    // 存酒取酒操作
    showStoreWineForm: () => {
      this.state.showStoreForm = true;
    },

    showTakeWineForm: () => {
      this.state.showTakeForm = true;
    },

    // 存酒操作
    storeWine: async (data: any) => {
      try {
        this.state.loading = true;

        // 确保operator字段存在
        if (!data.operatorId || !data.operatorName) {
          ElMessage.warning('操作人信息不完整');
          this.state.loading = false;
          return;
        }

        await storeWineToApi({
          ...data,
          operatorId: data.operatorId, // 确保传递操作人ID
          operatorName: data.operatorName // 确保传递操作人姓名
        });

        // 如果执行到这里，说明API调用成功 (code === 0)
        ElMessage.success('存酒成功');
        this.state.showStoreForm = false;
        this.actions.refreshData();
      } catch (error: any) {
        console.error('存酒操作失败:', error);
        ElMessage.error(error?.message || '存酒操作失败');
        this.state.loading = false;
      }
    },

    // 取酒操作
    takeWine: async (data: any) => {
      try {
        this.state.loading = true;

        // 确保operator字段存在
        if (!data.operatorId || !data.operatorName) {
          ElMessage.warning('操作人信息不完整');
          this.state.loading = false;
          return;
        }

        // request.ts的拦截器已经处理了code !== 0的情况
        await takeWineFromApi({
          recordId: data.recordId,
          customerId: data.customerId,
          customerName: data.customerName,
          productId: data.productId,
          productName: data.productName,
          quantity: data.quantity,
          remark: data.remark || '',
          operatorId: data.operatorId, // 使用传入的操作人ID
          operatorName: data.operatorName, // 使用传入的操作人姓名
          orderNo: data.orderNo || ''
        });

        // 如果执行到这里，说明API调用成功 (code === 0)
        ElMessage.success('取酒成功');
        this.actions.refreshData();
      } catch (error: any) {
        console.error('取酒操作失败:', error);
        ElMessage.error(error?.message || '取酒操作失败');
        this.state.loading = false;
      }
    },

    // 记录操作
    viewDetail: async (recordId: string) => {
      try {
        // 先关闭对话框，确保Vue能检测到状态变化
        this.state.showDetailDialog = false;

        // 获取存酒详情，期望服务端直接返回完整的存取酒历史记录
        const response = await getWineDetailFromApi(recordId);

        // 直接使用API返回的数据，不再手动构建操作历史
        if (response && response.data) {
          this.state.currentDetail = response.data;
          // 延迟一帧再打开对话框，确保Vue渲染循环能检测到状态变化
          setTimeout(() => {
            this.state.showDetailDialog = true;
          }, 0);
        } else {
          ElMessage.error('获取详情数据失败');
        }
      } catch (error) {
        console.error('查看详情失败:', error);
        ElMessage.error('查看详情失败');
      }
    },

    // 撤销存酒
    cancelWine: async (id: string, operatorId: string, operatorName: string, remark: string = '整单撤销') => {
      try {
        this.state.loading = true;

        // 调用服务执行撤销
        await wineOperationService.cancelWineStorage(id, operatorId, operatorName, remark);

        ElMessage.success('撤销存酒成功');
        this.actions.refreshData();
      } catch (error: any) {
        console.error('撤销存酒失败:', error);
        ElMessage.error(error?.message || '撤销存酒失败');
      } finally {
        this.state.loading = false;
      }
    },

    // 报废存酒
    discardWine: async (id: string, operatorId: string, operatorName: string, remark: string = '手动报废') => {
      try {
        this.state.loading = true;

        // 调用服务执行报废
        await wineOperationService.discardWineStorage(id, operatorId, operatorName, remark);

        ElMessage.success('存酒报废成功');
        this.actions.refreshData();
      } catch (error: any) {
        console.error('存酒报废失败:', error);
        ElMessage.error(error?.message || '存酒报废失败');
      } finally {
        this.state.loading = false;
      }
    },

    // 续存酒 - 延长有效期
    extendWine: async (id: string, expireTime: number, operatorId: string, operatorName: string, remark: string = '延长有效期') => {
      try {
        this.state.loading = true;

        // 调用服务执行续存
        await wineOperationService.extendWineStorage(id, expireTime, operatorId, operatorName, remark);

        ElMessage.success('续存成功');
        this.actions.refreshData();
      } catch (error: any) {
        console.error('续存失败:', error);
        ElMessage.error(error?.message || '续存失败');
      } finally {
        this.state.loading = false;
      }
    },

    // 添加商品到存酒单
    addItemsToWine: async (id: string, orderNo: string, operatorId: string, operatorName: string, items: any[]) => {
      try {
        this.state.loading = true;

        // 调用服务执行添加商品
        await wineOperationService.addItemsToWineStorage(id, orderNo, operatorId, operatorName, items);

        ElMessage.success('添加商品成功');
        this.actions.refreshData();
      } catch (error: any) {
        console.error('添加商品失败:', error);
        ElMessage.error(error?.message || '添加商品失败');
      } finally {
        this.state.loading = false;
      }
    },

    printRecord: (recordId: string) => {
      console.log('打印记录:', recordId);
      // 实现打印记录逻辑
    }
  };
}

// 导出组合式函数
export function useStoreWinePresenter(): IStoreWineViewModel {
  return new StoreWinePresenter();
}
