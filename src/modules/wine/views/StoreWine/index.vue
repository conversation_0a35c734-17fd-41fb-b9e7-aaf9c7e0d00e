<template>
  <div class="h-full flex flex-col relative">
    <!-- 表格内容区域 (可滚动) -->
    <div class="flex-1 overflow-hidden px-[20px] pb-[130px]">
      <div v-if="vm.state.loading" class="flex items-center justify-center h-64">
        <el-icon class="is-loading"><loading /></el-icon>
        <span class="ml-2">加载中...</span>
      </div>

      <div v-else-if="vm.computed.filteredRecords.value.length === 0" class="flex flex-col items-center justify-center h-64 mt-[200px]">
        <el-empty description="暂无存酒记录" />
      </div>

      <template v-else>
        <el-table :data="vm.computed.filteredRecords.value" stripe size="large" :height="tableHeight">
          <!-- 单号列 -->
          <el-table-column prop="parentOrderNo" label="单号" min-width="160"></el-table-column>

          <!-- 存取时间列 -->
          <el-table-column prop="phoneNumber" label="手机号" min-width="110"></el-table-column>

          <!-- 存取内容列 -->
          <el-table-column prop="customerName" label="存酒人姓名" min-width="80">
            <template #default="scope">
              <div class="flex items-center justify-center">
                <span>{{ scope.row.customerName || '--' }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 数量列 -->
          <el-table-column prop="productName" label="商品名称" min-width="130"> </el-table-column>

          <!-- 数量列 -->
          <el-table-column prop="remainingQty" label="数量" min-width="80"> </el-table-column>
          <!-- 存酒日期 -->
          <el-table-column prop="ctime" label="存酒日期" min-width="120">
            <template #default="scope">
              {{ formatUnixTimestamp(scope.row.ctime) }}
            </template>
          </el-table-column>
          <!-- 操作人列 -->
          <el-table-column prop="operatorName" label="操作人" min-width="80"></el-table-column>

          <!-- 负责人列 -->
          <el-table-column prop="statusName" label="存酒状态" min-width="80">
            <template #default="scope">
              <span
                :class="{
                  'text-red-500 font-medium': isExpiredAndNotDiscarded(scope.row),
                  'text-gray-600': !isExpiredAndNotDiscarded(scope.row)
                }">
                {{ getStatusDisplay(scope.row) }}
              </span>
            </template>
          </el-table-column>
          <!-- 操作列 -->
          <el-table-column label="操作" min-width="120" align="center">
            <template #default="scope">
              <div class="flex items-center justify-start gap-2">
                <el-button type="primary" size="small" plain @click="vm.actions.viewDetail(scope.row.id)"> 查看 </el-button>

                <el-button
                  type="default"
                  size="small"
                  v-show="!INOPERABLE_STATUS_CODES.includes(scope.row.statusCode) && isWithin24Hours(scope.row.lastOperationTime)"
                  plain
                  @click="handlePartialCancel(scope.row)">
                  撤销
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 - 与表格一体化设计 -->
        <!-- <div class="flex justify-center mt-4 py-4 border-t border-gray-200 bg-white" v-if="vm.state.total > vm.state.pageSize">
          <el-pagination
            v-model:current-page="vm.state.currentPage"
            v-model:page-size="vm.state.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="vm.state.total"
            @size-change="vm.actions.handleSizeChange"
            @current-change="vm.actions.handleCurrentChange" />
        </div> -->
      </template>
    </div>

    <!-- 使用通用底部操作栏组件 - 固定在底部 -->
    <PageFooterActions>
      <el-button class="!w-[160px] !h-[64px]" @click="handleTakeWineClick"> 取酒 </el-button>
      <el-button type="primary" class="!w-[160px] !h-[64px]" @click="emit('open-store-dialog')"> 存酒 </el-button>
    </PageFooterActions>

    <!-- 存酒详情弹窗 -->
    <StoreWineDetailDialog v-model="vm.state.showDetailDialog" :detail="vm.state.currentDetail" @refresh-list="vm.actions.refreshData" />

    <!-- 撤销存酒对话框 -->
    <el-dialog v-model="showCancelDialog" title="撤销存酒" width="500px" :close-on-click-modal="false" custom-class="cancel-wine-dialog">
      <div class="p-4">
        <p class="mb-5 text-lg">撤销存酒记录</p>

        <el-alert type="warning" show-icon :closable="false" class="mb-4">
          <p>您正在撤销此存酒记录，此操作将减少商品库存量，且不可恢复。</p>
        </el-alert>

        <div class="bg-gray-50 p-4 rounded mb-4">
          <p class="mb-2"><strong>商品名称：</strong>{{ selectedRecord?.productName }}</p>
          <p class="mb-2"><strong>当前剩余数量：</strong>{{ selectedRecord?.remainingQty }}</p>
          <p class="mb-0"><strong>存酒人：</strong>{{ selectedRecord?.customerName || '--' }}</p>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button @click="showCancelDialog = false" size="large">取消</el-button>
          <el-button type="primary" @click="confirmCancelWine" :loading="operationLoading" size="large"> 确认撤销 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 续存对话框 -->
    <el-dialog v-model="showExtendDialog" title="续存操作" width="500px" :close-on-click-modal="false" custom-class="extend-wine-dialog">
      <div class="p-4 text-center">
        <p class="text-xl mb-6">续存</p>
        <p class="text-lg mb-8">续存至 {{ extendDateFormatted }}</p>

        <div class="bg-gray-50 p-4 rounded mb-6">
          <p class="mb-2"><strong>商品名称：</strong>{{ selectedRecord?.productName }}</p>
          <p class="mb-2"><strong>当前到期时间：</strong>{{ formatUnixTimestamp(selectedRecord?.expireTime) }}</p>
          <p class="mb-2"><strong>续存天数：</strong>{{ storageStore.renewalDays }}天</p>
          <p class="mb-0"><strong>续存次数：</strong>{{ selectedRecord ? getRenewalInfo(selectedRecord).current + 1 : 1 }}/{{ storageStore.renewalTimes }}</p>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-center gap-6">
          <el-button @click="showExtendDialog = false" size="large" class="w-40">取消</el-button>
          <el-button type="primary" @click="confirmExtendWine" :loading="operationLoading" size="large" class="w-40"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 报废对话框 -->
    <el-dialog v-model="showDiscardDialog" title="报废" width="480px" :close-on-click-modal="false" custom-class="discard-wine-dialog">
      <div class="text-center p-6">
        <div class="text-[#E23939] text-xl font-medium mb-6">报废</div>

        <div class="my-8 text-base">操作报废会产生报废记录</div>
      </div>
      <template #footer>
        <div class="flex justify-center pb-6">
          <el-button type="danger" @click="handleConfirmDiscard" :loading="operationLoading" size="large" class="w-40 !h-[46px]"> 确认报废 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, onMounted, onUnmounted } from 'vue';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import dayjs from 'dayjs';
import { useStoreWinePresenter } from './presenter';
import PageFooterActions from '@/components/PageFooterActions.vue';
import StoreWineDetailDialog from '@/modules/wine/components/StoreWineDetailDialog/index.vue';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/userStore';
import { StorageStatusCode, INOPERABLE_STATUS_CODES } from '@/modules/wine/constants';
import { Loading } from '@element-plus/icons-vue';
import eventBus from '@/utils/eventBus';
import { useProductStorageStore } from '@/stores/productStorageStore';

// 定义props接收父组件传递的搜索参数
const props = defineProps<{
  searchParams?: {
    keyword: string;
    dateRange: [Date | null, Date | null];
    dateRangeText: string;
    staffId: string | null;
    staffName: string;
  };
}>();

// 定义事件，向父组件传递搜索参数和对话框事件
const emit = defineEmits<{
  (e: 'search', params: any): void;
  (e: 'open-store-dialog'): void;
  (e: 'open-take-dialog'): void;
}>();

// 使用Presenter组件
const vm = useStoreWinePresenter();

// 防止多次点击处理
const buttonClickProcessing = ref(false);

// 获取当前用户信息
const userStore = useUserStore();
const currentOperatorId = computed(() => userStore.userInfo?.employee?.id || '');
const currentOperatorName = computed(() => userStore.userInfo?.employee?.name || '');

// 表格高度计算 - 固定头部
const tableHeight = computed(() => {
  // 计算可用高度：100vh - 页面头部(60px) - 筛选区(80px) - 底部按钮(130px)
  return 'calc(100vh - 260px)';
});

// 引入存酒配置store
const storageStore = useProductStorageStore();

// 状态管理
const operationLoading = ref(false);
const selectedRecord = ref<any>(null);

// 撤销对话框状态
const showCancelDialog = ref(false);

// 续存对话框状态
const showExtendDialog = ref(false);
const extendForm = reactive({
  expireTime: new Date(), // 将在handleWineOperation中计算正确的续存时间
  remark: ''
});

// 报废对话框状态
const showDiscardDialog = ref(false);
const discardForm = reactive({
  remark: ''
});

// 限制日期选择，不能选择今天之前的日期
const disabledDate = (time: Date) => {
  return dayjs(time).isBefore(dayjs().startOf('day')); // 不能选择今天之前的日期
};

// 检查是否为续存操作 - 只根据标准字段判断，不依赖文案
const isRenewalOperation = (op: any) => {
  // 方式1: 检查操作类型字段
  if (op.type === 'extend') {
    return true;
  }

  // 方式2: 检查操作类型字段 (可能的其他命名)
  if (op.operationType === 'extend') {
    return true;
  }

  // 方式3: 检查操作动作字段
  if (op.action === 'extend') {
    return true;
  }

  return false;
};

// 检查是否可以续存
const canExtendRecord = (record: any) => {
  if (!record?.operationHistory) return true;

  // 计算当前已续存次数
  const extendCount = record.operationHistory.filter(isRenewalOperation).length;

  return storageStore.canRenew(extendCount);
};

// 获取续存次数信息
const getRenewalInfo = (record: any) => {
  if (!record?.operationHistory) return { current: 0, limit: storageStore.renewalTimes };

  const currentCount = record.operationHistory.filter(isRenewalOperation).length;

  return {
    current: currentCount,
    limit: storageStore.renewalTimes
  };
};

// 处理操作指令
const handleWineOperation = async (command: string, record: any) => {
  selectedRecord.value = record;

  switch (command) {
    case 'partial-cancel':
      showCancelDialog.value = true;
      break;

    case 'extend':
      // 确保配置已加载
      await storageStore.ensureSettings();

      // 检查是否可以续存
      if (!canExtendRecord(record)) {
        const info = getRenewalInfo(record);
        ElMessage.warning(`已达到续存上限（${info.limit}次），无法继续续存`);
        return;
      }

      if (!record.expireTime) {
        ElMessage.warning('无法获取原过期时间');
        return;
      }

      // 在原过期时间基础上 + 续存天数
      const newExpireTime = storageStore.calculateRenewExpireTime(record.expireTime);
      extendForm.expireTime = new Date(newExpireTime * 1000);
      extendForm.remark = `续存${storageStore.renewalDays}天`;

      showExtendDialog.value = true;
      break;

    case 'discard':
      showDiscardDialog.value = true;
      discardForm.remark = '';
      break;

    case 'print':
      vm.actions.printRecord(record.id);
      break;
  }
};

// 计算续存后的日期格式化显示
const extendDateFormatted = computed(() => {
  if (!extendForm.expireTime) return '';
  return dayjs(extendForm.expireTime).format('YYYY - MM - DD');
});

// 处理撤销按钮点击
const handlePartialCancel = (record: any) => {
  // 检查是否在24小时内
  if (!isWithin24Hours(record.lastOperationTime)) {
    ElMessage.warning('只能撤销24小时内的记录');
    return;
  }

  // 检查状态
  if (INOPERABLE_STATUS_CODES.includes(record.statusCode)) {
    ElMessage.warning('该记录状态不允许撤销操作');
    return;
  }

  selectedRecord.value = record;
  showCancelDialog.value = true;
};

// 确认撤销存酒
const confirmCancelWine = async () => {
  if (!selectedRecord.value) return;

  operationLoading.value = true;
  try {
    await vm.actions.cancelWine(
      selectedRecord.value.id,
      currentOperatorId.value,
      currentOperatorName.value,
      '整单撤销存酒' // 固定的撤销原因
    );

    showCancelDialog.value = false;
  } catch (error) {
    // 错误已在presenter中处理
  } finally {
    operationLoading.value = false;
  }
};

// 确认续存
const confirmExtendWine = async () => {
  if (!selectedRecord.value) return;

  operationLoading.value = true;
  try {
    // 使用计算后的到期时间戳
    const expireTimestamp = Math.floor(extendForm.expireTime.getTime() / 1000);

    await vm.actions.extendWine(
      selectedRecord.value.id,
      expireTimestamp,
      currentOperatorId.value,
      currentOperatorName.value,
      extendForm.remark // 使用计算的备注
    );

    showExtendDialog.value = false;
    // 删除重复的成功消息，因为presenter中已经有了
  } catch (error) {
    // 错误已在presenter中处理
  } finally {
    operationLoading.value = false;
  }
};

// 确认报废
const handleConfirmDiscard = async () => {
  if (!selectedRecord.value) return;

  // 检查状态，防止对已报废或已取走的记录操作
  if (selectedRecord.value.statusCode === StorageStatusCode.DISCARDED) {
    ElMessage.warning('该记录已报废，不能重复操作');
    showDiscardDialog.value = false;
    return;
  }

  if (selectedRecord.value.statusCode === StorageStatusCode.WITHDRAWN) {
    ElMessage.warning('该记录已取走，不能进行报废操作');
    showDiscardDialog.value = false;
    return;
  }

  // 防止重复提交
  if (operationLoading.value) return;

  operationLoading.value = true;
  try {
    // 生成唯一操作ID，防止重复提交
    const operationId = dayjs().valueOf().toString();

    await vm.actions.discardWine(
      selectedRecord.value.id,
      currentOperatorId.value,
      currentOperatorName.value,
      `系统报废_${operationId}` // 添加时间戳作为唯一标识
    );

    showDiscardDialog.value = false;
    ElMessage.success('报废成功');
    // 刷新数据
    vm.actions.refreshData();
  } catch (error: any) {
    console.error('报废操作失败:', error);
    // 错误处理
    if (error?.message && error.message.includes('Duplicate entry')) {
      ElMessage.error('该记录已经被报废，请刷新页面');
    } else {
      ElMessage.error(error?.message || '报废操作失败');
    }
  } finally {
    operationLoading.value = false;
  }
};

// 处理取酒按钮点击事件
const handleTakeWineClick = () => {
  if (buttonClickProcessing.value) return;

  buttonClickProcessing.value = true;
  emit('open-take-dialog');

  // 延迟重置处理状态
  setTimeout(() => {
    buttonClickProcessing.value = false;
  }, 100);
};

// 监听父组件传递的搜索参数
watch(
  () => props.searchParams,
  newParams => {
    if (newParams) {
      // 更新本地状态
      vm.state.searchKeyword = newParams.keyword;
      vm.state.dateRange = newParams.dateRange;
      vm.state.dateRangeText = newParams.dateRangeText;
      vm.state.selectedStaff = newParams.staffId;
      vm.state.selectedStaffName = newParams.staffName;

      // 触发搜索
      vm.actions.refreshData();
    }
  },
  { immediate: true, deep: true }
);

// 新增方法：检查存酒是否过期且未报废
const isExpiredAndNotDiscarded = (record: any): boolean => {
  // 如果已报废，不显示过期
  if (record.statusCode === StorageStatusCode.DISCARDED) {
    return false;
  }

  // 如果已取走，不显示过期
  if (record.statusCode === StorageStatusCode.WITHDRAWN) {
    return false;
  }

  // 使用 daysToExpire 字段判断是否过期
  // daysToExpire <= 0 表示已过期
  return record.daysToExpire !== undefined && record.daysToExpire <= 0;
};

// 新增方法：获取状态显示文本
const getStatusDisplay = (record: any): string => {
  // 如果过期且未报废，显示"已过期"
  if (isExpiredAndNotDiscarded(record)) {
    return '已过期';
  }

  // 否则显示原状态
  return record.statusName || '--';
};

// 获取按钮禁用时的提示文本
const getDisabledButtonTooltip = (record: any): string => {
  if (!record) return '';

  // 如果是已报废状态
  if (record.statusCode === StorageStatusCode.DISCARDED) {
    return '该记录已报废，不可撤销';
  }

  // 如果是其他不可操作状态
  if (INOPERABLE_STATUS_CODES.includes(record.statusCode)) {
    return `该记录状态为"${record.statusName}"，不可撤销`;
  }

  // 如果不在24小时内
  if (!isWithin24Hours(record.lastOperationTime)) {
    return '只能撤销24小时内的记录';
  }

  return '';
};

// 修改方法：检查记录是否在24小时内可撤销
const isWithin24Hours = (lastOperationTime: number): boolean => {
  if (!lastOperationTime) return false;

  // 将秒级时间戳转换为dayjs对象
  const lastOperationDateTime = dayjs.unix(lastOperationTime);
  const currentTime = dayjs();

  // 计算时间差，判断是否在24小时内
  return currentTime.diff(lastOperationDateTime, 'hour') <= 24;
};

// 监听事件总线的刷新事件
onMounted(() => {
  // 监听刷新事件
  eventBus.on('wine-list-refresh', () => {
    console.log('StoreWine组件收到wine-list-refresh事件，开始刷新数据');
    vm.actions.refreshData();
  });
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off('wine-list-refresh');
});
</script>

<style lang="scss" scoped>
// 固定表头样式
:deep(.fixed-header-table) {
  // Element Plus表格的固定头部已通过height属性自动启用
  // 这里只需要自定义样式
  .el-table__header-wrapper {
    background: #fafafa;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-table__header th {
    background: #fafafa !important;
    color: #606266;
    font-weight: 500;
    border-bottom: 1px solid #e4e7ed;
  }

  // 优化滚动条样式
  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
}

// 下拉菜单样式
:deep(.el-dropdown-menu__item) {
  &.el-dropdown-menu__item--divided {
    &:before {
      margin: 0;
      height: 1px;
    }
  }

  &:not(.is-disabled):hover {
    background-color: #f5f7fa;
  }

  &:focus {
    color: #409eff;
    background-color: #ecf5ff;
  }
}

// 对话框样式
:deep(.cancel-wine-dialog),
:deep(.extend-wine-dialog),
:deep(.discard-wine-dialog) {
  .el-dialog__header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 0;
    border-top: 1px solid #e4e7ed;
  }

  .el-input__wrapper {
    box-shadow: 0 0 0 1px #dcdfe6 inset;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }

  .el-input-number {
    width: 100%;
  }

  .el-form-item__label {
    font-weight: 500;
  }

  .el-button.is-loading {
    opacity: 0.8;
  }
}

// 特殊处理报废对话框
:deep(.discard-wine-dialog) {
  .el-dialog__title {
    display: none;
  }

  .el-alert {
    border-radius: 4px;

    .el-alert__icon {
      color: #f56c6c;
    }

    .el-alert__content {
      padding: 0 8px;
    }
  }

  .el-textarea__inner {
    min-height: 80px;
    border-color: #dcdfe6;

    &:focus {
      border-color: #409eff;
    }

    &::placeholder {
      color: #c0c4cc;
    }
  }
}
</style>
