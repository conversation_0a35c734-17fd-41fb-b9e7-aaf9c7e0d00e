import { ComputedRef } from 'vue';

// 存取酒记录接口
export interface WineRecord {
  id: string;
  memberName: string;
  cardNo: string;
  mobile: string;
  type: string;
  datetime: string;
  content: string;
  count: number;
  remainCount: number;
  staffName: string;
  status: string;
}

// 存取模式类型
export type StoreWineMode = 'store' | 'take';

// UI状态接口
export interface IStoreWineState {
  searchKeyword: string;
  dateRange: [Date | null, Date | null];
  dateRangeText: string;
  selectedStaff: string | null;
  selectedStaffName: string;
  records: WineRecord[];
  currentMode: StoreWineMode;

  // 加载状态
  loading: boolean;

  // 对话框控制
  showStoreForm: boolean;
  showTakeForm: boolean;
  showDetailDialog: boolean;
  currentDetail: any | null;

  // 分页相关已移除
  total: number;
}

// UI计算属性接口
export interface IStoreWineComputed {
  filteredRecords: ComputedRef<WineRecord[]>;
}

// UI动作接口
export interface IStoreWineActions {
  onSearchChange(): void;
  selectDateRange(range: [Date, Date]): void;
  selectStaff(staffId: string, staffName: string): void;
  switchToStoreMode(): void;
  switchToTakeMode(): void;
  refreshData(): void;

  // 存酒取酒操作
  showStoreWineForm(): void;
  showTakeWineForm(): void;
  storeWine(data: any): Promise<void>;
  takeWine(data: any): Promise<void>;

  // 分页相关方法已移除

  // 记录操作
  viewDetail(recordId: string): void;
  printRecord(recordId: string): void;

  // 扩展操作 - 撤销、续存、报废
  cancelWine(id: string, operatorId: string, operatorName: string, remark?: string): Promise<void>;
  discardWine(id: string, operatorId: string, operatorName: string, remark?: string): Promise<void>;
  extendWine(id: string, expireTime: number, operatorId: string, operatorName: string, remark?: string): Promise<void>;
  addItemsToWine(id: string, orderNo: string, operatorId: string, operatorName: string, items: any[]): Promise<void>;
}

// 总的ViewModel接口
export interface IStoreWineViewModel {
  state: IStoreWineState;
  computed: IStoreWineComputed;
  actions: IStoreWineActions;
}
