/**
 * 存酒状态码常量
 */

// 存酒状态码枚举
export enum StorageStatusCode {
  // 基本状态
  STORED = 'stored', // 已存酒
  PARTIAL = 'partial', // 部分支取
  WITHDRAWN = 'withdrawn', // 已取完

  // 扩展状态
  EXTENDED = 'extended', // 已续存
  CANCELED = 'canceled', // 已撤销
  DISCARDED = 'discarded' // 已报废
}

// 存酒状态名称映射
export const STORAGE_STATUS_NAMES = {
  [StorageStatusCode.STORED]: '已存酒',
  [StorageStatusCode.PARTIAL]: '部分支取',
  [StorageStatusCode.WITHDRAWN]: '已取完',
  [StorageStatusCode.EXTENDED]: '已续存',
  [StorageStatusCode.CANCELED]: '已撤销',
  [StorageStatusCode.DISCARDED]: '已报废'
};

// 辅助函数：获取状态名称
export function getStorageStatusName(statusCode: string | undefined): string {
  return statusCode ? STORAGE_STATUS_NAMES[statusCode as StorageStatusCode] || '未知状态' : '未知状态';
}

// 可操作的状态列表（可以进行撤销、续存等操作的状态）
export const OPERABLE_STATUS_CODES = [StorageStatusCode.STORED, StorageStatusCode.PARTIAL];

// 不可操作的状态列表（完全不可进行任何操作）
export const INOPERABLE_STATUS_CODES = [StorageStatusCode.WITHDRAWN, StorageStatusCode.CANCELED, StorageStatusCode.DISCARDED];

// 不可撤销的状态列表（不能撤销，但可以续存）
export const NON_CANCELABLE_STATUS_CODES = [StorageStatusCode.WITHDRAWN, StorageStatusCode.EXTENDED, StorageStatusCode.CANCELED, StorageStatusCode.DISCARDED];
