// 交班报告实体模型
export interface ShiftReportDaily {
  billDate: string; // 账单日期
  businessData: {
    openCount: number; // 开台数
    billCount: number; // 已结账单数
    orderUnpaidCount: number; // 未结订单数
  };
  businessOverview: {
    employeeGift: number; // 员工赠送
    lowConsumptionFee: number; // 低消费用
    memberDiscount: number; // 会员折扣
    merchantDiscount: number; // 商家折扣
    netFee: number; // 净费用
    productFee: number; // 商品费用
    roomFee: number; // 房间费用
    shouldFee: number; // 应收费用
    totalFee: number; // 总费用
    zeroFee: number; // 零费用
    unpaidFee: number; // 未结费用
  };
  employeeId: string; // 员工ID
  endTime: number; // 结束时间 (10位时间戳)
  orderVOs: OrderVO[]; // 订单列表
  payBillVOs: PayBillVO[]; // 支付账单列表
  payTypeData: {
    alipay: number; // 支付宝
    bank: number; // 银行卡
    cash: number; // 现金
    koubei: number; // 口碑
    meituan: number; // 美团
    other: number; // 其他
    ticket: number; // 票券
    wechat: number; // 微信
    leshua: number; // 扫码支付
  };
  // 会员卡支付数据
  memberCardPayData: {
    principalAmount: number; // 本金支付
    roomBonusAmount: number; // 包厢额度支付
    goodsBonusAmount: number; // 商品额度支付
    commonBonusAmount: number; // 通用额度支付
  };
  // 会员卡充值数据
  memberCardRechargeData: {
    rechargeAmount: number; // 充值金额
  };
  startTime: number; // 开始时间 (10位时间戳)
  venueId: string; // 场地ID
}

// 经过聚合后的交班报告
export interface AggregatedShiftReport {
  billDate: string; // 账单日期
  businessData: {
    openCount: number; // 开台数
    billCount: number; // 已结账单数
    orderUnpaidCount: number; // 未结订单数
  };
  businessOverview: {
    employeeGift: number; // 员工赠送
    lowConsumptionFee: number; // 低消费用
    memberDiscount: number; // 会员折扣
    merchantDiscount: number; // 商家折扣
    netFee: number; // 净费用
    unpaidFee: number; // 未结费用
    productFee: number; // 商品费用
    roomFee: number; // 房间费用
    shouldFee: number; // 应收费用
    totalFee: number; // 总费用
    zeroFee: number; // 零费用
  };
  employeeId: string; // 员工ID
  startTime: number; // 开始时间 (聚合后的最小开始时间)
  endTime: number; // 结束时间 (聚合后的最大结束时间)
  orderVOs: OrderVO[]; // 订单列表
  payBillVOs: PayBillVO[]; // 支付账单列表
  payTypeData: {
    alipay: number; // 支付宝
    bank: number; // 银行卡
    cash: number; // 现金
    leshua: number; // 扫码支付
    koubei: number; // 口碑
    meituan: number; // 美团
    other: number; // 其他
    ticket: number; // 票券
    wechat: number; // 微信
  };
  // 会员卡支付数据
  memberCardPayData: {
    principalAmount: number; // 本金支付
    roomBonusAmount: number; // 包厢额度支付
    goodsBonusAmount: number; // 商品额度支付
    commonBonusAmount: number; // 通用额度支付
  };
  // 会员卡充值数据
  memberCardRechargeData: {
    rechargeAmount: number; // 充值金额
  };
  venueId: string; // 场地ID
}

// 查询交班报告请求参数
export interface QueryShiftReportDailyRequestDto {
  date: number; // 日期
  employeeId: string; // 员工ID
  venueId: string; // 场地ID
}

// 订单实体
export interface OrderVO {
  configProductMemberDiscountType: number;
  configRoomMemberDiscountType: number;
  ctime: number;
  direction: string;
  employeeId: string;
  id: string;
  mark: string;
  markType: string;
  memberId: string;
  minimumCharge: number;
  orderNo: string;
  pOrderNo: string;
  roomId: string;
  sessionId: string;
  state: number;
  status: string;
  tag: string;
  type: string;
  utime: number;
  venueId: string;
  version: number;
}

// 支付账单实体
export interface PayBillVO {
  billDate: number;
  billId: string;
  billPid: string;
  changeAmount: number;
  creditAmount: number;
  ctime: number;
  direction: string;
  discountType: number;
  employeeId: string;
  finishTime: number;
  forceMinimumCharge: boolean;
  id: string;
  info: string;
  isBack: boolean; // 是否还原 0: 正常 1: 账单还原
  isFree: boolean;
  originalFee: number;
  productDiscount: number;
  productDiscountAmount: number;
  refundWay: string;
  roomDiscount: number;
  roomDiscountAmount: number;
  roomId: string;
  roomName: string;
  sessionId: string;
  shouldFee: number;
  state: number;
  status: string;
  totalFee: number;
  utime: number;
  venueId: string;
  version: number;
  zeroFee: number;
}

// 交班报告API响应
export interface ShiftReportDailyResponse {
  attachments?: Record<string, string>;
  code: number;
  data: ShiftReportDaily[];
  message: string;
  requestID: string;
  serverTime: number;
  traceId: string;
}
