<template>
  <AppDialog v-model="dialogVisible" :title="dialogTitle" :destroy-on-close="true" class="bill-detail-dialog !w-[864px]">
    <div class="p-4">
      <!-- 左侧应收/应退信息 -->
      <div class="flex justify-between mb-6">
        <div class="w-1/2 border-r pr-4">
          <div class="flex justify-between mb-2 items-center">
            <div class="text-[16px] font-bold" :class="isRefundBill ? 'text-red-600' : ''">
              {{ isRefundBill ? '应退金额' : '应收金额' }}
            </div>
            <PriceDisplay :amountInFen="shouldFee" :class="isRefundBill ? 'text-red-600' : ''" />
          </div>
          <!-- 优惠信息列表 -->
          <!-- <div v-for="(item, index) in discountList" :key="index" class="flex justify-between text-sm mt-2">
            <div class="text-gray-500">{{ item.name }}</div>
            <PriceDisplay :amountInFen="item.amount" />
          </div> -->
        </div>

        <div class="w-1/2 pl-4">
          <div class="flex justify-between mb-2 items-center">
            <div class="text-[16px] font-bold" :class="isRefundBill ? 'text-red-600' : ''">
              {{ isRefundBill ? '实退金额' : '实收金额' }}
            </div>
            <PriceDisplay :amountInFen="totalReceived" :class="isRefundBill ? 'text-red-600' : ''" />
          </div>
          <!-- 支付方式列表 -->
          <div v-for="(item, index) in paymentList" :key="index" class="flex justify-between text-sm mt-2">
            <div class="text-gray-500">{{ item.name }}</div>
            <PriceDisplay :amountInFen="item.amount" />
          </div>
        </div>
      </div>

      <!-- 账单列表 -->
      <div class="mt-4">
        <div class="text-lg font-bold mb-4">订单信息</div>
        <el-table v-loading="loading" :data="mergedRecordList" @selection-change="handleSelectionChange" stripe class="w-full" ref="recordTableRef">
          <el-table-column type="selection" width="48px" />
          <el-table-column label="订单编号" prop="billId">
            <template #default="scope">
              <div class="flex flex-col">
                <div class="flex flex-wrap items-center">
                  <span>{{ formatBillId(scope.row.billId || '') }}</span>
                  <el-tag v-if="scope.row.direction === 'refund'" size="small" type="danger" class="ml-[4px]">退款</el-tag>
                  <el-tag v-else-if="scope.row.isBack" size="small" type="warning" class="ml-[4px]">已还原</el-tag>
                  <el-tag v-if="scope.row.isFree" size="small" type="info" class="ml-[4px]">赠送</el-tag>
                </div>
                <div v-if="scope.row.billPid" class="text-[12px] text-gray-500 mt-1">关联: {{ formatBillId(scope.row.billPid) }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="employeeName" width="100" />
          <el-table-column label="金额" prop="totalFee" align="right" max-width="280">
            <template #default="scope">
              <!-- 退款记录只显示退款金额 -->
              <div v-if="scope.row.direction === 'refund'" class="flex justify-between items-center">
                <span class="text-gray-500">已退：</span>
                <span class="text-red-500 font-bold">¥ {{ (scope.row.totalFee || 0) / 100 }}</span>
              </div>

              <!-- 正常支付记录显示详细信息 -->
              <div v-else class="flex flex-col gap-1">
                <div class="flex justify-between">
                  <span class="text-gray-500">应收：</span>
                  <span>¥ {{ (scope.row.shouldFee || 0) / 100 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">实收：</span>
                  <span>¥ {{ (scope.row.totalFee || 0) / 100 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">优惠金额：</span>
                  <span class="text-red-500">-¥ {{ Math.max((scope.row.shouldFee || 0) - (scope.row.totalFee || 0), 0) / 100 }}</span>
                </div>
                <div class="border-t border-gray-200 pt-1 mt-1">
                  <!-- 显示多个支付方式 -->
                  <div v-for="(payment, idx) in scope.row.paymentMethods" :key="idx" class="flex justify-between mt-1">
                    <span class="text-gray-500">{{ getPayTypeText(payment.payType || '') }}：</span>
                    <span class="font-bold">¥ {{ (payment.amount || 0) / 100 }}</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center w-full">
        <button class="btn-default" :disabled="selectedRecords.length === 0" @click="handlePrint">账单打印</button>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { AppDialog } from '@/components/Dialog';
import { useOrderApi } from '@/modules/order/api/index';
import { formatDateTime } from '@/utils/dateUtils';
import type { BaseResponse } from '@/types/baseResponse';
import { PayBillVO, PayRecordVO } from '@/api/autoGenerated/shared/types';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { payTypeMap, PayType } from '@/utils/constant/payTyps';
import type { ElTable } from 'element-plus';
import { DialogManager } from '@/utils/dialog';

// 表格引用
const recordTableRef = ref<InstanceType<typeof ElTable>>();

// 接收props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  sessionId: {
    type: String,
    required: true
  },
  billId: {
    type: String,
    default: ''
  }
});

// 定义emit
const emit = defineEmits(['update:modelValue', 'print', 'error', 'close']);

// 组件状态
const loading = ref(false);
const billList = ref<PayBillVO[]>([]);
const payRecordList = ref<PayRecordVO[]>([]);
const selectedRecords = ref<any[]>([]);
const isInitialized = ref(false);
const employeeMap = ref<Record<string, string>>({});

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue || props.visible,
  set: val => {
    emit('update:modelValue', val);
  }
});

// API服务
const { billApi } = useOrderApi();

// 获取账单数据 - 简化版，仅获取指定billId的数据
const fetchBillData = async () => {
  if (!props.sessionId) return;

  loading.value = true;
  try {
    const response = await billApi.queryBillView({ sessionId: props.sessionId });
    if (response.code === 0 && response.data) {
      // 获取所有账单和支付记录
      const allBills = response.data.payBills || [];
      const allRecords = response.data.payRecords || [];

      // 如果指定了billId，只获取该billId的数据
      if (props.billId) {
        // 只获取指定billId的账单
        billList.value = allBills.filter(bill => bill.billId === props.billId);
        // 只获取指定billId的支付记录
        payRecordList.value = allRecords.filter(record => record.billId === props.billId);
      } else {
        // 不指定billId时显示所有
        billList.value = allBills;
        payRecordList.value = allRecords;
      }

      console.log(`[BillDetailDialog] 过滤后账单数: ${billList.value.length}, 支付记录数: ${payRecordList.value.length}`);

      // 构建员工映射
      const responseData = response.data as any;
      const employees = responseData.employeeVOs || [];
      employees.forEach((employee: any) => {
        if (employee && employee.id) {
          employeeMap.value[employee.id] = employee.name || '';
        }
      });

      console.log('[BillDetailDialog] 已获取账单数据:', {
        bills: billList.value,
        records: payRecordList.value,
        employees: employeeMap.value
      });

      // 默认选中全部记录，需等待表格渲染完成后再操作
      nextTick(() => {
        if (recordTableRef.value) {
          try {
            // 清理旧选择，避免重复
            recordTableRef.value.clearSelection();
            // 全选
            recordTableRef.value.toggleAllSelection();
          } catch (e) {
            console.warn('[BillDetailDialog] 默认全选失败:', e);
          }
        }
      });
    } else {
      console.error('获取账单数据失败:', response.message);
      emit('error', new Error(response.message || '获取账单数据失败'));
    }
  } catch (error) {
    console.error('获取账单数据异常:', error);
    emit('error', error);
  } finally {
    loading.value = false;
  }
};

// 监听参数变化
watch(
  () => [dialogVisible.value, props.sessionId],
  async ([visible, sessionId]) => {
    console.log('[BillDetailDialog] 监听参数变化:', {
      visible,
      sessionId
    });

    if (visible && !isInitialized.value && sessionId) {
      try {
        isInitialized.value = true;
        await fetchBillData();
      } catch (error) {
        console.error('初始化账单数据失败:', error);
        emit('error', error);
      }
    }

    if (!visible) {
      // 关闭对话框时重置状态
      selectedRecords.value = [];
      isInitialized.value = false;
    }
  },
  { immediate: true }
);

// 格式化单号
const formatBillId = (billId: string) => {
  if (!billId) return '';
  return billId.length > 12 ? `${billId.substring(0, 6)}***${billId.substring(billId.length - 6)}` : billId;
};

// 构建合并的记录列表 - 简化版，直接显示当前账单的信息
const mergedRecordList = computed(() => {
  if (billList.value.length === 0) return [];

  // 直接基于当前账单和其支付记录构建显示数据
  return billList.value.map((bill: PayBillVO) => {
    // 获取该账单的支付记录
    const payments = payRecordList.value.filter(record => record.billId === bill.billId);

    // 构建支付方式列表
    const paymentMethods = payments.map((payment: PayRecordVO) => ({
      payType: payment.payType || '',
      amount: payment.totalFee || 0,
      payId: payment.payId || '',
      id: payment.id || ''
    }));

    return {
      ...bill,
      id: bill.id || '',
      employeeName: employeeMap.value[bill.employeeId || ''] || bill.employeeId || '',
      // 支付记录信息
      paymentMethods: paymentMethods,
      // 保留原始属性
      isBack: bill.isBack || false
    };
  });
});

// 判断是否为退款账单
const isRefundBill = computed(() => {
  if (billList.value.length === 0) return false;
  const bill = billList.value[0];
  return bill.direction === 'refund';
});

// 动态弹窗标题
const dialogTitle = computed(() => {
  if (billList.value.length === 0) return '账单详情';
  const bill = billList.value[0];

  if (bill.direction === 'refund') {
    return bill.isBack ? '账单详情 - 退款已还原' : '账单详情 - 退款';
  } else {
    return bill.isBack ? '账单详情 - 已还原' : '账单详情';
  }
});

// 计算应收金额 - 简化版，直接取当前账单的应收
const shouldFee = computed(() => {
  if (billList.value.length === 0) return 0;

  // 直接返回第一个账单的应收金额（因为现在只查询一个billId）
  const bill = billList.value[0];
  const amount = bill.shouldFee || 0;

  console.log(`[BillDetailDialog] 应收金额: ${amount / 100}元`);
  return amount;
});

// 计算总实收金额 - 简化版，直接取当前账单的实收
const totalReceived = computed(() => {
  if (billList.value.length === 0) return 0;

  // 直接返回第一个账单的实收金额（因为现在只查询一个billId）
  const bill = billList.value[0];
  const amount = bill.totalFee || 0;

  console.log(`[BillDetailDialog] 实收金额: ${amount / 100}元`);
  return amount;
});

// 删除不需要的复杂计算属性

// 支付方式列表 - 简化版，直接显示当前账单的支付方式
const paymentList = computed(() => {
  if (payRecordList.value.length === 0) {
    return [];
  }

  // 直接基于支付记录构建支付方式列表
  return payRecordList.value
    .map((record: PayRecordVO) => ({
      name: getPayTypeText(record.payType || ''),
      amount: record.totalFee || 0,
      isRefund: false // 简化版不需要区分退款状态
    }))
    .filter(item => item.amount > 0);
});

// 处理选择变化 - 简化版
const handleSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection;
};

// 打印处理
const handlePrint = () => {
  if (selectedRecords.value.length === 0) return;
  const selectedBillIds = selectedRecords.value.map(record => record.billId).filter(Boolean);
  emit('print', selectedBillIds);
};

// 格式化日期
const formatDate = (timestamp: number) => {
  return formatDateTime(timestamp.toString());
};

// 获取支付方式文本
const getPayTypeText = (payType: string) => {
  return payTypeMap[payType as PayType] || payType;
};
</script>

<style scoped>
.bill-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}
</style>
