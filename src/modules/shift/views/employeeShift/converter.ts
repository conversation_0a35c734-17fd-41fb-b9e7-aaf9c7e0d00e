import { AggregatedShiftReport, ShiftReportDaily } from '../../entity/EmployeeShiftEntity';
import { formatUnixTimestamp } from '@/utils/dateUtils';

/**
 * 交班报告数据转换器
 */
export class EmployeeShiftConverter {
  /**
   * 将API返回的多条交班报告聚合为单个报告
   * @param reports 原始交班报告数组
   * @returns 聚合后的交班报告
   */
  static aggregateShiftReports(reports: ShiftReportDaily[]): AggregatedShiftReport | null {
    if (!reports || reports.length === 0) {
      return null;
    }
    console.log('[converter aggregateShiftReports] reports:', reports);
    console.log('[converter] 恢复账单合并 - 显示所有交班报告的账单');
    // 如果只有一条数据，直接返回并填充可能缺失的字段
    if (reports.length === 1) {
      // 确保venueId和employeeId不为空
      const report = reports[0];
      return {
        ...report,
        venueId: report.venueId || '',
        employeeId: report.employeeId || '',
        // 确保startTime和endTime有值
        startTime: report.startTime || 0,
        endTime: report.endTime || 0,
        // 确保businessOverview.unpaidFee有值
        businessOverview: {
          ...report.businessOverview,
          unpaidFee: report.businessOverview.unpaidFee || 0
        },
        // 确保memberCardPayData有值
        memberCardPayData: report.memberCardPayData || {
          principalAmount: 0,
          roomBonusAmount: 0,
          goodsBonusAmount: 0,
          commonBonusAmount: 0
        },
        // 确保memberCardRechargeData有值
        memberCardRechargeData: report.memberCardRechargeData || {
          rechargeAmount: 0
        },
        // 直接使用当前报告的账单数据（单报告情况）
        orderVOs: report.orderVOs || [],
        payBillVOs: report.payBillVOs || []
      };
    }

    // 查找有效的employeeId和venueId
    const validEmployeeId = reports.find(r => r.employeeId)?.employeeId || '';
    const validVenueId = reports.find(r => r.venueId)?.venueId || '';

    // 查找有效的开始时间和结束时间
    const validStartTimes = reports.filter(r => r.startTime).map(r => r.startTime);
    const validEndTimes = reports.filter(r => r.endTime).map(r => r.endTime);

    // 初始化聚合报告，使用第一条数据作为基础，但不包含会被累加的数值字段
    const result: AggregatedShiftReport = {
      ...reports[0],
      // 使用有效ID
      employeeId: validEmployeeId,
      venueId: validVenueId,
      // 聚合后取各报告的开始时间最小值(如果没有，则默认为0)
      startTime: validStartTimes.length > 0 ? Math.min(...validStartTimes) : 0,
      // 聚合后取各报告的结束时间最大值(如果没有，则默认为0)
      endTime: validEndTimes.length > 0 ? Math.max(...validEndTimes) : 0,
      // 初始化业务概览数据为0
      businessOverview: {
        shouldFee: 0,
        totalFee: 0,
        netFee: 0,
        roomFee: 0,
        productFee: 0,
        merchantDiscount: 0,
        memberDiscount: 0,
        lowConsumptionFee: 0,
        employeeGift: 0,
        zeroFee: 0,
        unpaidFee: 0
      },
      // 初始化业务数据为0
      businessData: {
        openCount: 0,
        orderUnpaidCount: 0,
        billCount: 0
      },
      // 初始化支付方式数据为0
      payTypeData: {
        alipay: 0,
        bank: 0,
        cash: 0,
        koubei: 0,
        meituan: 0,
        other: 0,
        ticket: 0,
        wechat: 0,
        leshua: 0
      },
      // 初始化会员卡支付数据
      memberCardPayData: {
        principalAmount: 0,
        roomBonusAmount: 0,
        goodsBonusAmount: 0,
        commonBonusAmount: 0
      },
      // 初始化会员卡充值数据
      memberCardRechargeData: {
        rechargeAmount: 0
      },
      // 合并所有报告的账单数据，但保持数据独立性
      orderVOs: [],
      payBillVOs: []
    };
    console.log('[converter aggregateShiftReports] result:', result);
    // 聚合数据
    reports.forEach(report => {
      // 聚合业务数据
      result.businessData.openCount += report.businessData.openCount;
      result.businessData.billCount += report.businessData.billCount;
      result.businessData.orderUnpaidCount += report.businessData.orderUnpaidCount;

      // 聚合业务概览 (处理负数)
      result.businessOverview.employeeGift += report.businessOverview.employeeGift || 0;
      result.businessOverview.lowConsumptionFee += report.businessOverview.lowConsumptionFee || 0;
      result.businessOverview.memberDiscount += report.businessOverview.memberDiscount || 0;
      result.businessOverview.merchantDiscount += report.businessOverview.merchantDiscount || 0;
      result.businessOverview.netFee += report.businessOverview.netFee || 0;
      result.businessOverview.productFee += report.businessOverview.productFee || 0;
      result.businessOverview.roomFee += report.businessOverview.roomFee || 0;
      result.businessOverview.shouldFee += report.businessOverview.shouldFee || 0;
      result.businessOverview.unpaidFee += report.businessOverview.unpaidFee || 0;

      // 聚合totalFee (保持原始符号)
      result.businessOverview.totalFee += report.businessOverview.totalFee || 0;

      result.businessOverview.zeroFee += report.businessOverview.zeroFee || 0;

      // 聚合支付方式数据
      result.payTypeData.alipay += report.payTypeData.alipay || 0;
      result.payTypeData.bank += report.payTypeData.bank || 0;
      result.payTypeData.cash += report.payTypeData.cash || 0;
      result.payTypeData.koubei += report.payTypeData.koubei || 0;
      result.payTypeData.meituan += report.payTypeData.meituan || 0;
      result.payTypeData.other += report.payTypeData.other || 0;
      result.payTypeData.ticket += report.payTypeData.ticket || 0;
      result.payTypeData.wechat += report.payTypeData.wechat || 0;
      result.payTypeData.leshua += report.payTypeData.leshua || 0;

      // 聚合会员卡支付数据
      if (report.memberCardPayData) {
        result.memberCardPayData.principalAmount += report.memberCardPayData.principalAmount || 0;
        result.memberCardPayData.roomBonusAmount += report.memberCardPayData.roomBonusAmount || 0;
        result.memberCardPayData.goodsBonusAmount += report.memberCardPayData.goodsBonusAmount || 0;
        result.memberCardPayData.commonBonusAmount += report.memberCardPayData.commonBonusAmount || 0;
      }

      // 聚合会员卡充值数据
      if (report.memberCardRechargeData) {
        result.memberCardRechargeData.rechargeAmount += report.memberCardRechargeData.rechargeAmount || 0;
      }

      // 合并所有报告的账单数据
      if (report.orderVOs) {
        result.orderVOs = [...result.orderVOs, ...report.orderVOs];
      }
      if (report.payBillVOs) {
        result.payBillVOs = [...result.payBillVOs, ...report.payBillVOs];
      }
    });

    console.log('[aggregateShiftReports] 聚合后的报告数据:', result);
    console.log('[converter] 账单数据数量 - orderVOs:', result.orderVOs?.length || 0, 'payBillVOs:', result.payBillVOs?.length || 0);
    return result;
  }

  /**
   * 格式化交班时间范围
   * @param startTime 开始时间戳
   * @param endTime 结束时间戳
   * @returns 格式化后的时间范围字符串
   */
  static formatShiftTimeRange(startTime: number, endTime: number): string {
    // 如果时间戳无效，返回空字符串
    if (!startTime || !endTime) {
      return '';
    }

    // 转换为标准格式 YYYY-MM-DD HH:mm:ss
    const startTimeStr = formatUnixTimestamp(startTime);
    const endTimeStr = formatUnixTimestamp(endTime);
    return `${startTimeStr} - ${endTimeStr}`;
  }

  /**
   * 计算线上收入（支付宝+微信+美团+口碑）
   * @param payTypeData 支付类型数据
   * @returns 线上收入总额
   */
  static calculateOnlineIncome(payTypeData: AggregatedShiftReport['payTypeData']): number {
    return (payTypeData.alipay || 0) + (payTypeData.wechat || 0) + (payTypeData.meituan || 0) + (payTypeData.koubei || 0);
  }

  /**
   * 计算总折扣（会员折扣+商家折扣）
   * @param businessOverview 业务概览数据
   * @returns 总折扣金额
   */
  static calculateTotalDiscount(businessOverview: AggregatedShiftReport['businessOverview']): number {
    return (businessOverview.memberDiscount || 0) + (businessOverview.merchantDiscount || 0);
  }

  /**
   * 计算会员支付总额
   * @param memberCardPayData 会员卡支付数据
   * @returns 会员支付总额
   */
  static calculateTotalMemberPayment(memberCardPayData: AggregatedShiftReport['memberCardPayData']): number {
    return (
      (memberCardPayData.principalAmount || 0) +
      (memberCardPayData.roomBonusAmount || 0) +
      (memberCardPayData.goodsBonusAmount || 0) +
      (memberCardPayData.commonBonusAmount || 0)
    );
  }
}
