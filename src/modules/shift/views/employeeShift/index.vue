<template>
  <div class="employee-shift-container h-full flex flex-col bg-gray-100">
    <!-- 导航栏 -->
    <nav class="p-4 bg-white border-b flex flex-row justify-between items-center">
      <div class="flex justify-between items-center">
        <!-- Tab切换区域 -->
        <div class="flex items-center">
          <!-- 根据模式显示不同的内容 -->
          <div v-if="isHistoryMode" class="flex items-center cursor-pointer" @click="handleGoBack">
            <el-icon class="text-xl mr-2"><ArrowLeft /></el-icon>
            <span class="text-xl">返回</span>
          </div>
          <!-- 正常模式显示tab -->
          <div v-else class="flex bg-[#F3F3F3] rounded-[10px] h-[68px] items-center p-[8px] gap-[8px]">
            <div
              class="flex-1 h-[52px] w-[104px] font-medium rounded-[10px] flex items-center justify-center transition-all duration-200 cursor-pointer"
              :class="activeTab === 'shift' ? 'bg-btn-focus text-white' : 'text-gray-500'"
              @click="handleTabClick('shift')">
              交班
            </div>
            <div
              class="flex-1 h-[52px] w-[104px] font-medium rounded-[10px] flex items-center justify-center transition-all duration-200 cursor-pointer"
              :class="activeTab === 'history' ? 'bg-btn-focus text-white' : 'text-gray-500'"
              @click="handleTabClick('history')">
              交班历史
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="flex flex-row justify-between items-center header-right">
        <!-- 员工显示 -->
        <div class="flex items-center text-[16px]">
          <span class="text-gray-600">员工:</span>
          <div class="text-gray-800 ml-[4px]">{{ employeeName }}</div>
        </div>

        <!-- 时间范围显示 -->
        <div class="flex items-center ml-[16px] text-[16px]">
          <span class="text-gray-600 mr-2">{{ isHistoryMode ? '交班时间:' : '时间:' }}</span>
          <div class="text-gray-800">{{ shiftTimeRange }}</div>
        </div>

        <!-- 历史模式下显示打印按钮，正常模式下显示交班历史和交班按钮 -->
        <template v-if="isHistoryMode">
          <button class="ml-[16px] btn-light-gray" @click="handlePrint">打印交班单</button>
        </template>
        <template v-else>
          <!-- 交班按钮 -->
          <el-button class="ml-[16px]" type="normal" @click="handleShift">交班</el-button>
        </template>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="flex-grow p-[16px] overflow-hidden">
      <!-- 当没有数据时显示空状态 -->
      <el-empty v-if="!vm.state.loading && !vm.computed.hasData.value" description="暂无交班数据" class="flex-grow flex items-center justify-center" />

      <!-- 加载状态 -->
      <div v-if="vm.state.loading" class="flex-grow flex items-center justify-center">
        <el-skeleton :rows="10" animated />
      </div>

      <!-- 主要内容区域 -->
      <div v-if="!vm.state.loading && vm.computed.hasData.value" class="flex h-full flex-row gap-4">
        <!-- 左侧栏 -->
        <div class="w-[492px] flex flex-col overflow-hidden gap-[24px] bg-white rounded-lg shadow-sm">
          <!-- 营业概况卡片 -->
          <div class="px-[46px] py-[24px] flex-shrink-0 h-[624px]">
            <h3 class="text-gray-700 text-lg font-medium mb-4">营业概况</h3>

            <!-- 半圆形进度图和实收数据 -->
            <div class="relative flex justify-center mb-[24px]">
              <semicircle-progress-bar :width="360" :height="180" :current-value="vm.computed.totalIncome.value" :target-value="shouldFee" title="营业实收">
                <template #center-content>
                  <!--  -->
                  <div class="flex items-center mt-[48px] big-text">
                    <PriceDisplay :show-thousand-separator="true" :amount-in-fen="vm.computed.totalIncome.value" class="mt-[40px] price-display-56" />
                  </div>
                  <div class="flex items-center mt-[16px]">
                    <span class="w-[52px] text-[14px]">应收:</span>
                    <PriceDisplay :show-thousand-separator="true" :amount-in-fen="shouldFee" class="price-display-right-align" />
                  </div>
                </template>
              </semicircle-progress-bar>
            </div>

            <!-- 营业数据明细 -->
            <div class="mt-[48px] grid grid-cols-2 gap-x-[32px] gap-y-3">
              <div class="flex justify-between items-center">
                <span class="label-width">营业应收:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="shouldFee" class="price-display-small bold" />
              </div>
              <div class="flex justify-between items-center">
                <span class="label-width">商家优惠:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="merchantDiscount" class="price-display-small bold" />
              </div>
              <div class="flex justify-between items-center">
                <span class="label-width">营业实收:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="totalFee" class="price-display-small bold" />
              </div>
              <div class="flex justify-between items-center">
                <span class="label-width">员工赠送:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="employeeGift" class="price-display-small bold" />
              </div>
              <div class="flex justify-between items-center">
                <span class="label-width">包厢收入:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="roomFee" class="price-display-small bold" />
              </div>
              <div class="flex justify-between items-center">
                <span class="label-width">会员优惠:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="memberDiscount" class="price-display-small bold" />
              </div>

              <div class="flex justify-between items-center">
                <span class="label-width">商品收入:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="productFee" class="price-display-small bold" />
              </div>
              <div class="flex justify-between items-center">
                <span class="label-width">抹零:</span>
                <PriceDisplay :show-thousand-separator="true" :amount-in-fen="zeroFee" class="price-display-small bold" />
              </div>
            </div>

            <!-- 待结总额 -->
            <div class="mt-[48px] pt-4 flex justify-between">
              <span class="label-width">待结总额:</span>
              <PriceDisplay :show-thousand-separator="true" :amount-in-fen="unpaidFee" class="price-display-normal price-display-red bold" />
            </div>
          </div>
          <div class="h-[1px] bg-gray-200 mx-[46px]"></div>
          <!-- 营业数据统计卡片 -->
          <div class="px-[46px] flex-shrink-0 flex-1 flex flex-row justify-between items-center">
            <div class="flex flex-col items-center gap-[16px]">
              <div class="label-width">总开台数</div>
              <div class="text-[40px] font-bold">{{ vm.computed.tableCount.value }}</div>
            </div>
            <div class="flex flex-col items-center gap-[16px]">
              <div class="label-width">已结账单数</div>
              <div class="text-[40px] font-bold">{{ vm.computed.paidBillCount.value }}</div>
            </div>
            <div class="flex flex-col items-center gap-[16px]">
              <div class="label-width">待结订单数</div>
              <div class="text-[40px] font-bold mt-[16px]">{{ vm.computed.unpaidOrderCount.value }}</div>
            </div>
          </div>
        </div>

        <!-- 中间栏 -->
        <div class="w-[500px] h-full flex flex-col justify-between overflow-hidden gap-[24px]">
          <!-- 普通支付统计卡片 -->
          <div
            class="bg-white rounded-lg shadow-sm p-[24px] flex flex-col min-h-0 payment-card-normal"
            :style="{ height: paymentCardHeight + 'px', minHeight: '200px' }">
            <div class="flex justify-between items-center h-[48px] flex-shrink-0">
              <h3 class="text-gray-700 text-lg font-medium">普通支付:</h3>
              <div class="font-medium">
                <PriceDisplay :amount-in-fen="vm.computed.totalNormalPayment.value" :show-thousand-separator="true" class="price-display-right-align bold" />
              </div>
            </div>

            <!-- 支付方式柱状图 - 使用垂直柱状图 -->
            <div class="flex justify-between items-end flex-1 min-h-0 overflow-hidden" :style="{ height: paymentChartHeight + 'px', minHeight: '120px' }">
              <!-- 动态生成普通支付类型图表 -->
              <div v-for="(value, key) in vm.computed.normalPaymentItems.value" :key="`normal-${key}`" class="flex flex-col items-center justify-end h-full">
                <div class="mb-2 text-sm">
                  {{ formatPaymentAmount(value) }}
                </div>
                <div
                  class="w-[32px] flex-shrink-0 bg-normal-payment"
                  :style="`height: ${getPaymentBarHeight(value, vm.computed.maxNormalPayment.value)}px`"
                  style="border-top-left-radius: 0.125rem; border-top-right-radius: 0.125rem"></div>
                <div class="text-gray-700 mt-1 text-xs flex-shrink-0" style="white-space: nowrap">{{ getPaymentName(key) }}</div>
              </div>
            </div>
          </div>

          <!-- 会员支付统计卡片 -->
          <div
            class="bg-white rounded-lg shadow-sm p-[24px] flex flex-col min-h-0 payment-card-member"
            :style="{ height: paymentCardHeight + 'px', minHeight: '200px' }">
            <div class="flex justify-between items-center h-[48px] flex-shrink-0">
              <h3 class="text-gray-700 text-lg font-medium">会员支付:</h3>
              <div class="font-medium">
                <PriceDisplay
                  :amount-in-fen="vm.computed.totalMemberPayment.value"
                  :show-thousand-separator="true"
                  class="price-display-right-align price-display-right-align bold" />
              </div>
            </div>

            <!-- 会员支付方式柱状图 - 使用垂直柱状图 -->
            <div class="flex justify-between items-end flex-1 min-h-0 overflow-hidden" :style="{ height: paymentChartHeight + 'px', minHeight: '120px' }">
              <!-- 动态生成会员支付类型图表 -->
              <div v-for="(item, index) in vm.computed.memberPaymentItems.value" :key="`member-${index}`" class="flex flex-col items-center justify-end h-full">
                <div class="mb-2 text-sm">
                  {{ formatPaymentAmount(item.value) }}
                </div>
                <div
                  class="w-[32px] bg-member-payment rounded-t-sm flex-shrink-0"
                  :style="`height: ${getPaymentBarHeight(item.value, vm.computed.maxMemberPayment.value)}px`"></div>
                <div class="text-gray-700 mt-1 text-xs flex-shrink-0">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧账单列表区域 -->
        <div class="w-[708px] bg-white rounded-lg shadow-sm flex flex-col overflow-hidden">
          <div class="p-5 flex-shrink-0">
            <h3 class="text-gray-700 text-lg font-medium mb-4">账单列表</h3>
          </div>

          <div class="flex-1 overflow-auto px-5 pb-5">
            <el-table class="table-no-padding bill-table-clickable" :data="billList" stripe type="small" @row-click="vm.actions.checkBill">
              <el-table-column prop="id" label="单号" align="center">
                <template #default="scope">
                  <div class="flex flex-col items-center">
                    <span class="text-[14px] font-medium">{{ getBillDisplayText(scope.row) }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="包厢" align="center">
                <template #default="scope">
                  <span class="text-[14px]">{{ scope.row.room }}</span>
                </template>
              </el-table-column>
              <el-table-column label="日期" align="center">
                <template #default="scope">
                  <div class="flex flex-col text-[14px]">
                    <span>{{ scope.row.date }}</span>
                    <span>{{ scope.row.time }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="实收金额" align="right">
                <template #default="scope">
                  <div class="flex flex-col">
                    <PriceDisplay
                      :amount-in-fen="getDisplayAmount(scope.row)"
                      :show-thousand-separator="true"
                      :class="getAmountDisplayClass(scope.row)"
                      class="price-display-right-align price-display-small bold" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="80">
                <template #default="scope">
                  <el-button type="default" size="small" @click.stop="vm.actions.checkBill(scope.row)"> 查看 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineExpose, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { ArrowLeft } from '@element-plus/icons-vue';
import { useEmployeeShift } from './presenter';
import type { IEmployeeShiftViewModel } from './viewmodel';
import { convertToYuan } from '@/utils/priceUtils';
import { useUserStore } from '@/stores/userStore';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import SemicircleProgressBar from '@/components/common/SemicircleProgressBar.vue';

// 定义组件接收的props
const props = defineProps({
  shiftId: {
    type: String,
    default: ''
  },
  handNo: {
    type: String,
    default: ''
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'normal', // 'normal' 正常交班模式, 'history' 历史查看模式
    validator: (value: string) => ['normal', 'history'].includes(value)
  }
});

// 获取ViewModel，传入shiftId和handNo
const vm: IEmployeeShiftViewModel = useEmployeeShift(props.shiftId, props.handNo);

// 路由和用户信息
const router = useRouter();
const userStore = useUserStore();

// 窗口高度响应式变量
const windowHeight = ref(window.innerHeight);

// 监听窗口大小变化
const handleResize = () => {
  windowHeight.value = window.innerHeight;
};

// 使用window事件监听器而不是生命周期钩子
if (typeof window !== 'undefined') {
  window.addEventListener('resize', handleResize);
}

// 计算支付卡片的动态高度
const paymentCardHeight = computed(() => {
  // 基础高度计算
  const navHeight = 100; // 导航栏高度
  const mainPadding = 32; // 主容器padding (16px * 2)
  const cardsGap = 0; // 两个支付卡片之间的gap
  const minCardHeight = 200; // 最小卡片高度

  // 计算实际可用高度 - 考虑左侧栏的布局
  // 左侧栏有两个卡片：营业概况(624px固定) + 营业数据(flex-1) + gap(24px)
  // 中间栏应该与左侧栏总高度保持一致
  const leftColumnFixedHeight = 624; // 营业概况卡片固定高度
  const leftColumnGap = 24; // 左侧两个卡片之间的gap

  // 总可用高度 = 窗口高度 - 导航栏 - 主容器padding
  const totalAvailableHeight = windowHeight.value - navHeight - mainPadding;

  // 中间栏可用高度应该等于总可用高度（与左侧栏保持一致）
  const middleColumnHeight = totalAvailableHeight;

  // 中间栏内两个支付卡片平均分配，减去gap
  const cardHeight = Math.floor((middleColumnHeight - cardsGap) / 2);
  console.log('[paymentCardHeight] cardHeight:', cardHeight, 'middleColumnHeight:', middleColumnHeight, 'cardsGap:', cardsGap);
  // 确保最小高度
  return Math.max(minCardHeight, cardHeight);
});

// 计算支付图表的高度
const paymentChartHeight = computed(() => {
  // 支付卡片高度 - 标题区域高度(48px) - 内边距(48px) - 预留空间
  const titleHeight = 48;
  const cardPadding = 48;
  const reservedSpace = 30; // 增加预留空间，为文字留出更多空间
  const minChartHeight = 100; // 最小图表高度

  const chartHeight = paymentCardHeight.value - titleHeight - cardPadding - reservedSpace;

  return Math.max(minChartHeight, chartHeight);
});

// 格式化支付金额显示（分转元）
const formatPaymentAmount = (amountInFen: number) => {
  if (amountInFen === 0 || amountInFen === null || amountInFen === undefined) return '0';
  return (amountInFen / 100).toFixed(1);
};

// 计算属性用于避免模板中的可选链解析问题
const businessOverview = computed(() => vm.state.aggregatedShiftReport?.businessOverview || {});

// 各种费用的计算属性 - 修复TypeScript类型问题
const shouldFee = computed(() => (businessOverview.value as any)?.shouldFee || 0);
const merchantDiscount = computed(() => (businessOverview.value as any)?.merchantDiscount || 0);
const totalFee = computed(() => (businessOverview.value as any)?.totalFee || 0);
const employeeGift = computed(() => (businessOverview.value as any)?.employeeGift || 0);
const roomFee = computed(() => (businessOverview.value as any)?.roomFee || 0);
const memberDiscount = computed(() => (businessOverview.value as any)?.memberDiscount || 0);
const productFee = computed(() => (businessOverview.value as any)?.productFee || 0);
const zeroFee = computed(() => (businessOverview.value as any)?.zeroFee || 0);
const unpaidFee = computed(() => (businessOverview.value as any)?.unpaidFee || 0);

// Tab相关状态
const activeTab = computed(() => 'shift');

// 处理tab点击
const handleTabClick = (tab: string) => {
  if (tab === 'history') {
    router.push({
      path: '/shift/history',
      query: { from: 'tab-switch' }
    });
  } else if (tab === 'shift') {
    // 已经在交班页面，不需要跳转
    return;
  }
};

// 检查是否为历史查看模式 - 简化逻辑
const isHistoryMode = computed(() => props.mode === 'history');

// 员工名称
const employeeName = computed(() => userStore.userInfo.employee.name || '');

// 时间范围显示
const shiftTimeRange = computed(() => {
  if (!vm.state.aggregatedShiftReport?.payBillVOs || vm.state.aggregatedShiftReport.payBillVOs.length === 0) {
    return '暂无数据';
  }

  // 计算最小和最大账单日期
  let minTime = Number.MAX_SAFE_INTEGER;
  let maxTime = 0;

  // 遍历所有账单获取最早和最晚的时间戳
  vm.state.aggregatedShiftReport.payBillVOs.forEach(bill => {
    const billTime = bill.finishTime || bill.ctime;
    if (billTime && billTime > 0) {
      minTime = Math.min(minTime, billTime);
      maxTime = Math.max(maxTime, billTime);
    }
  });

  // 确保找到了有效的时间范围
  if (minTime === Number.MAX_SAFE_INTEGER || maxTime === 0) {
    return '暂无数据';
  }

  const startDate = new Date(minTime * 1000);
  const endDate = new Date(maxTime * 1000);

  // 格式化日期 YYYY-MM-DD
  const formatDate = (date: Date) => {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  };

  // 如果是同一天，只显示一个日期
  if (formatDate(startDate) === formatDate(endDate)) {
    return formatDate(startDate);
  }

  // 否则显示日期范围
  return `${formatDate(startDate)} 至 ${formatDate(endDate)}`;
});

// 数字格式化函数
const formatNumber = (num: number) => {
  if (num === 0) return 0;
  if (num === null || num === undefined) return 0;
  return num.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
};

// 获取支付方式名称
const getPaymentName = (key: string) => {
  const nameMap: Record<string, string> = {
    cash: '现金',
    bank: '银行卡',
    wechat: '微信',
    alipay: '支付宝',
    meituan: '美团',
    koubei: '口碑',
    ticket: '票券',
    leshua: '扫码支付',
    other: '其他'
  };
  return nameMap[key] || key;
};

// 获取支付方式颜色类
const getPaymentBarColorClass = (key: string) => {
  const colorMap: Record<string, string> = {
    cash: 'bg-blue-500',
    bank: 'bg-blue-500',
    wechat: 'bg-green-500',
    alipay: 'bg-blue-400',
    leshua: 'bg-green-400',
    meituan: 'bg-green-400',
    koubei: 'bg-red-400',
    ticket: 'bg-orange-400',
    other: 'bg-gray-400'
  };
  return colorMap[key] || 'bg-gray-400';
};

// 柱状图高度计算函数 - 优化版
const getPaymentBarHeight = (value: number, maxValue: number) => {
  // 如果值为负数或0，不显示柱子
  if (value <= 0) return 0;

  // 确保maxValue有值
  if (!maxValue || maxValue <= 0) {
    maxValue = 1;
  }

  // 动态计算最大柱子高度，基于可用图表高度
  const maxBarHeight = Math.max(80, paymentChartHeight.value * 0.8); // 占图表高度的80%
  const minBarHeight = 8; // 最小高度

  // 计算比例高度
  const ratio = value / maxValue;
  const calculatedHeight = ratio * maxBarHeight;

  // 确保在最小和最大高度之间
  return Math.max(minBarHeight, Math.min(calculatedHeight, maxBarHeight));
};

// 账单列表 - 简化版，直接显示所有账单，不做合并
const billList = computed(() => {
  if (!vm.state.aggregatedShiftReport?.payBillVOs) return [];

  // 直接处理所有账单，按sessionId分组并排序
  const processedBills = vm.state.aggregatedShiftReport.payBillVOs.map(bill => {
    const date = new Date((bill.finishTime || bill.ctime) * 1000);
    return {
      id: bill.billId,
      room: bill.roomName,
      sessionId: bill.sessionId,
      timestamp: bill.finishTime || bill.ctime,
      amount: bill.totalFee, // 确保使用 totalFee
      roomId: bill.roomId,
      status: bill.status,
      direction: bill.direction,
      isBack: bill.isBack,
      billPid: bill.billPid,
      date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`,
      time: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`,
      // 保持原始数据
      originalBill: bill
    };
  });

  // 按sessionId分组
  const groupedBySession = new Map<string, typeof processedBills>();

  processedBills.forEach(bill => {
    const sessionId = bill.sessionId || 'unknown';
    if (!groupedBySession.has(sessionId)) {
      groupedBySession.set(sessionId, []);
    }
    groupedBySession.get(sessionId)!.push(bill);
  });

  // 对每个sessionId组内的账单按时间排序（最新的在前）
  groupedBySession.forEach(bills => {
    bills.sort((a, b) => b.timestamp - a.timestamp);
  });

  // 将分组后的结果合并，按每组最新时间排序
  const sortedGroups = Array.from(groupedBySession.entries()).sort(([, billsA], [, billsB]) => {
    // 按每组中最新的时间戳排序
    const maxTimeA = Math.max(...billsA.map(bill => bill.timestamp));
    const maxTimeB = Math.max(...billsB.map(bill => bill.timestamp));
    return maxTimeB - maxTimeA;
  });

  // 展平结果，保持sessionId分组和时间排序
  return sortedGroups.flatMap(([, bills]) => bills);
});

// 处理交班上报
const handleSubmitHandover = async () => {
  if (!vm.computed.hasData.value) {
    return;
  }

  try {
    await vm.actions.submitHandover();
  } catch (error) {
    console.error('交班上报错误:', error);
  }
};

// 处理交班
const handleShift = async () => {
  try {
    // 检查是否有交班数据
    if (!vm.computed.hasData.value) {
      console.warn('[shift] 没有可上报的交班数据:', vm.computed.hasData.value);
      ElMessageBox.alert('没有可上报的交班数据', '提示', { type: 'warning' });
      return;
    }

    // 确认交班
    await ElMessageBox.confirm('交班后将自动退出登录', '交班确认', {
      confirmButtonText: '确定',
      showCancelButton: false
    });

    // 调用交班方法
    await handleSubmitHandover();

    // 交班成功后登出
    userStore.logoutAction();

    // 重定向到登录页
    router.push('/login');
  } catch (error) {
    // 用户取消或交班出错时不执行登出
    console.log('交班已取消或出错:', error);
  }
};

// 处理返回按钮（历史模式）
const handleGoBack = () => {
  // 返回到交班历史页面，并标记为从详情返回
  router.push({
    path: '/shift/history',
    query: { returnTab: 'shiftHistory' }
  });
};

// 处理打印按钮（历史模式）
const handlePrint = () => {
  // TODO: 实现打印功能
  ElMessageBox.alert('打印功能正在开发中', '提示', { type: 'info' });
};

// 组件加载完成后自动查询
// 立即发起请求
vm.actions.queryShiftReport();

// 暴露方法给父组件
defineExpose({
  submitHandover: handleSubmitHandover,
  get hasData() {
    return vm.computed.hasData.value;
  },
  vm // 暴露整个vm对象
});

// 格式化单号的函数
const formatBillId = (billId: string) => {
  if (!billId) return '';
  const shortId = billId.length > 6 ? `${billId.substring(0, 2)}**${billId.substring(billId.length - 4)}` : billId;
  return shortId;
};

// 获取账单显示文本 - 简化版
const getBillDisplayText = (bill: any) => {
  const shortId = formatBillId(bill.id);

  // 根据账单类型和状态添加前缀
  if (bill.direction === 'refund') {
    if (bill.isBack) {
      return `【还原】${shortId}`;
    }
    return `【退款】${shortId}`;
  }

  if (bill.isBack) {
    return `【已还原】${shortId}`;
  }

  // 普通账单
  return shortId;
};

// 获取显示金额 - 只根据direction判断正负值
const getDisplayAmount = (bill: any) => {
  const baseAmount = bill.amount || 0;

  // 只根据direction判断正负：refund为负，normal为正
  if (bill.direction === 'refund') {
    const result = -Math.abs(baseAmount);
    return result;
  }

  // 普通账单（direction === 'normal'）显示正值
  return baseAmount;
};

// 获取金额显示样式类
const getAmountDisplayClass = (bill: any) => {
  // 只有退款账单使用红色样式
  if (bill.direction === 'refund') {
    return 'text-red-500';
  }

  // 普通账单使用默认样式
  return '';
};
</script>

<style scoped>
:deep(.el-table th) {
  color: #606266;
  font-weight: 500;
}

.label-width {
  font-family: MiSans;
  font-weight: 380;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  vertical-align: middle;
}

.value-width {
  width: 120px;
}

:deep(.big-text .price-unit) {
  font-size: 24px;
}

:deep(.big-text .price-integer) {
  font-size: 56px;
}

:deep(.big-text .price-decimal) {
  font-size: 24px;
}

:deep(.el-table .el-table__header-wrapper) {
  background-color: #f5f5f5;
}

/* 账单表格行可点击样式 */
:deep(.bill-table-clickable .el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

:deep(.bill-table-clickable .el-table__row:hover) {
  background-color: #f0f9ff !important;
}

/* 导航栏按钮样式 */
:deep(.header-right .el-button) {
  height: 56px;
  width: 160px;
  border-radius: 6px;
  font-size: 20px;
  font-weight: 450;
}

:deep(.header-right .el-button:hover) {
  background-color: #e23939;
  color: #fff;
}

/* 打印按钮样式 */
.btn-light-gray {
  height: 56px;
  width: 160px;
  border-radius: 6px;
  font-size: 20px;
  font-weight: 450;
  background-color: #f5f5f5;
  border: 1px solid #dcdfe6;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-light-gray:hover {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

/* 支付卡片样式优化 */
.payment-card {
  min-height: 200px;
  max-height: 80vh;
}

/* 普通支付卡片渐变背景 */
.payment-card-normal {
  background: linear-gradient(180deg, #e2e8f9 0%, #ffffff 44.39%);
}

/* 会员支付卡片渐变背景 */
.payment-card-member {
  background: linear-gradient(180deg, #fcf2d9 0%, #ffffff 44.39%);
}

/* 普通支付柱子颜色 */
.bg-normal-payment {
  background-color: #3968e2 !important;
}

/* 会员支付柱子颜色 */
.bg-member-payment {
  background-color: #dfb038 !important;
}

.payment-chart {
  min-height: 100px;
}

/* 柱状图容器样式 */
.payment-bar-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  min-height: 100px;
}

/* 确保文字不会被压缩 */
.payment-bar-label {
  flex-shrink: 0;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
}

.payment-bar-value {
  flex-shrink: 0;
  font-size: 12px;
  text-align: center;
  margin-bottom: 4px;
}

/* 响应式调整 */
@media (max-height: 800px) {
  .payment-card {
    min-height: 150px;
  }

  .payment-chart {
    min-height: 80px;
  }

  .payment-bar-value,
  .payment-bar-label {
    font-size: 11px;
  }
}

@media (max-height: 600px) {
  .payment-card {
    min-height: 120px;
  }

  .payment-chart {
    min-height: 60px;
  }

  .payment-bar-value,
  .payment-bar-label {
    font-size: 10px;
  }
}
</style>
