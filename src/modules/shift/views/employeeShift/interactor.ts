import { getShiftReportDaily, submitShiftHandover, getShiftReportById } from '../../api/employeeShift';
import type { ShiftReportDailyResponse, ShiftReportDaily } from '../../entity/EmployeeShiftEntity';
import { ElMessage } from 'element-plus';
import { PrintingService } from '@/application/printingService';

/**
 * 员工交班业务交互层
 */
export class EmployeeShiftInteractor {
  private static printingService = new PrintingService();
  /**
   * 获取交班报告数据
   * @param date 日期（时间戳）
   * @param employeeId 员工ID
   * @param venueId 场地ID
   * @returns 交班报告响应
   */
  static async getShiftReport(date: number, employeeId: string, venueId: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportDaily({
      date,
      employeeId,
      venueId
    });
  }

  /**
   * 通过交班单ID获取交班报告数据
   * @param shiftId 交班单ID
   * @param handNo 交班单号（可选）
   * @returns 交班报告响应
   */
  static async getShiftReportById(shiftId: string, handNo?: string): Promise<ShiftReportDailyResponse> {
    return await getShiftReportById(shiftId, handNo);
  }

  /**
   * 提交交班上报
   * @param date 日期
   * @param employeeId 员工ID
   * @param venueId 场馆ID
   * @param shiftReportData 交班数据
   * @returns 上报结果
   */
  static async submitShiftHandover(
    date: number,
    employeeId: string,
    venueId: string,
    shiftReportData: ShiftReportDaily[]
  ): Promise<any> {
    return await submitShiftHandover({
      date,
      employeeId,
      venueId,
      shiftReportDaily: shiftReportData
    });
  }

  /**
   * 打印交班报告
   * @param handNos 交班单号数组
   * @param employeeId 员工ID（可选）
   * @returns 打印是否成功
   */
  static async printShiftReport(handNos: string[], employeeId?: string): Promise<boolean> {
    try {
      if (!handNos || handNos.length === 0) {
        throw new Error('没有可打印的交班单号');
      }

      console.log('[EmployeeShiftInteractor] 准备打印交班报告:', { handNos, employeeId });

      // 调用PrintingService的交班单打印方法
      const success = await this.printingService.printShiftChangeByHandNos(handNos, employeeId);

      console.log('[EmployeeShiftInteractor] 打印交班报告结果:', success);

      return success;
    } catch (error) {
      console.error('[EmployeeShiftInteractor] 打印交班报告失败:', error);
      throw error;
    }
  }

  /**
   * 导出交班报告
   * 这里可以实现具体的导出业务逻辑
   */
  static exportShiftReport(reportData: any): void {
    // 导出业务逻辑实现
    // 可能需要生成Excel或CSV文件并触发下载
    console.log('导出交班报告:', reportData);
  }

  /**
   * 打印结账单 - 使用PrintingService封装的批量打印功能
   * @param billIds 账单ID列表
   * @param sessionId 会话ID
   * @returns 打印结果
   */
  static async printCheckoutBills(billIds: string[], sessionId: string): Promise<void> {
    if (!billIds || billIds.length === 0) {
      throw new Error('没有可打印的账单');
    }

    if (!sessionId) {
      throw new Error('会话ID不能为空');
    }

    console.log('[EmployeeShiftInteractor] 准备打印账单:', {
      billIds,
      sessionId,
      count: billIds.length
    });

    try {
      // 调用PrintingService的批量打印方法
      const result = await this.printingService.printCheckoutBillsByBillIds(billIds, sessionId);

      console.log('[EmployeeShiftInteractor] 打印结果:', result);

      // 显示打印结果
      if (result.failed === 0) {
        ElMessage.success(`成功打印 ${result.success} 张账单`);
      } else if (result.success === 0) {
        throw new Error(`所有账单打印失败`);
      } else {
        ElMessage.warning(`成功打印 ${result.success} 张账单，失败 ${result.failed} 张`);
      }

    } catch (error) {
      console.error('[EmployeeShiftInteractor] 批量打印失败:', error);
      throw error;
    }
  }
}