<template>
  <div class="flex flex-col h-full">
    <!-- 导航栏 -->
    <nav class="p-4 bg-white border-b flex flex-row justify-between items-center">
      <div class="flex justify-between items-center">
        <!-- Tab切换区域 -->
        <div class="flex bg-[#F3F3F3] rounded-[10px] h-[68px] items-center p-[8px] gap-[8px]">
          <div
            class="flex-1 h-[52px] w-[104px] font-medium rounded-[10px] flex items-center justify-center transition-all duration-200 cursor-pointer"
            :class="activeTab === 'shift' ? 'bg-btn-focus text-white' : 'text-gray-500'"
            @click="handleTabClick('shift')">
            交班
          </div>
          <div
            class="flex-1 h-[52px] w-[104px] font-medium rounded-[10px] flex items-center justify-center transition-all duration-200 cursor-pointer"
            :class="activeTab === 'history' ? 'bg-btn-focus text-white' : 'text-gray-500'"
            @click="handleTabClick('history')">
            交班历史
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="flex flex-row justify-between items-center header-right">
        <!-- 时间范围选择器 -->
        <div class="flex items-center">
          <span class="text-gray-600 mr-2">时间范围:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="x"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            @change="handleDateRangeChange" />
        </div>
      </div>
    </nav>

    <!-- 表格区域 -->
    <div class="flex-grow overflow-hidden p-4">
      <!-- 错误提示区域 -->
      <el-alert
        v-if="hasError"
        type="error"
        title="数据加载失败"
        description="加载交班历史数据时出现错误，请尝试刷新或稍后再试。"
        show-icon
        closable
        @close="hasError = false"
        class="mb-4">
        <template #default>
          <div class="mt-2">
            <el-button type="normal" @click="vm.actions.refresh"> 重新加载 </el-button>
          </div>
        </template>
      </el-alert>

      <el-table
        v-loading="vm.state.loading"
        :data="vm.computed.formattedHistoryList.value"
        stripe
        class="w-full"
        height="100%"
        v-infinite-scroll="handleInfiniteScroll"
        :infinite-scroll-disabled="infiniteScrollDisabled"
        :infinite-scroll-distance="50"
        :infinite-scroll-delay="200">
        <!-- 交班ID/单号 -->
        <el-table-column label="交班单号" min-width="18%" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.handNo || row.id || '--' }}</div>
          </template>
        </el-table-column>

        <!-- 交班员工 -->
        <el-table-column label="员工" min-width="12%" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.employeeName || '--' }}</div>
          </template>
        </el-table-column>

        <!-- 交班时间 -->
        <el-table-column label="交班时间" min-width="18%" align="center">
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.formattedHandTime || '--' }}</div>
          </template>
        </el-table-column>

        <!-- 总金额 -->
        <el-table-column label="总金额" min-width="12%" align="right">
          <template #default="{ row }">
            <div class="text-[16px] font-medium">{{ row.formattedTotalFee || '¥0.00' }}</div>
          </template>
        </el-table-column>

        <!-- 现金金额 -->
        <el-table-column label="现金金额" min-width="12%" align="right">
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.formattedCash || '¥0.00' }}</div>
          </template>
        </el-table-column>

        <!-- 线上金额 -->
        <el-table-column label="线上金额" min-width="12%" align="right">
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.formattedOnlinePayment || '¥0.00' }}</div>
          </template>
        </el-table-column>

        <!-- 开台数量 -->
        <el-table-column label="开台数量" min-width="10%" align="center">
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.openCount || 0 }}</div>
          </template>
        </el-table-column>

        <!-- 订单数量 -->
        <!-- <el-table-column label="订单数量" min-width="10%" align="center">
          <template #default="{ row }">
            <div class="text-[16px]">{{ row.totalOrderCount || 0 }}</div>
            <div class="text-xs text-gray-500">已结:{{ row.orderPaidCount || 0 }} / 未结:{{ row.orderUnpaidCount || 0 }}</div>
          </template>
        </el-table-column> -->

        <!-- 操作 -->
        <el-table-column label="操作" min-width="10%" align="center">
          <template #default="{ row }">
            <el-button type="primary" @click="vm.actions.viewDetail(row.id, row.handNo)" :disabled="!row.id"> 详情 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 加载更多提示 -->
      <div v-if="vm.state.loading && vm.state.historyList.length > 0" class="text-center py-4">
        <el-icon class="loading">
          <Loading />
        </el-icon>
        <span class="ml-2">加载中...</span>
      </div>

      <!-- 无数据提示 -->
      <div v-if="!vm.state.loading && vm.state.historyList.length === 0" class="text-center py-12 bg-white rounded-md mt-4">
        <el-empty description="暂无交班记录" />

        <!-- 初次加载时显示刷新按钮 -->
        <el-button class="mt-4" type="primary" @click="vm.actions.refresh"> 刷新数据 </el-button>
      </div>

      <!-- 加载完成提示 -->
      <div v-if="!vm.state.loading && !vm.computed.hasMoreData.value && vm.state.historyList.length > 0" class="text-center text-gray-500 py-4">
        没有更多数据了
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Search, Loading, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useShiftHistoryListPresenter } from './presenter';
import type { IShiftHistoryListViewModel } from './viewmodel';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import { useShiftStore } from '@/stores/shiftStore';

// 组件名称（Vue 3.3+ 语法）
const __componentName = 'EmployeeShiftHistory';

// 使用Presenter
const vm: IShiftHistoryListViewModel = useShiftHistoryListPresenter();

// 路由
const router = useRouter();
const route = useRoute();

// 使用交班store
const shiftStore = useShiftStore();

// Tab相关状态
const activeTab = computed(() => 'history');

// 时间范围选择器值
const dateRange = ref<[number, number] | null>(null);

// 错误状态
const hasError = ref(false);

// 滚动相关
const scrollContainerRef = ref<HTMLElement | null>(null);
const infiniteScrollDisabled = computed(() => {
  return vm.state.loading || !vm.computed.hasMoreData.value;
});

// 检查页面进入来源
const checkPageSource = () => {
  const fromTab = route.query.from === 'tab-switch';
  const returnTab = route.query.returnTab === 'shiftHistory';

  if (fromTab) {
    // 从tab切换进入
    shiftStore.enterHistoryFromTab();
  } else if (returnTab) {
    // 从详情页面返回
    shiftStore.enterHistoryFromDetail();
  } else {
    // 直接进入
    shiftStore.enterHistoryDirect();
  }
};

// 防抖处理无限滚动
let scrollDebounceTimer: number | null = null;
const handleInfiniteScroll = () => {
  if (scrollDebounceTimer) {
    clearTimeout(scrollDebounceTimer);
  }

  scrollDebounceTimer = window.setTimeout(() => {
    vm.actions.loadMore();
  }, 200);
};

// 处理时间范围变化 - 自动查询
const handleDateRangeChange = (value: [number, number] | null) => {
  if (value) {
    dateRange.value = value;
    // 更新Presenter中的时间范围
    vm.state.startTime = value[0];
    vm.state.endTime = value[1];

    // 保存到store
    shiftStore.saveHistoryFilter({
      dateRange: value,
      searchText: vm.state.searchText
    });

    // 重置其他筛选条件和分页
    vm.state.date = null; // 清除单日期筛选
    vm.state.currentPage = 1; // 重置页码
    vm.state.historyList = []; // 清空列表

    // 加载数据
    vm.actions.loadHistoryList().catch(error => {
      console.error('根据时间范围加载数据失败:', error);
      hasError.value = true;
    });
  } else {
    dateRange.value = null;
    // 清除时间范围筛选
    vm.state.startTime = null;
    vm.state.endTime = null;

    // 清除store中的筛选条件
    shiftStore.saveHistoryFilter({
      dateRange: null,
      searchText: vm.state.searchText
    });
  }
};

// 初始化页面数据
const initializePageData = async () => {
  try {
    // 检查页面来源
    checkPageSource();

    // 获取有效的日期范围
    const effectiveRange = shiftStore.effectiveDateRange;
    dateRange.value = effectiveRange;

    // 同步到presenter状态
    vm.state.startTime = effectiveRange[0];
    vm.state.endTime = effectiveRange[1];

    // 如果有保存的搜索文本，也要恢复
    if (shiftStore.shouldUseStoredFilter && shiftStore.historyFilter.searchText) {
      vm.state.searchText = shiftStore.historyFilter.searchText;
    }

    // 保存当前筛选条件到store
    shiftStore.saveHistoryFilter({
      dateRange: effectiveRange,
      searchText: vm.state.searchText
    });

    // 加载数据
    await vm.actions.loadHistoryList();
  } catch (error) {
    console.error('初始加载数据失败:', error);
    hasError.value = true;
  }
};

// 搜索处理函数
const handleSearch = () => {
  vm.actions.searchHistory(vm.state.searchText);
};

// 处理tab点击
const handleTabClick = (tab: string) => {
  if (tab === 'shift') {
    router.push('/shift/handover');
  } else if (tab === 'history') {
    // 已经在交班历史页面，不需要跳转
    return;
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initializePageData();
});

// 在组件卸载时保存当前筛选条件
onBeforeUnmount(() => {
  // 保存当前筛选条件到store
  shiftStore.saveHistoryFilter({
    dateRange: dateRange.value,
    searchText: vm.state.searchText
  });
});
</script>

<style scoped>
/* 自定义滚动条样式 */
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  border-radius: 4px;
  background-color: rgba(144, 147, 153, 0.3);
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  border-radius: 4px;
  background-color: rgba(144, 147, 153, 0.1);
}

/* 表格行高亮效果 */
:deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

/* 表格行高 */
:deep(.el-table .cell) {
  padding: 12px 8px;
}

/* 加载图标动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading {
  animation: rotate 1s linear infinite;
}

/* 导航栏按钮样式 */
:deep(.header-right .el-button) {
  height: 56px;
  width: 160px;
  border-radius: 6px;
  font-size: 20px;
  font-weight: 450;
}

:deep(.header-right .el-button:hover) {
  background-color: #e23939;
  color: #fff;
}

:deep(.el-date-editor--daterange) {
  width: 360px;
}
</style>
