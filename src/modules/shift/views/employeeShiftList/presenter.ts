import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { IShiftHistoryListViewModel, IShiftHistoryListState, IShiftHistoryListComputed, IShiftHistoryListActions, IShiftHistoryItem } from './viewmodel';
import { ShiftHistoryInteractor } from './interactor';
import { ShiftHistoryConverter } from './converter';

export class ShiftHistoryListPresenter implements IShiftHistoryListViewModel {
  private interactor: ShiftHistoryInteractor;
  private converter: ShiftHistoryConverter;
  private router: ReturnType<typeof useRouter>;

  /**
   * 状态管理
   */
  public state: IShiftHistoryListState = reactive({
    loading: false,
    historyList: [],
    total: 0,
    currentPage: 1,
    pageSize: 1000,
    searchText: '',
    date: null,
    startTime: null, // 开始时间
    endTime: null, // 结束时间
    employeeId: '',
    venueId: ''
  });

  /**
   * 计算属性
   */
  public computed: IShiftHistoryListComputed = {
    // 格式化后的历史列表
    formattedHistoryList: computed(() => {
      return this.converter.formatShiftHistoryList(this.state.historyList);
    }),

    // 是否有更多数据
    hasMoreData: computed(() => {
      return this.state.historyList.length < this.state.total;
    }),

    // 计算线上支付总额
    onlinePayment: computed(() => (item: IShiftHistoryItem) => {
      return this.converter.calculateOnlinePayment(item);
    })
  };

  /**
   * 动作
   */
  public actions: IShiftHistoryListActions = {
    // 加载历史列表
    loadHistoryList: async () => {
      try {
        this.state.loading = true;

        // 获取当前用户和场馆信息
        const { employeeId, venueId } = this.interactor.getUserAndVenueInfo();

        // 更新状态中的信息
        if (!this.state.employeeId) this.state.employeeId = employeeId;
        if (!this.state.venueId) this.state.venueId = venueId;

        // 创建请求参数
        const params = this.converter.createSearchParams({
          keyword: this.state.searchText,
          pageNum: this.state.currentPage,
          pageSize: this.state.pageSize,
          date: this.state.date || undefined,
          startTime: this.state.startTime || undefined,
          endTime: this.state.endTime || undefined,
          employeeId: this.state.employeeId,
          venueId: this.state.venueId
        });

        const result = await this.interactor.fetchShiftHistoryList(params);

        // 即使result为空，interactor现在也会返回一个带有空数组的对象，而不是null
        if (result) {
          this.state.historyList = result.content || [];
          this.state.total = result.total || 0;
          this.state.currentPage = result.page || 1;
        } else {
          this.state.historyList = [];
          this.state.total = 0;
        }
      } catch (error) {
        console.error('加载交班历史列表失败:', error);
        ElMessage.error('加载交班历史列表失败，请稍后重试');
      } finally {
        this.state.loading = false;
      }
    },

    // 加载更多数据
    loadMore: async () => {
      if (this.state.loading || !this.computed.hasMoreData.value) {
        return;
      }

      try {
        this.state.loading = true;
        this.state.currentPage += 1;

        // 创建请求参数
        const params = this.converter.createSearchParams({
          keyword: this.state.searchText,
          pageNum: this.state.currentPage,
          pageSize: this.state.pageSize,
          date: this.state.date || undefined,
          startTime: this.state.startTime || undefined,
          endTime: this.state.endTime || undefined,
          employeeId: this.state.employeeId,
          venueId: this.state.venueId
        });

        const result = await this.interactor.fetchShiftHistoryList(params);

        // 添加新加载的数据到列表末尾
        if (result && result.content && result.content.length > 0) {
          this.state.historyList = [...this.state.historyList, ...result.content];
          this.state.total = result.total || this.state.total;
        } else {
          // 如果没有更多数据，恢复页码
          this.state.currentPage -= 1;
        }
      } catch (error) {
        console.error('加载更多交班历史数据失败:', error);
        this.state.currentPage -= 1; // 恢复页码
        ElMessage.error('加载更多数据失败，请稍后重试');
      } finally {
        this.state.loading = false;
      }
    },

    // 搜索历史记录
    searchHistory: (keyword: string) => {
      this.state.searchText = keyword;
      this.state.currentPage = 1;
      this.state.historyList = [];
      this.actions.loadHistoryList();
    },

    // 查看详情
    viewDetail: (id: string, handNo?: string) => {
      if (!id) {
        ElMessage.warning('交班记录ID无效');
        return;
      }

      // 构建查询参数，包含返回标识
      const query: Record<string, string> = {
        returnTab: 'shiftHistory' // 标记返回时应该显示交班历史页面
      };

      // 如果提供了handNo，则添加到查询参数中
      if (handNo) {
        query.handNo = handNo;
      }

      this.router.push({
        path: `/shift/history/detail/${id}`,
        query
      });
    },

    // 刷新数据
    refresh: async () => {
      this.state.currentPage = 1;
      this.state.historyList = [];
      await this.actions.loadHistoryList();
    }
  };

  constructor() {
    this.interactor = new ShiftHistoryInteractor();
    this.converter = new ShiftHistoryConverter();
    this.router = useRouter();

    // 获取当前用户和场馆信息
    const { employeeId, venueId } = this.interactor.getUserAndVenueInfo();
    this.state.employeeId = employeeId;
    this.state.venueId = venueId;
  }
}

/**
 * 创建交班历史列表Presenter的组合式函数
 * @returns 交班历史列表ViewModel
 */
export function useShiftHistoryListPresenter(): IShiftHistoryListViewModel {
  return new ShiftHistoryListPresenter();
}
