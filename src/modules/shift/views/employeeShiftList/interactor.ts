import { IShiftHistoryItem } from './viewmodel';
import request from '@/utils/request';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';

// 查询交班历史请求参数接口
interface QueryShiftHandoverFormReqDto {
  date?: number; // 查询单日期
  ctimeStart?: number; // 查询开始时间戳（毫秒）
  ctimeEnd?: number; // 查询结束时间戳（毫秒）
  employeeId?: string; // 员工ID
  id?: string; // 交班ID（查询详情时使用）
  pageNum: number; // 页码
  pageSize: number; // 每页大小
  venueId?: string; // 场馆ID
}

// 交班历史API响应接口
interface ShiftHandoverFormResponse {
  code: number;
  message: string;
  data: {
    data: IShiftHistoryItem[];
    pageNum: number;
    pageSize: number;
    total: number;
  };
  serverTime: number;
  requestID: string;
  traceId?: string; // 可选字段
}

/**
 * 交班历史业务交互器
 */
export class ShiftHistoryInteractor {
  /**
   * 获取交班历史列表
   * @param params 查询参数
   * @returns 查询结果
   */
  async fetchShiftHistoryList(params: QueryShiftHandoverFormReqDto): Promise<{
    content: IShiftHistoryItem[];
    total: number;
    page: number;
    size: number;
  }> {
    try {
      const response = await request.post<ShiftHandoverFormResponse>('/api/v3/shift-report/handover/history', params);

      // 验证响应
      if (!response) {
        throw new Error('获取交班历史列表失败: 响应为空');
      }

      if (response.code !== 0) {
        throw new Error(response.data?.message || '获取交班历史列表失败');
      }

      // 根据真实API响应结构访问数据
      const responseData = response.data;
      if (!responseData) {
        return {
          content: [],
          total: 0,
          page: 1,
          size: params.pageSize || 10
        };
      }

      const historyList = responseData.data || [];

      return {
        content: historyList,
        total: responseData.total || 0,
        page: responseData.pageNum || 1,
        size: responseData.pageSize || params.pageSize || 10
      };
    } catch (error) {
      console.error('获取交班历史列表失败:', error);
      // 返回一个空结果而不是抛出错误，避免UI崩溃
      return {
        content: [],
        total: 0,
        page: 1,
        size: params.pageSize || 10
      };
    }
  }

  /**
   * 获取交班历史详情
   * @param id 交班历史ID
   * @returns 交班历史详情
   */
  async fetchShiftHistoryDetail(id: string): Promise<IShiftHistoryItem | null> {
    try {
      const response = await request.post<ShiftHandoverFormResponse>('/api/v3/shift-report/handover/history', {
        id,
        pageNum: 1,
        pageSize: 1
      });

      // 验证响应
      if (!response) {
        throw new Error('获取交班历史详情失败: 响应为空');
      }

      if (response.code !== 0) {
        throw new Error(response.data?.message || '获取交班历史详情失败');
      }

      if (!response.data || !response.data.data || response.data.data.length === 0) {
        return null; // 返回null而不是抛出错误
      }

      return response.data.data[0];
    } catch (error) {
      console.error('获取交班历史详情失败:', error);
      return null; // 返回null而不是抛出错误
    }
  }

  /**
   * 获取当前登录用户和场馆信息
   */
  getUserAndVenueInfo() {
    const userStore = useUserStore();
    const venueStore = useVenueStore();

    return {
      employeeId: userStore.userInfo.employee?.id || '',
      employeeName: userStore.userInfo.employee?.name || '',
      venueId: venueStore.venueId || ''
    };
  }
}
