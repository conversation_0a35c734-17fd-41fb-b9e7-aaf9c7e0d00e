import { ComputedRef } from 'vue';

// 交班历史记录项接口 - 与API响应对应
export interface IShiftHistoryItem {
  id: string; // 交班ID
  handNo: string; // 交班单号
  employeeId: string; // 员工ID
  operator: string; // 操作员（员工姓名）
  handTime: number; // 交班时间戳
  ctime: number; // 创建时间

  // 金额相关字段
  totalFee: number; // 总金额（分）
  shouldFee: number; // 应收金额（分）
  netFee: number; // 净收入（分）
  roomFee: number; // 包厢收入（分）
  productFee: number; // 商品收入（分）

  // 支付方式
  cash: number; // 现金（分）
  wechat: number; // 微信（分）
  alipay: number; // 支付宝（分）
  bank: number; // 银行卡（分）
  meituan: number; // 美团（分）
  koubei: number; // 口碑（分）
  ticket: number; // 招待券（分）
  other: number; // 其他（分）

  // 折扣和调整金额
  memberDiscount: number; // 会员优惠（分）
  merchantDiscount: number; // 商家优惠（分）
  lowConsumptionFee: number; // 低消差额（分）
  employeeGift: number; // 员工赠送（分）
  zeroFee: number; // 抹零金额（分）

  // 统计数据
  openCount: number; // 开台数
  orderPaidCount: number; // 已结账订单数
  orderUnpaidCount: number; // 未结账订单数

  // 其他字段
  state: number; // 状态
  remark: string; // 备注
  venueId: string; // 场馆ID
  version: number; // 版本号
  utime: number; // 更新时间
}

// 列表状态接口
export interface IShiftHistoryListState {
  loading: boolean;
  historyList: IShiftHistoryItem[];
  total: number;
  currentPage: number;
  pageSize: number;
  searchText: string;

  // 筛选条件
  date: number | null; // 查询单个日期
  startTime: number | null; // 查询开始时间
  endTime: number | null; // 查询结束时间
  employeeId: string; // 员工ID
  venueId: string; // 场馆ID
}

// 计算属性接口
export interface IShiftHistoryListComputed {
  // 格式化后的历史列表
  formattedHistoryList: ComputedRef<IShiftHistoryItem[]>;
  // 是否有更多数据
  hasMoreData: ComputedRef<boolean>;
  // 线上支付总额
  onlinePayment: ComputedRef<(item: IShiftHistoryItem) => number>;
}

// 动作接口
export interface IShiftHistoryListActions {
  // 加载历史列表数据
  loadHistoryList(): Promise<void>;
  // 加载更多数据
  loadMore(): Promise<void>;
  // 搜索历史记录
  searchHistory(keyword: string): void;
  // 查看详情
  viewDetail(id: string, handNo?: string): void;
  // 刷新数据
  refresh(): Promise<void>;
}

// 总的ViewModel接口
export interface IShiftHistoryListViewModel {
  state: IShiftHistoryListState;
  computed: IShiftHistoryListComputed;
  actions: IShiftHistoryListActions;
}
