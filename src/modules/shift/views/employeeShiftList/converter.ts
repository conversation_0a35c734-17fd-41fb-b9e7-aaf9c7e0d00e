import { IShiftHistoryItem } from './viewmodel';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import { formatYuanWithSymbol } from '@/utils/priceUtils';

/**
 * 视图模型转换器
 * 负责数据模型到视图模型的转换
 */
export class ShiftHistoryConverter {
  /**
   * 转换交班历史项到显示格式
   * @param item 交班历史项
   * @returns 格式化后的交班历史项
   */
  formatShiftHistoryItem(item: IShiftHistoryItem): IShiftHistoryItem & {
    formattedHandTime: string;
    formattedCtime: string;
    formattedTotalFee: string;
    formattedCash: string;
    formattedOnlinePayment: string;
    formattedRoomFee: string;
    formattedProductFee: string;
    formattedNetFee: string;
    totalOrderCount: number;
  } {
    if (!item) {
      console.warn('尝试格式化空的交班历史项');
      return {
        ...this.createEmptyHistoryItem(),
        formattedHandTime: '--',
        formattedCtime: '--',
        formattedTotalFee: '¥0.00',
        formattedCash: '¥0.00',
        formattedOnlinePayment: '¥0.00',
        formattedRoomFee: '¥0.00',
        formattedProductFee: '¥0.00',
        formattedNetFee: '¥0.00',
        totalOrderCount: 0
      };
    }

    try {
      // 计算线上支付总额 (微信+支付宝+美团+口碑)
      const onlinePayment = (item.wechat || 0) + (item.alipay || 0) + (item.meituan || 0) + (item.koubei || 0);

      // 计算订单总数
      const totalOrderCount = (item.orderPaidCount || 0) + (item.orderUnpaidCount || 0);

      // 确保时间戳是有效数字
      const handTime = typeof item.handTime === 'number' && !isNaN(item.handTime) ? item.handTime : 0;
      const ctime = typeof item.ctime === 'number' && !isNaN(item.ctime) ? item.ctime : 0;

      return {
        ...item,
        // 格式化时间
        formattedHandTime: handTime > 0 ? formatUnixTimestamp(handTime) : '--',
        formattedCtime: ctime > 0 ? formatUnixTimestamp(ctime) : '--',
        // 格式化金额（分转元）
        formattedTotalFee: formatYuanWithSymbol(item.totalFee || 0),
        formattedCash: formatYuanWithSymbol(item.cash || 0),
        formattedOnlinePayment: formatYuanWithSymbol(onlinePayment),
        formattedRoomFee: formatYuanWithSymbol(item.roomFee || 0),
        formattedProductFee: formatYuanWithSymbol(item.productFee || 0),
        formattedNetFee: formatYuanWithSymbol(item.netFee || 0),
        // 计算总订单数
        totalOrderCount
      };
    } catch (error) {
      console.error('格式化交班历史项失败:', error, item);
      return {
        ...item,
        formattedHandTime: '--',
        formattedCtime: '--',
        formattedTotalFee: '¥0.00',
        formattedCash: '¥0.00',
        formattedOnlinePayment: '¥0.00',
        formattedRoomFee: '¥0.00',
        formattedProductFee: '¥0.00',
        formattedNetFee: '¥0.00',
        totalOrderCount: 0
      };
    }
  }

  /**
   * 创建空的历史项
   * 用于处理空值情况
   */
  private createEmptyHistoryItem(): IShiftHistoryItem {
    return {
      id: '',
      handNo: '',
      employeeId: '',
      operator: '',
      handTime: 0,
      ctime: 0,
      totalFee: 0,
      shouldFee: 0,
      netFee: 0,
      roomFee: 0,
      productFee: 0,
      cash: 0,
      wechat: 0,
      alipay: 0,
      bank: 0,
      meituan: 0,
      koubei: 0,
      ticket: 0,
      other: 0,
      memberDiscount: 0,
      merchantDiscount: 0,
      lowConsumptionFee: 0,
      employeeGift: 0,
      zeroFee: 0,
      openCount: 0,
      orderPaidCount: 0,
      orderUnpaidCount: 0,
      state: 0,
      remark: '',
      venueId: '',
      version: 0,
      utime: 0
    };
  }

  /**
   * 批量转换交班历史列表
   * @param items 交班历史列表
   * @returns 格式化后的交班历史列表
   */
  formatShiftHistoryList(items: IShiftHistoryItem[]): Array<ReturnType<typeof this.formatShiftHistoryItem>> {
    if (!items || !Array.isArray(items)) {
      console.warn('尝试格式化无效的交班历史列表:', items);
      return [];
    }
    return items.map(item => this.formatShiftHistoryItem(item));
  }

  /**
   * 计算线上支付总额
   * @param item 交班历史项
   * @returns 线上支付总额
   */
  calculateOnlinePayment(item: IShiftHistoryItem): number {
    if (!item) return 0;
    return (item.wechat || 0) + (item.alipay || 0) + (item.meituan || 0) + (item.koubei || 0);
  }

  /**
   * 创建搜索参数
   * @param options 搜索选项
   * @returns 搜索参数对象
   */
  createSearchParams(options: {
    keyword?: string;
    pageNum: number;
    pageSize: number;
    date?: number;
    startTime?: number;
    endTime?: number;
    employeeId?: string;
    venueId?: string;
  }) {
    const { keyword, pageNum, pageSize, date, startTime, endTime, employeeId, venueId } = options;

    const params: any = {
      pageNum: pageNum || 1,
      pageSize: pageSize || 1000,
      venueId: venueId || ''
    };

    // 单日期查询（当指定startTime和endTime时，优先使用时间范围）
    if (date && !startTime && !endTime) {
      params.date = Math.floor(date / 1000); // 转换为秒级时间戳
    }

    // 时间范围查询 - 修复时间精度问题
    if (startTime) {
      params.ctimeStart = Math.floor(startTime / 1000); // 转换为秒级时间戳，向下取整
    }

    if (endTime) {
      params.ctimeEnd = Math.floor(endTime / 1000); // 转换为秒级时间戳，向下取整
    }

    // 其他可选参数
    if (employeeId) params.employeeId = employeeId;
    if (keyword) {
      // 假设搜索关键字可以用于查找交班单号或员工名称
      // 实际项目中可能需要调整此逻辑以匹配后端API
      if (keyword.startsWith('ID:')) {
        params.id = keyword.replace('ID:', '').trim();
      } else if (keyword.startsWith('单号:')) {
        params.handNo = keyword.replace('单号:', '').trim();
      } else {
        // 假设默认是搜索员工名称
        // 实际可能需要服务端支持模糊搜索
        params.operator = keyword;
      }
    }

    return params;
  }
}
