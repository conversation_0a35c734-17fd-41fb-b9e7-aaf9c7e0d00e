<template>
  <AppDialog v-model="dialogVisible" title="结账详情" :destroy-on-close="true" class="bill-detail-dialog !w-[864px]">
    <div class="p-4">
      <!-- 左侧应收应付信息 -->
      <div class="flex justify-between mb-6">
        <div class="w-1/2 border-r pr-4">
          <div class="flex justify-between mb-2 items-center">
            <div class="text-[16px] font-bold">应收金额</div>
            <PriceDisplay :amountInFen="shouldFee" />
          </div>
        </div>

        <div class="w-1/2 pl-4">
          <div class="flex justify-between mb-2 items-center">
            <div class="text-[16px] font-bold">实收金额</div>
            <PriceDisplay :amountInFen="totalReceived" />
          </div>
          <!-- 支付方式列表 -->
          <div v-for="(item, index) in paymentList" :key="index" class="flex justify-between text-sm mt-2">
            <div class="text-gray-500">{{ item.isRefund ? `${item.name}已退` : item.name }}</div>
            <PriceDisplay :amountInFen="item.amount" />
          </div>
        </div>
      </div>

      <!-- 账单列表 -->
      <div class="mt-4">
        <div class="text-lg font-bold mb-4">订单信息</div>
        <el-table v-loading="loading" :data="mergedRecordList" @selection-change="handleSelectionChange" stripe class="!w-full" ref="recordTableRef">
          <el-table-column type="selection" min-width="5%" align="center" />
          <el-table-column label="订单编号" prop="billId" min-width="28%" align="center">
            <template #default="scope">
              <div>
                <span v-if="scope.row.direction === 'refund'" class="text-[#E23939]">[退]</span>
                <span v-if="scope.row.isFree" class="text-[#12AA58]">[赠]</span>
                <span>{{ formatBillId(scope.row.billId || '') }}</span>
              </div>
              <div class="text-xs text-gray-400 mt-1">{{ formatUnixTimestampToSimple(scope.row.finishTime || 0) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="employeeName" min-width="15%" align="center" />
          <el-table-column label="金额" prop="totalFee" align="center" min-width="52%">
            <template #default="scope">
              <!-- 退款记录只显示退款金额 -->
              <div v-if="scope.row.direction === 'refund'" class="flex justify-between items-center">
                <span class="text-gray-500">已退：</span>
                <span class="text-red-500 font-bold">¥ {{ (scope.row.totalFee || 0) / 100 }}</span>
              </div>

              <!-- 正常支付记录显示详细信息 -->
              <div v-else class="flex flex-col gap-1">
                <div class="flex justify-between">
                  <span class="text-gray-500">应收：</span>
                  <span>¥ {{ (scope.row.shouldFee || 0) / 100 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">实收：</span>
                  <span>¥ {{ (scope.row.totalFee || 0) / 100 }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">优惠金额：</span>
                  <span class="text-red-500">-¥ {{ Math.max((scope.row.shouldFee || 0) - (scope.row.totalFee || 0), 0) / 100 }}</span>
                </div>
                <!-- 支付方式列表 -->
                <div class="border-t border-gray-200 pt-1 mt-1">
                  <div v-for="(payment, idx) in scope.row.paymentMethods" :key="idx" class="flex justify-between mt-1">
                    <span class="text-gray-500">{{ getPayTypeText(payment.payType || '') }}：</span>
                    <span class="font-bold">¥ {{ (payment.amount || 0) / 100 }}</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="flex flex-col items-center justify-center">
        <button class="btn-default" @click="handleRestore" :disabled="!canRestore">账单还原</button>
        <div class="text-[14px] mt-[12px]">
          <span>已选择 </span>
          <span class="text-primary">{{ selectedRecords.length }}</span>
          <span> 笔账单，</span>
          <template v-if="selectedRecords.some((record: any) => record.isFree)">
            <span v-if="isRefundOperation">实退金额: </span>
            <span v-else>实收金额: </span>
            <span class="text-primary font-bold">¥ {{ Math.abs(selectedAmount) / 100 }}</span>
            <span v-if="selectedRecords.filter((record: any) => record.isFree).length > 0" class="text-green-600 ml-2">
              (含{{ selectedRecords.filter((record: any) => record.isFree).length }}笔免单)
            </span>
          </template>
          <template v-else>
            <span v-if="isRefundOperation">实退金额: </span>
            <span v-else>实收金额: </span>
            <span class="text-primary font-bold">¥ {{ Math.abs(selectedAmount) / 100 }}</span>
          </template>
        </div>
      </div>
    </template>
  </AppDialog>

  <!-- 确认还原对话框 -->
  <BillRestoreConfirmDialog
    v-model="showConfirmDialog"
    :amount="selectedAmount"
    :bill-ids="selectedBillIds"
    :session-id="sessionId"
    :pay-records="selectedRecords"
    :is-refund-operation="isRefundOperation"
    @confirm="handleConfirmRestore"
    @cancel="handleConfirmCancel" />
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, WritableComputedRef } from 'vue';
import { AppDialog } from '@/components/Dialog';
import BillRestoreConfirmDialog from './BillRestoreConfirmDialog.vue';
import { useOrderApi } from '@/modules/order/api/index';
import { formatDateTime } from '@/utils/dateUtils';
import type { BaseResponse } from '@/types/baseResponse';
import { PayBillVO, PayRecordVO } from '@/api/autoGenerated/shared/types';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { payTypeMap, PayType } from '@/utils/constant/payTyps';
import type { ElTable } from 'element-plus';
import { formatUnixTimestampToSimple } from '@/utils/dateUtils';

// 表格引用
const recordTableRef = ref<InstanceType<typeof ElTable>>();

// 接收props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  sessionId: {
    type: String,
    required: true
  },
  sessionStatus: {
    type: String,
    required: true
  }
});

// 定义emit
const emit = defineEmits(['update:modelValue', 'restore-success', 'error', 'close', 'confirm', 'cancel']);

// 组件状态
const loading = ref(false);
const billList = ref<PayBillVO[]>([]);
const payRecordList = ref<PayRecordVO[]>([]);
const selectedRecords = ref<any[]>([]); // 使用any类型以支持合并记录
const showConfirmDialog = ref(false);
const isInitialized = ref(false);
const employeeMap = ref<Record<string, string>>({});
// 添加选择处理标记，防止无限循环
const isSelectionHandling = ref(false);

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue || props.visible,
  set: (val: boolean) => {
    emit('update:modelValue', val);
  }
});

// API服务
const { billApi } = useOrderApi();

// 获取账单数据
const fetchBillData = async () => {
  if (!props.sessionId) return;

  loading.value = true;
  try {
    const response = await billApi.queryBillView({ sessionId: props.sessionId });
    if (response.code === 0 && response.data) {
      // 设置账单和支付记录数据
      billList.value = response.data.payBills || [];
      payRecordList.value = response.data.payRecords || [];

      // 构建员工映射
      const responseData = response.data as any;
      const employees = responseData.employeeVOs || [];
      employees.forEach((employee: any) => {
        if (employee && employee.id) {
          employeeMap.value[employee.id] = employee.name || '';
        }
      });

      console.log('[BillRestoreDetailDialog] 已获取账单数据:', {
        bills: billList.value,
        records: payRecordList.value,
        employees: employeeMap.value
      });
    } else {
      console.error('获取账单数据失败:', response.message);
      emit('error', new Error(response.message || '获取账单数据失败'));
    }
  } catch (error) {
    console.error('获取账单数据异常:', error);
    emit('error', error);
  } finally {
    loading.value = false;
  }
};

// 监听参数变化
watch(
  () => [dialogVisible, props.sessionId],
  async ([visible, sessionId]) => {
    console.log('[BillDetailDialog] 监听参数变化:', {
      visible,
      sessionId
    });

    if (visible && !isInitialized.value && sessionId) {
      try {
        isInitialized.value = true;
        await fetchBillData();
      } catch (error) {
        console.error('初始化账单数据失败:', error);
        emit('error', error);
      }
    }

    if (!visible) {
      // 关闭对话框时重置状态
      selectedRecords.value = [];
      isInitialized.value = false;
    }
  },
  { immediate: true }
);

// 监听确认对话框状态
watch(showConfirmDialog, newVal => {
  console.log('[BillDetailDialog] 确认对话框状态变化:', newVal);
});

// 格式化单号
const formatBillId = (billId: string) => {
  if (!billId) return '';
  return billId.length > 12 ? `${billId.substring(0, 6)}***${billId.substring(billId.length - 6)}` : billId;
};

// 构建合并的记录列表（将账单和支付记录合并）
const mergedRecordList = computed(() => {
  // 创建账单ID到支付记录的映射
  const paymentMap = new Map<string, PayRecordVO[]>();
  payRecordList.value.forEach((record: PayRecordVO) => {
    if (!paymentMap.has(record.billId || '')) {
      paymentMap.set(record.billId || '', []);
    }
    paymentMap.get(record.billId || '')?.push(record);
  });

  // 基于账单列表构建记录
  const allRecords = billList.value.map((bill: PayBillVO) => {
    // 获取关联的支付记录
    const payments = paymentMap.get(bill.billId || '') || [];

    // 构建支付方式列表
    const paymentMethods = payments.map(payment => ({
      payType: payment.payType || '',
      amount: payment.totalFee || 0,
      payId: payment.payId || '',
      id: payment.id || ''
    }));

    return {
      ...bill,
      id: bill.id || '',
      employeeName: employeeMap.value[bill.employeeId || ''] || bill.employeeId || '',
      // 支付记录信息
      paymentMethods: paymentMethods,
      // 添加退款关联关系所需字段
      payPid: bill.billPid ? payments[0]?.payPid || '' : '',
      payId: payments[0]?.payId || ''
    };
  });

  // 过滤掉isBack的记录
  return allRecords.filter(record => !record.isBack);
});

// 根据退款关系构建记录关系图
const recordRelationMap = computed(() => {
  const result = new Map<string, string[]>();

  // 建立 billPid -> bill.id 的映射关系
  mergedRecordList.value.forEach((record: any) => {
    if (record.billPid) {
      const parentRecords = mergedRecordList.value.filter((r: any) => r.billId === record.billPid);
      if (parentRecords.length > 0) {
        // 找到了父记录
        const parentId = parentRecords[0].id || '';
        if (parentId) {
          if (!result.has(parentId)) {
            result.set(parentId, []);
          }
          const childIds = result.get(parentId);
          if (childIds) {
            childIds.push(record.id || '');
          }
        }
      }
    }
  });

  return result;
});

// 计算应收金额 (考虑退款减法，排除isBack的账单)
const shouldFee = computed(() => {
  return billList.value
    .filter((bill: PayBillVO) => !bill.isBack) // 过滤掉isBack的账单
    .reduce((sum: number, bill: PayBillVO) => {
      const amount = bill.shouldFee || 0;
      // 如果是退款，则减去应收金额
      if (bill.direction === 'refund') {
        return sum - amount;
      }
      return sum + amount;
    }, 0);
});

// 计算总实收金额 (考虑退款减法，排除isBack的记录)
const totalReceived = computed(() => {
  return payRecordList.value
    .filter((record: PayRecordVO) => {
      // 查找对应的账单，检查是否为isBack
      const bill = billList.value.find((b: PayBillVO) => b.billId === record.billId);
      return !(bill && bill.isBack);
    })
    .reduce((sum: number, record: PayRecordVO) => {
      const amount = record.totalFee || 0;
      // 如果是退款记录（通过检查是否有payPid字段判断）
      if (record.payPid) {
        return sum - amount;
      }
      return sum + amount;
    }, 0);
});

// 计算已选金额
const selectedAmount = computed(() => {
  return selectedRecords.value.reduce((sum: number, record: any) => {
    const amount = record.totalFee || 0;
    // 如果是退款记录（通过检查是否有payPid字段判断）
    if (record.direction === 'refund') {
      return sum - amount; // 退款记录作为负值计入
    }
    return sum + amount;
  }, 0);
});

// 判断是否为退款操作（所有选中的记录都是退款记录）
const isRefundOperation = computed(() => {
  // 如果没有选中记录，默认为false
  if (selectedRecords.value.length === 0) return false;

  // 检查是否所有选中记录都是退款记录
  return selectedRecords.value.every((record: any) => record.direction === 'refund');
});

// 计算选中的账单ID列表
const selectedBillIds = computed(() => {
  // 所有记录的ID都需要被传递，不再过滤退款记录
  return selectedRecords.value.map((record: any) => record.billId || '').filter((id: string) => id !== '');
});

// 优惠列表
const discountList = computed(() => {
  const list = [];

  // 过滤排除isBack的账单
  const filteredBills = billList.value.filter((bill: PayBillVO) => !bill.isBack);

  // 房费优惠
  const roomDiscount = filteredBills.reduce((sum: number, bill: PayBillVO) => {
    const amount = bill.roomDiscountAmount || 0;
    if (bill.direction === 'refund') {
      return sum - amount;
    }
    return sum + amount;
  }, 0);

  if (roomDiscount > 0) {
    list.push({ name: '会员优惠', amount: roomDiscount });
  }

  // 商家优惠 (原始费用 - 应付费用)
  const merchantDiscount = filteredBills.reduce((sum: number, bill: PayBillVO) => {
    let discount = 0;
    if (bill.totalFee !== undefined && bill.shouldFee !== undefined) {
      discount = bill.shouldFee - bill.totalFee;
    }

    if (bill.direction === 'refund') {
      return sum - (discount > 0 ? discount : 0);
    }
    return sum + (discount > 0 ? discount : 0);
  }, 0);

  if (merchantDiscount > 0) {
    list.push({ name: '商家优惠', amount: merchantDiscount });
  }

  return list;
});

// 支付方式列表
const paymentList = computed(() => {
  const payments = new Map();

  // 过滤排除isBack的记录
  const filteredRecords = payRecordList.value.filter((record: PayRecordVO) => {
    // 查找对应的账单，检查是否为isBack
    const bill = billList.value.find((b: PayBillVO) => b.billId === record.billId);
    return !(bill && bill.isBack);
  });

  filteredRecords.forEach((record: PayRecordVO) => {
    const payType = getPayTypeText(record.payType || '');
    const amount = record.totalFee || 0;
    const isRefund = !!record.payPid;

    const key = `${payType}-${isRefund ? 'refund' : 'normal'}`;

    if (payments.has(key)) {
      payments.set(key, {
        ...payments.get(key),
        amount: payments.get(key).amount + amount
      });
    } else {
      payments.set(key, {
        name: payType,
        amount: amount,
        isRefund: isRefund
      });
    }
  });

  return Array.from(payments.values());
});

// 计算属性：是否可以还原
const canRestore = computed(() => {
  // 1. 检查sessionStatus是否为ending

  console.log('[BillRestoreDetailDialog] 计算属性：是否可以还原:', {
    sessionStatus: props.sessionStatus,
    selectedRecords: selectedRecords.value,
    selectedAmount: selectedAmount.value
  });

  const isEnding = props.sessionStatus !== 'ending';

  // 2. 检查是否有选中记录
  const hasSelectedRecords = selectedRecords.value.length > 0;

  // 3. 检查选中金额是否大于0，或者包含免单账单
  const hasValidAmount = Math.abs(selectedAmount.value) > 0 || selectedRecords.value.some((record: any) => record.isFree);
  console.log('[BillRestoreDetailDialog] 计算属性：是否可以还原:', {
    isEnding,
    hasSelectedRecords,
    hasValidAmount,
    hasFreeRecords: selectedRecords.value.some((record: any) => record.isFree)
  });

  return isEnding && hasSelectedRecords && hasValidAmount;
});

// 关闭对话框
const closeDialog = () => {
  emit('update:modelValue', false);
  emit('close');
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  // 如果正在处理选择，则直接返回
  if (isSelectionHandling.value) return;

  // 获取之前的选中项ID集合
  const previousSelectedIds = new Set(selectedRecords.value.map((item: any) => item.id));
  // 当前选中项ID集合
  const currentSelectedIds = new Set(selection.map(item => item.id));

  // 判断是选中操作还是取消选中操作
  // 找出被取消选中的项目
  const unselectedItems = [...previousSelectedIds].filter(id => !currentSelectedIds.has(id));
  // 找出新选中的项目
  const newlySelectedItems = [...currentSelectedIds].filter(id => !previousSelectedIds.has(id));

  // 更新选择状态
  selectedRecords.value = selection;

  // 联动选择逻辑
  nextTick(() => {
    // 使用最新的选中项集合
    const selectedIds = new Set(selection.map(item => item.id));
    const newSelection = new Set(selectedIds);
    let selectionChanged = false;

    // 处理取消选择的联动逻辑（自动取消关联项）
    if (unselectedItems.length > 0) {
      unselectedItems.forEach(unselectedId => {
        const record = mergedRecordList.value.find(r => r.id === unselectedId);
        if (!record) return;

        // 如果取消选择了父记录，也取消选择其所有子记录
        const childIds = recordRelationMap.value.get(unselectedId) || [];
        childIds.forEach(childId => {
          if (newSelection.has(childId)) {
            newSelection.delete(childId);
            selectionChanged = true;
          }
        });

        // 如果取消选择了子记录，检查是否应该取消选择父记录
        if (record.payPid) {
          const parentId = findParentId(record);
          if (!parentId) return;

          // 检查该父记录的所有子记录是否都已取消选择
          const allChildIds = recordRelationMap.value.get(parentId) || [];
          const hasSelectedChild = allChildIds.some(childId => childId !== unselectedId && newSelection.has(childId));

          // 如果没有子记录被选中，且父记录已被选中，则取消选择父记录
          if (!hasSelectedChild && newSelection.has(parentId)) {
            newSelection.delete(parentId);
            selectionChanged = true;
          }
        }
      });
    }

    // 处理新选中的联动逻辑（自动选中关联项）
    if (newlySelectedItems.length > 0) {
      newlySelectedItems.forEach(selectedId => {
        const record = mergedRecordList.value.find(r => r.id === selectedId);
        if (!record) return;

        // 如果选中了退款记录，也选中其父记录
        if (record.payPid) {
          const parentId = findParentId(record);
          if (parentId && !newSelection.has(parentId)) {
            newSelection.add(parentId);
            selectionChanged = true;
          }
        }

        // 如果选中了有子记录的记录，也选中其所有子记录
        const childIds = recordRelationMap.value.get(selectedId) || [];
        childIds.forEach(childId => {
          if (!newSelection.has(childId)) {
            newSelection.add(childId);
            selectionChanged = true;
          }
        });
      });
    }

    // 如果选择发生了变化，更新表格选择
    if (selectionChanged) {
      const table = recordTableRef.value;
      if (table) {
        try {
          // 设置标记，防止无限循环
          isSelectionHandling.value = true;

          // 清除当前选择
          table.clearSelection();

          // 重新选择所有应选项
          mergedRecordList.value.forEach((record: any) => {
            if (newSelection.has(record.id)) {
              table.toggleRowSelection(record, true);
            }
          });

          // 更新选择结果
          selectedRecords.value = mergedRecordList.value.filter((record: any) => newSelection.has(record.id || ''));
        } finally {
          // 确保总是重置标记
          setTimeout(() => {
            isSelectionHandling.value = false;
          }, 0);
        }
      }
    }
  });
};

// 辅助函数：查找记录的父记录ID
const findParentId = (record: any): string => {
  if (!record || !record.billPid) return '';

  const parentRecords = mergedRecordList.value.filter((r: any) => r.billId === record.billPid);
  if (parentRecords.length > 0) {
    return parentRecords[0].id || '';
  }

  return '';
};

// 处理还原操作
const handleRestore = () => {
  if (selectedRecords.value.length === 0) return;
  showConfirmDialog.value = true;
};

// 确认还原操作
const handleConfirmRestore = () => {
  showConfirmDialog.value = false;
  emit('restore-success');
  closeDialog();
};

// 处理确认对话框取消
const handleConfirmCancel = () => {
  showConfirmDialog.value = false;
};

// 获取支付方式文本
const getPayTypeText = (payType: string) => {
  return payTypeMap[payType as PayType] || payType;
};
</script>

<style scoped>
.bill-detail-dialog :deep(.el-dialog__body) {
  padding: 0;
}
</style>
