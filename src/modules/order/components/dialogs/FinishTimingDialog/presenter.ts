import { ref, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import type { IFinishTimingDialogViewModel, IFinishTimingDialogState } from './viewmodel';
import { FINISH_TIMING_DIALOG_STATE_DEFAULTS } from './viewmodel';
import { FinishTimingDialogInteractor } from './interactor';
import { FinishTimingDialogConverter } from './converter';
import * as PriceModel from '@/modules/room/components/HourlyBilling/PriceModel';
import type { PriceTypeKey } from '@/modules/room/components/HourlyBilling/viewmodel';

/**
 * FinishTimingDialog Presenter层
 * 负责协调视图和业务逻辑
 */
export class FinishTimingDialogPresenter implements IFinishTimingDialogViewModel {
  // 组件状态
  public state = reactive<IFinishTimingDialogState>({
    ...FINISH_TIMING_DIALOG_STATE_DEFAULTS
  });

  // 私有属性 - 业务交互与数据转换器
  private interactor: FinishTimingDialogInteractor;
  private converter: FinishTimingDialogConverter;

  // 缓存的价格选项数据
  private cachedPriceOptions: PriceModel.PriceOption[] = [];
  private originalOrderData: any = null;

  // 计算属性
  public computed = {
    // 房间名称
    roomName: computed(() => {
      return this.state.roomName || '--';
    }),

    // 开始时间格式化
    startTimeFormatted: computed(() => {
      if (!this.state.startTime) return '--';
      return dayjs.unix(this.state.startTime).format('YY-MM-DD HH:mm');
    }),

    // 房间消费时间
    roomConsumptionTime: computed(() => {
      if (!this.state.startTime || !this.state.endTime) return '';
      return this.converter.formatTimeRange(this.state.startTime, this.state.endTime);
    }),

    // 格式化总时长
    formattedDuration: computed(() => {
      return this.converter.formatDuration(this.state.duration);
    }),

    // 格式化总金额
    formattedTotalAmount: computed(() => {
      return this.converter.formatAmount(this.state.totalAmount);
    }),

    // 当前系统时间格式化为HH:mm
    currentTimeFormatted: computed(() => {
      return dayjs().format('HH:mm');
    })
  };

  // 动作
  public actions = {
    // 弹窗开启
    onDialogOpen: () => {
      console.log('FinishTimingDialog 已打开');
      // 只设置当前时间作为默认的自定义结束时间
      this.state.customEndTime = this.computed.currentTimeFormatted.value;
    },

    // 弹窗关闭
    onDialogClose: () => {
      console.log('FinishTimingDialog 已关闭');
      this.state.isInitialized = false;
      this.state.useCustomEndTime = false;
      this.state.customEndTime = '';
      // 清除缓存数据
      this.cachedPriceOptions = [];
      this.originalOrderData = null;
    },

    // 初始化数据
    init: async (sessionId: string, roomId: string, roomTypeId: string, areaId: string, roomName: string) => {
      try {
        console.log('FinishTimingDialog init：', sessionId, roomId, roomTypeId, areaId, roomName);
        this.state.loading = true;

        // 通过sessionId获取订单数据
        const orderData = await this.interactor.getOrderBySession(sessionId);

        // 设置当前时间作为默认的自定义结束时间
        const currentTime = dayjs().format('HH:mm');
        this.state.customEndTime = currentTime;

        // 缓存原始订单数据
        this.originalOrderData = { ...orderData };

        // 获取价格选项数据 - 只需要获取一次
        const priceData = await this.interactor.getOpenViewRoom(sessionId, roomId, roomTypeId, areaId);

        if (priceData) {
          // 提取价格选项并缓存（保持与小时计费一致的方案筛选）
          this.cachedPriceOptions = this.interactor.extractPriceOptions(priceData, areaId, roomTypeId, null, dayjs().unix());
          console.log('[FinishTimingDialog] 提取并缓存价格选项:', this.cachedPriceOptions);
        }

        // 使用本地计算得到处理后的订单数据
        const processedOrderData = this.interactor.calculatePriceLocally(this.originalOrderData, this.cachedPriceOptions, currentTime);

        if (!processedOrderData) {
          throw new Error('[FinishTimingDialog] 获取订单数据失败');
        }

        // 转换为视图模型数据
        const viewModelData = this.converter.toViewModel(processedOrderData);

        // 更新状态 - 保持基础数据不被覆盖
        const baseData = {
          sessionId,
          roomId,
          roomTypeId,
          areaId,
          roomName,
          customEndTime: currentTime
        };

        // 先设置基础数据
        Object.assign(this.state, baseData);

        // 再设置其他数据，但不覆盖基础数据
        Object.assign(this.state, {
          ...viewModelData,
          ...baseData // 确保基础数据不被覆盖
        });

        this.state.isInitialized = true;
        console.log('FinishTimingDialog 数据初始化完成：', this.state);
      } catch (error) {
        console.error('FinishTimingDialog 初始化失败:', error);
        ElMessage.error('初始化失败: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        this.state.loading = false;
      }
    },

    // 切换是否使用自定义结束时间
    toggleCustomEndTime: (value: boolean) => {
      this.state.useCustomEndTime = value;
      console.log('切换自定义结束时间:', value);
    },

    // 更新自定义结束时间
    updateCustomEndTime: async (time: string) => {
      try {
        this.state.customEndTime = time;
        console.log('更新自定义结束时间:', time);

        // 使用本地计算更新订单数据
        if (this.originalOrderData && this.cachedPriceOptions.length > 0) {
          this.state.loading = true;

          // 使用本地计算方法计算新的订单数据
          const updatedOrderData = this.interactor.calculatePriceLocally(this.originalOrderData, this.cachedPriceOptions, time);

          // 转换并更新视图模型数据
          const viewModelData = this.converter.toViewModel(updatedOrderData);

          // 保持基础数据不变，只更新需要更新的字段
          const baseData = {
            sessionId: this.state.sessionId,
            roomId: this.state.roomId,
            roomTypeId: this.state.roomTypeId,
            areaId: this.state.areaId,
            roomName: this.state.roomName,
            customEndTime: time
          };

          // 更新状态
          Object.assign(this.state, {
            ...viewModelData,
            ...baseData // 确保基础数据不被覆盖
          });

          console.log('使用自定义结束时间更新订单数据完成:', this.state);
        } else {
          console.warn('缺少必要数据，无法更新价格计算:', {
            hasOriginalOrderData: !!this.originalOrderData,
            cachedPriceOptionsCount: this.cachedPriceOptions.length
          });
        }
      } catch (error) {
        console.error('更新自定义结束时间失败:', error);
        ElMessage.error('更新自定义结束时间失败: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        this.state.loading = false;
      }
    },

    // 处理结束计时
    handleFinishTiming: async () => {
      try {
        if (!this.state.orderRoomPlanVOS || this.state.orderRoomPlanVOS.length === 0) {
          throw new Error('没有可结束的计时项目');
        }

        this.state.loading = true;

        // 确保使用最新的自定义结束时间计算
        if (this.state.customEndTime) {
          await this.actions.updateCustomEndTime(this.state.customEndTime);
        }

        // 准备请求参数
        const params = this.converter.prepareFinishTimingParams(this);

        // 调用结束计时API
        const result = await this.interactor.finishTiming(params);

        // 处理成功
        ElMessage.success('结束计时成功');

        // 返回结果
        return {
          data: result,
          success: true
        };
      } catch (error) {
        // 用户取消操作，不显示错误
        if (error === 'cancel' || (error instanceof Error && error.message === 'cancel')) {
          console.log('用户取消了结束计时操作');
          return null;
        }

        console.error('结束计时失败:', error);
        ElMessage.error('结束计时失败: ' + (error instanceof Error ? error.message : String(error)));
        throw error;
      } finally {
        this.state.loading = false;
      }
    },

    // 处理取消按钮点击
    handleCancel: () => {
      if (this.state.onCancel) {
        this.state.onCancel();
      }
    }
  };

  // 构造函数
  constructor() {
    this.interactor = new FinishTimingDialogInteractor();
    this.converter = new FinishTimingDialogConverter();
  }
}

// 导出组合式函数
export function useFinishTimingDialog(): IFinishTimingDialogViewModel {
  return new FinishTimingDialogPresenter();
}
