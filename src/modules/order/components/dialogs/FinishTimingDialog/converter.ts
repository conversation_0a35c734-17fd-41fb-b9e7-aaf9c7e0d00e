import { cloneDeep } from 'lodash-es';
import dayjs from 'dayjs';
import { FinishTimingData, OrderTimingData } from './finishTimingEntity';
import type { IFinishTimingDialogViewModel } from './viewmodel';
import { OrderRoomPlanVO } from '@/types/projectobj';

/**
 * FinishTimingDialog 数据转换层
 * 负责业务数据和视图数据的转换
 */
export class FinishTimingDialogConverter {
  /**
   * 从会话数据转换为视图模型
   * @param sessionData 会话数据
   * @returns 视图模型数据
   */
  toViewModel(sessionData: OrderTimingData | null): Partial<IFinishTimingDialogViewModel['state']> {
    if (!sessionData) {
      return {
        orderData: null,
        orderRoomPlanVOS: []
      };
    }

    // 进行深拷贝，确保不改变原始数据
    const data = cloneDeep(sessionData);

    // 提取房间计时计划数据，并转换为FinishTimingData类型
    const timeConsumePlans = data.orderRoomPlanVOS
      .filter(plan => plan.isTimeConsume === true)
      .map(
        plan =>
          ({
            id: plan.id || '',
            orderNo: plan.orderNo || '',
            roomId: plan.roomId || '',
            roomName: plan.roomName || '',
            sessionId: plan.sessionId || '',
            venueId: plan.venueId || '',
            startTime: plan.startTime || 0,
            endTime: plan.endTime || 0,
            duration: plan.duration || 0,
            payAmount: plan.payAmount || 0,
            originalPayAmount: plan.originalAmount || plan.payAmount || 0,
            unitPrice: plan.payAmount && plan.duration ? Math.round(plan.payAmount / plan.duration) : 0,
            pricePlanId: plan.pricePlanId || '',
            pricePlanName: plan.pricePlanName || '',
            isTimeConsume: true,
            state: plan.state || 0,
            version: plan.version || 0,
            ctime: plan.ctime || 0,
            utime: plan.utime || 0
          }) as FinishTimingData
      );

    // 计算总价格和时长
    const totalAmount = timeConsumePlans.reduce((sum, plan) => sum + (plan.payAmount || 0), 0);
    const duration = timeConsumePlans.reduce((sum, plan) => sum + (plan.duration || 0), 0);

    // 获取第一个计时消费记录的开始时间
    const startTime = timeConsumePlans[0]?.startTime || 0;

    // 返回转换后的视图模型数据
    return {
      orderData: data,
      orderRoomPlanVOS: timeConsumePlans,
      roomId: data.roomId || '',
      roomName: data.roomName || '',
      sessionId: data.sessionId || '',
      venueId: data.venueId || '',
      startTime: startTime, // 使用计算得到的最早开始时间
      endTime: data.endTime || 0,
      duration: duration,
      totalAmount: totalAmount,
      pricePlanName: data.pricePlanName || '计时消费'
    };
  }

  /**
   * 准备提交给结束计时API的数据
   * @param viewModel 视图模型
   * @returns 请求参数
   */
  prepareFinishTimingParams(viewModel: IFinishTimingDialogViewModel): {
    roomId: string;
    sessionId: string;
    orderRoomPlanVOS: FinishTimingData[];
  } {
    // 准备结束计时的参数
    return {
      roomId: viewModel.state.roomId,
      sessionId: viewModel.state.sessionId,
      orderRoomPlanVOS: viewModel.state.orderRoomPlanVOS
    };
  }

  /**
   * 格式化消费时间范围
   * @param startTime 开始时间戳
   * @param endTime 结束时间戳
   * @returns 格式化的时间范围字符串
   */
  formatTimeRange(startTime: number, endTime: number): string {
    if (!startTime || !endTime) return '';

    const startDate = dayjs.unix(startTime);
    const endDate = dayjs.unix(endTime);

    return `${startDate.format('YY-MM-DD HH:mm')} ~ ${endDate.format('YY-MM-DD HH:mm')}`;
  }

  /**
   * 格式化持续时间
   * @param minutes 分钟数
   * @returns 格式化的持续时间字符串
   */
  formatDuration(minutes: number): string {
    if (minutes < 0) return '0分钟';

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours > 0) {
      return `${hours}小时${remainingMinutes > 0 ? ` ${remainingMinutes}分钟` : ''}`;
    }

    return `${remainingMinutes}分钟`;
  }

  /**
   * 格式化金额
   * @param amount 金额（单位：分）
   * @returns 格式化的金额字符串
   */
  formatAmount(amount: number): string {
    if (amount === undefined || amount === null) return '¥0.00';

    // 将分转换为元，并格式化为两位小数
    const yuan = amount / 100;
    return `¥${yuan.toFixed(2)}`;
  }
}
