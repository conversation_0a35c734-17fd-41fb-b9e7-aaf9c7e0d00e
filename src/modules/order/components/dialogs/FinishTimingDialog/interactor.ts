import { OrderApi, FinishTimingRequest } from '@/modules/order/api/order';
import { ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash-es';
import { RoomApi } from '@/modules/room/api/room';
import { now10 } from '@/utils/dateUtils';
import { useDeviceStore } from '@/stores/deviceStore';
import request from '@/utils/request';
import { BaseResponse } from '@/types/baseResponse';
import { FinishTimingData, PriceDetail } from './finishTimingEntity';

// 导入价格计算相关模块
import * as PriceModel from '@/modules/room/components/HourlyBilling/PriceModel';
import type { PriceTypeKey } from '@/modules/room/components/HourlyBilling/viewmodel';
import { HourlyBillingInteractor } from '@/modules/room/components/HourlyBilling/interactor';

/**
 * FinishTimingDialog 业务交互层
 * 处理与API的交互和业务逻辑
 */
export class FinishTimingDialogInteractor {
  /**
   * 通过场次ID获取订单数据
   * @param sessionId 场次ID
   * @returns 订单数据
   */
  async getOpenViewRoom(sessionId: string, roomId: string, roomTypeId: string, areaId: string) {
    try {
      console.log('通过sessionId获取订单数据:', sessionId, roomTypeId, roomId, areaId);
      // 调用API获取订单数据
      const response = await RoomApi.openViewRoom({ sessionId: sessionId, roomId: roomId, typeId: roomTypeId, areaId: areaId });

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '获取订单数据失败');
      }

      console.log('获取到订单数据:', response.data);

      // 对获取到的数据进行预处理，提取计时相关信息
      const processedData = await this.processOrderData(response.data);

      return processedData;
    } catch (error) {
      console.error('获取订单数据失败:', error);
      ElMessage.error('获取订单数据失败: ' + (error instanceof Error ? error.message : String(error)));
      return null;
    }
  }

  /**
   * 更新订单计划的结束时间
   * @param orderPlans 订单计划数据
   * @param customEndTime 自定义结束时间(格式: HH:mm)
   * @returns 更新后的订单计划数据
   */
  async updateOrderPlanEndTime(orderPlans: FinishTimingData[], customEndTime: string): Promise<FinishTimingData[]> {
    try {
      if (!orderPlans || orderPlans.length === 0 || !customEndTime) {
        return orderPlans;
      }

      console.log('[finishtiming] 开始使用自定义结束时间更新计划:', customEndTime);

      // 克隆数据以避免修改原始数据
      const clonedPlans = cloneDeep(orderPlans);

      // 将时间字符串转换为当天的时间戳
      const today = new Date();
      const [hours, minutes] = customEndTime.split(':').map(Number);
      const customDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, 0);
      const customTimestamp = Math.floor(customDate.getTime() / 1000);

      console.log(`[finishtiming] 自定义结束时间戳: ${customTimestamp}`);

      // 更新每个计划的结束时间
      for (const plan of clonedPlans) {
        const startTime = plan.startTime;
        // 确保结束时间不早于开始时间
        if (customTimestamp <= startTime) {
          console.warn('[finishtiming] 自定义结束时间早于开始时间，将使用开始时间作为结束时间');
          plan.endTime = startTime;
          plan.duration = 0;
          plan.payAmount = 0;
        } else {
          plan.endTime = customTimestamp;
          // 重新计算持续时间（分钟）
          plan.duration = Math.floor((customTimestamp - startTime) / 60);
          // 如果有单价，重新计算费用
          if (plan.unitPrice > 0) {
            plan.payAmount = plan.unitPrice * plan.duration;
          }
        }
      }

      // 如果需要重新计算价格方案，可以在这里调用价格计算函数
      // 但这需要获取价格方案数据，这超出了当前功能的范围
      // 这里简化处理，仅根据单价和新的持续时间计算费用

      console.log('[finishtiming] 更新后的计划:', clonedPlans);
      return clonedPlans;
    } catch (error) {
      console.error('[finishtiming] 更新自定义结束时间失败:', error);
      return orderPlans; // 发生错误时返回原始数据
    }
  }

  /**
   * 对从API获取的订单数据进行预处理
   * @param data API返回的原始数据
   * @returns 处理后的数据
   */
  private async processOrderData(data: any) {
    if (!data) return null;

    console.log('[finishtiming] 预处理订单数据开始:', data);

    // 处理计时消费
    const hasTimeConsume = data.orderRoomPlanVOS?.some((plan: any) => plan.isTimeConsume === true);
    if (hasTimeConsume) {
      console.log('[finishtiming] 检测到计时消费订单，需要重新计算房费');
      return await this.processTimeConsumeOrderPlans(data);
    } else {
      console.log('[finishtiming] 非计时消费订单，无需特殊处理');
      return data;
    }
  }

  /**
   * 获取房间开台价格方案
   * @param roomId 房间ID
   * @returns 价格方案数据
   */
  async getOrderBySession(sessionId: string) {
    try {
      // 调用开台接口获取价格方案
      const response = await OrderApi.queryOpenOrder({ sessionId });

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '获取价格方案失败');
      }

      console.log('获取到价格方案数据:', response.data);
      return response.data;
    } catch (error) {
      console.error('获取价格方案失败:', error);
      return null;
    }
  }

  /**
   * 本地计算价格方案和费用，避免重复请求API
   * @param orderData 订单数据
   * @param priceOptions 价格选项数据
   * @param customEndTime 自定义结束时间(格式: HH:mm)
   * @returns 处理后的订单数据
   */
  calculatePriceLocally(orderData: any, priceOptions: PriceModel.PriceOption[], customEndTime: string): any {
    try {
      console.log('[finishtiming] 开始本地计算价格:', { customEndTime, priceOptionsCount: priceOptions?.length || 0 });

      if (!orderData || !priceOptions || priceOptions.length === 0) {
        console.warn('[finishtiming] 缺少必要数据，无法计算价格');
        return orderData;
      }

      // 将时间字符串转换为时间戳，处理跨天情况
      const [hours, minutes] = customEndTime.split(':').map(Number);

      // 获取计时消费记录的起始时间戳
      const timeConsumePlan = orderData.orderRoomPlanVOS?.find((plan: any) => plan.isTimeConsume === true);
      const startTimestamp = timeConsumePlan?.startTime || 0;
      const startDate = new Date(startTimestamp * 1000);
      const startHours = startDate.getHours();
      const startMinutes = startDate.getMinutes();

      // 基于开始时间的日期构建结束时间
      let customDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), hours, minutes, 0);

      // 如果结束时间早于开始时间，认为是跨天，将日期加1天
      if (hours < startHours || (hours === startHours && minutes < startMinutes)) {
        customDate.setDate(customDate.getDate() + 1);
        console.log(
          `[finishtiming] 检测到跨天情况: 结束时间 ${customEndTime} 早于开始时间 ${startHours}:${startMinutes.toString().padStart(2, '0')}, 将日期加1天`
        );
      }

      const endTimestamp = Math.floor(customDate.getTime() / 1000);
      console.log(`[finishtiming] 自定义结束时间戳: ${endTimestamp}, 日期: ${customDate.toLocaleString()}`);

      // 调用共享的计算方法
      return this.calculateOrderPlans(orderData, priceOptions, endTimestamp);
    } catch (error) {
      console.error('[finishtiming] 本地计算价格失败:', error);
      return orderData;
    }
  }

  /**
   * 处理计时消费订单
   * @param orderData 订单数据
   * @param customEndTime 自定义结束时间(格式: HH:mm)，如果为空则使用当前时间
   * @returns 处理后的订单数据
   */
  async processTimeConsumeOrderPlans(orderData: any, customEndTime?: string) {
    try {
      console.log('[finishtiming] 开始处理计时消费订单:', orderData);

      // 找出计时消费计划的相关信息
      const timeConsumePlans = this.findTimeConsumePlans(orderData);
      if (!timeConsumePlans || timeConsumePlans.length === 0) {
        console.log('[finishtiming] 未检测到计时消费房间计划');
        return orderData;
      }

      // 获取结束时间戳
      let endTimestamp: number;
      if (customEndTime) {
        // 将时间字符串转换为时间戳，处理跨天情况
        const [hours, minutes] = customEndTime.split(':').map(Number);

        // 获取计时消费记录的起始时间戳
        const startTime = timeConsumePlans[0]?.startTime || 0;
        const startDate = new Date(startTime * 1000);
        const startHours = startDate.getHours();
        const startMinutes = startDate.getMinutes();

        // 基于开始时间的日期构建结束时间
        let customDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), hours, minutes, 0);

        // 如果结束时间早于开始时间，认为是跨天，将日期加1天
        if (hours < startHours || (hours === startHours && minutes < startMinutes)) {
          customDate.setDate(customDate.getDate() + 1);
          console.log(
            `[finishtiming] 检测到跨天情况: 结束时间 ${customEndTime} 早于开始时间 ${startHours}:${startMinutes.toString().padStart(2, '0')}, 将日期加1天`
          );
        }

        endTimestamp = Math.floor(customDate.getTime() / 1000);
        console.log(`[finishtiming] 使用自定义结束时间: ${customEndTime}, 时间戳: ${endTimestamp}, 日期: ${customDate.toLocaleString()}`);
      } else {
        endTimestamp = now10();
        console.log(`[finishtiming] 使用当前系统时间: ${new Date(endTimestamp * 1000).toLocaleTimeString()}`);
      }

      // 获取并处理价格方案
      const firstPlan = timeConsumePlans[0];
      const priceData = await this.getOpenViewRoom(firstPlan.sessionId, firstPlan.roomId, firstPlan.selectedRoomTypeId, firstPlan.selectedAreaId);

      if (!priceData) {
        console.warn('[finishtiming] 无法获取价格方案数据');
        return orderData;
      }

      // 提取价格选项
      const priceOptions = this.extractPriceOptions(priceData, firstPlan.selectedAreaId, firstPlan.selectedRoomTypeId, null, endTimestamp);
      if (!priceOptions || priceOptions.length === 0) {
        console.warn('[finishtiming] 无有效价格选项');
        return orderData;
      }

      // 使用共享的计算方法
      return this.calculateOrderPlans(orderData, priceOptions, endTimestamp);
    } catch (error) {
      console.error('[finishtiming] 处理计时消费失败:', error);
      return orderData;
    }
  }

  /**
   * 查找订单中的计时消费计划
   * @param orderData 订单数据
   * @returns 计时消费计划数组
   */
  private findTimeConsumePlans(orderData: any): any[] {
    // 1. 过滤出未结的订单
    const unpaidOrders = orderData.orderVOS?.filter((order: any) => order.state === 0) || [];
    console.log('[finishtiming] 未结订单:', unpaidOrders.length);

    // 2. 获取未结订单的订单号
    const unpaidOrderNos = unpaidOrders.map((order: any) => order.orderNo);

    // 3. 过滤出与未结订单相关的房间计划
    const relevantPlans = orderData.orderRoomPlanVOS?.filter((plan: any) => unpaidOrderNos.includes(plan.orderNo)) || [];

    // 4. 找出计时消费计划
    return relevantPlans.filter((plan: any) => plan.isTimeConsume === true);
  }

  /**
   * 计算订单价格和计划详情 - 共享计算逻辑
   * @param orderData 订单数据
   * @param priceOptions 价格选项
   * @param endTimestamp 结束时间戳
   * @returns 处理后的订单数据
   */
  private calculateOrderPlans(orderData: any, priceOptions: PriceModel.PriceOption[], endTimestamp: number): any {
    // 1. 找出计时消费计划
    const timeConsumePlans = this.findTimeConsumePlans(orderData);
    if (!timeConsumePlans || timeConsumePlans.length === 0) {
      console.log('[finishtiming] 未检测到计时消费房间计划');
      return orderData;
    }

    console.log('[finishtiming] 计时消费计划数量:', timeConsumePlans.length);

    // 2. 获取未结订单的订单号
    const unpaidOrders = orderData.orderVOS?.filter((order: any) => order.state === 0) || [];
    const unpaidOrderNos = unpaidOrders.map((order: any) => order.orderNo);

    // 3. 处理每个计时消费计划，创建新的计划
    const updatedPlans: any[] = [];
    for (const plan of timeConsumePlans) {
      const startTime = plan.startTime;

      // 确保结束时间不早于开始时间
      let currentTime = endTimestamp;
      let durationMinutes = Math.floor((currentTime - startTime) / 60);

      // 如果持续时间为负数（可能是跨天导致），直接使用endTimestamp，代码前面已处理跨天
      if (durationMinutes < 0) {
        console.log(`[finishtiming] 检测到可能跨天情况 - 开始时间戳: ${startTime}, 结束时间戳: ${endTimestamp}`);
        // 保持使用endTimestamp，前面方法已经处理了跨天情况
      }

      // 重新计算持续时间
      durationMinutes = Math.max(0, Math.floor((currentTime - startTime) / 60));
      let priceResult = null;

      if (durationMinutes <= 0) {
        priceResult = {
          details: [],
          totalPrice: 0
        };
      } else {
        // 转换为时间格式
        const startTimeStr = this.formatTimeString(new Date(startTime * 1000));
        const endTimeStr = this.formatTimeString(new Date(currentTime * 1000));

        console.log(`[finishtiming] 计算计时消费: 从 ${startTimeStr} 到 ${endTimeStr}, 持续 ${durationMinutes} 分钟`);

        // 计算价格
        priceResult = PriceModel.calculatePrice(startTimeStr, endTimeStr, priceOptions, orderData.roomVO?.baseTimePriceFee);
      }

      console.log('[finishtiming] 价格计算结果:', priceResult);

      // 为每个价格明细创建一个新的计划
      const newPlans = priceResult.details.map((detail: any) => {
        // 确定是否是跨天情况
        const startDate = new Date(startTime * 1000);
        const endDate = new Date(currentTime * 1000);
        const isOverallCrossDay =
          currentTime > startTime &&
          (endDate.getHours() < startDate.getHours() || (endDate.getHours() === startDate.getHours() && endDate.getMinutes() < startDate.getMinutes()));

        // 获取基准日期部分
        const startDateStr = startDate.toISOString().split('T')[0];

        // 解析当前时间段的开始和结束时间
        const [segmentStartHour, segmentStartMinute] = detail.startTime.split(':').map(Number);
        const [segmentEndHour, segmentEndMinute] = detail.endTime.split(':').map(Number);
        
        // 解析整体结束时间
        const [overallEndHour, overallEndMinute] = [endDate.getHours(), endDate.getMinutes()];
        
        // 创建时间段的开始时间戳
        let segmentStartDateObj = new Date(`${startDateStr}T${detail.startTime}:00`);
        
        // 创建时间段的结束时间戳
        let segmentEndDateObj = new Date(`${startDateStr}T${detail.endTime}:00`);

        if (isOverallCrossDay) {
          // 对于跨天场景，需要判断时间段是发生在当天还是次日
          
          // 如果时间段的开始时间早于整体结束时间，说明这个时间段发生在次日
          if (segmentStartHour < overallEndHour || (segmentStartHour === overallEndHour && segmentStartMinute < overallEndMinute)) {
            console.log(`[finishtiming] 时间段 ${detail.startTime}-${detail.endTime} 发生在次日，因为开始时间 ${segmentStartHour}:${segmentStartMinute} 早于整体结束时间 ${overallEndHour}:${overallEndMinute}`);
            segmentStartDateObj.setDate(segmentStartDateObj.getDate() + 1);
            segmentEndDateObj.setDate(segmentEndDateObj.getDate() + 1);
          }
          // 如果时间段内部跨天（开始时间晚于结束时间），结束时间需要加一天
          else if (segmentEndHour < segmentStartHour || (segmentEndHour === segmentStartHour && segmentEndMinute < segmentStartMinute)) {
            console.log(`[finishtiming] 时间段 ${detail.startTime}-${detail.endTime} 内部跨天，结束时间设为次日`);
            segmentEndDateObj.setDate(segmentEndDateObj.getDate() + 1);
          }
          // 特殊处理：如果时间段的结束时间是00:00，应该是次日的00:00
          else if (segmentEndHour === 0 && segmentEndMinute === 0) {
            console.log(`[finishtiming] 时间段 ${detail.startTime}-${detail.endTime} 结束于00:00，设为次日`);
            segmentEndDateObj.setDate(segmentEndDateObj.getDate() + 1);
          } else {
            console.log(`[finishtiming] 时间段 ${detail.startTime}-${detail.endTime} 发生在当天`);
          }
        }

        const priceFen = Math.round(detail.price * 100); // 元 → 分
        return {
          // 基础信息
          id: '', // 新计划ID为空
          orderNo: plan.orderNo,
          roomId: plan.roomId,
          roomName: plan.roomName,
          sessionId: plan.sessionId,
          venueId: plan.venueId,

          // 时间信息 - 使用修正后的时间戳
          startTime: Math.floor(segmentStartDateObj.getTime() / 1000),
          endTime: Math.floor(segmentEndDateObj.getTime() / 1000),
          duration: detail.duration,

          // 价格信息
          payAmount: priceFen,
          originalPayAmount: priceFen,
          unitPrice: Math.round(priceFen / detail.duration),

          // 方案信息
          pricePlanId: plan.pricePlanId,
          pricePlanName: detail.planName,
          pricePlanType: plan.pricePlanType,
          isTimeConsume: true,

          // 其他信息
          state: plan.state,
          version: plan.version,
          ctime: plan.ctime,
          utime: Math.floor(Date.now() / 1000),

          // 特殊字段标记
          selectedRoomTypeId: plan.selectedRoomTypeId,
          selectedAreaId: plan.selectedAreaId
        };
      });

      updatedPlans.push(...newPlans);
    }

    // 4. 更新orderRoomPlanVOS
    const finalOrderRoomPlanVOS = orderData.orderRoomPlanVOS
      .map((plan: any) => {
        // 如果是计时消费计划，替换为新的计划
        if (plan.isTimeConsume && unpaidOrderNos.includes(plan.orderNo)) {
          return updatedPlans.filter((newPlan: any) => newPlan.orderNo === plan.orderNo);
        }
        return plan;
      })
      .flat(); // 展平数组，因为有些元素可能是数组

    // 5. 重新计算总费用
    const newRoomFee = finalOrderRoomPlanVOS.reduce((sum: number, plan: any) => sum + (plan.payAmount || 0), 0);
    const newTotalAmount = newRoomFee + (orderData.productAmount || 0);
    const newDuration = finalOrderRoomPlanVOS.reduce((sum: number, plan: any) => sum + (plan.duration || 0), 0);
    const newEndTime = Math.max(...finalOrderRoomPlanVOS.map((plan: any) => plan.endTime)) || orderData.endTime;

    console.log(`[finishtiming] 更新后房费: ${newRoomFee}, 商品费: ${orderData.productAmount || 0}, 总费用: ${newTotalAmount}`);

    // 6. 更新场次数据
    const updatedSessionVO = orderData.sessionVO
      ? {
          ...orderData.sessionVO,
          roomFee: newRoomFee,
          duration: newDuration,
          endTime: newEndTime
        }
      : null;

    // 7. 返回更新后的订单数据
    const updatedOrderData = {
      ...orderData,
      orderRoomPlanVOS: finalOrderRoomPlanVOS,
      roomFee: newRoomFee,
      roomAmount: newRoomFee,
      totalAmount: newTotalAmount,
      actualAmount: newTotalAmount,
      endTime: newEndTime,
      duration: newDuration,
      sessionVO: updatedSessionVO || orderData.sessionVO
    };

    console.log('[finishtiming] 计算价格完成，更新后的订单数据');
    return updatedOrderData;
  }

  /**
   * 从价格方案数据中提取价格选项
   * @param priceData 价格方案数据
   * @returns 价格选项数组
   */
  public extractPriceOptions(
    priceData: any,
    areaId: string = '',
    roomTypeId: string = '',
    holidayVO: any = null,
    currentTime: number = now10()
  ): PriceModel.PriceOption[] {
    try {
      const { timePricePlanVOs = [], baseTimePriceFee = 0 } = priceData || {};

      // 复用 HourlyBillingInteractor 的统一方案筛选逻辑
      const hourlyInteractor = new HourlyBillingInteractor();

      const timeSlotPrices = hourlyInteractor.getAllTimeSlotPrices(timePricePlanVOs, areaId, holidayVO, roomTypeId, currentTime, baseTimePriceFee);

      if (!timeSlotPrices || timeSlotPrices.length === 0) {
        console.warn('[finishtiming] getAllTimeSlotPrices 返回空，使用兜底价格');
        return [
          {
            priceType: 'baseRoomFee' as PriceTypeKey,
            planName: '默认价格',
            price: baseTimePriceFee / 100, // 分转元
            priority: 999,
            timeRange: { start: '00:00', end: '23:59' }
          }
        ];
      }

      // 将 TimeSlotPrice 转为 PriceModel.PriceOption（仅取基础房费类型）
      const priceOptions = PriceModel.normalizeRawPriceOptions(timeSlotPrices, 'baseRoomFee');

      console.log('[finishtiming] 最终生成的价格选项列表:', JSON.stringify(priceOptions));
      return priceOptions;
    } catch (error) {
      console.error('[finishtiming] 提取价格选项失败:', error);
      return [
        {
          priceType: 'baseRoomFee' as PriceTypeKey,
          planName: '默认价格',
          price: 100,
          priority: 999,
          timeRange: { start: '00:00', end: '23:59' }
        }
      ];
    }
  }

  /**
   * 格式化时间为HH:MM格式
   * @param date Date对象
   * @returns 格式化的时间字符串
   */
  private formatTimeString(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  /**
   * 结束计时
   * @param params 结束计时参数
   * @returns 结束计时结果
   */
  async finishTiming(params: { roomId: string; sessionId: string; orderRoomPlanVOS: FinishTimingData[] }) {
    try {
      console.log('结束计时参数:', params);

      // 调用OrderApi的结束计时方法
      const response = await OrderApi.finishTiming(params as FinishTimingRequest);

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '结束计时失败');
      }

      console.log('结束计时成功:', response.data);
      return response.data;
    } catch (error) {
      console.error('结束计时失败:', error);
      throw error;
    }
  }

  /**
   * 调用结束计时API
   * @param params 请求参数
   * @returns API响应
   */
  private async callFinishTimingApi(params: { roomId: string; sessionId: string; orderRoomPlanVOS: FinishTimingData[] }): Promise<BaseResponse<any>> {
    // 这个方法现在直接使用OrderApi.finishTiming，保留此方法以便向后兼容
    return OrderApi.finishTiming(params as FinishTimingRequest);
  }
}
