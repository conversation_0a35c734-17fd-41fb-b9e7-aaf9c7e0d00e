<template>
  <AppDialog v-model="dialogVisible" :closable="true" :showFooter="false">
    <!-- 自定义标题 -->
    <template #header>
      <div class="el-dialog__title">{{ computedState.roomName.value }} 结束计时</div>
    </template>
    <el-skeleton :loading="state.loading" animated>
      <template #default>
        <!-- 右侧内容：计时明细 -->
        <div>
          <!-- 房间信息 -->
          <div class="px-[12px]">
            <div class="flex items-center justify-between">
              <div class="flex h-[68px] items-center bg-[#F3F3F3] rounded-[8px] flex-col justify-center w-[276px] text-[16px]">
                <span class="label">开始时间</span>
                <span class="value text-[#000] font-medium">{{ computedState.startTimeFormatted }}</span>
              </div>
              <span class="label"> 至 </span>
              <div class="info-item">
                <div class="value">
                  <el-time-select
                    v-model="state.customEndTime"
                    class="custom-time-select !w-[276px]"
                    start="00:00"
                    step="00:15"
                    end="23:45"
                    placeholder="请选择结束时间"
                    @change="actions.updateCustomEndTime" />
                </div>
              </div>
            </div>
            <div class="mt-[24px] bg-[#F3F3F3] rounded-[8px] px-[24px] py-[16px] flex items-center justify-between">
              <div class="info-item">
                <span class="label">开台时长</span>
                <span class="value">{{ computedState.formattedDuration }}</span>
              </div>
              <div class="info-item">
                <span class="label">总计费用</span>
                <span class="value amount">{{ computedState.formattedTotalAmount }}</span>
              </div>
            </div>
          </div>
          <!-- 左侧内容：包厢信息和计费方式 -->
          <div class="finish-timing-dialog">
            <!-- 计时明细列表 -->
            <div class="detail-section">
              <h3 class="section-title">计时明细</h3>
              <el-table :data="state.orderRoomPlanVOS" style="width: 100%" stripe>
                <!-- <el-table-column prop="pricePlanName" label="价格方案" min-width="110" /> -->
                <el-table-column label="开始时间" min-width="100">
                  <template #default="scope">
                    {{ formatTime(scope.row.startTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="结束时间" min-width="100">
                  <template #default="scope">
                    {{ formatTime(scope.row.endTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="时长(分钟)" min-width="80" />
                <el-table-column label="金额(元)" min-width="80">
                  <template #default="scope">
                    {{ formatPrice(scope.row.payAmount) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>
    <template #footer>
      <div class="flex flex-col items-center">
        <button class="btn-black" @click="handleConfirm">结束计时</button>
        <span class="text-gray-500 mt-[8px]">(当前系统时间: {{ computedState.currentTimeFormatted }})</span>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits } from 'vue';
import dayjs from 'dayjs';
import { useFinishTimingDialog } from './presenter';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { DialogUIType } from '@/types/dialog';

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  sessionId: {
    type: String,
    default: ''
  },
  roomId: {
    type: String,
    default: ''
  },
  roomTypeId: {
    type: String,
    default: ''
  },
  areaId: {
    type: String,
    default: ''
  },
  roomName: {
    type: String,
    default: ''
  }
});

// 定义emits
const emit = defineEmits(['update:modelValue', 'success', 'cancel', 'error', 'close']);

// 获取视图模型
const { state, computed: computedState, actions } = useFinishTimingDialog();

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue || props.visible,
  set: val => {
    emit('update:modelValue', val);
  }
});

// 监听参数变化，初始化数据
watch(
  () => [dialogVisible.value, props.sessionId, props.roomId, props.roomTypeId, props.areaId, props.roomName],
  async ([visible, sessionId, roomId, roomTypeId, areaId, roomName]) => {
    console.log('[FinishTimingDialog] 监听参数变化:', {
      visible,
      sessionId,
      roomId,
      roomTypeId,
      areaId,
      roomName
    });

    if (visible && !state.isInitialized) {
      try {
        // 初始化数据
        await actions.init(sessionId as string, roomId as string, roomTypeId as string, areaId as string, roomName as string);
      } catch (error) {
        console.error('初始化失败:', error);
        emit('error', error);
      }
    }
  },
  { immediate: true }
);

// 对话框事件处理
const onDialogOpen = () => {
  actions.onDialogOpen();
};

const onDialogClose = () => {
  actions.onDialogClose();
  emit('close');
};

// 确认按钮点击
const handleConfirm = async () => {
  try {
    const result = await actions.handleFinishTiming();
    if (result && result.success) {
      // 成功后关闭弹窗
      dialogVisible.value = false;
      return result; // 显式返回结果，确保数据传递给DialogManager
    }
    return result;
  } catch (error) {
    console.error('结束计时失败:', error);
    emit('error', error);
    return { success: false, error };
  }
};

// 取消按钮点击
const handleCancel = () => {
  actions.handleCancel();
  emit('cancel');
  dialogVisible.value = false;
};

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return '--';
  return dayjs.unix(timestamp).format('YY-MM-DD HH:mm');
};

// 格式化价格（分转元）
const formatPrice = (price: number) => {
  if (price === undefined || price === null) return '0.00';
  return (price / 100).toFixed(2);
};
</script>

<style scoped>
.finish-timing-dialog {
  padding: 10px;
}

.custom-time-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.time-picker-wrapper {
  margin-top: 10px;
}

.time-tip {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.detail-section {
  margin-top: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.amount {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  width: 96px;
  color: #6b7280;
  font-size: 16px;
}

.info-item .value {
  flex: 1;
  font-size: 20px;
}

.info-item .value.amount {
  color: #ef4444;
  font-weight: 500;
  font-size: 20px;
}
</style>
