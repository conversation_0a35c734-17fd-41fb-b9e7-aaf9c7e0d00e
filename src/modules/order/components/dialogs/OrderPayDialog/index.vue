<template>
  <div v-if="dialogVisible">
    <AppDialog
      v-model="dialogVisible"
      :closable="true"
      :showFooter="false"
      @open="vm.actions.onDialogOpen"
      @close="handleClose"
      class="!w-[1680px] !h-[85vh] !max-h-[85vh]"
      :showHeader="false">
      <!-- 对话框内容 -->
      <div class="flex h-full">
        <!-- 左侧订单信息 -->
        <div class="order-info-container flex flex-col">
          <!-- 订单信息头部 -->
          <div class="order-header mb-4 flex-shrink-0">
            <h2 class="title pl-[12px]">结账</h2>
            <div class="order-no mr-[32px]">订单号：{{ (vm.state.orderData && vm.state.orderData.orderNos && vm.state.orderData.orderNos[0]) || '-' }}</div>
          </div>

          <!-- 中间可滚动区域 -->
          <div class="fl flex-1 overflow-y-auto pr-2">
            <!-- 会员信息 -->
            <div class="room-info-container h-[124px] flex flex-row mb-5" v-if="false && vm.state.memberInfo">
              <h3 class="text-[18px] text-[#000] flex flex-1">会员信息</h3>
              <div class="w-[160px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">级别</span>
                <span class="text-[24px] text-[#000] font-bold">{{ (vm.state.memberInfo && vm.state.memberInfo.levelName) || '-' }}</span>
              </div>
              <div class="w-[160px] ml-[20px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">电话/卡号</span>
                <span class="text-[20px] text-[#000] font-bold">{{
                  (vm.state.memberInfo && vm.state.memberInfo.phone) || (vm.state.memberInfo && vm.state.memberInfo.cardNo) || '-'
                }}</span>
              </div>
              <div class="w-[160px] ml-[20px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">余额</span>
                <span class="text-[24px] text-[#000] font-bold">
                  <PriceDisplay :amount-in-fen="(vm.state.memberInfo && vm.state.memberInfo.balance) || 0" class="member-balance-price" />
                </span>
              </div>
            </div>

            <!-- 包厢账单 -->
            <div class="p-[16px] mb-5">
              <div>
                <h3 class="text-[18px] text-[#000] flex flex-1 mb-[16px]">包厢账单</h3>

                <!-- 表格式布局 -->
                <!-- 账单详情 -->
                <template v-if="vm.state.displayRoomInfo && Array.isArray(vm.state.displayRoomInfo)">
                  <el-table :data="vm.state.displayRoomInfo" stripe class="compact-table">
                    <el-table-column label="开始时间" min-width="160" align="left">
                      <template #default="{ row }">
                        {{ row.timeDisplay }}
                      </template>
                    </el-table-column>
                    <el-table-column label="包厢名称" min-width="100">
                      <template #default="{ row }">
                        {{ row.roomName }}
                      </template>
                    </el-table-column>
                    <el-table-column label="消费模式" min-width="80">
                      <template #default="{ row }">
                        {{ row.pricePlanName }}
                      </template>
                    </el-table-column>
                    <el-table-column label="时长" min-width="100" align="center">
                      <template #default="{ row }">
                        <span class="text-[16px]"> {{ Math.floor(row.duration) }}分钟 </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="金额" width="140" align="right">
                      <template #default="{ row }">
                        <PriceDisplay :amount-in-fen="row.roomFee || 0" class="price-display-right-align product-amount-price price-display-small" />
                      </template>
                    </el-table-column>
                  </el-table>
                </template>

                <!-- 无包厢消费时显示提示 -->
                <div v-else class="py-4 text-center text-gray-400">暂无包厢消费</div>
              </div>

              <!-- 商品明细 -->
              <div class="product-list-container mt-5">
                <h3 class="text-[18px] text-[#000] flex flex-1 mb-[16px]">商品账单</h3>

                <!-- 使用el-table组件替换原有的DIV结构 -->
                <el-table
                  :data="(vm.state.orderData && vm.state.orderData.productInfos) || []"
                  stripe
                  :empty-text="'暂无商品数据'"
                  :cell-style="{ padding: '8px' }"
                  :default-expand-all="true"
                  :row-key="row => row.productId || row.id || `product_${row.productName || 'unknown'}_${Math.random()}`"
                  :row-class-name="getRowClassName"
                  class="w-full mb-3 product-table compact-table">
                  <!-- 展开列 - 只对有套餐明细的行显示展开按钮 -->
                  <el-table-column type="expand" width="40">
                    <template #default="scope">
                      <!-- 只有真正有套餐明细的商品才显示展开内容 -->
                      <div
                        v-if="scope.row.packageProductInfo && Array.isArray(scope.row.packageProductInfo) && scope.row.packageProductInfo.length > 0"
                        class="pl-[48px] py-3 bg-white">
                        <div
                          v-for="(item, index) in scope.row.packageProductInfo"
                          :key="index"
                          class="package-item flex justify-between items-center py-1 px-3 bg-gray-50 rounded text-sm mb-1 last:mb-0">
                          <span class="text-gray-700">{{ item.name || '未知商品' }}</span>
                          <span class="text-gray-500">x{{ item.count || 1 }}</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="productName" label="商品名称" min-width="180">
                    <template #default="scope">
                      <div class="font-medium">{{ scope.row.productName }}</div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="口味" min-width="100">
                    <template #default="scope">
                      {{ scope.row.taste || scope.row.flavors || '无' }}
                    </template>
                  </el-table-column> -->
                  <el-table-column label="数量" min-width="80" align="center">
                    <template #default="scope">
                      <!-- 直接显示实际可用数量，不显示退款信息 -->
                      {{ scope.row.quantity }}
                    </template>
                  </el-table-column>
                  <el-table-column label="单价" min-width="100" align="right">
                    <template #default="scope">
                      <template v-if="scope.row.isFree">
                        <div class="line-through text-gray-500">
                          <PriceDisplay
                            :amount-in-fen="scope.row.originalPrice || scope.row.payPrice || 0"
                            class="price-display-right-align price-display-small" />
                        </div>
                      </template>
                      <template v-else>
                        <PriceDisplay
                          :amount-in-fen="scope.row.payPrice || scope.row.originalPrice || 0"
                          class="price-display-right-align price-display-small" />
                      </template>
                    </template>
                  </el-table-column>
                  <el-table-column label="金额" min-width="120" align="right">
                    <template #default="scope">
                      <!-- 直接显示实际金额，不显示退款信息 -->
                      <PriceDisplay :amount-in-fen="scope.row.payAmount || 0" class="price-display-right-align product-amount-price price-display-small" />
                    </template>
                  </el-table-column>
                </el-table>

                <div class="product-subtotal">
                  <span>小计：</span>
                  <span class="subtotal-amount">
                    <PriceDisplay :amount-in-fen="(vm.state.orderData && vm.state.orderData.productAmount) || 0" class="price-display-right-align" />
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 费用汇总 - 不参与滚动 -->
          <div class="h-[72px] mt-auto flex-shrink-0 flex flex-row items-end justify-end mr-[32px] border-t border-[#E5E5E5]">
            <div class="ml-[20px] flex flex-row items-baseline">
              <span>应收：</span>
              <PriceDisplay :amount-in-fen="(vm.state.orderData && vm.state.orderData.totalAmount) || 0" class="custom-price-display" />
              <span class="ml-[8px]" v-if="vm.state.orderData && vm.state.orderData.minConsumptionAmount && vm.state.orderData.minConsumptionAmount > 0"
                >（含低消：{{ convertToYuan((vm.state.orderData && vm.state.orderData.minConsumptionAmount) || 0) }}）</span
              >
            </div>
            <div class="ml-[20px] flex flex-row items-baseline" v-if="vm.state.orderData && vm.state.orderData.discountAmount > 0">
              <span>优惠：</span>
              <PriceDisplay :amount-in-fen="(vm.state.orderData && vm.state.orderData.discountAmount) || 0" class="custom-price-display" />
            </div>
          </div>
        </div>

        <!-- 右侧支付区域 -->
        <div class="payment-container flex flex-col">
          <!-- 中间可滚动区域 -->
          <div class="flex-1 overflow-y-auto">
            <div class="rounded-[16px] mb-[36px] flex flex-col">
              <h3 class="text-[18px] text-[#000] flex flex-1 mb-[16px]">优惠方式</h3>
              <div class="discount-methods flex flex-row justify-between gap-[12px]">
                <el-button class="w-[240px] h-[60px] btn-normal text-[18px]" @click="vm.actions.openAdjustDialog"> 商家调整 </el-button>
                <el-button disabled class="w-[240px] h-[60px] btn-normal text-[18px]" @click="vm.actions.openAdjustDialog"> 会员优惠 </el-button>

                <el-button disabled class="w-[240px] h-[60px] btn-normal text-[18px]" @click="vm.actions.openAdjustDialog">挂帐 </el-button>
              </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="mb-[36px]">
              <h3 class="text-[18px] text-[#000] flex flex-1 mb-[16px]">支付方式</h3>

              <!-- 0元单提示 -->
              <div
                v-if="vm.state.orderData && vm.state.orderData.actualAmount === 0"
                class="text-primary border border-dashed border-[#409EFF] rounded-[8px] p-[16px] mb-[16px] bg-[#409EFF10]">
                <div class="flex items-center">
                  <i class="el-icon-info-filled mr-2 text-[#409EFF] text-xl"></i>
                  <span>当前为0元单，将自动使用免单方式结账</span>
                </div>
              </div>

              <div class="payment-method-grid">
                <div
                  v-for="method in vm.computed.paymentMethods.value"
                  :key="method.value"
                  class="payment-method-item h-[64px] bg-white rounded-[8px] flex flex-row justify-between items-center"
                  :class="[
                    { active: vm.state.selectedPayMethod === method.value },
                    { 'opacity-50 cursor-not-allowed': vm.state.orderData && vm.state.orderData.actualAmount === 0 }
                  ]"
                  @click="handlePayMethodClick(method.value)">
                  <div class="flex items-center gap-[12px]">
                    <img :src="getPaymentIcon(method.value)" class="w-[32px] h-[32px]" :alt="method.label" />
                    <div class="text-[18px] text-[#000] font-bold">{{ method.label }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 交易区右侧 - 支付操作区域 -->
            <div class="payment-input-container">
              <!-- 支付记录列表 - 集成输入功能 -->
              <div class="payment-records-list mb-[16px]">
                <!-- 已添加的支付记录 -->
                <div
                  v-for="(record, index) in vm.state.payRecords"
                  :key="record.uuid"
                  class="payment-record-item mb-[8px] h-[60px]"
                  :class="{ active: record.isActive }">
                  <div class="payment-method-label flex items-center w-[120px]">
                    <img :src="getPaymentIcon(record.payType)" class="w-[20px] h-[20px] mr-[8px]" :alt="getPaymentMethodName(record.payType)" />
                    {{ getPaymentMethodName(record.payType) }}：
                  </div>
                  <div class="payment-amount flex-1 mx-[12px]">
                    <el-input
                      v-model="record.amountInput"
                      placeholder="请输入金额"
                      class="amount-input"
                      @input="val => vm.actions.handlePayRecordAmountChange(val, index)"
                      @blur="vm.actions.handlePayRecordAmountBlur(record, index)">
                    </el-input>
                  </div>
                  <el-button icon="Minus" @click="vm.actions.removePayRecord(index)" circle />
                </div>
              </div>
            </div>
          </div>
          <!-- 底部确认区域 -->
          <div class="payment-toolbar bg-white rounded-[8px]mt-[20px] shadow-sm flex justify-between items-center">
            <div class="flex items-end mr-[24px]">
              <div class="text-[16px] text-[#333] mr-[8px]">待结金额：</div>
              <div class="text-[24px] font-bold text-[#ff3b30]">
                <PriceDisplay :amount-in-fen="(vm.state.orderData && vm.state.orderData.actualAmount) || 0" class="price-display-large" />
              </div>
            </div>
            <div>
              <el-button
                @click="vm.actions.handleConfirmPay"
                :loading="vm.state.loading"
                :disabled="!vm.computed.canPay.value"
                class="app-button px-[24px] py-[12px] text-[18px] font-bold"
                :class="{ 'ready-to-pay': vm.computed.canPay.value }">
                {{ vm.state.orderData && vm.state.orderData.actualAmount === 0 ? '确认免单' : '确认结账' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </AppDialog>

    <!-- 扫码对话框 - 仅在确认支付时显示 -->
    <AppDialog
      v-model="vm.state.scannerDialogVisible"
      v-if="vm.state.scannerDialogVisible"
      title="扫码支付"
      :show-close="true"
      :show-footer="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      @close="handleScannerDialogClose"
      class="!w-[664px] !h-[496px] pb-[12px]">
      <div class="flex flex-col items-center justify-end h-full">
        <PriceDisplay
          :amount-in-fen="vm.state.actualAmount ? parseFloat(vm.state.actualAmount) * 100 : 0"
          class="price-display-56 price-display-primary mb-[24px]" />
        <div class="flex flex-col items-center justify-center">
          <img src="@/assets/resource/scan-barcode.png" alt="扫码支付" class="w-[320px]" />
        </div>
      </div>
    </AppDialog>

    <!-- 会员卡对话框 -->
    <MemberCardDialog :visible="vm.state.memberCardVisible" @update:visible="vm.state.memberCardVisible = $event" @confirm="handleMemberDialogConfirm" />

    <!-- 信用账户对话框 -->
    <el-dialog v-model="vm.state.creditAccountVisible" v-if="vm.state.creditAccountVisible" title="选择信用账户" width="500px" :destroy-on-close="true">
      <CreditAccountDialog
        :visible="vm.state.creditAccountVisible"
        @update:visible="vm.state.creditAccountVisible = $event"
        :amount="(vm.state.orderData && vm.state.orderData.actualAmount) || 0"
        @confirm="vm.actions.handleCreditAccountConfirm" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, computed } from 'vue';
import { useOrderPayDialog } from './presenter';
import { convertToYuan } from '@/utils/priceUtils';
import { IOrderPayDialogViewModel, OrderPayType } from './viewmodel';
import { PaymentMethodType, PAYMENT_METHODS, MemberInfo } from './orderPayEntity';
import { Edit, Discount, CreditCard, ArrowRight, Delete, Remove } from '@element-plus/icons-vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import MemberCardDialog from '@/modules/order/components/MemberCardDialog.vue';
import CreditAccountDialog from '@/modules/order/components/CreditAccountDialog.vue';
import type { MemberVO } from '@/api/member';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';

// 导入支付图标
import cashIcon from '@/assets/payicons/现金.svg';
import wechatIcon from '@/assets/payicons/微信.svg';
import alipayIcon from '@/assets/payicons/支付宝.svg';
import memberIcon from '@/assets/payicons/会员卡.svg';
import cardIcon from '@/assets/payicons/银行卡.svg';
import scannerIcon from '@/assets/payicons/扫码枪.svg';
import freeIcon from '@/assets/payicons/免单.svg';

// 定义组件属性
interface Props {
  visible: boolean;
  sessionId: string;
  // 订单数据对象
  orderData: object | null;
  // 'later'(后结) 或 'immediate'(立结) 或 'productOrderInstant'(点单立结) 或 'continuePay'(续台立结)
  payType: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  sessionId: '',
  orderData: null,
  payType: 'later'
});

// 定义事件
const emit = defineEmits<{
  'update:visible': [value: boolean];
  close: [];
  paySuccess: [result: any];
  payCancel: [];
  error: [error: any];
}>();

// 创建一个计算属性，用于vue的v-model绑定
const dialogVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  }
});

// 使用视图模型
const vm = useOrderPayDialog();

// 监听参数变化，初始化数据
watch(
  () => [props.visible, props.sessionId, props.orderData, props.payType],
  async ([visible, sessionId, orderData, payType]) => {
    console.log('[OrderPayDialog] 监听参数变化:', {
      visible,
      sessionId,
      orderData,
      payType
    });

    if (visible) {
      // 当对话框显示时，重新创建presenter实例
      const newVm = useOrderPayDialog();
      // 使用类型安全的方式更新vm
      vm.state = newVm.state;
      vm.computed = newVm.computed;
      vm.actions = newVm.actions;

      try {
        // 初始化数据
        let validPayType: OrderPayType = 'later';
        if (typeof payType === 'string') {
          if (payType === 'immediate') {
            validPayType = 'immediate';
          } else if (payType === 'productOrderInstant') {
            validPayType = 'productOrderInstant';
          } else if (payType === 'continuePay') {
            validPayType = 'continuePay';
          } else {
            validPayType = 'later';
          }
        }

        // 直接传递orderData和sessionId，不再需要additionalOrderParams
        await vm.actions.init(typeof sessionId === 'string' ? sessionId : undefined, typeof orderData === 'object' ? orderData : undefined, validPayType);
      } catch (error) {
        console.error('初始化失败:', error);
        emit('error', error);
      }
    }
  },
  { immediate: true }
);

// 简化后的关闭处理 - 单一职责：只负责UI关闭
const closeDialog = () => {
  console.log('[OrderPayDialog] 关闭对话框');
  // 1. 更新本地状态
  (dialogVisible as any).value = false;
  // 2. 清理内部状态
  vm.actions.onDialogClose();
  // 3. 通知父组件
  emit('close');
};

// 全部使用相同的关闭入口
const handleClose = closeDialog;

// 支付成功处理
const handleSuccess = (result: any) => {
  console.log('[OrderPayDialog] 支付成功，关闭对话框');
  emit('paySuccess', result);
  closeDialog();
};

// 取消支付处理
const handleCancel = () => {
  console.log('[OrderPayDialog] 取消支付，关闭对话框');
  emit('payCancel');
  closeDialog();
};

// 扩展actions，添加处理方法
vm.actions.handleSuccess = handleSuccess;
vm.actions.handleCancel = handleCancel;

// 添加会员卡对话框确认的处理函数
const handleMemberDialogConfirm = (memberVO: MemberVO) => {
  console.log('[OrderPayDialog] 会员卡选择对话框确认:', memberVO);
  // 适配MemberVO到MemberInfo并传递给presenter
  const memberInfo: MemberInfo = {
    cardNo: memberVO.cardNumber || '',
    name: memberVO.name || '',
    balance: memberVO.balance || 0,
    phone: memberVO.phone || '',
    id: memberVO.id // 确保ID字段正确传递到presenter
  };
  // 调用presenter的原始方法
  vm.actions.handleMemberConfirm(memberInfo);
};

// 处理扫码弹窗关闭
const handleScannerDialogClose = () => {
  console.log('[OrderPayDialog] 用户手动关闭扫码弹窗');
  vm.state.scannerDialogVisible = false;
  window.removeEventListener('keypress', vm.actions.handleScanInput);
  // 不清空支付记录，保持用户的选择
};

// 组件卸载时，移除扫码事件监听
onUnmounted(() => {
  if (vm.state.scannerDialogVisible) {
    window.removeEventListener('keypress', vm.actions.handleScanInput);
  }
});

// 获取支付方式名称的辅助函数
const getPaymentMethodName = (payType: string) => {
  const method = PAYMENT_METHODS.find(m => m.value === payType);
  return method ? method.label || payType : '未知支付方式';
};

// 修改获取支付图标的方法
const getPaymentIcon = (method: string) => {
  switch (method) {
    case PaymentMethodType.Cash:
      return cashIcon;
    case PaymentMethodType.Bank:
      return cardIcon;
    case PaymentMethodType.AliPay:
      return alipayIcon;
    case PaymentMethodType.WeChat:
      return wechatIcon;
    case PaymentMethodType.Scanner:
      return scannerIcon;
    case PaymentMethodType.Member:
      return memberIcon;
    case PaymentMethodType.Free:
      return freeIcon;
    default:
      return '';
  }
};

// 处理支付方式点击
const handlePayMethodClick = (methodValue: PaymentMethodType) => {
  // 如果是0元单，禁止选择支付方式
  if (vm.state.orderData && vm.state.orderData.actualAmount === 0) {
    return;
  }
  vm.actions.selectPayMethod(methodValue);
};

// 为表格行添加CSS类名，用于控制展开按钮的显示
const getRowClassName = ({ row }: { row: any }) => {
  // 检查是否有套餐明细信息
  const hasPackageInfo = row.packageProductInfo && Array.isArray(row.packageProductInfo) && row.packageProductInfo.length > 0;

  // console.log(`[getRowClassName] 商品[${row.productName}]套餐检查:`, {
  //   hasPackageInfo,
  //   packageProductInfo: row.packageProductInfo
  // });

  // 如果没有套餐明细，添加特殊类名用于隐藏展开按钮
  return hasPackageInfo ? 'has-package-info' : 'no-package-info';
};
</script>

<style scoped>
.order-info-container {
  width: 1020px;
  border-right: 1px solid #ebeef5;
  padding: 24px 0px 24px 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.payment-container {
  width: 660px;
  padding: 32px 32px 24px 32px;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  height: 100%;
}

.order-header,
.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 28px;
  font-weight: 500;
  color: #333;
}

.order-no {
  color: #666;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
  color: #333;
}

/* 包厢信息样式 */
.room-info-container,
.member-info-container,
.discount-method-container,
.payment-method-container {
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.room-info-content {
  display: flex;
  justify-content: space-between;
}

.room-info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  flex: 1;
}

/* 会员信息样式 */
.member-info-content {
  padding: 10px 0;
}

.member-info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.member-level {
  color: #ff9a00;
  font-weight: bold;
}

.member-phone {
  color: #666;
}

.member-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.member-balance {
  font-size: 28px;
  font-weight: bold;
  color: #ff3b30;
}

/* 账单列表样式 */
.billing-header,
.product-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-weight: bold;
}

.billing-row,
.product-row {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.billing-item,
.product-item {
  flex: 1;
  padding: 0 5px;
}

.amount {
  color: #ff3b30;
  font-weight: bold;
}

.billing-subtotal,
.product-subtotal {
  display: flex;
  justify-content: flex-end;
  padding: 15px 10px;
  font-weight: bold;
}

.method-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #409eff;
}

.method-content {
  flex: 1;
}

.method-name {
  font-size: 14px;
  font-weight: bold;
}

.method-desc {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.method-arrow {
  color: #c0c4cc;
}

/* 支付方式网格 */
.payment-method-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.payment-method-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.payment-method-item.active {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.payment-input-row {
  display: flex;
  margin-bottom: 16px;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
  padding: 24px;
}

.amount {
  font-size: 18px;
  font-weight: bold;
  color: #ff3b30;
}

.amount-input {
  display: flex;
  justify-content: flex-end;
}

:deep(.amount-input .el-input__wrapper) {
  border: none;
  box-shadow: none;
}

:deep(.amount-input .el-input__inner) {
  text-align: right;
  font-size: 20px;
  font-weight: bold;
  padding-right: 12px;
}

/* 底部工具栏 */
.payment-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.total-label {
  font-size: 16px;
  margin-right: 10px;
}

.total-value {
  font-size: 24px;
  font-weight: bold;
  color: #ff3b30;
}

.pay-button {
  font-size: 16px;
  padding: 12px 24px;
  background-color: #ff3b30;
  border-color: #ff3b30;
}

.pay-button:hover {
  background-color: #ff1000;
  border-color: #ff1000;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 6px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 其他组件样式保持不变 */
.scanner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.scanner-status {
  margin-top: 16px;
  text-align: center;
}

.scanner-hint {
  color: #666;
  text-align: center;
  margin-top: 16px;
}

.member-card {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 16px;
}

.member-hint {
  color: #666;
  text-align: center;
  margin-top: 16px;
}

/* 添加卡片样式 */
.room-bill-card {
  background-color: #f8f8f8;
  border-radius: 6px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  height: 80px;
}

.room-bill-label {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.room-bill-value {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  flex: 1;
  display: flex;
  align-items: center;
}

.room-bill-value.amount {
  color: #ff3b30;
}

/* 表格式商品列表样式调整 */
.product-list {
  width: 100%;
}

.product-header,
.product-row {
  display: flex;
  width: 100%;
}

.product-item {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
}

/* 对于金额列右对齐 */
.product-item:nth-child(4),
.product-item:nth-child(5) {
  justify-content: flex-end;
}

/* 这里确保小计金额右对齐 */
.product-subtotal {
  display: flex;
  justify-content: flex-end;
  padding: 15px 10px;
  font-weight: bold;
}

.subtotal-amount {
  min-width: 100px;
  display: flex;
  justify-content: flex-end;
}

/* 确保总消费等汇总数字右对齐 */
.total-amount {
  min-width: 100px;
  display: flex;
  justify-content: flex-end;
}

/* 房费价格样式 */
.room-fee-price :deep(.price-unit),
.room-fee-price :deep(.price-integer),
.room-fee-price :deep(.price-decimal) {
  color: #ff6b00 !important;
}

/* 会员余额价格样式 */
.member-balance-price :deep(.price-unit),
.member-balance-price :deep(.price-integer),
.member-balance-price :deep(.price-decimal) {
  color: #000 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

/* 会员卡余额价格样式 */
.member-card-balance-price :deep(.price-unit),
.member-card-balance-price :deep(.price-integer),
.member-card-balance-price :deep(.price-decimal) {
  color: inherit !important;
}

/* 商品表格样式 */
.product-table {
  border-radius: 8px;
  overflow: hidden;
  --el-table-border-color: #e0e0e0;
  --el-table-header-bg-color: transparent;
  --el-table-row-hover-bg-color: #f0f9ff;
}

.product-table :deep(.el-table__header) {
  font-weight: 600;
}

.product-table :deep(.el-table__body tr.el-table__row--striped) {
  background-color: #fafafa;
}

.product-table :deep(.el-table__empty-text) {
  padding: 20px 0;
  color: #909399;
}

.payment-records-container {
  margin-top: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.payment-record-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.payment-method-label {
  font-weight: 500;
  width: 120px;
}

.payment-amount {
  flex-grow: 1;
}

.delete-button {
  margin-left: 8px;
}

.current-payment {
  margin-top: 12px;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px dashed #409eff;
}

.remaining-amount-row {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding: 8px;
  background-color: #fff7e6;
  border-radius: 4px;
}

.confirm-pay-area {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.total-row {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-bottom: 12px;
}

/* 在支付记录项样式中添加激活状态样式 */
:deep(.payment-record-item.active) {
  background-color: rgba(64, 158, 255, 0.1);
  border: 1px solid #409eff;
}

/* 展开行样式优化 */
:deep(.el-table__expanded-cell) {
  padding: 0 !important;
  border-bottom: none !important;
}

/* 隐藏没有套餐明细的行的展开按钮 */
:deep(.no-package-info .el-table__expand-column .el-table__expand-icon) {
  display: none !important;
}

/* 确保有套餐明细的行正常显示展开按钮 */
:deep(.has-package-info .el-table__expand-column .el-table__expand-icon) {
  display: inline-block !important;
}
</style>
