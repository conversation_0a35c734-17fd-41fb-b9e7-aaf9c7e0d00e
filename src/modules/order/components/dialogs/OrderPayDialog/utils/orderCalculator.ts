/**
 * 订单计算工具 - 处理订单筛选、费用计算和退款逻辑
 */

interface OrderVO {
  orderNo: string;
  pOrderNo: string;
  status: 'unpaid' | 'paid' | 'refunded';
  type: 'product' | 'roomplan';
  direction: 'normal' | 'refund';
  // 其他属性
  [key: string]: any;
}

interface OrderRoomPlanVO {
  orderNo: string;
  payAmount: number;
  RefundFee?: number;
  // 其他属性
  [key: string]: any;
}

interface OrderProductInfo {
  orderNo: string;
  productId: string;
  quantity: number;
  payAmount: number;
  RefundCount?: number;
  RefundFee?: number;
  // 新增用于存储原始数量和退款数量的字段
  originalQuantity?: number;
  refundedQuantity?: number;
  // 其他属性
  [key: string]: any;
}

/**
 * 创建退款映射关系
 * @param orders 所有订单
 * @param products 所有商品
 * @returns 退款映射 Map<原始订单号, Map<商品ID, 已退数量>>
 */
export function createRefundMap(orders: OrderVO[], products: OrderProductInfo[]): Map<string, Map<string, number>> {
  const refundMap = new Map<string, Map<string, number>>();
  
  // 找出所有退款订单
  const refundOrders = orders.filter(o => o.direction === 'refund');
  
  console.log('[orderCalculator] 退款订单列表:', refundOrders.map(o => ({ orderNo: o.orderNo, pOrderNo: o.pOrderNo })));
  
  // 对每个有原始订单号的退款订单处理
  refundOrders.filter(o => o.pOrderNo).forEach(refundOrder => {
    const originalOrderNo = refundOrder.pOrderNo;
    
    // 获取该退款订单的商品
    const refundProducts = products.filter(p => p.orderNo === refundOrder.orderNo);
    
    console.log('[orderCalculator] 退款订单', refundOrder.orderNo, '包含商品:', refundProducts.length);
    
    // 如果原订单还没有退款记录,创建一个新的Map
    if (!refundMap.has(originalOrderNo)) {
      refundMap.set(originalOrderNo, new Map());
    }
    
    // 记录每个商品的退款数量 - 使用pId关联原始商品
    refundProducts.forEach(product => {
      const currentMap = refundMap.get(originalOrderNo)!;
      
      // 使用pId字段找到对应的原始商品ID
      const pId = (product as any).pId;
      if (!pId) {
        console.log('[orderCalculator] 警告: 退款商品缺少pId，无法关联原始商品:', product.productName || '未知商品');
        return;
      }
      
      const currentQuantity = currentMap.get(pId) || 0;
      currentMap.set(pId, currentQuantity + (product.quantity || 0));
      
      console.log('[orderCalculator] 退款商品', product.productName || '未知商品', 'pId:', pId, '退款数量:', product.quantity || 0);
    });
  });
  
  console.log('[orderCalculator] 退款映射结果:', 
    Array.from(refundMap.entries()).map(([key, value]) => ({
      originalOrderNo: key,
      products: Array.from(value.entries()).map(([pId, qty]) => ({ pId, quantity: qty }))
    }))
  );
  
  return refundMap;
}

/**
 * 筛选未结的订单
 * @param orders 订单数组
 * @returns 未结的订单数组
 */
export function filterUnpaidOrders(orders: OrderVO[]): OrderVO[] {
  if (!Array.isArray(orders)) return [];
  
  // 筛选未结的订单（status 不等于 paid）
  return orders.filter(order => order.status !== 'paid');
}

/**
 * 筛选有效的房间订单（未结的正常订单或未结的退款订单）
 * @param orders 订单数组 
 * @param roomPlans 房间计划数组
 * @returns 有效的房间计划数组
 */
export function filterValidRoomPlans(orders: OrderVO[], roomPlans: OrderRoomPlanVO[]): OrderRoomPlanVO[] {
  if (!Array.isArray(orders) || !Array.isArray(roomPlans)) return [];
  
  // 建立订单映射，用于快速查找
  const orderMap = new Map<string, OrderVO>();
  orders.forEach(order => {
    orderMap.set(order.orderNo, order);
  });
  
  // 筛选有效的房间计划
  return roomPlans.filter(plan => {
    const relatedOrder = orderMap.get(plan.orderNo);
    
    // 如果找不到关联订单，或者关联订单已结，则跳过
    if (!relatedOrder || relatedOrder.status === 'paid') {
      return false;
    }
    
    // 对于正常订单，直接保留
    if (relatedOrder.direction === 'normal') {
      return true;
    }
    
    // 对于退款订单，只保留未付费的
    if (relatedOrder.direction === 'refund') {
      // 查找原始订单
      const originalOrder = orderMap.get(relatedOrder.pOrderNo);
      // 如果原始订单不存在或未结，则保留这个退款订单
      return !originalOrder || originalOrder.status !== 'paid';
    }
    
    return false;
  });
}

/**
 * 筛选有效的商品订单（未结的正常订单或未结的退款订单）
 * 并处理部分退款商品的数量
 * @param orders 订单数组
 * @param productInfos 商品信息数组 
 * @param refundMap 退款映射关系
 * @returns 有效的商品信息数组
 */
export function filterValidProducts(
  orders: OrderVO[], 
  productInfos: OrderProductInfo[],
  refundMap?: Map<string, Map<string, number>>
): OrderProductInfo[] {
  if (!Array.isArray(orders) || !Array.isArray(productInfos)) return [];
  
  // 建立订单映射，用于快速查找
  const orderMap = new Map<string, OrderVO>();
  orders.forEach(order => {
    orderMap.set(order.orderNo, order);
  });
  
  // 处理和筛选有效的商品信息
  return productInfos
    .map(product => {
      const relatedOrder = orderMap.get(product.orderNo);
      
      // 如果找不到关联订单，则跳过
      if (!relatedOrder) return null;
      
      // 如果关联订单已结，则跳过
      if (relatedOrder.status === 'paid') {
        return null;
      }
      
      // 如果是正常订单
      if (relatedOrder.direction === 'normal') {
        // 检查该商品是否有退款记录 - 使用商品自身的id查找退款记录
        const orderRefundInfo = refundMap?.get(product.orderNo);
        const productId = product.id || product.productId;
        const refundedQuantity = productId ? (orderRefundInfo?.get(productId) || 0) : 0;
        
        // 如果商品完全退款，则直接过滤掉
        if (refundedQuantity >= (product.quantity || 0)) {
          console.log(`[orderCalculator] 商品已完全退款，过滤掉: ${product.orderNo} - ${productId}, 原数量:${product.quantity}, 已退:${refundedQuantity}`);
          return null;
        }
        
        // 如果有部分退款，调整数量和金额
        if (refundedQuantity > 0) {
          const originalQuantity = product.quantity || 0;
          const newQuantity = originalQuantity - refundedQuantity;
          
          // 计算单价
          const unitPrice = originalQuantity > 0 ? 
            Math.round((product.payAmount || 0) / originalQuantity) : 0;
          
          // 计算新的支付金额
          const newPayAmount = unitPrice * newQuantity;
          
          console.log(`[orderCalculator] 商品部分退款，调整数量: ${product.orderNo} - ${productId}, 原数量:${originalQuantity}, 已退:${refundedQuantity}, 新数量:${newQuantity}, 新金额:${newPayAmount}`);
          
          // 创建新的商品对象，更新数量和金额
          return {
            ...product,
            quantity: newQuantity,
            payAmount: newPayAmount,
            // 记录原始数量和退款数量，方便UI显示
            originalQuantity, 
            refundedQuantity,
            // 设置RefundCount一致
            RefundCount: refundedQuantity,
            // 设置RefundFee
            RefundFee: unitPrice * refundedQuantity
          };
        }
        
        // 没有退款记录，保持原样
        return product;
      }
      
      // 如果是退款订单，这里不再处理，已经在创建refundMap时使用了
      return null;
    })
    .filter(Boolean) as OrderProductInfo[]; // 过滤掉null值
}

/**
 * 处理部分退款商品
 * 合并相同商品，处理退款数量
 * @param products 商品数组
 * @returns 处理后的商品数组
 */
export function processPartialRefundProducts(products: OrderProductInfo[]): OrderProductInfo[] {
  if (!Array.isArray(products) || products.length === 0) return [];
  
  // 创建商品ID映射
  const productMap = new Map<string, OrderProductInfo>();
  
  // 处理每个商品
  products.forEach(product => {
    const productId = product.id || product.productId || product.packageId;
    if (!productId) return; // 确保商品有ID或套餐ID
    
    // 如果是正常商品
    if (!productMap.has(productId)) {
      productMap.set(productId, { ...product });
    } else {
      // 存在相同商品，需要合并
      const existingProduct = productMap.get(productId)!;
      
      // 更新数量和金额
      existingProduct.quantity = (existingProduct.quantity || 0) + (product.quantity || 0);
      existingProduct.payAmount = (existingProduct.payAmount || 0) + (product.payAmount || 0);
      
      // 处理原始数量和退款信息
      if (product.originalQuantity) {
        existingProduct.originalQuantity = (existingProduct.originalQuantity || 0) + product.originalQuantity;
      }
      if (product.refundedQuantity) {
        existingProduct.refundedQuantity = (existingProduct.refundedQuantity || 0) + product.refundedQuantity;
      }
      
      // 处理退款信息
      if (product.RefundCount) {
        existingProduct.RefundCount = (existingProduct.RefundCount || 0) + product.RefundCount;
      }
      if (product.RefundFee) {
        existingProduct.RefundFee = (existingProduct.RefundFee || 0) + product.RefundFee;
      }
    }
  });
  
  // 转换回数组
  return Array.from(productMap.values());
}

/**
 * 计算房间费用总额
 * @param roomPlans 房间计划数组
 * @returns 房间费用总额（分）
 */
export function calculateRoomFee(roomPlans: OrderRoomPlanVO[]): number {
  if (!Array.isArray(roomPlans)) return 0;
  
  return roomPlans.reduce((total, plan) => {
    // 计算实际费用 = 支付金额 - 退款金额
    const refundFee = plan.RefundFee || 0;
    return total + ((plan.payAmount || 0) - refundFee);
  }, 0);
}

/**
 * 计算商品费用总额
 * @param productInfos 商品信息数组
 * @returns 商品费用总额（分）
 */
export function calculateProductFee(productInfos: OrderProductInfo[]): number {
  if (!Array.isArray(productInfos)) return 0;
  
  return productInfos.reduce((total, product) => {
    // 注意：由于我们已经调整了商品的payAmount，这里直接使用即可
    return total + (product.payAmount || 0);
  }, 0);
}

/**
 * 合并所有有效订单号
 * @param validRoomPlans 有效的房间计划
 * @param validProductInfos 有效的商品信息
 * @returns 合并后的订单号数组
 */
export function mergeOrderNos(
  validRoomPlans: OrderRoomPlanVO[], 
  validProductInfos: OrderProductInfo[]
): string[] {
  const orderNoSet = new Set<string>();
  
  // 添加房间计划的订单号
  validRoomPlans.forEach(plan => {
    if (plan.orderNo) {
      orderNoSet.add(plan.orderNo);
    }
  });
  
  // 添加商品信息的订单号
  validProductInfos.forEach(product => {
    if (product.orderNo) {
      orderNoSet.add(product.orderNo);
    }
  });
  
  return Array.from(orderNoSet);
}

/**
 * 完整处理订单数据，返回用于展示和支付的数据
 * @param apiData API返回的数据
 * @returns 处理后的数据
 */
export function processOrderCalculation(apiData: any): {
  validOrders: OrderVO[];
  validRoomPlans: OrderRoomPlanVO[];
  validProductInfos: OrderProductInfo[];
  mergedProductInfos: OrderProductInfo[];
  roomFee: number;
  productFee: number;
  totalFee: number;
  orderNos: string[];
  minimumCharge: number;
} {
  console.log('[orderCalculator] 开始处理订单数据:', apiData);
  
  // 默认空值处理
  const data = apiData || {};
  const orders = data.orderVOS || [];
  const roomPlans = data.orderRoomPlanVOS || [];
  const inProductInfos = data.inOrderProductInfos || [];
  const outProductInfos = data.outOrderProductInfos || [];
  
  // 0. 创建退款映射关系
  const allProducts = [...inProductInfos, ...outProductInfos];
  const refundMap = createRefundMap(orders, allProducts);
  
  // 1. 筛选有效订单（过滤掉已结的）
  const validOrders = filterUnpaidOrders(orders);
  
  // 2. 筛选有效房间计划
  const validRoomPlans = filterValidRoomPlans(validOrders, roomPlans);
  
  // 3. 筛选有效商品信息，同时处理部分退款
  const validInProductInfos = filterValidProducts(validOrders, inProductInfos, refundMap);
  const validOutProductInfos = filterValidProducts(validOrders, outProductInfos, refundMap);
  const validProductInfos = [...validInProductInfos, ...validOutProductInfos];
  
  // 4. 处理部分退款商品（这里已经不需要处理退款了，因为在前面已经处理过了）
  const mergedProductInfos = processPartialRefundProducts(validProductInfos);
  // 5.计算低消
  const minimumCharge = validRoomPlans.reduce((total, plan) => {
    return total + (plan.minimumCharge || 0);
  }, 0);
  
  // 5. 计算费用（这里已经是基于处理后的商品数量和金额计算，不需要额外考虑退款）
  const roomFee = calculateRoomFee(validRoomPlans);
  const productFee = calculateProductFee(validProductInfos);
  const totalFee = roomFee + productFee;
  
  // 6. 合并订单号
  const orderNos = mergeOrderNos(validRoomPlans, validProductInfos);
  
  console.log('[orderCalculator] 处理结果:', {
    validOrdersCount: validOrders.length,
    validRoomPlansCount: validRoomPlans.length,
    validProductInfosCount: validProductInfos.length,
    mergedProductInfosCount: mergedProductInfos.length,
    roomFee,
    productFee,
    totalFee,
    orderNos,
    minimumCharge
  });
  
  // 返回处理结果
  return {
    validOrders,
    validRoomPlans,
    validProductInfos,
    mergedProductInfos,
    roomFee,
    productFee,
    totalFee,
    orderNos,
    minimumCharge
  };
} 