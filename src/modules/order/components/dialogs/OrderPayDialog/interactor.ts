import { OrderApi } from '@/modules/order/api/order';
import { ElMessage } from 'element-plus';
import { MemberApi } from '@/api/member';
import { cloneDeep } from 'lodash-es';
import { processOrderCalculation } from './utils/orderCalculator';
import { V3AddOrderAdditionalPayReqDto, V3AddOrderOpenPayReqDto, V3QueryOrderPayReqDto, postApiV3OrderPayQuery } from '@/api/autoGenerated';
import { printingService } from '@/application/printingService';
import type { OrderDetailData } from '@/domains/prints/order-detail/models/order-detail-data';
import type { SessionOrderData } from '@/domains/prints/session-order/models/session-order-data';
import { useDeviceStore } from '@/stores/deviceStore';
import { useUserStore } from '@/stores/userStore';
// 导入价格计算相关模块
import * as PriceModel from '@/modules/room/components/HourlyBilling/PriceModel';
import type { PriceTypeKey } from '@/modules/room/components/HourlyBilling/viewmodel';
import { RoomApi } from '@/modules/room/api/room'; // 正确导入RoomApi
import { now10 } from '@/utils/dateUtils';

/**
 * OrderPayDialog 业务交互层
 * 处理与API的交互和业务逻辑
 */
export class OrderPayDialogInteractor {
  /**
   * 通过场次ID获取订单数据
   * @param sessionId 场次ID
   * @returns 订单数据
   */
  async getOrderBySession(sessionId: string) {
    try {
      console.log('通过sessionId获取订单数据:', sessionId);
      // 调用API获取订单数据
      const response = await OrderApi.queryOpenOrder({ sessionId });

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '获取订单数据失败');
      }

      console.log('获取到订单数据:', response.data);

      // 对获取到的数据进行预处理，使其结构符合OrderPayDialog中converter的预期
      const processedData = await this.processOrderData(response.data);

      return processedData;
    } catch (error) {
      console.error('获取订单数据失败:', error);
      ElMessage.error('获取订单数据失败: ' + (error instanceof Error ? error.message : String(error)));
      return null;
    }
  }

  /**
   * 对从API获取的订单数据进行预处理
   * @param data API返回的原始数据
   * @returns 处理后的数据
   */
  private async processOrderData(data: any) {
    if (!data) return null;

    console.log('[paydialog timer] 预处理订单数据开始:', data);

    // 使用订单计算工具处理订单数据
    const calculationResult = processOrderCalculation(data);

    // 获取各项计算结果
    const { validOrders, validRoomPlans, validProductInfos, mergedProductInfos, roomFee, productFee, totalFee, orderNos } = calculationResult;

    console.log('[paydialog timer] 订单计算结果:', {
      roomFee,
      productFee,
      totalFee,
      orderNos,
      roomPlansCount: validRoomPlans.length,
      productInfosCount: mergedProductInfos.length
    });

    // 构建处理后的数据结构
    const processedData = {
      // 基本信息
      sessionId: data.sessionId || '',
      roomId: data.roomId || '',
      roomName: data.roomVO?.name || '',
      venueId: data.venueId || '',

      // 时间信息
      startTime: data.startTime || data.sessionVO?.startTime || 0,
      endTime: data.endTime || data.sessionVO?.endTime || 0,
      duration: data.duration || data.sessionVO?.duration || data.buyMinute || 0,
      buyMinute: data.buyMinute || 0,

      // 价格方案信息
      pricePlanId: data.pricePlanId || '',
      pricePlanName: data.pricePlanName || '',
      consumptionMode: data.consumptionMode || '',
      selectedAreaId: data.selectedAreaId || '',
      selectedRoomTypeId: data.selectedRoomTypeId || '',

      // 原始数组数据
      orderVOS: data.orderVOS || [], // 保留原始订单数组
      orderRoomPlanVOS: validRoomPlans, // 使用筛选后的房间计划
      productInfos: mergedProductInfos, // 使用处理后的商品信息
      inOrderProductInfos: data.inOrderProductInfos || [],
      outOrderProductInfos: data.outOrderProductInfos || [],

      // 计算后的费用数据（单位:分）
      roomFee: roomFee,
      roomAmount: roomFee, // 同步到 roomAmount 字段
      productAmount: productFee,
      supermarketFee: productFee, // 同步到 supermarketFee 字段
      totalAmount: totalFee,
      actualAmount: totalFee, // 默认实际支付金额等于总金额
      discountAmount: 0, // 默认没有折扣

      // 支付相关信息
      orderNos: orderNos, // 使用合并后的订单号数组
      validOrders: validOrders, // 保存筛选后的有效订单

      // 保留原始对象引用
      sessionVO: data.sessionVO || null,
      roomVO: data.roomVO || null,

      // 计算结果元数据 - 用于调试和验证
      _calculationResult: calculationResult,

      // 保留原始数据引用
      _rawData: data
    };

    // 处理计时消费
    const hasTimeConsume = processedData.orderRoomPlanVOS?.some(plan => plan.isTimeConsume === true);
    if (hasTimeConsume) {
      console.log('[paydialog timer] 检测到计时消费订单，需要重新计算房费');
      console.log(
        '[paydialog timer] 计时消费计划:',
        processedData.orderRoomPlanVOS.filter(plan => plan.isTimeConsume === true)
      );
      return await this.processTimeConsumeOrderPlans(processedData);
    } else {
      console.log('[paydialog timer] 非计时消费订单，无需特殊处理');
    }

    return processedData;
  }

  /**
   * 获取房间开台价格方案
   * @param roomId 房间ID
   * @returns 价格方案数据
   */
  async getRoomOpenViewData(roomId: string, typeId: string, areaId: string) {
    try {
      // 调用开台接口获取价格方案
      const response = await RoomApi.openViewRoom({
        roomId,
        sessionId: '', // 可选参数
        typeId: typeId, // 可选参数
        areaId: areaId // 可选参数
      });

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '获取价格方案失败');
      }

      console.log('获取到价格方案数据:', response.data);
      return response.data;
    } catch (error) {
      console.error('获取价格方案失败:', error);
      return null;
    }
  }

  /**
   * 处理计时消费订单
   * @param orderData 订单数据
   * @returns 处理后的订单数据
   */
  async processTimeConsumeOrderPlans(orderData: any) {
    try {
      console.log('[paydialog timer] 开始处理计时消费订单:', orderData);

      // 1. 过滤出未结的订单
      const unpaidOrders = orderData.orderVOS?.filter((order: any) => order.state === 0) || [];
      console.log('[paydialog timer] 未结订单:', unpaidOrders);

      // 2. 获取未结订单的订单号
      const unpaidOrderNos = unpaidOrders.map((order: any) => order.orderNo);
      console.log('[paydialog timer] 未结订单号:', unpaidOrderNos);

      // 3. 过滤出与未结订单相关的房间计划
      const relevantPlans = orderData.orderRoomPlanVOS?.filter((plan: any) => unpaidOrderNos.includes(plan.orderNo)) || [];
      console.log('[paydialog timer] 相关房间计划:', relevantPlans);

      // 4. 找出计时消费计划
      const timeConsumePlans = relevantPlans.filter((plan: any) => plan.isTimeConsume === true);
      if (!timeConsumePlans || timeConsumePlans.length === 0) {
        console.log('[paydialog timer] 未检测到计时消费房间计划');
        return orderData;
      }

      console.log('[paydialog timer] 计时消费计划数量:', timeConsumePlans.length);
      console.log('[paydialog timer] 计时消费计划详情:', JSON.stringify(timeConsumePlans));

      // 5. 处理每个计时消费计划
      const updatedPlans: any[] = [];
      for (const plan of timeConsumePlans) {
        // 获取价格方案
        const priceData = await this.getRoomOpenViewData(plan.roomId, plan.selectedRoomTypeId, plan.selectedAreaId);
        if (!priceData) {
          console.warn('[paydialog timer] 无法获取价格方案，跳过该计划');
          continue;
        }

        // 提取价格选项
        const priceOptions = this.extractPriceOptions(priceData);
        if (!priceOptions || priceOptions.length === 0) {
          console.warn('[paydialog timer] 无有效价格选项，跳过该计划');
          continue;
        }

        // 计算当前时间和持续时间
        const startTime = plan.startTime;
        let currentTime = now10();
        if (currentTime <= startTime) {
          currentTime = startTime;
        }
        const durationMinutes = Math.floor((currentTime - startTime) / 60);
        let priceResult = null;
        if (durationMinutes <= 0) {
          priceResult = {
            details: [],
            totalPrice: 0
          };
        } else {
          // 转换为时间格式
          const startTimeStr = this.formatTimeString(new Date(startTime * 1000));
          const endTimeStr = this.formatTimeString(new Date(currentTime * 1000));

          console.log(`[paydialog timer] 计算计时消费: 从 ${startTimeStr} 到 ${endTimeStr}, 持续 ${durationMinutes} 分钟`);

          // 计算价格
          priceResult = PriceModel.calculatePrice(startTimeStr, endTimeStr, priceOptions, orderData.roomVO?.baseTimePriceFee);
        }

        console.log('[paydialog timer] 价格计算结果:', priceResult);

        // 为每个价格明细创建一个新的计划
        const newPlans = priceResult.details.map((detail: any) => ({
          // 基础信息
          id: '', // 新计划ID为空
          orderNo: plan.orderNo,
          roomId: plan.roomId,
          roomName: plan.roomName,
          sessionId: plan.sessionId,
          venueId: plan.venueId,

          // 时间信息
          startTime: Math.floor(new Date(`${new Date().toISOString().split('T')[0]} ${detail.startTime}`).getTime() / 1000),
          endTime: Math.floor(new Date(`${new Date().toISOString().split('T')[0]} ${detail.endTime}`).getTime() / 1000),
          duration: detail.duration,

          // 价格信息
          payAmount: Math.round(detail.price),
          originalPayAmount: Math.round(detail.price),
          unitPrice: Math.round(detail.price / detail.duration),

          // 方案信息
          pricePlanId: plan.pricePlanId,
          pricePlanName: detail.planName,
          pricePlanType: plan.pricePlanType,
          isTimeConsume: true,

          // 其他信息
          state: plan.state,
          version: plan.version,
          ctime: plan.ctime,
          utime: Math.floor(Date.now() / 1000)
        }));

        updatedPlans.push(...newPlans);
      }

      // 6. 更新orderRoomPlanVOS
      const finalOrderRoomPlanVOS = orderData.orderRoomPlanVOS
        .map((plan: any) => {
          // 如果是计时消费计划，替换为新的计划
          if (plan.isTimeConsume && unpaidOrderNos.includes(plan.orderNo)) {
            return updatedPlans.filter((newPlan: any) => newPlan.orderNo === plan.orderNo);
          }
          return plan;
        })
        .flat(); // 展平数组，因为有些元素可能是数组

      // 7. 重新计算总费用
      const newRoomFee = finalOrderRoomPlanVOS.reduce((sum: number, plan: any) => sum + (plan.payAmount || 0), 0);
      const newTotalAmount = newRoomFee + (orderData.productAmount || 0);
      const newDuration = finalOrderRoomPlanVOS.reduce((sum: number, plan: any) => sum + (plan.duration || 0), 0);
      const newEndTime = Math.max(...finalOrderRoomPlanVOS.map((plan: any) => plan.endTime)) || orderData.endTime;

      console.log(`[paydialog timer] 更新后房费: ${newRoomFee}, 商品费: ${orderData.productAmount || 0}, 总费用: ${newTotalAmount}`);
      orderData.sessionVO.roomFee = newRoomFee;
      orderData.sessionVO.duration = newDuration;
      orderData.sessionVO.endTime = newEndTime;
      // 8. 返回更新后的订单数据
      const updatedOrderData = {
        ...orderData,
        orderRoomPlanVOS: finalOrderRoomPlanVOS,
        roomFee: newRoomFee,
        roomAmount: newRoomFee,
        totalAmount: newTotalAmount,
        actualAmount: newTotalAmount,
        endTime: newEndTime,
        duration: newDuration
      };

      console.log('[paydialog timer] 处理计时消费完成，更新后的订单数据:', updatedOrderData);
      return updatedOrderData;
    } catch (error) {
      console.error('[paydialog timer] 处理计时消费失败:', error);
      return orderData;
    }
  }

  /**
   * 从价格方案数据中提取价格选项
   * @param priceData 价格方案数据
   * @returns 价格选项数组
   */
  private extractPriceOptions(priceData: any): PriceModel.PriceOption[] {
    try {
      // console.log('[paydialog timer] 开始提取价格选项，原始数据:', JSON.stringify(priceData))
      // 基础价格选项
      const baseOptions: PriceModel.PriceOption[] = [];

      // 从API返回数据中提取价格方案
      const { timePricePlanVOs, baseTimePriceFee } = priceData || {};

      // console.log(`[paydialog timer] 提取参数: timePricePlanVOs长度=${timePricePlanVOs?.length || 0}, baseTimePriceFee=${baseTimePriceFee || 0}`)

      // 如果没有方案数据，使用默认价格
      if (!timePricePlanVOs || timePricePlanVOs.length === 0) {
        // console.log('[paydialog timer] 没有找到价格方案数据，使用默认价格:', baseTimePriceFee);
        const defaultOption: PriceModel.PriceOption = {
          priceType: 'baseRoomFee',
          planName: '默认价格',
          price: baseTimePriceFee || 0, // 单位转换：分 -> 元
          priority: 999,
          timeRange: {
            start: '00:00',
            end: '23:59'
          }
        };
        // console.log('[paydialog timer] 创建默认价格选项:', JSON.stringify(defaultOption));
        return [defaultOption];
      }

      // console.log('[paydialog timer] 找到价格方案数量:', timePricePlanVOs.length);

      // 处理每个价格方案
      timePricePlanVOs.forEach((plan: any, index: number) => {
        // console.log(`[paydialog timer] 处理价格方案 #${index + 1}:`, JSON.stringify(plan));

        // 提取价格配置列表
        const priceConfigList = plan.priceConfigList || [];

        // 时间配置
        const timeConfig = plan.timeConfig || {};
        const timeStart = timeConfig.billingTimeStart || '00:00';
        const timeEnd = timeConfig.billingTimeEnd || '23:59';

        // console.log(`[paydialog timer] 价格方案 #${index + 1} 时间范围: ${timeStart} - ${timeEnd}`);

        // 处理每种价格类型
        const baseConfig = priceConfigList.find((config: any) => config.type === 'base');
        if (baseConfig) {
          const basePrice = baseConfig.price || 0;
          // console.log(`[paydialog timer] 添加基础价格方案: ${plan.name}, 价格: ${basePrice / 100}元/小时, 原始值: ${basePrice}`);

          baseOptions.push({
            priceType: 'baseRoomFee',
            planName: plan.name || '买钟计时',
            price: basePrice,
            priority: 0, // 基础价格优先级最高
            timeRange: {
              start: timeStart,
              end: timeEnd
            }
          });
        } else {
          console.log(`[paydialog timer] 价格方案 #${index + 1} 没有基础价格配置`);
        }

        // 处理特殊价格类型
        if (priceConfigList.find((config: any) => config.type === 'birthday')) {
          const birthdayConfig = priceConfigList.find((config: any) => config.type === 'birthday');
          const birthdayPrice = birthdayConfig?.price || 0;
          if (birthdayPrice > 0) {
            console.log(`[paydialog timer] 添加生日价格方案: ${plan.name}, 价格: ${birthdayPrice / 100}元/小时, 原始值: ${birthdayPrice}`);

            baseOptions.push({
              priceType: 'birthdayFee',
              planName: plan.name || '买钟计时',
              price: birthdayPrice,
              priority: 1,
              timeRange: {
                start: timeStart,
                end: timeEnd
              }
            });
          }
        }

        if (priceConfigList.find((config: any) => config.type === 'activity')) {
          const activityConfig = priceConfigList.find((config: any) => config.type === 'activity');
          const activityPrice = activityConfig?.price || 0;
          if (activityPrice > 0) {
            console.log(`[paydialog timer] 添加活动价格方案: ${plan.name}, 价格: ${activityPrice / 100}元/小时, 原始值: ${activityPrice}`);

            baseOptions.push({
              priceType: 'activityFee',
              planName: plan.name || '价格方案',
              price: activityPrice,
              priority: 2,
              timeRange: {
                start: timeStart,
                end: timeEnd
              }
            });
          }
        }

        if (priceConfigList.find((config: any) => config.type === 'group')) {
          const groupConfig = priceConfigList.find((config: any) => config.type === 'group');
          const groupPrice = groupConfig?.price || 0;
          if (groupPrice > 0) {
            console.log(`[paydialog timer] 添加团购价格方案: ${plan.name}, 价格: ${groupPrice / 100}元/小时, 原始值: ${groupPrice}`);

            baseOptions.push({
              priceType: 'groupBuyFee',
              planName: plan.name || '价格方案',
              price: groupPrice,
              priority: 3,
              timeRange: {
                start: timeStart,
                end: timeEnd
              }
            });
          }
        }
      });

      // 如果没有找到有效价格配置，使用baseTimePriceFee作为默认价格
      if (baseOptions.length === 0) {
        console.log('[paydialog timer] 没有找到有效价格配置，使用默认价格:', baseTimePriceFee);
        return [
          {
            priceType: 'baseRoomFee',
            planName: '默认价格',
            price: baseTimePriceFee || 0,
            priority: 999,
            timeRange: {
              start: '00:00',
              end: '23:59'
            }
          }
        ];
      }

      console.log('[paydialog timer] 最终生成的价格选项列表:', JSON.stringify(baseOptions));
      return baseOptions;
    } catch (error) {
      console.error('[paydialog timer] 提取价格选项失败:', error);
      // 返回默认价格选项
      return [
        {
          priceType: 'baseRoomFee',
          planName: '默认价格',
          price: 100,
          priority: 999,
          timeRange: {
            start: '00:00',
            end: '23:59'
          }
        }
      ];
    }
  }

  /**
   * 格式化时间为HH:MM格式
   * @param date Date对象
   * @returns 格式化的时间字符串
   */
  private formatTimeString(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  /**
   * 支付订单
   * @param params 支付参数
   * @returns 支付结果
   */
  async payOrder(params: V3QueryOrderPayReqDto) {
    try {
      console.log('支付订单参数:', params);

      // 调用支付API
      const response = await OrderApi.payOrder(params);

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '支付失败');
      }

      return response.data;
    } catch (error) {
      console.error('支付订单失败:', error);
      throw error;
    }
  }

  /**
   * 查询支付状态
   * @param params 查询参数
   * @returns 支付查询结果
   */
  async queryOrderPay(params: { billId?: string; roomId: string; sessionId: string }) {
    try {
      console.log('查询支付状态参数:', params);

      // 调用查询支付API
      const response = await OrderApi.queryOrderPay(params);

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '查询支付状态失败');
      }

      return response.data;
    } catch (error) {
      console.error('查询支付状态失败:', error);
      throw error;
    }
  }

  /**
   * 开台立结
   * @param params 开台立结参数
   * @returns 支付结果
   */
  async openPay(params: V3AddOrderOpenPayReqDto) {
    try {
      console.log('开台立结参数:', params);
      // 调用开台立结API
      const response = await OrderApi.openPay(params);
      console.log('开台立结结果:', response);
      if (!response || response.code !== 0) {
        throw new Error(response?.message || '开台立结失败');
      }

      return response.data;
    } catch (error) {
      console.error('开台立结失败:', error);
      throw error;
    }
  }

  /**
   * 点单立结
   * @param params 点单立结参数
   * @returns 支付结果
   */
  async productOrderInstantPay(params: V3AddOrderAdditionalPayReqDto) {
    try {
      console.log('点单立结参数:', params);

      // 调用点单立结API - 使用已有的additionalOrderPay接口
      const response = await OrderApi.additionalOrderPay(params);

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '点单立结失败');
      }

      return response.data;
    } catch (error) {
      console.error('点单立结失败:', error);
      throw error;
    }
  }

  /**
   * 获取会员信息
   * @param cardNo 会员卡号
   * @returns 会员信息
   */
  async getMemberInfo(cardNo: string) {
    try {
      const response = await MemberApi.queryMembers({ cardNumber: cardNo });
      if (response.code === 0) {
        return response.data;
      } else {
        throw new Error(response.message || '获取会员信息失败');
      }
    } catch (error) {
      console.error('获取会员信息失败:', error);
      throw error;
    }
  }

  /**
   * 打印结账单
   * @param payBillId 结账单ID
   * @param sessionId 会话ID
   * @param orderNos 订单号数组
   */
  async printCheckoutBill(payBillId: string | undefined, sessionId: string | undefined, orderNos: string[] | undefined): Promise<void> {
    try {
      if (!payBillId || !sessionId) {
        console.warn('Interactor: 没有可打印结账单的payBillId或sessionId');
        return;
      }
      console.log('Interactor: 准备打印结账单:', payBillId, sessionId, orderNos);
      // 调用打印服务，不等待结果
      printingService
        .printCheckoutBillByPayBillId(payBillId, sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('Interactor: 结账单打印任务已发送');
          } else {
            console.error('Interactor: 结账单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('Interactor: 打印结账单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('Interactor: 准备打印结账单时出错:', error);
    }
  }

  /**
   * 根据会话ID打印开台单据 (异步，不阻塞调用者)
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   */
  async printSessionOrderBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<void> {
    try {
      if (!sessionId) {
        console.warn('Interactor: 没有可打印开台单的sessionId');
        return;
      }
      console.log('Interactor: 准备根据会话ID打印开台单:', sessionId);

      // 调用printingService的printSessionOrderBySessionId方法，不等待结果
      printingService
        .printSessionOrderBySessionId(sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('Interactor: 开台单打印任务已发送');
          } else {
            console.error('Interactor: 开台单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('Interactor: 打印开台单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('Interactor: 准备打印开台单时出错:', error);
    }
  }

  /**
   * 根据会话ID打印续房单 (异步，不阻塞调用者)
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   */
  async printRoomExtensionBySessionId(sessionId: string | undefined, orderNos: string[] | undefined): Promise<void> {
    try {
      if (!sessionId) {
        console.warn('Interactor: 没有可打印续房单的sessionId');
        return;
      }
      console.log('Interactor: 准备根据会话ID打印续房单:', sessionId);

      // 调用printingService的printRoomExtensionBySessionId方法，不等待结果
      printingService
        .printRoomExtensionBySessionId(sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('Interactor: 续房单打印任务已发送');
          } else {
            console.error('Interactor: 续房单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('Interactor: 打印续房单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('Interactor: 准备打印续房单时出错:', error);
    }
  }

  /**
   * 统一处理开台成功后的完整打印流程
   * 包括：开台单 + 出品单（如果有商品）
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @param payType 支付类型，用于判断是否打印开台单
   */
  async printOpenTableDocuments(sessionId: string | undefined, orderNos: string[] | undefined, payType?: string): Promise<void> {
    try {
      if (!sessionId) {
        console.warn('Interactor: 没有可打印单据的sessionId');
        return;
      }

      console.log('Interactor: 开始统一打印开台相关单据, 会话ID:', sessionId, '订单号:', orderNos, '支付类型:', payType);

      // 1. 根据支付类型决定打印开台单还是续房单
      try {
        if (payType === 'continuePay') {
          console.log('Interactor: 续台立结，准备打印续房单, 会话ID:', sessionId);
          this.printRoomExtensionBySessionId(sessionId, orderNos);
        } else {
          console.log('Interactor: 开台立结，准备打印开台单, 会话ID:', sessionId);
          this.printSessionOrderBySessionId(sessionId, orderNos);
        }
      } catch (printError) {
        console.error('Interactor: 打印单据时发生错误:', printError);
        // 打印失败不影响其他打印流程
      }

      // 2. 打印出品单 - 如果有订单号（说明有商品）
      if (orderNos && orderNos.length > 0) {
        try {
          console.log('Interactor: 准备打印出品单, 订单号:', orderNos, '会话ID:', sessionId);
          this.printProductionOrder(orderNos, sessionId);
        } catch (printError) {
          console.error('Interactor: 打印出品单时发生错误:', printError);
          // 打印失败不影响其他打印流程
        }
      } else {
        console.log('Interactor: 无订单号，跳过出品单打印');
      }

      console.log('Interactor: 开台相关单据打印流程完成');
    } catch (error) {
      console.error('Interactor: 统一打印开台单据时出错:', error);
    }
  }

  /**
   * 续台立结
   * @param params 续台立结参数
   * @returns 支付结果
   */
  async openContinuePay(params: any) {
    try {
      console.log('续台立结参数:', params);
      // 调用续台立结API
      const response = await OrderApi.openContinuePay(params);

      if (!response || response.code !== 0) {
        throw new Error(response?.message || '续台立结失败');
      }

      return response.data;
    } catch (error) {
      console.error('续台立结失败:', error);
      throw error;
    }
  }

  /**
   * 打印出品单 (异步，不阻塞调用者)
   * 用于支付成功后调用，打印出品单
   * @param orderNos 订单号列表
   * @param sessionId 会话ID（可选）
   */
  async printProductionOrder(orderNos: string[] | undefined, sessionId?: string): Promise<void> {
    try {
      if (!orderNos || orderNos.length === 0) {
        console.warn('Interactor: 没有可打印出品单的订单号');
        return;
      }

      console.log(`Interactor: 准备打印出品单 (异步)，订单号: ${orderNos.join(', ')}, 会话ID: ${sessionId || '无'}`);

      // 调用printingService的printProductOutBySessionId方法，不等待结果
      printingService
        .printProductOutBySessionId(sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('Interactor: 出品单打印任务已发送');
          } else {
            console.error('Interactor: 出品单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('Interactor: 打印出品单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('Interactor: 准备打印出品单时出错:', error);
      // 即使内部出错，也不阻塞外部调用
    }
  }
}
