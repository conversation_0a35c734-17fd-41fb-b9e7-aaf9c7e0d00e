import { OrderData, PaymentMethod, PaymentMethodType, ProductInfo, MemberInfo, PAYMENT_TYPE_B_SHOW_QR } from './orderPayEntity';
import { IOrderPayDialogState, ORDER_PAY_DIALOG_STATE_DEFAULTS, OrderPayType } from './viewmodel';

import { cloneDeep } from 'lodash-es';
import { formatFullTimeRangeFromUnix } from '@/utils/dateUtils';

// 引入订单计算工具
import { processOrderCalculation, filterUnpaidOrders } from './utils/orderCalculator';
import { V3AddOrderAdditionalPayReqDto } from '@/api/autoGenerated/shared/types/order';
import { formatYuanWithSymbol } from '@/utils/priceUtils';

export class OrderPayDialogConverter {
  /**
   * 将通过 sessionId 获取并经过 Interactor 处理的 API 数据转换为视图模型状态
   * @param apiData Interactor 处理后的 API 数据
   * @param options 转换选项，如 payType
   * @returns 视图模型状态
   */
  toViewModelFromApi(apiData: any, options?: { payType?: OrderPayType }): IOrderPayDialogState {
    console.log('[Converter] Converting API data:', apiData);
    // 使用 cloneDeep 避免修改原始数据
    const dataToProcess = cloneDeep(apiData);

    // 合并默认值 - 对于API数据，通常需要合并以确保所有字段存在
    const mergedData = { ...ORDER_PAY_DIALOG_STATE_DEFAULTS, ...dataToProcess };
    console.log('[Converter] Merged data:', mergedData);
    // 使用专门的API数据格式化函数
    const formattedOrderData: OrderData = this.formatOrderDataFromApi(mergedData);
    console.log('[Converter] Formatted OrderData from API:', formattedOrderData);

    // 返回视图模型状态
    return {
      ...ORDER_PAY_DIALOG_STATE_DEFAULTS, // 使用默认值作为基础
      orderData: formattedOrderData,
      memberInfo: this.extractMemberInfo(mergedData),
      displayRoomInfo: this.extractRoomInfo(mergedData),
      payType: options?.payType || 'later', // 从 options 获取 payType
      isInitialized: false, // 初始化状态由 Presenter 控制
      loading: false, // 加载状态由 Presenter 控制
      isDataFromProps: false, // 明确数据来源
      actualAmount: mergedData.actualAmount ? this.formatAmount(mergedData.actualAmount) : ORDER_PAY_DIALOG_STATE_DEFAULTS.actualAmount
    };
  }

  /**
   * 将外部直接传入的 orderData 转换为视图模型状态
   * @param propsData 外部传入的 orderData
   * @param options 转换选项，如 payType
   * @returns 视图模型状态
   */
  toViewModelFromProps(propsData: OrderData, options?: { payType?: OrderPayType }): IOrderPayDialogState {
    console.log('[Converter] Converting props data:', propsData);
    // 使用 cloneDeep 避免修改原始 props
    const dataToProcess = cloneDeep(propsData);

    // 对于直接传入的数据，我们使用专门的Props数据格式化函数
    const formattedOrderData: OrderData = this.formatOrderDataFromProps(dataToProcess);
    console.log('[Converter] Formatted OrderData from Props:', formattedOrderData);

    // 返回视图模型状态
    return {
      ...ORDER_PAY_DIALOG_STATE_DEFAULTS, // 使用默认值作为基础
      orderData: formattedOrderData,
      memberInfo: this.extractMemberInfo(dataToProcess),
      displayRoomInfo: this.extractRoomInfo(dataToProcess),
      payType: options?.payType || 'later', // 从 options 获取 payType
      isInitialized: false, // 初始化状态由 Presenter 控制
      loading: false, // 加载状态由 Presenter 控制
      isDataFromProps: true, // 明确数据来源
      actualAmount: formattedOrderData.actualAmount ? this.formatAmount(formattedOrderData.actualAmount) : ORDER_PAY_DIALOG_STATE_DEFAULTS.actualAmount
    };
  }

  /**
   * @deprecated 请使用 toViewModelFromApi 或 toViewModelFromProps 代替
   */
  toViewModel(orderData: any, options?: { payType?: 'later' | 'immediate' }): IOrderPayDialogState {
    // 保留旧方法并标记为废弃，或者直接删除，这里选择标记废弃以减少直接报错风险
    console.warn('toViewModel is deprecated. Use toViewModelFromApi or toViewModelFromProps instead.');
    // 可以根据需要决定这里的行为，例如抛出错误或默认调用其中一个新方法
    // 这里暂时返回一个空状态
    return { ...ORDER_PAY_DIALOG_STATE_DEFAULTS }; // <--- 使用导入的常量
  }

  /**
   * 将API返回的数据格式化为OrderData结构
   * 专用于处理API通过interactor处理后的数据格式
   */
  formatOrderDataFromApi(data: any): OrderData {
    console.log('[converter] formatOrderDataFromApi 处理API数据:', data);

    // 使用优化后的订单计算工具处理订单数据
    const calculationResult = processOrderCalculation(data);
    const allUnpaidOrderNos = filterUnpaidOrders(data.orderVOS)?.map(order => order.orderNo) || [];
    // 保存计算结果到原始数据，方便后续使用
    data._calculationResult = calculationResult;

    // API数据中可能通过roomVO获取roomId
    const roomId = data.roomVO ? data.roomVO.id : '';
    const roomName = data.roomVO ? data.roomVO.name : '';

    // 使用计算结果获取费用数据
    const roomFee = calculationResult.roomFee;
    const productAmount = calculationResult.productFee;
    const minConsumptionAmount = calculationResult.minimumCharge || 0;
    const totalAmount = roomFee + Math.max(productAmount, minConsumptionAmount);

    // 应收金额 - 优先取已有值，否则等于总金额
    const actualAmount = totalAmount;
    console.log('[formatOrderDataFromApi] minConsumptionAmount:', minConsumptionAmount);

    // 优惠金额
    const discountAmount = data.discountAmount !== undefined ? data.discountAmount : totalAmount - actualAmount > 0 ? totalAmount - actualAmount : 0;

    // 订单号 - 使用计算结果
    const orderNos = calculationResult.orderNos || data.orderNos || [];

    // 商品数据 - 使用计算结果的商品数据，但需要转换为ProductInfo类型
    const productInfos = calculationResult.mergedProductInfos.map((product: any) => {
      // 处理套餐明细信息 - API中packageProductInfo是JSON字符串，需要解析
      let packageProductInfo = null;
      if (product.packageProductInfo && typeof product.packageProductInfo === 'string') {
        try {
          packageProductInfo = JSON.parse(product.packageProductInfo);
          console.log('[Converter] 解析套餐明细:', product.productName, packageProductInfo);
        } catch (error) {
          console.error('[Converter] 解析套餐明细失败:', product.productName, product.packageProductInfo, error);
          packageProductInfo = null;
        }
      } else if (Array.isArray(product.packageProductInfo)) {
        // 如果已经是数组，直接使用
        packageProductInfo = product.packageProductInfo;
      }

      return {
        productName: product.productName || '',
        quantity: product.quantity || 0,
        price: product.originalPrice || 0,
        payPrice: product.payAmount ? (product.quantity > 0 ? Math.round(product.payAmount / product.quantity) : 0) : 0,
        orderNo: product.orderNo || '',
        originalPrice: product.originalPrice || 0,
        originalAmount: (product.originalPrice || 0) * (product.quantity || 0),
        payAmount: product.payAmount || 0,
        id: product.id || '',
        productId: product.productId || '',
        unit: product.unit || '',
        RefundCount: product.RefundCount || 0,
        RefundFee: product.RefundFee || 0,
        // 添加套餐明细信息
        packageProductInfo: packageProductInfo,
        packageId: product.packageId || '',
        // 添加其他必要字段，明确定义为联合类型
        status: 'unpaid' as 'unpaid' | 'paid' | 'refunded'
      };
    });

    data.sessionVO.unpaidAmount = actualAmount;
    data.sessionVO.totalAmount = totalAmount;
    // 返回标准的OrderData结构
    const formatted: OrderData = {
      orderNos: orderNos,
      unpaidOrderNos: allUnpaidOrderNos,
      roomId: roomId,
      roomName: roomName,
      roomAmount: roomFee,
      productAmount: productAmount,
      supermarketFee: productAmount,
      totalAmount: totalAmount,
      actualAmount: actualAmount,
      discountAmount: discountAmount,
      startTime: data.startTime || data.sessionVO?.startTime || 0,
      endTime: data.endTime || data.sessionVO?.endTime || 0,
      duration: data.duration || data.buyMinute || data.sessionVO?.duration || 0,
      roomFee: roomFee,
      orderRoomPlanVOS: data.orderRoomPlanVOS || [],
      productInfos: productInfos,
      inOrderProductInfos: [], // 初始化为空数组
      outOrderProductInfos: [], // 初始化为空数组
      sessionVO: data.sessionVO || null,
      productDiscountRate: data.productDiscountRate || 0,
      roomDiscountRate: data.roomDiscountRate || 0,
      productReduceAmount: data.productReduceAmount || 0,
      roomReduceAmount: data.roomReduceAmount || 0,
      freeAmount: data.freeAmount || 0,
      unpaidAmount: data.sessionVO?.unpaidAmount || 0,
      changeAmount: data.sessionVO?.changeAmount || 0,
      lowConsumptionDiffAmount: data.sessionVO?.lowConsumptionDiffAmount || 0,
      minConsumptionAmount: minConsumptionAmount,
      bookingId: ''
    };

    console.log('[formatOrderDataFromApi] 输出格式化数据:', formatted);
    return formatted;
  }

  /**
   * 将Props传入的数据格式化为OrderData结构
   * 专用于处理直接通过Props传入的数据格式
   */
  formatOrderDataFromProps(data: any): OrderData {
    console.log('[formatOrderDataFromProps] 处理Props数据:', data);

    // Props数据中通常直接包含所需字段
    let roomFee = data.roomFee || data.roomAmount || 0;
    let productAmount = data.productAmount || data.supermarketFee || 0;
    const minimumCharge = data.minimumCharge || 0;
    // 如果数据不完整，尝试补充计算
    if (roomFee === 0 && Array.isArray(data.orderRoomPlanVOS)) {
      roomFee = data.orderRoomPlanVOS.reduce((sum: number, plan: any) => sum + ((plan?.payAmount || 0) - (plan?.RefundFee || 0)), 0);
    }

    // 处理商品数据 - Props可能直接传入完整productInfos或分开的inOrder/outOrder
    let productInfos = [];
    if (Array.isArray(data.productInfos) && data.productInfos.length > 0) {
      productInfos = data.productInfos;
    } else {
      // 合并 inOrder 和 outOrder
      productInfos = [
        ...(Array.isArray(data.inOrderProductInfos) ? data.inOrderProductInfos : []),
        ...(Array.isArray(data.outOrderProductInfos) ? data.outOrderProductInfos : [])
      ];

      // 如果通过合并获取了商品数据，重新计算商品总额
      if (productInfos.length > 0 && productAmount === 0) {
        productAmount = productInfos.reduce((sum: number, item: any) => sum + ((item?.payAmount || 0) - (item?.RefundFee || 0)), 0);
      }
    }

    // 计算总金额
    let totalAmount = data.totalAmount || 0;
    if (totalAmount === 0) {
      totalAmount = roomFee + Math.max(productAmount, minimumCharge);
    }

    // 应收金额和优惠金额
    let actualAmount = data.actualAmount !== undefined ? data.actualAmount : totalAmount;
    let discountAmount = data.discountAmount !== undefined ? data.discountAmount : totalAmount - actualAmount > 0 ? totalAmount - actualAmount : 0;

    // 订单号处理
    const orderNos = Array.isArray(data.orderNos) ? data.orderNos : data.orderNo ? [data.orderNo] : [];

    // 确保初始化bookingId
    const bookingId = data.bookingId || '';

    // 返回标准的OrderData结构
    const formatted: OrderData = {
      orderNos: orderNos,
      unpaidOrderNos: orderNos,
      roomId: data.roomId || '',
      bookingId: bookingId,
      roomName: data.roomName || '',
      roomAmount: roomFee,
      productAmount: productAmount,
      supermarketFee: productAmount,
      totalAmount: totalAmount,
      actualAmount: actualAmount,
      discountAmount: discountAmount,
      startTime: data.startTime || 0,
      endTime: data.endTime || 0,
      duration: data.duration || 0,
      roomFee: roomFee,
      orderRoomPlanVOS: data.orderRoomPlanVOS || [],
      productInfos: productInfos,
      inOrderProductInfos: data.inOrderProductInfos || [],
      outOrderProductInfos: data.outOrderProductInfos || [],
      sessionVO: data.sessionVO || null,
      productDiscountRate: data.productDiscountRate || 0,
      roomDiscountRate: data.roomDiscountRate || 0,
      productReduceAmount: data.productReduceAmount || 0,
      roomReduceAmount: data.roomReduceAmount || 0,
      freeAmount: data.freeAmount || 0,
      minConsumptionAmount: minimumCharge,
      unpaidAmount: data.unpaidAmount || 0,
      changeAmount: data.changeAmount || 0,
      lowConsumptionDiffAmount: data.lowConsumptionDiffAmount || 0
    };

    console.log('[formatOrderDataFromProps] 输出格式化数据:', formatted);
    return formatted;
  }

  /**
   * @deprecated 请使用formatOrderDataFromApi或formatOrderDataFromProps代替
   */
  formatOrderData(data: any): OrderData {
    console.warn('formatOrderData is deprecated. Use formatOrderDataFromApi or formatOrderDataFromProps instead.');
    // 根据数据源选择合适的格式化函数
    if (data._calculationResult) {
      return this.formatOrderDataFromApi(data);
    } else {
      return this.formatOrderDataFromProps(data);
    }
  }

  /**
   * 提取包厢信息
   * @param data 订单数据 (可能是API返回处理后，也可能是Props传入)
   * @returns 包厢显示信息数组或 null
   */
  extractRoomInfo(data: any): IOrderPayDialogState['displayRoomInfo'] {
    console.log('[extractRoomInfo] Input data:', data);

    // 获取未结的订单号
    let roomPlans = [];
    if (data.orderVOS) {
      // 如果有orderVOS，过滤出未结订单
      const unpaidOrderNos = data.orderVOS?.filter((order: any) => order.status === 'unpaid')?.map((order: any) => order.orderNo) || [];

      console.log('[extractRoomInfo] 未结订单号:', unpaidOrderNos);

      // 从orderRoomPlanVOS中获取包厢信息，只保留未结订单的包厢信息
      roomPlans = (data.orderRoomPlanVOS || []).filter((plan: any) => unpaidOrderNos.includes(plan.orderNo));
    } else {
      // 如果没有orderVOS，直接使用orderRoomPlanVOS
      roomPlans = data.orderRoomPlanVOS || [];
    }

    if (roomPlans.length === 0) {
      console.warn('[extractRoomInfo] 没有找到未结的包厢计划信息');
      return null;
    }

    console.log('[extractRoomInfo] 未结的包厢计划:', roomPlans);

    // 转换每个包厢计划为显示格式
    return roomPlans.map((roomPlan: any) => {
      // 获取价格方案名称
      const pricePlanName = roomPlan.pricePlanName || '常规价';
      console.log('[extractRoomInfo] 价格方案名称:', pricePlanName);

      // 获取开始时间和结束时间
      const startTime = roomPlan.startTime || data.startTime || 0;
      const endTime = roomPlan.endTime || data.endTime || 0;

      // 使用dateUtils中的方法预格式化时间
      const timeDisplay = formatFullTimeRangeFromUnix(startTime, endTime);
      console.log('[extractRoomInfo] 格式化的时间:', timeDisplay);

      return {
        roomName: roomPlan.roomName || data.roomName || '-',
        startTime: startTime,
        endTime: endTime,
        duration: roomPlan.duration || data.duration || 0,
        roomFee: roomPlan.payAmount || data.roomAmount || 0,
        pricePlanName: pricePlanName,
        unitPrice: roomPlan.unitPrice || 0,
        isTimeConsume: roomPlan.isTimeConsume || false,
        timeDisplay: timeDisplay // 添加预格式化的时间显示
      };
    });
  }

  /**
   * 提取会员信息
   * @param data 订单数据
   * @returns 会员信息或 null
   */
  extractMemberInfo(data: any): MemberInfo | null {
    console.log('[extractMemberInfo] Input data:', data);
    if (data?.memberInfo) {
      // 如果数据中直接包含 memberInfo 对象
      return {
        cardNo: data.memberInfo.cardNo || data.memberInfo.cardNumber || '',
        name: data.memberInfo.name || '',
        phone: data.memberInfo.phone || '',
        balance: data.memberInfo.balance || 0,
        levelName: data.memberInfo.levelName || ''
        // 根据需要添加其他字段
      };
    } else if (data?.memberId || data?.memberCardNo || data?.memberPhone) {
      // 如果数据中包含会员相关字段
      return {
        cardNo: data.memberCardNo || '',
        name: data.memberName || '',
        phone: data.memberPhone || '',
        balance: data.memberBalance || 0,
        levelName: data.memberLevelName || ''
        // 根据需要添加其他字段
      };
    }
    console.log('[extractMemberInfo] No member information found.');
    return null; // 没有找到会员信息
  }

  /**
   * 格式化支付方式列表
   * @param methods 支付方式列表
   * @returns 格式化后的支付方式列表
   */
  formatPaymentMethods(methods: any[]): PaymentMethod[] {
    return methods.map(method => ({
      label: method.name || method.label || '',
      value: (method.type as PaymentMethodType) || method.value,
      icon: method.icon || this.getDefaultIconForType(method.type || method.value)
    }));
  }

  /**
   * 从视图模型状态获取支付记录数组
   * @param state 视图模型状态
   * @returns 支付记录数组
   */
  getPayRecords(state: IOrderPayDialogState): { payType: string; totalFee: number; bQROneCode?: string }[] {
    // 直接返回state中的支付记录数组
    return state.payRecords || [];
  }

  /**
   * 根据支付方式类型获取默认图标
   * @param type 支付方式类型
   * @returns 默认图标路径
   */
  getDefaultIconForType(type: string): string {
    const iconMap: Record<string, string> = {
      cash: 'icon-cash',
      wechat: 'icon-wechat',
      alipay: 'icon-alipay',
      creditCard: 'icon-credit-card',
      memberCard: 'icon-member-card',
      transfer: 'icon-transfer',
      creditAccount: 'icon-credit-account'
    };
    return iconMap[type] || 'icon-default';
  }

  /**
   * 格式化金额为字符串（元, 带两位小数）
   * @param amountInFen 以分为单位的金额
   * @returns 格式化后的金额字符串（元）
   */
  formatAmount(amountInFen: number | string | null | undefined): string {
    const numAmount = Number(amountInFen);
    if (isNaN(numAmount)) {
      return '0.00';
    }
    const amountInYuan = (numAmount / 100).toFixed(2);
    return amountInYuan;
  }

  /**
   * 将视图模型状态转换为API请求参数（开台后结）
   * @param state 视图模型状态
   * @returns API请求参数
   */
  toPayOrderParams(state: IOrderPayDialogState): any {
    if (!state.orderData) {
      return null;
    }
    console.log('[toPayOrderParams] state:', state);

    // 使用支付记录计算总支付金额
    const totalFee = state.payRecords.reduce((sum, record) => sum + (record.totalFee || 0), 0);

    // 其他金额参数
    const orderActualAmountInFen = state.orderData.actualAmount; // 订单应付金额(分)
    const minConsumptionAmount = state.orderData.minConsumptionAmount; // 最低消费金额(分)
    const originalFee = state.orderData.totalAmount; // 原始金额(分)
    const zeroFee = 0; // 抹零金额(分)

    // 计算找零 - 仅对现金支付有效
    const cashRecord = state.payRecords.find(record => record.payType === 'cash');
    const changeAmount = cashRecord && cashRecord.totalFee > orderActualAmountInFen ? cashRecord.totalFee - orderActualAmountInFen : 0;

    const shouldFee = state.orderData.totalAmount; // 应付金额(分)
    const creditAmount = 0; // 挂账金额(分)

    // 检查是否存在免单支付记录
    const hasFreePayRecord = state.payRecords.some(record => record.payType === 'free');

    console.log('[payorder]toPayOrderParams 处理参数:', state);
    console.log('[payorder] 金额计算:', { originalFee, totalFee, zeroFee, changeAmount, shouldFee, creditAmount });
    console.log('[payorder] 支付记录:', state.payRecords);
    console.log('[payorder] 是否免单:', hasFreePayRecord || !!state.orderData.freeAmount);

    const orderRoomPlanVOS = state.orderData?.orderRoomPlanVOS || [];
    console.log('[payorder] 房间计划:', orderRoomPlanVOS);
    const filteredOrderRoomPlanVOS = orderRoomPlanVOS.filter((plan: any) => plan.id === ''); //只保留planId为空的房间计时方案，因为这意味着他是计时的，重新计算出来的价格方案
    console.log('[payorder] 房间计划:', filteredOrderRoomPlanVOS);
    return {
      sessionId: state.orderData.sessionVO?.sessionId || state.orderData.orderNos?.[0] || '', // 优先用 sessionVO 的 sessionId
      roomId: state.orderData.roomId || '',
      bookingId: state.orderData.bookingId || '',

      // 金额相关 - API 接口定义的标准金额字段 (单位：分)
      originalFee: originalFee,
      shouldFee: shouldFee,
      totalFee: totalFee,
      changeAmount: changeAmount,
      zeroFee: zeroFee,
      creditAmount: creditAmount,
      minimumCharge: minConsumptionAmount,
      payRecords: state.payRecords, // 直接使用state中的支付记录
      orderNos: state.orderData.unpaidOrderNos,
      orderRoomPlanVOS: filteredOrderRoomPlanVOS,
      memberInfo: state.memberInfo
        ? {
            // 确保字段名匹配API预期
            cardNo: state.memberInfo.cardNo,
            name: state.memberInfo.name,
            phone: state.memberInfo.phone
            // 如果API需要其他会员信息，从 state.memberInfo 添加
          }
        : null,
      printReceipt: state.printReceipt,
      // 折扣信息 (单位：分)
      ...(state.orderData.discountAmount && { discountAmount: state.orderData.discountAmount }),
      // 如果API需要更详细的折扣信息，从 state.orderData 添加
      productDiscount: state.orderData.productDiscountRate || 100,
      productDiscountAmount: state.orderData.productReduceAmount,
      roomDiscount: state.orderData.roomDiscountRate || 100,
      roomDiscountAmount: state.orderData.roomReduceAmount,
      // 商家调整原因
      discountReason: state.orderData.adjustReason || '',
      // 免单判断：支付记录中存在免单类型或原始数据中有freeAmount
      isFree: hasFreePayRecord || !!state.orderData.freeAmount
    };
  }

  /**
   * 将视图模型状态转换为开台立结API请求参数
   */
  toOpenPayParams(state: IOrderPayDialogState): any {
    if (!state.orderData) {
      throw new Error('订单数据不存在');
    }

    // 根据数据来源选择不同的参数构建方法
    if (state.isDataFromProps) {
      return this.buildOpenPayParamsFromProps(state);
    } else {
      return this.buildOpenPayParamsFromApi(state);
    }
  }

  /**
   * 将视图模型转换为点单立结支付参数
   * @param state 视图模型状态
   * @param selectedPayMethod 可选的支付方式，用于向后兼容
   * @returns 点单立结支付参数
   */
  toProductOrderInstantParams(state: IOrderPayDialogState, selectedPayMethod?: string, bQROneCode?: string): V3AddOrderAdditionalPayReqDto {
    console.log('转换点单立结参数, state:', state);

    // 获取源数据
    const orderData = state.orderData;
    if (!orderData) {
      console.error('[toProductOrderInstantParams] 错误: orderData为空');
      throw new Error('订单数据不存在');
    }

    // 使用支付记录计算总支付金额
    const totalFee = state.payRecords.reduce((sum, record) => sum + (record.totalFee || 0), 0);

    // 计算找零 - 仅对现金支付有效
    const cashRecord = state.payRecords.find(record => record.payType === 'cash');
    const changeAmount = cashRecord && cashRecord.totalFee > orderData.actualAmount ? cashRecord.totalFee - orderData.actualAmount : 0;

    // 检查是否存在免单支付记录
    const hasFreePayRecord = state.payRecords.some(record => record.payType === 'free');
    const isFree = hasFreePayRecord || !!orderData.freeAmount;

    // 向后兼容处理：如果提供了支付方式和二维码，且支付记录为空，则创建一条记录
    const payRecords =
      state.payRecords.length > 0
        ? state.payRecords
        : orderData.actualAmount === 0
          ? [
              {
                // 0元单使用免单支付方式
                payType: PaymentMethodType.Free,
                totalFee: 0,
                amountInput: '0.00'
              }
            ]
          : [
              {
                payType: selectedPayMethod || '',
                totalFee: orderData.actualAmount,
                ...(bQROneCode ? { bQROneCode } : {})
              }
            ];

    // 检查支付记录中是否存在bShowQR，确保bQROneCode字段存在
    if (bQROneCode) {
      const qrRecord = payRecords.find(record => record.payType === PAYMENT_TYPE_B_SHOW_QR);
      if (qrRecord) {
        qrRecord.bQROneCode = bQROneCode;
      }
    }

    // 提取会员信息
    let memberId = '';
    let memberAmount = 0;
    if (state.memberInfo) {
      memberId = state.memberInfo.cardNo || '';
      memberAmount = state.memberInfo.balance || 0;
    }

    console.log('[toProductOrderInstantParams] 免单状态:', isFree);

    // 严格按照V3AddOrderAdditionalPayReqDto类型构建参数
    const params: V3AddOrderAdditionalPayReqDto = {
      // 房间和会话信息
      roomId: orderData.roomId || '',
      sessionId: orderData.sessionVO.id || '',
      // 时间相关
      currentTime: Math.floor(Date.now() / 1000),
      // 商品信息 - 使用统一的转换函数
      orderProductVOs: this.formatProductsForApi(orderData.productInfos),

      // 金额信息 - 使用计算的总支付金额，而不是最后一个支付方式的金额
      originalAmount: orderData.totalAmount || 0,
      originalFee: orderData.totalAmount || 0,
      payAmount: totalFee,
      shouldFee: orderData.totalAmount || 0,
      totalFee: totalFee,
      // 支付记录
      payRecords: payRecords,
      // 找零和抹零
      changeAmount: changeAmount,
      zeroFee: 0,
      // 折扣相关
      productDiscount: orderData.productDiscountRate || 100,
      productDiscountAmount: orderData.productReduceAmount || 0,
      roomDiscount: orderData.roomDiscountRate || 100,
      roomDiscountAmount: orderData.roomReduceAmount || 0,
      // 商家调整原因
      discountReason: orderData.adjustReason || '',
      // 会员相关
      memberId: memberId,
      memberAmount: memberAmount,
      // 挂账相关
      creditAmount: 0,
      creditAccountId: '',
      // 免单状态
      isFree: isFree
    };

    console.log('[toProductOrderInstantParams] 生成的点单立结参数:', params);
    return params;
  }

  /**
   * 构建从Props传入数据的开台立结参数
   */
  private buildOpenPayParamsFromProps(state: IOrderPayDialogState): any {
    const orderData = state.orderData as any; // 使用any类型避免类型错误
    if (!orderData) return null;

    console.log('[buildOpenPayParamsFromProps] state:', state);

    // 计算所有支付方式的总金额
    const totalPayAmount = state.payRecords.reduce((sum, record) => sum + (record.totalFee || 0), 0);

    // 使用计算得到的总支付金额，而不是actualAmount
    const actualAmountInFen = totalPayAmount;

    // 检查是否存在免单支付记录
    const hasFreePayRecord = state.payRecords.some(record => record.payType === 'free');
    const isFree = hasFreePayRecord || !!orderData.freeAmount;

    // 0元单特殊处理
    const payRecords =
      orderData.actualAmount === 0
        ? [
            {
              // 0元单使用免单支付方式
              payType: PaymentMethodType.Free,
              totalFee: 0,
              amountInput: '0.00'
            }
          ]
        : state.payRecords;

    // 处理套餐内商品，确保套餐属性被正确处理
    const inOrderProductInfos = this.formatProductsForApi(orderData.inOrderProductInfos || []);
    // 处理套餐外商品，确保套餐属性被正确处理
    const outOrderProductInfos = this.formatProductsForApi(orderData.outOrderProductInfos || []);

    // 处理房间计划数据，保持原始的orderRoomPlanVOS
    // 这样可以确保计时消费的更新被正确传递给API
    console.log('[buildOpenPayParamsFromProps] orderData.orderRoomPlanVOS:', orderData.orderRoomPlanVOS);

    const orderRoomPlanVOS = Array.isArray(orderData.orderRoomPlanVOS)
      ? orderData.orderRoomPlanVOS.map((plan: any) => {
          // 基本转换
          const basePlan = {
            pricePlanId: plan.pricePlanId || '',
            pricePlanName: plan.pricePlanName || '',
            roomId: plan.roomId || orderData.roomId,
            roomName: plan.roomName || orderData.roomName,
            startTime: plan.startTime || Math.floor(orderData.startTime / 1000),
            endTime: plan.endTime || Math.floor(orderData.endTime / 1000),
            duration: plan.duration || orderData.duration || 0,
            payAmount: plan.payAmount || orderData.roomAmount,
            originalPayAmount: plan.originalPayAmount || orderData.roomAmount,
            isTimeConsume: plan.isTimeConsume === true // 保留计时消费标志
          };

          // 保留其他可能有用的字段
          if (plan.isTimeConsume === true) {
            console.log('处理计时消费房间计划:', plan);
            // 对于计时消费，保留所有额外信息
            return {
              ...basePlan,
              pricePlanId: plan.pricePlanId || '',
              pricePlanName: plan.pricePlanName || '',
              selectedAreaId: plan.selectedAreaId || '',
              selectedRoomTypeId: plan.selectedRoomTypeId || '',
              timeChargeMode: plan.timeChargeMode || '',
              timeChargeType: plan.timeChargeType || '',
              unitPrice: plan.unitPrice || 0
            };
          }

          return basePlan;
        })
      : [
          {
            roomId: orderData.roomId,
            roomName: orderData.roomName,
            startTime: Math.floor(orderData.startTime / 1000),
            endTime: Math.floor(orderData.endTime / 1000),
            duration: orderData.duration || 0,
            payAmount: orderData.roomAmount,
            originalPayAmount: orderData.roomAmount
          }
        ];

    return {
      sessionId: state.sessionId,
      roomId: orderData.roomId,
      bookingId: orderData.bookingId,
      venueId: '',
      startTime: Math.floor(orderData.startTime / 1000),
      endTime: Math.floor(orderData.endTime / 1000),
      currentTime: Math.floor(Date.now() / 1000),
      consumptionMode: 'buyout',
      selectedAreaId: '',
      selectedRoomTypeId: '',
      timeChargeMode: '',
      buyMinute: orderData.duration || 0,
      timeChargeAmount: 0,
      timeChargeEndTime: '',
      timeChargeType: '',
      minimumCharge: orderData.minConsumptionAmount,
      originalAmount: orderData.totalAmount,
      payAmount: actualAmountInFen,
      originalFee: orderData.totalAmount,
      shouldFee: orderData.totalAmount,
      totalFee: actualAmountInFen,
      changeAmount: 0,
      zeroFee: 0,
      creditAmount: 0,
      orderRoomPlanVOS: orderRoomPlanVOS, // 使用处理后的房间计划数据
      inOrderProductInfos: inOrderProductInfos,
      outOrderProductInfos: outOrderProductInfos,
      payRecords: payRecords,
      discountAmount: orderData.discountAmount || 0,
      // 免单状态由支付记录和原始数据综合判断
      isFree: isFree,
      productDiscount: orderData.productDiscountRate || 100,
      productDiscountAmount: orderData.productReduceAmount || 0,
      roomDiscount: orderData.roomDiscountRate || 100,
      roomDiscountAmount: orderData.roomReduceAmount || 0,
      // 商家调整原因
      discountReason: orderData.adjustReason || '',
      memberInfo: state.memberInfo
        ? {
            cardNo: state.memberInfo.cardNo,
            name: state.memberInfo.name,
            phone: state.memberInfo.phone
          }
        : null,
      roomVO: null,
      printReceipt: state.printReceipt,
      employeeId: '',
      memberId: ''
    };
  }

  /**
   * 从 API 数据构建开台立结参数
   */
  private buildOpenPayParamsFromApi(state: IOrderPayDialogState): any {
    const orderData = state.orderData as any; // 使用any类型避免类型错误
    if (!orderData) return null;

    console.log('[buildOpenPayParamsFromApi] orderData:', orderData);

    // 计算所有支付方式的总金额
    const totalPayAmount = state.payRecords.reduce((sum, record) => sum + (record.totalFee || 0), 0);

    // 使用计算得到的总支付金额，而不是actualAmount
    const actualAmountInFen = totalPayAmount;

    // 检查是否存在免单支付记录
    const hasFreePayRecord = state.payRecords.some(record => record.payType === 'free');
    const isFree = hasFreePayRecord || !!orderData.freeAmount;

    // 0元单特殊处理
    const payRecords =
      orderData.actualAmount === 0
        ? [
            {
              // 0元单使用免单支付方式
              payType: PaymentMethodType.Free,
              totalFee: 0,
              amountInput: '0.00'
            }
          ]
        : state.payRecords;

    // 处理套餐内商品，确保套餐属性被正确处理
    const inOrderProductInfos = this.formatProductsForApi(state.orderData?.inOrderProductInfos || []);
    // 处理套餐外商品，确保套餐属性被正确处理
    const outOrderProductInfos = this.formatProductsForApi(state.orderData?.outOrderProductInfos || []);

    // 处理房间计划数据，保持原始的orderRoomPlanVOS
    // 这样可以确保计时消费的更新被正确传递给API
    const orderRoomPlanVOS = Array.isArray(orderData.orderRoomPlanVOS)
      ? orderData.orderRoomPlanVOS.map((plan: any) => {
          // 基本转换
          const basePlan = {
            roomId: plan.roomId || orderData.roomId,
            roomName: plan.roomName || orderData.roomName,
            startTime: plan.startTime,
            endTime: plan.endTime,
            duration: plan.duration,
            payAmount: plan.payAmount,
            originalPayAmount: plan.originalPayAmount,
            isTimeConsume: plan.isTimeConsume === true // 保留计时消费标志
          };

          // 保留其他可能有用的字段
          if (plan.isTimeConsume === true) {
            console.log('处理计时消费房间计划:', plan);
            // 对于计时消费，保留所有额外信息
            return {
              ...basePlan,
              pricePlanId: plan.pricePlanId || '',
              pricePlanName: plan.pricePlanName || '',
              selectedAreaId: plan.selectedAreaId || '',
              selectedRoomTypeId: plan.selectedRoomTypeId || '',
              timeChargeMode: plan.timeChargeMode || '',
              timeChargeType: plan.timeChargeType || '',
              unitPrice: plan.unitPrice || 0
            };
          }

          return basePlan;
        })
      : [
          {
            roomId: orderData.roomId,
            roomName: orderData.roomName,
            startTime: orderData._rawData?.startTime || Math.floor(orderData.startTime / 1000),
            endTime: orderData._rawData?.endTime || Math.floor(orderData.endTime / 1000),
            duration: orderData.duration || 0,
            payAmount: orderData.roomAmount,
            originalPayAmount: orderData.roomAmount
          }
        ];

    // API数据通常包含更多详细信息，能够从原始数据中提取，并保留原有的参数结构
    const rawData = orderData._rawData || {};
    return {
      sessionId: orderData.sessionId || state.sessionId,
      roomId: orderData.roomId,
      venueId: orderData.venueId || '',
      bookingId: rawData.bookingId,
      startTime: rawData.startTime || Math.floor(orderData.startTime / 1000),
      endTime: rawData.endTime || Math.floor(orderData.endTime / 1000),
      currentTime: Math.floor(Date.now() / 1000),
      consumptionMode: orderData.consumptionMode || 'buyout',
      selectedAreaId: orderData.selectedAreaId || '',
      selectedRoomTypeId: orderData.selectedRoomTypeId || '',
      timeChargeMode: rawData.timeChargeMode || '',
      buyMinute: orderData.buyMinute || orderData.duration || 0,
      timeChargeAmount: rawData.timeChargeAmount || 0,
      timeChargeEndTime: rawData.timeChargeEndTime || '',
      timeChargeType: rawData.timeChargeType || '',
      minimumCharge: orderData.minConsumptionAmount || 0,
      originalAmount: orderData.totalAmount,
      payAmount: actualAmountInFen,
      originalFee: orderData.totalAmount,
      shouldFee: actualAmountInFen,
      totalFee: actualAmountInFen,
      changeAmount: 0,
      zeroFee: 0,
      creditAmount: 0,
      orderRoomPlanVOS: orderRoomPlanVOS, // 使用处理后的房间计划数据
      inOrderProductInfos,
      outOrderProductInfos,
      payRecords,
      discountAmount: orderData.discountAmount || 0,
      // 免单状态由支付记录和原始数据综合判断
      isFree: isFree,
      productDiscount: orderData.productDiscountRate || 100,
      productDiscountAmount: orderData.productReduceAmount || 0,
      roomDiscount: orderData.roomDiscountRate || 100,
      roomDiscountAmount: orderData.roomReduceAmount || 0,
      // 商家调整原因
      discountReason: orderData.adjustReason || '',
      memberInfo: state.memberInfo
        ? {
            cardNo: state.memberInfo.cardNo,
            name: state.memberInfo.name,
            phone: state.memberInfo.phone
          }
        : null,
      roomVO: orderData.roomVO,
      printReceipt: state.printReceipt || false,
      employeeId: ''
    };
  }

  /**
   * 商品点单场景专用的转换方法，直接从orderData读取数据（包括之前在additionalOrderParams中的信息）
   * @param propsData 集成后的orderData（包含点单所需的全部信息）
   * @returns 视图模型状态
   */
  toViewModelFromProductOrder(propsData: OrderData): IOrderPayDialogState {
    console.log('[Converter:ProductOrder] 处理点单数据:', propsData);

    // 克隆数据避免修改原始数据
    const dataToProcess = cloneDeep(propsData);

    // 使用标准方法格式化订单数据
    const formattedOrderData: OrderData = this.formatOrderDataFromProps(dataToProcess);
    console.log('[Converter:ProductOrder] 格式化后的订单数据:', formattedOrderData);

    // 返回状态
    return {
      ...ORDER_PAY_DIALOG_STATE_DEFAULTS, // 基础默认值
      orderData: formattedOrderData,
      memberInfo: this.extractMemberInfo(dataToProcess),
      displayRoomInfo: this.extractRoomInfo(dataToProcess),
      payType: 'productOrderInstant', // 点单场景固定为productOrderInstant
      isInitialized: false, // 由Presenter控制
      loading: false, // 由Presenter控制
      isDataFromProps: true, // 标记为Props数据
      // 设置金额
      actualAmount: formattedOrderData.actualAmount ? this.formatAmount(formattedOrderData.actualAmount) : ORDER_PAY_DIALOG_STATE_DEFAULTS.actualAmount
    };
  }

  /**
   * 商品数据转换为API所需格式
   * @param items 购物车商品
   * @returns API所需的商品格式
   */
  formatProductsForApi(items: ProductInfo[] = []): any[] {
    return items.map(item => {
      // 判断是否为套餐 - 综合多种判断条件
      const isPackage =
        // 1. 明确标记为套餐
        item.isPackage === true ||
        // 2. 有套餐详情数组
        (!!item.packageProductInfo && Array.isArray(item.packageProductInfo) && item.packageProductInfo.length > 0) ||
        // 3. 有packageDetail字段
        !!item.packageDetail;

      console.log(`[formatProductsForApi] 商品[${item.productName}]判断是否为套餐:`, {
        isPackage,
        productId: item.productId || item.id,
        hasPackageInfo: !!item.packageProductInfo,
        isPackageArray: Array.isArray(item.packageProductInfo),
        packageInfoLength: item.packageProductInfo?.length,
        hasPackageDetail: !!item.packageDetail,
        packageProductInfo: item.packageProductInfo
      });

      // 处理套餐详情信息
      let packageDetail = undefined;
      if (isPackage) {
        try {
          // 优先使用packageProductInfo数组
          if (item.packageProductInfo && Array.isArray(item.packageProductInfo) && item.packageProductInfo.length > 0) {
            // 提取每个子商品的id、count信息，price从其他地方获取
            packageDetail = item.packageProductInfo.map(product => ({
              id: product.id || '',
              count: product.count || 1,
              // price字段不在BaseProductInfo中，从其他地方获取或设为0
              price: (product as any).price || 0,
              name: product.name || ''
            }));
            // 转为JSON字符串
            packageDetail = JSON.stringify(packageDetail);
            console.log(`[formatProductsForApi] 从packageProductInfo生成的packageDetail: ${packageDetail}`);
          }
          // 处理复杂的packageDetail字符串
          else if (item.packageDetail) {
            // 如果packageDetail已经是字符串
            if (typeof item.packageDetail === 'string') {
              try {
                // 尝试解析packageDetail字符串
                const detailObj = JSON.parse(item.packageDetail);

                // 提取packageProducts字段 - 这是我们需要的商品明细
                if (detailObj.packageProducts) {
                  // 如果packageProducts是字符串，解析它
                  if (typeof detailObj.packageProducts === 'string') {
                    packageDetail = detailObj.packageProducts; // 直接使用它
                  } else {
                    // 如果是对象，转换为字符串
                    packageDetail = JSON.stringify(detailObj.packageProducts);
                  }

                  console.log(`从packageDetail中提取的packageProducts: ${packageDetail}`);
                } else {
                  // 如果没有packageProducts字段，使用整个字符串
                  packageDetail = item.packageDetail;
                }
              } catch (e) {
                console.error('解析packageDetail字符串失败:', e);
                packageDetail = item.packageDetail; // 失败时使用原始字符串
              }
            }
            // 如果是对象，尝试转换
            else {
              // 如果packageDetail是对象且有packageProducts字段
              if (item.packageDetail.packageProducts) {
                if (typeof item.packageDetail.packageProducts === 'string') {
                  packageDetail = item.packageDetail.packageProducts; // 直接使用它
                } else {
                  // 如果是对象数组，转换为字符串
                  packageDetail = JSON.stringify(item.packageDetail.packageProducts);
                }
              } else {
                // 如果没有packageProducts字段，转换整个对象
                packageDetail = JSON.stringify(item.packageDetail);
              }
            }
          }

          // 如果还是没有packageDetail，创建一个空的结构
          if (!packageDetail) {
            packageDetail = '[]';
          }
        } catch (e) {
          console.error('处理套餐详情信息失败:', e);
          packageDetail = '[]'; // 出错时使用空数组
        }
      }

      // 构建API需要的商品结构
      return {
        // 套餐使用packageId，普通商品使用productId
        productId: isPackage ? '' : item.productId || item.id || '',
        packageId: isPackage ? item.productId || item.id || '' : '',
        productName: item.productName,
        quantity: item.quantity,
        flavors: item.flavors || '',
        unit: item.unit || '份',
        payPrice: item.payPrice,
        originalPrice: item.originalPrice,
        payAmount: item.payAmount,
        originalAmount: item.originalAmount,
        // 套餐详情 - 确保传递给API
        packageProductInfo: packageDetail,
        // 添加标记，方便调试
        isPackage: isPackage,
        isGift: item.isGift
      };
    });
  }
}
