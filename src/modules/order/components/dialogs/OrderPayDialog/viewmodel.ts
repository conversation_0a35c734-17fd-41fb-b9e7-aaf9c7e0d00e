import { ComputedRef } from 'vue';
import { OrderData, PaymentMethod, MemberInfo, AdjustData, ExtendedOrderData, PaymentMethodType } from './orderPayEntity';

// 支付类型定义
export type OrderPayType = 'later' | 'immediate' | 'productOrderInstant' | 'continuePay';

// UI状态接口
export interface IOrderPayDialogState {
  visible: boolean;
  loading: boolean;
  orderData: OrderData | null;
  selectedPayMethod: PaymentMethodType | '';
  actualAmount: string;
  printReceipt: boolean;
  memberInfo: MemberInfo | null;
  scannerDialogVisible: boolean;
  creditAccountVisible: boolean;
  memberCardVisible: boolean;
  displayRoomInfo: {
    roomName: string;
    startTime: number;
    endTime: number;
    duration: number;
    roomFee: number;
    pricePlanName: string;
    unitPrice: number;
    isTimeConsume: boolean;
    timeDisplay: string;
  } | null;
  // 支付方式：'later'(开台后结) 或 'immediate'(开台立结) 或 'productOrderInstant'(点单立结) 或 'continuePay'(续台立结)
  payType: OrderPayType;
  // 是否已初始化
  isInitialized: boolean;
  // 是否通过传入数据方式初始化
  isDataFromProps: boolean;
  orderNos: string[];
  sessionId: string;
  isComboPay: boolean;
  onSuccess?: (result: any) => void;
  onCancel?: () => void;
  // 附加订单参数
  additionalOrderParams?: any;
  // 支付记录列表 - 支持多笔支付方式组合
  payRecords: Array<{
    payType: string;
    totalFee: number;
    [key: string]: any;
  }>;
}

// UI计算属性接口
export interface IOrderPayDialogComputed {
  // 计算找零金额
  calculateChangeAmount: ComputedRef<string>;
  // 支付方式列表
  paymentMethods: ComputedRef<PaymentMethod[]>;
  // 支付类型文本
  payTypeText: ComputedRef<string>;
  // 房间消费时间格式化
  roomConsumptionTime: ComputedRef<string>;
  // 房间消费类型
  roomConsumptionType: ComputedRef<string>;
  // 计算已支付金额总和
  totalPaidAmount: ComputedRef<number>;
  // 是否可以确认支付
  canPay: ComputedRef<boolean>;
  // 计算剩余应付金额
  remainingAmount: ComputedRef<number>;
  // 判断当前输入的支付金额是否可以添加
  canAddCurrentPayment: ComputedRef<boolean>;
}

// UI动作接口
export interface IOrderPayDialogActions {
  // 弹窗生命周期
  onDialogOpen(): void;
  onDialogClose(): void;
  // 此方法目前由视图层负责，保留签名仅用于兼容性
  handleClose(): void;

  // 初始化
  init(sessionId?: string, orderData?: Record<string, any>, payType?: OrderPayType): Promise<void>;

  // 支付方式相关
  selectPayMethod(method: PaymentMethodType): void;
  handleActualAmountInput(value: string): void;
  handleActualAmountBlur(): void;

  // 扫码支付相关
  handleScanInput(e: KeyboardEvent): void;
  handleScanComplete(): Promise<void>;

  // 支付操作
  handlePay(): Promise<void>;

  // 对话框相关
  openMemberCardDialog(): void;
  openCreditAccountDialog(): void;
  openAdjustDialog(): void;
  handleMemberConfirm(info: MemberInfo): void;
  handleCreditAccountConfirm(data: any): void;
  handleAdjustConfirm(adjustData: AdjustData): void;

  // 弹窗操作
  handleConfirm(): Promise<void>;
  handleCancel(): void;
  handleSuccess(result: any): void;
  openMemberCardPayDialog(): Promise<void>;

  // 支付记录相关
  addCurrentPayment(): void;
  removePayRecord(index: number): void;
  handlePayRecordAmountChange(value: string, index: number): void;
  handlePayRecordAmountBlur(record: { payType: string; totalFee: number; amountInput?: string }, index: number): void;
  handleConfirmPay(): Promise<void>;
}

// 总的ViewModel接口
export interface IOrderPayDialogViewModel {
  state: IOrderPayDialogState;
  computed: IOrderPayDialogComputed;
  actions: IOrderPayDialogActions;
}

// 导出常量
export const ORDER_PAY_DIALOG_STATE_DEFAULTS: IOrderPayDialogState = {
  orderNos: [],
  visible: false,
  loading: false,
  orderData: null,
  selectedPayMethod: '',
  actualAmount: '0.00',
  printReceipt: true,
  memberInfo: null,
  scannerDialogVisible: false,
  creditAccountVisible: false,
  memberCardVisible: false,
  displayRoomInfo: null,
  payType: 'later',
  isInitialized: false,
  isDataFromProps: false,
  sessionId: '',
  isComboPay: false,
  payRecords: []
};
