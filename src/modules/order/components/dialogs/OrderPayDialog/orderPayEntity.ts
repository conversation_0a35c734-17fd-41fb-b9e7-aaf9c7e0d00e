import { BaseProductInfo, OrderOpenVO, OrderRoomPlanVO, RoomVO } from '@/types/projectobj';
import { SessionVO } from '@/api/autoGenerated/shared/types/other';

// 支付方式枚举
export enum PaymentMethodType {
  Scanner = 'scanner',
  WeChat = 'wechat_record',
  AliPay = 'alipay_record',
  Member = 'member_card',
  Cash = 'cash',
  Bank = 'bank_record',
  Meituan = 'meituan',
  Koubei = 'koubei',
  Coupon = 'coupon',
  Guazhang = 'guazhang',
  BShowQR = 'bShowQR', // 添加扫码支付类型
  Free = 'free' // 添加免单支付类型
}

// 支付类型映射
export const PAY_TYPE_MAP: Record<string, string> = {
  cash: 'cash', // 现金
  wechat: 'wechat_record', // 微信记账
  alipay: 'alipay_record', // 支付宝记账
  bank: 'bank_record', // 银联记账
  scanner: 'bShowQR', // 乐刷付款码
  member: 'member_card' // 会员卡
};

// 会员信息实体
export interface MemberInfo {
  cardNo: string;
  name: string;
  phone?: string;
  balance?: number;
  creditAccountId?: string;
  creditAmount?: number;
  levelName?: string;
  id?: string; // 兼容旧代码
}

// 商品信息实体
export interface ProductInfo {
  productName: string;
  quantity: number;
  price: number;
  payPrice: number;
  orderNo: string;
  originalPrice: number;
  originalAmount: number;
  payAmount: number;
  id?: string;
  // 商品ID、套餐ID
  productId?: string;
  packageId?: string; // 套餐ID
  packageProductInfo?: Array<{ id?: string; name?: string; count?: number; price?: number; [key: string]: any }> | null; // 套餐明细信息，支持API和Props两种格式
  packageDetail?: string | Record<string, any> | null; // 新增: 套餐详情（临时类型）
  isPackage?: boolean; // 新增: 是否套餐
  unit?: string;
  status?: 'unpaid' | 'paid' | 'refunded';
  taste?: string;
  flavors?: string;
  RefundCount?: number;
  RefundFee?: number;
  isGift?: boolean; // 新增: 是否赠品
  isFree?: boolean; // 是否免费
}

// 调整数据实体
export interface AdjustData {
  adjustedRoomAmount: number; // 调整后的房费(分)
  adjustedProductAmount: number; // 调整后的商品费(分)
  discountAmount: number; // 优惠金额
  productDiscountPercent: number; // 商品折扣率
  roomDiscountPercent: number; // 房费折扣率
  productDiscountAmount: number; // 商品减免金额
  roomDiscountAmount: number; // 房费减免金额
  freeOrder: boolean; // 是否免单
  reason: string; // 调整原因
}

// 支付参数实体
export interface PaymentParams {
  sessionId: string;
  payType: string;
  originalFee: number;
  payAmount: number;
  changeAmount: number;
  orderNos: string[];
  roomId: string;
  discountProductRate: number;
  discountRoomRate: number;
  reduceProductAmount: number;
  reduceRoomAmount: number;
  creditAccountId?: string;
  creditAmount?: number;
  freeAmount: number;
  bQROneCode?: string;
  discountAmount: number;
}

// 订单数据实体
export interface OrderData {
  roomName: string;
  roomAmount: number;
  productAmount: number;
  startTime: number;
  endTime: number;
  duration: number;
  roomFee: number;
  supermarketFee: number;
  totalAmount: number;
  unpaidAmount: number;
  actualAmount: number;
  changeAmount: number;
  minConsumptionAmount: number; // 最低消费金额
  lowConsumptionDiffAmount: number; // 低消差额
  inOrderProductInfos: ProductInfo[]; //买断套餐内商品
  outOrderProductInfos: ProductInfo[]; //买断套餐外商品
  productInfos: ProductInfo[]; //商品账单
  orderRoomPlanVOS: OrderRoomPlanVO[]; //包厢账单
  roomId: string;
  bookingId: string;
  orderNos: string[];
  unpaidOrderNos: string[];
  sessionVO: SessionVO;
  productDiscountRate?: number;
  roomDiscountRate?: number;
  productReduceAmount?: number;
  roomReduceAmount?: number;
  freeAmount?: number;
  discountAmount?: number; // 优惠金额
  adjustReason?: string; // 调整原因
  paymentMethods?: PaymentMethod[]; // 支付方式列表
}

// 支付方式配置实体
export interface PaymentMethod {
  label: string;
  value: PaymentMethodType;
  icon: string;
}

// 支付方式列表
export const PAYMENT_METHODS: PaymentMethod[] = [
  { label: '扫码枪', value: PaymentMethodType.Scanner, icon: 'Aim' },
  { label: '微信', value: PaymentMethodType.WeChat, icon: 'ChatDotRound' },
  { label: '支付宝', value: PaymentMethodType.AliPay, icon: 'Money' },
  { label: '会员卡', value: PaymentMethodType.Member, icon: 'UserFilled' },
  { label: '现金', value: PaymentMethodType.Cash, icon: 'Wallet' },
  { label: '银行卡', value: PaymentMethodType.Bank, icon: 'CreditCard' }
  // { label: '免单', value: PaymentMethodType.Free, icon: 'Discount' }
  // { label: '美团', value: PaymentMethodType.Meituan, icon: 'Food' },
  // { label: '口碑', value: PaymentMethodType.Koubei, icon: 'Star' },
];

/**
 * 扩展的订单数据接口
 * 用于点单场景，包含额外的必要字段
 */
export interface ExtendedOrderData extends OrderData {
  // 场所ID
  venueId?: string;
  // 员工ID
  employeeId?: string;
  // 订单商品列表
  orderProductVOs?: any[];
  // 原始订单金额
  originalOrderAmount?: number;
}

// 使用ExtendedOrderData类型更新toViewModelFromProductOrder函数签名

// 导出扫码支付类型常量
export const PAYMENT_TYPE_B_SHOW_QR = PaymentMethodType.BShowQR;

// 导出免单支付类型常量
export const PAYMENT_TYPE_FREE = PaymentMethodType.Free;
