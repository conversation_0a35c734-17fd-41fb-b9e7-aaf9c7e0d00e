import { reactive, computed, ComputedRef, watch, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { nanoid } from 'nanoid';
import {
  IOrderPayDialogViewModel,
  IOrderPayDialogState,
  IOrderPayDialogComputed,
  IOrderPayDialogActions,
  ORDER_PAY_DIALOG_STATE_DEFAULTS,
  OrderPayType
} from './viewmodel';
import { OrderPayDialogConverter } from './converter';
import { OrderPayDialogInteractor } from './interactor';
import { OrderData, PaymentMethod, PaymentMethodType, MemberInfo, AdjustData, PAYMENT_METHODS, PAYMENT_TYPE_B_SHOW_QR, PAY_TYPE_MAP } from './orderPayEntity';
import { ElMessage } from 'element-plus';
import { formatYuanWithSymbol, convertToYuan, floorDiscount } from '@/utils/priceUtils';
import { DialogManager } from '@/utils/dialog'; // 引入DialogManager

import { useDialogStore } from '@/stores/dialog/manager';
import type { PayBillVO } from '@/api/autoGenerated';
import { useVenueStore } from '@/stores/venueStore';

/**
 * OrderPayDialog 协调层
 * 实现 IOrderPayDialogViewModel 接口，协调视图和业务逻辑
 */

export class OrderPayDialogPresenter implements IOrderPayDialogViewModel {
  private readonly dialogManager = DialogManager;
  private readonly dialogStore = useDialogStore();
  private readonly venueStore = useVenueStore();
  state: IOrderPayDialogState;
  interactor: OrderPayDialogInteractor;
  converter: OrderPayDialogConverter;
  scanBuffer: string = ''; // 初始化为空字符串

  constructor() {
    this.state = reactive({ ...ORDER_PAY_DIALOG_STATE_DEFAULTS });
    this.interactor = new OrderPayDialogInteractor();
    this.converter = new OrderPayDialogConverter();
    this.scanBuffer = ''; // 在构造函数中重置
    // 确保初始化时有一个空数组，但只在第一次创建时使用reactive
    if (!this.state.payRecords) {
      this.state.payRecords = reactive([]);
    }
    // 观察扫码对话框状态变化
    this.watchScannerDialogVisible();
  }

  /**
   * 观察扫码对话框状态变化，添加或移除监听器
   */
  private watchScannerDialogVisible() {
    watch(
      () => this.state.scannerDialogVisible,
      newValue => {
        if (newValue) {
          // 打开扫码对话框时添加监听器
          window.addEventListener('keypress', this.actions.handleScanInput);
        } else {
          // 关闭扫码对话框时移除监听器
          window.removeEventListener('keypress', this.actions.handleScanInput);

          // 扫码弹窗关闭时不清空支付记录，保持用户已选择的支付方式
          // 这样用户再次点击确认结账时，可以继续使用之前选择的扫码支付方式
        }
      }
    );
  }

  /**
   * 计算属性
   */
  public computed: IOrderPayDialogComputed = {
    // 计算找零金额
    calculateChangeAmount: computed(() => {
      if (!this.state.orderData || !this.state.actualAmount || this.state.selectedPayMethod !== 'cash') {
        return '0.00';
      }

      const actualAmount = parseFloat(this.state.actualAmount) * 100;
      const orderAmount = this.state.orderData.actualAmount;

      console.log('[orderPayDialog] actualAmount', actualAmount, orderAmount);

      if (isNaN(actualAmount) || actualAmount <= orderAmount) {
        return '0.00';
      }

      return (actualAmount - orderAmount).toFixed(2);
    }),

    // 支付方式列表
    paymentMethods: computed(() => {
      // 获取当前场所信息
      const venue = this.venueStore.venue;
      
      // 过滤支付方式列表
      return PAYMENT_METHODS.filter(method => {
        // 如果是扫码支付，根据isLeshuaPay字段判断是否显示
        if (method.value === PaymentMethodType.Scanner) {
          // 如果isLeshuaPay为0，不显示扫码支付选项；其他值显示
          return venue?.isLeshuaPay !== 0;
        }
        // 其他支付方式正常显示
        return true;
      });
    }),

    // 支付类型文本
    payTypeText: computed(() => {
      if (this.state.payType === 'later') {
        return '开台后结';
      } else if (this.state.payType === 'immediate') {
        return '开台立结';
      } else if (this.state.payType === 'productOrderInstant') {
        return '点单立结';
      } else if (this.state.payType === 'continuePay') {
        return '续台立结';
      }
      return '';
    }),

    // 房间消费时间格式化
    roomConsumptionTime: computed(() => {
      // 如果displayRoomInfo是数组，选取第一个元素的timeDisplay
      if (Array.isArray(this.state.displayRoomInfo) && this.state.displayRoomInfo.length > 0) {
        return this.state.displayRoomInfo[0].timeDisplay;
      }

      // 如果displayRoomInfo是单个对象，直接使用其timeDisplay
      if (this.state.displayRoomInfo && this.state.displayRoomInfo.timeDisplay) {
        return this.state.displayRoomInfo.timeDisplay;
      }

      // 兼容旧数据，调用格式化方法
      const orderData = this.state.orderData as any;
      // 临时处理，避免报错
      return orderData?.originalConsumptionTime || '';
    }),

    // 房间消费类型
    roomConsumptionType: computed(() => {
      console.log('[orderPayDialog] this.state.orderData', this.state);
      return '';
    }),

    // 计算已支付金额总和
    totalPaidAmount: computed(() => {
      if (!this.state.payRecords || this.state.payRecords.length === 0) {
        return 0;
      }
      return this.state.payRecords.reduce((sum, record) => sum + (record.totalFee || 0), 0);
    }),

    // 是否可以确认支付
    canPay: computed(() => {
      if (!this.state.orderData) return false;

      // 如果是0元单可以直接支付
      if (this.state.orderData.actualAmount === 0) {
        return true;
      }

      // 总支付金额等于应付金额，则可以支付
      const orderAmount = this.state.orderData.actualAmount;
      return this.computed.totalPaidAmount.value === orderAmount;
    }),

    // 计算剩余应付金额
    remainingAmount: computed(() => {
      if (!this.state.orderData) return 0;
      const orderAmount = this.state.orderData.actualAmount;
      return Math.max(0, orderAmount - this.computed.totalPaidAmount.value);
    }),

    // 判断当前输入的支付金额是否可以添加
    canAddCurrentPayment: computed(() => {
      if (!this.state.selectedPayMethod || !this.state.actualAmount) return false;
      const amount = parseFloat(this.state.actualAmount) * 100;
      return !isNaN(amount) && amount > 0 && amount <= this.computed.remainingAmount.value;
    })
  };

  /**
   * 统一处理支付成功的逻辑
   * @param sessionId 开台sessionId
   * @param orderNosToPrint 需要打印的订单号数组
   */
  private processPaymentSuccess(sessionId?: string, orderNosToPrint?: string[], payBillId?: string) {
    // 1. 显示成功消息
    console.log('[orderPayDialog] processPaymentSuccess 支付成功!!!');
    ElMessage.success('支付成功');

    // 2. 打印小票
    if (sessionId) {
      try {
        this.interactor.printCheckoutBill(payBillId, sessionId, orderNosToPrint);
      } catch (printError) {
        console.error('打印小票过程发生错误:', printError);
        // 打印失败不影响支付流程
      }
    }

    // 3. 统一处理开台相关单据打印 - 针对开台立结和续台立结
    if (sessionId) {
      if (this.state.payType === 'immediate' || this.state.payType === 'continuePay') {
        try {
          console.log('支付成功后统一打印开台相关单据, 会话ID:', sessionId, '支付类型:', this.state.payType, '订单号:', orderNosToPrint);
          this.interactor.printOpenTableDocuments(sessionId, orderNosToPrint, this.state.payType);
        } catch (printError) {
          console.error('打印开台相关单据时发生错误:', printError);
          // 打印失败不影响支付流程
        }
      } else if (this.state.payType === 'productOrderInstant') {
        try {
          this.interactor.printProductionOrder(orderNosToPrint, sessionId);
        } catch (printError) {
          console.error('打印出品单时发生错误:', printError);
          // 打印失败不影响支付流程
        }
      }
    }

    // 4. 触发成功回调（视图层负责关闭弹窗）
    this.actions.handleSuccess(true);

    // 5. 如果有外部成功回调，也调用它
    if (this.state.onSuccess) {
      this.state.onSuccess(true);
    }

    return true;
  }

  /**
   * 统一处理错误的方法
   * @param message 错误消息
   * @param showMessage 是否显示消息提示
   */
  private handleError(message: string, showMessage: boolean = true) {
    console.error(message);
    if (showMessage) {
      ElMessage.error(message);
    }
  }

  /**
   * 计算调整后的房费和商品费
   * 基于原始金额和折扣/减免计算，避免重复调整
   */
  private calculateAdjustedAmounts() {
    if (!this.state.orderData) return { roomAmount: 0, productAmount: 0 };

    // 获取原始金额和折扣/减免信息
    const originalRoomAmount = this.state.orderData.roomAmount || 0;
    const originalProductAmount = this.state.orderData.productAmount || 0;
    const roomDiscountRate = this.state.orderData.roomDiscountRate || 100; // 默认100%
    const productDiscountRate = this.state.orderData.productDiscountRate || 100; // 默认100%
    const roomReduceAmount = this.state.orderData.roomReduceAmount || 0;
    const productReduceAmount = this.state.orderData.productReduceAmount || 0;

    // 计算调整后的金额
    const adjustedRoomAmount = Math.max(0, Math.round(originalRoomAmount * (roomDiscountRate / 100)) - roomReduceAmount);
    // 商品折扣采用抹分策略：直接舍去小数分，避免四舍五入
    const adjustedProductAmount = Math.max(0, floorDiscount(originalProductAmount, productDiscountRate) - productReduceAmount);

    console.log('[orderPayDialog] 动态计算调整后金额:', {
      originalRoomAmount,
      originalProductAmount,
      roomDiscountRate,
      productDiscountRate,
      roomReduceAmount,
      productReduceAmount,
      adjustedRoomAmount,
      adjustedProductAmount
    });

    return {
      roomAmount: adjustedRoomAmount,
      productAmount: adjustedProductAmount
    };
  }

  /**
   * 动作实现
   */
  public actions: IOrderPayDialogActions = {
    // 弹窗生命周期
    onDialogOpen: () => {
      console.log('订单支付弹窗打开');
      // 确保每次打开弹窗时重置支付记录
      if (this.state.payRecords && this.state.payRecords.length > 0) {
        this.state.payRecords.splice(0, this.state.payRecords.length);
      }
      this.state.selectedPayMethod = '';
      this.state.actualAmount = '';
      this.scanBuffer = '';
    },

    onDialogClose: () => {
      console.log('订单支付弹窗关闭');

      // 关闭所有子对话框
      if (this.state.scannerDialogVisible) {
        this.state.scannerDialogVisible = false;
        window.removeEventListener('keypress', this.actions.handleScanInput);
      }

      // 清空支付记录 - 使用数组清空方法而不是创建新数组
      if (this.state.payRecords && this.state.payRecords.length > 0) {
        this.state.payRecords.splice(0, this.state.payRecords.length);
      }

      // 清空会员信息
      this.state.memberInfo = null;

      // 清空支付方式选择
      this.state.selectedPayMethod = '';

      // 清空实际支付金额
      this.state.actualAmount = '';

      // 清空扫码缓冲区
      this.scanBuffer = '';

      // 重置全部状态
      Object.assign(this.state, ORDER_PAY_DIALOG_STATE_DEFAULTS);
      console.log('[OrderPayDialog] 关闭弹窗', this.state);
    },

    // 此方法由视图层负责，这里仅作为接口兼容保留
    handleClose: () => {
      // 这个方法将在组件中被重写
      console.log('[OrderPayDialog] handleClose方法已废弃，仅作为接口兼容保留');
    },

    // 初始化
    init: async (sessionId?: string, orderData?: OrderData, payType: OrderPayType = 'later') => {
      try {
        // 确保converter存在
        if (!this.converter) {
          this.converter = new OrderPayDialogConverter();
        }

        // 在初始化前重置支付相关状态
        if (this.state.payRecords && this.state.payRecords.length > 0) {
          this.state.payRecords.splice(0, this.state.payRecords.length);
        }
        this.state.selectedPayMethod = '';
        this.state.actualAmount = '';
        this.scanBuffer = '';

        this.state.loading = true;
        this.state.payType = payType;
        console.log('[OrderPayDialog] 初始化:', this.state.payType, this.state.payRecords);

        let viewModelState: IOrderPayDialogState | null = null;

        // 根据payType和数据来源选择不同的处理路径
        if (payType === 'productOrderInstant') {
          // 使用专用转换方法 - 直接从orderData中获取所有信息
          viewModelState = this.converter.toViewModelFromProps(orderData as OrderData, { payType });

          this.state.isDataFromProps = true;
        } else if (payType === 'continuePay') {
          // 续台立结处理...（保持原有代码，这里不变）
          if (sessionId && orderData) {
            // 两者都有时 - 直接使用Props数据并设置sessionId
            viewModelState = this.converter.toViewModelFromProps(orderData as OrderData, { payType });
            viewModelState.sessionId = sessionId;
            this.state.isDataFromProps = true;
          } else if (orderData) {
            // 只有orderData - 使用Props数据
            viewModelState = this.converter.toViewModelFromProps(orderData as OrderData, { payType });
            this.state.isDataFromProps = true;
          } else if (sessionId) {
            // 只有sessionId - 获取并使用API数据
            const data = await this.interactor.getOrderBySession(sessionId);
            if (data) {
              viewModelState = this.converter.toViewModelFromApi(data, { payType });
              this.state.isDataFromProps = false;
            }
          } else {
            throw new Error('续台立结需要提供订单数据或sessionId');
          }
        } else {
          // 其他场景（标准开台后结或立结）
          if (sessionId) {
            // 通过sessionId获取订单数据
            const data = await this.interactor.getOrderBySession(sessionId);
            if (data) {
              // 使用 converter 的新方法转换 API 数据
              viewModelState = this.converter.toViewModelFromApi(data, { payType });
              this.state.isDataFromProps = false;
            }
          } else if (orderData) {
            // 使用 converter 的新方法转换 Props 数据
            viewModelState = this.converter.toViewModelFromProps(orderData as OrderData, { payType });
            this.state.isDataFromProps = true;
          } else {
            throw new Error('未提供订单数据或sessionId');
          }
        }

        // 更新state
        if (viewModelState) {
          Object.assign(this.state, viewModelState);
        } else {
          // 处理获取或转换失败的情况，可以重置状态或显示错误
          console.error('无法获取或转换订单数据');
          Object.assign(this.state, ORDER_PAY_DIALOG_STATE_DEFAULTS); // 重置为默认状态
          ElMessage.error('加载订单数据失败');
        }

        this.state.isInitialized = true;
        console.log('[OrderPayDialog] this.state', this.state);
      } catch (error) {
        console.error('初始化失败:', error);
        ElMessage.error('初始化失败: ' + (error instanceof Error ? error.message : String(error)));
        // 出错时也重置状态
        Object.assign(this.state, ORDER_PAY_DIALOG_STATE_DEFAULTS);
      } finally {
        this.state.loading = false;
      }
    },

    // 支付方式相关
    selectPayMethod: (method: PaymentMethodType) => {
      // 如果选择的是会员卡支付，并且已经存在会员卡支付记录，则不允许再次添加
      if (method === PaymentMethodType.Member && this.state.payRecords.some(record => record.payType === PaymentMethodType.Member)) {
        ElMessage.warning('已存在会员卡支付，不能添加多个会员卡支付');
        return;
      }

      this.state.selectedPayMethod = method as any;

      // 特殊支付方式处理
      if (method === PaymentMethodType.Member) {
        // 如果选择会员卡支付，打开会员卡支付对话框
        this.actions.openMemberCardPayDialog();
      } else if (method === ('credit_account' as any)) {
        // 使用字符串常量代替枚举值
        // 如果选择信用账户支付，打开信用账户对话框
        this.actions.openCreditAccountDialog();
      } else {
        // 所有其他支付方式（包括扫码支付）都采用相同的处理流程
        // 确保关闭扫码对话框
        if (this.state.scannerDialogVisible) {
          this.state.scannerDialogVisible = false;
          window.removeEventListener('keypress', this.actions.handleScanInput);
        }

        if (this.state.orderData) {
          // 计算剩余应付金额
          const orderAmount = this.state.orderData.actualAmount;
          const paidAmount = this.computed.totalPaidAmount.value;
          const remainingAmount = Math.max(0, orderAmount - paidAmount);

          if (remainingAmount <= 0) {
            ElMessage.warning('已达到应付金额，无法添加更多支付方式');
            return;
          }

          // 设置金额
          const defaultAmount = remainingAmount / 100;
          this.state.actualAmount = defaultAmount.toFixed(2);

          // 查找或创建支付记录
          const existingRecordIndex = this.state.payRecords.findIndex(record => record.payType === method);

          if (existingRecordIndex >= 0) {
            // 更新已有记录
            this.state.payRecords[existingRecordIndex].totalFee = remainingAmount;
            // 确保有amountInput字段供UI显示
            this.state.payRecords[existingRecordIndex].amountInput = defaultAmount.toFixed(2);
          } else {
            // 添加新支付记录
            const newRecord: any = {
              uuid: nanoid(),
              payType: method as string,
              totalFee: remainingAmount,
              amountInput: defaultAmount.toFixed(2) // 添加amountInput字段
            };
            this.state.payRecords.push(newRecord);
          }
        }
      }
    },

    // 输入金额处理
    handleActualAmountInput: (value: string) => {
      this.state.actualAmount = value;

      // 同步更新当前支付方式的记录
      if (this.state.selectedPayMethod && this.state.orderData) {
        const amount = parseFloat(value || '0');
        if (!isNaN(amount)) {
          const amountInFen = Math.round(amount * 100);

          // 查找当前支付方式的记录
          const existingRecordIndex = this.state.payRecords.findIndex(record => record.payType === this.state.selectedPayMethod);

          if (existingRecordIndex >= 0) {
            // 更新金额
            this.state.payRecords[existingRecordIndex].totalFee = amountInFen;
            // 更新输入框显示值
            this.state.payRecords[existingRecordIndex].amountInput = value;
          } else if (amountInFen > 0) {
            // 添加新记录
            this.state.payRecords.push({
              uuid: nanoid(),
              payType: this.state.selectedPayMethod as string,
              totalFee: amountInFen,
              amountInput: value // 添加输入框显示值
            });
          }

          // 如果金额为0，从支付记录中移除
          if (amountInFen === 0 && existingRecordIndex >= 0) {
            this.state.payRecords.splice(existingRecordIndex, 1);
          }
        }
      }
    },

    // 添加当前支付方式到支付记录
    addCurrentPayment: () => {
      if (!this.computed.canAddCurrentPayment.value) return;

      const amount = parseFloat(this.state.actualAmount) * 100;
      this.actions.handleActualAmountInput(this.state.actualAmount);

      // 清空当前输入
      this.state.actualAmount = '';
    },

    // 移除支付记录
    removePayRecord: (index: number) => {
      this.state.payRecords.splice(index, 1);
    },

    // 处理支付记录金额变更
    handlePayRecordAmountChange: (value: string, index: number) => {
      // 更新UI显示值
      this.state.payRecords[index].amountInput = value;

      // 尝试解析为数字
      const amount = parseFloat(value) * 100;
      if (!isNaN(amount)) {
        if (amount <= 0) {
          // 如果输入值为0或负值，删除该支付方式记录
          this.state.payRecords.splice(index, 1);
          return;
        }
        // 更新实际金额（分）
        this.state.payRecords[index].totalFee = amount;
      }
    },

    // 处理失去焦点时的格式化
    handlePayRecordAmountBlur: (record: { payType: string; totalFee: number; amountInput?: string }, index: number) => {
      // 确保有输入值
      if (!record.amountInput) {
        // 如果没有输入，则使用原始值的格式化
        record.amountInput = (record.totalFee / 100).toFixed(2);
        return;
      }

      // 解析输入值
      const amount = parseFloat(record.amountInput);
      if (!isNaN(amount)) {
        // 格式化为两位小数
        record.amountInput = amount.toFixed(2);
        // 更新实际金额（分）
        record.totalFee = Math.round(amount * 100);
      } else {
        // 如果输入无效，恢复原始值
        record.amountInput = (record.totalFee / 100).toFixed(2);
      }
    },

    // 处理确认支付
    handleConfirmPay: async () => {
      try {
        // 1. 检查是否有足够的支付记录
        if (!this.state.orderData) {
          ElMessage.warning('订单数据不存在');
          return;
        }

        const orderAmount = this.state.orderData.actualAmount;
        const paidAmount = this.computed.totalPaidAmount.value;

        console.log('[handleConfirmPay] 开始处理支付, 应付金额:', orderAmount / 100, '已付金额:', paidAmount / 100);
        console.log('[handleConfirmPay] 当前支付记录列表:', JSON.stringify(this.state.payRecords));

        // 检查是否为0元单
        if (orderAmount === 0) {
          console.log('[OrderPayDialog] 检测到0元单，使用免单支付方式');

          // 清空现有支付记录并添加一个免单类型的记录
          if (this.state.payRecords.length > 0) {
            this.state.payRecords.splice(0, this.state.payRecords.length);
          }

          // 添加一个金额为0的免单支付记录
          this.state.payRecords.push({
            uuid: nanoid(),
            payType: PaymentMethodType.Free,
            totalFee: 0,
            amountInput: '0.00'
          });
        } else {
          // 如果支付记录为空，提示用户选择支付方式
          if (this.state.payRecords.length === 0) {
            ElMessage.warning('请选择至少一种支付方式');
            return;
          }
        }

        // 2. 检查是否有扫码类型的支付记录
        const scannerRecords = this.state.payRecords.filter(record => record.payType === PaymentMethodType.Scanner);
        console.log('[handleConfirmPay] 扫码支付记录:', JSON.stringify(scannerRecords));

        // 3. 如果有扫码类型的支付记录且没有code，则打开扫码窗口
        if (scannerRecords.length > 0) {
          // 检查是否已经有扫码记录有code
          const needScan = scannerRecords.some(record => !record.bQROneCode);
          console.log('[handleConfirmPay] 是否需要扫码:', needScan, '当前扫码弹窗状态:', this.state.scannerDialogVisible);

          if (needScan) {
            // 打开扫码窗口前，设置 actualAmount 为扫码支付记录的金额
            const scanRecord = scannerRecords.find(record => !record.bQROneCode);
            if (scanRecord && scanRecord.amountInput) {
              this.state.actualAmount = scanRecord.amountInput;
            }

            // 打开扫码窗口
            console.log('[OrderPayDialog] 发现扫码支付记录，打开扫码窗口，设置scannerDialogVisible=true');
            this.state.scannerDialogVisible = true;
            console.log('[OrderPayDialog] 扫码窗口状态已更新:', this.state.scannerDialogVisible);
            this.scanBuffer = ''; // 清空扫码缓冲区
            window.addEventListener('keypress', this.actions.handleScanInput);
            return; // 等待扫码，不继续执行支付流程
          }
        }

        // 4. 如果当前有选中的支付方式但未添加到payRecords，则添加
        if (this.state.selectedPayMethod && this.state.actualAmount && this.computed.canAddCurrentPayment.value) {
          this.actions.addCurrentPayment();
        }

        // 5. 执行支付
        await this.actions.handlePay();
        this.actions.handleSuccess(true);
      } catch (error) {
        console.error('支付处理失败:', error);
        this.handleError('支付失败: ' + (error instanceof Error ? error.message : String(error)));
      }
    },

    // 失去焦点时格式化金额
    handleActualAmountBlur: () => {
      // 验证并格式化actualAmount
      const amount = parseFloat(this.state.actualAmount);
      if (!isNaN(amount)) {
        this.state.actualAmount = amount.toFixed(2);
      } else {
        this.state.actualAmount = '0.00';
      }
    },

    // 扫码支付相关
    handleScanInput: (event: KeyboardEvent) => {
      // 收集扫码输入
      if (event.key) {
        console.log('扫码输入', event.key);
        // 判断扫码是否完成(通常扫码枪会以回车结束)
        if (event.key === 'Enter') {
          // 调用扫码完成处理函数
          this.actions.handleScanComplete();
        } else {
          this.scanBuffer += event.key;
        }
      }
    },

    handleScanComplete: async () => {
      console.log('扫码支付完成', this.scanBuffer, this.state.orderData);
      console.log('[扫码支付] 当前支付记录列表:', JSON.stringify(this.state.payRecords));
      if (!this.scanBuffer || !this.state.orderData) return;

      try {
        this.state.loading = true;

        // 关闭扫码对话框并移除监听器
        this.state.scannerDialogVisible = false;
        window.removeEventListener('keypress', this.actions.handleScanInput);

        // 找到需要更新的扫码支付记录 - 修复过滤条件，只处理scanner类型
        const scannerRecords = this.state.payRecords.filter(record => record.payType === PaymentMethodType.Scanner);

        console.log('[扫码支付] 过滤出的扫码支付记录:', JSON.stringify(scannerRecords));

        if (scannerRecords.length === 0) {
          ElMessage.warning('未找到扫码支付记录');
          this.state.loading = false;
          return;
        }

        // 更新扫码支付记录，添加扫码信息
        for (const record of scannerRecords) {
          if (!record.bQROneCode) {
            // 只更新第一条没有code的记录
            record.bQROneCode = this.scanBuffer;
            // 同时转换payType为B码支付类型
            record.originalPayType = record.payType; // 保存原始支付类型
            record.payType = PAYMENT_TYPE_B_SHOW_QR;
            console.log('[扫码支付] 更新支付记录:', JSON.stringify(record));
            break;
          }
        }

        // 打印更新后的支付记录列表
        console.log('[扫码支付] 更新后的支付记录列表:', JSON.stringify(this.state.payRecords));

        // 清空扫码缓冲区
        this.scanBuffer = '';

        // 扫码成功后，直接触发支付 - 修复错误调用
        await this.actions.handlePay();
      } catch (error) {
        console.error('扫码支付处理失败:', error);
        ElMessage.error('扫码支付处理失败: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        this.state.loading = false;
      }
    },

    // 支付操作
    handlePay: async () => {
      try {
        if (!this.state.orderData) {
          ElMessage.warning('订单数据不存在');
          return;
        }

        // 验证支付记录是否存在
        if (!this.state.payRecords || this.state.payRecords.length === 0) {
          ElMessage.warning('请选择至少一种支付方式');
          return;
        }

        // 验证支付金额是否等于订单金额
        const orderAmount = this.state.orderData.actualAmount;
        const paidAmount = this.computed.totalPaidAmount.value;

        if (paidAmount !== orderAmount) {
          ElMessage.warning(`支付金额(${paidAmount / 100}元)与应付金额(${orderAmount / 100}元)不一致`);
          return;
        }

        this.state.loading = true;
        console.log('[handlePay] 开始执行支付, 支付记录:', JSON.stringify(this.state.payRecords));

        // 确保支付记录被深拷贝而不是引用
        // 创建支付记录的深拷贝以防止reactive对象丢失
        const payRecordsCopy = JSON.parse(JSON.stringify(this.state.payRecords));
        console.log('[handlePay] 支付记录深拷贝:', JSON.stringify(payRecordsCopy));

        // 手动将支付记录添加到state中以确保其被正确传递
        const stateCopy = { ...this.state };
        stateCopy.payRecords = payRecordsCopy;

        // 根据支付类型选择不同的处理方式，但统一使用state.payRecords
        let orderNosToPrint: string[] = [];
        let payBillVO: PayBillVO | undefined = undefined;
        let sessionId: string | undefined = undefined;

        if (this.state.payType === 'later') {
          // 开台后结
          const params = this.converter.toPayOrderParams(stateCopy);
          console.log('后结支付参数:', params);
          console.log('[handlePay] 后结支付参数中的支付记录:', JSON.stringify(params.payRecords));
          const payResult = await this.interactor.payOrder(params);
          orderNosToPrint = payResult?.orderNos || [];
          sessionId = payResult?.sessionId;
          payBillVO = payResult?.payBills?.[0];
        } else if (this.state.payType === 'immediate') {
          // 开台立结
          const params = this.converter.toOpenPayParams(stateCopy);
          console.log('立结支付参数:', params);
          console.log('[handlePay] 立结支付参数中的支付记录:', JSON.stringify(params.payRecords));
          const payResult = await this.interactor.openPay(params);
          orderNosToPrint = payResult?.orderNos || [];
          payBillVO = payResult?.payBills?.[0];
          sessionId = payResult?.sessionId;
        } else if (this.state.payType === 'continuePay') {
          // 续台立结
          const params = this.converter.toOpenPayParams(stateCopy);
          console.log('续台立结支付参数:', params);
          console.log('[handlePay] 续台立结支付参数中的支付记录:', JSON.stringify(params.payRecords));
          const payResult = await this.interactor.openContinuePay(params);
          orderNosToPrint = payResult?.orderNos || [];
          payBillVO = payResult?.payBills?.[0];
          sessionId = payResult?.sessionId;
        } else if (this.state.payType === 'productOrderInstant') {
          // 点单立结
          const params = this.converter.toProductOrderInstantParams(stateCopy);
          console.log('点单立结支付参数:', params);
          console.log('[handlePay] 点单立结支付参数中的支付记录:', JSON.stringify(params.payRecords));
          const payResult = await this.interactor.productOrderInstantPay(params);
          orderNosToPrint = payResult?.orderNos || [];
          payBillVO = payResult?.payBills?.[0];
          sessionId = payResult?.sessionId;
        }

        // 处理扫码支付特殊验证逻辑
        const hasShowQrPayment = this.state.payRecords.some(record => record.payType === PAYMENT_TYPE_B_SHOW_QR);
        console.log('[handlePay] 是否有扫码支付记录:', hasShowQrPayment, 'payRecords:', JSON.stringify(this.state.payRecords));

        if (hasShowQrPayment) {
          if (payBillVO) {
            console.log('[handlePay] 准备验证扫码支付, 账单:', payBillVO);
            const isPaySuccess = await this.queryAndConfirmScanPayment(sessionId, payBillVO);
            console.log('[handlePay] 扫码支付验证结果:', isPaySuccess);
            if (!isPaySuccess) {
              this.state.loading = false;
              return;
            }
          } else {
            console.warn('扫码支付缺少payBillVO，无法验证支付状态');
          }
        }

        console.log('[handlePay] 处理支付成功, sessionId:', sessionId, '订单号:', orderNosToPrint, 'payBillVO:', payBillVO);
        // 非扫码支付或扫码验证通过，处理成功
        this.processPaymentSuccess(sessionId, orderNosToPrint, payBillVO?.billId);
      } catch (error) {
        console.error('支付失败:', error);
        // ElMessage.error('支付失败: ' + (error instanceof Error ? error.message : String(error)));
        // 支付失败时不关闭弹窗，不跳转到首页，只需释放loading状态
        this.state.loading = false;
        throw error;
      }
    },

    // 对话框相关
    openMemberCardDialog: () => {
      this.state.memberCardVisible = true;
    },

    openCreditAccountDialog: () => {
      this.state.creditAccountVisible = true;
    },

    openAdjustDialog: () => {
      // 使用DialogManager打开对话框，不再使用状态变量
      if (!this.state.orderData) {
        ElMessage.warning('订单数据不存在');
        return;
      }

      // 准备初始化数据 - 从当前订单数据中获取已有的调整信息
      const initialData = {
        roomDiscountPercent: this.state.orderData.roomDiscountRate || 100,
        productDiscountPercent: this.state.orderData.productDiscountRate || 100,
        freeOrder: this.state.orderData.roomDiscountRate === 100 && this.state.orderData.productDiscountRate === 100 && this.state.orderData.actualAmount === 0,
        reason: this.state.orderData.adjustReason || '', // 假设订单数据中有调整原因字段
        roomDiscountAmount: this.state.orderData.roomReduceAmount || 0,
        productDiscountAmount: this.state.orderData.productReduceAmount || 0
      };

      // 使用DialogManager打开对话框
      console.log('[orderPayDialog] 准备打开商家调整对话框，订单数据:', {
        roomAmount: this.state.orderData.roomAmount || 0,
        productAmount: this.state.orderData.productAmount || 0,
        initialData
      });

      // 这里返回的Promise才是关键，需要用then接收结果
      DialogManager.open('MerchantAdjustDialog', {
        roomAmount: this.state.orderData.roomAmount || 0,
        productAmount: this.state.orderData.productAmount || 0,
        initialData
      })
        .then((adjustData: AdjustData) => {
          console.log('[orderPayDialog] 收到调整对话框返回数据:', adjustData);
          // 处理调整确认
          this.actions.handleAdjustConfirm(adjustData);
        })
        .catch(error => {
          console.error('[orderPayDialog] 调整对话框发生错误:', error);
        });
    },

    handleMemberConfirm: (info: MemberInfo) => {
      // 检查是否已存在会员卡支付记录
      if (this.state.payRecords.some(record => record.payType === PaymentMethodType.Member)) {
        ElMessage.warning('已存在会员卡支付记录，不能添加多个会员卡支付');
        this.state.memberCardVisible = false;
        return;
      }

      this.state.memberInfo = info;
      this.state.memberCardVisible = false;

      // 获取会员卡ID
      const memberId = info.id;

      // 如果没有ID则返回
      if (!memberId) {
        ElMessage.warning('无效的会员信息');
        return;
      }

      // 确保订单数据存在
      if (!this.state.orderData) {
        ElMessage.warning('订单数据不存在');
        return;
      }

      // 计算剩余应付金额
      const totalOrderAmount = this.state.orderData.actualAmount || 0;
      const totalPaidAmount = this.computed.totalPaidAmount.value || 0;
      const remainingAmount = Math.max(0, totalOrderAmount - totalPaidAmount);

      // 动态计算调整后的房费和商品费
      const adjustedAmounts = this.calculateAdjustedAmounts();
      const adjustedRoomAmount = adjustedAmounts.roomAmount;
      const adjustedProductAmount = adjustedAmounts.productAmount;

      // 按比例计算已支付金额中的商品和房间占比
      let remainingProductAmount = adjustedProductAmount;
      let remainingRoomAmount = adjustedRoomAmount;

      // 优先扣减商品费用，商品费用扣完后再扣减房间费用
      if (totalPaidAmount > 0) {
        if (totalPaidAmount >= adjustedProductAmount) {
          // 已支付金额超过商品费用，商品费用已全部支付
          remainingProductAmount = 0;
          // 剩余的支付金额用于抵扣房间费用
          remainingRoomAmount = Math.max(0, adjustedRoomAmount - (totalPaidAmount - adjustedProductAmount));
        } else {
          // 已支付金额未超过商品费用，商品费用部分支付
          remainingProductAmount = adjustedProductAmount - totalPaidAmount;
          // 房间费用还未开始支付
          remainingRoomAmount = adjustedRoomAmount;
        }
      }

      console.log('[orderPayDialog] 选择会员后打开会员卡支付对话框, 会员ID:', memberId, {
        adjustedRoomAmount,
        adjustedProductAmount,
        remainingRoomAmount,
        remainingProductAmount
      });

      // 使用选中的会员ID打开会员卡支付对话框
      DialogManager.open('MemberCardPayDialog', {
        memberCardId: memberId,
        roomAmount: remainingRoomAmount,
        goodsAmount: remainingProductAmount
      })
        .then(payRecord => {
          console.log('[orderPayDialog] 会员卡支付对话框返回结果:', payRecord);

          // 验证返回的支付记录是否有效
          if (payRecord && payRecord.success === true && payRecord.memberCardId) {
            console.log('[orderPayDialog] 会员卡支付成功，将处理支付记录');

            // 将会员卡支付记录添加到支付记录列表中
            const memberCardPayRecord = {
              uuid: nanoid(),
              payType: PaymentMethodType.Member,
              totalFee: payRecord.totalFee,
              amountInput: (payRecord.totalFee / 100).toFixed(2),
              memberCardId: payRecord.memberCardId,
              principalAmount: payRecord.principalAmount,
              memberCommonBonusAmount: payRecord.memberCommonBonusAmount,
              memberGoodsBonusAmount: payRecord.memberGoodsBonusAmount,
              memberRoomBonusAmount: payRecord.memberRoomBonusAmount
            };

            // 添加到支付记录列表
            this.state.payRecords.push(memberCardPayRecord);
            console.log('[orderPayDialog] 已添加会员卡支付记录:', memberCardPayRecord);
          } else {
            console.log('[orderPayDialog] 会员卡支付未完成或取消');
          }
        })
        .catch(error => {
          console.error('[orderPayDialog] 会员卡支付对话框错误:', error);
          ElMessage.error('会员卡支付失败: ' + (error instanceof Error ? error.message : String(error)));
        });
    },

    handleCreditAccountConfirm: (data: any) => {
      // 处理信用账户确认
      this.state.creditAccountVisible = false;
    },

    handleAdjustConfirm: (adjustData: AdjustData) => {
      // 处理调整确认
      console.log('[orderPayDialog] handleAdjustConfirm 开始处理调整确认');
      if (!this.state.orderData) return;

      console.log('[orderPayDialog] handleAdjustConfirm 接收到的参数:', adjustData);
      console.log('[orderPayDialog] handleAdjustConfirm 调整前金额:', {
        roomAmount: this.state.orderData.roomAmount,
        productAmount: this.state.orderData.productAmount,
        actualAmount: this.state.orderData.actualAmount
      });

      // 获取调整后的房费和商品费
      const adjustedRoomAmount = adjustData.adjustedRoomAmount;
      const adjustedProductAmount = adjustData.adjustedProductAmount;

      // 获取低消金额
      const minConsumptionAmount = this.state.orderData.minConsumptionAmount || 0;

      // 计算 actualAmount = 房费 + max(低消, 商品费用)
      const effectiveProductAmount = Math.max(minConsumptionAmount, adjustedProductAmount);
      this.state.orderData.actualAmount = adjustedRoomAmount + effectiveProductAmount;

      // 不再直接更新房费和商品费，而是保留原始值并更新折扣和减免
      // this.state.orderData.roomAmount = adjustedRoomAmount;
      // this.state.orderData.productAmount = adjustedProductAmount;

      // 更新订单数据的其他字段
      this.state.orderData.discountAmount = adjustData.discountAmount;
      this.state.orderData.roomDiscountRate = adjustData.roomDiscountPercent;
      this.state.orderData.productDiscountRate = adjustData.productDiscountPercent;
      this.state.orderData.roomReduceAmount = adjustData.roomDiscountAmount;
      this.state.orderData.productReduceAmount = adjustData.productDiscountAmount;
      // 保存调整原因
      this.state.orderData.adjustReason = adjustData.reason;

      // 清空所有支付记录，确保金额调整后重新选择支付方式
      if (this.state.payRecords && this.state.payRecords.length > 0) {
        console.log('[orderPayDialog] 商家调整后清空支付记录，原有记录数:', this.state.payRecords.length);
        this.state.payRecords.splice(0, this.state.payRecords.length);
      }

      // 清除已选择的支付方式，确保金额能正确更新
      this.state.selectedPayMethod = '';
      // 清除现金支付输入框的值
      this.state.actualAmount = '';

      // 动态计算调整后的房费和商品费（用于日志显示）
      const calculatedAmounts = this.calculateAdjustedAmounts();

      console.log('[orderPayDialog] handleAdjustConfirm 更新后的订单数据:', {
        originalRoomAmount: this.state.orderData.roomAmount,
        originalProductAmount: this.state.orderData.productAmount,
        calculatedRoomAmount: calculatedAmounts.roomAmount,
        calculatedProductAmount: calculatedAmounts.productAmount,
        actualAmount: this.state.orderData.actualAmount,
        roomDiscountRate: this.state.orderData.roomDiscountRate,
        productDiscountRate: this.state.orderData.productDiscountRate,
        roomReduceAmount: this.state.orderData.roomReduceAmount,
        productReduceAmount: this.state.orderData.productReduceAmount
      });

      // 提示用户重新选择支付方式
      ElMessage.info('金额已调整，请重新选择支付方式');
    },

    // 弹窗操作
    handleConfirm: async () => {
      await this.actions.handlePay();
    },

    handleCancel: () => {
      // 这个方法将在组件中被重写
      console.log('支付被取消');
    },

    handleSuccess: (result: any) => {
      // 这个方法将在组件中被重写
      console.log('支付成功:', result);
    },

    // 打开会员卡支付对话框
    openMemberCardPayDialog: async () => {
      // 检查是否已存在会员卡支付记录
      if (this.state.payRecords.some(record => record.payType === PaymentMethodType.Member)) {
        ElMessage.warning('已存在会员卡支付记录，不能添加多个会员卡支付');
        return;
      }

      if (!this.state.orderData) {
        ElMessage.warning('订单数据不存在');
        return;
      }

      try {
        // 计算剩余应付金额
        const totalOrderAmount = this.state.orderData.actualAmount || 0;
        const totalPaidAmount = this.computed.totalPaidAmount.value || 0;
        const remainingAmount = Math.max(0, totalOrderAmount - totalPaidAmount);

        // 如果没有剩余金额，直接返回
        if (remainingAmount <= 0) {
          ElMessage.info('已无剩余应付金额');
          return;
        }

        // 动态计算调整后的房费和商品费
        const adjustedAmounts = this.calculateAdjustedAmounts();
        const adjustedRoomAmount = adjustedAmounts.roomAmount;
        const adjustedProductAmount = adjustedAmounts.productAmount;

        // 按比例计算已支付金额中的商品和房间占比
        let remainingProductAmount = adjustedProductAmount;
        let remainingRoomAmount = adjustedRoomAmount;

        // 优先扣减商品费用，商品费用扣完后再扣减房间费用
        if (totalPaidAmount > 0) {
          if (totalPaidAmount >= adjustedProductAmount) {
            // 已支付金额超过商品费用，商品费用已全部支付
            remainingProductAmount = 0;
            // 剩余的支付金额用于抵扣房间费用
            remainingRoomAmount = Math.max(0, adjustedRoomAmount - (totalPaidAmount - adjustedProductAmount));
          } else {
            // 已支付金额未超过商品费用，商品费用部分支付
            remainingProductAmount = adjustedProductAmount - totalPaidAmount;
            // 房间费用还未开始支付
            remainingRoomAmount = adjustedRoomAmount;
          }
        }

        console.log('[orderPayDialog] 准备打开会员卡选择对话框', {
          adjustedRoomAmount,
          adjustedProductAmount,
          remainingRoomAmount,
          remainingProductAmount,
          totalPaidAmount
        });

        // 先打开会员卡选择对话框
        // 设置memberCardVisible为true
        this.state.memberCardVisible = true;

        // 会员卡选择逻辑已通过handleMemberConfirm方法处理
        // 会员卡选择后的支付逻辑将在handleMemberConfirm方法中继续
      } catch (error) {
        console.error('[orderPayDialog] 会员卡支付失败:', error);
        ElMessage.error('会员卡支付失败: ' + (error instanceof Error ? error.message : String(error)));
      }
    }
  };

  // 查询并确认扫码支付结果 - 提取为类方法复用
  private async queryAndConfirmScanPayment(sessionId: string | undefined, payBillVO: PayBillVO): Promise<boolean> {
    // 如果没有billId，直接返回成功，因为不是扫码支付
    if (!payBillVO) {
      console.log('[queryAndConfirmScanPayment] 没有payBillVO, 直接返回成功');
    }

    ElMessage.info('正在查询支付结果...');

    // 从orderData中获取sessionId和roomId
    const roomId = payBillVO.roomId;
    const payBillId = payBillVO.billId;

    if (!sessionId || !roomId) {
      throw new Error('缺少查询支付状态所需的参数');
    }

    // 设置轮询参数
    const maxRetryTime = 180000; // 180秒超时
    const retryInterval = 2000; // 每2秒查询一次
    const startTime = Date.now();
    let isPaySuccess = false;

    while (!isPaySuccess && Date.now() - startTime < maxRetryTime) {
      try {
        // 等待2秒后查询
        await new Promise(resolve => setTimeout(resolve, retryInterval));

        // 查询支付状态
        const queryParams = {
          billId: payBillId,
          roomId,
          sessionId
        };

        console.log(`查询支付状态:`, queryParams);
        const queryResult = await this.interactor.queryOrderPay(queryParams);

        // 判断是否支付成功
        if (queryResult && queryResult.payBillVO) {
          isPaySuccess = true;
          console.log('[queryAndConfirmScanPayment] 支付查询成功, sessionId:', sessionId, 'payBillVO:', queryResult.payBillVO);
        } else {
          console.log('支付状态未完成，继续等待...');
        }
      } catch (queryError) {
        console.error('查询支付状态出错:', queryError);
        // 继续轮询，直到超时
      }
    }

    // 如果超过180秒仍未成功
    if (!isPaySuccess) {
      ElMessage.warning('支付超时，请稍后查询订单状态');
      return false;
    }

    return isPaySuccess;
  }

  // 获取支付方式名称的辅助函数
  public getPaymentMethodName(payType: string): string {
    // 1. 直接匹配预设列表
    let method = PAYMENT_METHODS.find(m => m.value === payType);
    if (method) {
      return method.label || payType;
    }

    // 2. 兼容扫码枪内部支付类型 (bShowQR)
    if (payType === PaymentMethodType.BShowQR) {
      method = PAYMENT_METHODS.find(m => m.value === PaymentMethodType.Scanner);
      return method ? method.label : '扫码支付';
    }

    // 3. 尝试通过 PAY_TYPE_MAP 反向映射，解决接口与前端枚举不一致的情况
    const originalKey = Object.keys(PAY_TYPE_MAP).find(key => PAY_TYPE_MAP[key] === payType);
    if (originalKey) {
      method = PAYMENT_METHODS.find(m => m.value === originalKey);
      if (method) {
        return method.label || originalKey;
      }
    }

    // 4. 默认兜底
    return payType || '未知支付方式';
  }

  // 格式化金额（元）
  public convertToYuan(amountInFen: number): string {
    const amount = amountInFen / 100;
    return amount.toFixed(2);
  }
}

/**
 * 导出组合式函数
 * @returns OrderPayDialog视图模型
 */
export function useOrderPayDialog(): IOrderPayDialogViewModel {
  let presenter = null;
  try {
    presenter = new OrderPayDialogPresenter();
  } catch (error) {
    console.error('创建OrderPayDialogPresenter失败:', error);
    // 创建一个基本可用的Presenter对象
    presenter = {
      state: reactive({ ...ORDER_PAY_DIALOG_STATE_DEFAULTS }),
      computed: {
        calculateChangeAmount: computed(() => '0.00'),
        paymentMethods: computed(() => PAYMENT_METHODS),
        payTypeText: computed(() => ''),
        roomConsumptionTime: computed(() => ''),
        roomConsumptionType: computed(() => ''),
        totalPaidAmount: computed(() => 0),
        canPay: computed(() => false),
        remainingAmount: computed(() => 0),
        canAddCurrentPayment: computed(() => false)
      },
      actions: {
        onDialogOpen: () => {},
        onDialogClose: () => {},
        handleClose: () => {},
        init: async () => {},
        selectPayMethod: () => {},
        handleActualAmountInput: () => {},
        handleActualAmountBlur: () => {},
        handleScanInput: () => {},
        handleScanComplete: async () => {},
        handlePay: async () => {},
        openMemberCardDialog: () => {},
        openCreditAccountDialog: () => {},
        openAdjustDialog: () => {},
        handleMemberConfirm: () => {},
        handleCreditAccountConfirm: () => {},
        handleAdjustConfirm: () => {},
        handleConfirm: async () => {},
        handleCancel: () => {},
        handleSuccess: () => {},
        openMemberCardPayDialog: async () => {},
        addCurrentPayment: () => {},
        removePayRecord: () => {},
        handlePayRecordAmountChange: () => {},
        handlePayRecordAmountBlur: () => {},
        handleConfirmPay: async () => {}
      }
    };
  }
  return presenter;
}
