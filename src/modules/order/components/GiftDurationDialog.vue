<template>
  <AppDialog
    :uiType="DialogUIType.DEFAULT"
    v-model="dialogVisible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    @close="handleCancel"
    class="h-[480px] !w-[620px]">
    <!-- 赠送时长输入 -->
    <div class="items-center gap-2 mx-[24px]">
      <p class="text-gray-600 text-[20px] pb-[12px]">赠送时长:</p>
      <el-input class="text-[20px] h-[64px]" v-model.number="form.duration" type="number" :min="0" :max="999" placeholder="请输入赠送时长">
        <template #append>分钟</template>
      </el-input>
    </div>

    <!-- 赠送人选择 -->
    <div class="items-center mx-[24px] mt-[24px] pb-[24px]">
      <p class="text-gray-600 text-[20px]">赠送人:</p>
      <div class="flex items-center mt-[12px]">
        <el-select
          v-model="form.operator"
          placeholder="请选择赠送人"
          class="w-[200px] customer-select"
          :loading="salespersonLoading"
          @change="handleSalespersonChange"
          filterable
          remote
          :remote-method="searchSalesperson"
          clearable>
          <el-option v-for="item in salespersonOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <!-- <span class="text-gray-400 text-sm">不限时长</span> -->
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center">
        <button class="btn-black" :loading="loading" @click="handleConfirm">确认</button>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { OrderApi } from '@/modules/order/api/order';
import { ElMessage } from 'element-plus';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { DialogUIType } from '@/types/dialog';
import { useUserStore } from '@/stores/userStore';
import { useEmployeeList } from '@/composables/useEmployeeList';

interface Props {
  visible: boolean;
  roomInfo: {
    id: string;
    sessionId: string;
    name: string;
  };
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
  (e: 'cancel'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();
const userStore = useUserStore();

// 使用员工列表hooks
const {
  employees: salespersonOptions,
  loading: salespersonLoading,
  loadEmployees: loadSalespersonData,
  searchEmployees,
  selectEmployee,
  selectedEmployee
} = useEmployeeList({
  autoLoad: true,
  reviewStatus: 1 // 只显示已审核的员工
});

// 监听员工列表变化，自动设置默认值
watch(
  salespersonOptions,
  (newEmployees) => {
    if (newEmployees.length > 0 && props.visible && !form.value.operator) {
      initializeDefaultOperator();
    }
  },
  { immediate: true }
);

const loading = ref(false);
const dialogVisible = computed({
  get: () => props.visible,
  set: val => emit('update:visible', val)
});

const form = ref({
  duration: 30,
  operator: '',
  operatorName: '',
  hasLimit: true
});

// 获取当前用户信息作为默认值
const currentUser = computed(() => {
  const employee = userStore.userInfo.employee;
  return {
    id: employee?.id || userStore.userInfo.unionId || '',
    name: employee?.name || userStore.userInfo.name || ''
  };
});

// 计算对话框标题
const dialogTitle = computed(() => {
  return (props.roomInfo?.name || '') + ' 赠送时长';
});

// 销售员选择变更处理
const handleSalespersonChange = (value: string) => {
  const employee = salespersonOptions.value.find((e: any) => e.id === value);
  if (employee) {
    selectEmployee(employee);
    form.value.operator = employee.id;
    form.value.operatorName = employee.name;
  } else {
    form.value.operator = '';
    form.value.operatorName = '';
  }
};

// 搜索销售员
const searchSalesperson = async (query: string) => {
  if (query) {
    await searchEmployees(query);
  } else {
    // 如果搜索为空，重新加载所有员工
    await loadSalespersonData(true);
  }
};

// 初始化默认赠送人为当前用户（如果在员工列表中）
const initializeDefaultOperator = () => {
  const current = currentUser.value;
  if (current.id) {
    // 检查当前用户是否在员工列表中
    const foundEmployee = salespersonOptions.value.find((emp: any) => emp.id === current.id);
    if (foundEmployee) {
      form.value.operator = foundEmployee.id;
      form.value.operatorName = foundEmployee.name;
      selectEmployee(foundEmployee);
    }
  }
};

const handleConfirm = async () => {
  // 基本验证
  if (!form.value.duration || form.value.duration < 1 || form.value.duration > 999) {
    ElMessage.warning('请输入1-999分钟的赠送时长');
    return;
  }
  if (!form.value.operator) {
    ElMessage.warning('请选择赠送人');
    return;
  }

  loading.value = true;
  try {
    const response = await OrderApi.timeGift({
      roomId: props.roomInfo.id,
      sessionId: props.roomInfo.sessionId,
      operator: form.value.operator,
      giftMinute: form.value.duration
    });

    if (response.code === 0) {
      ElMessage({
        message: '赠送时长成功',
        type: 'success',
        duration: 1500
      });
      emit('update:visible', false);
      emit('success');
    } else {
      ElMessage({
        message: `赠送时长失败: ${response.message}`,
        type: 'error',
        duration: 1500
      });
    }
  } catch (error) {
    console.error('赠送时长失败:', error);
    ElMessage({
      message: '赠送时长失败，请稍后重试',
      type: 'error',
      duration: 1500
    });
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  // 重置表单为默认值
  form.value = {
    duration: 30,
    operator: currentUser.value.id,
    operatorName: currentUser.value.name,
    hasLimit: true
  };
  emit('cancel');
};

// 监听对话框显示状态，初始化默认值
watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      // 确保员工数据已加载，然后初始化默认值
      await loadSalespersonData();
      initializeDefaultOperator();
    }
  }
);
</script>
