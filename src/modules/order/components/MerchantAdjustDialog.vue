<template>
  <app-dialog v-model="dialogVisible" title="商家调整" :close-on-click-modal="false" :show-footer="false" @close="handleCancel">
    <div class="w-full">
      <!-- 左侧表单 -->
      <el-form ref="formRef" :model="form" label-width="80px">
        <!-- 免单开关 -->
        <el-form-item label="免单:" class="mb-6 w-full">
          <el-switch v-model="form.freeOrder" @change="handleFreeOrderToggle" />
        </el-form-item>

        <!-- 折扣 -->
        <el-form-item label="折扣:" class="mb-6 items-center">
          <el-input v-model="form.discount" placeholder="请输入1到99的整数" class="h-[48px]" @input="handleDiscountInput">
            <template #append>%</template>
          </el-input>
        </el-form-item>

        <!-- 第一行调整 百分比 -->
        <div class="flex mb-6">
          <el-form-item label="包厢:" class="mr-4 flex-1 items-center">
            <el-input v-model="form.roomDiscountPercent" placeholder="请输入" class="h-[48px]" @input="handleRoomPercentInput">
              <template #append>%</template>
            </el-input>
          </el-form-item>
          <el-form-item label="商品:" class="flex-1 items-center">
            <el-input v-model="form.productDiscountPercent" placeholder="请输入" class="h-[48px]" @input="handleProductPercentInput">
              <template #append>%</template>
            </el-input>
          </el-form-item>
        </div>

        <!-- 减免金额 -->
        <el-form-item label="减免:" class="mb-6 items-center">
          <el-input v-model="form.reduction" placeholder="请输入" class="h-[48px]" @input="handleReductionInput" />
        </el-form-item>

        <!-- 第二行调整 金额 -->
        <div class="flex">
          <el-form-item label="包厢:" class="mr-4 flex-1 items-center">
            <el-input v-model="form.roomDiscountAmount" placeholder="请输入" class="h-[48px]" @input="handleRoomAmountInput" />
          </el-form-item>
          <el-form-item label="商品:" class="flex-1 items-center">
            <el-input v-model="form.productDiscountAmount" placeholder="请输入" class="h-[48px]" @input="handleProductAmountInput" />
          </el-form-item>
        </div>
        <el-form-item label="调整原因:" label-width="80px" class="w-full">
          <el-input v-model="form.reason" type="textarea" :rows="3" :maxlength="20" placeholder="不得超过20个字" show-word-limit resize="none" class="w-full" />
        </el-form-item>

        <!-- 快捷标签 -->
        <div class="flex flex-wrap gap-2 ml-[80px]">
          <el-button v-for="tag in quickTags" :key="tag" size="small" class="!rounded-full" @click="form.reason = tag">
            {{ tag }}
          </el-button>
        </div>
      </el-form>

      <!-- 错误提示 -->
      <div v-if="showZeroAmountError" class="mt-2 text-red-500 ml-[80px]">非免单情况下，不能将金额调整为0元</div>
    </div>
    <template #footer>
      <!-- 底部操作 -->
      <div class="mt-8 flex flex-col justify-between items-center w-full">
        <div class="flex gap-4">
          <el-button class="w-[172px] h-[64px] text-[20px]" color="#000" @click="handleConfirm" :disabled="isConfirmDisabled"> 确定 </el-button>
        </div>
        <div class="text-base mt-[12px]">
          商家减免: <span class="text-primary font-medium">¥ {{ convertToYuan(totalDiscountAmount) }}</span>
        </div>
      </div>
    </template>
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { convertToYuan, convertToFen, floorDiscount } from '@/utils/priceUtils';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';

// 定义调整数据接口
interface AdjustData {
  roomDiscountPercent: number;
  productDiscountPercent: number;
  freeOrder: boolean;
  reason: string;
  adjustedRoomAmount: number;
  adjustedProductAmount: number;
  discountAmount: number;
  roomDiscountAmount: number;
  productDiscountAmount: number;
}

interface Props {
  visible: boolean;
  modelValue: boolean;
  roomAmount: number; // 分
  productAmount: number; // 分
  // 新增初始化数据字段
  initialData?: {
    roomDiscountPercent?: number; // 房费折扣率（数字类型）
    productDiscountPercent?: number; // 商品折扣率（数字类型）
    freeOrder?: boolean; // 是否免单
    reason?: string; // 调整原因
    roomDiscountAmount?: number; // 房费减免金额（分）
    productDiscountAmount?: number; // 商品减免金额（分）
  };
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  modelValue: false,
  roomAmount: 0,
  productAmount: 0,
  initialData: undefined
});

const emit = defineEmits(['update:visible', 'update:modelValue', 'confirm', 'close']);

// 对话框可见状态，兼容modelValue和visible两种方式
const dialogVisible = computed({
  get: () => {
    const result = props.modelValue || props.visible;
    return result;
  },
  set: val => {
    emit('update:modelValue', val);
    emit('update:visible', val);
  }
});

// 表单数据 - 用户输入都是元为单位的字符串
const form = ref({
  freeOrder: false, // 是否免单
  discount: '', // 折扣 - 空字符串默认值
  roomDiscountPercent: '', // 房费折扣率 - 改为字符串
  productDiscountPercent: '', // 商品折扣率 - 改为字符串
  reduction: '', // 减免金额(元)
  roomDiscountAmount: '', // 房费减免金额(元)
  productDiscountAmount: '', // 商品减免金额(元)
  reason: ''
});

// 快捷标签
const quickTags = ['老客户', '等太久', '有活动'];

// 初始化表单数据的函数
const initializeFormData = () => {
  if (props.initialData) {
    const data = props.initialData;

    // 处理免单状态
    form.value.freeOrder = data.freeOrder || false;

    // 处理折扣率 - 将数字转换为字符串
    if (data.roomDiscountPercent !== undefined) {
      // 如果是100%且非免单，显示为空（表示无折扣），否则显示实际值
      if (data.roomDiscountPercent === 100 && !data.freeOrder) {
        form.value.roomDiscountPercent = '';
      } else {
        form.value.roomDiscountPercent = data.roomDiscountPercent.toString();
      }
    }

    if (data.productDiscountPercent !== undefined) {
      // 如果是100%且非免单，显示为空（表示无折扣），否则显示实际值
      if (data.productDiscountPercent === 100 && !data.freeOrder) {
        form.value.productDiscountPercent = '';
      } else {
        form.value.productDiscountPercent = data.productDiscountPercent.toString();
      }
    }

    // 处理减免金额 - 将分转换为元
    if (data.roomDiscountAmount !== undefined && data.roomDiscountAmount > 0) {
      form.value.roomDiscountAmount = convertToYuan(data.roomDiscountAmount).toString();
    }

    if (data.productDiscountAmount !== undefined && data.productDiscountAmount > 0) {
      form.value.productDiscountAmount = convertToYuan(data.productDiscountAmount).toString();
    }

    // 处理调整原因
    if (data.reason) {
      form.value.reason = data.reason;
    }

    // 更新主折扣字段
    updateMainDiscountField();
  } else {
    // 没有初始化数据时，重置为默认值
    form.value = {
      freeOrder: false,
      discount: '',
      roomDiscountPercent: '',
      productDiscountPercent: '',
      reduction: '',
      roomDiscountAmount: '',
      productDiscountAmount: '',
      reason: ''
    };
  }
};

// 处理折扣输入
const handleDiscountInput = (value: string) => {
  // 当输入为空时，保持所有折扣字段为空
  if (value === '') {
    form.value.discount = '';
    form.value.roomDiscountPercent = '';
    form.value.productDiscountPercent = '';
    return;
  }

  // 限制最大值为99，非免单不允许100%折扣
  let numValue = Number(value);
  numValue = Math.min(99, Math.max(1, numValue || 1));
  form.value.discount = numValue.toString();

  // 统一折扣时，同时设置房费和商品折扣率
  form.value.roomDiscountPercent = numValue.toString();
  form.value.productDiscountPercent = numValue.toString();
  // 清除分别调整的金额
  form.value.roomDiscountAmount = '';
  form.value.productDiscountAmount = '';
  form.value.reduction = '';
};

// 处理减免输入 (用户输入的是元)
const handleReductionInput = () => {
  // 获取输入的减免金额（元）
  let reduction = Number(form.value.reduction) || 0;
  const totalAmount = convertToYuan(props.roomAmount + props.productAmount); // 转换成元

  // 限制最大减免金额，非免单情况下不能全额减免
  const maxReduction = form.value.freeOrder ? totalAmount : Math.floor(totalAmount * 0.999 * 100) / 100;

  if (reduction > maxReduction) {
    reduction = maxReduction;
    form.value.reduction = maxReduction.toFixed(2);
  }

  if (totalAmount > 0 && reduction > 0) {
    // 根据房费和商品费的比例分配减免金额
    const roomRatio = convertToYuan(props.roomAmount) / totalAmount;
    const roomReduction = Math.floor(reduction * roomRatio * 100) / 100;
    form.value.roomDiscountAmount = roomReduction.toFixed(2);
    form.value.productDiscountAmount = (reduction - roomReduction).toFixed(2);
  } else {
    form.value.roomDiscountAmount = '';
    form.value.productDiscountAmount = '';
  }
};

// 处理包厢百分比输入
const handleRoomPercentInput = () => {
  // 清除统一减免
  form.value.reduction = '';

  // 限制非免单情况下，折扣率不能为100%(导致0元)
  const numValue = Number(form.value.roomDiscountPercent);
  if (!form.value.freeOrder && numValue >= 100) {
    form.value.roomDiscountPercent = '99';
  } else if (numValue > 100) {
    form.value.roomDiscountPercent = '100';
  }

  // 检查房间和商品折扣率是否相同
  updateMainDiscountField();
};

// 处理商品百分比输入
const handleProductPercentInput = () => {
  // 清除统一减免
  form.value.reduction = '';

  // 限制非免单情况下，折扣率不能为100%(导致0元)
  const numValue = Number(form.value.productDiscountPercent);
  if (!form.value.freeOrder && numValue >= 100) {
    form.value.productDiscountPercent = '99';
  } else if (numValue > 100) {
    form.value.productDiscountPercent = '100';
  }

  // 检查房间和商品折扣率是否相同
  updateMainDiscountField();
};

// 更新主折扣输入框
const updateMainDiscountField = () => {
  // 检查两个折扣是否都设置了
  const roomPercentValue = form.value.roomDiscountPercent;
  const productPercentValue = form.value.productDiscountPercent;

  // 如果两个值相同且都不为空，显示共同值
  if (roomPercentValue === productPercentValue && roomPercentValue !== '') {
    form.value.discount = roomPercentValue;
  } else {
    // 否则主折扣框为空
    form.value.discount = '';
  }
};

// 添加监听器，当房间或商品折扣率变化时，更新主折扣框
watch([() => form.value.roomDiscountPercent, () => form.value.productDiscountPercent], () => {
  updateMainDiscountField();
});

// 处理包厢金额输入
const handleRoomAmountInput = () => {
  // 分别调整时，清除统一减免
  form.value.reduction = '';

  // 限制最大减免金额不超过包厢金额
  const inputAmount = Number(form.value.roomDiscountAmount) || 0;
  let maxAmount = convertToYuan(props.roomAmount);

  // 非免单模式，至少保留1%的金额
  if (!form.value.freeOrder) {
    maxAmount = Math.floor(maxAmount * 0.99 * 100) / 100;
  }

  if (inputAmount > maxAmount) {
    form.value.roomDiscountAmount = maxAmount.toString();
  }
};

// 处理商品金额输入
const handleProductAmountInput = () => {
  // 分别调整时，清除统一减免
  form.value.reduction = '';

  // 限制最大减免金额不超过商品金额
  const inputAmount = Number(form.value.productDiscountAmount) || 0;
  let maxAmount = convertToYuan(props.productAmount);

  // 非免单模式，至少保留1%的金额
  if (!form.value.freeOrder) {
    maxAmount = Math.floor(maxAmount * 0.99 * 100) / 100;
  }

  if (inputAmount > maxAmount) {
    form.value.productDiscountAmount = maxAmount.toString();
  }
};

// 计算调整后的房费 (返回分)
const adjustedRoomAmount = computed(() => {
  // 免单情况返回0
  if (form.value.freeOrder) return 0;

  // 使用房间折扣率（整数），并抹分
  const roomDiscountPercent = Number(form.value.roomDiscountPercent) || 100;
  let roomAmount = floorDiscount(props.roomAmount, roomDiscountPercent); // 分

  if (form.value.roomDiscountAmount) {
    // 已有按照两位小数抹分后的房费减免金额，直接减
    roomAmount -= convertToFen(Number(form.value.roomDiscountAmount));
  } else if (form.value.reduction) {
    // 兼容旧逻辑：尚未分配到房费/商品，按比例分配
    const reduction = convertToFen(Number(form.value.reduction));
    const totalAmount = props.roomAmount + props.productAmount;
    const reductionRatio = reduction / totalAmount;
    roomAmount -= Math.floor(props.roomAmount * reductionRatio);
  }

  // 可能在比例减免后产生小数，再次抹分
  return Math.floor(Math.max(0, roomAmount));
});

// 计算调整后的商品费 (返回分)
const adjustedProductAmount = computed(() => {
  // 免单情况返回0
  if (form.value.freeOrder) return 0;

  let productAmount = props.productAmount; // 分

  // 使用商品折扣率，默认为100%
  const productDiscountPercent = Number(form.value.productDiscountPercent) || 100;
  productAmount = floorDiscount(productAmount, productDiscountPercent);

  if (form.value.productDiscountAmount) {
    productAmount -= convertToFen(Number(form.value.productDiscountAmount));
  } else if (form.value.reduction) {
    const reduction = convertToFen(Number(form.value.reduction));
    const totalAmount = props.roomAmount + props.productAmount;
    const reductionRatio = reduction / totalAmount;
    productAmount -= Math.floor(props.productAmount * reductionRatio);
  }

  // 可能在比例减免后产生小数，再次抹分
  return Math.floor(Math.max(0, productAmount));
});

// 计算总减免金额 (返回分)
const totalDiscountAmount = computed(() => {
  const originalTotal = props.roomAmount + props.productAmount;
  const adjustedTotal = adjustedRoomAmount.value + adjustedProductAmount.value;
  return originalTotal - adjustedTotal;
});

// 检查非免单情况下是否为0元单
const isZeroAmount = computed(() => {
  if (form.value.freeOrder) return false; // 免单情况下允许0元

  const totalAdjustedAmount = adjustedRoomAmount.value + adjustedProductAmount.value;
  return totalAdjustedAmount === 0;
});

// 是否禁用确认按钮
const isConfirmDisabled = computed(() => {
  return isZeroAmount.value;
});

// 是否显示0元单错误提示
const showZeroAmountError = computed(() => {
  return isZeroAmount.value;
});

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
  emit('close');
};

// 确认时转换单位
const handleConfirm = () => {
  // 非免单下不允许0元单
  if (isZeroAmount.value) {
    return;
  }

  // 计算实际的减免金额（分）
  let roomDiscountAmount = 0;
  let productDiscountAmount = 0;

  if (form.value.freeOrder) {
    // 免单情况下，减免金额等于原始金额
    roomDiscountAmount = props.roomAmount;
    productDiscountAmount = props.productAmount;
  } else {
    // 非免单情况
    roomDiscountAmount = form.value.roomDiscountAmount ? convertToFen(Number(form.value.roomDiscountAmount)) : 0;
    productDiscountAmount = form.value.productDiscountAmount ? convertToFen(Number(form.value.productDiscountAmount)) : 0;
  }

  const adjustData: AdjustData = {
    // 确保始终传递 number 类型的折扣率，如果为空或0则使用100%
    roomDiscountPercent: Number(form.value.roomDiscountPercent) || 100,
    productDiscountPercent: Number(form.value.productDiscountPercent) || 100,
    freeOrder: form.value.freeOrder,
    reason: form.value.reason, // 确保调整原因被包含在返回数据中
    // 调整后的金额
    adjustedRoomAmount: adjustedRoomAmount.value, // 调整后的房费(分)
    adjustedProductAmount: adjustedProductAmount.value, // 调整后的商品费(分)
    discountAmount: totalDiscountAmount.value, // 总优惠金额(分)
    // 减免金额 - 使用计算后的实际减免金额
    roomDiscountAmount: roomDiscountAmount, // 房费减免金额(分)
    productDiscountAmount: productDiscountAmount // 商品减免金额(分)
  };

  emit('confirm', adjustData);
  dialogVisible.value = false;
};

// 处理免单开关
const handleFreeOrderToggle = () => {
  if (form.value.freeOrder) {
    // 如果选择免单，设置100%折扣，并设置具体的减免金额
    form.value.discount = '100';
    form.value.roomDiscountPercent = '100';
    form.value.productDiscountPercent = '100';
    // 设置减免金额为原始金额（元）
    form.value.roomDiscountAmount = convertToYuan(props.roomAmount).toFixed(2);
    form.value.productDiscountAmount = convertToYuan(props.productAmount).toFixed(2);
    // 清空统一减免（因为已经分别设置）
    form.value.reduction = '';
  } else {
    // 如果取消免单，确保不会设置100%的折扣
    form.value.discount = '99';
    form.value.roomDiscountPercent = '99';
    form.value.productDiscountPercent = '99';
    // 清空减免金额
    form.value.roomDiscountAmount = '';
    form.value.productDiscountAmount = '';
  }
};

// 监听对话框可见状态的变化
watch(
  dialogVisible,
  (newVal, oldVal) => {
    if (newVal) {
      // 当对话框打开时，初始化表单数据
      initializeFormData();
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.text-primary {
  color: #606266;
}

:deep(.el-form-item__label) {
  color: #606266;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
}

:deep(.el-button) {
  border-radius: 4px;
}

/* 添加新的样式，确保表单元素填充满宽度 */
:deep(.el-form-item) {
  width: 100%;
  margin-right: 0;
}

:deep(.el-form-item__content) {
  width: calc(100% - 80px);
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-input-group__append) {
  padding: 0 10px;
}
</style>
