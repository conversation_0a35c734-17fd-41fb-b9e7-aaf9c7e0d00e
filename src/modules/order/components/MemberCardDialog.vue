<template>
  <AppDialog
    class="!w-[1080px]"
    v-model="dialogVisible"
    title="会员卡"
    :closeOnClickModal="false"
    @close="closeDialog"
    :show-header="false"
    :show-footer="false">
    <div class="flex h-full">
      <!-- 左侧输入区域 -->
      <div class="w-[600px] px-[24px] flex flex-col">
        <div class="mt-[24px]">
          <span class="text-[#E9223A] text-[28px]">会员卡选择</span>
        </div>

        <!-- 数字键盘 -->
        <div class="w-full flex-1 flex flex-col items-center justify-center h-full">
          <el-input v-model="queryValue" placeholder="输入手机号或卡号" class="custom-search-input w-[418px]" readonly />

          <div class="w-[418px] grid grid-cols-3 gap-[12px] mt-[24px]">
            <button v-for="btn in numberButtons" :key="btn" class="bg-[#F3F3F3] w-[132px] h-[68px] rounded-[8px] text-xl" @click="handleNumberInput(btn)">
              {{ btn }}
            </button>
          </div>

          <!-- 功能按钮 -->
          <div class="w-[418px] flex mt-[12px] gap-[12px]">
            <button class="bg-[#F3F3F3] w-[276px] h-[68px] text-[32px] rounded-[8px]" @click="handleDelete">←</button>
            <button class="bg-[#F3F3F3] w-[132px] h-[68px] rounded-[8px]" @click="handleClear">清空</button>
          </div>

          <!-- 确认按钮 -->
          <div class="mt-6">
            <el-button class="btn-default" @click="handleSearch">查询会员卡</el-button>
          </div>
        </div>
      </div>

      <!-- 右侧会员信息展示区 -->
      <div class="flex-1 overflow-y-auto bg-[#F3F3F3] flex flex-col items-center py-[48px] scrollbar-hide">
        <template v-if="memberList.length > 0">
          <div
            v-for="item in memberList"
            :key="item.id"
            class="member-card cursor-pointer"
            :class="{
              'member-card-selected': selectedMemberId === item.id,
              'member-card-frozen': isCardFrozen(item)
            }"
            @click="handleCardClick(item.id)">
            <div class="member-card-content">
              <div class="flex justify-between items-center">
                <span class="text-[#FFFFFF] text-[24px] font-bold">{{ item.cardNumber }}</span>
                <span class="text-[#365A63] text-[16px] font-bold">{{ item.name || '会员' }}</span>
              </div>

              <div class="mt-[8px] text-[#FFFFFF] text-[14px]">{{ item.cardLevelName || item.cardLevel || '普通会员' }}</div>

              <div class="card-balance flex items-baseline">
                <span class="mr-[8px] text-[#365A69] text-[14px] font-bold">余额</span>
                <PriceDisplay :amount-in-fen="getTotalBalance(item)" class="price-display-normal !text-[#365A69] member-balance-price" />
              </div>

              <!-- 冻结状态标识 -->
              <div v-if="isCardFrozen(item)" class="frozen-badge">
                <span class="text-[#fff] text-[12px] font-bold">已冻结</span>
              </div>

              <!-- 选中图标 -->
              <div v-if="selectedMemberId === item.id && !isCardFrozen(item)" class="card-checkbox">
                <el-icon color="#fff" class="text-[24px]"><Check /></el-icon>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="flex flex-col items-center justify-center h-full py-12">
            <div class="member-card">
              <div class="ml-[16px] mt-[20px] bg-[#C9D4D8] h-[20px] w-[120px] rounded-[3px]"></div>
              <div class="ml-[16px] mt-[20px] bg-[#C9D4D8] h-[20px] w-[60px] rounded-[3px]"></div>
            </div>
            <div class="text-gray-400 text-[14px] mt-[12px]">请输入4位手机尾号或完整号码进行查询</div>
          </div>
        </template>
      </div>
    </div>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { memberApi } from '@/modules/member/api/member';
import type { MemberVO } from '@/api/member';
import { Delete, Check } from '@element-plus/icons-vue';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 查询相关
const queryValue = ref('');
const hasSearched = ref(false);
const memberList = ref<MemberVO[]>([]);
const selectedMemberId = ref('');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 查询会员
const handleSearch = async () => {
  if (!queryValue.value) {
    ElMessage.warning('请输入手机号或卡号');
    return;
  }

  try {
    // 使用memberApi.getMemberList，参数改为phoneLike
    const res = await memberApi.SearchMemberList({
      phoneLike: queryValue.value
    });

    console.log('[memberCarddialog] search ', res, res.data);
    const response = res?.data;
    if (res.code === 0 && response.length > 0) {
      memberList.value = response;
      hasSearched.value = true;
      selectedMemberId.value = '';
    } else {
      memberList.value = [];
      ElMessage.info('未找到匹配的会员卡');
    }
  } catch (error) {
    console.error('查询会员失败:', error);
    ElMessage.error('查询会员失败');
  }
};

// 格式化金额为元
const formatYuan = (amount: number | undefined): string => {
  if (amount === undefined) return '0.00';
  return (amount / 100).toFixed(2);
};

// 点击会员卡触发选择
const handleCardClick = (id: string) => {
  // 检查卡片是否冻结
  const member = memberList.value.find(item => item.id === id);
  if (member && isCardFrozen(member)) {
    ElMessage.warning('该会员卡已冻结，无法选择');
    return;
  }

  selectedMemberId.value = id;
  handleMemberSelect();
};

// 会员选择处理
const handleMemberSelect = () => {
  if (!selectedMemberId.value) return;

  const selectedMember = memberList.value.find((item: MemberVO) => item.id === selectedMemberId.value);
  if (selectedMember) {
    emit('confirm', selectedMember);
    closeDialog();
  }
};

// 关闭弹窗重置状态
const handleClose = () => {
  queryValue.value = '';
  memberList.value = [];
  selectedMemberId.value = '';
  hasSearched.value = false;
};

const closeDialog = () => {
  handleClose();
  emit('update:visible', false);
};

// 数字键盘相关
const numberButtons = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '0', '00'];

// 数字输入处理
const handleNumberInput = (num: string) => {
  queryValue.value += num;
};

const handleDelete = () => {
  queryValue.value = queryValue.value.slice(0, -1);
};

const handleClear = () => {
  queryValue.value = '';
};

// 计算总余额
const getTotalBalance = (member: any): number => {
  if (!member) return 0;
  return (member.principalBalance || 0) + (member.roomBonusBalance || 0) + (member.goodsBonusBalance || 0) + (member.commonBonusBalance || 0);
};

// 判断会员卡是否冻结
const isCardFrozen = (member: MemberVO): boolean => {
  // 根据status字段判断，冻结状态可能是 'frozen', 'disabled', 'locked' 等
  // 或者根据isEnabled字段判断
  return member.status === 'frozen' || member.status === 'disabled' || member.status === 'locked' || !member.isEnabled;
};
</script>

<style scoped>
/* 按钮基础样式 */
.grid button {
  font-size: 1.25rem;
  transition: all 0.2s;
}

.grid button:hover {
  background-color: #e6e6e6;
}

/* 输入框样式 */
:deep(.el-input__inner) {
  font-size: 1.25rem;
  text-align: left;
  padding-left: 1rem;
}

/* 主题色变量 */
.text-primary {
  color: var(--el-color-primary);
}

.border-primary {
  border-color: var(--el-color-primary);
}

/* 会员卡样式 */
.member-card {
  position: relative;
  width: 332px;
  height: 180px;
  border-radius: 8px;
  margin-top: 24px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: url('@/assets/images/membercard_bg.png') no-repeat center center;
  background-size: cover;
  flex-shrink: 0;
}

.member-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 冻结状态的会员卡样式 */
.member-card-frozen {
  opacity: 0.6;
  cursor: not-allowed !important;
  position: relative;
}

.member-card-frozen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.member-card-frozen:hover {
  transform: none;
  box-shadow: none;
}

.member-card-frozen .member-card-content {
  position: relative;
  z-index: 2;
}

/* 冻结标识样式 */
.frozen-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(255, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 12px;
  z-index: 3;
}

.member-card-content {
  position: absolute;
  inset: 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.card-balance {
  margin-top: auto;
}

.member-card-selected {
  box-shadow:
    0 0 0 2px var(--el-color-primary),
    0 8px 16px -4px rgba(0, 0, 0, 0.2);
}

.card-checkbox {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 30px;
  height: 30px;
  background: var(--el-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义搜索输入框样式 */
:deep(.custom-search-input .el-input__wrapper) {
  background-color: #f3f3f3;
  box-shadow: none;
  height: 68px;
  border-radius: 8px;
}

:deep(.custom-search-input .el-input__inner) {
  font-size: 24px;
  text-align: center;
}
</style>
