import request from '@/utils/request';
import { BaseResponse } from '@/types/baseResponse';
import {
  OrderOpenVO,
  OrderProductVO,
  OrderRoomPlanVO,
  RoomVO
} from '@/types/projectobj';

import { SessionVO, SessionOperationVOVoderpltvvErpManagentApiVoSessionVO } from '@/api/autoGenerated/shared/types/other'
import { PayResultVO, SessionOperationVOVoPayResultVO } from '@/api/autoGenerated/shared/types/result'
import { V3AddOrderAdditionalPayReqDto, OrderVO, SessionOperationVOVoOrderVO, V3AddOrderOpenPayReqDto, V3QueryOrderPayReqDto, V3AddOrderAdditionalReqDto } from '@/api/autoGenerated/shared/types/order'

/** 创建订单请求 */
interface CreateOrderRequest {
  /** 场馆ID */
  venueId: string;
  /** 房间ID */
  roomId: string;
  /** 订单编号 */
  orderNo: string;
  /** 员工ID */
  employeeId: string;
  /** 会员ID */
  memberId: string;
  /** 总金额 */
  totalAmount: number;
}

/** 更新订单状态请求 */
interface UpdateOrderStatusRequest {
  /** 订单ID */
  orderId: string;
  /** 订单状态 */
  status: string;
  /** 员工ID */
  employeeId: string;
}

/** 获取订单详情请求 */
interface GetOrderDetailsRequest {
  /** 订单ID */
  orderId: string;
}

/** 获取订单列表请求 */
export interface ListOrdersRequest {
  /** 场馆ID */
  venueId?: string;
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 订单编号 */
  orderNo?: string;
  /** 房间ID */
  roomId?: string;
  /** 会员ID */
  memberId?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 订单状态 */
  payStatus?: string;
}

/** 查询开台订单请求 */
interface QueryOpenOrderRequest {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 会话ID */
  sessionId?: string;
  /** 场馆ID */
  venueId?: string;
  /** 房间ID */
  roomId?: string;
  /** 会员ID */
  memberId?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 订单状态 */
  status?: string;
}

/** 清扫房间请求 */
interface CleanRoomRequest {
  /** 房间ID */
  roomId: string;
  /** 员工ID */
  employeeId: string;
}

/** 开台创建订单请求 */
export interface OpenOrderRequest {
  venueId: string;
  roomId: string;
  bookingId?: string;
  startTime: number;
  endTime: number;
  pricePlanId: string;
  pricePlanName: string;
  consumptionMode: string;
  selectedAreaId: string;
  selectedRoomTypeId: string;
  timeChargeMode: string;
  buyMinute: number;
  timeChargeAmount: number;
  timeChargeEndTime: string;
  timeChargeType: string;
  totalAmount: number;
  isOpenTableSettled: boolean;
  minimumCharge: number;
  currentTime: number;
  orderRoomPlanVOS: OrderRoomPlanVO[];
  inOrderProductInfos: OrderProductVO[];
  outOrderProductInfos: OrderProductVO[];
  roomVO: RoomVO;
}

export interface AddOrderOpenReqDto {
  roomId: string;
  sessionId: string;
  giftMinute: number;
  operator: string;
}

interface TagRoomRequest {
  roomId: string;
}

interface BaseRoomRequest {
  roomId: string;
}

interface BaseSessionRequest {
  sessionId: string;
}

export interface BaseRoomSessionRequest {
  roomId: string;
  sessionId: string;
}

interface FaultRoomRequest {
  roomId: string;
  content: string;
}

/** 点单请求 */
interface AdditionalOrderRequest {
  sessionId: string;
  venueId: string;
  roomId: string;
  currentTime: number;
  orderProductVOs: OrderProductVO[];
  roomVO: RoomVO;
}


// 添加支付结果接口定义
export interface QueryOrderPayResponse {
  code: number
  message: string
  data: {
    order_id: string
    pay_amount: number
    pay_time: number
    pay_type: string
    pay_id: string
    refund_id: string
    status: string
    pay: {
      pay_id: string
      pay_url: string
    }
    err_msg: string
    err_code: string
  }
  requestID: string
  serverTime: number
}

export interface SwapRoomRequest {
  sessionVOOpeningA: BaseRoomSessionRequest;
  sessionVOOpeningB: BaseRoomSessionRequest;
}

export interface AddOrderTransferRoomReqDto {
  sessionVOOpening: BaseRoomSessionRequest;
  sessionVOIdle: BaseRoomSessionRequest;
}

export interface AddOrderAttachRoomReqDto {
  sessionVOIdle: BaseRoomSessionRequest;
  sessionVOOpening: BaseRoomSessionRequest;
}

export interface AddOrderUnionRoomReqDto {
  sessionVOOpeningA: BaseRoomSessionRequest;
  sessionVOOpeningB: BaseRoomSessionRequest;
}

export interface PayRecordVO {
  payType?: string;
  totalFee?: number;
}

/**
 * 结束计时请求接口
 */
export interface FinishTimingRequest {
  roomId: string;
  sessionId: string;
  orderRoomPlanVOS: {
    roomId: string;
    roomName: string;
    venueId: string;
    sessionId: string;
    orderNo: string;
    startTime: number;
    endTime: number;
    duration: number;
    payAmount: number;
    originalPayAmount: number;
    unitPrice: number;
    pricePlanId: string;
    pricePlanName: string;
    isTimeConsume: boolean;
    state: number;
    version: number;
    ctime: number;
    utime: number;
  }[];
}

export interface ProductSalesStatisticsRequest {
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
}

export interface ProductSalesStatisticsItem {
  /** 分类ID */
  categoryId: string;
  /** 分类名称 */
  categoryName: string;
  /** 员工ID */
  employeeId: string;
  /** 是否赠品 */
  isGift: boolean;
  /** 产品ID */
  productId: string;
  /** 产品名称 */
  productName: string;
  /** 数量 */
  quantity: number;
  /** 应收费用 */
  shouldFee: number;
  /** 总费用 */
  totalFee: number;
  /** 场馆ID */
  venueId: string;
}

export class OrderApi {
  /** 创建订单 */
  static async createOrder(params: CreateOrderRequest): Promise<BaseResponse<OrderVO>> {
    return request.post('/api/order/create', params);
  }

  /** 更新订单状态 */
  static async updateOrderStatus(params: UpdateOrderStatusRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/update-status', params);
  }

  /** 获取订单详情 */
  static async getOrderDetails(params: GetOrderDetailsRequest): Promise<BaseResponse<OrderVO>> {
    return request.post('/api/order/details', params);
  }

  /** 获取订单列表 */
  static async listOrders(params: ListOrdersRequest): Promise<BaseResponse<OrderVO[]>> {
    return request.post('/api/order/list', params);
  }

  /** 查询开台订单 */
  static async queryOpenOrder(params: BaseSessionRequest): Promise<BaseResponse<OrderOpenVO>> {
    return request.post('/api/order/query-open', params);
  }

  /** 取消开台 */
  static async cancelOpen(params: { roomId: string; sessionId: string }): Promise<BaseResponse<void>> {
    return request.post('/api/order/cancel-open', params);
  }

  /** 清扫房间 */
  static async cleanRoom(params: CleanRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/clean-room', params);
  }

  /** 清扫房间完成 */
  static async cleanRoomFinish(params: CleanRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/clean-room-finish', params);
  }

  /** 开台创建订单 */
  static async openOrder(params: OpenOrderRequest): Promise<BaseResponse<SessionOperationVOVoderpltvvErpManagentApiVoSessionVO>> {
    return request.post('/api/v3/order/open', params);
  }

  /** 支付订单 */
  static async payOrder(params: V3QueryOrderPayReqDto): Promise<BaseResponse<SessionOperationVOVoPayResultVO>> {
    return request.post('/api/v3/order/pay', params);
  }

  /** 查询支付状态 */
  static async queryOrderPay(params: {
    billId?: string;
    roomId: string;
    sessionId: string;
  }): Promise<BaseResponse<any>> {
    return request.post('/api/v3/order/pay/query', params);
  }

  /** 开台立结 */
  static async openPay(params: V3AddOrderOpenPayReqDto): Promise<BaseResponse<SessionOperationVOVoderpltvvErpManagentApiVoSessionVO>> {
    return request.post('/api/v3/order/open-pay', params);
  }

  /** 后付续台 */
  static async openContinue(params: OpenOrderRequest): Promise<BaseResponse<SessionOperationVOVoderpltvvErpManagentApiVoSessionVO>> {
    return request.post('/api/v3/order/open-continue', params);
  }

  /** 立结续台 */
  static async openContinuePay(params: OpenOrderRequest): Promise<BaseResponse<SessionOperationVOVoderpltvvErpManagentApiVoSessionVO>> {
    return request.post('/api/v3/order/open-continue-pay', params);
  }

  /** 点单 */
  static async additionalOrder(params: AdditionalOrderRequest): Promise<BaseResponse<SessionOperationVOVoOrderVO>> {
    return request.post('/api/order/additional-order', params);
  }

  static async additionalOrderV3(params: V3AddOrderAdditionalReqDto): Promise<BaseResponse<SessionOperationVOVoOrderVO>> {
    return request.post('/api/v3/order/additional-order', params);
  }

  /** 点单立付 */
  static async additionalOrderPay(params: V3AddOrderAdditionalPayReqDto): Promise<BaseResponse<SessionOperationVOVoOrderVO>> {
    return request.post('/api/v3/order/additional-order-pay', params)
  }

  /** 关房 */
  static async closeRoom(params: BaseRoomSessionRequest): Promise<BaseResponse<void>> {
    return request.post('/api/v3/order/close-room', params);
  }

  /** 重开 */
  static async reopenRoom(params: OpenOrderRequest): Promise<BaseResponse<void>> {
    return request.post('/api/v3/order/order-reopen', params);
  }

  /** 锁房 */
  static async lockRoom(params: TagRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/v3/order/lock-room', params);
  }
  /** 解锁房间 */
  static async unlockRoom(params: TagRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/v3/order/unlock-room', params);
  }

  /** 故障房间 */
  static async faultRoom(params: FaultRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/fault-room', params);
  }

  /** 解除故障房间 */
  static async unfaultRoom(params: TagRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/unfault-room', params);
  }

  /** 关台 */
  static async closeOrder(params: OpenOrderRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/close-order', params);
  }

  /** 带客 */
  static async withGuest(params: BaseRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/with-guest', params);
  }

  /** 取消带客 */
  static async cancelWithGuest(params: BaseRoomRequest): Promise<BaseResponse<void>> {
    return request.post('/api/order/cancel-with-guest', params);
  }

  /** 赠送时长 */
  static async timeGift(params: AddOrderOpenReqDto): Promise<BaseResponse<any>> {
    return request.post('/api/v3/order/gift-time', params);
  }

  /** 互换包房 */
  static async swapRoom(params: SwapRoomRequest): Promise<BaseResponse<any>> {
    return request.post('/api/v3/order/swap-room', params);
  }

  /** 转台 */
  static async transferRoom(params: AddOrderTransferRoomReqDto): Promise<BaseResponse<any>> {
    return request.post('/api/order/transfer-room', params);
  }

  /** 联房 */
  static async attachRoom(params: AddOrderAttachRoomReqDto): Promise<BaseResponse<any>> {
    return request.post('/api/order/attach-room', params);
  }

  /** 并房 */
  static async mergeRoom(params: AddOrderUnionRoomReqDto): Promise<BaseResponse<any>> {
    return request.post('/api/v3/order/merge-room', params);
  }

  /** 结束计时 */
  static async finishTiming(params: FinishTimingRequest): Promise<BaseResponse<any>> {
    return request.post('/api/v3/order/end-time-consume', params);
  }

  /** 查询产品销售统计 */
  static async queryProductSalesStatistics(params: ProductSalesStatisticsRequest): Promise<BaseResponse<ProductSalesStatisticsItem[]>> {
    return request.post('/api/v3/order/query/product/sales', params);
  }

  /** 取消联台 */
  static async cancelAttachRoom(params: { roomId: string; sessionId: string }): Promise<BaseResponse<void>> {
    console.log('[OrderApi] cancelAttachRoom', params);
    return request.post('/api/order/cancel-attach-room', {
      sessionVOSlave: {
        roomId: params.roomId,
        sessionId: params.sessionId
      }
    });
  }
}
