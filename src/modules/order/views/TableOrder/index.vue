<template>
  <div class="flex flex-col h-full">
    <!-- 顶部筛选区域 -->
    <nav class="p-4 border-b">
      <div class="flex items-center justify-between">
        <!-- 左侧标签页 -->
        <div class="flex bg-[#F3F3F3] rounded-[10px] h-[68px] items-center gap-[8px] px-[8px]">
          <div
            v-for="tab in tabOptions"
            :key="tab.value"
            class="h-[52px] w-[120px] font-medium flex items-center justify-center cursor-pointer text-[16px] rounded-[8px]"
            :class="{ 'bg-[#E23939] text-white': vm.state.activeTab === tab.value, 'text-gray-500': vm.state.activeTab !== tab.value }"
            @click="selectTab(tab.value)">
            {{ tab.label }}
          </div>
        </div>

        <!-- 中间搜索区域 -->
        <div class="flex-1 flex justify-center ml-4">
          <div class="relative w-full">
            <el-input v-model="searchKeyword" placeholder="单号 / 包厢 " class="custom-search-input" @keyup.enter="handleSearch" clearable>
              <template #prefix>
                <el-icon class="ml-4">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 时间筛选 -->
        <div class="flex items-center ml-[24px]">
          <el-date-picker
            class="customer-date-picker"
            v-model="vm.state.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY/MM/DD"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            @change="vm.actions.handleDateRangeChange" />
        </div>

        <!-- 右侧按钮区域 -->
        <div class="ml-[24px]">
          <el-button type="normal" class="h-[68px] px-6 text-lg" @click="handleReset">
            <el-icon class="mr-2">
              <RefreshRight />
            </el-icon>
            重置
          </el-button>
        </div>
      </div>
    </nav>

    <!-- 数据列表区域 -->
    <div class="flex-1 p-4 overflow-auto">
      <TableOrderlist
        ref="tableListRef"
        :search-params="searchParams"
        v-model:loading="vm.state.loading"
        @view-details="vm.actions.handleViewDetails"
        @refund="vm.actions.handleRefund" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { Search, RefreshRight, ArrowDown } from '@element-plus/icons-vue';
import { OrderTabType } from './viewmodel';
import { useOrder } from './presenter';
import { OrderConverter } from './converter';
import TableOrderlist from './components/TableOrderlist.vue';

defineOptions({
  name: 'TableOrder'
});

// 使用展示器
const vm = useOrder();

// 本地搜索关键词，避免直接修改searchForm
const searchKeyword = ref('');

// 构建搜索参数，避免直接传递vm.state.searchForm
const searchParams = computed(() => {
  return {
    ...vm.state.searchForm,
    // 从本地变量获取关键词
    orderNo: searchKeyword.value
  };
});

// 防抖计时器
let searchTimer: number | null = null;

// 处理搜索
const handleSearch = () => {
  // 取消之前的延迟搜索
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 更新vm中的orderNo
  vm.state.searchForm.orderNo = searchKeyword.value;
  // 触发本地搜索
  vm.actions.onSubmit();
};

// 监听搜索关键词变化，使用防抖
watch(searchKeyword, newValue => {
  // 取消之前的延迟搜索
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的延迟搜索（300ms延迟）
  searchTimer = setTimeout(() => {
    // 更新vm中的orderNo
    vm.state.searchForm.orderNo = newValue;
    // 触发本地搜索
    vm.actions.onSubmit();
  }, 300) as unknown as number;
});

// 处理重置
const handleReset = () => {
  searchKeyword.value = '';
  vm.actions.handleReset();
};

// 获取状态类型
const getPayStatusVariant = OrderConverter.getPayStatusVariant;

// 定义标签选项
const tabOptions = [
  { label: '全部订单', value: OrderTabType.ALL },
  { label: '待结订单', value: OrderTabType.PENDING },
  { label: '已结订单', value: OrderTabType.PAID }
  // { label: '挂单订单', value: OrderTabType.SUSPENDED }
];

// 选择标签的处理函数
const selectTab = (tabValue: OrderTabType) => {
  vm.state.activeTab = tabValue;
  vm.actions.handleTabChange(tabValue);
};

// 表格引用
const tableListRef = ref();

// 组件挂载后
onMounted(() => {
  // 同步初始值
  searchKeyword.value = vm.state.searchForm.orderNo || '';
  // 设置表格引用
  vm.setTableListRef(tableListRef.value);
});
</script>

<style scoped>
/* 按钮样式 */
:deep(.el-button) {
  border-radius: 8px;
}

/* 表格标题样式 */
:deep(.el-table__header .cell) {
  font-size: 18px;
  padding-bottom: 12px;
}
</style>
