import { ref, computed, reactive, readonly } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { toast } from '@/components/customer/toast';
import { OrderApi } from '../../api/order';
import { RoomOrderDetailConverter } from './converter';
import DialogManager from '@/utils/dialog';
import { ElMessage } from 'element-plus';

import { formatDuration, now10 } from '@/utils/dateUtils';
import type {
  IRoomOrderDetailViewModel,
  IRoomOrderDetailState,
  IRoomOrderDetailComputed,
  IRoomOrderDetailActions,
  OrderDisplay,
  SelectionState
} from './viewmodel';

export class RoomOrderDetailPresenter implements IRoomOrderDetailViewModel {
  private _state = reactive<IRoomOrderDetailState>({
    orderData: null,
    selectedRoomPlans: { selectAll: false, orders: [] },
    selectedInPackageOrders: { selectAll: false, orders: [] },
    selectedOutPackageOrders: { selectAll: false, orders: [] },
    selectedProducts: [],
    expandedOrders: [],
    printDialogVisible: false,
    refundMap: new Map(),
    expandedPackageProducts: [],
    defaultExpandPackageProducts: true // 默认展开套餐商品
  });

  private route = useRoute();
  private router = useRouter();

  public get state(): IRoomOrderDetailState {
    // 返回一个新对象，避免readonly类型问题
    return {
      orderData: this._state.orderData,
      selectedRoomPlans: this._state.selectedRoomPlans,
      selectedInPackageOrders: this._state.selectedInPackageOrders,
      selectedOutPackageOrders: this._state.selectedOutPackageOrders,
      selectedProducts: this._state.selectedProducts,
      expandedOrders: this._state.expandedOrders,
      printDialogVisible: this._state.printDialogVisible,
      refundMap: this._state.refundMap,
      expandedPackageProducts: this._state.expandedPackageProducts,
      defaultExpandPackageProducts: this._state.defaultExpandPackageProducts
    };
  }

  // 计算退款金额
  private calculateRefundAmount(product: any): number {
    const refundQuantity = product.quantity - (product.displayQuantity || 0);

    // 优先使用实际的退款金额
    if (product.refundAmount) {
      return product.refundAmount;
    }

    // 考虑折扣因素
    const discount = (product.payProductDiscount || 100) / 100; // 折扣率，如85表示85%

    // 使用单价 * 数量 * 折扣
    return refundQuantity * product.originalPrice * discount;
  }

  // 获取商品的退款信息
  private getProductRefundInfo(product: any) {
    const refundQuantity = product.quantity - (product.displayQuantity || 0);
    const refundAmount = this.calculateRefundAmount(product);
    return {
      refundQuantity,
      refundAmount
    };
  }

  // 创建退款映射
  private createRefundMap() {
    const refundMap = new Map<string, Map<string, { quantity: number; refundProducts: any[] }>>();

    // 没有数据时直接返回空映射
    if (!this._state.orderData?.orderVOS) return refundMap;

    // 找出所有退款订单
    const refundOrders = this._state.orderData.orderVOS.filter(order => order.direction === 'refund' && order.pOrderNo);

    console.log(
      '[Debug] 退款订单列表:',
      refundOrders.map(o => ({ orderNo: o.orderNo, pOrderNo: o.pOrderNo }))
    );

    // 为每个退款订单建立映射
    for (const refundOrder of refundOrders) {
      // 获取原始订单号
      const originalOrderNo = refundOrder.pOrderNo as string;

      // 确保该原始订单有一个映射
      if (!refundMap.has(originalOrderNo)) {
        refundMap.set(originalOrderNo, new Map<string, { quantity: number; refundProducts: any[] }>());
      }

      // 获取该退款订单下的所有商品 - 处理套餐外商品
      const refundOutProducts = this._state.orderData?.outOrderProductInfos?.filter(product => product.orderNo === refundOrder.orderNo) || [];

      // 获取该退款订单下的所有商品 - 处理套餐内商品
      const refundInProducts = this._state.orderData?.inOrderProductInfos?.filter(product => product.orderNo === refundOrder.orderNo) || [];

      console.log('[Debug] 退款订单', refundOrder.orderNo, '套餐外商品:', refundOutProducts.length, '套餐内商品:', refundInProducts.length);

      // 为每个退款商品记录数量 - 处理套餐外商品
      for (const product of refundOutProducts) {
        const productMap = refundMap.get(originalOrderNo) as Map<string, { quantity: number; refundProducts: any[] }>;

        // 根据pId字段找到对应的原始商品
        const pId = product.pId;
        if (!pId) {
          console.log('[Debug] 警告: 退款商品缺少pId，无法关联原始商品:', product.productName);
          continue;
        }

        const refundQuantity = product.quantity || 0;

        // 更新已退数量和退款商品列表
        const current = productMap.get(pId) || { quantity: 0, refundProducts: [] };
        current.quantity += refundQuantity;
        current.refundProducts.push(product);
        productMap.set(pId, current);

        console.log('[Debug] 套餐外退款商品', product.productName, 'pId:', pId, '退款数量:', refundQuantity);
      }

      // 为每个退款商品记录数量 - 处理套餐内商品
      for (const product of refundInProducts) {
        const productMap = refundMap.get(originalOrderNo) as Map<string, { quantity: number; refundProducts: any[] }>;

        // 根据pId字段找到对应的原始商品
        const pId = product.pId;
        if (!pId) {
          console.log('[Debug] 警告: 退款商品缺少pId，无法关联原始商品:', product.productName);
          continue;
        }

        const refundQuantity = product.quantity || 0;

        // 更新已退数量和退款商品列表
        const current = productMap.get(pId) || { quantity: 0, refundProducts: [] };
        current.quantity += refundQuantity;
        current.refundProducts.push(product);
        productMap.set(pId, current);

        console.log('[Debug] 套餐内退款商品', product.productName, 'pId:', pId, '退款数量:', refundQuantity);
      }
    }

    console.log(
      '[Debug] 退款映射结果:',
      Array.from(refundMap.entries()).map(([key, value]) => {
        return {
          originalOrderNo: key,
          products: Array.from(value.entries()).map(([pId, data]) => ({ pId, quantity: data.quantity, refundCount: data.refundProducts.length }))
        };
      })
    );

    return refundMap;
  }

  // 检查订单是否部分退款
  private isOrderPartialRefund(orderNo: string): boolean {
    const refundInfo = this._state.refundMap?.get(orderNo);
    return !!refundInfo && refundInfo.size > 0;
  }

  // 检查订单是否全部退款
  private isOrderFullyRefunded(orderNo: string): boolean {
    const refundInfo = this._state.refundMap?.get(orderNo);
    if (!refundInfo || refundInfo.size === 0) {
      return false;
    }

    // 获取该订单的所有商品
    const inPackageProducts = this._state.orderData?.inOrderProductInfos?.filter(product => product.orderNo === orderNo) || [];
    const outPackageProducts = this._state.orderData?.outOrderProductInfos?.filter(product => product.orderNo === orderNo) || [];
    const allProducts = [...inPackageProducts, ...outPackageProducts];

    // 如果订单没有商品，认为不是全退
    if (allProducts.length === 0) {
      return false;
    }

    // 检查每个商品是否都完全退款
    for (const product of allProducts) {
      const productId = product.id; // 使用商品自身的id
      const refundData = refundInfo.get(productId);
      const refundQuantity = refundData?.quantity || 0;
      const originalQuantity = product.quantity || 0;

      // 如果有任何商品未完全退款，则不是全退
      if (refundQuantity < originalQuantity) {
        return false;
      }
    }

    console.log('[Debug] 订单', orderNo, '判断为全部退款');
    return true;
  }

  // 获取订单退款状态
  private getOrderRefundStatus(orderNo: string): 'none' | 'partial' | 'full' {
    if (this.isOrderFullyRefunded(orderNo)) {
      return 'full';
    } else if (this.isOrderPartialRefund(orderNo)) {
      return 'partial';
    } else {
      return 'none';
    }
  }

  public computed: IRoomOrderDetailComputed = {
    isMinChargeReached: computed(() => {
      if (!this._state.orderData?.sessionVO) return false;
      // 使用sessionVO.supermarketFee而不是自行计算商品总额
      // supermarketFee包含了所有商品的消费金额
      const productTotalAmount = this._state.orderData.sessionVO.supermarketFee || 0;
      return productTotalAmount >= (this._state.orderData.sessionVO.minConsume || 0);
    }),

    groupedProducts: computed(() => {
      return RoomOrderDetailConverter.toGroupedProducts(this._state.orderData);
    }),

    getRoomPlanPayStatus: computed(() => {
      return (orderNo: string) => {
        const order = this._state.orderData?.orderVOS?.find(order => order.orderNo === orderNo);
        return order?.status || 'unknown';
      };
    }),

    // 房费订单
    roomPlanOrders: computed((): OrderDisplay[] => {
      if (!this._state.orderData?.orderVOS || !this._state.orderData?.orderRoomPlanVOS) {
        return [];
      }

      // 获取所有房费订单
      const roomPlanOrders = this._state.orderData.orderVOS.filter(order => order.type === 'roomplan' && order.direction === 'normal');

      // 转换为显示模型
      const result = roomPlanOrders.map(order => {
        // 找到该订单号对应的所有房费计划
        const roomPlans = this._state.orderData?.orderRoomPlanVOS?.filter(plan => plan.orderNo === order.orderNo) || [];

        // 计算总金额
        const totalAmount = roomPlans.reduce((sum, plan) => sum + (plan.payAmount || 0), 0);

        const refundStatus = this.getOrderRefundStatus(order.orderNo);

        return {
          orderNo: order.orderNo,
          type: order.type,
          status: order.status,
          ctime: order.ctime,
          payAmount: totalAmount,
          totalAmount: totalAmount,
          isPartialRefund: this.isOrderPartialRefund(order.orderNo),
          refundStatus: refundStatus, // 新增退款状态
          // 添加所有房费计划，用于展开详情显示
          roomPlans: roomPlans
        };
      });

      return result;
    }),

    // 套餐内商品订单
    inPackageOrders: computed((): OrderDisplay[] => {
      if (!this._state.orderData?.orderVOS || !this._state.orderData?.inOrderProductInfos) {
        return [];
      }

      // 获取所有套餐内商品订单
      const inPackageProductOrders = this._state.orderData.orderVOS.filter(order => {
        // 检查是否有套餐内商品
        const hasInPackageProducts = this._state.orderData?.inOrderProductInfos?.some(product => product.orderNo === order.orderNo);
        return order.type === 'product' && order.direction === 'normal' && hasInPackageProducts;
      });

      // 转换为显示模型
      return inPackageProductOrders.map(order => {
        // 订单的所有商品
        const products = this._state.orderData?.inOrderProductInfos?.filter(product => product.orderNo === order.orderNo) || [];

        // 应用退款数量调整
        const adjustedProducts = products.map(product => {
          // 使用商品自身的id来查找退款信息
          const productId = product.id;
          const refundData = this._state.refundMap?.get(order.orderNo)?.get(productId);
          const refundQuantity = refundData?.quantity || 0;
          const refundProducts = refundData?.refundProducts || [];

          // 获取订单的商品，克隆并调整显示数量
          const adjustedProduct = { ...product };

          // 计算剩余数量
          adjustedProduct.displayQuantity = Math.max(0, (product.quantity || 0) - refundQuantity);

          // 添加退款商品信息
          adjustedProduct.refundProducts = refundProducts;

          // 标记商品退款状态
          if (refundQuantity === 0) {
            adjustedProduct.statusInOrder = 'normal'; // 未退
          } else if (refundQuantity >= (product.quantity || 0)) {
            adjustedProduct.statusInOrder = 'refunded'; // 已全退
          } else {
            adjustedProduct.statusInOrder = 'partial_refunded'; // 部分退
          }

          console.log(
            '[Debug] 套餐内商品',
            product.productName,
            '状态:',
            adjustedProduct.statusInOrder,
            '退款数量:',
            refundQuantity,
            '原数量:',
            product.quantity,
            '商品ID:',
            productId
          );

          return adjustedProduct;
        });

        // 计算订单总金额（应付金额）
        const totalAmount = products.reduce((sum, product) => sum + (product.payAmount || 0), 0);

        // 计算实付金额（考虑退款后的实际支付金额）
        const actualPayAmount = adjustedProducts.reduce((sum, product) => {
          const displayQuantity = product.displayQuantity || product.quantity || 0;
          const unitPrice = product.payAmount && product.quantity ? Math.round(product.payAmount / product.quantity) : 0;
          return sum + unitPrice * displayQuantity;
        }, 0);

        const isPartialRefund = this.isOrderPartialRefund(order.orderNo);
        const refundStatus = this.getOrderRefundStatus(order.orderNo);
        console.log('[Debug] 套餐内订单', order.orderNo, '退款状态:', refundStatus);

        return {
          orderNo: order.orderNo,
          type: order.type,
          status: order.status,
          ctime: order.ctime,
          payAmount: actualPayAmount, // 实付金额
          totalAmount: totalAmount, // 应付金额
          isPartialRefund: isPartialRefund,
          refundStatus: refundStatus, // 新增退款状态
          products: adjustedProducts
        };
      });
    }),

    // 套餐外商品订单
    outPackageOrders: computed((): OrderDisplay[] => {
      if (!this._state.orderData?.orderVOS || !this._state.orderData?.outOrderProductInfos) {
        return [];
      }

      // 获取所有套餐外商品订单
      const outPackageProductOrders = this._state.orderData.orderVOS.filter(order => {
        // 检查是否有套餐外商品
        const hasOutPackageProducts = this._state.orderData?.outOrderProductInfos?.some(product => product.orderNo === order.orderNo);
        return order.type === 'product' && order.direction === 'normal' && hasOutPackageProducts;
      });

      // 转换为显示模型
      return outPackageProductOrders.map(order => {
        // 订单的所有商品
        const products = this._state.orderData?.outOrderProductInfos?.filter(product => product.orderNo === order.orderNo) || [];

        // 应用退款数量调整
        const adjustedProducts = products.map(product => {
          // 使用商品自身的id来查找退款信息
          const productId = product.id;
          const refundData = this._state.refundMap?.get(order.orderNo)?.get(productId);
          const refundQuantity = refundData?.quantity || 0;
          const refundProducts = refundData?.refundProducts || [];

          // 获取订单的商品，克隆并调整显示数量
          const adjustedProduct = { ...product };

          // 计算剩余数量
          adjustedProduct.displayQuantity = Math.max(0, (product.quantity || 0) - refundQuantity);

          // 添加退款商品信息
          adjustedProduct.refundProducts = refundProducts;

          // 标记商品退款状态
          if (refundQuantity === 0) {
            adjustedProduct.statusInOrder = 'normal'; // 未退
          } else if (refundQuantity >= (product.quantity || 0)) {
            adjustedProduct.statusInOrder = 'refunded'; // 已全退
          } else {
            adjustedProduct.statusInOrder = 'partial_refunded'; // 部分退
          }

          console.log(
            '[Debug] 套餐外商品',
            product.productName,
            '状态:',
            adjustedProduct.statusInOrder,
            '退款数量:',
            refundQuantity,
            '原数量:',
            product.quantity,
            '商品ID:',
            productId
          );

          return adjustedProduct;
        });

        // 计算订单应付总金额
        const totalAmount = products.reduce((sum, product) => sum + (product.payAmount || 0), 0);

        // 计算实付金额（考虑退款后的实际支付金额）
        const actualPayAmount = adjustedProducts.reduce((sum, product) => {
          const displayQuantity = product.displayQuantity || product.quantity || 0;
          const unitPrice = product.payAmount && product.quantity ? Math.round(product.payAmount / product.quantity) : 0;
          return sum + unitPrice * displayQuantity;
        }, 0);

        const refundStatus = this.getOrderRefundStatus(order.orderNo);

        return {
          orderNo: order.orderNo,
          type: order.type,
          status: order.status,
          ctime: order.ctime,
          payAmount: actualPayAmount, // 实付金额
          totalAmount: totalAmount, // 应付金额
          isPartialRefund: this.isOrderPartialRefund(order.orderNo),
          refundStatus: refundStatus, // 新增退款状态
          products: adjustedProducts
        };
      });
    }),

    // 新增使用时长计算属性
    usageDuration: computed((): string => {
      const sessionVO = this._state.orderData?.sessionVO;

      if (!sessionVO || !sessionVO.startTime) {
        return '-';
      }

      const startTime = sessionVO.startTime; // 转为毫秒
      let endTime: number;
      console.log('[Debug] this.state.orderData', this._state.orderData);
      console.log('[Debug] 使用时长计算:', sessionVO.closeTime, sessionVO.payStatus, sessionVO.endTime);

      if (sessionVO.closeTime) {
        // 如果已经关台，使用closeTime
        endTime = sessionVO.closeTime; // 转为毫秒
        console.log('[Debug] 使用时长计算 closeTime:', sessionVO.closeTime, sessionVO.payStatus, sessionVO.endTime);
      } else {
        // 如果未结账（使用中），使用当前时间
        endTime = now10();
        console.log('[Debug] 使用时长计算 now13:', sessionVO.closeTime, sessionVO.payStatus, sessionVO.endTime);
      }
      console.log('[Debug] 使用时长计算:', startTime, endTime);
      const durationMs = Math.max(0, endTime - startTime);
      console.log('[Debug] 使用时长计算 durationMs:', durationMs);
      return formatDuration(durationMs);
    }),

    // 获取商品的退款信息
    getProductRefundInfo: computed(() => {
      return (product: any) => {
        return this.getProductRefundInfo(product);
      };
    }),

    // 解析套餐商品详情
    getPackageProductDetails: computed(() => {
      return (packageProductInfo: string) => {
        try {
          if (!packageProductInfo) return [];
          return JSON.parse(packageProductInfo);
        } catch (error) {
          console.error('解析套餐商品信息失败:', error);
          return [];
        }
      };
    }),

    // 检查套餐商品是否已展开
    isPackageProductExpanded: computed(() => {
      return (productId: string) => {
        return this._state.expandedPackageProducts.includes(productId);
      };
    }),

    // 包厢费用
    roomFeeAmount: computed(() => {
      return this._state.orderData?.sessionVO?.roomFee || 0;
    }),

    // 商品费用
    productFeeAmount: computed(() => {
      return this._state.orderData?.sessionVO?.supermarketFee || 0;
    }),

    // 消费总计 = 包厢费用 + 商品费用
    totalConsumptionAmount: computed(() => {
      const roomFee = this._state.orderData?.sessionVO?.roomFee || 0;
      const productFee = this._state.orderData?.sessionVO?.supermarketFee || 0;
      return roomFee + productFee;
    })
  };

  public actions: IRoomOrderDetailActions = {
    handleManageProduct: () => {
      this.router.push({
        path: '/room/order/product',
        query: {
          orderId: this.route.params.orderId,
          roomId: this._state.orderData?.sessionVO?.roomId
        }
      });
    },

    handleSettlementDetail: () => {
      this.router.push({
        path: '/room/order/settlement',
        query: {
          orderId: this.route.params.orderId
        }
      });
    },

    handleCleanComplete: () => {
      toast({
        title: '功能开发中...',
        duration: 1000,
        description: '功能开发中...'
      });
    },

    handlePrint: () => {
      if (!this.route.query.orderId) {
        toast({
          title: '错误',
          description: '订单ID不能为空',
          duration: 1000
        });
        return;
      }
      this._state.printDialogVisible = true;
    },

    toggleOrder: (orderNo: string) => {
      const index = this._state.expandedOrders.indexOf(orderNo);
      if (index === -1) {
        this._state.expandedOrders.push(orderNo);
      } else {
        this._state.expandedOrders = this._state.expandedOrders.filter(no => no !== orderNo);
      }
    },

    calculateRoomFeesTotal: () => {
      if (!this._state.orderData?.orderRoomPlanVOS) return 0;
      return this._state.orderData.orderRoomPlanVOS.reduce((sum: number, item: any) => sum + item.payAmount, 0);
    },

    getStatusText: (status: string) => {
      const statusMap: Record<string, string> = {
        unpaid: '未结',
        unpay: '未结',
        payed: '已结',
        paid: '已结',
        partial_payment: '部分支付',
        cancel: '已取消',
        refund: '已退款',
        unknown: '未知状态'
      };
      return statusMap[status] || status;
    },

    handlePopup: () => {
      if (!this._state.orderData?.sessionVO?.unpaidAmount) {
        toast({
          title: '无需结账',
          description: '当前账单无需结账或金额为0',
          duration: 1500
        });
        return;
      }

      toast({
        title: '即将结账',
        description: `待结金额: ${this._state.orderData.sessionVO.unpaidAmount / 100}元`,
        duration: 1500
      });
    },

    handleSettlement: async () => {
      console.log('[Debug] 结账按钮点击');
      if (!this.route.query.orderId) {
        toast({
          title: '错误',
          description: '订单ID不能为空',
          duration: 1000
        });
        return;
      }
      console.log('[Debug] 结账金额:', this._state.orderData?.sessionVO?.unpaidAmount);
      if (this._state.orderData?.sessionVO?.unpaidAmount === 0) {
        toast({
          title: '无需结账',
          description: '当前账单无需结账或金额为0',
          duration: 1500
        });
        return;
      }
      // 使用DialogManager打开支付弹窗，替代路由跳转
      await DialogManager.open(
        'OrderPayDialog',
        {
          sessionId: this.route.query.orderId as string,
          payType: 'later' // 开台后结
        },
        {
          paySuccess: result => {
            console.log('支付成功:', result);
            // 刷新当前订单数据
            this.init();
          },
          payCancel: () => {
            console.log('支付已取消');
          }
        }
      );
    },

    // 批量选择功能
    toggleSelectAllRoomPlans: () => {
      const selectAll = this._state.selectedRoomPlans.selectAll;
      if (selectAll) {
        // 全选 - 添加所有订单号
        this._state.selectedRoomPlans.orders = this.computed.roomPlanOrders.value.map(order => order.orderNo);
      } else {
        // 取消全选
        this._state.selectedRoomPlans.orders = [];
      }
    },

    toggleSelectAllInPackageOrders: () => {
      const selectAll = this._state.selectedInPackageOrders.selectAll;
      if (selectAll) {
        // 全选 - 添加所有订单号
        this._state.selectedInPackageOrders.orders = this.computed.inPackageOrders.value.map(order => order.orderNo);
      } else {
        // 取消全选
        this._state.selectedInPackageOrders.orders = [];
      }
    },

    toggleSelectAllOutPackageOrders: () => {
      const selectAll = this._state.selectedOutPackageOrders.selectAll;
      if (selectAll) {
        // 全选 - 添加所有订单号
        this._state.selectedOutPackageOrders.orders = this.computed.outPackageOrders.value.map(order => order.orderNo);
      } else {
        // 取消全选
        this._state.selectedOutPackageOrders.orders = [];
      }
    },

    // 支付详情
    handleShowPayDetail: () => {
      if (!this._state.orderData) {
        ElMessage.warning('订单数据不存在');
        return;
      }

      // 获取需要的参数
      const sessionId = this.route.query.orderId as string;
      const roomId = this._state.orderData.sessionVO?.roomId;
      const sessionStatus = this._state.orderData.sessionVO?.status;

      console.log('[Debug] 结账详情', { sessionId, roomId, sessionStatus });

      // 使用DialogManager.open打开账单还原对话框，并监听还原成功事件
      DialogManager.open(
        'BillRestoreDetailDialog',
        {
          roomId,
          sessionId,
          sessionStatus
        },
        {
          'restore-success': () => {
            // 账单还原成功后刷新消费明细页面数据
            ElMessage.success('账单还原成功');
            this.init();
          }
        }
      ).catch(error => {
        console.error('账单还原对话框关闭或发生错误:', error);
      });
    },

    // 结束计时
    handleFinishTiming: async () => {
      const sessionId = (this.route.query.orderId as string) || this._state.orderData?.sessionVO?.sessionId;
      const roomId = this._state.orderData?.sessionVO?.roomId;
      const roomTypeId = (this._state.orderData as any)?.roomVO?.typeId;
      const areaId = (this._state.orderData as any)?.roomVO?.areaId;
      const roomName = this._state.orderData?.roomVO?.name;

      if (!sessionId || !roomId) return;

      try {
        // 使用对话框代替跳转
        DialogManager.open('FinishTimingDialog', {
          sessionId,
          roomId,
          roomTypeId,
          areaId,
          roomName
        }).then(() => {
          // 结束计时成功后刷新当前订单数据
          this.init();
        });
      } catch (error) {
        console.error('结束计时过程中发生错误:', error);
      }
    },

    // 切换套餐商品展开/收起状态
    togglePackageProduct: (productId: string) => {
      const index = this._state.expandedPackageProducts.indexOf(productId);
      if (index === -1) {
        // 展开
        this._state.expandedPackageProducts.push(productId);
      } else {
        // 收起
        this._state.expandedPackageProducts.splice(index, 1);
      }
    },

    // 全部展开/收起套餐商品
    toggleAllPackageProducts: () => {
      if (!this._state.orderData) return;

      // 收集所有套餐商品ID
      const allPackageProductIds: string[] = [];

      this._state.orderData.inOrderProductInfos?.forEach(product => {
        if (product.packageProductInfo && product.packageProductInfo.trim()) {
          allPackageProductIds.push(product.id);
        }
      });

      this._state.orderData.outOrderProductInfos?.forEach(product => {
        if (product.packageProductInfo && product.packageProductInfo.trim()) {
          allPackageProductIds.push(product.id);
        }
      });

      // 如果当前全部展开，则全部收起；否则全部展开
      const allExpanded = allPackageProductIds.every(id => this._state.expandedPackageProducts.includes(id));

      if (allExpanded) {
        // 全部收起
        this._state.expandedPackageProducts = [];
      } else {
        // 全部展开
        this._state.expandedPackageProducts = [...allPackageProductIds];
      }
    }
  };

  public async init() {
    const orderId = this.route.query.orderId as string;
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      const response = await OrderApi.queryOpenOrder({
        sessionId: orderId
      });

      if (response.data) {
        // 转换API返回的数据以匹配OrderDetailData接口
        this._state.orderData = {
          roomVO: response.data.roomVO || {},
          sessionVO: response.data.sessionVO || {},
          orderRoomPlanVOS: response.data.orderRoomPlanVOS || [],
          orderVOS: response.data.orderVOS || [],
          payBillVOs: response.data.payBillVOs || [],
          payRecordVOs: response.data.payRecordVOs || [],
          inOrderProductInfos: (response.data.inOrderProductInfos || []).map((item: any) => ({
            ...item,
            totalAmount: item.payAmount || 0, // 确保totalAmount字段存在
            src: 'in' // 添加src字段
          })),
          outOrderProductInfos: (response.data.outOrderProductInfos || []).map((item: any) => ({
            ...item,
            totalAmount: item.payAmount || 0, // 确保totalAmount字段存在
            src: 'out' // 添加src字段
          }))
        };

        // 创建退款映射
        this._state.refundMap = this.createRefundMap();

        // 默认展开所有订单
        this._state.expandedOrders = this._state.orderData.orderVOS
          .filter(order => order.direction === 'normal') // 只展开普通订单，不展开退款订单
          .map(order => order.orderNo);

        // 根据配置决定是否默认展开套餐商品
        if (this._state.defaultExpandPackageProducts) {
          const packageProductIds: string[] = [];

          // 收集套餐内商品中的套餐商品ID
          this._state.orderData.inOrderProductInfos?.forEach(product => {
            if (product.packageProductInfo && product.packageProductInfo.trim()) {
              packageProductIds.push(product.id);
            }
          });

          // 收集套餐外商品中的套餐商品ID
          this._state.orderData.outOrderProductInfos?.forEach(product => {
            if (product.packageProductInfo && product.packageProductInfo.trim()) {
              packageProductIds.push(product.id);
            }
          });

          // 设置默认展开的套餐商品
          this._state.expandedPackageProducts = packageProductIds;
        }
      } else {
        throw new Error('未获取到订单数据');
      }
    } catch (error) {
      console.error('获取订单数据失败:', error);
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '获取订单数据失败',
        duration: 1000
      });
    }
  }
}

// 导出组合式函数
export function useRoomOrderDetail(): IRoomOrderDetailViewModel {
  const presenter = new RoomOrderDetailPresenter();
  // 使用类型断言解决readonly类型问题
  return presenter as IRoomOrderDetailViewModel;
}
