<template>
  <div class="h-full flex flex-col">
    <!-- 打印组件 -->
    <OrderDetailPrinter v-model:visible="vm.state.printDialogVisible" :session-id="String(route.query.orderId)" :data="vm.state.orderData" />
    <!-- 内容区 - 左右布局 -->
    <div class="flex gap-4 flex-1 overflow-auto">
      <!-- 左侧 - 消费明细(包厢基本信息) -->
      <div class="w-[630px] bg-white p-4">
        <!-- 顶部导航和标题 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center px-4 py-2 gap-4">
            <el-button class="flex items-center w-[108px] h-[56px]" @click="handleGoBack">
              <el-icon class="mr-1">
                <ArrowLeft />
              </el-icon>
              返回
            </el-button>
            <div class="flex flex-col">
              <h1 class="text-[24px] font-bold text-[#E9223A]">消费明细 / {{ vm.state.orderData?.roomVO?.name }}</h1>
              <div class="text-gray-700">{{ route.query.orderId }}</div>
            </div>
          </div>
          <div class="text-[24px]" :class="[vm.state.orderData?.sessionVO?.payStatus === 'paid' ? 'text-[#12AA58]' : 'text-[#E9223A]']">
            {{ vm.actions.getStatusText(vm.state.orderData?.sessionVO?.payStatus || '') }}
          </div>
        </div>

        <!-- 消费总计 -->
        <div class="text-center mb-[32px] mt-[64px] flex flex-col items-center justify-center">
          <div class="text-gray-500 text-[18px] mb-2">消费总计</div>
          <div class="price-64 mt-[16px]">
            <PriceDisplay :amount-in-fen="vm.computed.totalConsumptionAmount.value" />
          </div>
        </div>

        <!-- 包厢费用和商品费用 -->
        <div class="my-[52px] flex flex-row items-center justify-center">
          <!-- 包厢费用卡片 -->
          <div class="flex flex-col items-center justify-center">
            <div class="text-gray-500 text-[18px] mb-[16px]">包厢费用</div>
            <div class="text-[28px] font-bold">
              <PriceDisplay class="price-36" :amount-in-fen="vm.computed.roomFeeAmount.value" />
            </div>
          </div>
          <span class="text-[28px] text-[#CCC] mx-[56px]"> + </span>
          <!-- 商品费用卡片 -->
          <div class="flex flex-col items-center justify-center">
            <div class="text-gray-500 text-[18px] mb-2">商品费用</div>
            <div class="text-[28px] font-bold">
              <PriceDisplay class="price-36" :amount-in-fen="vm.computed.productFeeAmount.value" />
            </div>
          </div>
        </div>

        <div class="h-[1px] bg-gray-200"></div>

        <!-- 详细信息 -->
        <div class="mt-[32px]">
          <div class="px-[48px]">
            <div class="flex items-center py-2 justify-between">
              <span class="text-gray-500 w-[80px] text-[18px]">开台时段:</span>
              <span class="text-[18px] text-[#000] font-bold">{{
                formatUnixTimestampRangeToSimple(vm.state.orderData?.sessionVO?.startTime, vm.state.orderData?.sessionVO?.endTime)
              }}</span>
            </div>
            <div class="flex items-center py-2 justify-between">
              <span class="text-gray-500 w-[80px] text-[18px]">关台时间:</span>
              <span class="text-[18px] text-[#000] font-bold">{{ formatUnixTimestampToSimple(vm.state.orderData?.sessionVO?.closeTime) }}</span>
            </div>
            <div class="flex items-center py-2 justify-between">
              <span class="text-gray-500 w-[80px] text-[18px]">使用时长:</span>
              <span class="text-[18px] text-[#000] font-bold">{{ vm.computed.usageDuration.value }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧 - 账单区域 -->
      <div class="w-[1150px] bg-[#F5F5F5] overflow-y-auto px-[24px] pb-[48px] h-full">
        <!-- 标题栏 -->
        <div class="w-full h-[60px] flex items-center justify-center text-gray-500 text-sm">全部账单</div>

        <!-- 房费账单区域 -->
        <div class="bg-white rounded-lg shadow-md mb-6 border border-gray-100" v-if="vm.computed.roomPlanOrders.value.length > 0">
          <!-- 房费标题栏 -->
          <div class="px-4 py-3 border-b border-gray-100">
            <div class="flex items-center">
              <el-checkbox hidden v-model="vm.state.selectedRoomPlans.selectAll" @change="vm.actions.toggleSelectAllRoomPlans"></el-checkbox>
              <span class="ml-2 font-medium">包厢费</span>
            </div>
          </div>

          <!-- 使用el-table替换订单表格 -->
          <el-table :data="vm.computed.roomPlanOrders.value" style="width: 100%" default-expand-all class="custom-table">
            <el-table-column label="订单号" align="center" width="288px">
              <template #default="{ row }">
                <div class="flex items-center">
                  <el-checkbox hidden v-model="vm.state.selectedRoomPlans.orders" :value="row.orderNo" class="custom-checkbox" @click.stop></el-checkbox>
                  <span class="ml-[4px]">{{ row.orderNo }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="下单时间" align="right">
              <template #default="{ row }">
                {{ formatUnixTimestampToSimple(row.ctime) }}
              </template>
            </el-table-column>
            <el-table-column label="消费类型" align="right">
              <template #default="{}"> 包厢 </template>
            </el-table-column>
            <el-table-column label="支付金额" align="right">
              <template #default="{ row }">
                <PriceDisplay :amount-in-fen="row.payAmount || 0" class="price-display-right-align" />
              </template>
            </el-table-column>
            <el-table-column label="支付状态" align="right">
              <template #default="{ row }">
                <span v-if="row.refundStatus === 'full'" class="text-[#FF6B00]">已退</span>
                <span v-else :class="row.status === 'paid' ? 'text-[#12AA58]' : 'text-[#E9223A]'">
                  {{ row.status === 'paid' ? '已结' : '未结' }}
                </span>
                <div v-if="row.refundStatus === 'partial'" class="text-xs text-gray-400">(部分退款)</div>
              </template>
            </el-table-column>

            <!-- 展开详情列 -->
            <el-table-column type="expand">
              <template #default="scope">
                <div class="bg-white w-full py-[12px] pr-[68px] pl-[192px]">
                  <div
                    v-for="roomPlan in [...scope.row.roomPlans].sort((a, b) => a.startTime - b.startTime)"
                    :key="roomPlan.id"
                    class="grid grid-cols-[32%_12%_28%_28%] py-[4px] pr-[56px] gap-4">
                    <div align="right">包厢: {{ roomPlan.roomName }}</div>
                    <div v-if="roomPlan.isGift">赠送</div>
                    <div v-else>{{ roomPlan.pricePlanName }}</div>
                    <div align="right">
                      {{ formatUnixTimestampRangeToSimple(roomPlan.startTime, roomPlan.endTime) }}
                    </div>
                    <div class="text-right" align="right">消费金额: ¥{{ (roomPlan.payAmount / 100).toFixed(2) }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 套餐内商品区域 -->
        <div class="bg-white rounded-lg shadow-md mb-6 border border-gray-100" v-if="vm.computed.inPackageOrders.value.length > 0">
          <!-- 套餐标题栏 -->
          <div class="px-4 py-3 border-b border-gray-100">
            <div class="flex items-center">
              <el-checkbox hidden v-model="vm.state.selectedInPackageOrders.selectAll" @change="vm.actions.toggleSelectAllInPackageOrders"></el-checkbox>
              <span class="ml-2 font-medium">买断内商品</span>
            </div>
          </div>

          <!-- 使用el-table替换订单表格 -->
          <el-table :data="vm.computed.inPackageOrders.value" style="width: 100%" default-expand-all class="custom-table">
            <el-table-column label="订单号" width="288px" align="center">
              <template #default="{ row }">
                <div class="flex items-center">
                  <el-checkbox v-model="vm.state.selectedInPackageOrders.orders" :value="row.orderNo" class="hidden custom-checkbox" @click.stop></el-checkbox>
                  <span>{{ row.orderNo }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="下单时间" align="right">
              <template #default="{ row }">
                {{ formatUnixTimestampToSimple(row.ctime) }}
              </template>
            </el-table-column>
            <el-table-column label="消费类型" align="right">
              <template #default="{ row }">
                {{ row.type === 'product' ? '商品' : '包厢' }}
              </template>
            </el-table-column>
            <el-table-column label="消费金额" align="right">
              <template #default="{ row }">
                <PriceDisplay :amount-in-fen="row.payAmount || 0" class="price-display-right-align" />
              </template>
            </el-table-column>
            <el-table-column label="支付状态" align="right">
              <template #default="{ row }">
                <span v-if="row.refundStatus === 'full'" class="text-[#FF6B00]">已退</span>
                <span v-else :class="row.status === 'paid' ? 'text-[#12AA58]' : 'text-[#E9223A]'">
                  {{ row.status === 'paid' ? '已结' : '未结' }}
                </span>
                <div v-if="row.refundStatus === 'partial'" class="text-xs text-gray-400">(部分退款)</div>
              </template>
            </el-table-column>

            <!-- 展开详情列 -->
            <el-table-column type="expand">
              <template #default="scope">
                <div class="bg-white pr-[68px] pl-[264px]">
                  <div v-for="product in scope.row.products" :key="product.id" class="py-2 text-[14px] border-b border-gray-200 last:border-0">
                    <!-- 套餐商品主行 -->
                    <div
                      class="grid grid-cols-[40%_12%_20%_28%]"
                      :class="{ 'cursor-pointer hover:bg-gray-50': product.packageProductInfo }"
                      @click="product.packageProductInfo ? vm.actions.togglePackageProduct(product.id) : null">
                      <div class="flex items-center">
                        <!-- 套餐商品展开/收起按钮 -->
                        <el-icon v-if="product.packageProductInfo" class="mr-2 text-gray-400">
                          <ArrowDown v-if="!vm.computed.isPackageProductExpanded.value(product.id)" />
                          <ArrowUp v-else />
                        </el-icon>
                        <span v-if="product.isFree" class="ml-1 text-[#12AA58]">[免费]</span>
                        <span v-else-if="product.isGift" class="ml-1 text-[#12AA58]">[赠送]</span>
                        {{ product.productName }}
                      </div>
                      <span>数量: {{ product.quantity }}</span>
                      <span class="text-right">单价: ¥{{ (product.originalPrice / 100).toFixed(2) }}</span>
                      <span class="text-right">消费金额: ¥{{ (product.payAmount / 100).toFixed(2) }}</span>
                    </div>

                    <!-- 套餐商品明细（展开时显示） -->
                    <div v-if="product.packageProductInfo && vm.computed.isPackageProductExpanded.value(product.id)" class="mt-2 ml-4 bg-gray-50 p-3 rounded">
                      <div
                        v-for="packageProduct in vm.computed.getPackageProductDetails.value(product.packageProductInfo)"
                        :key="packageProduct.id"
                        class="grid grid-cols-[38%_14%_20%_28%] py-1 text-[14px] text-[#777]">
                        <span>{{ packageProduct.name }}</span>
                        <span>数量: {{ packageProduct.count }}</span>
                        <span class="text-right">原价: ¥{{ (packageProduct.price / 100).toFixed(2) }}</span>
                        <span class="text-right">小计: ¥{{ ((packageProduct.count * packageProduct.price) / 100).toFixed(2) }}</span>
                      </div>
                    </div>

                    <!-- 如果有退款，显示退款信息 -->
                    <div
                      v-if="product.refundProducts && product.refundProducts.length > 0"
                      v-for="refundItem in product.refundProducts"
                      :key="refundItem.id"
                      class="grid grid-cols-[40%_12%_20%_28%] py-1 text-[14px] text-[#E9223A]">
                      <div><span class="text-[#E9223A]">[退] </span>{{ refundItem.productName }}</div>
                      <span>数量: {{ refundItem.quantity }}</span>
                      <span class="text-right">单价: ¥{{ (refundItem.originalPrice / 100).toFixed(2) }}</span>
                      <span class="text-right">退款金额: -¥{{ (refundItem.payAmount / 100).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 套餐外商品区域 -->
        <div class="bg-white rounded-lg shadow-md border border-gray-100" v-if="vm.computed.outPackageOrders.value.length > 0">
          <!-- 标题栏 -->
          <div class="px-4 py-3 border-b border-gray-100">
            <div class="flex items-center">
              <el-checkbox hidden v-model="vm.state.selectedOutPackageOrders.selectAll" @change="vm.actions.toggleSelectAllOutPackageOrders"></el-checkbox>
              <span class="ml-2 font-medium">点单商品</span>
            </div>
          </div>

          <!-- 使用el-table替换订单表格 -->
          <el-table :data="vm.computed.outPackageOrders.value" style="width: 100%" default-expand-all class="custom-table">
            <el-table-column label="订单号" width="288px" align="center">
              <template #default="{ row }">
                <div class="flex items-center">
                  <el-checkbox hidden v-model="vm.state.selectedOutPackageOrders.orders" :value="row.orderNo" class="custom-checkbox" @click.stop></el-checkbox>
                  <span class="ml-2">{{ row.orderNo }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="下单时间" align="right">
              <template #default="{ row }">
                {{ formatUnixTimestampToSimple(row.ctime) }}
              </template>
            </el-table-column>
            <el-table-column label="消费类型" align="right">
              <template #default="{ row }">
                {{ row.type === 'product' ? '商品' : '包厢' }}
              </template>
            </el-table-column>
            <el-table-column label="消费金额" align="right">
              <template #default="{ row }">
                <PriceDisplay :amount-in-fen="row.payAmount || 0" class="price-display-right-align" />
              </template>
            </el-table-column>
            <el-table-column label="支付状态" align="right">
              <template #default="{ row }">
                <span v-if="row.refundStatus === 'full'" class="text-[#FF6B00]">已退</span>
                <span v-else :class="row.status === 'paid' ? 'text-[#12AA58]' : 'text-[#E9223A]'">
                  {{ row.status === 'paid' ? '已结' : '未结' }}
                </span>
                <div v-if="row.refundStatus === 'partial'" class="text-xs text-gray-400">(部分退款)</div>
              </template>
            </el-table-column>

            <!-- 展开详情列 -->
            <el-table-column type="expand">
              <template #default="scope">
                <div class="bg-white px-6 py-3 pl-[264px] pr-[68px]">
                  <div v-for="product in scope.row.products" :key="product.id" class="py-2 text-[14px] border-b border-gray-200 last:border-0">
                    <!-- 套餐商品主行 -->
                    <div
                      class="grid grid-cols-[40%_12%_20%_28%]"
                      :class="{ 'cursor-pointer hover:bg-gray-50': product.packageProductInfo }"
                      @click="product.packageProductInfo ? vm.actions.togglePackageProduct(product.id) : null">
                      <div class="flex items-center">
                        <el-icon v-if="product.packageProductInfo" class="mr-2 text-gray-400">
                          <ArrowDown v-if="!vm.computed.isPackageProductExpanded.value(product.id)" />
                          <ArrowUp v-else />
                        </el-icon>
                        <span v-if="product.isFree" class="ml-1 text-[#12AA58]">[免费]</span>
                        <span v-else-if="product.isGift" class="ml-1 text-[#12AA58]">[赠送]</span>
                        {{ product.productName }}
                      </div>
                      <span>数量: {{ product.quantity }}</span>
                      <span class="text-right">单价: ¥{{ (product.originalPrice / 100).toFixed(2) }}</span>
                      <span class="text-right">消费金额: ¥{{ (product.payAmount / 100).toFixed(2) }}</span>
                    </div>

                    <!-- 套餐商品明细（展开时显示） -->
                    <div v-if="product.packageProductInfo && vm.computed.isPackageProductExpanded.value(product.id)" class="mt-2 ml-4 bg-gray-50 p-3 rounded">
                      <div
                        v-for="packageProduct in vm.computed.getPackageProductDetails.value(product.packageProductInfo)"
                        :key="packageProduct.id"
                        class="grid grid-cols-[38%_14%_20%_28%] py-1 text-[14px] text-[#777]">
                        <span>{{ packageProduct.name }}</span>
                        <span>数量: {{ packageProduct.count }}</span>
                        <span class="text-right">原价: ¥{{ (packageProduct.price / 100).toFixed(2) }}</span>
                        <span class="text-right">小计: ¥{{ ((packageProduct.count * packageProduct.price) / 100).toFixed(2) }}</span>
                      </div>
                    </div>

                    <!-- 如果有退款，显示退款信息 -->
                    <div
                      v-if="product.refundProducts && product.refundProducts.length > 0"
                      v-for="refundItem in product.refundProducts"
                      :key="refundItem.id"
                      class="grid grid-cols-[40%_12%_20%_28%] py-1 text-[14px] text-[#E9223A]">
                      <div><span class="text-[#E9223A]">[退] </span>{{ refundItem.productName }}</div>
                      <span>数量: {{ refundItem.quantity }}</span>
                      <span class="text-right">单价: ¥{{ (refundItem.originalPrice / 100).toFixed(2) }}</span>
                      <span class="text-right">退款金额: -¥{{ (refundItem.payAmount / 100).toFixed(2) }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 无订单时的提示 -->
        <div
          v-if="
            vm.computed.roomPlanOrders.value.length === 0 && vm.computed.inPackageOrders.value.length === 0 && vm.computed.outPackageOrders.value.length === 0
          "
          class="flex items-center justify-center h-40 bg-white rounded-lg shadow-md border border-gray-100">
          <span class="text-gray-400">暂无账单数据</span>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="flex-shrink-0 flex flex-row w-full bg-white h-[120px] items-center border-t justify-between pr-[32px]">
      <div class="flex space-x-4 items-center gap-x-[24px]">
        <el-button class="hidden w-[170px] h-[64px]" @click="vm.actions.handleManageProduct">商品管理</el-button>
        <el-button class="w-[170px] h-[64px]" @click="vm.actions.handleShowPayDetail">结账详情</el-button>
        <el-button class="hidden w-[170px] h-[64px]" @click="vm.actions.handlePrint">
          <el-icon class="mr-1">
            <Printer />
          </el-icon>
          打印
        </el-button>
      </div>
      <div class="flex items-center gap-[24px]">
        <div class="flex items-end">
          <span class="text-[16px] text-[#999] mr-[8px] font-medium">待结合计: </span>
          <PriceDisplay :amount-in-fen="vm.state.orderData?.sessionVO?.unpaidAmount || 0" class="price-display-large" />
        </div>

        <!-- 根据是否计时显示按钮 -->
        <template v-if="vm.state.orderData?.sessionVO?.isTimeConsume">
          <el-button class="app-button" type="primary" @click="vm.actions.handleFinishTiming"> 结束计时 </el-button>
        </template>
        <template v-else>
          <el-button class="app-button" type="primary" :disabled="vm.state.orderData?.sessionVO?.unpaidAmount === 0" @click="vm.actions.handleSettlement">
            确认结账
          </el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useRoomOrderDetail } from './presenter';
import type { IRoomOrderDetailViewModel, OrderDisplay } from './viewmodel';
import OrderDetailPrinter from '../../components/OrderDetailPrinter.vue';
import { ArrowDown, ArrowLeft, ArrowUp, Printer, Warning, CircleCheckFilled } from '@element-plus/icons-vue';
import {
  formatUnixTimestamp,
  formatUnixTimestampToSimple,
  formatDuration,
  formatFullTimeRangeFromUnix,
  formatUnixTimestampRangeToSimple
} from '@/utils/dateUtils';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';

defineOptions({
  name: 'RoomOrderDetail'
});

const route = useRoute();
const router = useRouter();
const vm: IRoomOrderDetailViewModel = useRoomOrderDetail();

// 格式化时间范围
const formatTimeRangeFromUnix = (timestamp: number) => {
  // 这里只是示例，实际应该获取真实的时间范围数据
  // 通常从orderRoomPlanVOS中获取startTime和endTime
  const roomPlan = vm.state.orderData?.orderRoomPlanVOS?.find(plan => plan.ctime === timestamp);

  if (roomPlan?.startTime && roomPlan?.endTime) {
    const start = new Date(roomPlan.startTime * 1000);
    const end = new Date(roomPlan.endTime * 1000);
    const startStr = `${start.getHours().toString().padStart(2, '0')}:${start.getMinutes().toString().padStart(2, '0')}`;
    const endStr = `${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`;
    return `(${startStr}-${endStr})`;
  }

  return '(未知时段)';
};

// 安全的返回处理函数
const handleGoBack = () => {
  try {
    // 检查是否有历史记录可以返回
    if (window.history.length > 1) {
      router.back();
    } else {
      // 没有历史记录时，直接跳转到首页
      router.push('/');
    }
  } catch (error) {
    console.error('返回操作失败:', error);
    // 发生错误时，直接跳转到首页
    router.push('/');
  }
};

onMounted(() => {
  // 尝试获取订单详情数据
  (vm as any).init?.();
});
</script>

<style scoped>
.col-span-0 {
  display: flex;
  justify-content: left;
  font-size: 14px;
  color: #666;
}

.col-span-1 {
  display: flex;
  justify-content: right;
  font-size: 14px;
  color: #666;
}

.refund-info .col-span-1 {
  color: #f56c6c;
}

.col-span-2 {
  display: flex;
  justify-content: right;
  font-size: 14px;
  color: #999;
}

:deep(.price-64 .price-unit) {
  font-size: 32px;
  padding-right: 4px;
}

:deep(.price-64 .price-integer) {
  font-size: 64px;
}

:deep(.price-64 .price-decimal) {
  font-size: 32px;
}

:deep(.price-36 .price-unit) {
  font-size: 18px;
}

:deep(.price-36 .price-integer) {
  font-size: 36px;
}

:deep(.price-36 .price-decimal) {
  font-size: 18px;
}
</style>
