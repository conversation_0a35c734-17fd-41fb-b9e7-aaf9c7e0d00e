<template>
  <div class="tab-content">
    <div class="server-content">
      <div class="info-row">
        <span class="label">授权信息:</span>
        <div class="value-container">
          <div>{{ vm.state.serverInfo.license || '未知' }}</div>
        </div>
      </div>
      <div class="info-row">
        <span class="label">当前VOD服务器IP:</span>
        <div class="value-container">
          <div>{{ vm.state.serverInfo.currentIp || '未设置' }}</div>
        </div>
      </div>
      <!-- <div class="info-row">
        <span class="label">扫描:</span>
        <div class="scan-controls">
          <select v-model="vm.state.selectedIp">
            <option value="">-- 请选择 --</option>
            <option v-for="ip in vm.state.availableIps" :key="ip" :value="ip">
              {{ ip }}
            </option>
          </select>
          <el-button size="large" @click="vm.actions.scanServersIp">扫描</el-button>
        </div>
      </div> -->
      <div class="info-row">
        <span class="label">自定义:</span>
        <div class="custom-ip-container">
          <input type="text" class="custom-input" v-model="vm.state.customIp" placeholder="请输入IP地址，例如: *************" @input="validateIp" />
          <div v-if="customIpError" class="ip-error-message">{{ customIpError }}</div>
        </div>
      </div>
      <el-button
        class="btn-black !h-[64px] !w-[240px] mt-[24px] !rounded-[8px]"
        @click="saveServer"
        :loading="vm.state.isSaving"
        :disabled="(!vm.state.selectedIp && !vm.state.customIp) || !!customIpError">
        保存
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useVodSettings } from './presenter';
import type { IVodSettingsViewModel } from './viewmodel';
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const vm: IVodSettingsViewModel = useVodSettings();
const customIpError = ref('');

// 验证IP地址格式
const validateIp = () => {
  customIpError.value = '';

  if (!vm.state.customIp) {
    return true;
  }

  // 使用presenter中的validateIp方法
  if (!vm.actions.validateIp(vm.state.customIp)) {
    customIpError.value = '请输入有效的IP地址格式';
    return false;
  }

  return true;
};

// 优先使用自定义IP，其次使用选择的IP
const saveServer = () => {
  const serverIp = vm.state.customIp || vm.state.selectedIp;

  if (serverIp) {
    // 如果使用的是自定义IP，则验证格式
    if (vm.state.customIp && !validateIp()) {
      ElMessage.error('IP地址格式不正确，请修改后重试');
      return;
    }

    console.log('保存服务器IP:', serverIp);
    vm.actions.saveServerConfig(serverIp);
  }
};

// 使用生命周期钩子
vm.actions.initPageData('server');
</script>

<style scoped>
.tab-content {
  padding: 20px;
}

.server-content {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
}

.info-row {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  padding: 16px 0px;
}

.label {
  width: 142px;
  color: #666;
}

.value-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.value-container div {
  word-break: break-all;
  line-height: 1.5;
  font-weight: 500;
}

.scan-controls {
  display: flex;
  gap: 24px;
}

select,
.custom-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 240px;
}

.custom-input {
  width: 300px;
}

.custom-ip-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ip-error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}
</style>
