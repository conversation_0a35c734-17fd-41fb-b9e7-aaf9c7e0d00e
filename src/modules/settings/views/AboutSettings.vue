<template>
  <div class="about-container ml-[32px] mt-[32px]">
    <div class="about-content">
      <!-- 信息列表 -->
      <div class="info-list">
        <!-- 门店名称 -->
        <div class="info-item flex justify-between py-[4px]">
          <div class="text-gray-600">门店名称</div>
          <div class="text-right">{{ venueStore.venue?.name || '未设置' }}</div>
        </div>

        <!-- 设备编号 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">设备编号</div>
          <div class="text-right">{{ deviceStore.deviceId || '未设置' }}</div>
        </div>

        <!-- 操作系统版本 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">操作系统版本</div>
          <div class="text-right">{{ osVersion }}</div>
        </div>

        <!-- 应用版本 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">应用版本</div>
          <div class="text-right">{{ deviceStore.appVersion }} ({{ deviceStore.appVersionCode }})</div>
        </div>

        <!-- 客户端类型 (仅在开发模式显示) -->
        <div v-if="showDebugInfo" class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">客户端类型</div>
          <div class="text-right">{{ deviceStore.clientType }}</div>
        </div>

        <!-- 应用类型 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">设备类型</div>
          <div class="text-right">{{ deviceStore.deviceType }}</div>
        </div>

        <!-- IP 地址 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">IP 地址</div>
          <div class="text-right">{{ deviceStore.ipAddress || '未获取' }}</div>
        </div>

        <!-- MAC 地址 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">MAC 地址</div>
          <div class="text-right">{{ formattedMacAddress }}</div>
        </div>

        <!-- 序列号 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">序列号</div>
          <div class="text-right">{{ deviceStore.deviceId || '设备编号' }}</div>
        </div>

        <!-- 版权声明 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">版权声明</div>
          <div class="text-right">{{ copyright }}</div>
        </div>
      </div>

      <!-- 检查更新 -->
      <div class="mt-8 flex justify-center gap-4">
        <el-button class="btn-default" @click="checkUpdate" :loading="isChecking">检查更新</el-button>
        <el-button class="btn-light-gray" @click="restartApp">重启应用</el-button>
      </div>

    </div>
    
    <!-- 升级弹窗 -->
    <app-dialog
      v-model="upgradeDialogVisible"
      :title="upgradeDialogData.title"
      width="500px"
      :close-on-click-modal="!upgradeDialogData.forceUpgrade"
      :show-close="!upgradeDialogData.forceUpgrade"
      @confirm="handleUpgradeConfirm"
      @cancel="handleUpgradeCancel"
      :confirm-text="'立即升级'"
      :cancel-text="upgradeDialogData.forceUpgrade ? '' : '稍后升级'"
      :show-cancel="!upgradeDialogData.forceUpgrade">
      
      <div class="upgrade-dialog-content">
        <div class="version-info">
          <div class="version-row">
            <span class="label">当前版本：</span>
            <span class="value">{{ upgradeDialogData.currentVersion }}</span>
          </div>
          <div class="version-row">
            <span class="label">最新版本：</span>
            <span class="value latest">{{ upgradeDialogData.latestVersion }}</span>
          </div>
        </div>
        
        <div class="update-content">
          <div class="content-title">更新内容：</div>
          <div class="content-text">{{ upgradeDialogData.content }}</div>
        </div>
        
        <div v-if="upgradeDialogData.forceUpgrade" class="force-upgrade-notice">
          ⚠️ 此版本为强制升级，请立即更新
        </div>
      </div>
    </app-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { ElMessage } from 'element-plus';
import { checkAppUpgradeVersion, type GetAppUpgradeByVersionReqDto } from '@/api/upgradeVersion';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';

// 版本信息

const deviceStore = useDeviceStore();
const venueStore = useVenueStore();
const isChecking = ref(false);
const osVersion = ref('10.0.19045');
const copyright = ref('北京雷石天地电子技术股份有限公司');

// 开发模式显示调试信息
const showDebugInfo = ref(import.meta.env.DEV || window.location.hostname.includes('localhost'));

// 升级弹窗相关
const upgradeDialogVisible = ref(false);
const upgradeDialogData = ref({
  title: '发现新版本',
  currentVersion: '',
  latestVersion: '',
  content: '',
  forceUpgrade: false,
  latestVersionData: null as any
});

// 格式化MAC地址
const formattedMacAddress = computed(() => {
  if (!deviceStore.macAddress) return '未获取';
  return deviceStore.macAddress.toLowerCase();
});

// 获取系统信息
async function getSystemInfo() {
  try {
    // 如果需要从API获取更多系统信息,可以在这里实现
    console.log('获取系统信息');
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }
}

// 检查更新方法
async function checkUpdate() {
  try {
    isChecking.value = true;

    // 获取当前设备信息
    const deviceInfo = deviceStore.getDeviceInfo();
    
    // 检查必要的参数
    if (!deviceInfo.appVersionCode || !deviceInfo.clientType) {
      ElMessage.warning('无法获取版本信息，请重新启动应用');
      return;
    }

    // 使用壳应用传递的 clientType
    const clientType = deviceInfo.clientType;

    // 确定环境类型
    const environment = getEnvironmentType();

    // 构造请求参数
    const requestData: GetAppUpgradeByVersionReqDto = {
      clientType,
      environment,
      versionCode: parseInt(deviceInfo.appVersionCode)
    };

    console.log('检查版本升级请求:', requestData);

    // 调用API检查版本
    const response = await checkAppUpgradeVersion(requestData);

    const { hasUpgrade, currentVersion, latestVersion } = response;
    
    if (hasUpgrade && latestVersion) {
      // 显示升级弹窗
      showUpgradeDialog(currentVersion, latestVersion);
    } else {
      ElMessage.success('已是最新版本');
    }

  } catch (error) {
    console.error('检查更新失败:', error);
    
    // 处理不同的错误情况
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = error.message as string;
      if (errorMessage.includes('record not found')) {
        ElMessage.info('当前版本配置未找到，可能已经是最新版本');
      } else if (errorMessage.includes('Network Error') || errorMessage.includes('timeout')) {
        ElMessage.error('网络连接失败，请检查网络后重试');
      } else {
        ElMessage.error(`检查更新失败: ${errorMessage}`);
      }
    } else {
      ElMessage.error('检查更新失败，请稍后重试');
    }
  } finally {
    isChecking.value = false;
  }
}

// 获取环境类型
function getEnvironmentType(): string {
  const hostname = window.location.hostname;
  if (hostname.includes('test') || hostname.includes('dev')) {
    return 'TEST';
  } else if (hostname.includes('preview') || hostname.includes('staging')) {
    return 'PREVIEW';
  } else {
    return 'PRODUCTION';
  }
}

// 显示升级弹窗
function showUpgradeDialog(currentVersion: any, latestVersion: any) {
  // 清理升级内容中的 HTML 标签，只保留文本内容
  let cleanContent = latestVersion.upgradeContent || '版本升级';
  cleanContent = cleanContent.replace(/<[^>]*>/g, '\n'); // 移除HTML标签，替换为换行
  cleanContent = cleanContent.replace(/\n+/g, '\n'); // 合并多个换行
  cleanContent = cleanContent.trim();
  
  // 设置弹窗数据
  upgradeDialogData.value = {
    title: latestVersion.upgradeTitle || '发现新版本',
    currentVersion: currentVersion?.versionName || '未知',
    latestVersion: latestVersion.versionName,
    content: cleanContent,
    forceUpgrade: latestVersion.forceUpgrade || false,
    latestVersionData: latestVersion
  };
  
  // 显示弹窗
  upgradeDialogVisible.value = true;
}

// 处理升级确认
function handleUpgradeConfirm() {
  upgradeDialogVisible.value = false;
  startUpgrade(upgradeDialogData.value.latestVersionData);
}

// 处理升级取消
function handleUpgradeCancel() {
  upgradeDialogVisible.value = false;
  if (upgradeDialogData.value.forceUpgrade) {
    ElMessage.warning('强制升级版本，请完成升级后继续使用');
  }
}

// 开始升级
function startUpgrade(latestVersion: any) {
  console.log('开始升级:', latestVersion);
  
  try {
    // Android: 调用下载安装接口
    if (typeof window !== 'undefined' && (window as any).webViewBridge?.downloadAndInstallApk) {
      console.log('调用 Android 下载接口');
      
      // 显示下载提示
      ElMessage({
        message: '正在启动下载，请稍候...',
        type: 'info',
        duration: 3000
      });
      
      // 调用下载接口
      (window as any).webViewBridge.downloadAndInstallApk(latestVersion.downloadUrl, latestVersion.fileMd5);
      
      // 显示后续引导
      setTimeout(() => {
        ElMessage({
          message: '下载已开始，请查看下载进度通知',
          type: 'success',
          duration: 5000
        });
        
        setTimeout(() => {
          ElMessage({
            message: '下载完成后将自动弹出安装提示，请允许安装权限',
            type: 'warning',
            duration: 8000
          });
        }, 2000);
      }, 1000);
      
      return;
    }
    
    // Electron: 调用现有的升级接口
    if (typeof window !== 'undefined' && (window as any).appUpgradeBridge?.upgrade) {
      console.log('调用 Electron 升级接口');
      
      // 检查是否为开发环境
      const isDev = import.meta.env.DEV || window.location.hostname.includes('localhost');
      
      if (isDev) {
        ElMessage({
          message: '开发环境不支持 autoUpdater 升级，正在打开下载链接...',
          type: 'warning',
          duration: 5000
        });
        
        // 在开发环境中直接打开下载链接
        if (latestVersion.downloadUrl) {
          window.open(latestVersion.downloadUrl, '_blank');
          ElMessage({
            message: '请手动下载并安装升级包',
            type: 'info',
            duration: 8000
          });
        }
        return;
      }
      
      // 生产环境正常调用升级接口
      ElMessage({
        message: '正在检查更新服务器...',
        type: 'info',
        duration: 3000
      });
      
      // 设置升级状态监听
      setupElectronUpgradeListener();
      
      // 定义回调函数
      (window as any).onUpgradeResult = (result: any) => {
        console.log('升级结果:', result);
        if (result?.success) {
          ElMessage({
            message: '升级检查已启动，请等待下载完成',
            type: 'success',
            duration: 5000
          });
        } else {
          ElMessage.error('升级失败: ' + (result?.message || '未知错误'));
        }
        // 清理回调
        delete (window as any).onUpgradeResult;
      };
      
      (window as any).appUpgradeBridge.upgrade('onUpgradeResult');
      return;
    }
    
    // 备用方案：打开下载链接
    console.log('使用备用方案：打开下载链接');
    ElMessage.info('当前环境不支持自动升级，正在打开下载页面...');
    if (latestVersion.downloadUrl) {
      window.open(latestVersion.downloadUrl, '_blank');
      ElMessage({
        message: '请手动下载并安装升级包',
        type: 'warning',
        duration: 8000
      });
    }
  } catch (error) {
    console.error('启动升级失败:', error);
    ElMessage.error('启动升级失败，请手动下载升级包');
  }
}

// 设置 Electron 升级状态监听
function setupElectronUpgradeListener() {
  // 监听升级事件
  if (typeof window !== 'undefined' && (window as any).electronAPI?.on) {
    const { electronAPI } = window as any;
    
    // 监听升级开始
    electronAPI.on('upgrade-event', (event: any, data: any) => {
      console.log('收到升级事件:', event, data);
      
      switch (data.event) {
        case 'onUpgradeStarted':
          ElMessage({
            message: '开始下载更新包...',
            type: 'info',
            duration: 3000
          });
          break;
          
        case 'onDownloadProgress':
          if (data.data?.percent) {
            ElMessage({
              message: `下载进度: ${Math.round(data.data.percent)}%`,
              type: 'info',
              duration: 2000
            });
          }
          break;
          
        case 'onUpgradeComplete':
          ElMessage({
            message: '下载完成！请选择是否立即安装',
            type: 'success',
            duration: 5000
          });
          break;
          
        case 'onUpgradeFailed':
          ElMessage.error(`升级失败: ${data.data || '未知错误'}`);
          break;
      }
    });
  }
}

// 重启应用方法
function restartApp() {
  try {
    // 检查是否有webViewBridge可用
    if (typeof window !== 'undefined' && (window as any).webViewBridge) {
      (window as any).webViewBridge.restartApp();
    } else {
      ElMessage.warning('当前环境不支持重启应用');
    }
  } catch (error) {
    console.error('重启应用失败:', error);
    ElMessage.error('重启应用失败，请稍后重试');
  }
}


onMounted(() => {
  getSystemInfo();
});
</script>

<style scoped>
.upgrade-dialog-content {
  padding: 20px;
}

.version-info {
  margin-bottom: 20px;
}

.version-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.version-row .label {
  color: #606266;
  width: 80px;
}

.version-row .value {
  font-weight: 500;
}

.version-row .value.latest {
  color: #409eff;
}

.update-content {
  margin-bottom: 16px;
}

.content-title {
  color: #606266;
  margin-bottom: 8px;
}

.content-text {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  white-space: pre-line;
  color: #303133;
  line-height: 1.5;
}

.force-upgrade-notice {
  background: #fef0f0;
  color: #f56c6c;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #fbc4c4;
  font-weight: 500;
  text-align: center;
}
</style>

<style scoped>
.about-container {
  height: 100%;
}

.about-content {
  max-width: 600px;
}

.info-list {
  background: white;
  border-radius: 8px;
}

.info-item {
  height: 60px;
  align-items: center;
}
</style>
