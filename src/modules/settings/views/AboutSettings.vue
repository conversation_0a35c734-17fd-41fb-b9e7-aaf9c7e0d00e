<template>
  <div class="about-container ml-[32px] mt-[32px]">
    <div class="about-content">
      <!-- 信息列表 -->
      <div class="info-list">
        <!-- 门店名称 -->
        <div class="info-item flex justify-between py-[4px]">
          <div class="text-gray-600">门店名称</div>
          <div class="text-right">{{ venueStore.venue?.name || '未设置' }}</div>
        </div>

        <!-- 设备编号 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">设备编号</div>
          <div class="text-right">{{ deviceStore.deviceId || '未设置' }}</div>
        </div>

        <!-- 操作系统版本 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">操作系统版本</div>
          <div class="text-right">{{ osVersion }}</div>
        </div>

        <!-- 应用版本 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">应用版本</div>
          <div class="text-right">{{ deviceStore.appVersion }}</div>
        </div>

        <!-- 应用类型 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">设备类型</div>
          <div class="text-right">{{ deviceStore.deviceType }}</div>
        </div>

        <!-- IP 地址 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">IP 地址</div>
          <div class="text-right">{{ deviceStore.ipAddress || '未获取' }}</div>
        </div>

        <!-- MAC 地址 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">MAC 地址</div>
          <div class="text-right">{{ formattedMacAddress }}</div>
        </div>

        <!-- 序列号 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">序列号</div>
          <div class="text-right">{{ deviceStore.deviceId || '设备编号' }}</div>
        </div>

        <!-- 版权声明 -->
        <div class="info-item flex justify-between py-[4px] border-t border-gray-200">
          <div class="text-gray-600">版权声明</div>
          <div class="text-right">{{ copyright }}</div>
        </div>
      </div>

      <!-- 检查更新 -->
      <div class="mt-8 flex justify-center">
        <el-button type="primary" @click="checkUpdate" :loading="isChecking">检查更新</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { ElMessage } from 'element-plus';

// 版本信息

const deviceStore = useDeviceStore();
const venueStore = useVenueStore();
const isChecking = ref(false);
const osVersion = ref('10.0.19045');
const appType = ref('android');
const copyright = ref('北京雷石天地电子技术股份有限公司');

// 格式化MAC地址
const formattedMacAddress = computed(() => {
  if (!deviceStore.macAddress) return '未获取';
  return deviceStore.macAddress.toLowerCase();
});

// 获取系统信息
async function getSystemInfo() {
  try {
    // 如果需要从API获取更多系统信息,可以在这里实现
    console.log('获取系统信息');
  } catch (error) {
    console.error('获取系统信息失败:', error);
  }
}

// 检查更新方法
async function checkUpdate() {
  try {
    isChecking.value = true;

    // TODO: 调用实际的检查更新接口
    await new Promise(resolve => setTimeout(resolve, 1500));

    ElMessage.success('已是最新版本');
  } catch (error) {
    console.error('检查更新失败:', error);
    ElMessage.error('检查更新失败,请稍后重试');
  } finally {
    isChecking.value = false;
  }
}

onMounted(() => {
  getSystemInfo();
});
</script>

<style scoped>
.about-container {
  height: 100%;
}

.about-content {
  max-width: 600px;
}

.info-list {
  background: white;
  border-radius: 8px;
}

.info-item {
  height: 60px;
  align-items: center;
}
</style>
