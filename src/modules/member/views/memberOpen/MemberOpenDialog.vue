<template>
  <app-dialog v-model="visible" :title="title" @confirm="handleConfirm" @close="handleClose" @cancel="handleCancel">
    <div class="bg-white p-[12px]">
      <el-form ref="formRef" :model="vm.state" :rules="vm.formRules" label-position="top">
        <!-- 卡类型选择 -->
        <div class="mb-6 flex items-center">
          <span class="mr-4 text-gray-600">卡类型:</span>
          <el-radio-group v-model="vm.state.cardType" @change="vm.actions.setCardType" :disabled="!isFieldEditable.cardType">
            <el-radio :label="ConstCardType.PhysicalCard">实体卡</el-radio>
            <el-radio :label="ConstCardType.ElectronicCard">电子卡</el-radio>
          </el-radio-group>
        </div>

        <!-- 使用Grid布局替代Form -->
        <div class="grid grid-cols-2 gap-x-[24px] gap-y-[12px] pb-[12px]">
          <!-- 实体卡号 -->
          <div v-if="vm.computed.isPhysicalCard.value || props.isEdit" class="form-item">
            <el-form-item prop="cardNumber">
              <el-input class="form-input" v-bind="createModelBinding('cardNumber')" placeholder="请输入卡号" :disabled="props.isEdit" />
            </el-form-item>
          </div>

          <!-- 会员等级 -->
          <div class="form-item">
            <el-form-item prop="cardLevel">
              <el-select class="form-select" v-bind="createModelBinding('cardLevel')" placeholder="请选择等级" :disabled="!isFieldEditable.cardLevel">
                <el-option v-for="item in CardLevels" :key="item.slug" :label="item.cardLevelName" :value="item.slug" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 性别 -->
          <div class="form-item">
            <el-form-item prop="gender">
              <el-select class="form-select" v-bind="createModelBinding('gender')" :disabled="!isFieldEditable.gender">
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 手机号 -->
          <div class="form-item">
            <el-form-item prop="mobile">
              <el-input class="form-input" v-bind="createModelBinding('mobile')" placeholder="请输入手机号" :disabled="!isFieldEditable.mobile" />
            </el-form-item>
          </div>

          <!-- 姓名 -->
          <div class="form-item">
            <el-form-item prop="name">
              <el-input class="form-input" v-bind="createModelBinding('name')" placeholder="请输入姓名" :disabled="!isFieldEditable.name" />
            </el-form-item>
          </div>

          <!-- 生日 -->
          <div class="form-item">
            <el-form-item prop="birthday">
              <el-date-picker
                class="form-date-picker"
                v-bind="createModelBinding('birthday')"
                type="date"
                placeholder="请选择生日"
                :default-value="defaultBirthday"
                :disabled="!isFieldEditable.birthday" />
            </el-form-item>
          </div>
        </div>

        <!-- 会员卡有效期 -->
        <div class="mt-[24px] flex items-center">
          <!-- 销售员 -->
          <div class="w-[280px]">
            <div class="mb-[12px] text-gray-600 ml-[12px]">销售员</div>
            <el-form-item prop="salesperson">
              <el-select
                v-bind="createModelBinding('salesperson')"
                placeholder="请选择销售员"
                class="form-select"
                :loading="salespersonLoading"
                @change="handleSalespersonChange"
                :disabled="!isFieldEditable.salesperson">
                <el-option v-for="item in salespersonOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </div>

          <!-- 会员卡有效期区域 -->
          <div class="ml-[24px] flex-1">
            <!-- 开始和结束时间显示，放在上方 -->
            <div class="flex mb-[12px] text-sm ml-[12px]">
              <div class="flex-1">
                <span class="text-gray-500">有效期: </span>
                <span class="text-gray-700">{{ vm.computed.formattedStartTime.value }}</span>
              </div>
              <div class="flex-1">
                <span class="text-gray-500"> - </span>
                <span class="text-gray-700">{{ vm.computed.formattedEndTime.value }}</span>
              </div>
            </div>

            <!-- 有效期选择器 -->
            <el-form-item prop="validMonths">
              <el-select v-bind="createModelBinding('validMonths')" placeholder="请选择有效期" class="form-select" :disabled="!isFieldEditable.validMonths">
                <el-option v-for="month in 120" :key="month" :label="`${month}个月`" :value="month" />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 自定义底部按钮 -->
    <template #footer>
      <div class="flex justify-center gap-4 border-t pt-[24px]">
        <el-button class="btn-default" @click="handleDirectOpenCard">{{ props.isEdit ? '保存' : '直接开卡' }}</el-button>
        <!-- <el-button class="btn-default" @click="handleConfirm">充值开卡</el-button> -->
      </div>
    </template>
  </app-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import type { FormInstance } from 'element-plus';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { useMemberOpen } from './presenter';
import type { IMemberOpenViewModel, IMemberOpenState } from './viewmodel';
import { ConstCardType, CardLevels } from './entity';
import { useUserStore } from '@/stores/userStore';
import { useEmployeeList } from '@/composables/useEmployeeList';

// 使用用户存储
const userStore = useUserStore();

// 获取当前用户信息
const currentUser = computed(() => {
  const employee = userStore.userInfo.employee;
  return {
    id: employee?.id || '',
    name: employee?.name || userStore.userInfo.name || ''
  };
});

// 使用员工列表hooks
const {
  employees: salespersonOptions,
  loading: salespersonLoading,
  loadEmployees: loadSalespersonData,
  selectEmployee,
  selectedEmployee
} = useEmployeeList({
  autoLoad: true,
  reviewStatus: 1 // 只显示已审核的员工
});

// 监听员工列表变化，自动设置默认值
watch(
  salespersonOptions,
  (newEmployees) => {
    if (newEmployees.length > 0 && visible.value && !vm.state.salesperson) {
      initializeDefaultSalesperson();
    }
  },
  { immediate: true }
);

// 定义 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '开卡'
  },
  width: {
    type: String,
    default: '900px'
  },
  data: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editMode: {
    type: String,
    default: 'all' // 'all', 'basicInfo', 'cardLevel', 'validMonths'
  }
});

// 定义事件
const emit = defineEmits(['confirm', 'cancel', 'close', 'update']);

// 表单引用
const formRef = ref<FormInstance>();
// 控制变量
const visible = ref(props.visible);
// 计算标题，根据是否为编辑模式
const title = computed(() => props.title || (props.isEdit ? '编辑会员' : '开卡'));
// 使用原有的视图模型
const vm: IMemberOpenViewModel = useMemberOpen();
// 结果数据
const result = ref({});

// 绑定数据模型
const createModelBinding = (field: keyof IMemberOpenState) => ({
  'model-value': vm.state[field],
  'onUpdate:model-value': (value: any) => vm.actions.updateState(field, value)
});

// 计算18年前的今天作为默认生日
const defaultBirthday = computed(() => {
  const today = new Date();
  return new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
});

// 添加计算属性控制字段可编辑性
const isFieldEditable = computed(() => {
  const mode = props.editMode;
  // 默认所有字段不可编辑
  const editableFields: Record<string, boolean> = {
    cardType: false,
    cardLevel: false,
    gender: false,
    mobile: false,
    name: false,
    birthday: false,
    salesperson: false,
    validMonths: false
  };
  // 根据编辑模式启用特定字段
  if (mode === 'all') {
    // 全部可编辑
    Object.keys(editableFields).forEach(key => {
      editableFields[key] = true;
    });
  } else if (mode === 'basicInfo') {
    // 只允许编辑姓名，手机号，性别，生日
    editableFields.name = true;
    editableFields.mobile = true;
    editableFields.gender = true;
    editableFields.birthday = true;
  } else if (mode === 'cardLevel') {
    // 只允许编辑卡类型和等级
    editableFields.cardType = true;
    editableFields.cardLevel = true;
  } else if (mode === 'validMonths') {
    // 只允许编辑有效期月份
    editableFields.validMonths = true;
  }

  return editableFields;
});

// 确认按钮处理
const handleConfirm = async () => {
  try {
    await vm.actions.submitForm(formRef.value);
    // 成功开卡后，发送确认事件并带上结果数据
    const cardInfo = {
      cardId: vm.state.cardId,
      cardNumber: vm.state.cardNumber,
      cardType: vm.state.cardType,
      cardLevel: vm.state.cardLevel,
      cardLevelName: vm.state.cardLevelName,
      amount: vm.state.amount,
      mobile: vm.state.mobile,
      name: vm.state.name,
      gender: vm.state.gender,
      birthday: vm.state.birthday,
      salesperson: vm.state.salesperson,
      cardStartTime: vm.state.cardStartTime,
      validMonths: vm.state.validMonths,
      cardEndTime: vm.state.cardEndTime
    };
    emit('confirm', cardInfo);
    // 关闭对话框
    visible.value = false;
  } catch (error) {
    // 提交失败，不关闭对话框
    console.error('开卡失败:', error);
  }
};

// 取消按钮处理
const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

// 关闭处理
const handleClose = () => {
  emit('close');
};

// 初始化默认销售员为当前用户（如果在员工列表中）
const initializeDefaultSalesperson = () => {
  const current = currentUser.value;
  if (current.id) {
    // 检查当前用户是否在员工列表中
    const foundEmployee = salespersonOptions.value.find((emp: any) => emp.id === current.id);
    if (foundEmployee) {
      selectEmployee(foundEmployee);
      vm.actions.updateState('salesperson', foundEmployee.id);
      vm.actions.updateState('salespersonName', foundEmployee.name);
    }
  }
};

// 销售员选择变更处理
const handleSalespersonChange = (value: string) => {
  const employee = salespersonOptions.value.find(e => e.id === value);
  if (employee) {
    selectEmployee(employee);
    vm.actions.updateState('salesperson', employee.id);
    vm.actions.updateState('salespersonName', employee.name);
  }
};

// 监听对话框显示状态
watch(visible, async (val) => {
  if (val) {
    // 加载销售员数据
    await loadSalespersonData();
    
    // 设置默认销售员
    initializeDefaultSalesperson();

    // 设置生日默认值为18年前的今天
    vm.actions.updateState('birthday', defaultBirthday.value.toISOString().split('T')[0]);
  }
});

// 直接开卡处理
const handleDirectOpenCard = async () => {
  try {
    const cardInfo = await vm.actions.directOpenCard(formRef.value);
    if (cardInfo) {
      emit('confirm', cardInfo);
      visible.value = false;
    }
  } catch (error) {
    console.error('开卡失败:', error);
  }
};

// 监听data变化，用于编辑模式
watch(
  () => props.data,
  newData => {
    console.log('[memberOpen] 监听data变化:', newData);
    if (props.isEdit && newData && Object.keys(newData).length > 0) {
      // 如果是编辑模式且有数据，则初始化表单
      vm.actions.initializeWithMemberData({
        ...newData,
        editMode: props.editMode
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.form-item {
  @apply flex flex-col;
}

:deep(.form-select) {
  .el-select__wrapper {
    border-radius: 10px;
    height: 68px;
    padding: 0 20px;
    width: 100%;
  }
}

:deep(.form-input) {
  .el-input__wrapper {
    border-radius: 10px;
    height: 68px;
    padding: 0 20px;
    width: 100%;
  }
}

:deep(.form-date-picker) {
  width: 100%;
  .el-input__wrapper {
    border-radius: 10px;
    height: 68px;
    padding: 0 20px;
  }
}
</style>
