/**
 * 会员办理记录业务交互层
 */

import { MemberOperationListQueryParams } from './viewmodel';
import { MemberVO } from '@/api/autoGenerated/shared/types/member';
import { memberApi } from '@/modules/member/api/member';

// 定义一个mock的store替代实际的store
const mockStore = {
  currentVenue: {
    id: '123456'
  }
};

export class MemberOperationListInteractor {
  private store = mockStore;

  /**
   * 获取会员办理记录列表
   * @param params 查询参数
   * @returns 会员列表和总数
   */
  async getMemberOperationList(params: MemberOperationListQueryParams): Promise<{
    list: MemberVO[];
  }> {
    try {
      // 确保venueId存在
      if (!params.venueId) {
        params.venueId = this.store.currentVenue?.id || '';
      }

      // 调用实际API
      const response = await memberApi.getMemberOperationList({
        venueId: params.venueId,
        keyword: params.keyword,
        optionType: params.optionType,
        startTime: params.startTime,
        endTime: params.endTime
      });
      // 处理API返回结果
      if (response.code === 0 && response.data) {
        return {
          list: response.data || []
        };
      }

      // 如果API返回错误，返回空结果
      return {
        list: []
      };
    } catch (error) {
      console.error('获取会员办理记录失败', error);
      return {
        list: []
      };
    }
  }
}
