/**
 * 会员办理记录数据转换器
 */

import { MemberOperationRecordItem } from './viewmodel';
import { MemberVO } from '@/api/autoGenerated/shared/types/member';

export class MemberOperationListConverter {
  /**
   * 将API会员数据转换为视图数据
   * @param apiData API返回的会员数据
   * @returns 转换后的视图数据
   */
  static convertToViewData(apiData: any[]): MemberOperationRecordItem[] {
    if (!apiData || !Array.isArray(apiData) || !apiData.length) {
      return [];
    }

    return apiData
      .filter(item => !!item.id)
      .map(item => ({
        id: item.id,
        cardNumber: item.cardNumber || '',
        cardLevelName: item.cardLevelName || '',
        cardName: item.cardName || '',
        cardPhone: item.cardPhone || '',
        formatePhone: item.cardPhone ? item.cardPhone.replace(/(\d{5})\d{4}(\d{2})/, '$1****$2') : '',
        amount: this.parseNumber(item.totalFee || 0),
        receivedAmount: this.parseNumber(item.principalAmount || 0),
        operatorName: item.operatorName,
        operationTime: item.ctime,
        optionType: item.info || '',
        sellerName: item.sellerName || ''
      }));
  }

  /**
   * 解析数字，确保返回数值类型
   * @param value 要解析的值
   * @returns 解析后的数值
   */
  private static parseNumber(value: any): number {
    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }

    return 0;
  }
}
