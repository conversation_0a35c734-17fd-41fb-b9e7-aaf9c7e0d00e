/**
 * 会员办理记录列表视图模型接口
 */

import { MemberVO } from '@/api/autoGenerated/shared/types/member';
import { Ref, ComputedRef } from 'vue';

/**
 * 会员办理记录视图数据项
 */
export interface MemberOperationRecordItem {
  id: string;
  cardNumber: string;
  cardLevelName: string;
  cardName: string;
  cardPhone: string;
  formatePhone?: string;
  amount: number;
  receivedAmount: number;
  operatorName: string;
  sellerName: string;
  operationTime: number;
  optionType?: string; // 操作类型
}

/**
 * 操作类型选项
 */
export const OPERATION_TYPE_OPTIONS = [
  { label: '全部', value: '' },
  { label: '开卡', value: '开卡' },
  { label: '充值', value: '充值' },
  { label: '续卡', value: '续卡' },
  { label: '冻结', value: '冻结' },
  { label: '解冻', value: '解冻' }
];

/**
 * 会员办理记录列表查询参数
 */
export interface MemberOperationListQueryParams {
  venueId: string;
  keyword?: string; // 统一关键字查询：卡号/姓名/手机号
  optionType?: string; // 操作类型
  startTime?: number;
  endTime?: number;
}

/**
 * 会员办理记录列表视图模型接口
 */
export interface IMemberOperationListViewModel {
  // 列表加载状态
  loading: Ref<boolean>;

  // 列表数据
  tableData: ComputedRef<MemberOperationRecordItem[]>;

  // 查询参数
  queryParams: MemberOperationListQueryParams;

  // 操作类型选项
  operationTypeOptions: typeof OPERATION_TYPE_OPTIONS;

  // 搜索方法
  handleSearch: () => void;

  // 重置搜索方法
  handleReset: () => void;
}
