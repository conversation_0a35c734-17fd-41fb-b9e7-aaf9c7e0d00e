/**
 * 会员办理记录列表状态管理
 */

import { ref, reactive, onMounted, computed } from 'vue';
import { IMemberOperationListViewModel, MemberOperationListQueryParams, MemberOperationRecordItem, OPERATION_TYPE_OPTIONS } from './viewmodel';
import { MemberOperationListInteractor } from './interactor';
import { MemberOperationListConverter } from './converter';

export function useMemberOperationListPresenter(): IMemberOperationListViewModel {
  // 创建interactor实例
  const interactor = new MemberOperationListInteractor();

  // 列表加载状态
  const loading = ref(false);

  // 原始数据列表
  const originalData = ref<MemberOperationRecordItem[]>([]);

  // 过滤后的数据列表 - 使用computed确保响应式更新
  const tableData = computed(() => {
    // 如果没有关键词和操作类型，返回全部数据
    if (!queryParams.keyword && !queryParams.optionType) {
      return originalData.value;
    }

    return originalData.value.filter(item => {
      // 关键词过滤 (卡号、姓名或手机号)
      const keywordMatch =
        !queryParams.keyword ||
        item.cardNumber.toLowerCase().includes(queryParams.keyword.toLowerCase()) ||
        item.cardName.toLowerCase().includes(queryParams.keyword.toLowerCase()) ||
        (item.cardPhone && item.cardPhone.includes(queryParams.keyword));

      // 操作类型过滤
      const typeMatch = !queryParams.optionType || item.optionType === queryParams.optionType;

      // 同时满足关键词和类型筛选条件
      return keywordMatch && typeMatch;
    });
  });

  // 查询参数
  const queryParams = reactive<MemberOperationListQueryParams>({
    venueId: '',
    keyword: '',
    optionType: ''
  });

  /**
   * 加载会员办理记录列表
   */
  const loadMemberOperationList = async () => {
    try {
      loading.value = true;

      // 调用interactor获取数据 - 不传递筛选参数，获取所有数据
      const result = await interactor.getMemberOperationList({
        venueId: queryParams.venueId
      });

      // 转换数据并保存到原始数据中
      originalData.value = MemberOperationListConverter.convertToViewData(result.list);
      console.log('[operation-list] 会员办理记录加载成功', originalData.value);
    } catch (error) {
      console.error('加载会员办理记录失败', error);
      // 出错时确保tableData仍为数组
      originalData.value = [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * 搜索处理 - 本地搜索，不需要重新加载数据
   */
  const handleSearch = () => {
    // 本地过滤，不需要额外操作，computed属性会自动更新
    console.log('执行本地搜索，关键词:', queryParams.keyword, '操作类型:', queryParams.optionType);
  };

  /**
   * 重置搜索
   */
  const handleReset = () => {
    // 重置查询参数（保留venueId）
    const venueId = queryParams.venueId;
    Object.assign(queryParams, {
      venueId,
      keyword: '',
      optionType: '',
      startTime: undefined,
      endTime: undefined
    });
    // 本地过滤，不需要重新加载数据
  };

  // 在组件挂载时自动加载数据
  onMounted(() => {
    console.log('会员办理记录组件已挂载，开始加载数据');
    loadMemberOperationList();
  });

  // 直接返回响应式对象，不解构它们的 .value
  return {
    loading,
    tableData,
    queryParams,
    operationTypeOptions: OPERATION_TYPE_OPTIONS,
    handleSearch,
    handleReset
  };
}
