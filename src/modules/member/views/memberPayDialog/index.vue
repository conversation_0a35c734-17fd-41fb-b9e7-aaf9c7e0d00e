<template>
  <div>
    <AppDialog
      v-model="dialogVisible"
      :closable="true"
      :showFooter="false"
      @open="vm.actions.onDialogOpen"
      @close="handleClose"
      class="!w-[1126px]"
      :showHeader="false">
      <!-- 对话框内容 -->
      <div class="flex h-full">
        <!-- 左侧会员信息 -->
        <div class="order-info-container flex flex-col">
          <!-- 会员信息头部 -->
          <div class="order-header mb-[24px] flex-shrink-0">
            <h2 class="title pl-[12px]">会员充值</h2>
            <div class="order-no mr-[32px]">会员卡号：{{ vm.state.memberInfo.cardNo || '-' }}</div>
          </div>
          <!-- 会员信息 -->
          <div class="h-[92px] flex flex-col mb-[24px] mt-[24px]" v-if="vm.state.memberInfo">
            <h3 class="text-[16px] text-[#000] flex flex-1 ml-[16px]">会员信息</h3>
            <div class="flex flex-row">
              <div class="w-[160px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">卡类型</span>
                <span class="text-[#000] font-bold">{{ vm.computed.cardType.value || '-' }}</span>
              </div>
              <div class="w-[160px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">级别</span>
                <span class="text-[#000] font-bold">{{ vm.state.memberInfo.levelName || '-' }}</span>
              </div>
              <div class="w-[160px] ml-[20px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">电话/卡号</span>
                <span class="text-[#000] font-bold">{{ vm.state.memberInfo.phone || vm.state.memberInfo.cardNo || '-' }}</span>
              </div>
              <div class="w-[160px] ml-[20px] flex flex-col items-center justify-center">
                <span class="text-[14px] text-[#333]">余额</span>
                <span class="text-[#000] font-bold">
                  <PriceDisplay :amount-in-fen="vm.state.memberInfo.totalBalance" class="member-balance-price" />
                </span>
              </div>
            </div>
          </div>
          <!-- 中间可滚动区域 -->
          <div class="flex-1 overflow-y-auto pr-2">
            <!-- 充值信息 -->
            <div class="p-[16px] mb-5">
              <div>
                <div class="recharge-inputs">
                  <div class="border-b-[1px] border-[#EBEBEB] pb-[24px]">
                    <div class="label">充值</div>
                    <div class="input-container mt-[8px]">
                      <el-input
                        v-model="vm.state.totalFee"
                        class="customer-input"
                        align="right"
                        @input="vm.actions.handleTotalFeeInput"
                        @blur="vm.actions.handleTotalFeeBlur"
                        @click="$event.target.select()">
                      </el-input>
                    </div>
                  </div>

                  <div class="rounded-[8px] mt-[24px]">
                    <div class="label">赠送</div>
                    <div class="bonus-item mt-[12px]">
                      <div class="text-[16px] text-[#333] mr-[12px]">通用赠金</div>
                      <div class="value">
                        <el-input v-model="vm.state.commonBonusAmount" class="customer-input-small" align="right" @click="$event.target.select()"> </el-input>
                      </div>
                    </div>
                    <div class="bonus-item">
                      <div class="text-[16px] text-[#333] mr-[12px]">房间赠金</div>
                      <div class="value">
                        <el-input v-model="vm.state.roomBonusAmount" class="customer-input-small" align="right" @click="$event.target.select()"> </el-input>
                      </div>
                    </div>
                    <div class="bonus-item">
                      <div class="text-[16px] text-[#333] mr-[12px]">商品赠金</div>
                      <div class="value">
                        <el-input v-model="vm.state.goodsBonusAmount" class="customer-input-small" align="right" @click="$event.target.select()"> </el-input>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 费用汇总 - 不参与滚动 -->
          <div class="h-[72px] mt-auto flex-shrink-0 flex flex-row items-end justify-end mr-[32px]">
            <div class="ml-[20px] flex flex-row items-baseline">
              <span>应收：</span>
              <PriceDisplay :amount-in-fen="vm.computed.actualAmountInFen.value || 0" class="custom-price-display" />
            </div>
          </div>
        </div>

        <!-- 右侧支付区域 -->
        <div class="payment-container flex flex-col">
          <!-- 中间可滚动区域 -->
          <div class="flex-1 overflow-y-auto">
            <!-- 支付方式选择 -->
            <div class="mb-[36px]">
              <h3 class="text-[18px] text-[#000] flex flex-1 mb-[16px]">支付方式</h3>

              <!-- 充值金额为0时的提示 -->
              <div v-if="!canSelectPaymentMethod" class="text-warning border border-dashed border-[#E6A23C] rounded-[8px] p-[16px] mb-[16px] bg-[#FDF6EC]">
                <div class="flex items-center">
                  <i class="el-icon-warning-filled mr-2 text-[#E6A23C] text-xl"></i>
                  <span>请先输入有效的充值金额再选择支付方式</span>
                </div>
              </div>

              <div class="payment-method-grid">
                <div
                  v-for="method in vm.computed.paymentMethods.value"
                  :key="method.value"
                  class="payment-method-item h-[60px] bg-white rounded-[8px] flex flex-row justify-between items-center px-[16px]"
                  :class="{
                    active: vm.state.selectedPayMethod === method.value,
                    'opacity-50 cursor-not-allowed': !canSelectPaymentMethod
                  }"
                  @click="handlePayMethodClick(method.value)">
                  <div class="flex items-center gap-[12px]">
                    <img :src="getPaymentIcon(method.value)" class="w-[24px] h-[24px]" :alt="method.label" />
                    <div class="text-[16px] text-[#000] font-bold">{{ method.label }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 支付输入区域 -->
            <div class="">
              <!-- 扫码支付 -->
              <template v-if="vm.state.selectedPayMethod === PaymentMethodType.Scanner">
                <div class="payment-input-row">
                  <div class="label">支付金额：</div>
                  <div class="input-container">
                    <PriceDisplay :amount-in-fen="vm.computed.actualAmountInFen.value || 0" class="price-display-large price-display-danger" />
                  </div>
                </div>
                <div class="scanner-container">
                  <!-- 根据扫码弹窗状态显示不同内容 -->
                  <template v-if="vm.state.scannerDialogVisible">
                    <div class="scanner-hint">扫码窗口已打开，请使用扫码枪扫描付款码</div>
                    <div class="scanner-status">
                      <span class="text-[16px] text-[#999] font-bold">等待扫码...</span>
                    </div>
                  </template>
                  <template v-else>
                    <div class="scanner-hint">点击"确认充值"按钮将打开扫码窗口</div>
                    <div class="scanner-status">
                      <span class="text-[16px] text-[#666] font-bold">扫码支付已选择</span>
                    </div>
                  </template>
                </div>
              </template>

              <!-- 记账支付 -->
              <template
                v-else-if="
                  vm.state.selectedPayMethod === PaymentMethodType.Cash ||
                  vm.state.selectedPayMethod === PaymentMethodType.Bank ||
                  vm.state.selectedPayMethod === PaymentMethodType.AliPay ||
                  vm.state.selectedPayMethod === PaymentMethodType.WeChat
                ">
                <div class="payment-input-row">
                  <div class="label">支付金额：</div>
                  <div class="input-container">
                    <el-input v-model="vm.state.actualAmount" class="amount-input" align="right" :readonly="true" @click="$event.target.select()"> </el-input>
                  </div>
                </div>
                <div class="payment-input-row" v-if="Number(vm.computed.calculateChangeAmount.value || 0) > 0">
                  <div class="label">找零金额：</div>
                  <div class="input-container">
                    <PriceDisplay :amount-in-fen="Number(vm.computed.calculateChangeAmount.value || 0)" class="price-display-right-align change-amount-price" />
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 底部工具栏 - 不参与滚动 -->
          <div class="payment-toolbar mt-auto flex-shrink-0 mr-p">
            <div class="toolbar-right flex items-center gap-[24px]">
              <div class="flex items-end">
                <div class="total-label">充值金额：</div>
                <div class="total-value">
                  <PriceDisplay :amount-in-fen="vm.computed.actualAmountInFen.value || 0" class="price-display-large" />
                </div>
              </div>
              <el-button @click="vm.actions.handlePay" :loading="vm.state.loading" :disabled="!canPay" class="app-button"> 确认充值 </el-button>
            </div>
          </div>
        </div>
      </div>
    </AppDialog>

    <!-- 扫码对话框 -->
    <AppDialog
      v-model="vm.state.scannerDialogVisible"
      title="扫码支付"
      :show-close="true"
      :show-footer="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="!w-[664px] !h-[496px] pb-[12px]"
      @close="handleScannerDialogClose">
      <div class="flex flex-col items-center justify-end h-full">
        <PriceDisplay :amount-in-fen="vm.computed.actualAmountInFen.value || 0" class="price-display-56 price-display-primary mb-[24px]" />
        <div class="flex flex-col items-center justify-center">
          <img src="@/assets/resource/scan-barcode.png" alt="扫码支付" class="w-[320px]" />
        </div>
      </div>
      <template #footer>
        <div></div>
      </template>
    </AppDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, defineProps, defineEmits, reactive } from 'vue';
import { useMemberPayDialog } from './presenter';
import { convertToYuan } from '@/utils/priceUtils';
import { IMemberPayDialogViewModel, MEMBER_PAY_DIALOG_STATE_DEFAULTS } from './viewmodel';
import { PaymentMethodType } from './memberPayEntity';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { ElMessage } from 'element-plus';

// 导入支付图标
import cashIcon from '@/assets/payicons/现金.svg';
import wechatIcon from '@/assets/payicons/微信.svg';
import alipayIcon from '@/assets/payicons/支付宝.svg';
import memberIcon from '@/assets/payicons/会员卡.svg';
import cardIcon from '@/assets/payicons/银行卡.svg';
import scannerIcon from '@/assets/payicons/扫码枪.svg';

// 获取视图模型
let vm: any = null;
try {
  vm = useMemberPayDialog();
} catch (error) {
  console.error('初始化MemberPayDialog失败:', error);
  // 创建一个最小可用的组件
  vm = {
    state: reactive({
      ...MEMBER_PAY_DIALOG_STATE_DEFAULTS,
      visible: false,
      loading: false
    }),
    actions: {
      onDialogOpen: () => {},
      onDialogClose: () => {},
      init: async () => {},
      handlePay: async () => {},
      handleCancel: () => {}
    },
    computed: {
      actualAmountInFen: computed(() => 0),
      calculateChangeAmount: computed(() => 0),
      paymentMethods: computed(() => [])
    }
  };
}

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  memberInfo: {
    type: Object,
    default: () => ({})
  }
});

// 定义emits
const emit = defineEmits(['update:modelValue', 'paySuccess', 'payCancel', 'error', 'close']);

// 创建一个内部状态
const dialogVisibleState = ref(props.modelValue || props.visible);

// 计算属性：对话框可见性
const dialogVisible = computed<boolean>({
  get: () => dialogVisibleState.value,
  set: val => {
    dialogVisibleState.value = val;
    emit('update:modelValue', val);
  }
});

// 监听props变化更新内部状态
watch(
  () => [props.modelValue, props.visible],
  ([modelValue, visible]) => {
    dialogVisibleState.value = modelValue || visible;
  }
);

// 监听参数变化，初始化数据
watch(
  () => [dialogVisibleState.value, props.memberInfo],
  async ([visible, memberInfo]) => {
    console.log('[MemberPayDialog] 监听参数变化:', {
      visible,
      memberInfo
    });

    if (visible && !vm.state.isInitialized && memberInfo) {
      try {
        await vm.actions.init(memberInfo);
      } catch (error) {
        console.error('初始化失败:', error);
        emit('error', error);
      }
    }
  },
  { immediate: true }
);

// 支付成功处理
const handleSuccess = (result: any) => {
  emit('paySuccess', result);
  dialogVisibleState.value = false;
};

// 取消支付处理
const handleCancel = () => {
  emit('payCancel');
  dialogVisibleState.value = false;
};

// 处理主对话框关闭
const handleClose = () => {
  emit('close');
};

// 扩展actions，添加处理方法
vm.actions.registerPaySuccessHandler(handleSuccess);
vm.actions.registerPayCancelHandler(handleCancel);

// 添加支付按钮启用/禁用的计算属性
const canPay = computed(() => {
  // 如果没有选择支付方式，则禁用
  if (!vm.state.selectedPayMethod) {
    return false;
  }

  // 如果充值金额为0或无效，则禁用
  const totalFee = parseFloat(vm.state.totalFee || '0');
  if (isNaN(totalFee) || totalFee <= 0) {
    return false;
  }

  // 如果是现金支付，需要验证输入金额
  if (vm.state.selectedPayMethod === 'cash') {
    const inputAmount = parseFloat(vm.state.actualAmount || '0');
    return !isNaN(inputAmount) && inputAmount > 0;
  }

  // 其他情况，只要选择了支付方式且充值金额有效就可以支付
  return true;
});

// 添加支付方式是否可选择的计算属性
const canSelectPaymentMethod = computed(() => {
  const totalFee = parseFloat(vm.state.totalFee || '0');
  return !isNaN(totalFee) && totalFee > 0;
});

// 处理支付方式点击
const handlePayMethodClick = (methodValue: string) => {
  // 如果充值金额为0或无效，禁止选择支付方式
  if (!canSelectPaymentMethod.value) {
    ElMessage.warning('请先输入有效的充值金额');
    return;
  }
  vm.actions.selectPayMethod(methodValue);
};

// 处理扫码弹窗关闭
const handleScannerDialogClose = () => {
  console.log('[MemberPayDialog Vue] 用户手动关闭扫码弹窗');
  vm.actions.onScannerDialogClose();
};

// 修改获取支付图标的方法
const getPaymentIcon = (method: string) => {
  switch (method) {
    case PaymentMethodType.Cash:
      return cashIcon;
    case PaymentMethodType.Bank:
      return cardIcon;
    case PaymentMethodType.AliPay:
      return alipayIcon;
    case PaymentMethodType.WeChat:
      return wechatIcon;
    case PaymentMethodType.Scanner:
      return scannerIcon;
    default:
      return '';
  }
};
</script>

<style scoped>
.order-info-container {
  width: 563px;
  border-right: 1px solid #ebeef5;
  padding: 24px 0px 24px 24px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.payment-container {
  width: 563px;
  padding: 32px 32px 24px 32px;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  height: 100%;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 28px;
  font-weight: 500;
  color: #333;
}

.order-no {
  color: #666;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
  color: #333;
}

/* 支付方式网格 */
.payment-method-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.payment-method-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.payment-method-item.active {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.payment-method-item.opacity-50 {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.payment-method-item.opacity-50:hover {
  border-color: #dcdfe6 !important;
  background-color: #fff !important;
}

.payment-input-row {
  display: flex;
  margin-bottom: 16px;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
  padding: 24px;
}

.label {
  color: #666;
  font-size: 18px;
}

.input-container {
  flex: 1;
}

:deep(.amount-input .el-input__wrapper) {
  border: none;
  box-shadow: none;
}

:deep(.amount-input .el-input__inner) {
  text-align: right;
  font-size: 20px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

/* 底部工具栏 */
.payment-toolbar {
  display: flex;
  justify-content: end;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.total-label {
  font-size: 16px;
  margin-right: 10px;
}

.total-value {
  font-size: 24px;
  font-weight: bold;
  color: #ff3b30;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 6px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 扫码区域样式 */
.scanner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.scanner-status {
  margin-top: 16px;
  text-align: center;
}

.scanner-hint {
  color: #666;
  text-align: center;
  margin-top: 16px;
}

.bonus-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.bonus-item:last-child {
  margin-bottom: 0;
}

/* 会员余额价格样式 */
.member-balance-price :deep(.price-unit),
.member-balance-price :deep(.price-integer),
.member-balance-price :deep(.price-decimal) {
  color: #000 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
}

:deep(.customer-input .el-input__wrapper) {
  border-radius: 8px;
  height: 64px !important;
}

:deep(.customer-input-small .el-input__wrapper) {
  border-radius: 8px;
  height: 48px !important;
}
</style>
