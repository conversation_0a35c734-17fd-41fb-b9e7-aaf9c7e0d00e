import { provide, inject, reactive, computed } from 'vue';
import {
  IMemberPayDialogViewModel,
  MemberPayDialogInjectionKey,
  MEMBER_PAY_DIALOG_STATE_DEFAULTS,
  PaySuccessHandler,
  PayCancelHandler,
  DialogCloseHandler
} from './viewmodel';
import { PaymentMethodType, MemberInfo } from './memberPayEntity';
import { MemberPayInteractor } from './interactor';
import { ElMessage } from 'element-plus';
import { CardTypeMap, CardType } from '../memberOpen/entity';
import { useVenueStore } from '@/stores/venueStore';
export function useMemberPayDialog(): IMemberPayDialogViewModel {
  // 尝试注入视图模型，如果没有则创建新的
  const existingVM = inject(MemberPayDialogInjectionKey, null);
  if (existingVM) return existingVM;

  // 创建交互器
  const interactor = new MemberPayInteractor();
  
  // 获取venue store
  const venueStore = useVenueStore();

  // 状态初始化
  const state = reactive({
    ...MEMBER_PAY_DIALOG_STATE_DEFAULTS
  });

  // 事件处理器
  let paySuccessHandler: PaySuccessHandler | null = null;
  let payCancelHandler: PayCancelHandler | null = null;
  let dialogCloseHandler: DialogCloseHandler | null = null;

  // 添加轮询控制变量
  let isPollingCancelled = false;

  // 计算属性
  const computedProps = {
    // 支付方式列表
    paymentMethods: computed(() => {
      // 获取当前场所信息
      const venue = venueStore.venue;
      
      // 定义所有支付方式
      const allPaymentMethods = [
        { label: '现金', value: PaymentMethodType.Cash },
        { label: '银行卡', value: PaymentMethodType.Bank },
        { label: '支付宝', value: PaymentMethodType.AliPay },
        { label: '微信', value: PaymentMethodType.WeChat },
        { label: '扫码枪', value: PaymentMethodType.Scanner }
      ];
      
      // 过滤支付方式列表
      return allPaymentMethods.filter(method => {
        // 如果是扫码支付，根据isLeshuaPay字段判断是否显示
        if (method.value === PaymentMethodType.Scanner) {
          // 如果isLeshuaPay为0，不显示扫码支付选项；其他值显示
          return venue?.isLeshuaPay !== 0;
        }
        // 其他支付方式正常显示
        return true;
      });
    }),
    totalBalance: computed(() => {
      console.log('[MemberPayDialog totalBalance] state.memberInfo', state.memberInfo);
      return state.memberInfo?.balance || 0;
    }),
    // 计算充值金额（元转分）
    actualAmountInFen: computed(() => {
      const amount = parseFloat(state.totalFee || '0');
      return isNaN(amount) ? 0 : Math.floor(amount * 100);
    }),
    // 计算找零金额
    calculateChangeAmount: computed(() => {
      if (state.selectedPayMethod !== PaymentMethodType.Cash) return 0;

      const inputAmount = parseFloat(state.actualAmount || '0');
      const required = parseFloat(state.totalFee || '0');

      if (isNaN(inputAmount) || isNaN(required)) return 0;

      const change = inputAmount - required;
      return change > 0 ? Math.floor(change * 100) : 0;
    }),
    cardType: computed(() => {
      console.log(
        '[MemberPayDialog cardType] state.memberInfo?.cardType',
        state.memberInfo?.cardType,
        CardTypeMap,
        CardTypeMap[state.memberInfo?.cardType as CardType]
      );
      return CardTypeMap[state.memberInfo?.cardType as CardType] || '';
    })
  };

  // 动作方法
  const actions = {
    // 初始化
    init: async (memberInfo: any) => {
      if (!memberInfo) {
        console.error('会员信息不能为空');
        return;
      }

      state.loading = true;
      try {
        // 从传入的memberInfo对象中获取所需信息
        state.memberInfo = {
          cardId: memberInfo.id || '',
          cardNo: memberInfo.cardNumber || '',
          cardType: memberInfo.cardType || '',
          name: memberInfo.name || '',
          phone: memberInfo.phone || '',
          balance: memberInfo.balance || 0,
          levelName: memberInfo.cardLevelName || '普通会员',
          totalBalance: memberInfo.principalBalance + memberInfo.roomBonusBalance + memberInfo.goodsBonusBalance + memberInfo.commonBonusBalance
        };
        state.isInitialized = true;
      } catch (error) {
        console.error('初始化会员支付对话框失败:', error);
      } finally {
        state.loading = false;
      }
    },

    // 对话框打开处理
    onDialogOpen: () => {
      window.addEventListener('keypress', actions.handleScanInput);
    },

    // 对话框关闭处理
    onDialogClose: () => {
      window.removeEventListener('keypress', actions.handleScanInput);
      state.selectedPayMethod = '';
      state.scannerDialogVisible = false;
      state.scannerCode = '';
      state.totalFee = '0';
      state.actualAmount = '0';
      state.commonBonusAmount = '0';
      state.roomBonusAmount = '0';
      state.goodsBonusAmount = '0';

      // 设置取消轮询标志
      isPollingCancelled = true;

      // 触发对话框关闭事件
      if (dialogCloseHandler) {
        dialogCloseHandler();
      }
    },

    // 扫码弹窗关闭处理
    onScannerDialogClose: () => {
      console.log('[MemberPayDialog] 用户手动关闭扫码弹窗');
      console.log('[MemberPayDialog] 关闭前状态:', {
        selectedPayMethod: state.selectedPayMethod,
        scannerCode: state.scannerCode,
        scannerDialogVisible: state.scannerDialogVisible
      });
      // 清空扫码内容
      state.scannerCode = '';
      // 移除键盘监听器
      window.removeEventListener('keypress', actions.handleScanInput);
      // 确保弹窗状态为关闭
      state.scannerDialogVisible = false;
      console.log('[MemberPayDialog] 关闭后状态:', {
        selectedPayMethod: state.selectedPayMethod,
        scannerCode: state.scannerCode,
        scannerDialogVisible: state.scannerDialogVisible
      });
      // 不清空选择的支付方式，保持用户的选择，这样用户可以再次点击支付时继续使用扫码支付
    },

    // 选择支付方式
    selectPayMethod: (method: string) => {
      state.selectedPayMethod = method;

      // 如果选择的不是扫码支付，确保扫码弹窗关闭
      if (method !== PaymentMethodType.Scanner) {
        state.scannerDialogVisible = false;
        window.removeEventListener('keypress', actions.handleScanInput);
      }

      // 清空扫码内容，等待用户点击确认充值时重新处理
      state.scannerCode = '';

      // 如果是现金支付，默认设置输入金额为应付金额
      if (
        (method === PaymentMethodType.Cash ||
          method === PaymentMethodType.AliPay ||
          method === PaymentMethodType.WeChat ||
          method === PaymentMethodType.Bank) &&
        state.totalFee
      ) {
        state.actualAmount = state.totalFee;
      }
    },

    // 充值金额输入处理
    handleTotalFeeInput: (value: string) => {
      // 确保输入格式正确
      const numValue = value.replace(/[^\d.]/g, '');
      // 最多保留两位小数
      const parts = numValue.split('.');
      if (parts.length > 1) {
        parts[1] = parts[1].substring(0, 2);
        state.totalFee = parts[0] + '.' + parts[1];
      } else {
        state.totalFee = numValue;
      }

      // 如果选择了现金支付方式，同步更新实际支付金额
      if (
        state.selectedPayMethod === PaymentMethodType.Cash ||
        state.selectedPayMethod === PaymentMethodType.AliPay ||
        state.selectedPayMethod === PaymentMethodType.WeChat ||
        state.selectedPayMethod === PaymentMethodType.Bank
      ) {
        state.actualAmount = state.totalFee;
      }
    },

    // 充值金额失焦处理
    handleTotalFeeBlur: () => {
      // 确保有两位小数
      let value = parseFloat(state.totalFee || '0');
      if (isNaN(value)) value = 0;
      state.totalFee = value.toFixed(2);
    },

    // 实际支付金额输入处理（现金支付时计算找零）
    handleActualAmountInput: (value: string) => {
      // 确保输入格式正确
      const numValue = value.replace(/[^\d.]/g, '');
      // 最多保留两位小数
      const parts = numValue.split('.');
      if (parts.length > 1) {
        parts[1] = parts[1].substring(0, 2);
        state.actualAmount = parts[0] + '.' + parts[1];
      } else {
        state.actualAmount = numValue;
      }
    },

    // 实际支付金额失焦处理
    handleActualAmountBlur: () => {
      // 确保有两位小数
      let value = parseFloat(state.actualAmount || '0');
      if (isNaN(value)) value = 0;
      state.actualAmount = value.toFixed(2);
    },

    // 扫码输入处理
    handleScanInput: (event: KeyboardEvent) => {
      // 只在扫码对话框打开时处理
      if (!state.scannerDialogVisible) return;

      console.log('[MemberPayDialog handleScanInput] event.key', event.key);

      // 如果是回车，表示扫码完成
      if (event.key === 'Enter') {
        // 处理扫码完成
        console.log('[MemberPayDialog handleScanInput] state.scannerCode', state.scannerCode);
        if (state.scannerCode) {
          state.scannerDialogVisible = false;
          // 移除键盘监听器
          window.removeEventListener('keypress', actions.handleScanInput);
          // 这里应该处理扫码支付
          console.log('[MemberPayDialog] 扫码完成:', state.scannerCode);

          // 执行支付
          actions.handlePay();
        }
        return;
      }

      // 累积扫码内容
      state.scannerCode += event.key;
    },

    // 处理支付
    handlePay: async () => {
      if (!state.selectedPayMethod) {
        ElMessage.error('请选择支付方式');
        return null;
      }

      if (!state.totalFee || parseFloat(state.totalFee) <= 0) {
        ElMessage.error('请输入有效的充值金额');
        return null;
      }

      // 如果选择了扫码支付但没有扫码，则重新打开扫码弹窗
      if (state.selectedPayMethod === PaymentMethodType.Scanner && !state.scannerCode) {
        console.log('[MemberPayDialog] 扫码支付缺少扫码，重新打开扫码弹窗');
        console.log('[MemberPayDialog] 当前状态:', {
          selectedPayMethod: state.selectedPayMethod,
          scannerCode: state.scannerCode,
          scannerDialogVisible: state.scannerDialogVisible
        });
        state.scannerDialogVisible = true;
        state.scannerCode = '';
        window.addEventListener('keypress', actions.handleScanInput);
        console.log('[MemberPayDialog] 扫码弹窗已打开，状态已更新为:', state.scannerDialogVisible);
        return null; // 等待扫码，不继续执行支付流程
      }

      // 获取必要参数
      const totalFee = parseFloat(state.totalFee || '0');
      const roomBonusAmount = parseFloat(state.roomBonusAmount || '0');
      const goodsBonusAmount = parseFloat(state.goodsBonusAmount || '0');
      const commonBonusAmount = parseFloat(state.commonBonusAmount || '0');

      // 构建支付记录
      const rechargeRecord = {
        payType: actions.getPayTypeRecord(state.selectedPayMethod),
        bQROneCode: state.scannerCode || '',
        TotalFee: Math.floor(totalFee * 100) // 转换为分
      };

      console.log('[MemberPayDialog] 开始处理支付, 支付方式:', state.selectedPayMethod, '扫码内容:', state.scannerCode, '支付记录:', rechargeRecord);

      // 构建请求参数
      const params = {
        memberCardId: state.memberInfo?.cardId || '',
        totalFee: Math.floor(totalFee * 100), // 转换为分
        roomBonusAmount: Math.floor(roomBonusAmount * 100), // 转换为分
        goodsBonusAmount: Math.floor(goodsBonusAmount * 100), // 转换为分
        commonBonusAmount: Math.floor(commonBonusAmount * 100), // 转换为分
        memberRechargeRecordVOs: [rechargeRecord]
      };

      state.loading = true;
      try {
        // 调用会员充值API
        const result = await interactor.memberRecharge(params);
        console.log('充值结果:', result);

        // 如果是扫码支付，需要查询支付结果
        console.log('充值结果:', result);
        if (state.selectedPayMethod === PaymentMethodType.Scanner && result && result.data.length > 0) {
          return await actions.queryAndConfirmScanPayment(result);
        }
        // 其他支付方式直接处理成功
        return actions.processPaymentSuccess(result);
      } catch (error) {
        console.error('充值失败:', error);
        ElMessage.error('充值失败');
        throw error;
      } finally {
        state.loading = false;
      }
    },

    // 查询并确认扫码支付结果
    queryAndConfirmScanPayment: async (result: any): Promise<boolean> => {
      // 如果没有返回必要的查询参数，直接返回成功
      console.log('[MemberPayDialog queryAndConfirmScanPayment] result', result);
      if (!result || result.length === 0) {
        return actions.processPaymentSuccess(result);
      }
      const billId = result.data[0].billId;
      if (!billId) {
        return actions.processPaymentSuccess(result);
      }

      ElMessage.info('正在查询支付结果...');

      // 重置轮询取消标志
      isPollingCancelled = false;

      // 设置轮询参数
      const maxRetryTime = 30000; // 30秒超时，从180秒改为30秒
      const retryInterval = 2000; // 每2秒查询一次
      const startTime = Date.now();
      let isPaySuccess = false;

      while (!isPaySuccess && Date.now() - startTime < maxRetryTime && !isPollingCancelled) {
        try {
          // 等待2秒后查询
          await new Promise(resolve => setTimeout(resolve, retryInterval));

          // 如果已取消轮询，退出循环
          if (isPollingCancelled) {
            console.log('[MemberPayDialog queryAndConfirmScanPayment] 轮询已取消');
            break;
          }

          // 查询支付状态
          const queryParams = {
            billId
          };

          console.log(`[MemberPayDialog queryAndConfirmScanPayment] 查询会员充值支付状态:`, queryParams);
          const queryResult = await interactor.queryRechargePayStatus(queryParams);
          console.log('[MemberPayDialog queryAndConfirmScanPayment] 查询会员充值支付状态结果:', queryResult);

          // 判断是否支付成功
          if (queryResult && queryResult.code === 0 && queryResult.data && queryResult.data.status === 'paid') {
            console.log('[MemberPayDialog queryAndConfirmScanPayment] 充值支付查询成功:', queryResult);
            isPaySuccess = true;
            // 使用统一方法处理支付成功
            break;
          } else {
            console.log('支付状态未完成，继续等待...');
          }
        } catch (queryError) {
          console.error('查询支付状态出错:', queryError);
          // 继续轮询，直到超时
        }
      }

      // 如果取消了轮询，返回false
      if (isPollingCancelled) {
        console.log('[MemberPayDialog queryAndConfirmScanPayment] 轮询已取消，返回false');
        return false;
      }

      // 如果超过轮询时间仍未成功
      if (!isPaySuccess) {
        ElMessage.warning('支付超时，请稍后查询会员状态');
        return false;
      }

      return actions.processPaymentSuccess(result);
    },

    // 处理支付成功
    processPaymentSuccess: (result: any) => {
      ElMessage.success('充值成功');

      // 调用支付成功回调
      if (paySuccessHandler) {
        paySuccessHandler(result);
      }

      return true;
    },

    //支付失败
    processPaymentFailed: () => {
      ElMessage.error('充值失败');
      if (payCancelHandler) {
        payCancelHandler();
      }
      return false;
    },

    // 注册支付成功处理器
    registerPaySuccessHandler: (handler: PaySuccessHandler) => {
      paySuccessHandler = handler;
    },

    // 注册支付取消处理器
    registerPayCancelHandler: (handler: PayCancelHandler) => {
      payCancelHandler = handler;
    },

    // 注册对话框关闭处理器
    registerDialogCloseHandler: (handler: DialogCloseHandler) => {
      dialogCloseHandler = handler;
    },

    getPayTypeRecord: (payType: string) => {
      switch (payType) {
        case PaymentMethodType.Scanner:
          return 'bShowQR';
        case PaymentMethodType.AliPay:
          return 'alipay_record';
        case PaymentMethodType.WeChat:
          return 'wechat_record';
        case PaymentMethodType.Bank:
          return 'bank_record';
        default:
          return payType;
      }
    }
  };

  // 创建视图模型
  const viewModel: IMemberPayDialogViewModel = {
    state,
    computed: computedProps,
    actions
  };

  // 提供视图模型
  provide(MemberPayDialogInjectionKey, viewModel);

  return viewModel;
}
