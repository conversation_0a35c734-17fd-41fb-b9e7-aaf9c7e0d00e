import { reactive, computed as vueComputed } from 'vue';
import type { IPaymentViewModel, IBillState, IPaymentState } from './viewmodel';
import { PaymentConverter } from './converter';
import { PaymentInteractor } from './interactor';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';

export function usePayment(): IPaymentViewModel {
  const interactor = new PaymentInteractor();
  const router = useRouter();
  const route = useRoute();

  // 从URL参数获取初始值
  const initFromQuery = () => {
    try {
      const { data } = route.query;
      if (data) {
        const queryData = JSON.parse(data as string);
        return {
          cardFee: Number(queryData.cardFee) || 0,
          amount: Number(queryData.amount) || 0,
          giftType: '',
          giftAmount: 0,
          roomType: '',
          salesperson: queryData.salesperson || ''
        };
      }
    } catch (error) {
      console.error('Failed to parse query data:', error);
    }

    // 如果解析失败或没有数据，返回默认值
    return {
      cardFee: 0,
      amount: 0,
      giftType: '',
      giftAmount: 0,
      roomType: '',
      salesperson: ''
    };
  };

  // 账单状态
  const billState = reactive<IBillState>(initFromQuery());

  // 支付状态
  const paymentState = reactive<IPaymentState>({
    paymentMethod: '',
    receivable: 0,
    received: 0,
    change: 0,
    discount: 0,
    isAdjusted: false
  });

  // 计算属性
  const computed = {
    // 是否可以提交
    canSubmit: vueComputed(() => {
      return paymentState.paymentMethod && computed.received.value > 0;
    }),

    // 总金额
    totalAmount: vueComputed(() => {
      return Number(billState.cardFee || 0) + Number(billState.amount || 0);
    }),

    // 应收金额 = 充值金额 + 制卡费
    receivable: vueComputed(() => {
      return Number(billState.amount || 0) + Number(billState.cardFee || 0);
    }),

    // 实收金额 = 应收金额 - 折扣金额（如果有商家调整）
    received: vueComputed(() => {
      if (paymentState.isAdjusted) {
        return computed.receivable.value - Number(paymentState.discount || 0);
      }
      return computed.receivable.value;
    }),

    // 还差金额 = 实收金额（就是客户实际需要支付的金额）
    change: vueComputed(() => {
      return computed.received.value;
    })
  };

  // 动作
  const actions = {
    updateGiftAmount(amount: number) {
      billState.giftAmount = amount;
    },

    selectPaymentMethod(method: string) {
      paymentState.paymentMethod = method;
    },

    async onSuspendAccount() {
      const entity = PaymentConverter.toEntity(billState, paymentState);
      const result = await interactor.suspendAccount(entity);
      if (result.success) {
        ElMessage.success('挂账成功');
      } else {
        ElMessage.error(result.error || '挂账失败');
      }
    },

    async onMerchantAdjust() {
      paymentState.isAdjusted = true;
      // 这里可以弹出对话框让商家输入折扣金额
      // TODO: 实现折扣输入逻辑
      const entity = PaymentConverter.toEntity(billState, paymentState);
      const result = await interactor.merchantAdjust(entity);
      if (result.success) {
        ElMessage.success('调整成功');
      } else {
        ElMessage.error(result.error || '调整失败');
        paymentState.isAdjusted = false;
        paymentState.discount = 0;
      }
    },

    async onConfirmPayment() {
      const entity = PaymentConverter.toEntity(billState, paymentState);
      const result = await interactor.submitPayment(entity);
      if (result.success) {
        ElMessage.success(`支付成功，订单号: ${result.orderId}`);
      } else {
        ElMessage.error(result.error || '支付失败');
      }
    },

    goBack() {
      router.back();
    }
  };

  return {
    billState,
    paymentState,
    computed,
    actions
  };
}
