import { ComputedRef } from 'vue';

// 账单状态
export interface IBillState {
  cardFee: number;
  amount: number;
  giftType: string;
  giftAmount: number;
  roomType: string;
  salesperson: string;
}

// 支付状态
export interface IPaymentState {
  paymentMethod: string;
  receivable: number;
  received: number;
  change: number;
  discount: number; // 折扣金额
  isAdjusted: boolean; // 是否经过商家调整
}

// 计算属性
export interface IPaymentComputed {
  canSubmit: ComputedRef<boolean>;
  totalAmount: ComputedRef<number>;
  // 应收金额 = 充值金额 + 制卡费
  receivable: ComputedRef<number>;
  // 实收金额 = 应收金额 + 赠送金额
  received: ComputedRef<number>;
  // 还差金额 = 应收金额 - 实收金额
  change: ComputedRef<number>;
}

// 动作
export interface IPaymentActions {
  selectPaymentMethod: (method: string) => void;
  onConfirmPayment: () => void;
  onSuspendAccount: () => void;
  onMerchantAdjust: () => void;
  updateGiftAmount: (amount: number) => void;
  goBack: () => void;
}

// 总的ViewModel接口
export interface IPaymentViewModel {
  billState: IBillState;
  paymentState: IPaymentState;
  computed: IPaymentComputed;
  actions: IPaymentActions;
}
