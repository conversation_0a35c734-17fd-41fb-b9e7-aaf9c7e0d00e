import type { PaymentEntity } from './entity';
import type { IBillState, IPaymentState } from './viewmodel';

export class PaymentConverter {
  // 视图模型转实体
  static toEntity(billState: IBillState, paymentState: IPaymentState): PaymentEntity {
    return {
      cardFee: billState.cardFee,
      rechargeAmount: billState.rechargeAmount,
      giftType: billState.giftType,
      giftAmount: billState.giftAmount,
      roomFee: billState.roomFee,
      salesperson: billState.salesperson,
      paymentMethod: paymentState.paymentMethod,
      receivable: paymentState.receivable,
      received: paymentState.received,
      change: paymentState.change
    };
  }

  // 实体转视图模型
  static toBillState(entity: PaymentEntity): IBillState {
    return {
      cardFee: entity.cardFee,
      rechargeAmount: entity.rechargeAmount,
      giftType: entity.giftType,
      giftAmount: entity.giftAmount,
      roomFee: entity.roomFee,
      salesperson: entity.salesperson
    };
  }

  static toPaymentState(entity: PaymentEntity): IPaymentState {
    return {
      paymentMethod: entity.paymentMethod,
      receivable: entity.receivable,
      received: entity.received,
      change: entity.change
    };
  }
}
