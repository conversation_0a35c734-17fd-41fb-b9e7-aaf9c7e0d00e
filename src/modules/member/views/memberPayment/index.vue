<template>
  <leave-confirm-layout>
    <el-container class="h-full bg-gray-50 flex flex-col">
      <!-- 顶部导航 -->
      <el-header height="50px" class="flex items-center justify-between">
        <el-page-header @back="vm.actions.goBack" icon="ArrowLeft" class="w-full">
          <template #content>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/member' }">会员</el-breadcrumb-item>
              <el-breadcrumb-item>结账</el-breadcrumb-item>
            </el-breadcrumb>
          </template>
          <template #extra>
            <el-button link @click="vm.actions.goBack">返回上级</el-button>
          </template>
        </el-page-header>
      </el-header>

      <!-- 主要内容区 -->
      <el-main class="flex-1 flex gap-5 p-5 min-h-0 overflow-hidden">
        <!-- 左侧表单区域 -->
        <div class="flex-[2] bg-white rounded-lg p-5 flex flex-col overflow-auto">
          <el-form label-width="100px" class="flex-1">
            <el-form-item label="制卡费">
              <span class="text-sm text-gray-500">¥ {{ vm.billState.cardFee }}</span>
            </el-form-item>

            <el-form-item label="充值金额">
              <el-input-number v-model="vm.billState.amount" :min="0" class="w-full" />
            </el-form-item>

            <el-form-item label="赠送金额" class="flex gap-2.5">
              <el-select v-model="vm.billState.giftType" class="w-[150px]">
                <el-option label="请选择类型" value="" />
                <el-option label="充值赠送" value="recharge" />
                <el-option label="活动赠送" value="activity" />
              </el-select>
              <el-input v-model="vm.billState.giftAmount" placeholder="请输入金额" class="flex-1" />
            </el-form-item>

            <div class="flex gap-5 w-full">
              <el-form-item label="包厢" class="flex-1 mb-0">
                <el-select v-model="vm.billState.roomType" class="w-full">
                  <el-option label="请选择" value="" />
                </el-select>
              </el-form-item>
              <el-form-item label="销售员" class="flex-[2] mb-0">
                <div class="flex gap-2.5">
                  <el-input v-model="vm.billState.salesperson" placeholder="选择或扫码">
                    <template #append>
                      <el-button icon="Aim" @click="onScan" />
                    </template>
                  </el-input>
                </div>
              </el-form-item>
            </div>
          </el-form>
          <el-button type="primary" class="mt-5 w-[120px]"> 自定义 </el-button>
        </div>

        <!-- 中间支付方式区域 -->
        <div class="w-[200px] bg-white rounded-lg p-5 overflow-auto">
          <h3>支付方式</h3>
          <div class="grid grid-cols-1 gap-2.5 mt-5">
            <el-button
              v-for="method in PAYMENT_METHODS"
              :key="method.value"
              class="h-[60px] flex items-center justify-center ml-0-import"
              :class="{ 'bg-primary text-white': vm.paymentState.paymentMethod === method.value }"
              @click="vm.actions.selectPaymentMethod(method.value)">
              <el-icon><component :is="method.icon" /></el-icon>
              <span>{{ method.label }}</span>
            </el-button>
          </div>
        </div>

        <!-- 右侧结算区域 -->
        <div class="flex-1 bg-white rounded-lg p-5 flex flex-col overflow-auto">
          <div class="flex gap-2.5 mb-5">
            <el-button plain>挂账</el-button>
            <el-button plain>商家调整</el-button>
          </div>

          <div class="flex-1 flex flex-col gap-4">
            <div class="flex justify-between text-base">
              <span>应收:</span>
              <span class="font-bold text-primary">¥ {{ vm.computed.receivable }}</span>
            </div>
            <div class="flex justify-between text-base">
              <span>实收:</span>
              <span class="font-bold text-primary">¥ {{ vm.computed.received }}</span>
            </div>
            <template v-if="vm.paymentState.isAdjusted">
              <div class="flex justify-between text-base">
                <span>折扣:</span>
                <span class="font-bold text-red-500">-¥ {{ vm.paymentState.discount }}</span>
              </div>
            </template>
            <div class="flex justify-between text-base">
              <span>还差:</span>
              <span class="font-bold text-primary">¥ {{ vm.computed.change }}</span>
            </div>
          </div>

          <el-button type="primary" class="h-[50px] text-lg" :disabled="!vm.computed.canSubmit" @click="vm.actions.onConfirmPayment"> 确认结账 </el-button>
        </div>
      </el-main>
    </el-container>
  </leave-confirm-layout>
</template>

<script setup lang="ts">
import { usePayment } from './presenter';
import { PAYMENT_METHODS } from './entity';
import LeaveConfirmLayout from '@/layouts/LeaveConfirmLayout.vue';

const vm = usePayment();

const onScan = () => {
  // TODO: 实现扫码逻辑
};
</script>

<style>
.ml-0-import {
  margin-left: 0 !important;
}
</style>
