// 会员卡支付弹窗的实体类定义

// 会员卡详情信息
export interface MemberCardDetail {
  cardId: string;
  cardNo: string;
  name: string;
  phone: string;
  cardType: string;
  levelName: string;
  balance: number; // 现金余额
  roomBonusBalance: number; // 包厢赠金余额
  goodsBonusBalance: number; // 商品赠金余额
  commonBonusBalance: number; // 通用赠金余额
  totalBalance: number; // 总余额
  status: number; // 状态
}

// 会员卡支付参数
export interface MemberCardPayParams {
  memberCardId: string; // 会员卡ID
  roomAmount: number; // 包厢金额 (分)
  goodsAmount: number; // 商品金额 (分)
  useCashAmount: number; // 使用现金余额 (分)
  useCommonBonusAmount: number; // 使用通用赠金 (分)
  useRoomBonusAmount: number; // 使用包厢赠金 (分)
  useGoodsBonusAmount: number; // 使用商品赠金 (分)
}

// 会员卡支付结果
export interface MemberCardPayResult {
  success: boolean;
  memberCardId: string;
  useCashAmount?: number;
  useCommonBonusAmount?: number;
  useRoomBonusAmount?: number;
  useGoodsBonusAmount?: number;
  totalAmount?: number;
  totalFee?: number;
  principalAmount?: number;
  memberCommonBonusAmount?: number;
  memberGoodsBonusAmount?: number;
  memberRoomBonusAmount?: number;
}
