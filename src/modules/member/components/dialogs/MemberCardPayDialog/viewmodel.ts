import { InjectionKey, ComputedRef } from 'vue';
import { MemberCardDetail, MemberCardPayResult } from './entity';

// 定义弹窗状态接口
export interface IMemberCardPayState {
  visible: boolean;
  loading: boolean;
  // 会员卡ID
  memberCardId: string;
  // 会员卡详情
  memberCardDetail: MemberCardDetail | null;
  // 消费金额 (分)
  roomAmount: number;
  goodsAmount: number;
  // 输入金额 (元)
  cashAmount: string;
  commonBonusAmount: string;
  roomBonusAmount: string;
  goodsBonusAmount: string;
  // 会员卡支付金额汇总 (分) - 在平衡后更新
  totalMemberCardPayAmount: number;
  // 回调函数
  onSuccess?: (result: MemberCardPayResult) => void;
  onCancel?: () => void;
}

// 定义弹窗计算属性接口
export interface IMemberCardPayComputed {
  // 显示的总金额 (分)
  totalAmount: ComputedRef<number>;
  // 已分配金额 (分)
  allocatedAmount: ComputedRef<number>;
  // 剩余未分配金额 (分)
  remainingAmount: ComputedRef<number>;
  // 是否可以支付
  canPay: ComputedRef<boolean>;
  // 使用的现金余额 (分)
  useCashAmount: ComputedRef<number>;
  // 使用的通用赠金 (分)
  useCommonBonusAmount: ComputedRef<number>;
  // 使用的包厢赠金 (分)
  useRoomBonusAmount: ComputedRef<number>;
  // 使用的商品赠金 (分)
  useGoodsBonusAmount: ComputedRef<number>;
}

// 定义弹窗操作接口
export interface IMemberCardPayActions {
  // 初始化
  init: (memberCardId: string, roomAmount: number, goodsAmount: number) => Promise<void>;
  // 对话框事件
  onDialogOpen: () => void;
  onDialogClose: () => void;
  // 金额输入事件（失去焦点时触发）
  handleCashAmountInput: (event: Event) => void;
  handleCommonBonusAmountInput: (event: Event) => void;
  handleRoomBonusAmountInput: (event: Event) => void;
  handleGoodsBonusAmountInput: (event: Event) => void;
  // 自动分配金额
  autoAllocateAmount: () => void;
  // 确认支付
  handleConfirm: () => Promise<void>;
  // 取消支付
  handleCancel: () => void;
  // 更换会员卡
  changeMemberCard?: () => void;
}

// 定义ViewModel接口
export interface IMemberCardPayViewModel {
  state: IMemberCardPayState;
  computed: IMemberCardPayComputed;
  actions: IMemberCardPayActions;
}

// 依赖注入Key
export const MemberCardPayDialogKey: InjectionKey<IMemberCardPayViewModel> = Symbol('MemberCardPayViewModel');

// 默认状态
export const MEMBER_CARD_PAY_STATE_DEFAULTS: IMemberCardPayState = {
  visible: false,
  loading: false,
  memberCardId: '',
  memberCardDetail: null,
  roomAmount: 0,
  goodsAmount: 0,
  cashAmount: '0',
  commonBonusAmount: '0',
  roomBonusAmount: '0',
  goodsBonusAmount: '0',
  totalMemberCardPayAmount: 0
};
