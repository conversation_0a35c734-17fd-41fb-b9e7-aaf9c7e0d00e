import { reactive, computed } from 'vue';
import { IMemberCardPayViewModel, IMemberCardPayState, IMemberCardPayComputed, IMemberCardPayActions, MEMBER_CARD_PAY_STATE_DEFAULTS } from './viewmodel';
import { MemberCardPayInteractor } from './interactor';
import { MemberCardPayResult } from './entity';
import { ElMessage } from 'element-plus';

/**
 * 会员卡支付弹窗协调层
 */
export class MemberCardPayPresenter implements IMemberCardPayViewModel {
  state: IMemberCardPayState;
  interactor: MemberCardPayInteractor;

  constructor() {
    this.state = reactive({ ...MEMBER_CARD_PAY_STATE_DEFAULTS });
    this.interactor = new MemberCardPayInteractor();
  }

  /**
   * 计算属性
   */
  computed: IMemberCardPayComputed = {
    // 计算总金额 (分)
    totalAmount: computed(() => {
      return this.state.roomAmount + this.state.goodsAmount;
    }),

    // 计算已分配金额 (分)
    allocatedAmount: computed(() => {
      return (
        this.computed.useCashAmount.value +
        this.computed.useCommonBonusAmount.value +
        this.computed.useRoomBonusAmount.value +
        this.computed.useGoodsBonusAmount.value
      );
    }),

    // 计算剩余未分配金额 (分)
    remainingAmount: computed(() => {
      const total = this.computed.totalAmount.value;
      const allocated = this.computed.allocatedAmount.value;
      return Math.max(0, total - allocated);
    }),

    // 计算是否可以支付
    canPay: computed(() => {
      // 必须有会员卡详情
      if (!this.state.memberCardDetail) return false;

      // 支付金额可以小于等于总金额
      return this.computed.allocatedAmount.value <= this.computed.totalAmount.value;
    }),

    // 计算使用的现金余额 (分)
    useCashAmount: computed(() => {
      return parseFloat(this.state.cashAmount || '0') * 100 || 0;
    }),

    // 计算使用的通用赠金 (分)
    useCommonBonusAmount: computed(() => {
      return parseFloat(this.state.commonBonusAmount || '0') * 100 || 0;
    }),

    // 计算使用的包厢赠金 (分)
    useRoomBonusAmount: computed(() => {
      return parseFloat(this.state.roomBonusAmount || '0') * 100 || 0;
    }),

    // 计算使用的商品赠金 (分)
    useGoodsBonusAmount: computed(() => {
      return parseFloat(this.state.goodsBonusAmount || '0') * 100 || 0;
    })
  };

  /**
   * 动作实现
   */
  actions: IMemberCardPayActions = {
    // 初始化
    init: async (memberCardId: string, roomAmount: number, goodsAmount: number) => {
      try {
        this.state.loading = true;
        this.state.memberCardId = memberCardId;
        this.state.roomAmount = roomAmount;
        this.state.goodsAmount = goodsAmount;

        // 重置金额输入
        this.state.cashAmount = '0';
        this.state.commonBonusAmount = '0';
        this.state.roomBonusAmount = '0';
        this.state.goodsBonusAmount = '0';

        // 获取会员卡详情
        const response = await this.interactor.getMemberCardDetail(memberCardId);

        if (!response) {
          throw new Error('获取会员卡详情失败');
        }

        // 将API返回的数据映射到所需的会员卡详情格式
        this.state.memberCardDetail = response;
        this.state.memberCardDetail.totalBalance =
          this.state.memberCardDetail.balance +
          this.state.memberCardDetail.roomBonusBalance +
          this.state.memberCardDetail.goodsBonusBalance +
          this.state.memberCardDetail.commonBonusBalance;

        console.log('会员卡详情：', this.state.memberCardDetail);

        // 自动分配金额
        this.actions.autoAllocateAmount();
      } catch (error) {
        console.error('初始化失败:', error);
        ElMessage.error('初始化失败: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        this.state.loading = false;
      }
    },

    // 对话框事件
    onDialogOpen: () => {
      console.log('会员卡支付弹窗打开');
    },

    onDialogClose: () => {
      console.log('会员卡支付弹窗关闭');
      // 重置状态
      Object.assign(this.state, MEMBER_CARD_PAY_STATE_DEFAULTS);
    },

    // 金额输入事件（失去焦点时触发）
    handleCashAmountInput: (event: Event) => {
      const value = (event.target as HTMLInputElement)?.value || this.state.cashAmount;
      this.state.cashAmount = value;
      this.validateAndRebalance('cashAmount');
    },

    handleCommonBonusAmountInput: (event: Event) => {
      const value = (event.target as HTMLInputElement)?.value || this.state.commonBonusAmount;
      this.state.commonBonusAmount = value;
      this.validateAndRebalance('commonBonusAmount');
    },

    handleRoomBonusAmountInput: (event: Event) => {
      const value = (event.target as HTMLInputElement)?.value || this.state.roomBonusAmount;
      this.state.roomBonusAmount = value;
      this.validateAndRebalance('roomBonusAmount');
    },

    handleGoodsBonusAmountInput: (event: Event) => {
      const value = (event.target as HTMLInputElement)?.value || this.state.goodsBonusAmount;
      this.state.goodsBonusAmount = value;
      this.validateAndRebalance('goodsBonusAmount');
    },

    // 自动分配金额
    autoAllocateAmount: () => {
      if (!this.state.memberCardDetail) return;

      const memberCard = this.state.memberCardDetail;
      const totalAmount = this.computed.totalAmount.value;
      let remainingAmount = totalAmount;
      let remainingRoomAmount = this.state.roomAmount;
      let remainingGoodsAmount = this.state.goodsAmount;

      console.log('自动分配金额开始:', {
        totalAmount,
        roomAmount: this.state.roomAmount,
        goodsAmount: this.state.goodsAmount,
        cashBalance: memberCard.balance,
        roomBonusBalance: memberCard.roomBonusBalance,
        goodsBonusBalance: memberCard.goodsBonusBalance,
        commonBonusBalance: memberCard.commonBonusBalance
      });

      // 重置所有输入值
      this.state.cashAmount = '0';
      this.state.commonBonusAmount = '0';
      this.state.roomBonusAmount = '0';
      this.state.goodsBonusAmount = '0';

      // 1. 严格优先使用现金余额，不考虑其他情况
      if (remainingAmount > 0 && memberCard.balance > 0) {
        // 可用的现金余额取较小值
        const availableCash = Math.min(memberCard.balance, remainingAmount);

        if (availableCash > 0) {
          // 设置使用的现金金额
          this.state.cashAmount = (availableCash / 100).toFixed(2);

          // 减少剩余总金额
          remainingAmount -= availableCash;

          // 计算现金支付分别覆盖的包厢和商品金额
          if (totalAmount > 0) {
            // 按比例计算现金支付覆盖的包厢和商品金额
            const roomCoveredRatio = this.state.roomAmount / totalAmount;
            const goodsCoveredRatio = this.state.goodsAmount / totalAmount;

            // 计算现金覆盖的包厢金额
            const roomCovered = Math.floor(availableCash * roomCoveredRatio);
            // 计算现金覆盖的商品金额
            const goodsCovered = availableCash - roomCovered; // 确保总和等于availableCash

            // 减少剩余需要支付的包厢和商品金额
            remainingRoomAmount = Math.max(0, remainingRoomAmount - roomCovered);
            remainingGoodsAmount = Math.max(0, remainingGoodsAmount - goodsCovered);
          }

          console.log('使用现金后:', {
            usedCash: availableCash,
            remainingAmount,
            remainingRoomAmount,
            remainingGoodsAmount
          });
        }
      }

      // 只有在现金余额不足以支付全部金额时，才考虑使用赠金

      // 2. 如果还有未支付的包厢费用，使用包厢赠金
      if (remainingAmount > 0 && remainingRoomAmount > 0 && memberCard.roomBonusBalance > 0) {
        // 可用的包厢赠金取剩余包厢金额和赠金余额的较小值
        const availableRoomBonus = Math.min(memberCard.roomBonusBalance, remainingRoomAmount);

        if (availableRoomBonus > 0) {
          // 设置使用的包厢赠金金额
          this.state.roomBonusAmount = (availableRoomBonus / 100).toFixed(2);

          // 减少剩余总金额和包厢金额
          remainingAmount -= availableRoomBonus;
          remainingRoomAmount -= availableRoomBonus;

          console.log('使用包厢赠金后:', {
            usedRoomBonus: availableRoomBonus,
            remainingAmount,
            remainingRoomAmount
          });
        }
      }

      // 3. 如果还有未支付的商品费用，使用商品赠金
      if (remainingAmount > 0 && remainingGoodsAmount > 0 && memberCard.goodsBonusBalance > 0) {
        // 可用的商品赠金取剩余商品金额和赠金余额的较小值
        const availableGoodsBonus = Math.min(memberCard.goodsBonusBalance, remainingGoodsAmount);

        if (availableGoodsBonus > 0) {
          // 设置使用的商品赠金金额
          this.state.goodsBonusAmount = (availableGoodsBonus / 100).toFixed(2);

          // 减少剩余总金额和商品金额
          remainingAmount -= availableGoodsBonus;
          remainingGoodsAmount -= availableGoodsBonus;

          console.log('使用商品赠金后:', {
            usedGoodsBonus: availableGoodsBonus,
            remainingAmount,
            remainingGoodsAmount
          });
        }
      }

      // 4. 最后，如果还有剩余未分配金额，使用通用赠金
      if (remainingAmount > 0 && memberCard.commonBonusBalance > 0) {
        // 可用的通用赠金取剩余总金额和赠金余额的较小值
        const availableCommonBonus = Math.min(memberCard.commonBonusBalance, remainingAmount);

        if (availableCommonBonus > 0) {
          // 设置使用的通用赠金金额
          this.state.commonBonusAmount = (availableCommonBonus / 100).toFixed(2);

          // 减少剩余总金额
          remainingAmount -= availableCommonBonus;

          console.log('使用通用赠金后:', {
            usedCommonBonus: availableCommonBonus,
            remainingAmount
          });
        }
      }

      // 更新会员卡支付金额汇总
      this.state.totalMemberCardPayAmount =
        (parseFloat(this.state.cashAmount) || 0) * 100 +
        (parseFloat(this.state.commonBonusAmount) || 0) * 100 +
        (parseFloat(this.state.roomBonusAmount) || 0) * 100 +
        (parseFloat(this.state.goodsBonusAmount) || 0) * 100;

      // 记录最终分配结果
      console.log('金额分配结果:', {
        cashAmount: this.state.cashAmount,
        roomBonusAmount: this.state.roomBonusAmount,
        goodsBonusAmount: this.state.goodsBonusAmount,
        commonBonusAmount: this.state.commonBonusAmount,
        totalMemberCardPayAmount: (this.state.totalMemberCardPayAmount / 100).toFixed(2),
        remainingAmount
      });

      // 如果还有剩余未分配金额，将导致无法支付
      if (remainingAmount > 0) {
        ElMessage.warning(`会员卡余额不足，还差 ${(remainingAmount / 100).toFixed(2)} 元`);
      }
    },

    // 确认支付
    handleConfirm: async () => {
      try {
        if (!this.state.memberCardDetail) {
          ElMessage.warning('会员卡详情不存在');
          return;
        }

        if (!this.computed.canPay.value) {
          ElMessage.warning('支付金额不足或超出');
          return;
        }

        this.state.loading = true;

        // 计算实际支付的总金额（四个支付方式的总和）
        const actualTotalFee =
          this.computed.useCashAmount.value +
          this.computed.useCommonBonusAmount.value +
          this.computed.useRoomBonusAmount.value +
          this.computed.useGoodsBonusAmount.value;

        // 构建支付结果数据
        const payResult: MemberCardPayResult = {
          success: true,
          memberCardId: this.state.memberCardId,
          useCashAmount: this.computed.useCashAmount.value,
          useCommonBonusAmount: this.computed.useCommonBonusAmount.value,
          useRoomBonusAmount: this.computed.useRoomBonusAmount.value,
          useGoodsBonusAmount: this.computed.useGoodsBonusAmount.value,
          totalAmount: this.computed.totalAmount.value
        };

        // 按照要求的格式准备数据
        const payRecord = {
          memberCardId: this.state.memberCardId,
          totalFee: actualTotalFee, // 修改为实际支付总额
          principalAmount: this.computed.useCashAmount.value,
          memberCommonBonusAmount: this.computed.useCommonBonusAmount.value,
          memberGoodsBonusAmount: this.computed.useGoodsBonusAmount.value,
          memberRoomBonusAmount: this.computed.useRoomBonusAmount.value,
          success: true
        };

        console.log('会员卡支付确认，发送支付记录:', payRecord);

        // 调用成功回调，传递支付记录
        if (this.state.onSuccess) {
          this.state.onSuccess(payRecord);
        }

        // 不需要在这里关闭弹窗，由对话框组件的回调控制关闭
        // this.state.visible = false;
      } catch (error) {
        console.error('支付处理失败:', error);
        ElMessage.error('支付处理失败: ' + (error instanceof Error ? error.message : String(error)));
      } finally {
        this.state.loading = false;
      }
    },

    // 取消支付
    handleCancel: () => {
      // 如果存在取消回调，则调用
      if (this.state.onCancel) {
        this.state.onCancel();
      }
    },

    // 更换会员卡
    changeMemberCard: () => {
      console.log('尝试更换会员卡');
      ElMessage.info('更换会员卡功能暂未实现');
      // 在这里可以打开选择会员卡的对话框
    }
  };

  /**
   * 验证输入金额
   * @param field 字段名
   */
  private validateInputAmount(field: keyof Pick<IMemberCardPayState, 'cashAmount' | 'commonBonusAmount' | 'roomBonusAmount' | 'goodsBonusAmount'>) {
    if (!this.state.memberCardDetail) return;

    // 转换为数值
    const value = parseFloat(this.state[field] || '0');

    // 如果不是有效数字，重置为0
    if (isNaN(value) || value < 0) {
      this.state[field] = '0';
      return;
    }

    // 检查各种约束条件
    let maxAllowed = 0;
    let warningMessage = '';

    switch (field) {
      case 'cashAmount':
        // 现金：不能超过现金余额
        maxAllowed = this.state.memberCardDetail.balance / 100;
        warningMessage = '现金余额不足';
        break;

      case 'commonBonusAmount':
        // 通用赠金：不能超过通用赠金余额，也不能超过应付金额
        const commonBonusBalance = this.state.memberCardDetail.commonBonusBalance / 100;
        const totalAmount = this.computed.totalAmount.value / 100;
        maxAllowed = Math.min(commonBonusBalance, totalAmount);

        if (value > commonBonusBalance) {
          warningMessage = '通用赠金余额不足';
        } else if (value > totalAmount) {
          warningMessage = '通用赠金不能超过应付金额';
        }
        break;

      case 'roomBonusAmount':
        // 包厢赠金：不能超过包厢赠金余额，也不能超过包厢费用
        const roomBonusBalance = this.state.memberCardDetail.roomBonusBalance / 100;
        const roomAmount = this.state.roomAmount / 100;
        maxAllowed = Math.min(roomBonusBalance, roomAmount);

        if (value > roomBonusBalance) {
          warningMessage = '包厢赠金余额不足';
        } else if (value > roomAmount) {
          warningMessage = '包厢赠金不能超过包厢费用';
        }
        break;

      case 'goodsBonusAmount':
        // 商品赠金：不能超过商品赠金余额，也不能超过商品费用
        const goodsBonusBalance = this.state.memberCardDetail.goodsBonusBalance / 100;
        const goodsAmount = this.state.goodsAmount / 100;
        maxAllowed = Math.min(goodsBonusBalance, goodsAmount);

        if (value > goodsBonusBalance) {
          warningMessage = '商品赠金余额不足';
        } else if (value > goodsAmount) {
          warningMessage = '商品赠金不能超过商品费用';
        }
        break;
    }

    // 如果超出限制，调整到最大允许值并显示警告
    if (value > maxAllowed) {
      this.state[field] = maxAllowed.toFixed(2);
      ElMessage.warning(warningMessage);

      console.log(`${field} 输入验证:`, {
        输入值: value,
        最大允许值: maxAllowed,
        调整后值: this.state[field],
        警告: warningMessage
      });
    }
  }

  /**
   * 验证输入并重新平衡金额分配
   * @param field 发生变化的字段
   */
  private validateAndRebalance(field: keyof Pick<IMemberCardPayState, 'cashAmount' | 'commonBonusAmount' | 'roomBonusAmount' | 'goodsBonusAmount'>) {
    if (!this.state.memberCardDetail) return;

    // 首先验证当前输入字段的基本规则
    this.validateInputAmount(field);

    // 然后进行智能重新分配，保持当前输入字段不变
    this.smartRebalanceKeepCurrent(field);
  }

  /**
   * 智能重新分配金额，保持当前输入字段不变
   * @param currentField 当前正在输入的字段，此字段不会被修改
   */
  private smartRebalanceKeepCurrent(
    currentField: keyof Pick<IMemberCardPayState, 'cashAmount' | 'commonBonusAmount' | 'roomBonusAmount' | 'goodsBonusAmount'>
  ) {
    if (!this.state.memberCardDetail) return;

    const memberCard = this.state.memberCardDetail;
    const totalAmountInFen = this.computed.totalAmount.value;
    const roomAmountInFen = this.state.roomAmount;
    const goodsAmountInFen = this.state.goodsAmount;

    // 获取当前输入的金额（分）
    let cashAmountInFen = Math.floor((parseFloat(this.state.cashAmount || '0') || 0) * 100);
    let commonBonusInFen = Math.floor((parseFloat(this.state.commonBonusAmount || '0') || 0) * 100);
    let roomBonusInFen = Math.floor((parseFloat(this.state.roomBonusAmount || '0') || 0) * 100);
    let goodsBonusInFen = Math.floor((parseFloat(this.state.goodsBonusAmount || '0') || 0) * 100);

    console.log(`用户在 ${currentField} 字段输入，当前金额:`, {
      现金: (cashAmountInFen / 100).toFixed(2),
      通用赠金: (commonBonusInFen / 100).toFixed(2),
      包厢赠金: (roomBonusInFen / 100).toFixed(2),
      商品赠金: (goodsBonusInFen / 100).toFixed(2)
    });

    // 计算当前总输入金额
    const currentTotalInput = cashAmountInFen + commonBonusInFen + roomBonusInFen + goodsBonusInFen;

    // 如果总输入金额超过应付金额，需要从其他字段削减
    if (currentTotalInput > totalAmountInFen) {
      const excessAmount = currentTotalInput - totalAmountInFen;
      let remainingExcess = excessAmount;

      console.log(`总金额超出 ${excessAmount / 100} 元，需要从其他字段削减，保持 ${currentField} 不变`);

      // 定义削减优先级：现金 -> 通用赠金 -> 包厢赠金 -> 商品赠金
      const reductionPriority: Array<{
        field: keyof Pick<IMemberCardPayState, 'cashAmount' | 'commonBonusAmount' | 'roomBonusAmount' | 'goodsBonusAmount'>;
        currentAmount: () => number;
        setAmount: (amount: number) => void;
        name: string;
      }> = [
        {
          field: 'cashAmount',
          currentAmount: () => cashAmountInFen,
          setAmount: amount => {
            cashAmountInFen = amount;
          },
          name: '现金'
        },
        {
          field: 'commonBonusAmount',
          currentAmount: () => commonBonusInFen,
          setAmount: amount => {
            commonBonusInFen = amount;
          },
          name: '通用赠金'
        },
        {
          field: 'roomBonusAmount',
          currentAmount: () => roomBonusInFen,
          setAmount: amount => {
            roomBonusInFen = amount;
          },
          name: '包厢赠金'
        },
        {
          field: 'goodsBonusAmount',
          currentAmount: () => goodsBonusInFen,
          setAmount: amount => {
            goodsBonusInFen = amount;
          },
          name: '商品赠金'
        }
      ];

      // 按优先级削减，但跳过当前正在输入的字段
      for (const item of reductionPriority) {
        if (remainingExcess <= 0) break;

        // 跳过当前正在输入的字段
        if (item.field === currentField) continue;

        const currentAmount = item.currentAmount();
        if (currentAmount > 0) {
          const reduction = Math.min(remainingExcess, currentAmount);
          item.setAmount(currentAmount - reduction);
          remainingExcess -= reduction;

          console.log(`从 ${item.name} 中削减 ${reduction / 100} 元，剩余 ${(currentAmount - reduction) / 100} 元`);
        }
      }

      // 如果还有剩余多余金额，说明其他字段都为0了，这时需要检查当前字段是否可以调整
      if (remainingExcess > 0) {
        console.log(`其他字段削减完毕，还有 ${remainingExcess / 100} 元多余，需要调整当前输入字段`);

        // 获取当前字段的值并削减
        const currentFieldItem = reductionPriority.find(item => item.field === currentField);
        if (currentFieldItem) {
          const currentAmount = currentFieldItem.currentAmount();
          const reduction = Math.min(remainingExcess, currentAmount);
          currentFieldItem.setAmount(Math.max(0, currentAmount - reduction));

          console.log(`从当前输入字段 ${currentFieldItem.name} 中削减 ${reduction / 100} 元`);
        }
      }
    }

    // 更新状态
    this.state.cashAmount = (cashAmountInFen / 100).toFixed(2);
    this.state.commonBonusAmount = (commonBonusInFen / 100).toFixed(2);
    this.state.roomBonusAmount = (roomBonusInFen / 100).toFixed(2);
    this.state.goodsBonusAmount = (goodsBonusInFen / 100).toFixed(2);

    // 更新会员卡支付金额汇总（在平衡完成后）
    this.state.totalMemberCardPayAmount = cashAmountInFen + commonBonusInFen + roomBonusInFen + goodsBonusInFen;

    console.log('智能分配结果:', {
      现金: this.state.cashAmount,
      通用赠金: this.state.commonBonusAmount,
      包厢赠金: this.state.roomBonusAmount,
      商品赠金: this.state.goodsBonusAmount,
      总计: (this.state.totalMemberCardPayAmount / 100).toFixed(2),
      应付: (totalAmountInFen / 100).toFixed(2)
    });
  }
}

/**
 * 导出组合式函数
 * @returns MemberCardPayDialog视图模型
 */
export function useMemberCardPayDialog(): IMemberCardPayViewModel {
  return new MemberCardPayPresenter();
}
