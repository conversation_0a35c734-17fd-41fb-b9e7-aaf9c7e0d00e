<template>
  <AppDialog
    v-model="dialogVisible"
    title="会员卡支付"
    :closable="true"
    :showFooter="false"
    @open="vm.actions.onDialogOpen"
    @close="handleClose"
    class="!w-[880px]"
    :showHeader="true">
    <div class="flex flex-col">
      <!-- 会员卡展示 -->
      <div class="flex justify-center w-full mb-6">
        <div class="member-card rounded-lg overflow-hidden">
          <div class="card-content">
            <div class="p-[16px] h-full flex flex-col">
              <div class="flex justify-between items-center">
                <span class="text-[#FFFFFF] text-[28px] font-bold">{{ (vm.state.memberCardDetail && vm.state.memberCardDetail.cardNo) || '' }}</span>
              </div>

              <div class="text-[#FFFFFF] text-[16px]">
                {{ (vm.state.memberCardDetail && vm.state.memberCardDetail.name) || '' }}
              </div>

              <div class="mt-auto flex items-baseline">
                <span class="text-[#365a69] text-[16px] opacity-80">余额</span>
                <PriceDisplay :amount-in-fen="vm.state.memberCardDetail && vm.state.memberCardDetail.totalBalance" class="price-display-large ml-[12px]" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-center mb-6">
        <el-button type="normal" plain size="small" class="w-[60px]" @click="vm.actions.changeMemberCard">更换</el-button>
      </div>

      <div class="p-4">
        <!-- 现金余额 -->
        <div class="payment-item">
          <div class="flex justify-between mb-2">
            <span>现金余额（￥{{ formatYuan((vm.state.memberCardDetail && vm.state.memberCardDetail.balance) || 0) }}）</span>
          </div>
          <el-input v-model="vm.state.cashAmount" placeholder="请输入" class="custom-input" @blur="vm.actions.handleCashAmountInput" type="number">
            <template #append>元</template>
          </el-input>
        </div>

        <!-- 通用赠金 -->
        <div class="payment-item">
          <div class="flex justify-between mb-2">
            <span>通用赠金余额（￥{{ formatYuan((vm.state.memberCardDetail && vm.state.memberCardDetail.commonBonusBalance) || 0) }}）</span>
          </div>
          <el-input
            v-model="vm.state.commonBonusAmount"
            placeholder="请输入"
            class="custom-input"
            @blur="vm.actions.handleCommonBonusAmountInput"
            type="number">
            <template #append>元</template>
          </el-input>
        </div>

        <!-- 包厢赠金 -->
        <div class="payment-item">
          <div class="flex justify-between mb-2">
            <span>包厢赠金余额（￥{{ formatYuan((vm.state.memberCardDetail && vm.state.memberCardDetail.roomBonusBalance) || 0) }}）</span>
          </div>
          <el-input v-model="vm.state.roomBonusAmount" placeholder="请输入" class="custom-input" @blur="vm.actions.handleRoomBonusAmountInput" type="number">
            <template #append>元</template>
          </el-input>
        </div>

        <!-- 商品赠金 -->
        <div class="payment-item">
          <div class="flex justify-between mb-2">
            <span>商品赠金余额（￥{{ formatYuan((vm.state.memberCardDetail && vm.state.memberCardDetail.goodsBonusBalance) || 0) }}）</span>
          </div>
          <el-input v-model="vm.state.goodsBonusAmount" placeholder="请输入" class="custom-input" @blur="vm.actions.handleGoodsBonusAmountInput" type="number">
            <template #append>元</template>
          </el-input>
        </div>

        <!-- 会员卡支付金额汇总 -->
        <div class="payment-summary">
          <div class="flex justify-start items-center">
            <span class="text-[#666]">会员卡支付总计</span>
            <PriceDisplay :amount-in-fen="vm.state.totalMemberCardPayAmount" class="ml-[16px] text-[#E9223A]" />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <!-- 底部按钮 -->
      <div class="flex justify-between items-end">
        <!-- 费用明细 -->
        <div class="flex gap-8 items-end">
          <div class="flex flex-col items-center justify-end h-full">
            <span class="text-gray-600 mb-1">应付</span>
            <PriceDisplay :amount-in-fen="vm.computed.totalAmount.value" class="price-display-large" />
          </div>
          <div class="flex flex-col items-center justify-end h-full">
            <span class="text-gray-600">包厢费</span>
            <PriceDisplay :amount-in-fen="vm.state.roomAmount" class="price-display-small" />
          </div>
          <div class="flex flex-col items-center justify-end h-full">
            <span class="text-gray-600">商品费</span>
            <PriceDisplay :amount-in-fen="vm.state.goodsAmount" class="price-display-small" />
          </div>
        </div>
        <div class="flex gap-4">
          <el-button class="btn-default" @click="vm.actions.handleConfirm" :loading="vm.state.loading" :disabled="!vm.computed.canPay.value">
            确认结账
          </el-button>
        </div>
      </div>
    </template>
  </AppDialog>
</template>

<script>
import { computed, watch } from 'vue';
import { useMemberCardPayDialog } from './presenter';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { ElMessage } from 'element-plus';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';

export default {
  components: {
    AppDialog,
    PriceDisplay
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    memberCardId: {
      type: String,
      required: true
    },
    roomAmount: {
      type: Number,
      required: true
    },
    goodsAmount: {
      type: Number,
      required: true
    }
  },
  emits: ['update:modelValue', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const vm = useMemberCardPayDialog();

    // 对话框可见性
    const dialogVisible = computed({
      get: () => props.modelValue,
      set: val => emit('update:modelValue', val)
    });

    // 格式化金额显示，将分转为元并格式化
    const formatYuan = fen => {
      if (fen === undefined || fen === null) return '0.00';
      return (fen / 100).toFixed(2);
    };

    // 监听对话框可见性变化
    watch(
      () => props.modelValue,
      async visible => {
        if (visible) {
          console.log('[MemberCardPayDialog] 对话框打开，开始初始化数据');

          // 设置回调函数
          vm.state.onSuccess = result => {
            console.log('[MemberCardPayDialog] 支付成功，发出confirm事件并关闭对话框:', result);
            emit('confirm', result);
            dialogVisible.value = false;
          };
          vm.state.onCancel = () => {
            console.log('[MemberCardPayDialog] 取消支付，发出cancel事件并关闭对话框');
            emit('cancel');
            dialogVisible.value = false;
          };

          // 初始化数据
          await vm.actions.init(props.memberCardId, props.roomAmount, props.goodsAmount);
        }
      },
      { immediate: true }
    );

    // 关闭对话框处理
    function handleClose() {
      vm.actions.onDialogClose();
      dialogVisible.value = false;
    }

    // 在actions中添加更换会员卡方法
    if (!vm.actions.changeMemberCard) {
      vm.actions.changeMemberCard = () => {
        console.log('更换会员卡');
        ElMessage.info('更换会员卡功能暂未实现');
      };
    }

    return {
      vm,
      dialogVisible,
      handleClose,
      formatYuan
    };
  }
};
</script>

<style scoped>
/* 会员卡样式 */
.member-card {
  position: relative;
  width: 334px;
  height: 180px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  background: url('@/assets/images/membercard_bigbg.png') no-repeat center center;
  background-size: cover;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  margin: 0 auto;
}

.card-content {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 基础样式 - 不使用tailwind */
.balance-item {
  background-color: white;
}

.balance-item:hover {
  background-color: #f0f9ff;
}

.payment-item {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
}

.payment-item span {
  font-size: 14px;
  color: #606266;
}

.payment-summary {
  margin-top: 16px;
  margin-bottom: 12px;
}

.custom-input {
  width: 160px;
  height: 40px;
}

.custom-input :deep(.el-input__inner) {
  height: 40px;
  border-radius: 4px;
}

/* 全局样式修正 */
:deep(.el-input-group__append) {
  background-color: #f3f4f6;
}

:deep(.price-display-red) .price-unit,
:deep(.price-display-red) .price-integer,
:deep(.price-display-red) .price-decimal {
  color: #e9223a !important;
}
</style>
