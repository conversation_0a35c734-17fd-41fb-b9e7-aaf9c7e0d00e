import { memberApi } from '@/modules/member/api/member';
import { MemberCardDetail } from './entity';
import { BaseResponse } from '@/types/baseResponse';

/**
 * 会员卡支付弹窗交互层
 */
export class MemberCardPayInteractor {
  /**
   * 获取会员卡详情
   * @param memberCardId 会员卡ID
   * @returns 会员卡详情
   */
  async getMemberCardDetail(memberCardId: string): Promise<MemberCardDetail | null> {
    try {
      const response = await memberApi.getMemberCardDetail({ id: memberCardId });

      if (response && response.data) {
        // 处理API返回的数组格式数据
        let cardData;

        // 确保处理数组格式的返回数据
        if (Array.isArray(response.data)) {
          if (response.data.length === 0) {
            return null;
          }
          // 使用数组中的第一项
          cardData = response.data[0];
        } else {
          // 兼容直接返回对象的情况
          cardData = response.data;
        }

        // 转换后端数据为前端实体模型
        return {
          cardId: cardData.id || '',
          cardNo: cardData.cardNumber || '',
          name: cardData.name || '',
          phone: cardData.phone || '',
          cardType: cardData.cardLevelName || cardData.levelName || '',
          levelName: cardData.cardLevelName || cardData.levelName || '',
          balance: cardData.principalBalance || cardData.balance || 0, // 现金余额
          roomBonusBalance: cardData.roomBonusBalance || 0, // 包厢赠金余额
          goodsBonusBalance: cardData.goodsBonusBalance || 0, // 商品赠金余额
          commonBonusBalance: cardData.commonBonusBalance || 0, // 通用赠金余额
          totalBalance:
            (cardData.principalBalance || cardData.balance || 0) +
            (cardData.roomBonusBalance || 0) +
            (cardData.goodsBonusBalance || 0) +
            (cardData.commonBonusBalance || 0),
          status: cardData.status || 0
        };
      }
      return null;
    } catch (error) {
      console.error('获取会员卡详情失败:', error);
      throw new Error('获取会员卡详情失败');
    }
  }

  /**
   * 执行会员卡支付
   * @param params 支付参数
   * @returns 支付结果
   */
  async memberCardPay(params: any): Promise<any> {
    try {
      // 这里需要根据实际接口调整
      // TODO: 替换为实际的会员卡支付接口
      const response = await Promise.resolve({
        success: true,
        data: {
          memberCardId: params.memberCardId,
          useCashAmount: params.useCashAmount,
          useCommonBonusAmount: params.useCommonBonusAmount,
          useRoomBonusAmount: params.useRoomBonusAmount,
          useGoodsBonusAmount: params.useGoodsBonusAmount
        }
      });

      return response;
    } catch (error) {
      console.error('会员卡支付失败:', error);
      throw new Error('会员卡支付失败');
    }
  }
}
