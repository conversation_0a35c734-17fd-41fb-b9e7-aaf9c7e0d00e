import request from '@/utils/request';
import { BaseResponse } from '@/types/baseResponse';
import { OrderProductVO, OrderVO, RoomVO, SessionVO, VenueVO } from '@/types/projectobj';

/** 查看退款请求参数 */
interface QueryOrderRefundViewReqDto {
  /** 房间ID */
  roomId: string;
  /** 会话ID */
  sessionId: string;
}

/** 包厢时段VO接口定义 */
interface OrderRoomPlanVO {
  id: string;
  venueId: string;
  roomId: string;
  sessionId: string;
  employeeId: string;
  memberId: string;
  memberCardId: string;
  orderNo: string;
  pId: string;
  roomName: string;
  pricePlanId: string;
  pricePlanName: string;
  startTime: number;
  endTime: number;
  duration: number;
  selectedAreaId: string;
  selectedRoomTypeId: string;
  consumptionMode: string;
  timeChargeMode: string;
  timeChargeType: string;
  isTimeConsume: boolean;
  memberDiscountable: boolean;
  isGift: boolean;
  originalPayAmount: number;
  minimumCharge: number;
  memberPrice: number;
  memberDiscount: number;
  isPriceDiff: boolean;
  isFree: boolean;
  payAmount: number;
  payRoomDiscount: number;
  payRoomDiscountAmount: number;
  ctime: number;
  utime: number;
  state: number;
  version: number;
  unitPrice: number;
  isDiscounted: boolean;
  RefundFee: number;
}

/** 退款查看页面响应 */
interface OrderRefundViewVO {
  /** 订单商品列表 */
  orderProductVOs: OrderProductVO[];
  /** 订单列表 */
  orderVOs: OrderVO[];
  /** 包厢时段列表 */
  orderRoomPlanVOS: OrderRoomPlanVO[];
  /** 房间信息 */
  roomVO: RoomVO;
  /** 会话信息 */
  sessionVO: SessionVO;
  /** 场馆信息 */
  venueVO: VenueVO;
}

/** 加载退款查看页面 */
export function queryOrderRefundView(params: QueryOrderRefundViewReqDto) {
  return request<OrderRefundViewVO>({
    url: '/api/v3/order/refund-view',
    method: 'POST',
    data: params
  });
}

/** 页面退款请求参数 */
interface QueryOrderRefundReqDto {
  /** 员工ID */
  employeeId: string;
  /** 订单商品列表 */
  orderProductVos: OrderProductVO[];
  /** 退款金额 */
  refundAmount: number;
  /** 退款方式 */
  refundWayType: string;
  /** 房间ID */
  roomId: string;
  /** 会话ID */
  sessionId: string;
  /** 场馆ID */
  venueId: string;
}

/** 页面退款响应 */
interface OrderRefundVO {
  refundId: string[];
}

/** 页面退款 */
export function refundOrder(params: QueryOrderRefundReqDto) {
  return request<BaseResponse<OrderRefundVO>>({
    url: '/api/v3/order/refund-do',
    method: 'POST',
    data: params
  });
}

/** 包厢订单退单请求参数 */
interface RoomOrderRefundReqDto {
  /** 员工ID */
  employeeId: string;
  /** 房间ID */
  roomId: string;
  /** 会话ID */
  sessionId: string;
  /** 要退款的订单号列表 */
  orderNos: string[];
  /** 退单备注 */
  remark: string;
  /** 退款方式 */
  refundWayType: 'back' | 'cash';
  /** 退款金额 */
  refundAmount: number;
  /** 场馆ID */
  venueId: string;
}

/** 包厢订单退单响应 */
interface RoomOrderRefundVO {
  /** 退款ID列表 */
  refundId: string[];
}

/** 包厢订单退单 */
export function refundRoomOrder(params: RoomOrderRefundReqDto) {
  return request<BaseResponse<RoomOrderRefundVO>>({
    url: '/api/v3/order/room-fee-refund-do',
    method: 'POST',
    data: params
  });
}

/** 每日已结场次请求参数 */
interface QueryDailyPaiedSessionReqDto {
  /** 场馆ID */
  venueId: string;
}

/** 每日进行中的场次请求参数 */
interface QueryDailyOpeningSessionReqDto {
  /** 场馆ID */
  venueId: string;
}

/** 每日已结场次返回参数 */
interface DailySessionVO {
  startTime: number;
  endTime: number;
  sessionVOs: {
    id: string;
    sessionId: string;
    venueId: string;
    roomId: string;
    startTime: number;
    endTime: number;
    duration: number;
    status: string;
    orderSource: string;
    customerSource: string;
    customerTag: string;
    agentPerson: string;
    dutyPerson: string;
    rankNumber: string;
    minConsume: number;
    roomFee: number;
    supermarketFee: number;
    totalFee: number;
    unpaidAmount: number;
    prePayBalance: number;
    isOpenTableSettled: boolean;
    info: string;
    employeeId: string;
    employeeIdPay: string;
    ctime: number;
    utime: number;
    state: number;
    version: number;
  }[];
}

/** 获取当天已结场次列表 */
export function queryDailyPaiedSession(params: QueryDailyPaiedSessionReqDto) {
  return request<BaseResponse<DailySessionVO>>({
    url: '/api/session/daily-paied-session',
    method: 'POST',
    data: params
  });
}

/** 获取当天进行中的场次列表 */
export function queryDailyOpeningSession(params: QueryDailyOpeningSessionReqDto) {
  return request<BaseResponse<DailySessionVO>>({
    url: '/api/session/daily-opening-session',
    method: 'POST',
    data: params
  });
}
