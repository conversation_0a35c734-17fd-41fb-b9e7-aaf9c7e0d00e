import request from '@/utils/request';
import type { BaseResponse } from '@/types/baseResponse';
import type { ProductVO, ProductTypeAndPackageVO, ProductOrPackageRVO, OrderProductVO } from '@/types/projectobj';
import type { QueryProductReqDto, ProductPackageVO } from '@/api/autoGenerated/shared/types/product';
import type { QueryOrderProductReqDto, SessionOperationVOVoOrderVO } from '@/api/autoGenerated';
import _ from 'lodash';

/** 创建产品请求 */
interface CreateProductRequest {
  /** 场馆ID */
  venueId: string;
  /** 产品名称 */
  name: string;
  /** 产品类型 */
  type: string;
  /** 当前价格 */
  currentPrice: number;
  /** 条形码 */
  barcode: string;
  /** 区域价格 */
  areaPrices: string;
  /** 买赠方案 */
  buyGiftPlan: string;
  /** 时段价格 */
  timeSlotPrices: string;
  /** 可用时段 */
  availableTimeSlots: string;
  /** 可用房型 */
  availableRoomTypes: string;
  /** 可用区域 */
  availableAreas: string;
  /** 配送渠道 */
  distributionChannels: string;
  /** 会员卡支付限制 */
  memberCardPaymentRestrictions: string;
  /** 最小销售数量 */
  minimumSaleQuantity: number;
  /** 是否实价商品 */
  isRealPriceProduct: boolean;
  /** 分类 */
  category: string;
  /** 折扣 */
  discounts: string;
  /** 允许重复购买 */
  allowRepeatBuy: boolean;
  /** 推荐套餐 */
  recommendCombos: string;
  /** 会员卡限制 */
  memberCardLimits: string;
  /** 口味 */
  flavors: string;
  /** 配料 */
  ingredients: string;
  /** 是否显示 */
  isDisplayed: boolean;
  /** 允许员工赠送 */
  allowStaffGift: boolean;
  /** 计入最低消费 */
  countToMinCharge: boolean;
  /** 计入业绩 */
  countToPerformance: boolean;
  /** 是否促销 */
  isPromotion: boolean;
  /** 是否售罄 */
  isSoldOut: boolean;
  /** 允许存酒 */
  allowWineStorage: boolean;
  /** 礼券 */
  giftVoucher: string;
  /** 计算库存 */
  calculateInventory: boolean;
  /** 是否区域特定 */
  isAreaSpecified: boolean;
  /** 选定区域 */
  selectedAreas: string;
  /** 是否房型特定 */
  isRoomTypeSpecified: boolean;
  /** 选定房型 */
  selectedRoomTypes: string;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 描述 */
  description: string;
  /** 图片 */
  image: string;
  /** 低库存阈值 */
  lowStockThreshold: number;
  /** 配送超时时间 */
  deliveryTimeout: number;
  /** 支持外部配送 */
  supportsExternalDelivery: boolean;
  /** 外部配送价格 */
  externalDeliveryPrice: number;
  /** 单位 */
  unit: string;
}

/** 更新产品请求 */
interface UpdateProductRequest extends CreateProductRequest {
  /** 产品ID */
  id: string;
}

interface UpdateProductSoldOutReqDto {
  id: string;
  soldOut: boolean;
}

/** 查询产品列表请求 */
interface QueryProductListRequest {
  /** 产品名称 */
  name?: string;
  /** 产品类型 */
  type?: string;
  /** 最低价格 */
  minPrice?: number;
  /** 最高价格 */
  maxPrice?: number;
  /** 是否实价商品 */
  isRealPriceProduct?: boolean;
  /** 产品类型ID */
  productTypeId?: string;
  /** 产品包类型ID */
  productPackageTypeId?: string;
  /** 页码 */
  page?: number;
  /** 每页大小 */
  pageSize?: number;
}

/** 赠送商品请求参数 */
interface GiftProductRequest {
  /** 员工ID */
  employeeId: string;
  /** 会员ID */
  memberId: string;
  /** 商品信息列表 */
  orderProductVOs: OrderProductVO[];
  /** 原始金额 */
  originalAmount: number;
  /** 支付总金额 */
  payAmount: number;
  /** 房间ID */
  roomId: string;
  /** 场次ID */
  sessionId: string;
  /** 门店ID */
  venueId: string;
}

/**
 * 商品相关接口
 */
export const ProductApi = {
  /**
   * 创建新产品
   * @param params - 创建产品参数
   */
  createProduct: (params: CreateProductRequest): Promise<BaseResponse<ProductVO>> => {
    return request.post('/api/product/create', params);
  },

  /**
   * 更新产品信息
   * @param params - 更新产品参数
   */
  updateProduct: (params: UpdateProductRequest): Promise<BaseResponse<ProductVO>> => {
    return request.post('/api/product/update', params);
  },

  /**
   * 删除产品
   * @param id - 产品ID
   */
  deleteProduct: (id: string): Promise<BaseResponse<void>> => {
    return request.post('/api/product/delete', { id });
  },

  /**
   * 获取产品详情
   * @param id - 产品ID
   */
  getProductDetails: (id: string): Promise<BaseResponse<ProductVO>> => {
    return request.post('/api/product/details', { id });
  },

  /**
   * 查询产品列表
   * @param params - 查询参数
   */
  queryProductList: (params: QueryOrderProductReqDto): Promise<BaseResponse<ProductVO[]>> => {
    return request.post('/api/product/list', params);
  },

  /**
   * 查询商品类型列表视图
   * @param params - 查询参数
   */
  listProductTypes: (params: QueryProductListRequest): Promise<BaseResponse<ProductTypeAndPackageVO>> => {
    return request.post('/api/product/list-types', params);
  },

  /**
   * 根据类型查询商品详情
   * @param params - 查询参数
   */
  queryDetailByType: async (params: QueryProductReqDto): Promise<BaseResponse<ProductOrPackageRVO>> => {
    const response = await request.post('/api/product/query-detail-bytype', params);

    // 拦截处理：如果套餐内任意商品售罄，则套餐也标记为售罄
    if (_.get(response.data, 'productPackageVOs')) {
      const updatedPackageVOs = _.map(response.data.productPackageVOs, (packageVO: ProductPackageVO) => {
        // 使用 lodash 检查套餐内商品是否有售罄的
        const hasAnyProductSoldOut = _.some(packageVO.productVOList, (product: ProductVO) => _.get(product, 'isSoldOut') === true);

        // 如果套餐内有任意商品售罄，则将套餐也标记为售罄
        if (hasAnyProductSoldOut) {
          return _.assign({}, packageVO, { isSoldOut: true });
        }

        return packageVO;
      });

      // 使用 lodash 更新响应数据
      _.set(response.data, 'productPackageVOs', updatedPackageVOs);
    }

    return response as unknown as BaseResponse<ProductOrPackageRVO>;
  },

  /**
   * 产品估清
   * @param params - 更新产品估清状态参数
   */
  updateProductSoldOut: (params: UpdateProductSoldOutReqDto): Promise<BaseResponse<any>> => {
    return request.post('/api/product/sold-out', params);
  },

  /**
   * 赠送商品接口
   * @param params 赠送商品参数
   * @returns 接口返回值
   */
  giftProduct: (params: GiftProductRequest): Promise<BaseResponse<SessionOperationVOVoOrderVO>> => {
    return request.post('/api/v3/order/gift-product', params);
  }
};
