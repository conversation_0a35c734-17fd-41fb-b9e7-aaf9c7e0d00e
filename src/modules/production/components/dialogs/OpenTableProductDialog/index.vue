<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择商品"
    width="90%"
    :close-on-click-modal="false"
    :show-close="true"
    @close="handleClose"
    class="product-dialog"
    :modal-append-to-body="true"
    :append-to-body="true"
    :destroy-on-close="false"
    :lock-scroll="true"
    :z-index="10000">
    <div class="dialog-content bg-gray-100">
      <div class="flex flex-col h-full">
        <div class="flex flex-1 overflow-hidden">
          <!-- 左侧商品选择器 -->
          <ProductSelector @add-to-cart="vm.actions.addToCart" />

          <!-- 右侧购物车 -->
          <aside class="flex flex-col min-w-0 flex-1 bg-white">
            <!-- 点单模式下显示当前包厢信息 -->
            <div v-if="vm.computed.isAdditionalOrder.value" class="bg-white p-4 mb-2 rounded-lg shadow">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="text-lg font-bold">{{ vm.state.currentStage?.roomVO?.name || '--' }}</h3>
                  <p class="text-sm text-gray-600">开台时间: {{ formatStartTime(vm.state.currentStage?.sessionVO?.startTime) }}</p>
                </div>
                <div class="text-right">
                  <p class="text-sm text-gray-600">消费模式: {{ vm.state.currentStage?.sessionVO?.consumptionMode || '--' }}</p>
                  <p class="text-sm text-gray-600">已消费: {{ formatYuanWithSymbol(vm.state.currentStage?.summaryVO?.totalAmount || 0) }}</p>
                </div>
              </div>
            </div>

            <h2 class="text-lg font-semibold p-3 border-b">购物车</h2>
            <div class="flex-1 overflow-y-auto w-full">
              <div class="cart-list">
                <!-- 表头 -->
                <div class="cart-header grid grid-cols-6 bg-gray-50 p-3 font-semibold text-gray-600">
                  <div class="col-span-2">名称</div>
                  <!-- <div>口味</div> -->
                  <div>单价</div>
                  <div>数量</div>
                  <div>金额</div>
                </div>

                <!-- 购物车列表 -->
                <div v-for="row in vm.state.cartItems" :key="row.id" class="cart-item border-b">
                  <!-- 商品主信息 -->
                  <div class="grid grid-cols-6 p-3 items-center">
                    <div class="col-span-2">
                      <div class="flex items-center">
                        <span>{{ row.name }}</span>
                        <el-tag v-if="row.isPackage" size="small" type="info" class="ml-2">套餐</el-tag>
                      </div>
                    </div>
                    <div>{{ row.flavors || '-' }}</div>
                    <div>{{ formatYuanWithSymbol(row.currentPrice) }}</div>
                    <div>
                      <div class="flex items-center">
                        <el-button plain size="small" @click="vm.actions.decreaseQuantity(row)">-</el-button>
                        <span class="mx-2">{{ row.quantity }}</span>
                        <el-button plain size="small" @click="vm.actions.increaseQuantity(row)">+</el-button>
                      </div>
                    </div>
                    <div>{{ formatYuanWithSymbol(row.currentPrice * row.quantity) }}</div>
                  </div>

                  <!-- 套餐明细 -->
                  <div v-if="row.isPackage && row.packageDetail" class="package-detail bg-gray-50 p-3 ml-4 mb-3">
                    <div class="text-sm text-gray-600 mb-2">套餐明细：</div>
                    <!-- 使用格式化后的套餐明细字符串 -->
                    <div class="ml-4 text-sm text-gray-500">
                      {{ row.packageDetail.detailString }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="p-3 border-t">
              <p class="text-right mb-2 text-sm">
                总金额：<span class="font-bold">{{ formatYuanWithSymbol(vm.computed.totalAmountInFen.value) }}</span> (共{{ vm.computed.totalItems.value }}件)
              </p>
              <div class="flex justify-end space-x-2">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm" :loading="vm.state.isSubmitting">
                  {{ vm.computed.isAdditionalOrder.value ? '确认点单' : '确定' }}
                </el-button>
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { formatFullTimeRangeFromUnix } from '@/utils/dateUtils';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import ProductSelector from '@/modules/production/components/ProductSelector.vue';
import { onMounted, ref, computed, watch } from 'vue';

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogId: {
    type: String,
    default: ''
  },
  cartItems: {
    type: Array as () => ICartItem[],
    default: () => []
  },
  currentStage: {
    type: Object,
    default: null
  },
  mode: {
    type: String as () => 'add2room' | 'normal',
    default: 'normal'
  }
});

// 对话框可见性
const dialogVisible = computed({
  get: () => {
    console.log('dialogVisible.get 被调用, 返回:', props.visible);
    // 始终返回 true，确保对话框显示
    return true;
  },
  set: value => {
    console.log('dialogVisible.set 被调用, value:', value);
    emit('update:modelValue', value);
    if (!value) {
      // 如果设置为 false，则触发关闭事件
      emit('close');
    }
  }
});

// 定义事件
const emit = defineEmits(['confirm', 'cancel', 'update', 'close', 'update:modelValue']);

console.log('OpenTableProductDialog 组件初始化, props:', props);

// 使用简化版的 Presenter
const vm = {
  state: {
    cartItems: props.cartItems || [],
    currentStage: props.currentStage,
    packageDialogVisible: false,
    currentPackage: null as any,
    isSubmitting: false
  },
  computed: {
    isAdditionalOrder: { value: props.mode === 'add2room' },
    totalAmountInFen: { value: (props.cartItems || []).reduce((total: number, item: ICartItem) => total + item.currentPrice * item.quantity, 0) },
    totalItems: { value: (props.cartItems || []).reduce((total: number, item: ICartItem) => total + item.quantity, 0) }
  },
  actions: {
    addToCart: (product: ICartItem) => {
      console.log('添加商品到购物车:', product);
      try {
        // 简化版的添加商品逻辑
        const existingItem = vm.state.cartItems.find(item => item.id === product.id);
        if (existingItem) {
          console.log('商品已存在，增加数量:', existingItem);
          existingItem.quantity++;
        } else {
          console.log('添加新商品:', product);
          vm.state.cartItems.push({
            ...product,
            quantity: 1,
            flavors: '',
            unit: product.unit || '份'
          });
        }
        console.log('更新后的购物车:', vm.state.cartItems);
        emit('update', vm.state.cartItems);
      } catch (error) {
        console.error('添加商品到购物车时出错:', error);
      }
    },
    increaseQuantity: (item: ICartItem) => {
      item.quantity++;
      emit('update', vm.state.cartItems);
    },
    decreaseQuantity: (item: ICartItem) => {
      if (item.quantity > 1) {
        item.quantity--;
      } else {
        vm.state.cartItems = vm.state.cartItems.filter(cartItem => cartItem.id !== item.id);
      }
      emit('update', vm.state.cartItems);
    }
  }
};

// 处理确认
const handleConfirm = async () => {
  console.log('OpenTableProductDialog.handleConfirm 被调用');
  console.log('确认选择商品:', vm.state.cartItems);
  console.log('发送 confirm 事件');
  emit('confirm', vm.state.cartItems);
  console.log('设置 dialogVisible.value = false');
  dialogVisible.value = false;
};

// 处理取消
const handleCancel = () => {
  console.log('OpenTableProductDialog.handleCancel 被调用');
  console.log('取消选择商品');
  console.log('发送 cancel 事件');
  emit('cancel');
  console.log('设置 dialogVisible.value = false');
  dialogVisible.value = false;
};

// 处理关闭
const handleClose = () => {
  console.log('OpenTableProductDialog.handleClose 被调用');
  console.log('发送 close 事件');
  emit('close');
  console.log('发送 update:modelValue 事件, value: false');
  emit('update:modelValue', false);
};

// 在组件挂载后执行
onMounted(() => {
  console.log('OpenTableProductDialog 组件已挂载');
  console.log('当前购物车商品:', vm.state.cartItems);
  console.log('当前阶段信息:', vm.state.currentStage);

  // 添加更多调试日志
  console.log('ProductSelector 组件已加载:', !!ProductSelector);
  console.log('formatYuanWithSymbol 函数已加载:', !!formatYuanWithSymbol);
  console.log('formatFullTimeRangeFromUnix 函数已加载:', !!formatFullTimeRangeFromUnix);

  // 确保对话框可见
  console.log('设置对话框可见性为 true');
  emit('update:modelValue', true);

  // 检查对话框元素
  setTimeout(() => {
    const dialog = document.querySelector('.product-dialog');
    console.log('对话框元素:', dialog);
    if (dialog) {
      console.log('对话框样式:', window.getComputedStyle(dialog));
    }

    // 检查商品列表元素
    const productSelector = document.querySelector('.product-selector');
    console.log('商品列表元素:', productSelector);
  }, 100);
});

// 格式化开台时间
const formatStartTime = (timestamp?: number) => {
  if (!timestamp) return '--';
  const endTime = Date.now();
  return formatFullTimeRangeFromUnix(timestamp, endTime);
};

// 定义 ICartItem 接口
interface ICartItem {
  id: string;
  name: string;
  currentPrice: number;
  quantity: number;
  flavors?: string;
  unit: string;
  isPackage?: boolean;
  packageDetail?: any;
}

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newValue, oldValue) => {
    console.log('props.visible 变化:', oldValue, '->', newValue);
    if (newValue) {
      console.log('对话框变为可见');
    } else {
      console.log('对话框变为不可见');
    }
  },
  { immediate: true }
);

defineOptions({
  name: 'OpenTableProductDialog'
});
</script>

<style scoped>
/* 添加样式 */
.product-dialog :deep(.el-dialog__body) {
  padding: 0;
  overflow: hidden;
}

.product-dialog :deep(.el-dialog) {
  margin-top: 5vh !important;
  pointer-events: auto;
}

.product-dialog :deep(.el-dialog__wrapper) {
  pointer-events: auto;
}

.product-dialog :deep(.el-dialog__header) {
  pointer-events: auto;
}

.product-dialog :deep(.el-dialog__footer) {
  pointer-events: auto;
}

.dialog-content {
  height: 80vh;
  width: 100%;
  overflow: hidden;
  background-color: #f3f4f6;
  pointer-events: auto;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-gray-500 {
  color: #6b7280;
}

.border-t {
  border-top: 1px solid #e5e7eb;
}

.pt-1 {
  padding-top: 0.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.cart-list {
  @apply border rounded-lg bg-white;
}

.cart-header {
  @apply border-b;
}

.cart-item:last-child {
  @apply border-b-0;
}

.package-detail {
  @apply rounded-lg border border-gray-100;
}
</style>
