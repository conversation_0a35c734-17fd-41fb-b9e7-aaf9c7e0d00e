import type { ComputedRef } from 'vue';
import type { ProductPackageVO, ProductVO } from '@/types/projectobj';

// 扩展ProductPackageVO接口，添加packageDetail属性
interface ExtendedProductPackageVO extends ProductPackageVO {
  packageDetail?: string | any;
}

// 定义一个可选组类型接口（与presenter中保持一致）
export interface OptionalGroupViewModel {
  name: string;
  optionCount: number;
  optionType: 'by_count' | 'by_plan';
  products: Array<{
    id: string;
    name: string;
    currentPrice: number;
    count: number;
    selected_count: number;
    // 对于by_plan类型，保存原始的count作为基数
    originalCount?: number;
    // 标记是否为by_plan类型的商品
    isPlanProduct?: boolean;
  }>;
}

// 套餐内所选商品项接口
export interface SelectedPackageProduct {
  id: string;
  name: string;
  count: number;
  currentPrice: number;
  groupIndex?: number;
  productIndex?: number;
}

// UI状态接口
export interface IPackageDialogState {
  packageData: ExtendedProductPackageVO | null;
  visible: boolean;
  packageQuantity: number;
  // 跟踪套餐内所选商品的状态
  selectedPackageProducts: SelectedPackageProduct[];
}

// UI计算属性接口
export interface IPackageDialogComputed {
  // 套餐总价
  totalPrice: ComputedRef<number>;
  // 默认商品列表
  defaultProducts: ComputedRef<
    Array<{
      id: string;
      name: string;
      currentPrice: number;
      count: number;
      // 对于by_plan类型，保存原始的count作为基数
      originalCount?: number;
      // 标记是否为by_plan类型的商品
      isPlanProduct?: boolean;
    }>
  >;
  // 可选组列表
  optionalGroups: ComputedRef<Array<OptionalGroupViewModel>>;
  // 已选可选商品列表（用于左侧显示）
  selectedOptionalItems: ComputedRef<Array<OptionalGroupViewModel>>;
  // 验证是否可以提交
  isValid: ComputedRef<boolean>;
  // 计算所有已选商品
  allSelectedProducts: ComputedRef<SelectedPackageProduct[]>;
}

// UI动作接口
export interface IPackageDialogActions {
  updateProductCount(groupIndex: number, productIndex: number, count: number): void;
  // 不再需要传参，直接从内部状态中获取数据
  convertToConfirmData(): ExtendedProductPackageVO;
  // 更新已选商品的状态
  updateSelectedProduct(product: SelectedPackageProduct): void;
  // 移除已选商品
  removeSelectedProduct(productId: string): void;
}

// 总的ViewModel接口
export interface IPackageDialogViewModel {
  state: IPackageDialogState;
  computed: IPackageDialogComputed;
  actions: IPackageDialogActions;
}
