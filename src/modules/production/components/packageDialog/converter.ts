import { ProductPackageVO, ProductVO } from '@/api/autoGenerated/shared/types/product';
import {
  getDefaultByCountSelections,
  getDefaultByPlanSelection,
  isOptionalByCount,
  isOptionalByPlan,
  getNormalizedOptionType,
  type ProductInPackageDefaultSelection,
  type PlanInPackageDefaultSelection
} from '@/utils/productPackageUtils';
import { computed } from 'vue';
import type { ProductPackageVO as ProjectObjProductPackageVO } from '@/types/projectobj';
import { isString, isNumber, isArray, isEmpty, keyBy } from 'lodash';

// 扩展ProductPackageVO接口，添加packageDetail属性
interface ExtendedProductPackageVO extends ProductPackageVO {
  packageDetail?: string | any;
}

interface OptionalGroupProduct {
  id: string;
  name: string;
  currentPrice: number;
  count: number; // 商品单位数量
  selected_count: number; // 选中的数量
}

export class PackageDialogConverter {
  // 计算总价
  calculateTotalPrice(packageData: ExtendedProductPackageVO, quantity: number = 1): number {
    if (!packageData) return 0;

    try {
      // 基础套餐价格，不加入默认商品价格
      let total = (packageData.currentPrice || 0) * quantity;
      // 不计算默认商品，只用套餐本身的价格
      return total;
    } catch (error) {
      return 0;
    }
  }

  // 获取默认商品列表
  getDefaultProducts(packageData: ExtendedProductPackageVO) {
    if (!packageData?.packageProducts) return [];

    try {
      const defaultProducts = JSON.parse(packageData.packageProducts);

      // 检查套餐是否包含 by_plan 类型的可选组
      let hasOptionalByPlan = false;
      if (packageData.optionalGroups) {
        try {
          const optionalGroups = JSON.parse(packageData.optionalGroups);
          hasOptionalByPlan = optionalGroups.some((group: any) => isOptionalByPlan(group));
        } catch (e) {}
      }

      return defaultProducts.map((product: any) => {
        const productInfo = packageData.productVOList?.find(p => p.id === product.id);
        return {
          id: product.id,
          name: productInfo?.name || '',
          currentPrice: productInfo?.currentPrice || 0,
          count: product.count,
          // 对于包含 by_plan 类型可选组的套餐，默认商品也需要显示基数信息
          originalCount: product.count || 1,
          isPlanProduct: hasOptionalByPlan && product.count > 1
        };
      });
    } catch (error) {
      return [];
    }
  }

  // 获取可选组列表
  getOptionalGroups(packageData: ExtendedProductPackageVO) {
    if (!packageData?.optionalGroups) return [];

    // Helper to get product details for default selection logic, matching ProductInPackageDefaultSelection
    const getProductDetailsForDefaultLogic = (productId: string): ProductInPackageDefaultSelection | null => {
      const product = packageData.productVOList?.find(p => p.id === productId);
      // Ensure product and product.id are valid and product.id is a string
      if (product && typeof product.id === 'string') {
        return {
          id: product.id, // Now product.id is confirmed as string
          // Ensure these fields align with your ProductVO or provide fallbacks
          salesVolume: (product as any).salesVolume || 0,
          profitRate: (product as any).profitRate || 0,
          createdAt: (product as any).createdAt || new Date(0).toISOString() // Default to epoch if not present
        };
      }
      return null;
    };

    try {
      const optionalGroupsSource = JSON.parse(packageData.optionalGroups) as any[];

      let savedSelectionsMap: Map<string, { count: number }> = new Map();
      let hasSavedSelections = false;
      if (packageData.packageDetail) {
        try {
          const packageDetail = typeof packageData.packageDetail === 'string' ? JSON.parse(packageData.packageDetail) : packageData.packageDetail;

          if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts) && packageDetail.selectedProducts.length > 0) {
            hasSavedSelections = true;
            packageDetail.selectedProducts.forEach((p: any) => {
              savedSelectionsMap.set(p.id, { count: p.count });
            });
          }
        } catch (e) {}
      }

      return optionalGroupsSource.map((group: any) => {
        const groupItems = group.products || [];

        // Memoized defaults for this group if utils are used
        let defaultByCountIds: string[] | null = null;
        let defaultByPlanId: string | null | undefined = undefined; // undefined means not computed yet

        return {
          name: group.name,
          optionCount: group.optionCount,
          optionType: group.optionType,
          products: groupItems.map((item: any, productIndex: number) => {
            const productInfo = packageData.productVOList?.find(p => p.id === item.id);

            let initialSelectedCount = 0;
            const savedProductState = savedSelectionsMap.get(item.id);

            if (hasSavedSelections && savedProductState) {
              initialSelectedCount = savedProductState.count;
            } else if (!hasSavedSelections && group.defaultSelection) {
              // Use backend default if no saved state
              if (isOptionalByCount(group) && Array.isArray(group.defaultSelection)) {
                if (group.defaultSelection.includes(item.id)) {
                  initialSelectedCount = 1;
                }
              } else if (isOptionalByPlan(group) && typeof group.defaultSelection === 'string') {
                if (item.id === group.defaultSelection) {
                  initialSelectedCount = 1;
                }
              }
            } else if (!hasSavedSelections && !group.defaultSelection) {
              // Fallback to utils if no saved state & no backend default
              if (isOptionalByCount(group)) {
                // 🔥 修复：按照文档规则 - 将所有数量分配给第一个商品
                const requiredCount = group.optionCount || 0;

                if (productIndex === 0 && requiredCount > 0) {
                  // 第一个商品获得所有数量
                  const productCountPerItem = item.count || 1;
                  initialSelectedCount = Math.ceil(requiredCount / productCountPerItem);
                } else {
                  // 其他商品数量为0
                  initialSelectedCount = 0;
                }
              } else if (isOptionalByPlan(group)) {
                // 对于by_plan类型，根据optionCount选择前N个方案
                const planIndex = groupItems.findIndex((p: any) => p.id === item.id);
                if (planIndex >= 0 && planIndex < group.optionCount) {
                  initialSelectedCount = 1;
                } else {
                  initialSelectedCount = 0;
                }
              }
            }

            return {
              id: item.id,
              name: productInfo?.name || item.name || '', // For by_plan, item.name should be the plan name
              currentPrice: productInfo?.currentPrice || item.currentPrice || 0, // For by_plan, this might be plan price or 0
              count: item.count || 1,
              selected_count: initialSelectedCount,
              // 对于by_plan类型，保存原始的count作为基数，用于显示和计算
              originalCount: item.count || 1,
              // 标记是否为by_plan类型的商品
              isPlanProduct: isOptionalByPlan(group)
            };
          })
        };
      });
    } catch (error) {
      return [];
    }
  }

  // 更新商品选择数量
  updateProductCount(packageData: ExtendedProductPackageVO, groupIndex: number, productIndex: number, selected_count: number) {
    if (!packageData?.optionalGroups) return;

    try {
      const optionalGroups = JSON.parse(packageData.optionalGroups);
      const group = optionalGroups[groupIndex];
      const product = group.products[productIndex];

      // 确保selected_count不小于0
      selected_count = Math.max(0, selected_count);

      // 计算当前总数（不包括当前要修改的商品）
      const currentTotal = group.products.reduce((sum: number, p: { count: number; selected_count: any }, idx: number) => {
        if (idx === productIndex) return sum;
        return sum + p.count * (p.selected_count || 0);
      }, 0);

      if (isOptionalByCount(group)) {
        // 对第一个商品进行特殊处理
        if (productIndex === 0) {
          // 计算当前总选择数量（包括自身）
          const totalWithNewValue = currentTotal + selected_count * product.count;

          // 如果总数已经达到最低要求，允许继续增加
          if (totalWithNewValue >= group.optionCount) {
            // 不限制上限
          } else {
            // 未达到最低要求，则必须满足最低要求
            const minRequired = Math.ceil((group.optionCount - currentTotal) / product.count);
            selected_count = Math.max(selected_count, minRequired);
          }
        } else {
          // 非第一个商品的处理逻辑保持不变
          // 计算剩余可选数量
          const remainingCount = group.optionCount - currentTotal;
          const maxAllowed = Math.floor(remainingCount / product.count);
          // 限制不超过最大允许数量
          selected_count = Math.min(selected_count, maxAllowed);
        }
      } else if (isOptionalByPlan(group)) {
        // by_plan只能选择0或1
        selected_count = selected_count > 0 ? 1 : 0;

        if (selected_count === 1) {
          // 计算当前选中的选项数（不包括当前商品）
          const selectedCount = group.products.filter((p: { selected_count: number }, idx: number) => idx !== productIndex && p.selected_count > 0).length;

          if (selectedCount >= group.optionCount) {
            // 如果已达到上限，取消最后一个选择
            for (let i = group.products.length - 1; i >= 0; i--) {
              if (i !== productIndex && group.products[i].selected_count > 0) {
                group.products[i].selected_count = 0;
                break;
              }
            }
          }
        } else {
          // 对于by_plan类型，取消选择是允许的，不需要额外检查
          // 因为用户可能需要重新选择不同的方案组合
        }
      }

      group.products[productIndex].selected_count = selected_count;
      packageData.optionalGroups = JSON.stringify(optionalGroups);
    } catch (error) {}
  }

  // 验证数据
  validate(packageData: ExtendedProductPackageVO): boolean {
    if (!packageData?.optionalGroups) return false;
    try {
      const optionalGroups = JSON.parse(packageData.optionalGroups);

      return optionalGroups.every((group: any) => {
        // 计算所有选中商品的总数
        const totalCount = group.products.reduce((sum: number, p: any) => {
          return sum + p.count * (p.selected_count || 0);
        }, 0);

        if (isOptionalByCount(group)) {
          // by_count: 总数必须大于等于optionCount，允许超出
          return totalCount >= group.optionCount;
        } else if (isOptionalByPlan(group)) {
          // by_plan: 选中的方案数必须等于optionCount，且每个方案的selected_count只能是0或1
          const selectedPlansCount = group.products.filter((p: { selected_count: number }) => p.selected_count > 0).length;
          const isValidSelections = group.products.every((p: { selected_count: number }) => p.selected_count === 0 || p.selected_count === 1);
          return selectedPlansCount === group.optionCount && isValidSelections;
        }
        return false;
      });
    } catch (error) {
      return false;
    }
  }

  // 添加新的转换方法，用于处理确认时的数据格式化
  convertToConfirmData(packageData: ExtendedProductPackageVO, quantity: number): ExtendedProductPackageVO {
    if (!packageData) return packageData;

    try {
      // 创建新对象，避免修改原始数据
      // TODO: 类型匹配问题，需check
      const confirmData = {
        ...packageData,
        quantity,
        currentPrice: packageData.currentPrice,
        // 添加格式化后的套餐明细字符串
        packageDetail: this.formatPackageDetailsToString(packageData),
        optionalGroups: this.formatOptionalGroups(packageData)
      };
      return confirmData;
    } catch (error) {
      return packageData;
    }
  }

  // 格式化可选商品组数据
  private formatOptionalGroups(packageData: ExtendedProductPackageVO): string {
    if (!packageData.optionalGroups) return '';

    try {
      const groups = JSON.parse(packageData.optionalGroups);
      const formattedGroups = groups.map((group: any) => ({
        ...group,
        products: group.products.map((product: any) => {
          const productInfo = packageData.productVOList?.find(p => p.id === product.id);
          return {
            ...product,
            // 确保价格使用 currentPrice
            currentPrice: productInfo?.currentPrice || 0,
            // 只保留必要的选择数量信息
            selected_count: product.selected_count || 0
          };
        })
      }));
      return JSON.stringify(formattedGroups);
    } catch (error) {
      return packageData.optionalGroups;
    }
  }

  // 添加新的方法：格式化套餐明细为字符串
  private formatPackageDetailsToString(packageData: ExtendedProductPackageVO): string {
    try {
      const details: string[] = [];

      // 添加默认商品
      if (packageData.packageProducts && isString(packageData.packageProducts) && !isEmpty(packageData.packageProducts)) {
        const defaultProducts = JSON.parse(packageData.packageProducts);
        if (isArray(defaultProducts) && !isEmpty(defaultProducts)) {
          // 使用 lodash keyBy 优化商品信息查找
          const productInfoMap = keyBy(packageData.productVOList || [], 'id');

          defaultProducts.forEach((product: any) => {
            if (product && isString(product.id) && !isEmpty(product.id)) {
              const productInfo = productInfoMap[product.id];
              const count = isNumber(product.count) && product.count > 0 ? product.count : 1;

              if (productInfo) {
                details.push(`${productInfo.name}x${count}`);
              }
            }
          });
        }
      }

      // 添加已选择的可选商品
      if (packageData.optionalGroups && isString(packageData.optionalGroups) && !isEmpty(packageData.optionalGroups)) {
        const groups = JSON.parse(packageData.optionalGroups);
        if (isArray(groups) && !isEmpty(groups)) {
          // 使用 lodash keyBy 优化商品信息查找
          const productInfoMap = keyBy(packageData.productVOList || [], 'id');

          groups.forEach((group: any) => {
            if (group && isArray(group.products) && !isEmpty(group.products)) {
              group.products.forEach((product: any) => {
                if (product && isString(product.id) && !isEmpty(product.id) && isNumber(product.selected_count) && product.selected_count > 0) {
                  const productInfo = productInfoMap[product.id];

                  // 🔥 修复：根据可选组类型正确计算商品数量
                  let finalCount = product.selected_count;
                  if (isOptionalByPlan(group)) {
                    // by_plan类型：显示数量 = count × selected_count
                    const productCount = isNumber(product.count) && product.count > 0 ? product.count : 1;
                    finalCount = productCount * product.selected_count;
                  }

                  if (productInfo) {
                    details.push(`${productInfo.name}x${finalCount}`);
                  }
                }
              });
            }
          });
        }
      }

      return details.join(', ');
    } catch (error) {
      return '';
    }
  }
}

export function usePackageDialogConverter() {
  return new PackageDialogConverter();
}
