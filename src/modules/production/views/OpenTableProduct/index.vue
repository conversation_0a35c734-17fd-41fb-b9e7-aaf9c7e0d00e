<template>
  <div class="flex flex-col h-full">
    <div class="flex flex-1 overflow-hidden">
      <!-- 左侧商品选择器 -->
      <ProductSelector @add-to-cart="vm.actions.addToCart" />

      <!-- 右侧购物车 -->
      <aside class="flex flex-col min-w-0 flex-1">
        <!-- 点单模式下显示当前包厢信息 -->
        <div v-if="vm.computed.isAdditionalOrder" class="bg-white p-4 mb-2 rounded-lg shadow">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-bold">{{ vm.state.currentStage?.roomVO?.name || '--' }}</h3>
              <p class="text-sm text-gray-600">开台时间: {{ formatFullTimeRangeFromUnix(vm.state.currentStage?.sessionVO?.startTime || 0) }}</p>
            </div>
            <div class="text-right">
              <p class="text-sm text-gray-600">消费模式: {{ vm.state.currentStage?.sessionVO?.consumptionMode || '--' }}</p>
              <p class="text-sm text-gray-600">已消费: {{ formatYuanWithSymbol(vm.state.currentStage?.summaryVO?.totalAmount || 0) }}</p>
            </div>
          </div>
        </div>

        <h2 class="text-lg font-semibold p-3 border-b">购物车</h2>
        <div class="flex-1 overflow-y-auto w-full">
          <div class="cart-list">
            <!-- 表头 -->
            <div class="cart-header grid grid-cols-6 bg-gray-50 p-3 font-semibold text-gray-600">
              <div class="col-span-2">名称</div>
              <!-- <div>口味</div> -->
              <div>单价</div>
              <div>数量</div>
              <div>金额</div>
            </div>

            <!-- 购物车列表 -->
            <div v-for="row in vm.state.cartItems" :key="row.id" class="cart-item border-b">
              <!-- 商品主信息 -->
              <div class="grid grid-cols-6 p-3 items-center">
                <div class="col-span-2">
                  <div class="flex items-center">
                    <span>{{ row.name }}</span>
                    <el-tag v-if="row.isPackage" size="small" type="info" class="ml-2">套餐</el-tag>
                  </div>
                </div>
                <div>{{ row.flavors || '-' }}</div>
                <div>{{ formatYuanWithSymbol(row.currentPrice) }}</div>
                <div>
                  <div class="flex items-center">
                    <el-button plain size="small" @click="vm.actions.decreaseQuantity(row)">-</el-button>
                    <span class="mx-2">{{ row.quantity }}</span>
                    <el-button plain size="small" @click="vm.actions.increaseQuantity(row)">+</el-button>
                  </div>
                </div>
                <div>{{ formatYuanWithSymbol(row.currentPrice * row.quantity) }}</div>
              </div>

              <!-- 套餐明细 -->
              <div v-if="row.isPackage && row.packageDetail" class="package-detail bg-gray-50 p-3 ml-4 mb-3">
                <div class="text-sm text-gray-600 mb-2">套餐明细：</div>
                <!-- 使用格式化后的套餐明细字符串 -->
                <div class="ml-4 text-sm text-gray-500">
                  {{ row.packageDetail.detailString }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="p-3 border-t">
          <p class="text-right mb-2 text-sm">
            总金额：<span class="font-bold">{{ formatYuanWithSymbol(vm.computed.totalAmountInFen.value) }}</span> (共{{ vm.computed.totalItems.value }}件)
          </p>
          <el-button type="primary" class="w-full" @click="vm.actions.handleConfirm" :loading="vm.state.loading">
            {{ vm.computed.isAdditionalOrder ? '确认点单' : '确定' }}
          </el-button>
        </div>
      </aside>
    </div>

    <!-- 添加套餐选择弹窗 -->
    <package-dialog
      v-if="vm.state.packageDialogVisible"
      v-model="vm.state.currentPackage"
      v-model:visible="vm.state.packageDialogVisible"
      @confirm="vm.actions.handlePackageConfirm"
      @cancel="vm.actions.closePackageDialog" />
  </div>
</template>

<script setup lang="ts">
import { formatFullTimeRangeFromUnix } from '@/utils/dateUtils';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import ProductSelector from '@/modules/production/components/ProductSelector.vue';
import PackageDialog from '@/modules/production/components/packageDialog/index.vue';
import { useOpenTableProduct } from './presenter';
import type { IOpenTableProductViewModel } from './viewmodel';

// 使用Presenter
const vm: IOpenTableProductViewModel = useOpenTableProduct();

defineOptions({
  name: 'OpenTableProduct'
});
</script>

<style scoped>
/* 添加样式 */
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-gray-500 {
  color: #6b7280;
}

.border-t {
  border-top: 1px solid #e5e7eb;
}

.pt-1 {
  padding-top: 0.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.cart-list {
  @apply border rounded-lg bg-white;
}

.cart-header {
  @apply border-b;
}

.cart-item:last-child {
  @apply border-b-0;
}

.package-detail {
  @apply rounded-lg border border-gray-100;
}
</style>
