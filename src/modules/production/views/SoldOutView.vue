<template>
  <div class="flex h-full">
    <!-- 左侧分类导航栏 -->
    <div class="w-[176px] h-full flex-shrink-0 border-r border-gray-200 bg-white py-4 px-[28px] overflow-y-auto scrollbar-hide">
      <el-menu :default-active="selectedCategoryId" @select="handleCategorySelect">
        <el-menu-item v-for="category in categories" :key="category.id" :index="category.id">
          {{ category.name }}
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 右侧主内容区 -->
    <div class="flex flex-1 flex-col min-w-0">
      <!-- 顶部搜索筛选区 -->
      <div class="flex items-center gap-4 bg-white p-[24px] border-gray-200">
        <el-input v-model="searchKeyword" placeholder="搜索商品名称" @input="handleSearch" clearable class="!h-[72px] flex-1">
          <template #prefix>
            <el-icon class="text-[20px]">
              <Search />
            </el-icon>
          </template>
        </el-input>

        <div class="flex items-center gap-4">
          <el-select v-model="soldOutStatus" placeholder="沽清状态" @change="handleLocalFilter" class="customer-select">
            <el-option label="全部" value="" />
            <el-option label="未沽清" :value="false" />
            <el-option label="已沽清" :value="true" />
          </el-select>
          <!-- 
          <el-select v-model="selectedWarehouse" placeholder="选择仓库" @change="handleLocalFilter" class="customer-select">
            <el-option label="全部仓库" value="" />
            <el-option v-for="warehouse in warehouses" :key="warehouse.id" :label="warehouse.name" :value="warehouse.id" />
          </el-select> -->
        </div>
      </div>

      <!-- 商品列表区 -->
      <div class="flex-1 px-[24px] overflow-auto">
        <el-table v-loading="loading" :data="processedProducts" stripe class="w-full" height="100%">
          <el-table-column prop="name" label="名称" min-width="200px" />
          <el-table-column prop="typeDisplayName" label="类型" />
          <el-table-column prop="unit" label="单位" />
          <!-- <el-table-column label="库存">
            <template #default="{ row }">
              <span v-if="row.calculateInventory">{{ row.lowStockThreshold }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column> -->
          <el-table-column label="沽清状态">
            <template #default="{ row }">
              <span class="status-tag" :class="{ 'status-tag-active': row.isSoldOut }">
                {{ row.isSoldOut ? '已沽清' : '正常' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="沽清时间" min-width="200px">
            <template #default="{ row }">
              {{ row.soldOutTime ? formatUnixTimestamp(row.soldOutTime) : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" min-width="140px">
            <template #default="{ row }">
              <button class="btn-option" :class="{ 'btn-option-active': row.isSoldOut }" @click="handleSoldOutToggle(row)">
                {{ row.isSoldOut ? '取消沽清' : '沽清' }}
              </button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center mt-4">
          <el-button :loading="loading" @click="loadMore"> 加载更多 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { ProductVO, ProductTypeVO } from '@/types/projectobj';
import { ProductApi } from '../api/product';
import { now10, formatUnixTimestamp } from '@/utils/dateUtils';

// 扩展ProductVO类型
interface ExtendedProductVO extends ProductVO {
  operator?: string;
  warehouseId?: string;
  isPackage?: boolean;
  stock?: number;
  tag?: string;
  price?: string;
  typeDisplayName?: string;
}

// 状态定义
const searchKeyword = ref('');
const selectedWarehouse = ref('');
const selectedCategoryId = ref('');
const soldOutStatus = ref<boolean | string>('');
const loading = ref(false);
const error = ref<string | null>(null);

// 数据列表
const categories = ref<ProductTypeVO[]>([]);
const allProducts = ref<ProductVO[]>([]);
const warehouses = ref([{ id: 'warehouse1', name: '主仓库' }]);

// 商品类型ID和名称的映射表
const categoryMap = ref<Record<string, string>>({});

// 分页参数
const currentPage = ref(1);
const pageSize = ref(100); // 修改为100条记录每页
const totalPages = ref(2); // 最多查询2页数据
const hasMore = ref(true);

// 处理商品列表数据
const processedProducts = computed(() => {
  // 首先过滤掉套餐类型的商品
  let filtered = allProducts.value
    .filter((item: ProductVO) => item.type !== '套餐') // 过滤掉套餐
    .map((item: ProductVO): ExtendedProductVO => {
      // 使用类型断言扩展ProductVO类型
      const extendedItem: ExtendedProductVO = {
        ...item,
        isPackage: false, // 确保不是套餐
        stock: item.calculateInventory ? item.lowStockThreshold : -1,
        tag: item.isPromotion ? '促销' : '',
        price: item.currentPrice.toFixed(2),
        unit: item.unit,
        operator: item.operator || '-',
        warehouseId: item.warehouseId || 'warehouse1', // 添加默认仓库ID
        typeDisplayName: categoryMap.value[item.category || ''] || item.category || '-' // 显示类型名称
      };

      return extendedItem;
    });

  // 本地搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter((p: ExtendedProductVO) => p.name.toLowerCase().includes(keyword) || (p.barcode && p.barcode.toLowerCase().includes(keyword)));
  }

  // 按照分类过滤 - 使用ID进行匹配
  if (selectedCategoryId.value) {
    filtered = filtered.filter((p: ExtendedProductVO) => p.category === selectedCategoryId.value);
  }

  // 按照仓库过滤
  if (selectedWarehouse.value) {
    filtered = filtered.filter((p: ExtendedProductVO) => p.warehouseId === selectedWarehouse.value);
  }

  // 根据沽清状态筛选
  if (soldOutStatus.value !== '') {
    filtered = filtered.filter((p: ExtendedProductVO) => p.isSoldOut === soldOutStatus.value);
  }

  return filtered;
});

// 获取分类列表并建立ID-名称映射
const fetchCategories = async () => {
  try {
    const response = await ProductApi.listProductTypes({});
    if (response.code === 0) {
      // 创建"全部商品"选项，使用完整的ProductTypeVO结构
      const allProductsCategory: ProductTypeVO = {
        id: '',
        name: '全部商品',
        distributionChannels: '',
        isDisplayed: true,
        deliveryTimeout: 0,
        supportsPoints: false,
        ctime: 0,
        customStorageConfig: '',
        isIncludedInDrinkAnalysis: false,
        isKitchenMonitoring: false,
        sortOrder: 0,
        utime: 0,
        version: 0
      };

      // 保存分类列表
      categories.value = [allProductsCategory, ...response.data.productTypeVOs];

      // 创建ID到名称的映射表
      const map: Record<string, string> = {};
      response.data.productTypeVOs.forEach((type: ProductTypeVO) => {
        if (type.id && type.name) {
          map[type.id] = type.name;
        }
      });
      categoryMap.value = map;
      console.log(categoryMap.value);
    } else {
      throw new Error(response.message);
    }
  } catch (err) {
    const errorMsg = '获取分类失败: ' + (err as Error).message;
    error.value = errorMsg;
    ElMessage.error(errorMsg);
  }
};

// 获取商品列表
const fetchProducts = async (isLoadingMore = false) => {
  if (!isLoadingMore) {
    loading.value = true;
    error.value = null;
    allProducts.value = [];
    currentPage.value = 1;
  }

  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: '', // 不使用后端过滤，获取所有数据
      productTypeId: '', // 不使用后端过滤，获取所有数据
      warehouseId: '' // 不使用后端过滤，获取所有数据
    };

    const response = await ProductApi.queryDetailByType(params);
    if (response.code === 0) {
      // 处理商品数据 - 只保留普通商品，过滤掉套餐
      const newProducts = response.data.productVOs || [];

      if (isLoadingMore) {
        // 追加数据
        allProducts.value = [...allProducts.value, ...newProducts];
      } else {
        // 首次加载
        allProducts.value = newProducts;

        // 如果第一页数据满了，自动加载第二页
        if (newProducts.length === pageSize.value && currentPage.value < totalPages.value) {
          currentPage.value++;
          await fetchProducts(true);
        }
      }

      // 更新是否有更多数据的标志
      hasMore.value = currentPage.value < totalPages.value && newProducts.length === pageSize.value;
    }
  } catch (err) {
    const errorMsg = '获取商品列表失败: ' + (err as Error).message;
    error.value = errorMsg;
    ElMessage.error(errorMsg);
  } finally {
    loading.value = false;
  }
};

// 更新沽清状态 - 修复同步问题
const handleSoldOutToggle = async (product: ExtendedProductVO) => {
  try {
    const response = await ProductApi.updateProductSoldOut({
      id: product.id,
      soldOut: !product.isSoldOut
    });
    if (response.code === 0) {
      // 关键修复：在原始数据源中找到对应商品并更新状态
      const originalProduct = allProducts.value.find(p => p.id === product.id);
      if (originalProduct) {
        originalProduct.isSoldOut = !originalProduct.isSoldOut;
        originalProduct.soldOutTime = now10();
        originalProduct.operator = '系统';
      }
      ElMessage.success('操作成功');
    }
  } catch (err) {
    ElMessage.error('更新沽清状态失败: ' + (err as Error).message);
  }
};

// 事件处理
const handleCategorySelect = (categoryId: string) => {
  selectedCategoryId.value = categoryId;
  // 使用本地过滤，不重新请求
  handleLocalFilter();
};

const handleSearch = () => {
  // 使用本地过滤，不重新请求
  handleLocalFilter();
};

const handleLocalFilter = () => {
  // 本地过滤数据，不发送请求
  // 这里可以留空，因为已经通过计算属性processedProducts实现了过滤
};

const loadMore = () => {
  if (!hasMore.value || loading.value) return;
  currentPage.value++;
  fetchProducts(true);
};

// 生命周期
onMounted(() => {
  fetchCategories();
  fetchProducts();
});
</script>

<style scoped>
.status-tag-active {
  color: #e23939;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

:deep(.el-menu) {
  border-right: none;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-menu::-webkit-scrollbar) {
  display: none;
}

:deep(.el-menu-item) {
  color: #666;
  font-size: 16px;
  margin-bottom: 16px;
}

:deep(.el-menu-item:hover) {
  background: #eee;
  color: #999;
  border-radius: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-menu-item.is-active) {
  background: #e23939;
  color: #fff;
  border-radius: 8px;
}

:deep(.el-input__inner) {
  height: 72px;
  font-size: 20px;
}

:deep(.customer-select .el-select__wrapper) {
  height: 72px;
  border-radius: 8px;
  font-size: 20px;
  width: 200px;
}

:deep(.customer-select .el-select-dropdown__item) {
  font-size: 20px;
  height: 48px;
  line-height: 48px;
}

:deep(.customer-select .el-select__placeholder) {
  font-size: 20px;
}

.btn-option {
  border-radius: 8px;
  border: 1px solid #eee;
  width: 140px;
  height: 56px;
  font-size: 20px;
  color: #000;
  background: #fff;
}

.btn-option:hover {
  border: 1px solid #e23939;
  color: #e23939;
}

.btn-option-active {
  border: 1px solid #e23939;
  color: #e23939;
}

.btn-option-active:hover {
  border: 1px solid #eee;
  color: #000;
}
</style>
