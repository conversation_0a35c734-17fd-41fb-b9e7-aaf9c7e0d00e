import { ref, reactive, computed, ComputedRef } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { IRefundDetailViewModel, IRefundDetailState, IRefundDetailComputed, IRefundDetailActions, RefundProductVO, OrderGroup } from './viewmodel';
import { RefundDetailInteractor } from './interactor';
import { RefundDetailViewModelConverter } from './converter';
// 定义初始化参数接口
interface RefundDetailParams {
  sessionId: string;
  roomId: string;
}

// 定义成功回调函数类型
type RefundSuccessCallback = (amount: number) => void;

export class RefundDetailPresenter implements IRefundDetailViewModel {
  // 初始化interactor实例
  private interactor: RefundDetailInteractor = new RefundDetailInteractor();
  // 成功回调
  private onRefundSuccess?: RefundSuccessCallback;

  // 内部状态
  public state: IRefundDetailState = reactive({
    roomInfo: {
      roomName: '',
      orderNo: '',
      startTime: 0,
      endTime: 0
    },
    orderStats: {
      totalOrders: 0,
      productOrders: 0,
      giftOrders: 0,
      unpaidOrders: 0
    },
    amountStats: {
      totalAmount: 0,
      productAmount: 0,
      unpaidAmount: 0
    },
    orderGroups: [],
    refundQuantityMap: new Map<string, number>(),
    remark: '',
    refundDialog: {
      visible: false,
      totalQuantity: 0,
      totalAmount: 0,
      paymentMethod: 'back',
      payAmount: 0
    },
    remarkDialog: {
      visible: false,
      presetOptions: ['客人不要', '更换', '沽清']
    },
    params: {
      sessionId: '',
      roomId: ''
    },
    loading: false
  });

  // 计算属性
  public computed: IRefundDetailComputed = {
    totalRefundAmount: computed(() => {
      let total = 0;
      // 遍历订单组和商品，只计算已结订单的退款金额
      this.state.orderGroups.forEach(group => {
        // 只处理已结订单 ('paid')
        if (group.status === 'paid') {
          group.products.forEach(product => {
            const refundQuantity = this.state.refundQuantityMap.get(product.id) || 0;
            if (refundQuantity > 0) {
              // 确保使用正确的单价计算退款金额
              total += product.payPrice * refundQuantity;
            }
          });
        }
      });
      console.log('[refund totalRefundAmount]', total);
      return total;
    }),

    totalRefundQuantity: computed(() => {
      let total = 0;
      this.state.refundQuantityMap.forEach(quantity => {
        total += quantity;
      });
      return total;
    }),

    canRefund: computed(() => {
      return this.computed.totalRefundQuantity.value > 0;
    }),

    // 解析套餐商品信息
    getPackageProductDetails: computed(() => {
      return (packageProductInfo: string): Array<{ id: string; name: string; count: number; price: number }> => {
        if (!packageProductInfo) return [];

        try {
          const packageProducts = JSON.parse(packageProductInfo);
          return Array.isArray(packageProducts) ? packageProducts : [];
        } catch (error) {
          console.warn('解析套餐商品信息失败:', error);
          return [];
        }
      };
    })
  };

  // 动作实现
  public actions: IRefundDetailActions = {
    init: async () => {
      try {
        this.state.loading = true;
        // 使用传入的参数
        const sessionId = this.state.params.sessionId;
        const roomId = this.state.params.roomId;

        if (!sessionId || !roomId) {
          ElMessage.error('缺少必要参数');
          return;
        }

        // 调用Interactor获取退单数据
        const result = await this.interactor.getRefundDetail(sessionId, roomId);

        if (!result.room || !result.session) {
          ElMessage.error('获取退单数据失败: 缺少必要信息');
          return;
        }

        // 使用Converter处理数据转换
        // 1. 处理包厢信息
        this.state.roomInfo = RefundDetailViewModelConverter.convertBasicInfo(result.room, result.session);

        // 2. 处理订单统计
        this.state.orderStats = RefundDetailViewModelConverter.convertOrderStats(result.orders || []);

        // 3. 创建退款映射
        const refundMap = RefundDetailViewModelConverter.createRefundMap(result.orders || [], result.products || []);

        // 4. 处理金额统计，传入退款映射
        this.state.amountStats = RefundDetailViewModelConverter.convertAmountStats(result.orders || [], result.products || [], refundMap);

        // 5. 处理订单分组
        this.state.orderGroups = RefundDetailViewModelConverter.convertOrderGroups(result.orders || [], result.products || []);
        // 清空之前的选择
        this.state.refundQuantityMap.clear();
        this.state.remark = '';
      } catch (error) {
        console.error('初始化退单数据出错:', error);
        ElMessage.error('加载退单数据失败');
      } finally {
        this.state.loading = false;
      }
    },

    // 调整退单数量
    adjustRefundQuantity: (productId: string, delta: number) => {
      // 查找商品
      let targetProduct: RefundProductVO | null = null;
      // let targetGroup: OrderGroup | null = null; // targetGroup 似乎没用到，可以注释掉

      for (const group of this.state.orderGroups) {
        const product = group.products.find(p => p.id === productId);
        if (product) {
          targetProduct = product;
          // targetGroup = group;
          break;
        }
      }

      if (!targetProduct) return;

      // 如果商品状态是 'refunded' (即可退数量为0)，不允许再调整
      if (targetProduct.statusInOrder === 'refunded' || targetProduct.refundableQuantity <= 0) {
        // 可以选择不提示，或者用更温和的方式
        // ElMessage.info('该商品已全额退款或无剩余可退数量');
        return;
      }

      const currentQuantity = this.state.refundQuantityMap.get(productId) || 0;
      // 使用商品自带的可退数量作为上限
      const maxRefundableQuantity = targetProduct.refundableQuantity;
      // 计算新的退单数量，确保在 [0, maxRefundableQuantity] 范围内
      const newQuantity = Math.max(0, Math.min(currentQuantity + delta, maxRefundableQuantity));

      if (newQuantity !== currentQuantity) {
        this.state.refundQuantityMap.set(productId, newQuantity);
        // 强制更新 Map 以触发响应式
        this.state.refundQuantityMap = new Map(this.state.refundQuantityMap);
      }
    },

    // 全部退款
    refundAll: (productId: string) => {
      // 查找商品
      let targetProduct: RefundProductVO | null = null;
      // let targetGroup: OrderGroup | null = null;

      for (const group of this.state.orderGroups) {
        const product = group.products.find(p => p.id === productId);
        if (product) {
          targetProduct = product;
          // targetGroup = group;
          break;
        }
      }

      if (!targetProduct) return;

      // 如果商品状态是 'refunded' 或可退数量为0，不允许操作
      if (targetProduct.statusInOrder === 'refunded' || targetProduct.refundableQuantity <= 0) {
        // ElMessage.info('该商品已全额退款或无剩余可退数量');
        return;
      }

      // 设置退单数量为最大可退数量
      const maxRefundableQuantity = targetProduct.refundableQuantity;
      this.state.refundQuantityMap.set(productId, maxRefundableQuantity);
      // 强制更新 Map
      this.state.refundQuantityMap = new Map(this.state.refundQuantityMap);
    },

    // 隐藏退单对话框
    hideRefundDialog: () => {
      this.state.refundDialog.visible = false;
    },

    // 处理退单确认（结账弹窗中的确认）
    handleRefundConfirm: async () => {
      try {
        this.state.loading = true; // 开始 loading

        // 创建退款商品映射
        const productMap = new Map<string, { product: RefundProductVO; orderNo: string }>();
        this.state.orderGroups.forEach(group => {
          group.products.forEach(product => {
            productMap.set(product.id, {
              product,
              orderNo: group.orderNo
            });
          });
        });
        const refundProducts = Array.from(this.state.refundQuantityMap.entries())
          .filter(([_, quantity]) => quantity > 0)
          .map(([id, quantity]) => {
            const info = productMap.get(id);
            if (!info) {
              throw new Error(`找不到商品信息: ${id}`);
            }
            return {
              productId: id,
              quantity,
              orderNo: info.orderNo
            };
          });

        const refundAmount = this.computed.totalRefundAmount.value; // 使用计算属性获取应退金额

        await this.interactor.submitRefund({
          roomId: this.state.params.roomId,
          sessionId: this.state.params.sessionId,
          products: refundProducts,
          remark: this.state.remark,
          refundWayType: this.state.refundDialog.paymentMethod,
          refundAmount: refundAmount // 传递计算出的金额
        });

        ElMessage.success('退单成功');
        this.state.refundDialog.visible = false;

        // 调用成功回调，通知 View 关闭主弹窗
        this.onRefundSuccess?.(refundAmount);

        // 刷新数据
        await this.actions.init();
      } catch (error) {
        console.error('退单确认失败:', error);
        ElMessage.error(error instanceof Error ? error.message : '退单失败');
      } finally {
        this.state.loading = false; // 结束 loading
      }
    },

    // 显示备注对话框
    showRemarkDialog: () => {
      this.state.remarkDialog.visible = true;
    },

    // 隐藏备注对话框
    hideRemarkDialog: () => {
      this.state.remarkDialog.visible = false;
    },

    // 确认备注
    confirmRemark: () => {
      this.state.remarkDialog.visible = false;
    },

    // 新增：处理预制备注点击
    handlePresetRemark: (option: string) => {
      const currentRemark = this.state.remark.trim();
      if (currentRemark) {
        // 如果已有备注，用逗号分隔
        this.state.remark = `${currentRemark}, ${option}`;
      } else {
        // 如果没有备注，直接添加
        this.state.remark = option;
      }
    },

    // 点击主弹窗"确认退单"按钮的逻辑入口
    showRefundDialog: () => {
      const totalQuantity = this.computed.totalRefundQuantity.value;
      const totalAmount = this.computed.totalRefundAmount.value; // 使用区分支付状态的计算属性

      if (totalQuantity <= 0) {
        ElMessage.warning('请先选择要退款的商品和数量');
        return;
      }

      // 如果应退金额为 0 (全是未结订单商品)，直接调用无金额退款逻辑
      if (totalAmount === 0) {
        this.actions.handleDirectRefund();
      } else {
        // 否则，显示退款结账弹窗
        this.state.refundDialog.totalQuantity = totalQuantity;
        this.state.refundDialog.totalAmount = totalAmount;
        this.state.refundDialog.payAmount = totalAmount; // 默认支付金额为应退金额
        this.state.refundDialog.paymentMethod = 'back'; // 默认原路返回
        this.state.refundDialog.visible = true;
      }
    },

    // 新增：处理无金额退款 (直接调用接口)
    handleDirectRefund: async () => {
      try {
        this.state.loading = true; // 开始 loading

        // 创建退款商品映射
        const productMap = new Map<string, { product: RefundProductVO; orderNo: string }>();
        this.state.orderGroups.forEach(group => {
          group.products.forEach(product => {
            productMap.set(product.id, {
              product,
              orderNo: group.orderNo
            });
          });
        });
        const refundProducts = Array.from(this.state.refundQuantityMap.entries())
          .filter(([_, quantity]) => quantity > 0)
          .map(([id, quantity]) => {
            const info = productMap.get(id);
            if (!info) {
              throw new Error(`找不到商品信息: ${id}`);
            }
            return {
              productId: id,
              quantity,
              orderNo: info.orderNo
            };
          });

        // 调用退款接口，金额为0，方式可约定为 cash 或其他
        await this.interactor.submitRefund({
          roomId: this.state.params.roomId,
          sessionId: this.state.params.sessionId,
          products: refundProducts,
          remark: this.state.remark,
          refundWayType: 'cash', // 对于0元退款，方式可能不重要，或与后端约定
          refundAmount: 0 // 金额为0
        });

        ElMessage.success('退单成功 (未结商品)');

        // 调用成功回调，通知 View 关闭主弹窗
        this.onRefundSuccess?.(0); // 传递金额 0

        // 刷新数据
        await this.actions.init();
      } catch (error) {
        console.error('直接退单失败:', error);
        ElMessage.error(error instanceof Error ? error.message : '退单失败');
      } finally {
        this.state.loading = false; // 结束 loading
      }
    }
  };

  // 构造函数 - 接受回调
  constructor(params?: RefundDetailParams, onRefundSuccess?: RefundSuccessCallback) {
    // 初始化参数
    if (params) {
      this.state.params.sessionId = params.sessionId;
      this.state.params.roomId = params.roomId;
    }
    this.onRefundSuccess = onRefundSuccess; // 保存回调

    // 在构造函数中进行初始化
    setTimeout(() => {
      this.actions.init();
    }, 0);
  }
}

// 导出组合式函数 - 传递回调
export function useRefundDetail(params?: RefundDetailParams, onRefundSuccess?: RefundSuccessCallback): IRefundDetailViewModel {
  const presenter = new RefundDetailPresenter(params, onRefundSuccess);
  return presenter;
}
