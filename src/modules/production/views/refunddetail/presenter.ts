import { ref, reactive, computed, ComputedRef } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { IRefundDetailViewModel, IRefundDetailState, IRefundDetailComputed, IRefundDetailActions, RefundProductVO, OrderGroup } from './viewmodel';
import { RefundDetailInteractor } from './interactor';
import { RefundDetailViewModelConverter } from './converter';
// 定义初始化参数接口
interface RefundDetailParams {
  sessionId: string;
  roomId: string;
}

// 定义成功回调函数类型
type RefundSuccessCallback = (amount: number) => void;

export class RefundDetailPresenter implements IRefundDetailViewModel {
  // 初始化interactor实例
  private interactor: RefundDetailInteractor = new RefundDetailInteractor();
  // 成功回调
  private onRefundSuccess?: RefundSuccessCallback;

  // 内部状态
  public state: IRefundDetailState = reactive({
    // Tab状态
    activeTab: 'product',

    roomInfo: {
      roomName: '',
      orderNo: '',
      startTime: 0,
      endTime: 0
    },
    orderStats: {
      totalOrders: 0,
      productOrders: 0,
      giftOrders: 0,
      unpaidOrders: 0
    },
    amountStats: {
      totalAmount: 0,
      productAmount: 0,
      unpaidAmount: 0
    },

    // 商品订单相关
    orderGroups: [],
    refundQuantityMap: new Map<string, number>(),

    // 包厢订单相关
    roomOrders: [],
    selectedRoomOrders: new Set<string>(),

    remark: '',
    refundDialog: {
      visible: false,
      totalQuantity: 0,
      totalAmount: 0,
      paymentMethod: 'back',
      payAmount: 0
    },
    roomRefundDialog: {
      visible: false,
      totalAmount: 0,
      paymentMethod: 'back'
    },
    remarkDialog: {
      visible: false,
      presetOptions: ['客人不要', '更换', '沽清']
    },
    params: {
      sessionId: '',
      roomId: ''
    },
    loading: false
  });

  // 计算属性
  public computed: IRefundDetailComputed = {
    totalRefundAmount: computed(() => {
      let total = 0;
      // 遍历订单组和商品，只计算已结订单的退款金额
      this.state.orderGroups.forEach(group => {
        // 只处理已结订单 ('paid')
        if (group.status === 'paid') {
          group.products.forEach(product => {
            const refundQuantity = this.state.refundQuantityMap.get(product.id) || 0;
            if (refundQuantity > 0) {
              // 确保使用正确的单价计算退款金额
              total += product.payPrice * refundQuantity;
            }
          });
        }
      });
      console.log('[refund totalRefundAmount]', total);
      return total;
    }),

    totalRefundQuantity: computed(() => {
      let total = 0;
      this.state.refundQuantityMap.forEach(quantity => {
        total += quantity;
      });
      return total;
    }),

    canRefund: computed(() => {
      return this.computed.totalRefundQuantity.value > 0;
    }),

    // 包厢订单相关计算属性
    totalRoomRefundAmount: computed(() => {
      let total = 0;
      this.state.selectedRoomOrders.forEach(orderNo => {
        const order = this.state.roomOrders.find(o => o.orderNo === orderNo);
        // 只有已付订单才计入实退金额，与商品退单逻辑保持一致
        if (order && order.status === 'paid') {
          total += order.totalAmount;
        }
      });
      console.log('[room totalRoomRefundAmount]', total);
      return total;
    }),

    canRoomRefund: computed(() => {
      return this.state.selectedRoomOrders.size > 0;
    }),

    // 解析套餐商品信息
    getPackageProductDetails: computed(() => {
      return (packageProductInfo: string): Array<{ id: string; name: string; count: number; price: number }> => {
        if (!packageProductInfo) return [];

        try {
          const packageProducts = JSON.parse(packageProductInfo);
          return Array.isArray(packageProducts) ? packageProducts : [];
        } catch (error) {
          console.warn('解析套餐商品信息失败:', error);
          return [];
        }
      };
    })
  };

  // 动作实现
  public actions: IRefundDetailActions = {
    // Tab切换
    switchTab: (tab: 'product' | 'room') => {
      this.state.activeTab = tab;
    },

    // 商品订单操作
    init: async () => {
      try {
        this.state.loading = true;
        // 使用传入的参数
        const sessionId = this.state.params.sessionId;
        const roomId = this.state.params.roomId;

        if (!sessionId || !roomId) {
          ElMessage.error('缺少必要参数');
          return;
        }

        // 调用Interactor获取退单数据
        const result = await this.interactor.getRefundDetail(sessionId, roomId);

        if (!result.room || !result.session) {
          ElMessage.error('获取退单数据失败: 缺少必要信息');
          return;
        }

        // 使用Converter处理数据转换
        // 1. 处理包厢信息
        this.state.roomInfo = RefundDetailViewModelConverter.convertBasicInfo(result.room, result.session);

        // 2. 处理订单统计
        this.state.orderStats = RefundDetailViewModelConverter.convertOrderStats(result.orders || []);

        // 3. 创建退款映射
        const refundMap = RefundDetailViewModelConverter.createRefundMap(result.orders || [], result.products || []);

        // 4. 处理金额统计，传入退款映射
        this.state.amountStats = RefundDetailViewModelConverter.convertAmountStats(result.orders || [], result.products || [], refundMap);

        // 5. 处理订单分组
        this.state.orderGroups = RefundDetailViewModelConverter.convertOrderGroups(result.orders || [], result.products || []);

        // 6. 处理包厢订单数据
        this.state.roomOrders = RefundDetailViewModelConverter.convertRoomOrders(result.orders || [], result.roomPlans || []);

        // 清空之前的选择
        this.state.refundQuantityMap.clear();
        this.state.selectedRoomOrders.clear();
        this.state.remark = '';
      } catch (error) {
        console.error('初始化退单数据出错:', error);
        ElMessage.error('加载退单数据失败');
      } finally {
        this.state.loading = false;
      }
    },

    // 调整退单数量
    adjustRefundQuantity: (productId: string, delta: number) => {
      // 查找商品
      let targetProduct: RefundProductVO | null = null;
      // let targetGroup: OrderGroup | null = null; // targetGroup 似乎没用到，可以注释掉

      for (const group of this.state.orderGroups) {
        const product = group.products.find(p => p.id === productId);
        if (product) {
          targetProduct = product;
          // targetGroup = group;
          break;
        }
      }

      if (!targetProduct) return;

      // 如果商品状态是 'refunded' (即可退数量为0)，不允许再调整
      if (targetProduct.statusInOrder === 'refunded' || targetProduct.refundableQuantity <= 0) {
        // 可以选择不提示，或者用更温和的方式
        // ElMessage.info('该商品已全额退款或无剩余可退数量');
        return;
      }

      const currentQuantity = this.state.refundQuantityMap.get(productId) || 0;
      // 使用商品自带的可退数量作为上限
      const maxRefundableQuantity = targetProduct.refundableQuantity;
      // 计算新的退单数量，确保在 [0, maxRefundableQuantity] 范围内
      const newQuantity = Math.max(0, Math.min(currentQuantity + delta, maxRefundableQuantity));

      if (newQuantity !== currentQuantity) {
        this.state.refundQuantityMap.set(productId, newQuantity);
        // 强制更新 Map 以触发响应式
        this.state.refundQuantityMap = new Map(this.state.refundQuantityMap);
      }
    },

    // 全部退款
    refundAll: (productId: string) => {
      // 查找商品
      let targetProduct: RefundProductVO | null = null;
      // let targetGroup: OrderGroup | null = null;

      for (const group of this.state.orderGroups) {
        const product = group.products.find(p => p.id === productId);
        if (product) {
          targetProduct = product;
          // targetGroup = group;
          break;
        }
      }

      if (!targetProduct) return;

      // 如果商品状态是 'refunded' 或可退数量为0，不允许操作
      if (targetProduct.statusInOrder === 'refunded' || targetProduct.refundableQuantity <= 0) {
        // ElMessage.info('该商品已全额退款或无剩余可退数量');
        return;
      }

      // 设置退单数量为最大可退数量
      const maxRefundableQuantity = targetProduct.refundableQuantity;
      this.state.refundQuantityMap.set(productId, maxRefundableQuantity);
      // 强制更新 Map
      this.state.refundQuantityMap = new Map(this.state.refundQuantityMap);
    },

    // 隐藏退单对话框
    hideRefundDialog: () => {
      this.state.refundDialog.visible = false;
    },

    // 处理退单确认（结账弹窗中的确认）
    handleRefundConfirm: async () => {
      try {
        this.state.loading = true; // 开始 loading

        // 创建退款商品映射
        const productMap = new Map<string, { product: RefundProductVO; orderNo: string }>();
        this.state.orderGroups.forEach(group => {
          group.products.forEach(product => {
            productMap.set(product.id, {
              product,
              orderNo: group.orderNo
            });
          });
        });
        const refundProducts = Array.from(this.state.refundQuantityMap.entries())
          .filter(([_, quantity]) => quantity > 0)
          .map(([id, quantity]) => {
            const info = productMap.get(id);
            if (!info) {
              throw new Error(`找不到商品信息: ${id}`);
            }
            return {
              productId: id,
              quantity,
              orderNo: info.orderNo
            };
          });

        const refundAmount = this.computed.totalRefundAmount.value; // 使用计算属性获取应退金额

        await this.interactor.submitRefund({
          roomId: this.state.params.roomId,
          sessionId: this.state.params.sessionId,
          products: refundProducts,
          remark: this.state.remark,
          refundWayType: this.state.refundDialog.paymentMethod,
          refundAmount: refundAmount // 传递计算出的金额
        });

        ElMessage.success('退单成功');
        this.state.refundDialog.visible = false;

        // 调用成功回调，通知 View 关闭主弹窗
        this.onRefundSuccess?.(refundAmount);

        // 刷新数据
        await this.actions.init();
      } catch (error) {
        console.error('退单确认失败:', error);
        ElMessage.error(error instanceof Error ? error.message : '退单失败');
      } finally {
        this.state.loading = false; // 结束 loading
      }
    },

    // 显示备注对话框
    showRemarkDialog: () => {
      this.state.remarkDialog.visible = true;
    },

    // 隐藏备注对话框
    hideRemarkDialog: () => {
      this.state.remarkDialog.visible = false;
    },

    // 确认备注
    confirmRemark: () => {
      this.state.remarkDialog.visible = false;
    },

    // 新增：处理预制备注点击
    handlePresetRemark: (option: string) => {
      const currentRemark = this.state.remark.trim();
      if (currentRemark) {
        // 如果已有备注，用逗号分隔
        this.state.remark = `${currentRemark}, ${option}`;
      } else {
        // 如果没有备注，直接添加
        this.state.remark = option;
      }
    },

    // 点击主弹窗"确认退单"按钮的逻辑入口
    showRefundDialog: () => {
      const totalQuantity = this.computed.totalRefundQuantity.value;
      const totalAmount = this.computed.totalRefundAmount.value; // 使用区分支付状态的计算属性

      if (totalQuantity <= 0) {
        ElMessage.warning('请先选择要退款的商品和数量');
        return;
      }

      // 如果应退金额为 0 (全是未结订单商品)，直接调用无金额退款逻辑
      if (totalAmount === 0) {
        this.actions.handleDirectRefund();
      } else {
        // 否则，显示退款结账弹窗
        this.state.refundDialog.totalQuantity = totalQuantity;
        this.state.refundDialog.totalAmount = totalAmount;
        this.state.refundDialog.payAmount = totalAmount; // 默认支付金额为应退金额
        this.state.refundDialog.paymentMethod = 'back'; // 默认原路返回
        this.state.refundDialog.visible = true;
      }
    },

    // 新增：处理无金额退款 (直接调用接口)
    handleDirectRefund: async () => {
      try {
        this.state.loading = true; // 开始 loading

        // 创建退款商品映射
        const productMap = new Map<string, { product: RefundProductVO; orderNo: string }>();
        this.state.orderGroups.forEach(group => {
          group.products.forEach(product => {
            productMap.set(product.id, {
              product,
              orderNo: group.orderNo
            });
          });
        });
        const refundProducts = Array.from(this.state.refundQuantityMap.entries())
          .filter(([_, quantity]) => quantity > 0)
          .map(([id, quantity]) => {
            const info = productMap.get(id);
            if (!info) {
              throw new Error(`找不到商品信息: ${id}`);
            }
            return {
              productId: id,
              quantity,
              orderNo: info.orderNo
            };
          });

        // 调用退款接口，金额为0，方式可约定为 cash 或其他
        await this.interactor.submitRefund({
          roomId: this.state.params.roomId,
          sessionId: this.state.params.sessionId,
          products: refundProducts,
          remark: this.state.remark,
          refundWayType: 'cash', // 对于0元退款，方式可能不重要，或与后端约定
          refundAmount: 0 // 金额为0
        });

        ElMessage.success('退单成功 (未结商品)');

        // 调用成功回调，通知 View 关闭主弹窗
        this.onRefundSuccess?.(0); // 传递金额 0

        // 刷新数据
        await this.actions.init();
      } catch (error) {
        console.error('直接退单失败:', error);
        ElMessage.error(error instanceof Error ? error.message : '退单失败');
      } finally {
        this.state.loading = false; // 结束 loading
      }
    },

    // 包厢订单操作
    toggleRoomOrderSelection: (orderNo: string) => {
      const order = this.state.roomOrders.find(o => o.orderNo === orderNo);
      if (!order || !order.canRefund) {
        ElMessage.warning('该订单不可退款');
        return;
      }

      // 获取可退的订单并按开始时间排序
      const refundableOrders = this.state.roomOrders.filter(o => o.canRefund).sort((a, b) => a.earliestStartTime - b.earliestStartTime);

      // 找到当前订单在排序后数组中的索引
      const currentIndex = refundableOrders.findIndex(o => o.orderNo === orderNo);

      if (currentIndex === -1) {
        ElMessage.warning('该订单不可退款');
        return;
      }

      const isCurrentlySelected = this.state.selectedRoomOrders.has(orderNo);

      if (isCurrentlySelected) {
        // 如果当前选中，则取消当前订单及之前的所有已选中订单
        for (let i = 0; i <= currentIndex; i++) {
          this.state.selectedRoomOrders.delete(refundableOrders[i].orderNo);
        }
      } else {
        // 如果当前未选中，则选中当前订单及之后的所有可退订单
        for (let i = currentIndex; i < refundableOrders.length; i++) {
          this.state.selectedRoomOrders.add(refundableOrders[i].orderNo);
        }
      }

      // 强制更新Set以触发响应式
      this.state.selectedRoomOrders = new Set(this.state.selectedRoomOrders);
    },

    selectAllRoomOrders: () => {
      // 获取可退的订单并按开始时间排序
      const refundableOrders = this.state.roomOrders.filter(order => order.canRefund).sort((a, b) => a.earliestStartTime - b.earliestStartTime);

      refundableOrders.forEach(order => {
        this.state.selectedRoomOrders.add(order.orderNo);
      });
      this.state.selectedRoomOrders = new Set(this.state.selectedRoomOrders);
    },

    clearRoomOrderSelection: () => {
      this.state.selectedRoomOrders.clear();
      this.state.selectedRoomOrders = new Set(this.state.selectedRoomOrders);
    },

    showRoomRefundDialog: () => {
      if (this.state.selectedRoomOrders.size === 0) {
        ElMessage.warning('请选择要退款的包厢订单');
        return;
      }

      // 检查选中的订单是否有已支付的
      const selectedOrders = this.state.roomOrders.filter(order => this.state.selectedRoomOrders.has(order.orderNo));

      const hasPaidOrders = selectedOrders.some(order => order.status === 'paid');
      const totalAmount = this.computed.totalRoomRefundAmount.value;

      // 如果没有已支付的订单或退款金额为0，直接退单
      if (!hasPaidOrders || totalAmount === 0) {
        this.actions.handleDirectRoomRefund();
      } else {
        // 有已支付的订单，显示确认对话框
        this.state.roomRefundDialog.totalAmount = totalAmount;
        this.state.roomRefundDialog.paymentMethod = 'back'; // 默认原路返回
        this.state.roomRefundDialog.visible = true;
      }
    },

    hideRoomRefundDialog: () => {
      this.state.roomRefundDialog.visible = false;
    },

    // 新增：处理包厢订单直接退单（无需确认）
    handleDirectRoomRefund: async () => {
      try {
        this.state.loading = true;

        const selectedOrderNos = Array.from(this.state.selectedRoomOrders);
        const selectedOrders = this.state.roomOrders.filter(order => this.state.selectedRoomOrders.has(order.orderNo));

        // 检查是否全部为未支付订单
        const allUnpaid = selectedOrders.every(order => order.status === 'unpaid');
        const totalAmount = this.computed.totalRoomRefundAmount.value;

        // 调用包厢退单API
        await this.interactor.submitRoomRefund({
          roomId: this.state.params.roomId,
          sessionId: this.state.params.sessionId,
          orderNos: selectedOrderNos,
          remark: this.state.remark,
          refundWayType: allUnpaid || totalAmount === 0 ? 'cash' : 'back', // 未支付订单用现金，有金额的用原路返回
          refundAmount: totalAmount
        });

        if (allUnpaid) {
          ElMessage.success('包厢订单退单成功 (未结订单)');
        } else {
          ElMessage.success('包厢订单退单成功');
        }

        // 调用成功回调
        if (this.onRefundSuccess) {
          this.onRefundSuccess(totalAmount);
        }

        // 刷新数据
        await this.actions.init();
      } catch (error) {
        console.error('包厢订单直接退单失败:', error);
        ElMessage.error(error instanceof Error ? error.message : '包厢订单退单失败');
      } finally {
        this.state.loading = false;
      }
    },

    handleRoomRefundConfirm: async () => {
      try {
        this.state.loading = true;

        const selectedOrderNos = Array.from(this.state.selectedRoomOrders);

        // 调用包厢退单API
        await this.interactor.submitRoomRefund({
          roomId: this.state.params.roomId,
          sessionId: this.state.params.sessionId,
          orderNos: selectedOrderNos,
          remark: this.state.remark,
          refundWayType: this.state.roomRefundDialog.paymentMethod,
          refundAmount: this.state.roomRefundDialog.totalAmount
        });

        ElMessage.success('包厢订单退款成功');

        // 关闭对话框
        this.state.roomRefundDialog.visible = false;

        // 调用成功回调
        if (this.onRefundSuccess) {
          this.onRefundSuccess(this.state.roomRefundDialog.totalAmount);
        }
      } catch (error) {
        console.error('包厢订单退款失败:', error);
        ElMessage.error('包厢订单退款失败');
      } finally {
        this.state.loading = false;
      }
    }
  };

  // 构造函数 - 接受回调
  constructor(params?: RefundDetailParams, onRefundSuccess?: RefundSuccessCallback) {
    // 初始化参数
    if (params) {
      this.state.params.sessionId = params.sessionId;
      this.state.params.roomId = params.roomId;
    }
    this.onRefundSuccess = onRefundSuccess; // 保存回调

    // 在构造函数中进行初始化
    setTimeout(() => {
      this.actions.init();
    }, 0);
  }
}

// 导出组合式函数 - 传递回调
export function useRefundDetail(params?: RefundDetailParams, onRefundSuccess?: RefundSuccessCallback): IRefundDetailViewModel {
  const presenter = new RefundDetailPresenter(params, onRefundSuccess);
  return presenter;
}
