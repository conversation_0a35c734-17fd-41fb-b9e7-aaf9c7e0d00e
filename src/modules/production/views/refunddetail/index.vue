<template>
  <app-dialog
    v-model="visible"
    title="包厢退单"
    :fullscreen="false"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @close="handleClose"
    class="refund-detail-dialog !w-[85%] !h-[85%]">
    <!-- Loading 覆盖层 -->
    <div v-if="vm.state.loading" class="loading-overlay">
      <el-icon class="is-loading" :size="30">
        <Loading />
      </el-icon>
      <span class="ml-2">处理中...</span>
    </div>

    <div class="refund-detail h-full flex">
      <!-- 左侧基础信息区 -->
      <div class="w-72 h-full flex-shrink-0 bg-white p-4 border-r overflow-y-auto">
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">包厢信息</div>
          <div class="grid gap-3">
            <div class="flex justify-between">
              <span class="text-gray-600">包厢名称:</span>
              <span>{{ vm.state.roomInfo.roomName }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">开台单号:</span>
              <span>{{ vm.state.roomInfo.orderNo }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">开台时间:</span>
              <span>{{ formatUnixTimestamp(vm.state.roomInfo.startTime) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">关房时间:</span>
              <span>{{ formatUnixTimestamp(vm.state.roomInfo.endTime) }}</span>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <div class="text-lg font-bold mb-4">订单统计</div>
          <div class="grid gap-3">
            <div class="flex justify-between">
              <span class="text-gray-600">订单总数:</span>
              <span>{{ vm.state.orderStats.totalOrders }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">包厢订单:</span>
              <span>{{ vm.state.orderStats.roomOrders }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">商品订单:</span>
              <span>{{ vm.state.orderStats.productOrders }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">未结订单:</span>
              <span>{{ vm.state.orderStats.unpaidOrders }}</span>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <div class="text-lg font-bold mb-4">金额统计</div>
          <div class="grid gap-3">
            <div class="flex justify-between">
              <span class="text-gray-600">总计消费:</span>
              <span>¥{{ (vm.state.amountStats.totalAmount / 100).toFixed(2) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">已结商品:</span>
              <span>¥{{ (vm.state.amountStats.productAmount / 100).toFixed(2) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">未结商品:</span>
              <span>¥{{ (vm.state.amountStats.unpaidAmount / 100).toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div>
          <div class="text-lg font-bold mb-4">备注</div>
          <div class="text-gray-600">{{ vm.state.remark }}</div>
        </div>
      </div>

      <!-- 右侧订单内容区 -->
      <div class="flex-1 h-full flex flex-col">
        <!-- Tab切换区域 -->
        <div class="bg-white px-[32px]">
          <el-tabs v-model="vm.state.activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="商品订单" name="product"></el-tab-pane>
            <el-tab-pane label="包厢订单" name="room"></el-tab-pane>
          </el-tabs>
        </div>

        <!-- 商品订单内容 -->
        <div v-if="vm.state.activeTab === 'product'" class="flex-1 bg-white p-4 overflow-auto">
          <!-- 为每个订单创建单独的表格 -->
          <div v-for="group in vm.state.orderGroups" :key="group.orderNo" class="mb-6">
            <!-- 订单标题 -->
            <div class="flex justify-between items-center mb-2 p-2 bg-gray-50 rounded">
              <div class="flex items-center gap-4">
                <span class="font-bold">订单号: {{ group.orderNo }}</span>
                <span class="text-gray-500">下单时间: {{ formatUnixTimestamp(group.ctime) }}</span>
                <el-tag :type="group.status === 'paid' ? 'success' : 'warning'">
                  {{ group.status === 'paid' ? '已结' : '未结' }}
                </el-tag>
              </div>
            </div>

            <!-- 订单商品列表 -->
            <ElTable :data="group.products" border stripe>
              <!-- 商品信息 -->
              <ElTableColumn prop="productName" label="商品名称" min-width="35%">
                <template #default="{ row }">
                  <div class="flex flex-col">
                    <div class="flex items-center">
                      <span v-if="row.isFree" class="mr-1 text-[#12AA58] text-xs">[免费]</span>
                      <span v-else-if="row.isGift" class="mr-1 text-[#12AA58] text-xs">[赠送]</span>
                      <span>{{ row.productName }}</span>
                    </div>
                    <!-- 套餐明细显示 -->
                    <div v-if="row.packageProductInfo" class="mt-1 text-xs text-gray-500">
                      <div v-for="packageProduct in vm.computed.getPackageProductDetails.value(row.packageProductInfo)" :key="packageProduct.id">
                        • {{ packageProduct.name }} x{{ packageProduct.count }}
                      </div>
                    </div>
                  </div>
                </template>
              </ElTableColumn>

              <!-- 数量 -->
              <ElTableColumn label="数量" min-width="8%" align="center">
                <template #default="{ row }">
                  <!-- 使用displayQuantity显示处理后的数量 -->
                  <div class="flex flex-col items-center">
                    <span>{{ row.displayQuantity }}</span>
                    <!-- 如果有退款记录，显示退款标记 -->
                    <template v-if="row.refundedQuantity > 0">
                      <!-- 原始数量和已退数量提示 (可选) -->
                      <div class="text-gray-400 text-xs mt-1">(原购: {{ row.quantity }}, 已退: {{ row.refundedQuantity }})</div>
                    </template>
                  </div>
                </template>
              </ElTableColumn>

              <ElTableColumn label="单价" align="right" min-width="12%">
                <template #default="{ row }">
                  <div class="flex flex-col">
                    <span>¥{{ (row.payPrice / 100).toFixed(2) }}</span>
                    <span v-if="row.originalPrice !== row.payPrice" class="text-gray-400 text-sm line-through">
                      ¥{{ (row.originalPrice / 100).toFixed(2) }}
                    </span>
                  </div>
                </template>
              </ElTableColumn>

              <ElTableColumn label="消费金额" align="right" min-width="12%">
                <template #default="{ row }">
                  <div class="flex flex-col">
                    <!-- 使用displayQuantity计算金额 -->
                    <span>¥{{ ((row.payPrice * row.displayQuantity) / 100).toFixed(2) }}</span>
                    <span v-if="row.originalPrice !== row.payPrice" class="text-gray-400 text-sm line-through">
                      ¥{{ ((row.originalPrice * row.displayQuantity) / 100).toFixed(2) }}
                    </span>
                  </div>
                </template>
              </ElTableColumn>

              <!-- 状态 -->
              <ElTableColumn label="状态" align="center" min-width="8%">
                <template #default="{ row }">
                  <!-- 查找当前商品所属的订单组 -->
                  <el-tag
                    v-for="orderGroup in vm.state.orderGroups"
                    :key="orderGroup.orderNo"
                    v-show="orderGroup.products.includes(row)"
                    :type="getStatusTagType(row.statusInOrder, orderGroup.status)">
                    {{ getStatusText(row.statusInOrder, orderGroup.status) }}
                  </el-tag>
                </template>
              </ElTableColumn>

              <!-- 退单数量 -->
              <ElTableColumn label="退单数量" min-width="25%" align="center">
                <template #default="{ row }">
                  <div class="flex items-center justify-center">
                    <!-- 如果状态是已退款，或可退数量为0 -->
                    <template v-if="row.statusInOrder === 'refunded' || row.refundableQuantity === 0">
                      <span class="text-gray-400">
                        {{ row.statusInOrder === 'refunded' ? '已退款' : '无剩余可退' }}
                      </span>
                    </template>
                    <template v-else>
                      <div class="flex flex-col">
                        <div class="flex items-center">
                          <ElButton size="small" @click="vm.actions.adjustRefundQuantity(row.id, -1)" :disabled="!vm.state.refundQuantityMap.get(row.id)">
                            -
                          </ElButton>
                          <span class="mx-2 w-10 text-center"> {{ vm.state.refundQuantityMap.get(row.id) || 0 }} / {{ row.refundableQuantity }} </span>
                          <ElButton
                            size="small"
                            @click="vm.actions.adjustRefundQuantity(row.id, 1)"
                            :disabled="(vm.state.refundQuantityMap.get(row.id) || 0) >= row.refundableQuantity">
                            +
                          </ElButton>
                          <ElButton
                            size="small"
                            type="primary"
                            class="ml-2"
                            @click="vm.actions.refundAll(row.id)"
                            :disabled="(vm.state.refundQuantityMap.get(row.id) || 0) >= row.refundableQuantity">
                            全退
                          </ElButton>
                        </div>
                        <div v-if="row.actualPayPrice === 0" class="hidden text-red-400 text-sm mt-1">(未结订单, 退款金额为0)</div>
                      </div>
                    </template>
                  </div>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>

          <!-- 没有订单时显示提示 -->
          <div v-if="vm.state.orderGroups.length === 0" class="text-center py-10 text-gray-500">暂无商品订单数据</div>
        </div>

        <!-- 包厢订单内容 -->
        <div v-if="vm.state.activeTab === 'room'" class="flex-1 bg-white p-4 overflow-auto">
          <!-- 选择提示 -->
          <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
            <i class="el-icon-info mr-2"></i>
            选择说明：选中某个时间段时，将自动选中该时间段之后的所有可退订单；取消选中时，将自动取消该时间段之前的所有已选订单。
          </div>

          <!-- 包厢订单表格 -->
          <ElTable :data="vm.state.roomOrders" stripe>
            <!-- 选择框 -->
            <ElTableColumn width="50" align="center">
              <template #default="{ row }">
                <el-checkbox
                  :model-value="vm.state.selectedRoomOrders.has(row.orderNo)"
                  @change="vm.actions.toggleRoomOrderSelection(row.orderNo)"
                  :disabled="!row.canRefund" />
              </template>
            </ElTableColumn>

            <!-- 订单信息 -->
            <ElTableColumn prop="orderNo" label="订单号" min-width="20%">
              <template #default="{ row }">
                <div class="flex flex-col">
                  <span class="font-medium">{{ row.orderNo }}</span>
                  <span class="text-xs text-gray-500">{{ formatUnixTimestamp(row.ctime) }}</span>
                </div>
              </template>
            </ElTableColumn>

            <!-- 价格方案 -->
            <ElTableColumn prop="pricePlanName" label="价格方案" min-width="12%" />

            <!-- 时间段 -->
            <ElTableColumn label="时间段" min-width="25%" align="center">
              <template #default="{ row }">
                <div class="flex flex-col">
                  <span>{{ formatFullTimeRangeFromUnix(row.earliestStartTime, row.latestEndTime) }}</span>
                </div>
              </template>
            </ElTableColumn>

            <!-- 时长 -->
            <ElTableColumn label="时长" min-width="12%" align="center">
              <template #default="{ row }">
                <span class="text-sm"> {{ row.totalDuration }} 分钟 </span>
              </template>
            </ElTableColumn>

            <!-- 订单金额 -->
            <ElTableColumn label="订单金额" align="right" min-width="12%">
              <template #default="{ row }">
                <span class="font-medium">¥{{ (row.totalAmount / 100).toFixed(2) }}</span>
              </template>
            </ElTableColumn>

            <!-- 订单状态 -->
            <ElTableColumn label="支付状态" align="center" min-width="12%">
              <template #default="{ row }">
                <el-tag :type="row.status === 'paid' ? 'success' : 'warning'">
                  {{ row.status === 'paid' ? '已结' : '未结' }}
                </el-tag>
              </template>
            </ElTableColumn>

            <!-- 不可退原因 -->
            <ElTableColumn label="备注" min-width="15%">
              <template #default="{ row }">
                <span v-if="!row.canRefund" class="text-gray-500 text-[14px]"> 已进入消费时段 </span>
                <span v-else class="text-green-600 text-[14px]"> 未进入消费时段 </span>
              </template>
            </ElTableColumn>
          </ElTable>

          <!-- 没有包厢订单时显示提示 -->
          <div v-if="vm.state.roomOrders.length === 0" class="text-center py-10 text-gray-500">暂无包厢订单数据</div>
        </div>
      </div>

      <!-- 商品退单结账弹窗 -->
      <AppDialog
        :uiType="DialogUIType.CARD"
        v-model="vm.state.refundDialog.visible"
        title="退单结账"
        width="500px"
        :close-on-click-modal="false"
        @close="vm.actions.hideRefundDialog">
        <div class="p-4">
          <div class="flex justify-between mb-4 text-[20px]">
            <span class="w-[120px]">共退商品:</span>
            <span class="text-black font-bold">{{ vm.state.refundDialog.totalQuantity }}件</span>
          </div>
          <div class="flex justify-between mb-4 text-[20px]">
            <span class="w-[120px]">实退金额:</span>
            <span class="text-red-500 font-bold">¥{{ (vm.state.refundDialog.totalAmount / 100).toFixed(2) }}</span>
          </div>
          <div class="flex justify-between items-center mb-4 text-[20px]">
            <span class="w-[120px]">可退金额:</span>
            <span class="text-red-500 font-bold py-[12px]">¥{{ (vm.state.refundDialog.totalAmount / 100).toFixed(2) }}</span>
          </div>
          <div class="flex items-center text-[20px] mt-[20px]">
            <span class="mr-4">支付方式:</span>
            <ElRadioGroup v-model="vm.state.refundDialog.paymentMethod" :disabled="vm.state.loading">
              <ElRadio label="back">原路返回</ElRadio>
              <ElRadio label="cash">现金</ElRadio>
            </ElRadioGroup>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-center gap-12">
            <button
              class="btn-black"
              @click="vm.actions.handleRefundConfirm"
              :disabled="vm.state.loading"
              :class="{ 'opacity-70 cursor-not-allowed': vm.state.loading }">
              <span v-if="vm.state.loading" class="mr-2">
                <i class="el-icon-loading"></i>
              </span>
              {{ vm.state.loading ? '处理中...' : '结账' }}
            </button>
          </div>
        </template>
      </AppDialog>

      <!-- 包厢订单退单结账弹窗 -->
      <AppDialog
        :uiType="DialogUIType.CARD"
        v-model="vm.state.roomRefundDialog.visible"
        title="包厢订单退单结账"
        width="500px"
        :close-on-click-modal="false"
        @close="vm.actions.hideRoomRefundDialog">
        <div class="p-4">
          <div class="flex justify-between mb-4 text-[20px]">
            <span class="w-[120px]">退单数量:</span>
            <span class="text-black font-bold">{{ vm.state.selectedRoomOrders.size }}个订单</span>
          </div>
          <div class="flex justify-between mb-4 text-[20px]">
            <span class="w-[120px]">实退金额:</span>
            <span class="text-red-500 font-bold">¥{{ (vm.state.roomRefundDialog.totalAmount / 100).toFixed(2) }}</span>
          </div>
          <div class="flex items-center text-[20px] mt-[20px]">
            <span class="mr-4">支付方式:</span>
            <ElRadioGroup v-model="vm.state.roomRefundDialog.paymentMethod" :disabled="vm.state.loading">
              <ElRadio label="back">原路返回</ElRadio>
              <ElRadio label="cash">现金</ElRadio>
            </ElRadioGroup>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-center gap-12">
            <button
              class="btn-black"
              @click="vm.actions.handleRoomRefundConfirm"
              :disabled="vm.state.loading"
              :class="{ 'opacity-70 cursor-not-allowed': vm.state.loading }">
              <span v-if="vm.state.loading" class="mr-2">
                <i class="el-icon-loading"></i>
              </span>
              {{ vm.state.loading ? '处理中...' : '结账' }}
            </button>
          </div>
        </template>
      </AppDialog>

      <!-- 添加备注窗 -->
      <AppDialog :uiType="DialogUIType.CARD" v-model="vm.state.remarkDialog.visible" title="填写退单备注">
        <ElInput class="mt-[24px] text-[20px]" v-model="vm.state.remark" type="textarea" :rows="4" placeholder="请输入退单备注" />
        <!-- 预制备注按钮 -->
        <div class="flex gap-2 mt-[24px]">
          <button
            v-for="option in vm.state.remarkDialog.presetOptions"
            :key="option"
            class="btn-light-gray !w-[120px] !h-[40px] text-[14px] px-3 py-1 !text-[#333]"
            @click="vm.actions.handlePresetRemark(option)">
            {{ option }}
          </button>
        </div>
        <template #footer>
          <div class="flex justify-center">
            <button class="btn-black" @click="vm.actions.confirmRemark">确定</button>
          </div>
        </template>
      </AppDialog>
    </div>

    <!-- 自定义底部按钮区域 -->
    <template #footer>
      <!-- 底部退单操作区 -->
      <div class="flex justify-end bg-white p-4 border-t">
        <!-- 左侧信息展示区 -->
        <div class="flex items-end gap-8">
          <!-- 商品订单退单信息 -->
          <template v-if="vm.state.activeTab === 'product'">
            <!-- 退单数量 -->
            <div class="flex items-baseline">
              <span class="text-gray-600 mr-2">退单数量:</span>
              <span class="text-gray-600 font-bold text-[20px]">{{ vm.computed.totalRefundQuantity.value }}件</span>
            </div>

            <!-- 退款金额 -->
            <div class="flex items-baseline">
              <span class="text-gray-600 mr-2">应退金额:</span>
              <PriceDisplay :amount-in-fen="vm.computed.totalRefundAmount.value" />
            </div>

            <!-- 备注按钮 -->
            <button class="btn-light-gray" @click="vm.actions.showRemarkDialog" :disabled="vm.state.loading">备注</button>
            <el-button class="btn-black" :disabled="!vm.computed.canRefund.value || vm.state.loading" :loading="vm.state.loading" @click="handleConfirm">
              确认
            </el-button>
          </template>

          <!-- 包厢订单退单信息 -->
          <template v-if="vm.state.activeTab === 'room'">
            <!-- 退单数量 -->
            <div class="flex items-baseline">
              <span class="text-gray-600 mr-2">退单数量:</span>
              <span class="text-gray-600 font-bold text-[20px]">{{ vm.state.selectedRoomOrders.size }}个订单</span>
            </div>

            <!-- 退款金额 -->
            <div class="flex items-baseline">
              <span class="text-gray-600 mr-2">应退金额:</span>
              <PriceDisplay :amount-in-fen="vm.computed.totalRoomRefundAmount.value" />
            </div>

            <!-- 备注按钮 -->
            <button class="btn-light-gray" @click="vm.actions.showRemarkDialog" :disabled="vm.state.loading">备注</button>
            <el-button
              class="btn-black"
              :disabled="!vm.computed.canRoomRefund.value || vm.state.loading"
              :loading="vm.state.loading"
              @click="handleRoomConfirm">
              确认
            </el-button>
          </template>
        </div>
      </div>
    </template>
  </app-dialog>
</template>

<script setup lang="ts" name="RefundDetailDialog">
import { ref, computed } from 'vue';
import { useRefundDetail } from './presenter';
import type { IRefundDetailViewModel } from './viewmodel';
import { formatUnixTimestamp, formatFullTimeRangeFromUnix } from '@/utils/dateUtils';
import { DialogUIType } from '@/types/dialog';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';

// 新增的对话框属性定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  sessionId: {
    type: String,
    required: true
  },
  roomId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['confirm', 'close', 'cancel']);

// 对话框状态
const visible = ref(props.visible);

// 定义退款成功后的回调处理
const handleRefundSuccess = (amount: number) => {
  emit('confirm', { success: true, totalAmount: amount }); // 发出 confirm 事件
  visible.value = false; // 关闭主对话框
};

// 使用VM，传入必要的参数和成功回调
const vm: IRefundDetailViewModel = useRefundDetail(
  {
    sessionId: props.sessionId,
    roomId: props.roomId
  },
  handleRefundSuccess
); // 传入回调函数

// Tab切换处理
const handleTabClick = (tab: any) => {
  vm.actions.switchTab(tab.props.name);
};

// 确认按钮处理函数 - 商品订单
const handleConfirm = () => {
  vm.actions.showRefundDialog();
};

// 确认按钮处理函数 - 包厢订单
const handleRoomConfirm = () => {
  vm.actions.showRoomRefundDialog();
};

// 取消按钮处理函数
const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

// 关闭处理
const handleClose = () => {
  emit('close');
};

// 状态标签类型处理
const getStatusTagType = (statusInOrder: string, orderStatus: string) => {
  const statusMap = {
    normal: orderStatus === 'paid' ? 'success' : 'warning',
    partial_refunded: 'warning',
    refunded: 'info'
  };
  return statusMap[statusInOrder as keyof typeof statusMap] || 'info';
};

// 状态文本处理
const getStatusText = (statusInOrder: string, orderStatus: string) => {
  const statusMap = {
    normal: orderStatus === 'paid' ? '已结' : '未结',
    partial_refunded: '部分退款',
    refunded: '已退款'
  };
  return statusMap[statusInOrder as keyof typeof statusMap] || '未知';
};

// 添加显示金额的计算属性
const displayPayAmount = computed({
  get: () => vm.state.refundDialog.payAmount / 100,
  set: value => {
    vm.state.refundDialog.payAmount = Math.round(value * 100);
  }
});

// 添加金额变化处理函数
const handlePayAmountChange = (value: number) => {
  if (value > vm.state.refundDialog.totalAmount / 100) {
    vm.state.refundDialog.payAmount = vm.state.refundDialog.totalAmount;
  }
};
</script>

<style scoped>
:deep(.el-table) {
  height: 100% !important;
}

:deep(.el-table__inner-wrapper) {
  height: 100%;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  padding: 12px;
}

:deep(.el-collapse-item__content) {
  padding: 12px;
}

/* 添加样式 */
.is-has-content {
  background-color: rgb(239 246 255);
  border-color: rgb(147 197 253);
  color: rgb(59 130 246);
}

/* 为对话框添加样式 */
.refund-detail-dialog {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

:deep(.el-dialog__body) {
  height: calc(100% - 120px);
  padding: 0;
  overflow: hidden;
}

/* 可以添加一个 loading 覆盖层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  /* 确保在最上层 */
}

:deep(.el-radio__label) {
  font-size: 20px;
}

:deep(.el-tabs__item) {
  font-size: 20px;
  color: #666;
  padding-bottom: 4px;
}
:deep(.el-tabs__item.is-active) {
  color: #e9223a;
}
</style>
