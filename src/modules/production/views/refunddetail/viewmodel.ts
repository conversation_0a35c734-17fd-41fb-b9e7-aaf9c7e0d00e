import { ComputedRef } from 'vue';

// 商品VO类型定义
export interface RefundProductVO {
  id: string;
  productName: string;
  quantity: number; // 原始下单数量
  displayQuantity: number; // 新增：UI显示的数量 (quantity - refundedQuantity)
  unit: string;
  payPrice: number; // 计算出的单价
  originalPrice: number; // 原价
  actualPayPrice: number; // 实际支付单价 (考虑订单支付状态)
  statusInOrder: 'normal' | 'refunded' | 'partial_refunded';
  refundedQuantity: number; // 新增：已退数量
  refundableQuantity: number; // 新增：可退数量
  packageProductInfo: string; // 新增：套餐商品信息（JSON字符串）
  isGift: boolean; // 新增：是否赠送
  isFree: boolean; // 新增：是否免费
}

// 订单分组类型定义
export interface OrderGroup {
  orderNo: string;
  ctime: number;
  status: 'paid' | 'unpaid';
  products: RefundProductVO[];
}

// 订单统计
export interface IOrderStats {
  totalOrders: number; // 总单数
  productOrders: number; // 商品单数
  giftOrders: number; // 赠送单数
  unpaidOrders: number; // 未结单数
}

// 金额统计
export interface IAmountStats {
  totalAmount: number;
  productAmount: number;
  unpaidAmount: number;
}

// 房间信息
export interface IRoomInfo {
  roomName: string;
  orderNo: string;
  startTime: number;
  endTime: number;
}

// 退单对话框状态
export interface IRefundDialogState {
  visible: boolean;
  totalAmount: number;
  totalQuantity: number;
  payAmount: number;
  paymentMethod: 'back' | 'cash';
}

// 备注对话框状态
export interface IRemarkDialogState {
  visible: boolean;
  presetOptions: string[]; // 新增：预制备注选项
}

// 初始化参数
export interface IRefundParams {
  sessionId: string;
  roomId: string;
}

// UI状态
export interface IRefundDetailState {
  roomInfo: IRoomInfo;
  orderStats: IOrderStats;
  amountStats: IAmountStats;
  // 按订单分组的商品列表
  orderGroups: OrderGroup[];
  refundQuantityMap: Map<string, number>;
  refundDialog: IRefundDialogState;
  remarkDialog: IRemarkDialogState;
  remark: string;
  params: IRefundParams;
  loading: boolean;
}

// 计算属性
export interface IRefundDetailComputed {
  totalRefundAmount: ComputedRef<number>;
  totalRefundQuantity: ComputedRef<number>;
  canRefund: ComputedRef<boolean>;
  getPackageProductDetails: ComputedRef<(packageProductInfo: string) => Array<{ id: string; name: string; count: number; price: number }>>;
}

// 操作
export interface IRefundDetailActions {
  init: () => Promise<void>;
  adjustRefundQuantity: (productId: string, delta: number) => void;
  refundAll: (productId: string) => void;
  hideRefundDialog: () => void;
  handleRefundConfirm: () => Promise<void>;
  showRemarkDialog: () => void;
  hideRemarkDialog: () => void;
  confirmRemark: () => void;
  showRefundDialog: () => void;
  handleDirectRefund: () => Promise<void>;
  handlePresetRemark: (option: string) => void;
}

// 总ViewModel
export interface IRefundDetailViewModel {
  state: IRefundDetailState;
  computed: IRefundDetailComputed;
  actions: IRefundDetailActions;
}
