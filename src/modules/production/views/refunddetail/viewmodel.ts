import { ComputedRef } from 'vue';

// 商品VO类型定义
export interface RefundProductVO {
  id: string;
  productName: string;
  quantity: number; // 原始下单数量
  displayQuantity: number; // 新增：UI显示的数量 (quantity - refundedQuantity)
  unit: string;
  payPrice: number; // 计算出的单价
  originalPrice: number; // 原价
  actualPayPrice: number; // 实际支付单价 (考虑订单支付状态)
  statusInOrder: 'normal' | 'refunded' | 'partial_refunded';
  refundedQuantity: number; // 新增：已退数量
  refundableQuantity: number; // 新增：可退数量
  packageProductInfo: string; // 新增：套餐商品信息（JSON字符串）
  isGift: boolean; // 新增：是否赠送
  isFree: boolean; // 新增：是否免费
}

// 包厢订单VO类型定义
export interface RefundRoomOrderVO {
  id: string;
  orderNo: string;
  pricePlanName: string; // 价格方案名称
  totalAmount: number; // 订单总金额
  totalDuration: number; // 总时长（分钟）
  earliestStartTime: number; // 最早开始时间
  latestEndTime: number; // 最晚结束时间
  status: 'paid' | 'unpaid';
  canRefund: boolean; // 是否可以退单
  timePlans: Array<{
    id: string;
    startTime: number;
    endTime: number;
    duration: number;
    payAmount: number;
  }>; // 时间段明细
  ctime: number; // 创建时间
}

// 订单分组类型定义
export interface OrderGroup {
  orderNo: string;
  ctime: number;
  status: 'paid' | 'unpaid';
  products: RefundProductVO[];
}

// 订单统计
export interface IOrderStats {
  totalOrders: number; // 总单数
  productOrders: number; // 商品单数
  roomOrders: number; // 包厢单数
  unpaidOrders: number; // 未结单数
}

// 金额统计
export interface IAmountStats {
  totalAmount: number;
  productAmount: number;
  unpaidAmount: number;
}

// 房间信息
export interface IRoomInfo {
  roomName: string;
  orderNo: string;
  startTime: number;
  endTime: number;
}

// 退单对话框状态
export interface IRefundDialogState {
  visible: boolean;
  totalAmount: number;
  totalQuantity: number;
  payAmount: number;
  paymentMethod: 'back' | 'cash';
}

// 备注对话框状态
export interface IRemarkDialogState {
  visible: boolean;
  presetOptions: string[]; // 新增：预制备注选项
}

// 包厢订单退单对话框状态
export interface IRoomRefundDialogState {
  visible: boolean;
  totalAmount: number;
  paymentMethod: 'back' | 'cash';
}

// 初始化参数
export interface IRefundParams {
  sessionId: string;
  roomId: string;
}

// UI状态
export interface IRefundDetailState {
  // Tab状态
  activeTab: 'product' | 'room'; // 新增：当前激活的tab

  roomInfo: IRoomInfo;
  orderStats: IOrderStats;
  amountStats: IAmountStats;

  // 商品订单相关
  orderGroups: OrderGroup[];
  refundQuantityMap: Map<string, number>;

  // 包厢订单相关
  roomOrders: RefundRoomOrderVO[]; // 新增：包厢订单列表
  selectedRoomOrders: Set<string>; // 新增：选中的包厢订单ID

  refundDialog: IRefundDialogState;
  roomRefundDialog: IRoomRefundDialogState; // 新增：包厢订单退单对话框
  remarkDialog: IRemarkDialogState;
  remark: string;
  params: IRefundParams;
  loading: boolean;
}

// 计算属性
export interface IRefundDetailComputed {
  totalRefundAmount: ComputedRef<number>;
  totalRefundQuantity: ComputedRef<number>;
  canRefund: ComputedRef<boolean>;

  // 包厢订单相关计算属性
  totalRoomRefundAmount: ComputedRef<number>; // 新增：包厢订单退款总金额
  canRoomRefund: ComputedRef<boolean>; // 新增：是否可以退包厢订单

  getPackageProductDetails: ComputedRef<(packageProductInfo: string) => Array<{ id: string; name: string; count: number; price: number }>>;
}

// 操作
export interface IRefundDetailActions {
  // Tab切换
  switchTab: (tab: 'product' | 'room') => void; // 新增：切换tab

  // 商品订单操作
  init: () => Promise<void>;
  adjustRefundQuantity: (productId: string, delta: number) => void;
  refundAll: (productId: string) => void;
  hideRefundDialog: () => void;
  handleRefundConfirm: () => Promise<void>;
  showRemarkDialog: () => void;
  hideRemarkDialog: () => void;
  confirmRemark: () => void;
  showRefundDialog: () => void;
  handleDirectRefund: () => Promise<void>;
  handlePresetRemark: (option: string) => void;

  // 包厢订单操作
  toggleRoomOrderSelection: (orderNo: string) => void; // 新增：切换包厢订单选择状态
  selectAllRoomOrders: () => void; // 新增：全选包厢订单
  clearRoomOrderSelection: () => void; // 新增：清空包厢订单选择
  showRoomRefundDialog: () => void; // 新增：显示包厢订单退单对话框
  hideRoomRefundDialog: () => void; // 新增：隐藏包厢订单退单对话框
  handleDirectRoomRefund: () => Promise<void>; // 新增：直接包厢订单退单（无需确认）
  handleRoomRefundConfirm: () => Promise<void>; // 新增：确认包厢订单退单
}

// 总ViewModel
export interface IRefundDetailViewModel {
  state: IRefundDetailState;
  computed: IRefundDetailComputed;
  actions: IRefundDetailActions;
}
