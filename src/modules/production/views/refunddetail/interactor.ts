import { queryOrderRefundView, refundOrder, refundRoomOrder } from '../../api/refund';
import type { OrderProductVO, OrderVO, RoomVO, SessionVO } from '@/types/projectobj';

export class RefundDetailInteractor {
  // 获取退单详情
  async getRefundDetail(sessionId: string, roomId: string) {
    try {
      // 调用退单查看接口
      console.log('getRefundDetail sessionId:', sessionId);
      const res = await queryOrderRefundView({
        sessionId,
        roomId
      });
      console.log('getRefundDetail res:', res);

      if (!res.data) {
        throw new Error('获取退单详情失败: 无数据');
      }

      const { roomVO, sessionVO, orderVOs, orderProductVOs, orderRoomPlanVOs } = res.data;

      console.log('getRefundDetail response:', {
        roomVO,
        sessionVO,
        orderVOs,
        orderProductVOs,
        orderRoomPlanVOs
      });

      // 只过滤出商品订单，移除包厢订单
      const productOrders = orderVOs.filter(order => order.type === 'product');
      console.log('filteredProductOrders:', productOrders);

      return {
        room: roomVO,
        session: sessionVO,
        orders: orderVOs, // 保留所有订单，由converter进行过滤
        productOrders,
        products: orderProductVOs,
        roomPlans: orderRoomPlanVOs || [] // 新增：包厢时段数据
      };
    } catch (error) {
      console.error('获取退单详情失败:', error);
      throw error;
    }
  }

  // 提交退单
  async submitRefund(params: {
    roomId: string;
    sessionId: string;
    products: Array<{
      productId: string;
      quantity: number;
      orderNo: string;
    }>;
    remark: string;
    refundWayType: 'back' | 'cash';
    refundAmount: number;
  }) {
    try {
      // 构造退款请求参数
      const orderProductVos = params.products.map(p => ({
        id: p.productId,
        quantity: p.quantity,
        orderNo: p.orderNo
      })) as OrderProductVO[];

      const refundParams = {
        orderProductVos,
        refundAmount: params.refundAmount,
        refundWayType: params.refundWayType,
        roomId: params.roomId,
        sessionId: params.sessionId,
        // 增加缺少的必填字段，从localStorage或context中获取
        employeeId: localStorage.getItem('employeeId') || '',
        venueId: localStorage.getItem('venueId') || ''
      };

      const res = await refundOrder(refundParams);

      return res;
    } catch (error) {
      console.error('提交退单失败:', error);
      throw error;
    }
  }

  // 新增：提交包厢订单退单
  async submitRoomRefund(params: {
    roomId: string;
    sessionId: string;
    orderNos: string[]; // 要退款的订单号列表
    remark: string;
    refundWayType: 'back' | 'cash';
    refundAmount: number;
  }) {
    try {
      const refundParams = {
        roomId: params.roomId,
        sessionId: params.sessionId,
        orderNos: params.orderNos,
        remark: params.remark,
        refundWayType: params.refundWayType,
        refundAmount: params.refundAmount,
        // 增加缺少的必填字段，从localStorage或context中获取
        employeeId: localStorage.getItem('employeeId') || '',
        venueId: localStorage.getItem('venueId') || ''
      };

      // 调用包厢订单退单API
      const res = await refundRoomOrder(refundParams);

      return res;
    } catch (error) {
      console.error('提交包厢订单退单失败:', error);
      throw error;
    }
  }
}
