import type { OrderProductVO, OrderVO, RoomVO, SessionVO } from '@/types/projectobj';
import type { IRefundDetailState, OrderGroup, RefundProductVO, IRoomInfo, IAmountStats, RefundRoomOrderVO } from './viewmodel';
import { now13 } from '@/utils/dateUtils';

export class RefundDetailViewModelConverter {
  // 转换基础信息
  static convertBasicInfo(room: RoomVO, session: SessionVO): IRoomInfo {
    return {
      roomName: room.name || '',
      orderNo: session.sessionId || '',
      startTime: session.startTime || 0,
      endTime: session.endTime || 0
    };
  }

  // 转换订单统计
  static convertOrderStats(orders: OrderVO[]): IRefundDetailState['orderStats'] {
    // 过滤出正常订单（排除退款订单）
    const normalOrders = orders.filter(o => o.direction === 'normal');

    // 分别统计商品订单和包厢订单
    const productOrders = normalOrders.filter(o => o.type === 'product');
    const roomOrders = normalOrders.filter(o => o.type === 'roomplan');

    return {
      totalOrders: normalOrders.length, // 总订单数（商品+包厢）
      productOrders: productOrders.length, // 商品订单数
      roomOrders: roomOrders.length, // 包厢订单数
      unpaidOrders: normalOrders.filter(o => o.status === 'unpaid').length // 未结订单数（商品+包厢）
    };
  }

  // 转换金额统计
  static convertAmountStats(orderVOs: OrderVO[], orderProductVOs: OrderProductVO[], refundMap?: Map<string, Map<string, number>>): IAmountStats {
    // 只统计商品订单
    const productOrders = orderVOs.filter(o => o.type === 'product' && o.direction === 'normal');

    // 计算消费总计（所有商品订单的总金额）
    const totalAmount = productOrders.reduce((sum, order) => {
      const orderProducts = orderProductVOs.filter(p => p.orderNo === order.orderNo);
      return sum + orderProducts.reduce((pSum, product) => pSum + (product.payAmount || 0), 0);
    }, 0);

    // 考虑退款影响的商品金额计算
    let productAmount = 0;
    let unpaidAmount = 0;

    for (const order of productOrders) {
      const orderProducts = orderProductVOs.filter(p => p.orderNo === order.orderNo);
      const orderRefundInfo = refundMap?.get(order.orderNo);

      for (const product of orderProducts) {
        // 计算该商品的退款数量 - 使用商品自身的id
        const refundedQuantity = orderRefundInfo?.get(product.id) || 0;
        // 计算实际有效的商品数量 = 原数量 - 已退数量
        const effectiveQuantity = Math.max(0, (product.quantity || 0) - refundedQuantity);
        // 计算单价
        const unitPrice = (product.quantity || 0) > 0 ? Math.round((product.payAmount || 0) / product.quantity) : 0;
        // 计算有效金额 = 单价 * 有效数量
        const effectiveAmount = unitPrice * effectiveQuantity;

        // 根据订单支付状态，累加到对应的金额中
        if (order.status === 'paid') {
          productAmount += effectiveAmount;
        } else {
          // unpaid
          unpaidAmount += effectiveAmount;
        }
      }
    }

    // 如果没有提供退款映射，使用原始金额计算逻辑
    if (!refundMap) {
      // 计算已结商品金额（status为paid的订单中的商品）
      productAmount = productOrders
        .filter(order => order.status === 'paid')
        .reduce((sum, order) => {
          const orderProducts = orderProductVOs.filter(p => p.orderNo === order.orderNo);
          return sum + orderProducts.reduce((pSum, product) => pSum + (product.payAmount || 0), 0);
        }, 0);

      // 计算未结商品金额（status为unpaid的订单中的商品）
      unpaidAmount = productOrders
        .filter(order => order.status === 'unpaid')
        .reduce((sum, order) => {
          const orderProducts = orderProductVOs.filter(p => p.orderNo === order.orderNo);
          return sum + orderProducts.reduce((pSum, product) => pSum + (product.payAmount || 0), 0);
        }, 0);
    }

    return {
      totalAmount,
      productAmount,
      unpaidAmount
    };
  }

  // 转换商品列表（按订单分组）
  static convertOrderGroups(orders: OrderVO[], products: OrderProductVO[]): OrderGroup[] {
    // 1. 过滤出商品订单，只显示正常订单
    const productOrders = orders.filter(o => o.type === 'product' && o.direction === 'normal');

    // 2. 创建退款映射 (原始订单号 -> 退款商品Map<productId, refundedQuantity>)
    const refundMap = this.createRefundMap(orders, products);
    console.log('[refundDetail] refundMap:', refundMap);
    console.log('[refundDetail] products:', products);

    // 3. 按订单分组处理商品
    const orderGroups: OrderGroup[] = [];

    productOrders.forEach(order => {
      // 获取订单关联的商品
      const orderProducts = products.filter(p => p.orderNo === order.orderNo);

      // 如果订单没有商品，跳过
      if (orderProducts.length === 0) return;

      // 获取该订单的退款记录 Map<productId, refundedQuantity>
      const orderRefundInfo = refundMap.get(order.orderNo);

      // 转换商品列表，计算退款状态
      const convertedProducts: RefundProductVO[] = orderProducts.map(product => {
        // 从 refundMap 获取该商品已退的数量 - 使用商品自身的id
        const refundedQuantity = orderRefundInfo?.get(product.id) || 0;
        // 计算可退数量
        const refundableQuantity = Math.max(0, (product.quantity || 0) - refundedQuantity);
        // 计算显示数量（原始数量 - 已退数量）
        const displayQuantity = Math.max(0, (product.quantity || 0) - refundedQuantity);

        console.log('[Debug] 转换商品', product.productName, '商品ID:', product.id, '已退数量:', refundedQuantity, '可退数量:', refundableQuantity);

        // 确定商品在订单中的状态
        let statusInOrder: 'normal' | 'partial_refunded' | 'refunded';
        if (refundedQuantity === 0) {
          statusInOrder = 'normal';
        } else if (refundableQuantity === 0) {
          // 使用 refundableQuantity 判断是否已全退
          statusInOrder = 'refunded';
        } else {
          statusInOrder = 'partial_refunded';
        }

        // 计算单价 = 总金额 / 数量 (确保 quantity > 0)
        const unitPrice = (product.quantity || 0) > 0 ? Math.round((product.payAmount || 0) / product.quantity) : 0;

        return {
          id: product.id,
          productName: product.productName || '',
          quantity: product.quantity || 0, // 原始数量
          displayQuantity, // 添加显示数量
          unit: product.unit || '',
          payPrice: unitPrice, // 使用计算得到的单价
          originalPrice: product.originalPrice || 0, // 添加原价
          // 实际支付单价：只有订单状态为 paid 时才认为有实际支付
          actualPayPrice: order.status === 'paid' ? unitPrice : 0,
          statusInOrder,
          refundedQuantity, // 添加已退数量
          refundableQuantity, // 添加可退数量
          packageProductInfo: product.packageProductInfo || '', // 添加套餐信息
          isGift: product.isGift || false, // 添加赠送标识
          isFree: product.isFree || false // 添加免费标识
        };
      });

      // 创建订单组
      orderGroups.push({
        orderNo: order.orderNo,
        ctime: order.ctime,
        status: order.status as 'paid' | 'unpaid',
        products: convertedProducts
      });
    });

    // 按创建时间排序，最新的订单排在前面
    console.log('[refundDetail] orderGroups:', orderGroups);
    return orderGroups.sort((a, b) => b.ctime - a.ctime);
  }

  // 创建退款映射
  static createRefundMap(orders: OrderVO[], products: OrderProductVO[]): Map<string, Map<string, number>> {
    const refundMap = new Map<string, Map<string, number>>();

    // 找出所有退款订单
    const refundOrders = orders.filter(o => o.direction === 'refund' && o.pOrderNo);

    console.log(
      '[Debug] 退款订单列表:',
      refundOrders.map(o => ({ orderNo: o.orderNo, pOrderNo: o.pOrderNo }))
    );

    refundOrders.forEach(refundOrder => {
      const originalOrderNo = refundOrder.pOrderNo!;

      // 获取该退款订单的商品
      const refundProducts = products.filter(p => p.orderNo === refundOrder.orderNo);

      console.log('[Debug] 退款订单', refundOrder.orderNo, '包含商品:', refundProducts.length);

      // 如果原订单还没有退款记录,创建一个新的Map
      if (!refundMap.has(originalOrderNo)) {
        refundMap.set(originalOrderNo, new Map());
      }

      // 记录每个商品的退款数量 - 使用pId关联原始商品
      refundProducts.forEach(product => {
        const currentMap = refundMap.get(originalOrderNo)!;

        // 使用pId字段找到对应的原始商品ID
        const pId = (product as any).pId;
        if (!pId) {
          console.log('[Debug] 警告: 退款商品缺少pId，无法关联原始商品:', product.productName);
          return;
        }

        const currentQuantity = currentMap.get(pId) || 0;
        currentMap.set(pId, currentQuantity + (product.quantity || 0));

        console.log('[Debug] 退款商品', product.productName, 'pId:', pId, '退款数量:', product.quantity);
      });
    });

    console.log(
      '[Debug] 退款映射结果:',
      Array.from(refundMap.entries()).map(([key, value]) => ({
        originalOrderNo: key,
        products: Array.from(value.entries()).map(([pId, qty]) => ({ pId, quantity: qty }))
      }))
    );

    return refundMap;
  }

  // 新增：转换包厢订单数据
  static convertRoomOrders(orderVOs: OrderVO[], roomPlans: any[]): RefundRoomOrderVO[] {
    // 过滤出包厢订单 (type为roomplan, direction为normal, 排除退款订单)
    const roomOrders = orderVOs.filter(order => order.type === 'roomplan' && order.direction === 'normal');

    // 创建包厢退款映射
    const roomRefundMap = this.createRoomRefundMap(orderVOs, roomPlans);

    const convertedOrders: RefundRoomOrderVO[] = [];

    roomOrders.forEach(order => {
      // 找到该订单对应的时间段
      const orderTimePlans = roomPlans.filter(plan => plan.orderNo === order.orderNo);

      if (orderTimePlans.length === 0) return;

      // 检查该订单是否已经全部退款
      const isFullyRefunded = this.isRoomOrderFullyRefunded(order.orderNo, orderTimePlans, roomRefundMap);
      
      // 如果已经全部退款，不显示在可退列表中
      if (isFullyRefunded) {
        console.log('[Debug] 包厢订单', order.orderNo, '已全部退款，不显示在可退列表中');
        return;
      }

      // 计算最早开始时间和最晚结束时间
      const startTimes = orderTimePlans.map(plan => plan.startTime);
      const endTimes = orderTimePlans.map(plan => plan.endTime);
      const earliestStartTime = Math.min(...startTimes);
      const latestEndTime = Math.max(...endTimes);

      // 计算总时长（分钟）和总金额
      const totalDuration = orderTimePlans.reduce((sum, plan) => sum + (plan.duration || 0), 0);
      const totalAmount = orderTimePlans.reduce((sum, plan) => sum + (plan.payAmount || 0), 0);

      // 获取当前时间（使用now13转换为秒）
      const currentTime = Math.floor(now13() / 1000);

      // 判断是否可以退单：只有未进入消费时段的订单才能退
      // 即当前时间 < 最早开始时间
      const canRefund = currentTime < earliestStartTime;

      // 转换时间段明细
      const timePlans = orderTimePlans.map(plan => ({
        id: plan.id,
        startTime: plan.startTime,
        endTime: plan.endTime,
        duration: plan.duration || 0,
        payAmount: plan.payAmount || 0
      }));

      // 获取价格方案名称（从第一个时间段获取）
      const pricePlanName = orderTimePlans[0]?.pricePlanName || '未知方案';

      convertedOrders.push({
        id: order.id,
        orderNo: order.orderNo,
        pricePlanName,
        totalAmount,
        totalDuration,
        earliestStartTime,
        latestEndTime,
        status: order.status as 'paid' | 'unpaid',
        canRefund,
        timePlans,
        ctime: order.ctime
      });
    });

    // 按创建时间排序，最新的订单排在前面
    return convertedOrders.sort((a, b) => b.ctime - a.ctime);
  }

  // 创建包厢退款映射
  static createRoomRefundMap(orderVOs: OrderVO[], roomPlans: any[]): Map<string, Set<string>> {
    const refundMap = new Map<string, Set<string>>();

    // 找出所有包厢退款订单
    const roomRefundOrders = orderVOs.filter(o => o.type === 'roomplan' && o.direction === 'refund' && o.pOrderNo);

    console.log(
      '[Debug] 包厢退款订单列表:',
      roomRefundOrders.map(o => ({ orderNo: o.orderNo, pOrderNo: o.pOrderNo }))
    );

    roomRefundOrders.forEach(refundOrder => {
      const originalOrderNo = refundOrder.pOrderNo!;

      // 获取该退款订单的包厢费用
      const refundRoomPlans = roomPlans.filter(plan => plan.orderNo === refundOrder.orderNo);

      console.log('[Debug] 包厢退款订单', refundOrder.orderNo, '包含费用项:', refundRoomPlans.length);

      // 如果原订单还没有退款记录,创建一个新的Set
      if (!refundMap.has(originalOrderNo)) {
        refundMap.set(originalOrderNo, new Set());
      }

      // 记录已退款的包厢费用ID - 使用pId关联原始包厢费用
      refundRoomPlans.forEach(plan => {
        const currentSet = refundMap.get(originalOrderNo)!;

        // 使用pId字段找到对应的原始包厢费用ID
        const pId = plan.pId;
        if (!pId) {
          console.log('[Debug] 警告: 退款包厢费用缺少pId，无法关联原始费用:', plan.roomName);
          return;
        }

        currentSet.add(pId);
        console.log('[Debug] 退款包厢费用', plan.roomName, 'pId:', pId);
      });
    });

    console.log(
      '[Debug] 包厢退款映射结果:',
      Array.from(refundMap.entries()).map(([key, value]) => ({
        originalOrderNo: key,
        refundedPlanIds: Array.from(value)
      }))
    );

    return refundMap;
  }

  // 检查包厢订单是否已全部退款
  static isRoomOrderFullyRefunded(orderNo: string, orderTimePlans: any[], refundMap: Map<string, Set<string>>): boolean {
    const refundedPlanIds = refundMap.get(orderNo);
    
    // 如果没有退款记录，肯定不是全退
    if (!refundedPlanIds || refundedPlanIds.size === 0) {
      return false;
    }

    // 检查订单的所有包厢费用是否都已退款
    for (const plan of orderTimePlans) {
      if (!refundedPlanIds.has(plan.id)) {
        // 如果有任何一个费用项没有退款，则不是全退
        return false;
      }
    }

    console.log('[Debug] 包厢订单', orderNo, '判断为全部退款');
    return true;
  }
}
