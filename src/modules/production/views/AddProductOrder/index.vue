<template>
  <!-- 使用LeftRightLayout组件实现左右布局 -->
  <LeftRightLayout left-class="!w-[1120px]" right-class="!px-0">
    <!-- 左侧商品选择区域 -->
    <template #left>
      <div class="h-full bg-white product-container">
        <!-- 将ProductSelector替换为EnhancedProductSelector -->
        <EnhancedProductSelector @add-to-cart="vm.actions.addToCart" />
      </div>
    </template>

    <!-- 右侧购物车区域 -->
    <template #right>
      <div class="flex flex-col h-full relative w-full p-[24px]">
        <div class="flex-1">
          <!-- 包厢信息 -->
          <div class="grid grid-cols-3 gap-0 mb-[16px] rounded-[12px]">
            <div>
              <CustomSelector
                :model-value="'room'"
                label="类型"
                placeholder="选择类型"
                :options="[{ value: 'room', label: '包厢点单' }]"
                class="w-full"
                disabled />
            </div>
            <div>
              <CustomSelector
                v-model="vm.state.currentRoomId"
                label="送达包厢"
                placeholder="选择包厢"
                :options="vm.state.roomOptions"
                class="w-full"
                :loading="vm.state.loadingRooms"
                @focus="vm.actions.loadRooms"
                @change="handleRoomChange" />
            </div>
            <div>
              <CustomSelector
                v-model="vm.state.selectedSeller"
                label="销售员"
                disabled
                placeholder="选择销售员"
                :options="vm.state.employeeOptions"
                class="w-full"
                :loading="vm.state.loadingEmployees"
                @focus="vm.actions.loadEmployees" />
            </div>
          </div>

          <!-- 使用包厢选择组件,临时注释 -->
          <!-- <RoomSelectorDialog v-if="vm.state.showRoomSelector" v-model="vm.state.showRoomSelector" :filters="{ requireSession: true }"
            @select="vm.actions.handleRoomSelect" /> -->

          <!-- 购物车内容区域 -->
          <el-card class="bg-white rounded-[12px] shadow-sm overflow-hidden flex-1" :body-style="{ padding: '0px' }">
            <!-- 使用 el-card 的 header 插槽 -->
            <template #header>
              <div class="flex justify-between items-center">
                <div class="text-base font-medium">商品账单</div>
                <div class="text-red-500 cursor-pointer" @click="vm.actions.clearCart" v-if="vm.state.cartItems.length > 0">清空</div>
              </div>
            </template>

            <!-- 购物车表格 - 移动回卡片内部 -->
            <el-table
              :data="processedCartItems"
              class="w-full cart-table"
              min-height="400"
              max-height="60vh"
              table-layout="fixed"
              :span-method="objectSpanMethod"
              :row-class-name="cartItemRowClassName">
              <el-table-column prop="name" label="商品名称" min-width="100px" align="left">
                <template #default="scope">
                  <div v-if="!scope.row.isDetail">{{ scope.row.name }}</div>
                  <PackageDetailItem
                    v-if="scope.row.isDetail"
                    :row="scope.row"
                    :isDetail="scope.row.isDetail"
                    :parentId="scope.row.parentId"
                    :parent="findParentItem(scope.row.parentId || '')" />
                </template>
              </el-table-column>

              <!-- <el-table-column prop="flavors" label="口味" min-width="70" align="center">
                <template #default="{ row }">
                  <span v-if="!row.isDetail" class="text-gray-500">{{ row.flavors || '无' }}</span>
                </template>
              </el-table-column> -->

              <el-table-column label="数量" min-width="120px" align="center">
                <template #default="scope">
                  <div v-if="!scope.row.isDetail" class="flex items-center justify-center py-2">
                    <ErpInputNumber
                      v-model="scope.row.quantity"
                      type="primary"
                      size="small"
                      mode="cart"
                      :step="1"
                      @update:modelValue="val => handleQuantityUpdate(scope.row, val)"
                      @delete-item="handleDeleteItem(scope.row)" />
                    <!-- 套餐数量变化不会影响套餐内部商品，套餐总价 = 套餐单价 × 数量 -->
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="currentPrice" label="单价" align="center">
                <template #default="{ row }">
                  <span v-if="!row.isDetail" class="text-gray-800">{{ formatYuanWithSymbol(row.currentPrice || 0) }}</span>
                </template>
              </el-table-column>

              <el-table-column label="金额" align="right">
                <template #default="{ row }">
                  <span v-if="!row.isDetail" class="text-gray-800">{{ formatYuanWithSymbol((row.currentPrice || 0) * row.quantity) }}</span>
                </template>
              </el-table-column>

              <template #empty>
                <div class="py-10 text-center text-gray-400">购物车是空的，请从左侧添加商品</div>
              </template>
            </el-table>
          </el-card>
        </div>

        <!-- 底部操作区域 - 使用absolute而不是fixed -->
        <div class="absolute inset-x-0 bottom-0 z-10 bg-white">
          <!-- 功能按钮区 - 五个均匀分布的按钮，有间距 -->
          <!-- <div class="grid grid-cols-5 gap-[10px] px-[30px] mb-[16px]">
            <el-button class="!h-[54px] text-base font-medium !rounded-[30px]" plain disabled>商品打折</el-button>
            <el-button class="!h-[54px] text-base font-medium !rounded-[30px]" plain disabled>存单</el-button>
            <el-button class="!h-[54px] text-base font-medium !rounded-[30px]" plain disabled>取单</el-button>
            <el-button class="!h-[54px] text-base font-medium !rounded-[30px]" plain disabled>核销</el-button>
            <el-button class="!h-[54px] text-base font-medium !rounded-[30px]" plain @click="vm.actions.addNote"
              disabled>备注</el-button>
          </div> -->

          <!-- 底部结算区 - 完全填充宽度，无间距 -->
          <div class="w-full px-[24px] items-center">
            <div class="rounded-[10px] overflow-hidden">
              <OrderButton
                :amount="vm.computed.totalAmountInFen.value"
                label="待结总计"
                primary-text="确认下单"
                secondary-text="挂单"
                primary-button-class="confirm-button"
                secondary-button-class="pending-button"
                :disabled="vm.state.loading"
                @primary-click="vm.actions.handleConfirm"
                @secondary-click="vm.actions.handlePending" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </LeftRightLayout>

  <ProductionOrderPrinter
    v-if="vm.state.showPrinter"
    v-model:visible="vm.state.showPrinter"
    :data="vm.state.printData"
    @print-success="vm.actions.handlePrintSuccess"
    @print-failed="vm.actions.handlePrintFailed" />

  <!-- 添加套餐选择弹窗 -->
  <package-dialog
    v-if="vm.state.packageDialogVisible"
    v-model="vm.state.currentPackage"
    v-model:visible="vm.state.packageDialogVisible"
    @confirm="vm.actions.handlePackageConfirm"
    @cancel="vm.actions.closePackageDialog" />
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue';
import { ref, watch } from 'vue';
import EnhancedProductSelector from './components/EnhancedProductSelector.vue';
import RoomSelectorDialog from '@/modules/room/components/RoomSelectorDialog.vue';
import ProductionOrderPrinter from '../../components/ProductionOrderPrinter.vue';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import { useAddProductOrder } from './presenter';
import PackageDialog from '@/modules/production/components/packageDialog/index.vue';
import LeftRightLayout from '@/components/Dialog/layout';
import IconButton from '@/components/icons/IconButton.vue';
import { IconPlus, IconMinus } from '@/components/icons';
import OrderButton from '@/components/CheckoutBar/OrderButton.vue';
import CustomSelector from '@/components/customer/CustomSelector.vue';
import { ElSpace } from 'element-plus';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import { ICartItem } from '@/modules/production/views/OpenTableProduct/viewmodel';
import PackageDetailItem from './components/PackageDetailItem.vue';

const vm = useAddProductOrder();

// 选中的房间名称
const selectedRoomName = ref('');

// 当房间选择变化时
const handleRoomChange = (roomId: string) => {
  if (!roomId) {
    vm.actions.clearRoomSelection();
    return;
  }

  // 找到对应的房间选项
  const roomOption = vm.state.roomOptions.find(option => option.value === roomId);
  if (roomOption) {
    selectedRoomName.value = roomOption.label;
    // 更新选中的房间
    vm.actions.handleRoomSelect(roomOption.roomVO);
  }
};

// 类型定义：扩展购物车项目
interface ExtendedCartItem extends ICartItem {
  isDetail?: boolean;
  parentId?: string;
  packageProducts?: any[];
  cartItemId?: string; // 添加cartItemId属性
}

// 处理购物车数据
const processedCartItems = ref<ExtendedCartItem[]>([]);

// 监听购物车中套餐数量变化
watch(
  () => vm.state.cartItems.map(item => ({ id: item.id, quantity: item.quantity })),
  () => {
    // 触发购物车数据处理逻辑重新执行
    processedCartItems.value = processCartItems(vm.state.cartItems);
  },
  { deep: true }
);

// 处理购物车数据的函数
const processCartItems = (cartItems: ICartItem[]): ExtendedCartItem[] => {
  const processed: ExtendedCartItem[] = [];

  // 遍历购物车中的每个项目
  cartItems.forEach(item => {
    // 添加原始项目
    processed.push(item as ExtendedCartItem);

    // 如果是套餐，则添加一个明细行
    if (item.isPackage) {
      // 将item转换为包含cartItemId的类型
      const cartItem = item as any; // 临时转换，因为cartItemId在运行时是存在的
      const itemId = cartItem.cartItemId || item.id;

      const detailItem = {
        ...item, // 先复制所有属性以确保类型兼容性
        id: `detail-${itemId}`, // 使用cartItemId确保唯一性
        name: `${item.name} 明细`,
        currentPrice: 0,
        quantity: 0,
        unit: item.unit, // 确保包含必需属性
        isPackage: false,
        isDetail: true,
        parentId: itemId, // 使用cartItemId作为父项标识符
        cartItemId: itemId // 明确设置cartItemId
      };
      processed.push(detailItem);
    }
  });

  return processed;
};

// 监听购物车数据变化，添加明细行
watch(
  () => vm.state.cartItems,
  newItems => {
    const processed = processCartItems(newItems);
    processedCartItems.value = processed;
  },
  { immediate: true, deep: true }
);

// 根据父项ID查找对应的购物车项目
const findParentItem = (parentId: string): ICartItem | undefined => {
  // 先尝试通过cartItemId查找
  const cartItemWithId = vm.state.cartItems.find(item => {
    const cartItem = item as any;
    return cartItem.cartItemId === parentId;
  });

  if (cartItemWithId) {
    return cartItemWithId;
  }

  // 如果没找到，回退到用基础ID查找（兼容旧数据）
  return vm.state.cartItems.find(item => item.id === parentId);
};

// 处理对象跨行
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  // 如果是详情行，则合并所有列
  if (row.isDetail) {
    if (columnIndex === 0) {
      return {
        colspan: 5, // 合并所有列
        rowspan: 1
      };
    } else {
      return {
        colspan: 0,
        rowspan: 0
      };
    }
  }
};

// 购物车行类名
const cartItemRowClassName = ({ row }: { row: ExtendedCartItem }) => {
  if (row.isDetail) {
    return 'is-detail-row';
  }
  if (row.isPackage) {
    return 'is-package';
  }
  return '';
};

// 处理数量更新
const handleQuantityUpdate = (item: any, newValue: number | undefined) => {
  console.log('🎯 handleQuantityUpdate 被调用:', {
    item: {
      id: item.id,
      name: item.name,
      cartItemId: item.cartItemId,
      currentQuantity: item.quantity
    },
    newValue
  });

  // 获取旧值（在v-model更新之前）
  const oldValue = item.quantity;

  // 调用presenter的方法
  vm.actions.handleQuantityChange(item, newValue, oldValue);
};

// 处理删除商品（当数量将要变为0时）
const handleDeleteItem = (item: any) => {
  console.log('🗑️ handleDeleteItem 被调用 - 直接删除商品:', {
    item: {
      id: item.id,
      name: item.name,
      cartItemId: item.cartItemId,
      currentQuantity: item.quantity
    }
  });

  // 直接调用删除逻辑，不经过数量更新
  vm.actions.handleQuantityChange(item, 0, item.quantity);
};

// 当销售员选择变化时
const handleSellerChange = (sellerId: string) => {
  console.log('销售员改变:', sellerId);
  console.log('可用选项:', vm.state.employeeOptions);

  // 可以在这里添加其他逻辑
};
</script>

<style scoped>
/* Ensure the card body acts as the containing block for sticky positioning */
:deep(.el-card__body) {
  position: relative;
}

:deep(.cart-table td.el-table__cell) {
  background-color: #fff !important;
}

/* Target the table's header wrapper */
:deep(.el-table__header-wrapper) {
  position: sticky;
  top: 0;
  /* Stick to the top of the scrolling container (el-card__body) */
  z-index: 1;
  /* Ensure header stays above table content */
}

:deep(.is-detail-row) td {
  padding: 0 !important;
  height: auto !important;
  border-top: none !important;
}

:deep(.panel-content) {
  padding: 0px;
}

:deep(.pending-button) {
  background-color: #eee;
  color: #333;
}

/* 表格行分割线颜色差异化设置 - 直接使用border属性 */
/* 为普通行设置加深的边框颜色 */
:deep(.cart-table .el-table__row:not(.is-package):not(.is-detail-row) td) {
  border-bottom: 2px solid #eee !important; /* 普通行使用更深的分割线 */
}

/* 套餐行保持浅色边框 */
:deep(.cart-table .is-package td) {
  border-bottom: none !important;
}

/* 详情行无边框 */
:deep(.cart-table .is-detail-row td) {
  border-bottom: 2px solid #eee !important; /* 详情行无分割线 */
}
</style>
