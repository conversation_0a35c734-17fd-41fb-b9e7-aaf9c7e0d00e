import { OrderProductVO, RoomVO } from '@/api/autoGenerated/shared/types';
import type { IOpenTableProductState, IOpenTableProductComputed, IOpenTableProductActions, IOpenTableProductViewModel } from '../OpenTableProduct/viewmodel';

// 房间选项接口
export interface RoomOption {
  value: string; // 使用 roomId 作为 value
  label: string;
  roomVO: RoomVO;
}

// 购物车商品类型定义
export interface ShoppingCartItem {
  id: string;
  name: string;
  currentPrice: number;
  quantity: number;
  unit: string;
  isPackage: boolean;
  packageUniqueId?: string;
  packageDetail?: any;
  // 标记当前是否处于编辑模式
  isEditing?: boolean;
  // 购物车项目的唯一标识符，用于区分相同商品的不同实例
  cartItemId?: string;
}

// 扩展状态接口
export interface IAddProductOrderState extends IOpenTableProductState {
  // 打印相关
  showPrinter: boolean;
  printData:
    | {
        orderNo: string;
        roomVO: RoomVO;
        employeeName: string;
        ctime: number;
        orderProductVOs: OrderProductVO[];
        sessionId: string;
      }
    | null
    | Record<string, any>;
  // 支付相关
  isChargeToRoom: boolean;
  // UI相关 - 包厢和区域选择
  selectedRoom: RoomVO | null;
  selectedArea: string;
  showRoomSelector: boolean;
  // 销售员信息
  selectedSeller: string;
  // 员工选项列表
  employeeOptions: { value: string; label: string }[];
  // 加载员工状态
  loadingEmployees: boolean;
  // 当前包厢ID（使用roomId而不是sessionId，避免联台时的多sessionId问题）
  currentRoomId: string;
  // 购物车商品
  cartItems: ShoppingCartItem[];
  // 总金额
  totalAmount: number;
  // 当前套餐
  currentPackage: any;
  // 套餐弹窗是否显示
  packageDialogVisible: boolean;
  // 房间选项列表
  roomOptions: RoomOption[];
  // 加载状态
  loadingRooms: boolean;
}

// 扩展计算属性接口
export interface IAddProductOrderComputed extends IOpenTableProductComputed {
  // 如果需要额外的计算属性，在这里添加
}

// 扩展动作接口
export interface IAddProductOrderActions extends IOpenTableProductActions {
  // 打印相关动作
  handlePrintSuccess(): void;
  handlePrintFailed(): void;
  // 房间选择相关
  clearRoomSelection(): void;
  handleRoomSelect(room: any): void;
  // 加载房间列表
  loadRooms(): void;
  // 加载员工列表
  loadEmployees(): void;
  // 购物车管理
  clearCart(): void;
  increaseQuantity(item: ShoppingCartItem): void;
  decreaseQuantity(item: ShoppingCartItem): void;
  updateQuantity(item: ShoppingCartItem, value: number | undefined): void;
  handleQuantityChange(item: ShoppingCartItem, currentValue: number | undefined, oldValue: number | undefined): void;
  // 备注相关
  addNote?(): void;
  // 挂单功能
  handlePending(): void;
}

// 总的ViewModel接口
export interface IAddProductOrderViewModel extends IOpenTableProductViewModel {
  state: IAddProductOrderState;
  computed: IAddProductOrderComputed;
  actions: IAddProductOrderActions;
}
