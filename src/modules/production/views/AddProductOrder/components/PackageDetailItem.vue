<template>
  <div v-if="isDetail" class="package-detail-expanded-item">
    <div class="w-full px-4 py-2">
      <template v-if="parentId && parent?.packageDetail">
        <div v-if="displayProductItems.length">
          <div class="flex flex-wrap -mx-1">
            <div v-for="(product, index) in displayProductItems" :key="index" class="px-1 py-1">
              <span class="inline-block rounded-full bg-[#EEE] px-3 py-1 text-[14px] text-[#444]"> {{ product.name }} x {{ product.displayCount }} </span>
            </div>
          </div>
        </div>

        <!-- 如果没有详细数据，显示简单字符串 -->
        <div v-if="!displayProductItems.length" class="text-gray-600">
          {{ getPackageDetailsText() }}
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import { ICartItem } from '@/modules/production/views/OpenTableProduct/viewmodel';
import { AddProductOrderConverter } from '../converter';

interface DetailItem {
  id: string;
  name: string;
  count: number;
  displayCount: number; // 新增：用于显示的计算后数量
  [key: string]: any;
}

const props = defineProps({
  // 行数据
  row: {
    type: Object,
    required: true
  },
  // 是否为详情行
  isDetail: {
    type: Boolean,
    default: false
  },
  // 父项ID
  parentId: {
    type: String,
    default: ''
  },
  // 父项数据
  parent: {
    type: Object as () => ICartItem | undefined,
    default: undefined
  }
});

// 📝 使用computed计算显示用的商品列表（包含计算后的显示数量）
const displayProductItems = computed<DetailItem[]>(() => {
  if (!props.parent) {
    return [];
  }

  // 🔥 关键：从converter获取原始数据，然后在显示层计算最终数量
  const rawProducts = AddProductOrderConverter.formatPackageProductInfoForDisplay(props.parent);
  const packageQuantity = props.parent.quantity || 1;

  console.log('PackageDetailItem: 原始商品数据', rawProducts);
  console.log('PackageDetailItem: 套餐数量', packageQuantity);

  // 📝 在显示层计算最终数量：原始count × 套餐数量
  const displayProducts = rawProducts.map(product => ({
    ...product,
    displayCount: product.count * packageQuantity // 显示数量 = 原始count × 套餐数量
  }));

  console.log('PackageDetailItem: 计算后的显示数据', displayProducts);

  return displayProducts;
});

// 获取套餐明细显示文本(仅作为备选)
const getPackageDetailsText = (): string => {
  return props.parent?.packageDetail?.detailString || '';
};
</script>

<style lang="scss" scoped>
.package-detail-expanded-item {
  padding: 2px;
}
</style>
