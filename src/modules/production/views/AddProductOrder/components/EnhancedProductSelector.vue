<template>
  <div class="relative h-full product-selector">
    <!-- 左侧分类列表 - 改为absolute布局 -->
    <aside class="absolute left-0 top-0 bottom-0 w-[160px] overflow-y-auto scrollbar-hide border-r bg-white z-10 border-r border-gray-200">
      <!-- 分类列表，直接从顶部开始 -->
      <div class="flex flex-col py-2">
        <!-- 所有分类项（包括全部分类） -->
        <div
          v-for="category in categories"
          :key="category.id || 'all'"
          class="mx-2 mb-1 px-4 py-3 cursor-pointer rounded-lg"
          :class="{
            'bg-red-500 text-white shadow-sm': category.isActive,
            'text-gray-600 hover:bg-red-50 hover:text-red-600': !category.isActive
          }"
          @click="changeCategory(category.id)">
          <span class="text-sm font-medium truncate block">{{ category.name }}</span>
        </div>
      </div>
    </aside>

    <!-- 右侧商品列表 - 添加左边距以避免被分类列表遮挡 -->
    <main class="flex-1 flex flex-col h-full overflow-hidden ml-[160px] relative">
      <!-- 商品列表标题和搜索 -->
      <div class="p-[24px]">
        <div class="text-rose-600 text-2xl font-normal mb-4">商品列表</div>
        <div class="w-full rounded-lg border border-gray-300 flex items-center px-4 py-2 !h-[64px]">
          <input v-model="searchKeyword" placeholder="搜索商品" class="w-full outline-none text-black/40 text-[20px] font-medium" @input="handleSearch" />
          <el-button v-if="searchKeyword" link @click="clearSearch" class="ml-2">
            <el-icon class="text-[24px] text-black/40"><Close /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 商品表格区域，添加pb-[110px]来为底部操作区留出足够空间 -->
      <div class="flex-1 overflow-y-auto px-[36px] pb-[110px]" ref="productListRef" @scroll="debouncedScroll">
        <el-skeleton v-if="loading" :rows="3" animated class="p-4" />
        <div v-else-if="error" class="p-4 text-center text-red-500">{{ error }}</div>
        <div v-else-if="searchKeyword && products.length === 0" class="p-4 text-center text-gray-500">没有找到匹配"{{ searchKeyword }}"的商品</div>
        <el-table v-else :data="products" stripe>
          <el-table-column prop="name" label="商品名称" align="left" min-width="100">
            <template #default="{ row }">
              <span class="font-medium">{{ row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="tag" label="状态" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag v-if="row.isSoldOut" type="info" size="small" effect="plain" class="text-sm">沽清</el-tag>
              <el-tag
                v-else-if="row.hasOptionalGroups"
                size="small"
                effect="plain"
                class="text-sm"
                style="color: #319cff; border-color: #319cff; background-color: rgba(49, 156, 255, 0.1)"
                >可选</el-tag
              >
              <el-tag v-else-if="row.tag" type="warning" size="small" effect="plain" class="text-sm">{{ row.tag }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <el-table-column prop="unit" label="单位" min-width="60" align="center" />

          <el-table-column label="单价" min-width="80" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center">
                <span class="text-red-500 font-medium">{{ formatYuanWithSymbol(row.currentPrice) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" min-width="100" align="center">
            <template #default="{ row }">
              <erp-input-number :disabled="row.isSoldOut" v-model="row.quantity" simpleMode type="primary" class="ml-2" @click="handleAddToCart(row)" />
            </template>
          </el-table-column>

          <template #empty>
            <div class="py-10 text-center text-gray-400">没有找到商品，请更换分类或搜索条件</div>
          </template>
        </el-table>

        <!-- 加载更多提示 -->
        <div v-if="loadingMore" class="p-4 text-center">
          <el-icon class="is-loading mr-2">
            <Loading />
          </el-icon>
          加载更多...
        </div>

        <!-- 没有更多数据提示 -->
        <div v-else-if="!hasMore && products.length > 0" class="p-4 text-center text-gray-400">没有更多商品了</div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Plus, Loading, Search, Close } from '@element-plus/icons-vue';
import { ProductApi } from '@/modules/production/api/product';
import { formatYuanWithSymbol } from '@/utils/priceUtils';
import ErpInputNumber from '@/components/input/ErpInputNumber.vue';
import { QueryProductReqDto } from '@/api/autoGenerated/shared/types/product';
import { debounce } from 'lodash-es';
import { useRouter } from 'vue-router';
import { ProductOrPackageRVO, ProductPackageVO, ProductVO } from '@/types/projectobj';

const router = useRouter();
const emit = defineEmits(['add-to-cart']);

// 定义类型
interface Category {
  id: string | null;
  name: string;
  count: string | number;
  isActive: boolean;
  type: string;
  isPackage: boolean;
  tag?: string;
}

interface Product {
  id: string;
  name: string;
  stock: number;
  tag?: string;
  currentPrice: number;
  unit: string;
  isPackage: boolean;
  isSoldOut: boolean;
  packageProducts?: string;
  optionalGroups?: string;
  productVOList?: any[];
  flavors?: string;
  quantity?: number;
  hasOptionalGroups?: boolean;
}

// 搜索关键词
const searchKeyword = ref('');

// 状态
const categories = ref<Category[]>([]);
const products = ref<Product[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const selectedCategoryId = ref<string | null>(null);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(200);
const productListRef = ref<HTMLElement | null>(null);
const loadingMore = ref(false);
const hasMore = ref(true);

// 类型定义
const CATEGORY_TYPE = {
  ALL: 'all',
  PACKAGE: 'package',
  PRODUCT: 'product'
} as const;

// 获取商品分类
const fetchCategories = async () => {
  console.log('开始获取商品分类...');
  try {
    // 使用实际的 ProductApi
    const response = await ProductApi.listProductTypes({});
    console.log('获取商品分类响应:', response);
    if (response.code === 0) {
      const productTypes = response.data.productTypeVOs.map((type: any) => ({
        id: type.id,
        name: type.name,
        count: type.count,
        isActive: false,
        type: CATEGORY_TYPE.PRODUCT,
        isPackage: false
      }));

      const packageTypes = response.data.productPackageTypeVOs.map((type: any) => ({
        id: type.id,
        name: type.name,
        count: type.count,
        isActive: false,
        type: CATEGORY_TYPE.PACKAGE,
        isPackage: true
      }));

      // 添加"全部分类"选项
      const allCategory: Category = {
        id: null,
        name: '全部分类',
        count: '',
        isActive: true, // 默认选中
        type: CATEGORY_TYPE.ALL,
        isPackage: false
      };

      categories.value = [
        allCategory, // 将全部分类放在最前面
        ...packageTypes,
        ...productTypes
      ];
      selectedCategoryId.value = null;
    } else {
      throw new Error(response.message || '获取商品分类失败');
    }
  } catch (err: any) {
    console.error('获取商品分类失败:', err);
    error.value = '获取分类失败: ' + err.message;
  }
};

// 获取商品列表
const fetchProducts = async (isLoadingMore = false) => {
  if (!isLoadingMore) {
    loading.value = true;
    error.value = null;
  }
  try {
    const selectedCategory = categories.value.find(c => c.id === selectedCategoryId.value);

    // 创建API参数对象
    const apiParams: QueryProductReqDto = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    // 根据选中的分类设置不同的参数,如果选中的分类是套餐，则设置productPackageTypeId，否则设置category
    if (selectedCategory && selectedCategory.id !== null) {
      if (selectedCategory.isPackage) {
        apiParams.productPackageTypeId = selectedCategory.id;
      } else {
        apiParams.category = selectedCategory.id;
      }
    }

    // 如果有搜索关键词，添加到参数中
    if (searchKeyword.value) {
      apiParams.name = searchKeyword.value.trim();
    }

    console.log('查询商品参数:', apiParams);

    // 使用实际的 ProductApi
    const response = await ProductApi.queryDetailByType(apiParams);

    if (response.code === 0) {
      let newProducts: Product[] = [];

      // 处理套餐数据 - 注意价格已经是分为单位
      const packageList = response.data.productPackageVOs || [];
      const packageProducts = packageList.map((item: ProductPackageVO) => ({
        id: item.id,
        name: item.name,
        stock: -1,
        tag: item.isPromoted ? '促销' : '',
        currentPrice: item.currentPrice, // 服务端返回的价格已经是分为单位
        unit: '套餐',
        isPackage: true,
        isSoldOut: item.isSoldOut,
        packageProducts: item.packageProducts,
        optionalGroups: item.optionalGroups,
        productVOList: item.productVOList,
        hasOptionalGroups: item.optionalGroups && JSON.parse(item.optionalGroups).length > 0
      }));

      // 处理普通商品数据 - 注意价格已经是分为单位
      const productList = response.data.productVOs || [];
      const normalProducts = productList.map((item: ProductVO) => ({
        id: item.id,
        name: item.name,
        stock: item.calculateInventory ? item.lowStockThreshold : -1,
        tag: item.isPromotion ? '促销' : '',
        currentPrice: item.currentPrice, // 服务端返回的价格已经是分为单位
        unit: item.unit,
        isPackage: false,
        isSoldOut: item.isSoldOut
      }));

      // 根据选中的分类类型决定显示哪些产品
      if (selectedCategory && selectedCategory.id !== null) {
        // 如果选中了具体分类，直接使用对应的产品
        newProducts = selectedCategory.isPackage ? packageProducts : normalProducts;
      } else {
        // 如果是"全部分类"，需要处理套餐去重问题
        if (isLoadingMore) {
          // 分页加载时，只添加普通商品，避免套餐重复
          newProducts = normalProducts;
          console.log('分页加载：只加载普通商品，避免套餐重复');
        } else {
          // 首次加载时，加载套餐 + 普通商品
          newProducts = [...packageProducts, ...normalProducts];
          console.log('首次加载：加载套餐和普通商品');
        }
      }

      if (isLoadingMore) {
        // 分页加载时，使用去重逻辑避免重复数据
        const existingIds = new Set(products.value.map(p => p.id));
        const uniqueNewProducts = newProducts.filter(p => !existingIds.has(p.id));
        products.value = [...products.value, ...uniqueNewProducts];
      } else {
        products.value = newProducts;
      }

      // 判断是否还有更多数据：
      // 1. 如果是套餐分类，以套餐数量判断
      // 2. 如果是普通商品分类，以普通商品数量判断
      // 3. 如果是全部分类，以普通商品数量判断（因为套餐只在第一页加载）
      let dataCountForPagination = 0;
      if (selectedCategory && selectedCategory.id !== null) {
        dataCountForPagination = selectedCategory.isPackage ? packageProducts.length : normalProducts.length;
      } else {
        // 全部分类时，分页主要看普通商品
        dataCountForPagination = normalProducts.length;
      }

      hasMore.value = dataCountForPagination >= pageSize.value;
      console.log(`加载完成，数据量: ${dataCountForPagination}, 是否还有更多: ${hasMore.value}`);
    } else {
      throw new Error(response.message || '获取商品列表失败');
    }
  } catch (err: any) {
    console.error('获取商品列表失败:', err);
    error.value = '获取商品列表失败: ' + err.message;
  } finally {
    if (!isLoadingMore) {
      loading.value = false;
    }
    loadingMore.value = false;
  }
};

// 切换分类
const changeCategory = (categoryId: string | null) => {
  selectedCategoryId.value = categoryId;

  // 更新分类活跃状态
  categories.value.forEach(category => {
    // 判断当前分类是否为所选分类
    category.isActive = category.id === categoryId;
  });

  // 重置分页状态
  currentPage.value = 1;
  hasMore.value = true;
  loadingMore.value = false;

  console.log('切换分类:', categoryId, '重置分页状态');
  fetchProducts();
};

// 处理搜索的实际逻辑
const doSearch = () => {
  // 重置分页和其他状态
  currentPage.value = 1;
  hasMore.value = true;
  loadingMore.value = false;

  // 如果是搜索操作，重置分类选择
  if (searchKeyword.value.trim()) {
    // 更新分类活跃状态，选中"全部分类"
    categories.value.forEach(category => {
      category.isActive = category.type === CATEGORY_TYPE.ALL;
    });
    selectedCategoryId.value = null;
  }

  console.log('执行搜索:', searchKeyword.value, '重置分页状态');
  // 重新获取商品列表
  fetchProducts();
};

// 处理滚动加载
const handleScroll = async () => {
  if (loadingMore.value || !hasMore.value || !productListRef.value) return;

  const container = productListRef.value;
  // 检查是否滚动到底部附近（距离底部100px以内）
  const scrollThreshold = 100;
  const isNearBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - scrollThreshold;

  if (isNearBottom) {
    console.log('触发滚动加载，当前页:', currentPage.value);
    loadingMore.value = true;
    currentPage.value++;
    await fetchProducts(true);
  }
};

// 使用lodash的debounce创建防抖函数
const debouncedSearch = debounce(doSearch, 300);

// 为滚动加载创建防抖函数，避免频繁触发
const debouncedScroll = debounce(handleScroll, 100);

// 处理搜索 - 使用lodash debounce
const handleSearch = () => {
  debouncedSearch();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  // 如果当前没有选中分类，则默认选中全部分类
  if (selectedCategoryId.value === null) {
    categories.value.forEach(category => {
      category.isActive = category.type === CATEGORY_TYPE.ALL;
    });
  }
  // 重新加载商品列表
  currentPage.value = 1;
  hasMore.value = true;
  loadingMore.value = false;

  console.log('清除搜索，重置分页状态');
  fetchProducts();
};

// 处理添加到购物车
const handleAddToCart = (product: Product) => {
  if (!product.quantity) {
    product.quantity = 1;
  }
  emit('add-to-cart', { ...product });
};

// 处理商品沽清按钮点击
const handleSoldOutClick = () => {
  router.push('/production/soldout');
};

/**
 * 格式化商品标签显示
 * @param isSoldOut 是否售空
 * @param tag 原始标签文本
 * @returns 格式化后的标签文本
 */
const formatProductTag = (isSoldOut: boolean, tag: string) => {
  // 判断商品是否售空
  return isSoldOut ? '售空' : tag;
};

// 初始化
onMounted(() => {
  // 初始化分页状态
  currentPage.value = 1;
  hasMore.value = true;
  loadingMore.value = false;

  // 开始加载数据
  fetchCategories();
  fetchProducts();
  // 确保默认选中"全部分类"
  selectedCategoryId.value = null;

  console.log('组件初始化完成，开始加载数据');
});

// 在组件卸载前取消未执行的防抖函数
onBeforeUnmount(() => {
  debouncedSearch.cancel();
  debouncedScroll.cancel();
});
</script>

<style scoped>
/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
