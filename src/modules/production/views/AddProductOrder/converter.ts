import { OpenTableProductConverter } from '../OpenTableProduct/converter';
import type { OrderProductVO, RoomVO } from '@/types/projectobj';
import { omit } from 'lodash';

export class AddProductOrderConverter extends OpenTableProductConverter {
  /**
   * 将购物车商品转换为订单商品
   */
  static cartItemsToOrderProducts(cartItems: any[]): any[] {
    return cartItems.map(item => {
      // 基础字段
      const baseProduct = {
        id: '', // 添加必需的id字段
        venueId: '', // 添加必需的venueId字段
        roomId: '', // 添加必需的roomId字段
        sessionId: '', // 添加必需的sessionId字段
        productName: item.name,
        flavors: item.flavor || '',
        quantity: item.quantity,
        unit: item.unit,
        payPrice: item.currentPrice,
        originalPrice: item.currentPrice,
        payAmount: item.currentPrice * item.quantity!,
        originalAmount: item.currentPrice * item.quantity!,
        payStatus: 'unpaid'
      };

      // 套餐特殊处理
      if (item.isPackage) {
        // 解析套餐明细，提取包含商品信息
        let packageProductInfo: any[] = [];
        const productMap = new Map<string, any>(); // 用于去重
        let packageDetail: any = null; // 在外部声明packageDetail变量
        try {
          // 尝试从packageDetail中解析选择的商品信息
          if (item.packageDetail) {
            // 如果是字符串格式需要解析
            packageDetail = typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : item.packageDetail;
            console.log('[packageDetail] get:', item);
            console.log('[packageDetail] packageDetail:', packageDetail);

            // 🔥 优先使用 selectedProducts，这是已经计算好的最终结果
            if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts) && packageDetail.selectedProducts.length > 0) {
              packageProductInfo = packageDetail.selectedProducts.map((selectedProduct: any) => ({
                id: selectedProduct.id,
                count: selectedProduct.count || 1, // 直接使用已计算好的最终数量
                price: selectedProduct.currentPrice || 0,
                name: selectedProduct.name || '未命名商品'
              }));

              console.log('[packageDetail] 使用 selectedProducts，简化逻辑');
            } else {
              // 兜底逻辑：如果没有 selectedProducts，使用原来的复杂逻辑
              console.log('[packageDetail] selectedProducts 不存在，使用兜底逻辑');

              // 处理默认产品
              if (packageDetail.packageProducts && typeof packageDetail.packageProducts === 'string') {
                const defaultProducts = JSON.parse(packageDetail.packageProducts);
                if (Array.isArray(defaultProducts)) {
                  defaultProducts.forEach((product: any) => {
                    const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
                    if (productInfo) {
                      const key = product.id;
                      if (!productMap.has(key)) {
                        productMap.set(key, {
                          id: product.id,
                          count: product.count || 1,
                          price: productInfo.price || productInfo.currentPrice || 0,
                          name: productInfo.name || '未命名商品'
                        });
                      } else {
                        const existing = productMap.get(key);
                        existing.count += product.count || 1;
                      }
                    }
                  });
                }
              }

              // 处理可选组商品（optionalGroups）
              if (packageDetail.optionalGroups && Array.isArray(packageDetail.optionalGroups)) {
                packageDetail.optionalGroups.forEach((group: any) => {
                  if (group.products && Array.isArray(group.products)) {
                    group.products.forEach((product: any) => {
                      const selectedCount = product.selected_count || 0;
                      if (selectedCount > 0) {
                        const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
                        if (productInfo) {
                          const key = product.id;
                          if (!productMap.has(key)) {
                            productMap.set(key, {
                              id: product.id,
                              count: selectedCount,
                              price: productInfo.price || productInfo.currentPrice || 0,
                              name: productInfo.name || '未命名商品'
                            });
                          } else {
                            const existing = productMap.get(key);
                            existing.count += selectedCount;
                          }
                        }
                      }
                    });
                  }
                });
              }

              // 将Map转换为数组
              packageProductInfo = Array.from(productMap.values());
            }
          }
        } catch (error) {
          console.error('解析套餐明细失败:', error);
          // 发生错误时使用基本信息
          packageProductInfo = [
            {
              id: item.id,
              count: 1, // 📝 错误时使用单个数量，保持数据纯粹
              price: item.currentPrice || 0,
              name: item.name || '未命名商品'
            }
          ];
        }
        console.log('[packageDetail] packageProductInfo:', packageProductInfo);

        return {
          ...baseProduct,
          packageId: item.id, // 使用packageId替代productId
          packageProductInfo: JSON.stringify(packageProductInfo), // 序列化时不包含name字段
          inPackageTag: 'yes'
        };
      } else {
        // 普通商品处理
        return {
          ...baseProduct,
          productId: item.id,
          inPackageTag: 'no'
        };
      }
    });
  }

  /**
   * 格式化套餐商品信息用于前端显示（包含name字段）
   * 📝 简化方法：只解析原始数据，不做数量计算，交给显示层处理
   */
  static formatPackageProductInfoForDisplay(item: any): { id: string; count: number; price: number; name: string }[] {
    if (!item.isPackage || !item.packageDetail) {
      return [];
    }

    let packageProductInfo: any[] = [];
    const productMap = new Map<string, any>(); // 用于去重

    try {
      // 如果是字符串格式需要解析
      const packageDetail = typeof item.packageDetail === 'string' ? JSON.parse(item.packageDetail) : item.packageDetail;

      // 🔥 优先检查selectedProducts，如果存在且有数据，直接使用
      if (packageDetail.selectedProducts && Array.isArray(packageDetail.selectedProducts) && packageDetail.selectedProducts.length > 0) {
        packageDetail.selectedProducts.forEach((selectedProduct: any) => {
          const productInfo = packageDetail.productVOList?.find((p: any) => p.id === selectedProduct.id);
          const productData = {
            id: selectedProduct.id,
            count: selectedProduct.count || 1, // 📝 保持原始count，不乘以套餐数量
            price: productInfo?.price || productInfo?.currentPrice || selectedProduct.price || 0,
            name: selectedProduct.name || productInfo?.name || '未命名商品'
          };
          productMap.set(selectedProduct.id, productData);
        });

        // 📝 直接返回原始数据，不做数量计算
        packageProductInfo = Array.from(productMap.values());
        return packageProductInfo;
      }

      // 兜底逻辑：处理默认产品和可选组商品
      // 处理默认产品
      if (packageDetail.packageProducts && typeof packageDetail.packageProducts === 'string') {
        const defaultProducts = JSON.parse(packageDetail.packageProducts);

        if (Array.isArray(defaultProducts)) {
          defaultProducts.forEach((product: any) => {
            const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
            if (productInfo) {
              const key = product.id;
              if (!productMap.has(key)) {
                const productData = {
                  id: product.id,
                  count: product.count || 1, // 📝 保持原始count
                  price: productInfo.price || productInfo.currentPrice || 0,
                  name: productInfo.name || '未命名商品'
                };
                productMap.set(key, productData);
              } else {
                const existing = productMap.get(key);
                existing.count += product.count || 1;
              }
            }
          });
        }
      }

      // 处理可选组商品（optionalGroups）
      if (packageDetail.optionalGroups && Array.isArray(packageDetail.optionalGroups)) {
        packageDetail.optionalGroups.forEach((group: any) => {
          if (group.products && Array.isArray(group.products)) {
            group.products.forEach((product: any) => {
              const selectedCount = product.selected_count || 0;

              if (selectedCount > 0) {
                const productInfo = packageDetail.productVOList?.find((p: any) => p.id === product.id);
                if (productInfo) {
                  const key = product.id;

                  if (!productMap.has(key)) {
                    const productData = {
                      id: product.id,
                      count: selectedCount, // 📝 保持原始选择数量，不做额外计算
                      price: productInfo.price || productInfo.currentPrice || 0,
                      name: productInfo.name || '未命名商品'
                    };
                    productMap.set(key, productData);
                  } else {
                    const existing = productMap.get(key);
                    existing.count += selectedCount; // 📝 累加原始选择数量
                  }
                }
              }
            });
          }
        });
      }

      // 📝 返回原始数据，不乘以套餐数量
      packageProductInfo = Array.from(productMap.values());
    } catch (error) {
      console.error('格式化套餐明细失败:', error);
    }

    return packageProductInfo;
  }

  /**
   * 生成打印数据
   */
  static generatePrintData(params: { orderNo: string; roomVO: RoomVO; employeeName: string; orderProducts: OrderProductVO[]; sessionId: string }) {
    const { orderNo, roomVO, employeeName, orderProducts, sessionId } = params;
    return {
      orderNo,
      roomVO,
      employeeName,
      ctime: Date.now(),
      orderProductVOs: orderProducts,
      sessionId
    };
  }

  /**
   * 计算订单总金额
   */
  static calculateTotalAmounts(cartItems: any[]) {
    // 套餐价格计算逻辑：套餐总价 = 套餐单价 × 数量
    // 套餐内部的商品数量变化不影响套餐总价
    return {
      totalPayAmount: cartItems.reduce((total, item) => total + item.currentPrice * item.quantity!, 0),
      totalOriginalAmount: cartItems.reduce((total, item) => total + item.currentPrice * item.quantity!, 0)
    };
  }
}
