# 数据纯粹性修改说明

## 修改目标

保持数据层纯粹，不在数据转换时做业务计算，只在显示层通过computed进行格式化。

## 问题描述

- **显示正确**：套餐明细显示时，商品数量 = 单个count × 套餐数量
- **提交错误**：确认下单和挂单时，packageProductInfo中的count应该是原始的单个count，不应该乘以套餐数量

## 修改内容

### 1. converter.ts 修改

#### 1.1 cartItemsToOrderProducts 方法

- **修改前**：在处理可选组商品时，会根据optionType计算finalCount
- **修改后**：直接使用selectedCount，保持原始选择数量
- **影响**：确保提交到后端的packageProductInfo中的count是原始规格数量

#### 1.2 formatPackageProductInfoForDisplay 方法

- **修改前**：会将count乘以套餐数量用于显示
- **修改后**：只返回原始count，不做数量计算
- **影响**：数据层保持纯粹，计算逻辑移到显示层

### 2. PackageDetailItem.vue 修改

#### 2.1 显示逻辑重构

- **修改前**：使用复杂的解析逻辑和calculateQuantity方法
- **修改后**：使用computed `displayProductItems`，直接调用converter获取原始数据，然后在显示层计算
- **优势**：
  - 逻辑简化，职责清晰
  - 数据层和显示层分离
  - 易于维护和调试

#### 2.2 计算公式

```typescript
displayCount = originalCount * packageQuantity;
```

## 数据流程

### 修改前

```
原始数据 → converter加工 → 显示层直接使用
```

### 修改后

```
原始数据 → converter保持纯粹 → 显示层computed计算 → UI显示
```

## 验证点

1. **显示验证**：套餐明细中的商品数量应该正确显示为 `单个规格数量 × 套餐数量`
2. **提交验证**：确认下单和挂单时，packageProductInfo中的count应该是原始的单个规格数量
3. **数据一致性**：同一个套餐的显示和提交数据应该基于同一份原始数据

## 好处

1. **数据纯粹性**：converter只负责数据解析，不做业务计算
2. **职责分离**：显示逻辑归显示层，数据逻辑归数据层
3. **易于调试**：可以清晰地看到原始数据和计算后的显示数据
4. **一致性**：确保显示和提交使用相同的数据源
