
import { printingService } from '@/application/printingService'

export interface IAddProductOrderInteractor {
  
  /**
   * 打印出品单
   * @param orderNos 订单号列表
   * @param sessionId 会话ID
   */
  printProductionOrder(orderNos: string[] | undefined, sessionId?: string): Promise<void>
}

export class AddProductOrderInteractor implements IAddProductOrderInteractor {
  /**
   * 打印出品单 (异步，不阻塞调用者)
   * 用于挂单成功后调用，打印出品
   * @param orderNos 订单号列表
   * @param sessionId 会话ID（可选）
   */
  async printProductionOrder(orderNos: string[] | undefined, sessionId?: string): Promise<void> {
    try {
      if (!orderNos || orderNos.length === 0) {
        console.warn('AddProductOrderInteractor: 没有可打印出品单的订单号');
        return;
      }

      console.log(`AddProductOrderInteractor: 准备打印出品单 (异步)，订单号: ${orderNos.join(', ')}, 会话ID: ${sessionId || '无'}`);

      // 调用printingService的printProductOutBySessionId方法，不等待结果
      printingService
        .printProductOutBySessionId(sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('AddProductOrderInteractor: 出品单打印任务已发送');
          } else {
            console.error('AddProductOrderInteractor: 出品单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('AddProductOrderInteractor: 打印出品单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('AddProductOrderInteractor: 准备打印出品单时出错:', error);
      // 即使内部出错，也不阻塞外部调用
    }
  }
}

// 导出单例
export const addProductOrderInteractor = new AddProductOrderInteractor() 