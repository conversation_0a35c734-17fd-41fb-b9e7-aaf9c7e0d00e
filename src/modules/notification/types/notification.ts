import type { RoomVO } from '@/types/projectobj';

export interface RoomNotifyPayload {
  id: string; // 通知唯一ID
  roomId: string; // 房间ID
  roomNumber: string; // 房间号
  type: string; // 通知类型 (例如："CALL_SERVICE", "CALL_FOOD", "CALL_MANAGER", "TECHNICAL_ISSUE")
  message: string; // 通知消息内容
  timestamp: number; // 时间戳 (毫秒)
  status: 'pending' | 'processed'; // 通知状态，pending: 未处理, processed: 已处理
  roomVO?: RoomVO; // 关联的包厢信息 (可选),
  optName?: string; // 处理人姓名
  optTime?: number; // 处理时间
}

export interface NotificationFilter {
  dateRange: [Date, Date] | null;
  roomNumber?: string; // 房间号筛选
  status: 'pending' | 'processed' | 'all';
}

export interface NotificationPagination {
  page: number;
  pageSize: number;
  total: number;
}

// 新增出品通知相关类型
export interface ProductionNotifyPayload {
  id: string; // 通知唯一ID
  roomId: string; // 房间ID
  roomNumber: string; // 房间号
  itemName: string; // 商品名称
  quantity: number; // 数量
  failureType: 'PRINT_FAILURE' | 'NETWORK_ERROR' | string; // 失败类型
  timestamp: number; // 时间戳 (毫秒)
  status: 'pending' | 'processed'; // 通知状态，pending: 未处理, processed: 已处理
  roomVO?: RoomVO; // 关联的包厢信息 (可选)
}

// 出品项目
export interface ProductionItem {
  name: string; // 商品名称
  quantity: number; // 数量
}
