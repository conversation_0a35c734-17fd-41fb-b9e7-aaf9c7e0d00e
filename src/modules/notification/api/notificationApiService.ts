import request from '@/utils/request';
import type { RoomNotifyPayload, NotificationFilter, NotificationPagination, ProductionNotifyPayload } from '../types/notification';

// API响应类型定义
interface CallMessageVO {
  callSrc: string;
  callType: string;
  callTypeName: string;
  cancelId: string;
  cancelName: string;
  cancelTime: number;
  ctime: number;
  id: string;
  optId: string;
  optName: string;
  optTime: number;
  roomId: string;
  roomName: string;
  sessionId: string;
  state: number;
  status: number;
}

interface ProductionItemVO {
  id: string;
  roomId: string;
  roomName: string;
  itemName: string;
  quantity: number;
  failureType: string;
  status: string;
  timestamp: number;
}

const BASE_URL = '/api/v3/call';
const SERVICE_CALL_URL = '/api/v3/call';
const PRODUCTION_URL = '/api/production';

export const notificationApiService = {
  /**
   * 获取未读呼叫通知列表
   * @returns 未读通知列表
   */
  getUnreadList: async (): Promise<{ data: RoomNotifyPayload[] }> => {
    const response = await request({
      url: `${BASE_URL}/list`,
      method: 'post'
    });

    // 转换API响应格式为前端使用格式
    const unreadNotifications = ((response.data as CallMessageVO[]) || [])
      .filter((item: CallMessageVO) => item.status === 0) // 假设0表示未处理状态
      .map((item: CallMessageVO) => ({
        id: item.id,
        roomId: item.roomId,
        roomNumber: item.roomName,
        type: item.callType,
        message: item.callTypeName,
        status: item.status === 0 ? 'pending' : ('processed' as 'pending' | 'processed'),
        timestamp: item.ctime,
        optName: item.optName || '',
        optTime: item.optTime || 0
      }));

    return { data: unreadNotifications };
  },

  /**
   * 获取未处理呼叫通知列表（专门用于消息通知弹窗）
   * @param filters 筛选条件
   * @returns 未处理通知列表
   */
  getUnprocessedList: async (filters?: NotificationFilter): Promise<{ data: RoomNotifyPayload[] }> => {
    const response = await request({
      url: `${BASE_URL}/unprocessed/list`,
      method: 'post',
      data: filters
    });

    // 转换API响应格式为前端使用格式
    const unreadNotifications = ((response.data as CallMessageVO[]) || []).map((item: CallMessageVO) => ({
      id: item.id,
      roomId: item.roomId,
      roomNumber: item.roomName,
      type: item.callType,
      message: item.callTypeName,
      status: item.status === 0 ? 'pending' : ('processed' as 'pending' | 'processed'),
      timestamp: item.ctime,
      optName: item.optName || '',
      optTime: item.optTime || 0
    }));

    return { data: unreadNotifications };
  },

  /**
   * 获取呼叫通知列表（带筛选）
   * @param filters 筛选条件
   * @returns 通知列表
   */
  getList: async (filters: NotificationFilter): Promise<{ data: { list: RoomNotifyPayload[]; total: number } }> => {
    console.log('[NotificationApiService] getList - 筛选条件:', filters);

    const response = await request({
      url: `${BASE_URL}/list`,
      method: 'post'
    });

    // 转换API响应格式为前端使用格式
    let list = ((response.data as CallMessageVO[]) || []).map((item: CallMessageVO) => ({
      id: item.id,
      roomId: item.roomId,
      roomNumber: item.roomName,
      type: item.callType,
      message: item.callTypeName,
      status: item.status === 0 ? 'pending' : ('processed' as 'pending' | 'processed'),
      timestamp: item.ctime,
      optName: item.optName || '',
      optTime: item.optTime || 0
    }));

    console.log('[NotificationApiService] 获取到原始数据:', list.length, '条');

    // 客户端过滤 - 可以考虑将这些过滤条件添加到API请求中
    // 根据状态过滤
    if (filters.status && filters.status !== 'all') {
      const statusValue = filters.status === 'pending' ? 'pending' : 'processed';
      console.log('[NotificationApiService] 按状态过滤:', statusValue);
      list = list.filter(item => item.status === statusValue);
      console.log('[NotificationApiService] 过滤后数据:', list.length, '条');
    }

    // 根据日期范围过滤
    if (filters.dateRange) {
      const [startDate, endDate] = filters.dateRange;
      list = list.filter(item => {
        const itemDate = new Date(item.timestamp);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // 根据房间号过滤
    if (filters.roomNumber && filters.roomNumber.trim() !== '') {
      const searchTerm = filters.roomNumber.toLowerCase();
      list = list.filter(item => item.roomNumber.toLowerCase().includes(searchTerm));
    }

    return {
      data: {
        list,
        total: list.length
      }
    };
  },

  /**
   * 将单个呼叫通知标记为已读
   * @param notificationId 通知ID
   * @returns 操作结果
   */
  markAsDeal: async (payload: { id: string; remark: string }): Promise<{ success: boolean }> => {
    return await request({
      url: `${BASE_URL}/deal`,
      method: 'post',
      data: payload
    });
  },

  /**
   * 将所有呼叫通知标记为已读
   * @param filterParams 可选筛选条件
   * @returns 操作结果
   */
  markAllAsRead: async (filterParams?: Partial<NotificationFilter>): Promise<{ success: boolean }> => {
    try {
      // 获取所有未读通知
      const { data } = await notificationApiService.getUnreadList();

      // 批量处理所有未读通知
      const promises = data.map(notification => notificationApiService.markAsDeal({ id: notification.id, remark: '' }));

      await Promise.all(promises);
      return { success: true };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return { success: false };
    }
  }
};

export const productionNotificationApiService = {
  /**
   * 获取未读出品通知列表
   * @returns 未读出品通知列表
   */
  getUnreadList: async (): Promise<{ data: ProductionNotifyPayload[] }> => {
    const response = await request({
      url: `${PRODUCTION_URL}/list`,
      method: 'get'
    });

    // 转换API响应格式为前端使用格式
    const unreadNotifications = ((response.data as ProductionItemVO[]) || [])
      .filter((item: ProductionItemVO) => item.status === 'pending')
      .map((item: ProductionItemVO) => ({
        id: item.id,
        roomId: item.roomId,
        roomNumber: item.roomName || '',
        itemName: item.itemName || '',
        quantity: item.quantity || 1,
        failureType: item.failureType || 'UNKNOWN',
        status: (item.status === 'pending' ? 'pending' : 'processed') as 'pending' | 'processed',
        timestamp: item.timestamp || Date.now()
      }));

    return { data: unreadNotifications };
  },

  /**
   * 获取出品通知列表（带筛选）
   * @param filters 筛选条件
   * @returns 出品通知列表
   */
  getList: async (filters: NotificationFilter): Promise<{ data: { list: ProductionNotifyPayload[]; total: number } }> => {
    const response = await request({
      url: `${PRODUCTION_URL}/list`,
      method: 'get',
      params: filters
    });

    // 转换API响应格式为前端使用格式
    let list = ((response.data?.list as ProductionItemVO[]) || []).map((item: ProductionItemVO) => ({
      id: item.id,
      roomId: item.roomId,
      roomNumber: item.roomName || '',
      itemName: item.itemName || '',
      quantity: item.quantity || 1,
      failureType: item.failureType || 'UNKNOWN',
      status: (item.status === 'pending' ? 'pending' : 'processed') as 'pending' | 'processed',
      timestamp: item.timestamp || Date.now()
    }));

    return {
      data: {
        list,
        total: response.data?.total || list.length
      }
    };
  },

  /**
   * 将单个出品通知标记为已读/重打
   * @param notificationId 通知ID
   * @returns 操作结果
   */
  markAsProcessed: async (notificationId: string): Promise<{ success: boolean }> => {
    try {
      await request({
        url: `${PRODUCTION_URL}/process`,
        method: 'post',
        data: {
          id: notificationId
        }
      });
      return { success: true };
    } catch (error) {
      console.error('Error marking production notification as processed:', error);
      return { success: false };
    }
  },

  /**
   * 将所有出品通知标记为已读
   * @param filterParams 可选筛选条件
   * @returns 操作结果
   */
  markAllAsProcessed: async (filterParams?: Partial<NotificationFilter>): Promise<{ success: boolean }> => {
    try {
      // 获取所有未读通知
      const { data } = await productionNotificationApiService.getUnreadList();

      // 批量处理所有未读通知
      const promises = data.map(notification => productionNotificationApiService.markAsProcessed(notification.id));

      await Promise.all(promises);
      return { success: true };
    } catch (error) {
      console.error('Error marking all production notifications as processed:', error);
      return { success: false };
    }
  }
};

export const serviceCallApiService = {
  /**
   * 上报呼叫事件
   * @param payload 呼叫事件数据
   * @returns 操作结果及生成的通知ID
   */
  report: async (payload: any): Promise<{ success: boolean; notificationId?: string }> => {
    try {
      const response = await request({
        url: `${SERVICE_CALL_URL}/add`,
        method: 'post',
        data: payload
      });

      return {
        success: true,
        notificationId: response.data?.id
      };
    } catch (error) {
      console.error('Error reporting service call:', error);
      return { success: false };
    }
  }
};

/**
 * 房间状态变更事件处理器
 * 当房间状态发生变化时，触发相应的通知
 * @param roomId 房间ID
 * @param statusChange 状态变更信息
 * @returns 生成的通知ID
 */
export const roomStatusChanged = async (
  roomId: string,
  statusChange: {
    roomNumber: string;
    type: string;
    message: string;
  }
): Promise<string> => {
  // 调用服务呼叫API
  const result = await serviceCallApiService.report({
    roomId,
    roomName: statusChange.roomNumber,
    type: statusChange.type,
    message: statusChange.message
  });

  // 返回生成的通知ID
  return result.notificationId || '';
};

export default {
  notification: notificationApiService,
  productionNotification: productionNotificationApiService,
  serviceCall: serviceCallApiService,
  // 导出事件处理函数
  events: {
    roomStatusChanged
  }
};
