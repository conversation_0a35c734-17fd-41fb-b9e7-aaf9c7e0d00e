import { defineStore } from 'pinia';
import { notificationApiService, productionNotificationApiService, serviceCallApiService, roomStatusChanged } from '../api/notificationApiService';
import type { RoomNotifyPayload, ProductionNotifyPayload, NotificationFilter } from '../types/notification';
import { useStageStore } from '@/stores/stageStore';
import { ElMessage } from 'element-plus';

interface NotificationState {
  // 呼叫通知
  notifications: RoomNotifyPayload[]; // 所有未读通知
  unreadCount: number; // 未读通知数量
  popoverNotifications: RoomNotifyPayload[]; // Popover中显示的通知
  isLoading: boolean; // 加载状态

  // 出品通知
  productionNotifications: ProductionNotifyPayload[]; // 所有未读出品通知
  productionUnreadCount: number; // 未读出品通知数量
  popoverProductionNotifications: ProductionNotifyPayload[]; // Popover中显示的出品通知
  isProductionLoading: boolean; // 出品通知加载状态

  // 搜索和筛选
  currentFilter: NotificationFilter;
  currentProductionFilter: NotificationFilter;
}

// 默认筛选条件
const DEFAULT_FILTER: NotificationFilter = {
  dateRange: null,
  roomNumber: '',
  status: 'all'
};

export const useNotificationStore = defineStore('notification', {
  state: (): NotificationState => ({
    // 呼叫通知
    notifications: [],
    unreadCount: 0,
    popoverNotifications: [],
    isLoading: false,

    // 出品通知
    productionNotifications: [],
    productionUnreadCount: 0,
    popoverProductionNotifications: [],
    isProductionLoading: false,

    // 搜索和筛选
    currentFilter: { ...DEFAULT_FILTER },
    currentProductionFilter: { ...DEFAULT_FILTER }
  }),

  getters: {
    // 呼叫通知getters
    getUnreadCount(): number {
      return this.unreadCount;
    },
    getPopoverNotifications(): RoomNotifyPayload[] {
      return this.popoverNotifications;
    },
    getAllNotificationsForListPage(): RoomNotifyPayload[] {
      return this.notifications;
    },
    // 获取过滤后的呼叫通知
    getFilteredNotifications(): RoomNotifyPayload[] {
      return this.notifications;
    },

    // 出品通知getters
    getProductionUnreadCount(): number {
      return this.productionUnreadCount;
    },
    getPopoverProductionNotifications(): ProductionNotifyPayload[] {
      return this.popoverProductionNotifications;
    },
    getAllProductionNotificationsForListPage(): ProductionNotifyPayload[] {
      return this.productionNotifications;
    },
    // 获取过滤后的出品通知
    getFilteredProductionNotifications(): ProductionNotifyPayload[] {
      return this.productionNotifications;
    }
  },

  actions: {
    /**
     * 获取最新未读呼叫通知
     * @param isInitialLoad 是否为初始加载
     * @returns 是否有未读通知
     */
    async fetchAndUpdateUnreadNotifications(this: any, isInitialLoad: boolean = false) {
      try {
        this.isLoading = true;
        console.log('[Notification] NotificationStore - 获取未读通知');

        // 使用专门的未读消息接口
        const response = await notificationApiService.getUnprocessedList();

        if (response.data) {
          this.notifications = response.data;
          this.unreadCount = this.notifications.length;
          // 对于Popover，只显示最新的6条
          this.popoverNotifications = this.notifications.slice(0, 6);

          console.log('[Notification] NotificationStore - 未读通知更新成功, 数量:', this.unreadCount);

          // 返回是否有未读通知
          return this.unreadCount > 0;
        }
        return false;
      } catch (error) {
        console.error('[Notification] NotificationStore - 获取未读通知失败:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * 获取呼叫通知列表（带筛选）
     * @param filter 筛选条件
     */
    async fetchNotificationList(this: any, filter: NotificationFilter = this.currentFilter) {
      try {
        this.isLoading = true;
        console.log('NotificationStore - 获取通知列表', filter);

        this.currentFilter = filter;
        const response = await notificationApiService.getList(filter);

        if (response.data) {
          this.notifications = response.data.list;
          console.log('NotificationStore - 通知列表获取成功, 数量:', response.data.total);
        }

        return response.data;
      } catch (error) {
        console.error('NotificationStore - 获取通知列表失败:', error);
        return { list: [], total: 0 };
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * 获取最新未读出品通知
     * @param isInitialLoad 是否为初始加载
     */
    async fetchAndUpdateUnreadProductionNotifications(isInitialLoad: boolean = false) {
      try {
        this.isProductionLoading = true;
        console.log('NotificationStore - 获取未读出品通知');

        const response = await productionNotificationApiService.getUnreadList();

        if (response.data) {
          this.productionNotifications = response.data;
          this.productionUnreadCount = this.productionNotifications.length;
          // 对于Popover，只显示最新的6条
          this.popoverProductionNotifications = this.productionNotifications.slice(0, 6);

          console.log('NotificationStore - 未读出品通知更新成功, 数量:', this.productionUnreadCount);
        }
      } catch (error) {
        console.error('NotificationStore - 获取未读出品通知失败:', error);
      } finally {
        this.isProductionLoading = false;
      }
    },

    /**
     * 获取出品通知列表（带筛选）
     * @param filter 筛选条件
     */
    async fetchProductionNotificationList(this: any, filter: NotificationFilter = this.currentProductionFilter) {
      try {
        this.isProductionLoading = true;
        console.log('NotificationStore - 获取出品通知列表', filter);

        this.currentProductionFilter = filter;
        const response = await productionNotificationApiService.getList(filter);

        if (response.data) {
          this.productionNotifications = response.data.list;
          console.log('NotificationStore - 出品通知列表获取成功, 数量:', response.data.total);
        }

        return response.data;
      } catch (error) {
        console.error('NotificationStore - 获取出品通知列表失败:', error);
        return { list: [], total: 0 };
      } finally {
        this.isProductionLoading = false;
      }
    },

    /**
     * 将呼叫通知标记为已读
     * @param notificationId 通知ID
     */
    async markAsDeal(notificationId: string) {
      try {
        console.log('[Notification] NotificationStore - 将通知标记为已处理:', notificationId);
        const response = await notificationApiService.markAsDeal({ id: notificationId, remark: '' });
        console.log('[Notification] NotificationStore - 标记通知处理响应:', response);
        if (response.success) {
          console.log('[Notification] NotificationStore - 标记通知处理成功');
          // 重新加载未读通知列表以保持同步
          await this.fetchAndUpdateUnreadNotifications();

          // 如果在列表页面，也需要刷新列表
          if (this.currentFilter) {
            await this.fetchNotificationList(this.currentFilter);
          }
        }

        return response;
      } catch (error) {
        console.error('[Notification] NotificationStore - 标记通知已处理失败:', error);
        throw error;
      }
    },

    /**
     * 将所有呼叫通知标记为已读
     * @param filterParams 筛选条件（可选）
     */
    async markAllAsRead(filterParams?: Partial<NotificationFilter>) {
      try {
        console.log('NotificationStore - 将所有通知标记为已读');
        const response = await notificationApiService.markAllAsRead(filterParams);

        if (response.success) {
          // 重新加载未读通知列表以保持同步
          await this.fetchAndUpdateUnreadNotifications();

          // 如果在列表页面，也需要刷新列表
          if (this.currentFilter) {
            await this.fetchNotificationList();
          }
        }

        return response.success;
      } catch (error) {
        console.error('NotificationStore - 标记所有通知已读失败:', error);
        return false;
      }
    },

    /**
     * 处理出品通知
     * @param notificationId 通知ID
     */
    async processProductionNotification(notificationId: string) {
      try {
        console.log('NotificationStore - 处理出品通知:', notificationId);
        const response = await productionNotificationApiService.markAsProcessed(notificationId);

        if (response.success) {
          // 重新加载未读出品通知列表以保持同步
          await this.fetchAndUpdateUnreadProductionNotifications();

          // 如果在列表页面，也需要刷新列表
          if (this.currentProductionFilter) {
            await this.fetchProductionNotificationList();
          }
        }

        return response.success;
      } catch (error) {
        console.error('NotificationStore - 处理出品通知失败:', error);
        return false;
      }
    },

    /**
     * 处理所有出品通知
     * @param filterParams 筛选条件（可选）
     */
    async processAllProductionNotifications(filterParams?: Partial<NotificationFilter>) {
      try {
        console.log('NotificationStore - 处理所有出品通知');
        const response = await productionNotificationApiService.markAllAsProcessed(filterParams);

        if (response.success) {
          // 重新加载未读出品通知列表以保持同步
          await this.fetchAndUpdateUnreadProductionNotifications();

          // 如果在列表页面，也需要刷新列表
          if (this.currentProductionFilter) {
            await this.fetchProductionNotificationList();
          }
        }

        return response.success;
      } catch (error) {
        console.error('NotificationStore - 处理所有出品通知失败:', error);
        return false;
      }
    },

    /**
     * 上报呼叫事件
     * @param ip 设备IP地址
     * @param callType 呼叫类型
     * @param callTypeName 呼叫类型名称
     * @param callSrc 呼叫来源
     */
    async reportServiceCall(ip: string, callType: string, callTypeName: string, callSrc: string) {
      try {
        // 获取stageStore中的房间数据
        const stageStore = useStageStore();
        const allStages = stageStore.getStages();

        // 根据IP查找匹配的房间
        const matchedStage = allStages.find(stage => stage.roomVO.deviceIp === ip);

        if (!matchedStage) {
          console.error('[Notification] NotificationStore - 未找到匹配IP的房间:', ip);
          return { success: false };
        }

        // 构造呼叫事件数据
        const payload = {
          roomId: matchedStage.roomVO.id,
          sessionId: matchedStage.sessionVO?.sessionId || '',
          roomName: matchedStage.roomVO.name,
          callType,
          callTypeName,
          callSrc
        };

        console.log('[Notification] NotificationStore - 上报呼叫事件:', payload);
        const response = await serviceCallApiService.report(payload);

        if (response.success) {
          // 同时更新未读通知列表和当前列表页数据
          await this.fetchAndUpdateUnreadNotifications();

          // 如果当前有筛选条件，表示在列表页，也需要刷新列表页数据
          if (this.currentFilter && this.currentFilter.status !== 'processed') {
            await this.fetchNotificationList(this.currentFilter);
          }
        }

        return {
          success: response.success,
          notificationId: response.notificationId
        };
      } catch (error) {
        console.error('[Notification] NotificationStore - 上报呼叫事件失败:', error);
        return { success: false };
      }
    },

    /**
     * 模拟房间状态变更触发通知
     * 当房间状态发生变化时，触发相应的通知
     * @param roomId 房间ID
     * @param statusChange 状态变更信息
     */
    async mockRoomStatusChangedEvent(
      roomId: string,
      statusChange: {
        roomNumber: string;
        type: string;
        message: string;
      }
    ) {
      try {
        console.log('NotificationStore - 模拟房间状态变更事件:', statusChange);

        // 调用模拟事件处理函数
        const notificationId = await roomStatusChanged(roomId, statusChange);

        if (notificationId) {
          // 自动更新通知列表
          await this.fetchAndUpdateUnreadNotifications();

          console.log('NotificationStore - 房间状态变更触发通知成功, ID:', notificationId);
          return { success: true, notificationId };
        }

        return { success: false };
      } catch (error) {
        console.error('NotificationStore - 房间状态变更触发通知失败:', error);
        return { success: false };
      }
    },

    /**
     * 初始化获取所有未读通知
     */
    async initializeNotifications() {
      await Promise.all([this.fetchAndUpdateUnreadNotifications(true), this.fetchAndUpdateUnreadProductionNotifications(true)]);
    },

    /**
     * 搜索呼叫通知
     * @param searchTerm 搜索关键词
     */
    searchNotifications(searchTerm: string) {
      if (!searchTerm.trim()) {
        // 空搜索，恢复原始列表
        return this.fetchNotificationList();
      }

      // 更新过滤条件并重新获取列表
      const filter = { ...this.currentFilter, roomNumber: searchTerm };
      return this.fetchNotificationList(filter);
    },

    /**
     * 搜索出品通知
     * @param searchTerm 搜索关键词
     */
    searchProductionNotifications(searchTerm: string) {
      if (!searchTerm.trim()) {
        // 空搜索，恢复原始列表
        return this.fetchProductionNotificationList();
      }

      // 更新过滤条件并重新获取列表
      const filter = { ...this.currentProductionFilter, roomNumber: searchTerm };
      return this.fetchProductionNotificationList(filter);
    },

    /**
     * 处理服务呼叫
     * @param notificationId 通知ID
     * @param employeeId 员工ID
     * @param remark 备注信息
     * @returns 是否处理成功
     */
    async processServiceCall(notificationId: string, employeeId: string, remark: string = '') {
      try {
        console.log('[Notification] NotificationStore - 处理呼叫通知:', notificationId);

        // 调用处理接口
        const response = await notificationApiService.markAsDeal({
          id: notificationId,
          remark: remark
        });
        console.log('[Notification] NotificationStore - 处理呼叫通知响应:', response);
        if (response.code === 0) {
          // 重新加载未读通知列表以保持同步
          await this.fetchAndUpdateUnreadNotifications();
          // console.log('[Notification] NotificationStore - 处理呼叫通知成功');
          ElMessage.success('消息处理成功');
          return true;
        }
        ElMessage.error('消息处理失败:' + response.message);
        return false;
      } catch (error) {
        console.error('[Notification] NotificationStore - 处理呼叫通知失败:', error);
        ElMessage.error('消息处理失败:' + error);
        return false;
      }
    }
  }
});
