import type { RoomNotifyPayload, NotificationFilter, NotificationPagination, ProductionNotifyPayload } from '../types/notification';

/**
 * 呼叫通知列表项视图模型
 */
export interface INotificationListItem extends RoomNotifyPayload {
  formattedTimestamp?: string; // 格式化后的时间
  formattedOptTime?: string; // 格式化后的处理时间
}

/**
 * 出品通知列表项视图模型
 */
export interface IProductionNotificationListItem extends ProductionNotifyPayload {
  formattedTimestamp?: string; // 格式化后的时间
}

/**
 * 通知列表视图模型接口
 */
export interface INotificationListViewModel {
  // 呼叫通知相关
  isLoading: boolean; // 加载状态
  notifications: INotificationListItem[]; // 通知列表
  filter: NotificationFilter; // 筛选条件
  highlightId: string | null; // 高亮通知ID

  // 出品通知相关
  isProductionLoading: boolean; // 出品通知加载状态
  productionNotifications: IProductionNotificationListItem[]; // 出品通知列表
  productionFilter: NotificationFilter; // 出品通知筛选条件
  productionHighlightId: string | null; // 高亮出品通知ID
}

/**
 * 通知列表操作接口
 */
export interface INotificationListActions {
  /**
   * 加载呼叫通知列表
   */
  loadNotifications(): Promise<void>;

  /**
   * 加载出品通知列表
   */
  loadProductionNotifications(): Promise<void>;

  /**
   * 标记呼叫通知为已读
   * @param notificationId 通知ID
   */
  markAsDeal(notificationId: string): Promise<boolean>;

  /**
   * 标记所有呼叫通知为已读
   */
  markAllAsRead(): Promise<boolean>;

  /**
   * 处理出品通知（重打）
   * @param notificationId 出品通知ID
   */
  processProductionNotification(notificationId: string): Promise<boolean>;

  /**
   * 处理所有出品通知
   */
  processAllProductionNotifications(): Promise<boolean>;

  /**
   * 设置呼叫通知筛选状态
   * @param status 状态
   */
  setFilterStatus(status: 'pending' | 'processed' | 'all'): void;

  /**
   * 设置出品通知筛选状态
   * @param status 状态
   */
  setProductionFilterStatus(status: 'pending' | 'processed' | 'all'): void;

  /**
   * 搜索呼叫通知
   * @param searchTerm 搜索关键词
   */
  searchNotifications(searchTerm: string): void;

  /**
   * 搜索出品通知
   * @param searchTerm 搜索关键词
   */
  searchProductionNotifications(searchTerm: string): void;

  /**
   * 上报呼叫事件
   * @param ip 设备IP地址
   * @param callType 呼叫类型
   * @param callTypeName 呼叫类型名称
   * @param callSrc 呼叫来源
   */
  reportServiceCall(ip: string, callType?: string, callTypeName?: string, callSrc?: string): Promise<{ success: boolean; notificationId?: string }>;

  /**
   * 模拟房间状态变更触发通知
   */
  mockRoomStatusChangedEvent(): Promise<{ success: boolean; notificationId?: string }>;
}
