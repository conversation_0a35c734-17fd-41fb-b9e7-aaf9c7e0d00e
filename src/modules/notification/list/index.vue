<template>
  <div class="notification-list-container">
    <!-- 单列布局容器 -->
    <div class="h-[100%] bg-gray-100">
      <!-- 呼叫通知 -->
      <div class="notification-panel p-[24px] h-full flex flex-col">
        <div class="panel-header p-[24px]">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-bold">呼叫通知</h2>
            <div class="flex gap-2" v-if="isDev">
              <el-input v-model="testIP" placeholder="测试IP地址" class="w-[200px]" />
              <el-button type="primary" @click="handleReportCall">模拟呼叫服务 (A01)</el-button>
            </div>
          </div>
          <!-- 呼叫通知筛选器 -->
          <div class="filter-container mt-4">
            <div class="flex flex-wrap items-center gap-2 flex-row">
              <!-- 状态筛选 -->
              <div class="flex bg-white rounded-[10px] h-[68px] items-center gap-[8px] px-[8px]">
                <div
                  v-for="status in statusOptions"
                  :key="status.value"
                  class="h-[52px] w-[120px] font-medium flex items-center justify-center cursor-pointer text-[16px] rounded-[8px]"
                  :class="{ 'bg-[#E23939] text-white': activeStatus === status.value, 'text-gray-500': activeStatus !== status.value }"
                  @click="handleStatusChange(status.value)">
                  {{ status.label }}
                </div>
              </div>
              <div class="flex-1">
                <el-input v-model="localSearchKeyword" placeholder="包厢 / 消息 / 处理人 / 时间" class="custom-search-input" clearable>
                  <template #prefix>
                    <el-icon class="ml-4">
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </div>

        <!-- 呼叫通知列表 -->
        <div class="notification-list overflow-auto flex-1 px-[24px]">
          <div v-if="presenter.viewModel.isLoading" class="flex justify-center py-8">
            <el-loading></el-loading>
          </div>
          <div v-else-if="filteredNotifications.length === 0" class="flex justify-center items-center py-8 text-gray-500">暂无通知</div>

          <!-- 未处理通知使用列表样式 -->
          <ul v-else-if="activeStatus === 'pending'" class="notification-list-container">
            <li
              v-for="item in filteredNotifications"
              :key="item.id"
              class="notification-item flex items-center justify-between px-[24px] py-[16px] border-b border-gray-100"
              :class="{ 'highlight-row': presenter.viewModel.highlightId && item.id === presenter.viewModel.highlightId }">
              <div class="flex items-center flex-1">
                <div class="flex notification-icon mr-3 w-[64px] h-[64px] bg-gray-100 rounded-md items-center justify-center">
                  <NotifyIcon class="text-[32px]" />
                </div>
                <div class="notification-content flex-1">
                  <div class="text-[20px] font-medium">{{ item.roomNumber }}: {{ item.message }}</div>
                  <div class="text-[16px] text-gray-500">{{ item.formattedTimestamp }}</div>
                </div>
              </div>
              <el-button type="primary" class="btn-default w-[110px] h-[53px] rounded-md text-[16px]" @click="handleMarkAsDeal(item.id)"> 处理 </el-button>
            </li>
          </ul>

          <!-- 已处理通知使用表格样式 -->
          <div v-else>
            <div class="mb-4 flex justify-between">
              <span class="text-gray-500">共 {{ filteredNotifications.length }} 条记录</span>
            </div>
            <el-table :data="filteredNotifications" style="width: 100%" :row-class-name="rowClassName" stripe highlight-current-row>
              <el-table-column prop="roomNumber" label="包厢号" width="120" align="center" />
              <el-table-column prop="message" label="呼叫内容" min-width="140" show-overflow-tooltip />
              <el-table-column label="呼叫时间" width="170" align="center">
                <template #default="scope">
                  <span>{{ scope.row.formattedTimestamp }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="optName" label="处理人" width="120" align="center">
                <template #default="scope">
                  <el-tag type="info" effect="plain" v-if="!scope.row.optName">未知</el-tag>
                  <span v-else>{{ scope.row.optName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="处理时间" width="170" align="center">
                <template #default="scope">
                  <span v-if="scope.row.formattedOptTime">{{ scope.row.formattedOptTime }}</span>
                  <el-tag type="info" effect="plain" v-else>未记录</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="160" align="center">
                <template #default>
                  <span class="text-[16px] text-gray-500">已处理</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { NotificationListPresenter } from './presenter';
import type { INotificationListItem } from './viewmodel';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import NotifyIcon from '@/assets/v3/notify.vue';

// 获取环境变量，判断是否为开发环境
const isDev = import.meta.env.VITE_ENV !== 'product';

// 创建presenter
const presenter = new NotificationListPresenter();

// 初始化presenter
presenter.init();

// 定义状态选项
const statusOptions = [
  { value: 'pending' as const, label: '未处理' },
  { value: 'processed' as const, label: '已处理' }
];

// 测试IP输入框
const testIP = ref('************');

// 绑定呼叫通知筛选器状态
const activeStatus = ref<'pending' | 'processed' | 'all'>(presenter.viewModel.filter.status);
const roomNumberKeyword = ref(presenter.viewModel.filter.roomNumber || '');

// 本地筛选功能
const localSearchKeyword = ref('');

// 监听presenter中状态变化并同步到本地状态
watch(
  () => presenter.viewModel.filter.status,
  newStatus => {
    activeStatus.value = newStatus;
  }
);

// 本地筛选后的通知列表
const filteredNotifications = computed(() => {
  if (!localSearchKeyword.value) {
    return presenter.viewModel.notifications;
  }

  const keyword = localSearchKeyword.value.toLowerCase();
  return presenter.viewModel.notifications.filter(item => {
    return (
      // 包厢号搜索
      item.roomNumber.toLowerCase().includes(keyword) ||
      // 处理人搜索
      (item.optName && item.optName.toLowerCase().includes(keyword)) ||
      // 消息内容搜索
      item.message.toLowerCase().includes(keyword) ||
      // 类型搜索
      item.type.toLowerCase().includes(keyword) ||
      // 时间搜索 (格式化后的时间)
      (item.formattedTimestamp && item.formattedTimestamp.includes(keyword)) ||
      // 处理时间搜索
      (item.formattedOptTime && item.formattedOptTime.includes(keyword))
    );
  });
});

// 标记通知为已读
const handleMarkAsDeal = async (id: string) => {
  console.log('handleMarkAsDeal', id);
  await presenter.markAsDeal(id);
};

// 标记所有通知为已读
const handleMarkAllAsRead = async () => {
  try {
    await ElMessageBox.confirm('确认将筛选条件下的所有通知标记为已处理？', '确认操作', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const success = await presenter.markAllAsRead();
    if (success) {
      ElMessage.success('所有通知已标记为已处理');
    } else {
      ElMessage.error('处理失败，请重试');
    }
  } catch {
    // 用户取消操作
  }
};

// 状态筛选变更 - 呼叫通知
const handleStatusChange = (status: 'pending' | 'processed' | 'all') => {
  console.log('切换状态:', activeStatus.value, '->', status);
  activeStatus.value = status; // 先更新本地状态，确保UI立即响应

  // 修复：无论切换到哪种状态，都设置筛选条件并重新加载数据
  presenter.setFilterStatus(status);
  console.log('加载完成，当前状态:', presenter.viewModel.filter.status);
};

// 房间号搜索 - 呼叫通知 (调用后端接口)
const handleRoomNumberSearch = () => {
  presenter.searchNotifications(roomNumberKeyword.value);
};

// 本地搜索 - 呼叫通知
const handleLocalSearch = (keyword: string) => {
  localSearchKeyword.value = keyword;
};

// 上报呼叫
const handleReportCall = async () => {
  try {
    const result = await presenter.reportServiceCall(
      testIP.value || '', // 如果用户没有输入IP，使用默认IP
      'CALL_SERVICE',
      '呼叫服务员',
      'CLIENT'
    );

    if (result.success) {
      ElMessage.success('呼叫上报成功');
    } else {
      ElMessage.error('呼叫上报失败，请重试');
    }
  } catch (error) {
    ElMessage.error('呼叫上报异常');
    console.error('上报呼叫异常:', error);
  }
};

// 呼叫通知行样式计算
const rowClassName = ({ row }: { row: INotificationListItem }) => {
  // 如果是高亮行，添加高亮样式
  if (presenter.viewModel.highlightId && row.id === presenter.viewModel.highlightId) {
    return 'highlight-row';
  }
  return '';
};

// 导出到Excel
const exportToExcel = () => {
  if (filteredNotifications.value.length === 0) {
    ElMessage.warning('没有数据可导出');
    return;
  }

  try {
    // 这里可以实现实际的导出逻辑，例如使用库如xlsx等
    // 简单模拟导出功能
    const data = filteredNotifications.value.map(item => ({
      包厢号: item.roomNumber,
      呼叫内容: item.message,
      呼叫时间: item.formattedTimestamp,
      处理人: item.optName || '未知',
      处理时间: item.formattedOptTime || '未记录',
      状态: '已处理'
    }));

    // 在实际项目中，这里可以使用xlsx库导出为Excel文件
    // 这里只是显示一个成功提示
    console.log('导出数据:', data);
    ElMessage.success(`已导出${data.length}条记录`);
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败，请重试');
  }
};
</script>

<style scoped>
.notification-list-container {
  height: 100%;
}

.notification-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  border-bottom: 1px solid #f0f0f0;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
}

:deep(.highlight-row) {
  background-color: #fef9e7;
}

:deep(.el-table__empty-text) {
  width: 100%;
}
.notification-list {
  list-style: none;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.notification-item {
  height: 90px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none !important;
  margin-bottom: 8px;
}

.notification-item:last-child {
  margin-bottom: 0;
}
</style>
