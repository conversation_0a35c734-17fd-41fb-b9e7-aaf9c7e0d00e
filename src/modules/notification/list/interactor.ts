import { useNotificationStore } from '../stores/notificationStore';
import { notificationApiService, productionNotificationApiService } from '../api/notificationApiService';
import type { NotificationFilter } from '../types/notification';

/**
 * 通知列表交互器
 */
export class NotificationListInteractor {
  private notificationStore = useNotificationStore();

  /**
   * 获取呼叫通知列表
   * @param filters 筛选条件
   * @returns 通知列表和总数
   */
  async getNotifications(filters: NotificationFilter) {
    try {
      const result = await this.notificationStore.fetchNotificationList(filters);
      return {
        list: result.list,
        total: result.total
      };
    } catch (error) {
      console.error('获取呼叫通知列表失败:', error);
      return {
        list: [],
        total: 0
      };
    }
  }

  /**
   * 获取出品通知列表
   * @param filters 筛选条件
   * @returns 出品通知列表和总数
   */
  async getProductionNotifications(filters: NotificationFilter) {
    try {
      const result = await this.notificationStore.fetchProductionNotificationList(filters);
      return {
        list: result.list,
        total: result.total
      };
    } catch (error) {
      console.error('获取出品通知列表失败:', error);
      return {
        list: [],
        total: 0
      };
    }
  }

  /**
   * 标记呼叫通知为已读
   * @param id 通知ID
   * @returns 操作是否成功
   */
  async markNotificationAsDeal(id: string): Promise<boolean> {
    return this.notificationStore.markAsDeal(id);
  }

  /**
   * 标记所有呼叫通知为已读
   * @param filterParams 可选的筛选条件
   * @returns 操作是否成功
   */
  async markAllNotificationsAsRead(filterParams?: Partial<NotificationFilter>): Promise<boolean> {
    return this.notificationStore.markAllAsRead(filterParams);
  }

  /**
   * 处理出品通知
   * @param id 出品通知ID
   * @returns 操作是否成功
   */
  async processProductionNotification(id: string): Promise<boolean> {
    return this.notificationStore.processProductionNotification(id);
  }

  /**
   * 处理所有出品通知
   * @param filterParams 可选的筛选条件
   * @returns 操作是否成功
   */
  async processAllProductionNotifications(filterParams?: Partial<NotificationFilter>): Promise<boolean> {
    return this.notificationStore.processAllProductionNotifications(filterParams);
  }

  /**
   * 搜索呼叫通知
   * @param searchTerm 搜索关键词
   */
  async searchNotifications(searchTerm: string) {
    return this.notificationStore.searchNotifications(searchTerm);
  }

  /**
   * 搜索出品通知
   * @param searchTerm 搜索关键词
   */
  async searchProductionNotifications(searchTerm: string) {
    return this.notificationStore.searchProductionNotifications(searchTerm);
  }

  /**
   * 上报呼叫事件
   * @param ip 设备IP地址
   * @param callType 呼叫类型
   * @param callTypeName 呼叫类型名称
   * @param callSrc 呼叫来源
   * @returns 操作结果
   */
  async reportServiceCall(
    ip: string,
    callType: string = 'CALL_SERVICE',
    callTypeName: string = '呼叫服务员',
    callSrc: string = 'CLIENT'
  ): Promise<{ success: boolean; notificationId?: string }> {
    return this.notificationStore.reportServiceCall(ip, callType, callTypeName, callSrc);
  }
}
