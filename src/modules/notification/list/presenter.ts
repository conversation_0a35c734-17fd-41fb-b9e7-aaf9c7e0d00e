import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { NotificationFilter } from '../types/notification';
import { NotificationListConverter } from './converter';
import { NotificationListInteractor } from './interactor';
import type { 
  INotificationListViewModel, 
  INotificationListState, 
  INotificationListComputed, 
  INotificationListActions,
  INotificationListItem 
} from './viewmodel';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useNotificationStore } from '../stores/notificationStore';

/**
 * 通知列表Presenter - 严格遵循VIPER-VueC架构
 */
export class NotificationListPresenter implements INotificationListViewModel {
  private converter: NotificationListConverter;
  private interactor: NotificationListInteractor;
  private notificationStore: ReturnType<typeof useNotificationStore>;

  // UI状态 - 实现INotificationListState
  public state = reactive<INotificationListState>({
    ...NotificationListConverter.createInitialState(),
    isDev: import.meta.env.VITE_ENV !== 'product'
  });

  // UI计算属性 - 实现INotificationListComputed
  public computed: INotificationListComputed = {
    // 筛选后的通知列表
    filteredNotifications: computed(() => {
      if (!this.state.localSearchKeyword) {
        return this.state.notifications;
      }

      const keyword = this.state.localSearchKeyword.toLowerCase();
      return this.state.notifications.filter(item => {
        return (
          // 包厢号搜索
          item.roomNumber.toLowerCase().includes(keyword) ||
          // 处理人搜索
          (item.optName && item.optName.toLowerCase().includes(keyword)) ||
          // 消息内容搜索
          item.message.toLowerCase().includes(keyword) ||
          // 类型搜索
          item.type.toLowerCase().includes(keyword) ||
          // 时间搜索 (格式化后的时间)
          (item.formattedTimestamp && item.formattedTimestamp.includes(keyword)) ||
          // 处理时间搜索
          (item.formattedOptTime && item.formattedOptTime.includes(keyword))
        );
      });
    }),

    // 状态选项
    statusOptions: computed(() => [
      { value: 'pending' as const, label: '未处理' },
      { value: 'processed' as const, label: '已处理' }
    ]),

    // 通知总数
    totalCount: computed(() => this.computed.filteredNotifications.value.length)
  };

  // UI行为 - 实现INotificationListActions
  public actions: INotificationListActions = {
    // 数据加载
    loadNotifications: async (): Promise<void> => {
      try {
        this.state.isLoading = true;
        const result = await this.interactor.getNotifications(this.state.filter);
        this.state.notifications = this.converter.convertNotificationsToViewModel(result.list);
      } catch (error) {
        console.error('加载呼叫通知列表失败:', error);
      } finally {
        this.state.isLoading = false;
      }
    },

    loadProductionNotifications: async (): Promise<void> => {
      try {
        this.state.isProductionLoading = true;
        const result = await this.interactor.getProductionNotifications(this.state.productionFilter);
        this.state.productionNotifications = this.converter.convertProductionNotificationsToViewModel(result.list);
      } catch (error) {
        console.error('加载出品通知列表失败:', error);
      } finally {
        this.state.isProductionLoading = false;
      }
    },

    // 通知处理
    markAsDeal: async (notificationId: string): Promise<boolean> => {
      try {
        const success = await this.interactor.markNotificationAsDeal(notificationId);
        if (success) {
          ElMessage.success('消息处理成功');
          // 重新加载通知列表
          await this.actions.loadNotifications();
        } else {
          ElMessage.error('消息处理失败');
        }
        return success;
      } catch (error) {
        console.error('标记通知已处理失败:', error);
        ElMessage.error('消息处理失败：' + error);
        return false;
      }
    },

    markAllAsRead: async (): Promise<boolean> => {
      try {
        const success = await this.interactor.markAllNotificationsAsRead(this.state.filter);
        if (success) {
          // 重新加载通知列表
          await this.actions.loadNotifications();
        }
        return success;
      } catch (error) {
        console.error('标记所有通知已读失败:', error);
        return false;
      }
    },

    processProductionNotification: async (notificationId: string): Promise<boolean> => {
      try {
        const success = await this.interactor.processProductionNotification(notificationId);
        if (success) {
          // 重新加载出品通知列表
          await this.actions.loadProductionNotifications();
        }
        return success;
      } catch (error) {
        console.error('处理出品通知失败:', error);
        return false;
      }
    },

    processAllProductionNotifications: async (): Promise<boolean> => {
      try {
        const success = await this.interactor.processAllProductionNotifications(this.state.productionFilter);
        if (success) {
          // 重新加载出品通知列表
          await this.actions.loadProductionNotifications();
        }
        return success;
      } catch (error) {
        console.error('处理所有出品通知失败:', error);
        return false;
      }
    },

    // 筛选和搜索
    setFilterStatus: (status: 'pending' | 'processed' | 'all'): void => {
      console.log('[Presenter] 设置筛选状态:', this.state.filter.status, '->', status);

      // 清除之前的数据，显示加载状态
      this.state.isLoading = true;

      // 更新筛选条件
      this.state.filter = {
        ...this.state.filter,
        status
      };

      // 加载新数据
      this.actions.loadNotifications().then(() => {
        console.log('[Presenter] 筛选后数据加载完成，获取到', this.state.notifications.length, '条记录');
      });
    },

    setProductionFilterStatus: (status: 'pending' | 'processed' | 'all'): void => {
      this.state.productionFilter.status = status;
      this.actions.loadProductionNotifications();
    },

    handleStatusChange: (status: 'pending' | 'processed' | 'all'): void => {
      console.log('切换状态:', this.state.activeStatus, '->', status);
      this.state.activeStatus = status; // 先更新本地状态，确保UI立即响应

      // 修复：无论切换到哪种状态，都设置筛选条件并重新加载数据
      this.actions.setFilterStatus(status);
      console.log('加载完成，当前状态:', this.state.filter.status);
    },

    handleLocalSearch: (keyword: string): void => {
      this.state.localSearchKeyword = keyword;
    },

    searchNotifications: (searchTerm: string): void => {
      const filter = { ...this.state.filter, roomNumber: searchTerm };
      this.state.filter = filter;
      this.actions.loadNotifications();
    },

    searchProductionNotifications: (searchTerm: string): void => {
      const filter = { ...this.state.productionFilter, roomNumber: searchTerm };
      this.state.productionFilter = filter;
      this.actions.loadProductionNotifications();
    },

    // 业务操作
    reportServiceCall: async (
      ip: string,
      callType: string = 'CALL_SERVICE',
      callTypeName: string = '呼叫服务员',
      callSrc: string = 'CLIENT'
    ): Promise<{ success: boolean; notificationId?: string }> => {
      try {
        const result = await this.interactor.reportServiceCall(ip, callType, callTypeName, callSrc);
        if (result.success) {
          // 刷新呼叫通知列表
          await this.actions.loadNotifications();

          // 设置高亮ID
          if (result.notificationId) {
            this.state.highlightId = result.notificationId;

            // 3秒后清除高亮
            setTimeout(() => {
              this.state.highlightId = null;
            }, 3000);
          }
        }
        return result;
      } catch (error) {
        console.error('上报呼叫事件失败:', error);
        return { success: false };
      }
    },

    // UI交互
    rowClassName: ({ row }: { row: INotificationListItem }): string => {
      // 如果是高亮行，添加高亮样式
      if (this.state.highlightId && row.id === this.state.highlightId) {
        return 'highlight-row';
      }
      return '';
    },

    exportToExcel: (): void => {
      if (this.computed.filteredNotifications.value.length === 0) {
        ElMessage.warning('没有数据可导出');
        return;
      }

      try {
        // 这里可以实现实际的导出逻辑，例如使用库如xlsx等
        // 简单模拟导出功能
        const data = this.computed.filteredNotifications.value.map(item => ({
          包厢号: item.roomNumber,
          呼叫内容: item.message,
          呼叫时间: item.formattedTimestamp,
          处理人: item.optName || '未知',
          处理时间: item.formattedOptTime || '未记录',
          状态: this.actions.getStatusText(item.rawStatus)
        }));

        // 在实际项目中，这里可以使用xlsx库导出为Excel文件
        // 这里只是显示一个成功提示
        console.log('导出数据:', data);
        ElMessage.success(`已导出${data.length}条记录`);
      } catch (error) {
        console.error('导出失败:', error);
        ElMessage.error('导出失败，请重试');
      }
    },

    // 状态转换方法
    getStatusText: (status: number): string => {
      switch (status) {
        case 0:
          return '未处理';
        case 1:
          return '已处理';
        case 2:
          return '已取消';
        default:
          return '未知';
      }
    },

    getStatusClass: (status: number): string => {
      switch (status) {
        case 0:
          return 'text-orange-500'; // 未处理 - 橙色
        case 1:
          return 'text-gray-500'; // 已处理 - 绿色
        case 2:
          return 'text-gray-400'; // 已取消 - 红色
        default:
          return 'text-gray-500'; // 未知 - 灰色
      }
    }
  };

  constructor() {
    this.converter = new NotificationListConverter();
    this.interactor = new NotificationListInteractor();
    this.notificationStore = useNotificationStore();

    this.setupLifecycles();
  }

  /**
   * 设置生命周期和路由解析
   */
  private setupLifecycles(): void {
    // 从路由解析高亮ID
    const route = useRoute();
    if (route.query.highlight) {
      this.state.highlightId = route.query.highlight as string;
    }

    // 监听activeStatus变化并同步到filter.status
    watch(
      () => this.state.filter.status,
      newStatus => {
        this.state.activeStatus = newStatus;
      }
    );

    onMounted(() => {
      // 加载呼叫通知
      this.actions.loadNotifications();
    });

    // 监听notificationStore的数据变化，实现实时更新
    watch(
      () => this.notificationStore.notifications,
      (newNotifications) => {
        // 当store中的notifications更新时，检查当前是否在显示未处理状态
        if (this.state.activeStatus === 'pending') {
          console.log('[Presenter] 检测到通知数据更新，自动刷新列表');
          // 转换并更新本地状态
          this.state.notifications = this.converter.convertNotificationsToViewModel(newNotifications);
        }
      },
      { deep: true, immediate: false }
    );

    // 监听未读通知数量变化
    watch(
      () => this.notificationStore.unreadCount,
      (newCount, oldCount) => {
        // 当未读数量发生变化时，如果当前显示的是未处理状态，刷新数据
        if (this.state.activeStatus === 'pending' && newCount !== oldCount) {
          console.log('[Presenter] 未读通知数量变化，自动刷新列表');
          this.actions.loadNotifications();
        }
      }
    );
  }

  /**
   * 标记所有通知为已读的确认处理
   */
  public async handleMarkAllAsRead(): Promise<void> {
    try {
      await ElMessageBox.confirm('确认将筛选条件下的所有通知标记为已处理？', '确认操作', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      });

      const success = await this.actions.markAllAsRead();
      if (success) {
        ElMessage.success('所有通知已标记为已处理');
      } else {
        ElMessage.error('处理失败，请重试');
      }
    } catch {
      // 用户取消操作
    }
  }
}

/**
 * 通知列表Presenter的组合函数 - 供View层使用
 */
export function useNotificationListPresenter(): INotificationListViewModel {
  return new NotificationListPresenter();
}
