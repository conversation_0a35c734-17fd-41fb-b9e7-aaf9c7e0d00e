import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import type { NotificationFilter } from '../types/notification';
import { NotificationListConverter } from './converter';
import { NotificationListInteractor } from './interactor';
import type { INotificationListViewModel, INotificationListActions, INotificationListItem, IProductionNotificationListItem } from './viewmodel';
import { ElMessage } from 'element-plus';

/**
 * 通知列表Presenter
 */
export class NotificationListPresenter implements INotificationListActions {
  private converter: NotificationListConverter;
  private interactor: NotificationListInteractor;

  // 视图模型
  public viewModel = reactive<INotificationListViewModel>({
    // 呼叫通知
    isLoading: false,
    notifications: [],
    filter: {
      dateRange: null,
      roomNumber: '',
      status: 'pending' // 默认展示未处理的通知
    },
    highlightId: null,

    // 出品通知
    isProductionLoading: false,
    productionNotifications: [],
    productionFilter: {
      dateRange: null,
      roomNumber: '',
      status: 'pending' // 默认展示未处理的通知
    },
    productionHighlightId: null
  });

  constructor() {
    this.converter = new NotificationListConverter();
    this.interactor = new NotificationListInteractor();

    // 从路由解析高亮ID
    const route = useRoute();
    if (route.query.highlight) {
      this.viewModel.highlightId = route.query.highlight as string;
    }
  }

  /**
   * 初始化
   */
  init() {
    onMounted(() => {
      // 加载呼叫通知和出品通知
      this.loadNotifications();
      // this.loadProductionNotifications();
    });
  }

  /**
   * 加载呼叫通知列表
   */
  async loadNotifications(): Promise<void> {
    try {
      this.viewModel.isLoading = true;
      const result = await this.interactor.getNotifications(this.viewModel.filter);
      this.viewModel.notifications = this.converter.convertNotificationsToViewModel(result.list);
    } catch (error) {
      console.error('加载呼叫通知列表失败:', error);
    } finally {
      this.viewModel.isLoading = false;
    }
  }

  /**
   * 加载出品通知列表
   */
  async loadProductionNotifications(): Promise<void> {
    try {
      this.viewModel.isProductionLoading = true;
      const result = await this.interactor.getProductionNotifications(this.viewModel.productionFilter);
      this.viewModel.productionNotifications = this.converter.convertProductionNotificationsToViewModel(result.list);
    } catch (error) {
      console.error('加载出品通知列表失败:', error);
    } finally {
      this.viewModel.isProductionLoading = false;
    }
  }

  /**
   * 标记呼叫通知为已读
   * @param notificationId 通知ID
   */
  async markAsDeal(notificationId: string): Promise<boolean> {
    try {
      const success = await this.interactor.markNotificationAsDeal(notificationId);
      if (success) {
        // 重新加载通知列表
        ElMessage.success('消息处理成功');
      } else {
        ElMessage.error('消息处理失败');
      }
      await this.loadNotifications();
      return success;
    } catch (error) {
      console.error('标记通知已处理失败:', error);
      ElMessage.error('消息处理失败：' + error);
      return false;
    }
  }

  /**
   * 标记所有呼叫通知为已读
   */
  async markAllAsRead(): Promise<boolean> {
    try {
      const success = await this.interactor.markAllNotificationsAsRead(this.viewModel.filter);
      if (success) {
        // 重新加载通知列表
        await this.loadNotifications();
      }
      return success;
    } catch (error) {
      console.error('标记所有通知已读失败:', error);
      return false;
    }
  }

  /**
   * 处理出品通知
   * @param notificationId 出品通知ID
   */
  async processProductionNotification(notificationId: string): Promise<boolean> {
    try {
      const success = await this.interactor.processProductionNotification(notificationId);
      if (success) {
        // 重新加载出品通知列表
        await this.loadProductionNotifications();
      }
      return success;
    } catch (error) {
      console.error('处理出品通知失败:', error);
      return false;
    }
  }

  /**
   * 处理所有出品通知
   */
  async processAllProductionNotifications(): Promise<boolean> {
    try {
      const success = await this.interactor.processAllProductionNotifications(this.viewModel.productionFilter);
      if (success) {
        // 重新加载出品通知列表
        await this.loadProductionNotifications();
      }
      return success;
    } catch (error) {
      console.error('处理所有出品通知失败:', error);
      return false;
    }
  }

  /**
   * 设置呼叫通知筛选状态
   * @param status 状态
   */
  setFilterStatus(status: 'pending' | 'processed' | 'all'): void {
    console.log('[Presenter] 设置筛选状态:', this.viewModel.filter.status, '->', status);

    // 清除之前的数据，显示加载状态
    this.viewModel.isLoading = true;

    // 更新筛选条件
    this.viewModel.filter = {
      ...this.viewModel.filter,
      status
    };

    // 加载新数据
    this.loadNotifications().then(() => {
      console.log('[Presenter] 筛选后数据加载完成，获取到', this.viewModel.notifications.length, '条记录');
    });
  }

  /**
   * 设置出品通知筛选状态
   * @param status 状态
   */
  setProductionFilterStatus(status: 'pending' | 'processed' | 'all'): void {
    this.viewModel.productionFilter.status = status;
    this.loadProductionNotifications();
  }

  /**
   * 搜索呼叫通知
   * @param searchTerm 搜索关键词
   */
  searchNotifications(searchTerm: string): void {
    const filter = { ...this.viewModel.filter, roomNumber: searchTerm };
    this.viewModel.filter = filter;
    this.loadNotifications();
  }

  /**
   * 搜索出品通知
   * @param searchTerm 搜索关键词
   */
  searchProductionNotifications(searchTerm: string): void {
    const filter = { ...this.viewModel.productionFilter, roomNumber: searchTerm };
    this.viewModel.productionFilter = filter;
    this.loadProductionNotifications();
  }

  /**
   * 上报呼叫事件
   * @param ip 设备IP地址
   * @param callType 呼叫类型
   * @param callTypeName 呼叫类型名称
   * @param callSrc 呼叫来源
   */
  async reportServiceCall(
    ip: string,
    callType: string = 'CALL_SERVICE',
    callTypeName: string = '呼叫服务员',
    callSrc: string = 'CLIENT'
  ): Promise<{ success: boolean; notificationId?: string }> {
    try {
      const result = await this.interactor.reportServiceCall(ip, callType, callTypeName, callSrc);
      if (result.success) {
        // 刷新呼叫通知列表
        await this.loadNotifications();

        // 设置高亮ID
        if (result.notificationId) {
          this.viewModel.highlightId = result.notificationId;

          // 3秒后清除高亮
          setTimeout(() => {
            this.viewModel.highlightId = null;
          }, 3000);
        }
      }
      return result;
    } catch (error) {
      console.error('上报呼叫事件失败:', error);
      return { success: false };
    }
  }

  /**
   * 模拟房间状态变更触发通知
   * @deprecated 已弃用，使用reportServiceCall替代
   */
  async mockRoomStatusChangedEvent(): Promise<{ success: boolean; notificationId?: string }> {
    console.warn('此方法已弃用，请使用reportServiceCall替代');
    return { success: false };
  }
}
