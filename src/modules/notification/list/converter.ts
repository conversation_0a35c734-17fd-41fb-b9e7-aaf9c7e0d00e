import type { RoomNotifyPayload, ProductionNotifyPayload } from '../types/notification';
import type { INotificationListItem, IProductionNotificationListItem } from './viewmodel';
import dayjs from 'dayjs';

/**
 * 通知列表转换器
 */
export class NotificationListConverter {
  /**
   * 将原始呼叫通知数据转换为视图模型项
   * @param payload 原始通知数据
   * @returns 视图模型项
   */
  convertNotificationToViewModel(payload: RoomNotifyPayload): INotificationListItem {
    return {
      ...payload,
      formattedTimestamp: this.formatTimestamp(payload.timestamp),
      formattedOptTime: payload.optTime ? this.formatTimestamp(payload.optTime) : ''
    };
  }

  /**
   * 将原始呼叫通知数据列表转换为视图模型项列表
   * @param payloads 原始通知数据列表
   * @returns 视图模型项列表
   */
  convertNotificationsToViewModel(payloads: RoomNotifyPayload[]): INotificationListItem[] {
    return payloads.map(payload => this.convertNotificationToViewModel(payload));
  }

  /**
   * 将原始出品通知数据转换为视图模型项
   * @param payload 原始出品通知数据
   * @returns 视图模型项
   */
  convertProductionNotificationToViewModel(payload: ProductionNotifyPayload): IProductionNotificationListItem {
    return {
      ...payload,
      formattedTimestamp: this.formatTimestamp(payload.timestamp)
    };
  }

  /**
   * 将原始出品通知数据列表转换为视图模型项列表
   * @param payloads 原始出品通知数据列表
   * @returns 视图模型项列表
   */
  convertProductionNotificationsToViewModel(payloads: ProductionNotifyPayload[]): IProductionNotificationListItem[] {
    return payloads.map(payload => this.convertProductionNotificationToViewModel(payload));
  }

  /**
   * 格式化时间戳
   * @param timestamp 时间戳
   * @returns 格式化后的时间字符串
   */
  private formatTimestamp(timestamp: number): string {
    return dayjs(timestamp).format('MM-DD HH:mm:ss');
  }
}
