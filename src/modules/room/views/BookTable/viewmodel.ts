import { ComputedRef } from 'vue';
import type { BookingVO, RoomVO } from '@/types/projectobj';

// 搜索参数类型
export interface BookingSearchParams {
  customerPhone: string;
  customerName: string;
  roomId: string;
  customerSource: string;
  status?: number; // 添加status字段用于状态筛选
  arrivalTimeStart?: number; // 开始时间
  arrivalTimeEnd?: number; // 结束时间
  startDate?: string;
  endDate?: string;
}

// 表格数据状态
export interface IBookingTableState {
  tableData: BookingVO[];
  loading: boolean;
}

// 搜索表单状态
export interface IBookingSearchState {
  searchForm: BookingSearchParams;
  dateRange: string[];
  roomList: RoomVO[];
}

// UI状态接口
export interface IBookingState extends IBookingTableState, IBookingSearchState {}

// UI计算属性接口
export interface IBookingComputed {
  // 源选项
  sourceOptions: ComputedRef<Array<{ label: string; value: string }>>;
  // 状态选项
  statusOptions: ComputedRef<Array<{ label: string; value: number }>>;

  getRoomStatusText(status: string): string;
  // 是否可以编辑
  canEdit(booking: BookingVO): ComputedRef<boolean>;
  // 是否可以开台
  canStartTable(booking: BookingVO): ComputedRef<boolean>;
  // 是否可以撤销
  canCancelBooking(booking: BookingVO): ComputedRef<boolean>;
}

// UI表格动作接口
export interface IBookingTableActions {
  handleViewDetails(booking: BookingVO): void;
  handleStartTable(booking: BookingVO): void;
  handleCancelBooking(booking: BookingVO): void;
  loadData(): Promise<void>;
  refresh(): void;
}

// UI搜索动作接口
export interface IBookingSearchActions {
  handleSearchInput(): void;
  handleDateChange(val: string[] | null): void;
  handleSearch(): void;
  resetFilters(): void;
  handleAddBooking(): void;
  handleStatusChange(status: number | null): void; // 添加状态变化处理方法
}

// 格式化工具函数接口
export interface IBookingFormatters {
  getBookingSourceType(source: string): string;
  getBookingSourceText(source: string): string;
  getBookingStatusType(status: number): string;
  getBookingStatusText(status: number): string;
  formatUnixTimestamp(timestamp: number): string;
  formatYuanWithSymbol(amount: number): string;
  formatGenderText(gender: any): string;
}

// UI动作接口
export interface IBookingActions extends IBookingTableActions, IBookingSearchActions {}

// 事件接口
export interface IBookingEvents {
  'booking-added': (result: any) => void;
}

// 总的ViewModel接口
export interface IBookingViewModel {
  state: IBookingState;
  computed: IBookingComputed;
  actions: IBookingActions;
  formatters: IBookingFormatters;
  emit: (event: keyof IBookingEvents, ...args: any[]) => void;
}
