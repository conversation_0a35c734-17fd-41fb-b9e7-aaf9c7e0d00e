<template>
  <div class="flex flex-col h-full">
    <!-- 导航栏 -->
    <nav class="p-[24px] border-b">
      <div class="flex items-center justify-between">
        <!-- 左侧标签页 -->
        <div class="flex bg-[#F3F3F3] rounded-[10px] h-[68px] w-[224px] px-2 items-center">
          <router-link
            to="/room/realtimetable"
            class="flex-1 h-[52px] w-[100px] font-medium rounded-[6px] text-gray-500 flex items-center justify-center"
            active-class="bg-btn-focus text-white"
            exact>
            实时包厢
          </router-link>
          <router-link
            to="/room/booking"
            class="flex-1 h-[52px] w-[100px] font-medium text-gray-500 rounded-[6px] flex items-center justify-center"
            active-class="bg-btn-focus text-white border-transparent"
            exact>
            预定管理
          </router-link>
        </div>

        <!-- 中间搜索区域 -->
        <div class="flex-1 flex justify-center ml-4">
          <div class="relative w-full">
            <el-input v-model="searchKeyword" placeholder="搜索客人电话/姓名/包厢名称" class="custom-search-input" @input="handleSearch" clearable>
              <template #prefix>
                <el-icon class="ml-4">
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 状态筛选下拉框 -->
        <div class="ml-[24px]">
          <el-dropdown @command="handleStatusFilter" trigger="click" class="customer-dropdown sort-dropdown w-[152px] h-[68px]">
            <div class="flex flex-col justify-center items-center bg-white rounded-[10px] h-full w-full cursor-pointer shadow-sm">
              <div class="text-xs text-gray-400">状态筛选</div>
              <div class="flex items-center text-sm">
                {{ getCurrentStatusLabel() }}
                <el-icon class="ml-1">
                  <ArrowDown />
                </el-icon>
              </div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="option in vm.computed.statusOptions.value" :key="option.value" :command="option.value">
                  <el-icon v-if="vm.state.searchForm.status === option.value || (option.value === -1 && vm.state.searchForm.status === undefined)" class="mr-1">
                    <Check />
                  </el-icon>
                  {{ option.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 预定日期筛选 -->
        <div class="flex items-center ml-[24px]">
          <el-date-picker
            v-model="vm.state.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            class="customer-date-picker"
            @change="vm.actions.handleDateChange" />
        </div>
        <!-- 右侧按钮区域 -->
        <div class="ml-[24px]">
          <el-button type="normal" class="h-[68px] px-6 text-lg" @click="vm.actions.handleAddBooking">新增预定</el-button>
        </div>
      </div>
    </nav>
    <!-- 列表内容区域 -->
    <div class="flex-1 p-4 overflow-auto">
      <!-- 表格组件 -->
      <div class="flex flex-col h-full">
        <el-table v-loading="vm.state.loading" :data="vm.state.tableData" stripe class="flex-grow" height="100%" label-class-name="table-label">
          <!-- 客人电话列 -->
          <el-table-column prop="customerPhone" label="客人电话" min-width="10%" align="center">
            <template #default="{ row }">
              <div class="flex items-center text-[20px] justify-center">{{ row.customerPhone }}</div>
            </template>
          </el-table-column>

          <el-table-column label="客人姓名" align="center" min-width="10%">
            <template #default="{ row }">
              <div class="text-[20px]">
                {{ row.customerName }}
                <span class="text-gray-500">{{ vm.formatters.formatGenderText(row.gender) }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 订单来源列 -->
          <el-table-column label="订单来源" align="center" min-width="10%">
            <template #default="{ row }">
              <span class="text-[20px]">{{ row.customerSource ? vm.formatters.getBookingSourceText(row.customerSource) : '--' }}</span>
            </template>
          </el-table-column>

          <!-- 包厢名称列 -->
          <el-table-column prop="roomName" label="包厢" min-width="10%" align="center">
            <template #default="{ row }">
              <div class="text-[20px]">
                {{ row.roomName }}
                <span class="text-gray-500 text-sm ml-1">({{ vm.computed.getRoomStatusText(row.stageRoomStatus) }})</span>
              </div>
            </template>
          </el-table-column>

          <!-- 包厢类型列 -->
          <el-table-column label="包厢类型" align="center" min-width="10%">
            <template #default="{ row }">
              <div class="text-[20px]">{{ row.stageRoomType || '--' }}</div>
            </template>
          </el-table-column>

          <!-- 预抵时间列 -->
          <el-table-column label="预抵时间" min-width="10%">
            <template #default="{ row }">
              <div class="text-[20px]">{{ vm.formatters.formatUnixTimestamp(row.arrivalTime) }}</div>
            </template>
          </el-table-column>
          <!-- 备注列 -->
          <el-table-column label="备注" min-width="10%">
            <template #default="{ row }">
              <div
                class="text-[16px] text-gray-700 leading-tight max-h-[2.5em] overflow-hidden"
                :class="{ 'line-clamp-2': row.remark }"
                :title="row.remark || ''">
                {{ row.remark || '--' }}
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" min-width="30%" align="center">
            <template #default="{ row }">
              <div class="flex gap-2">
                <!-- 已取消状态 -->
                <span v-if="row.status === 2" class="text-red-500 px-4">已取消</span>
                <!-- 已开台状态 -->
                <span v-else-if="row.status === 1" class="text-green-500 px-4">已开台</span>
                <!-- 未开台状态 -->
                <div v-else>
                  <!-- 开台按钮 -->
                  <el-button
                    v-if="vm.computed.canStartTable(row).value"
                    @click="vm.actions.handleStartTable(row)"
                    class="btn-option"
                    :disabled="row.stageRoomStatus === 'in_use'">
                    开台
                  </el-button>
                  <!-- 编辑按钮 -->
                  <el-button v-if="vm.computed.canEdit(row).value" @click="vm.actions.handleViewDetails(row)" class="btn-option"> 编辑 </el-button>
                  <!-- 撤销按钮 -->
                  <el-button v-if="vm.computed.canCancelBooking(row).value" @click="vm.actions.handleCancelBooking(row)" class="btn-option"> 撤销 </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Search, ArrowDown, Check } from '@element-plus/icons-vue';
import { useBooking } from './presenter';

// 使用VIPER架构的视图模型
const vm = useBooking();
const route = useRoute();

// 统一搜索关键词
const searchKeyword = ref('');

// 处理统一搜索
const handleSearch = () => {
  const keyword = searchKeyword.value.trim().toLowerCase();

  // 将关键词应用到customerPhone字段即可
  // presenter中的搜索逻辑会处理跨字段的搜索
  vm.state.searchForm.customerPhone = keyword;
  vm.state.searchForm.customerName = '';
  vm.state.searchForm.roomId = '';

  // 触发搜索
  vm.actions.handleSearchInput();
};

// 获取当前选中的状态标签
const getCurrentStatusLabel = () => {
  const status = vm.state.searchForm.status;
  // 如果状态未定义或为-1，显示"全部"
  if (status === undefined || status === -1) {
    return '全部';
  }
  const option = vm.computed.statusOptions.value.find(opt => opt.value === status);
  return option ? option.label : '全部'; // 默认显示全部
};

// 处理状态过滤
const handleStatusFilter = (status: number) => {
  vm.actions.handleStatusChange(status);
};

// 初始化
vm.initialize();

// 处理路由参数中的 roomName
const roomName = route.query.roomName as string;
if (roomName) {
  // 监听数据加载变化，简单方式
  watch(
    () => vm.state.tableData,
    newData => {
      if (newData && newData.length > 0) {
        // 只在第一次数据加载后设置一次
        if (searchKeyword.value === '') {
          searchKeyword.value = roomName;
          handleSearch();
        }
      }
    }
  );
}
</script>

<style scoped>
/* 自定义搜索框样式 */
:deep(.custom-search-input) {
  width: 100%;
  height: 68px;
  border-radius: 10px;
}

:deep(.custom-search-input .el-input__wrapper) {
  border-radius: 10px;
  height: 68px;
  padding-left: 20px;
  box-shadow: 0 0 0 1px #e0e0e0 inset;
}

:deep(.custom-search-input .el-input__inner) {
  height: 68px;
  font-size: 16px;
}

:deep(.el-table__header .cell) {
  font-size: 20px;
  padding-bottom: 12px;
}

.btn-option {
  border: 1px solid #ddd;
  width: 120px;
  height: 56px;
  border-radius: 8px;
  font-size: 16px;
  margin-right: 12px;
}

.btn-option:hover {
  background-color: #f5f5f5;
}

/* 状态筛选下拉框样式 */
.customer-dropdown {
  display: flex;
  justify-content: center;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 0 0 1px #e0e0e0 inset;
}

/* 备注显示限制两行样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
