import { ref, reactive, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useRoomStore } from '@/modules/room/store/roomStore';
import { useStageStore } from '@/stores/stageStore';
import type { BookingVO, RoomVO } from '@/types/projectobj';
import type { IBookingViewModel, IBookingState, IBookingComputed, IBookingActions, IBookingFormatters, BookingSearchParams, IBookingEvents } from './viewmodel';
import { BookingInteractor } from './interactor';
import { BookingViewModelConverter } from './converter';
import { DialogManager } from '@/utils/dialog';
import { ElMessage, ElMessageBox } from 'element-plus';

/**
 * 预订管理Presenter实现
 */
export class BookingPresenter implements IBookingViewModel {
  // 依赖注入
  private router = useRouter();
  private roomStore = useRoomStore();
  private stageStore = useStageStore();
  private interactor = new BookingInteractor();
  private converter = new BookingViewModelConverter();
  private eventHandlers: Map<keyof IBookingEvents, Function[]> = new Map();

  // 原始数据缓存 - 用于本地搜索
  private originalData: BookingVO[] = [];

  // 实现emit方法
  public emit(event: keyof IBookingEvents, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(...args));
    }
  }

  // 添加事件监听
  public on(event: keyof IBookingEvents, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)?.push(handler);
  }

  // 移除事件监听
  public off(event: keyof IBookingEvents, handler: Function): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // UI状态
  public state: IBookingState = reactive({
    // 表格数据
    tableData: [],
    loading: false,

    // 搜索表单
    searchForm: {
      customerPhone: '',
      customerName: '',
      roomId: '',
      customerSource: '',
      status: 0,
      arrivalTimeStart: this.getStartOfDay(),
      arrivalTimeEnd: this.getEndOfDay(7)
    },
    dateRange: [],
    roomList: []
  });

  // 计算属性
  public computed: IBookingComputed = {
    sourceOptions: computed(() => [
      { label: '线上', value: 'ONLINE' },
      { label: '电话', value: 'PHONE' },
      { label: '现场', value: 'ONSITE' },
      { label: '抖音', value: 'DOUYIN' }
    ]),
    statusOptions: computed(() => [
      { label: '全部', value: -1 },
      { label: '预订中', value: 0 },
      { label: '已开台', value: 1 },
      { label: '已取消', value: 2 }
    ]),
    canStartTable: (booking: BookingVO) =>
      computed(() => {
        const bookingWithStatus = booking as BookingVO & { stageRoomStatus?: string };
        return booking.status === 0 && bookingWithStatus.stageRoomStatus !== 'in_use';
      }),
    canEdit: (booking: BookingVO) =>
      computed(() => {
        return booking.status !== 2;
      }),
    canCancelBooking: (booking: BookingVO) =>
      computed(() => {
        return booking.status !== 2;
      }),

    getRoomStatusText: (status: string) => {
      switch (status) {
        case 'idle':
          return '空闲';
        case 'in_use':
          return '使用中';
        case 'fault':
          return '故障';
        case 'cleaning':
          return '清洁中';
        case 'with_guest':
          return '带客';
        default:
          return '未知';
      }
    }
  };

  // 格式化工具
  public formatters: IBookingFormatters = {
    getBookingSourceType: (source: string) => this.converter.getBookingSourceType(source),
    getBookingSourceText: (source: string) => this.converter.getBookingSourceText(source),
    getBookingStatusType: (status: number) => this.converter.getBookingStatusType(status),
    getBookingStatusText: (status: number) => this.converter.getBookingStatusText(status),
    formatUnixTimestamp: (timestamp: number) => this.converter.formatTimestamp(timestamp),
    formatYuanWithSymbol: (amount: number) => this.converter.formatAmount(amount),
    formatGenderText: (gender: any) => {
      // 处理各种可能的数据类型
      if (gender === 0 || gender === '0') {
        return '(先生)';
      } else if (gender === 1 || gender === '1') {
        return '(女士)';
      }
      return ''; // 如果性别信息不明确，返回空字符串
    }
  };

  // 获取当天开始时间的时间戳
  private getStartOfDay(offsetDays = 0): number {
    const date = new Date();
    if (offsetDays !== 0) {
      date.setDate(date.getDate() + offsetDays);
    }
    date.setHours(0, 0, 0, 0);
    return Math.floor(date.getTime() / 1000);
  }

  // 获取当天结束时间的时间戳
  private getEndOfDay(offsetDays = 0): number {
    const date = new Date();
    if (offsetDays !== 0) {
      date.setDate(date.getDate() + offsetDays);
    }
    date.setHours(23, 59, 59, 999);
    return Math.floor(date.getTime() / 1000);
  }

  // 获取Stage信息 - 仅用于开台操作
  private getStageInfo(roomId: string) {
    return this.stageStore.getStageByRoomId(roomId);
  }

  // 限制日期范围最大为30天
  private enforceDateRangeLimit(startDate: Date, endDate: Date): [Date, Date] {
    const maxDays = 30;
    const diffDays = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays > maxDays) {
      // 如果超过30天，则将开始日期设置为结束日期前30天
      const newStartDate = new Date(endDate);
      newStartDate.setDate(endDate.getDate() - maxDays);
      return [newStartDate, endDate];
    }

    return [startDate, endDate];
  }

  // 本地搜索过滤
  public applyLocalSearch(): void {
    if (!this.originalData.length) return;

    const searchForm = this.state.searchForm;
    let filteredData = [...this.originalData];

    // 先应用日期筛选
    if (searchForm.arrivalTimeStart && searchForm.arrivalTimeEnd) {
      filteredData = filteredData.filter(booking => {
        return booking.arrivalTime >= searchForm.arrivalTimeStart! && booking.arrivalTime <= searchForm.arrivalTimeEnd!;
      });
    }

    // 应用模糊搜索
    const phoneQuery = searchForm.customerPhone.toLowerCase();
    const nameQuery = searchForm.customerName.toLowerCase();
    const roomQuery = searchForm.roomId.toLowerCase();

    // 如果任何一个字段有搜索词
    if (phoneQuery || nameQuery || roomQuery) {
      const combinedQuery = phoneQuery || nameQuery || roomQuery;

      filteredData = filteredData.filter(booking => {
        // 检查电话号码匹配
        const phoneMatch = booking.customerPhone && booking.customerPhone.toLowerCase().includes(combinedQuery);

        // 检查姓名匹配
        const nameMatch = booking.customerName && booking.customerName.toLowerCase().includes(combinedQuery);

        // 检查包厢名匹配
        const roomMatch = booking.roomName && booking.roomName.toLowerCase().includes(combinedQuery);

        // 任何一个字段匹配就返回true
        return phoneMatch || nameMatch || roomMatch;
      });
    }

    // 应用来源筛选
    if (searchForm.customerSource) {
      filteredData = filteredData.filter(booking => booking.customerSource === searchForm.customerSource);
    }

    // 应用状态筛选
    if (searchForm.status !== undefined && searchForm.status !== -1) {
      filteredData = filteredData.filter(booking => booking.status === searchForm.status);
    }

    this.state.tableData = filteredData;
  }

  // UI动作
  public actions: IBookingActions = {
    // 表格相关动作
    handleViewDetails: async (booking: BookingVO) => {
      try {
        // 使用对话框代替路由跳转
        await DialogManager.open('PreTableDialog', {
          roomId: booking.roomId,
          mode: 'edit',
          bookingId: booking.id
        })
          .then(result => {
            console.log('预订编辑成功，结果:', result);
            // 刷新当前数据
            this.actions.refresh();
          })
          .catch(error => {});
      } catch (error) {
        console.error('打开预订编辑对话框失败:', error);
        ElMessage.error('打开预订编辑对话框失败');
      }
    },
    handleStartTable: (booking: BookingVO) => {
      const stage = this.getStageInfo(booking.roomId);
      if (!stage) {
        console.error('未找到对应的Stage信息');
        return;
      }
      this.router.push({
        name: 'room-opentable',
        query: {
          roomId: booking.roomId,
          bookingId: booking.id,
          typeId: stage.roomTypeVO?.id || ''
        }
      });
    },
    handleCancelBooking: async (booking: BookingVO) => {
      try {
        // 添加确认弹窗
        await ElMessageBox.confirm(
          `取消预订 " ${booking.customerName || ''}（${booking.customerPhone || ''}）"的 ${booking.roomName || ''}包厢吗？`,
          '取消包厢预订？',
          {
            confirmButtonText: '确定取消',
            confirmButtonClass: 'el-button--danger',
            showCancelButton: false
          }
        );

        // 用户确认后执行取消操作
        await this.interactor.cancelBooking(booking.id || '');
        ElMessage.success('预订已成功取消');
        // 刷新列表
        this.actions.refresh();
      } catch (error) {
        // 如果是用户取消弹窗，不显示错误
        if (error !== 'cancel' && (error as any)?.message !== 'cancel') {
          console.error('取消预订失败:', error);
          ElMessage.error('取消预订失败');
        }
      }
    },
    loadData: async () => {
      this.state.loading = true;
      try {
        // 创建一个不包含status字段的请求参数对象
        const { status, ...apiParams } = this.state.searchForm;

        // 传递不含status的参数给API
        const result = await this.interactor.getBookingList(apiParams);
        // 保存原始数据用于本地搜索
        this.originalData = result.data;

        // 应用本地搜索
        this.applyLocalSearch();
      } catch (error) {
        console.error('获取预订列表失败:', error);
      } finally {
        this.state.loading = false;
      }
    },
    refresh: () => {
      this.actions.loadData();
    },

    // 搜索相关动作
    handleSearchInput: () => {
      // 应用本地搜索而不是重新请求数据
      setTimeout(() => {
        this.applyLocalSearch();
      }, 300);
    },
    handleDateChange: (val: string[] | null) => {
      if (!val || val.length !== 2) {
        // 如果清空了时间范围，设置为今天到未来7天
        this.resetToDefaultDateRange();
        return;
      }

      const [startDateStr, endDateStr] = val;

      let startDate = new Date(startDateStr);
      let endDate = new Date(endDateStr);

      // 应用30天限制
      [startDate, endDate] = this.enforceDateRangeLimit(startDate, endDate);

      // 更新日期字符串
      const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // 如果日期被修改，更新UI中的日期选择器
      const newStartDateStr = formatDate(startDate);
      const newEndDateStr = formatDate(endDate);

      if (newStartDateStr !== startDateStr || newEndDateStr !== endDateStr) {
        this.state.dateRange = [newStartDateStr, newEndDateStr];
      }

      // 更新时间戳
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);

      this.state.searchForm.startDate = newStartDateStr;
      this.state.searchForm.endDate = newEndDateStr;
      this.state.searchForm.arrivalTimeStart = Math.floor(startDate.getTime() / 1000);
      this.state.searchForm.arrivalTimeEnd = Math.floor(endDate.getTime() / 1000);

      // 触发新的API请求获取数据，而不仅是本地过滤
      this.actions.loadData();
    },
    handleSearch: () => {
      this.actions.loadData();
    },
    handleStatusChange: (status: number | null) => {
      if (status === null || status === -1) {
        this.state.searchForm.status = undefined;
      } else {
        this.state.searchForm.status = status;
      }
      // 应用本地搜索
      this.applyLocalSearch();
    },
    resetFilters: () => {
      // 重置搜索条件
      this.state.searchForm = {
        customerPhone: '',
        customerName: '',
        roomId: '',
        customerSource: '',
        status: 0,
        arrivalTimeStart: this.getStartOfDay(),
        arrivalTimeEnd: this.getEndOfDay(7)
      };

      this.resetToDefaultDateRange();
      this.actions.loadData();
    },
    handleAddBooking: async () => {
      try {
        // 使用对话框代替路由跳转
        const result = await DialogManager.open('PreTableDialog', {
          mode: 'add'
        });

        if (result?.success) {
          // 立即刷新列表数据
          await this.actions.loadData();
        }
      } catch (error) {
        ElMessage.error('打开新增预订对话框失败');
      }
    }
  };

  // 重置为默认日期范围（今天到未来7天）
  private resetToDefaultDateRange(): void {
    const today = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);

    const formatDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const todayStr = formatDate(today);
    const nextWeekStr = formatDate(nextWeek);

    this.state.dateRange = [todayStr, nextWeekStr];
    this.state.searchForm.startDate = todayStr;
    this.state.searchForm.endDate = nextWeekStr;
    this.state.searchForm.arrivalTimeStart = this.getStartOfDay();
    this.state.searchForm.arrivalTimeEnd = this.getEndOfDay(7);

    // 触发新的API请求获取数据，而不仅是本地过滤
    this.actions.loadData();
  }

  /**
   * 初始化方法
   */
  public initialize(): void {
    // 初始化房间列表
    this.state.roomList = this.roomStore.rooms;

    // 设置默认日期范围
    this.resetToDefaultDateRange();

    // 加载数据
    this.actions.loadData();

    // 监听搜索表单变化
    watch(
      () => [this.state.searchForm.customerPhone, this.state.searchForm.customerName, this.state.searchForm.roomId, this.state.searchForm.customerSource],
      () => {
        // 当搜索条件改变时应用本地搜索
        this.applyLocalSearch();
      },
      { deep: true }
    );
  }
}

/**
 * 组合式函数，提供预订管理ViewModel
 */
export function useBooking(): IBookingViewModel & { initialize: () => void } {
  const presenter = new BookingPresenter();
  return presenter;
}

// UI搜索动作接口
export interface IBookingSearchActions {
  handleSearchInput(): void;
  handleDateChange(val: string[] | null): void;
  handleSearch(): void;
  resetFilters(): void;
  handleAddBooking(): void;
  handleStatusChange(status: number | null): void; // 添加状态变化处理方法
}
