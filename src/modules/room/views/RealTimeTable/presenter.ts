import { reactive, computed, ref, onMounted, onActivated, onBeforeUnmount, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { toast } from '@/components/customer/toast';
import DialogManager from '@/utils/dialog';
import { useUserStore } from '@/stores/userStore';

import {
  IRealTimeTableViewModel,
  IRealTimeTableState,
  IRealTimeTableComputed,
  IRealTimeTableActions,
  IRealTimeTableHelpers,
  IRealTimeTableFormatters
} from './viewmodel';
import { RealTimeTableConverter } from './converter';
import { useRealTimeTableInteractor } from './interactor';
import { RoomStageVO, RoomVO } from '@/types/projectobj';
import { useStageStore } from '@/stores/stageStore';
import { useTimeStore } from '@/stores/timeStore';
import { TimeoutType } from '@/application/vodService';
import type { CancelTableForm } from '@/modules/order/types';
import { ExtendedStageVO } from '../../types/extendedStageVO';
import { SESSION_PAY_STATUS, SESSION_TAGS, getCombinedStatusName, ROOM_STATUS, getStatusPriority } from '@/modules/room/constants/stageStatus';
import type { SessionOperationVOVoOrderVO } from '@/api/autoGenerated/shared/types/order';
import { formatUnixTimestampToSimple, now10 } from '@/utils/dateUtils';

export class RealTimeTablePresenter implements IRealTimeTableViewModel {
  private router = useRouter();
  private interactor = useRealTimeTableInteractor();
  private stageStore = useStageStore();
  private timeStore = useTimeStore();
  private userStore = useUserStore();
  private roomGridRef: any = null; // 添加 roomGridRef 属性
  // 用于 autoCloseTimeoutRoom 批量执行后的统一刷新
  private refreshAfterAutoCloseTimer: number | null = null;

  // 排序选项
  public sortOptions = [
    { label: '默认排序', value: 'default' },
    { label: '名称排序', value: 'name' },
    { label: '状态排序', value: 'status' },
    { label: '消费排序', value: 'consumption' },
    { label: '开台时间', value: 'openTime' },
    { label: '关台时间', value: 'closeTime' }
  ];

  // 状态
  public state: IRealTimeTableState = reactive({
    loading: false,
    stages: [],
    currentStageId: null,
    searchText: '',
    sortType: 'default',
    filters: [],
    displayMode: 'grid',
    showCloseRoomDialog: false,
    showFaultDialog: false,
    roomToFault: null,
    showCancelTableDialog: false,
    showGiftDurationDialog: false,
    showRoomOptionDialog: false,
    showPrintDialog: false,
    showFinishTimingDialog: false,
    showReopenRoomDialog: false,
    showCancelAttachDialog: false,
    currentOptionMode: '',
    refreshTimers: [], // 确保初始化为空数组
    // 新增状态
    tempFilters: [], // 临时筛选条件
    filterPopoverVisible: false, // 筛选弹窗可见状态
    selectedButton: 'grid', // 当前选中的视图模式按钮
    showGiftProductConfirmDialog: false, // 赠送商品确认对话框
    faultReason: '', // 初始化故障原因为空字符串
    processedTimeoutRoomIds: [] // 记录已尝试自动关台的房间
  });

  // 计算属性
  public computed: IRealTimeTableComputed = {
    stages: computed(() => {
      const stages = this.stageStore.getStages();
      // 返回已处理好的ExtendedStageVO类型数据
      return stages as ExtendedStageVO[];
    }),
    isInUse: computed(() => {
      return this.computed.currentStage.value?.roomStatus === ROOM_STATUS.IN_USE;
    }),
    currentStage: computed(() => {
      const stage = this.stageStore.getCurrentStage;
      // console.log('currentStage:', stage);
      if (!stage) return null;
      // 返回已处理好的ExtendedStageVO类型数据
      return stage as ExtendedStageVO;
    }),
    currentStageConsumptionMode: computed(() => {
      // @ts-ignore
      const consumptionMode = this.computed.currentStage.value?.orderPricePlanVOs?.[0]?.consumptionMode || '--';
      if (consumptionMode === 'timeCharge') {
        return '买钟';
      } else if (consumptionMode === 'buyout') {
        return '买断';
      } else if (consumptionMode === 'sales') {
        return '核销';
      }
      return consumptionMode;
    }),
    // 新增时段计算属性
    currentStageTimeRange: computed(() => {
      const stage = this.computed.currentStage.value;
      if (!stage?.sessionVO) return { start: '--', end: '--' };

      const startTime = stage.sessionVO.startTime ? new Date(stage.sessionVO.startTime * 1000) : null;
      const endTime = stage.sessionVO.endTime ? new Date(stage.sessionVO.endTime * 1000) : null;

      const formatTime = (date: Date | null) => {
        if (!date) return '--';
        return date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0');
      };

      return {
        start: formatTime(startTime),
        end: formatTime(endTime)
      };
    }),
    currentStageDuration: computed(() => {
      const duration = this.computed.currentStage.value?.sessionVO?.duration || 0;
      if (duration >= 0) {
        return duration / 60 + '';
      }
      return '--';
    }),
    // 新增已使用时长计算属性
    currentStageUsedDuration: computed(() => {
      const stage = this.computed.currentStage.value;
      if (!stage?.sessionVO?.startTime) return '--';

      const startTime = stage.sessionVO.startTime;
      const now = now10();

      // 计算已使用时长（分钟）
      const usedDurationMinutes = Math.max(0, Math.floor((now - startTime) / 60));

      return `${usedDurationMinutes}分钟`;
    }),
    // 新增剩余时长计算属性
    currentStageRemainingDuration: computed(() => {
      const stage = this.computed.currentStage.value;
      if (!stage?.sessionVO) return '--';

      // 如果是计时方案，显示为"计时"
      if (stage.tags && stage.tags.includes(SESSION_TAGS.ISTIMECONSUME)) {
        return '计时';
      }

      if (!stage.sessionVO.endTime) return '--';

      const endTime = stage.sessionVO.endTime;
      const now = now10();

      // 计算剩余时长（分钟）
      const remainingDurationMinutes = Math.max(0, Math.floor((endTime - now) / 60));

      return `${remainingDurationMinutes}分钟`;
    }),
    currentStageStatusName: computed(() => {
      return getCombinedStatusName(this.computed.currentStage.value);
    }),
    currentStageTagLabel: computed(() => {
      const tags = this.computed.currentStage.value?.tags;
      if (tags?.includes(SESSION_TAGS.TIMEOUT)) {
        return '超时';
      }
      if (tags?.includes(SESSION_TAGS.LOCKED)) {
        return '锁房';
      }
      if (tags?.includes(SESSION_TAGS.UNION)) {
        return '联房';
      }
      if (tags?.includes(SESSION_TAGS.BOOKED)) {
        return '预定';
      }
      return '';
    }),
    currentStagePayStatus: computed(() => {
      const payStatus = this.computed.currentStage.value?.payStatus;
      const tags = this.computed.currentStage.value?.tags;
      const roomStatus = this.computed.currentStage.value?.roomStatus;
      let roomStatusLabel = '';
      if (roomStatus === ROOM_STATUS.IN_USE) {
        roomStatusLabel = '使用中';
      }
      if (tags?.includes(SESSION_TAGS.TIMEOUT)) {
        roomStatusLabel = '超时';
      }
      if (tags?.includes(SESSION_TAGS.ISTIMECONSUME) || tags?.includes(SESSION_TAGS.TIMING)) {
        roomStatusLabel = '计时';
      }
      if (tags?.includes(SESSION_TAGS.LOCKED)) {
        roomStatusLabel = '锁房';
      }
      if (payStatus === SESSION_PAY_STATUS.UNPAID) {
        return roomStatusLabel ? `${roomStatusLabel}/未结` : '未结';
      } else if (payStatus === SESSION_PAY_STATUS.PAID) {
        return roomStatusLabel ? `${roomStatusLabel}/已结` : '已结';
      }
      if (roomStatus === ROOM_STATUS.WITH_GUEST) {
        return '带客中';
      }
      if (roomStatus === ROOM_STATUS.FAULT) {
        return '故障中';
      }
      if (roomStatus === ROOM_STATUS.CLEANING) {
        return '清洁中';
      }
      if (tags?.includes(SESSION_TAGS.BOOKED)) {
        return '预定中';
      }
      if (roomStatus === ROOM_STATUS.IDLE) {
        return '空闲中';
      }
      return '--';
    }),
    filteredStages: computed(() => {
      let stages = this.computed.stages.value;

      // 应用搜索过滤
      if (this.state.searchText) {
        const searchText = this.state.searchText.toLowerCase();
        stages = stages.filter(stage => stage.roomVO?.name?.toLowerCase().includes(searchText));
      }

      // 应用筛选条件
      console.log('this.state.filters:', this.state.filters);
      if (this.state.filters.length > 0) {
        stages = stages.filter(stage => {
          return this.state.filters.every(filter => {
            if (filter.startsWith('area_')) {
              const filterArea = filter.split('_')[1].toLowerCase();

              // 如果过滤条件是"未设置区域"
              if (filterArea === '未设置区域') {
                // 包厢没有设置区域时匹配
                return !stage.areaVO || !stage.areaVO.name;
              } else {
                // 区域名称比较时不区分大小写
                const areaName = stage.areaVO?.name?.toLowerCase();
                return areaName === filterArea;
              }
            } else if (filter.startsWith('status_')) {
              // 确保正确处理状态筛选条件，如 status_in_use
              const filterStatus = filter.substring(7); // 移除 'status_' 前缀
              return stage.roomStatus === filterStatus;
            } else if (filter.startsWith('pay_')) {
              const filterPayStatus = filter.substring(4); // 移除 'pay_' 前缀
              return stage.payStatus === filterPayStatus;
            }
            return true;
          });
        });
      }

      // 应用排序
      if (this.state.sortType === 'default') {
        // 默认排序：按区域名称 + 包厢名称排序
        stages.sort((a, b) => {
          // 处理区域名称，没有设置区域的放到最后
          const areaA = a.areaVO?.name || '未设置区域';
          const areaB = b.areaVO?.name || '未设置区域';

          // 先按区域排序
          const areaCompare = areaA.localeCompare(areaB);
          if (areaCompare !== 0) {
            return areaCompare;
          }

          // 区域相同时，按包厢名称排序
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB, undefined, { numeric: true }); // numeric: true 支持数字排序，如"房间1", "房间10"
        });
      } else if (this.state.sortType === 'status') {
        // 使用getStatusPriority函数进行排序
        stages.sort((a, b) => {
          const priorityA = getStatusPriority(a);
          const priorityB = getStatusPriority(b);
          return priorityA - priorityB; // 正确的升序排列，优先级高的(数值小的)排在前面
        });
      } else if (this.state.sortType === 'consumption') {
        stages.sort((a, b) => {
          const consumptionA = a.sessionVO?.totalFee || 0;
          const consumptionB = b.sessionVO?.totalFee || 0;
          return consumptionB - consumptionA;
        });
      } else if (this.state.sortType === 'area') {
        stages.sort((a, b) => {
          const areaA = a.areaVO?.name || '';
          const areaB = b.areaVO?.name || '';
          return areaA.localeCompare(areaB);
        });
      } else if (this.state.sortType === 'name') {
        stages.sort((a, b) => {
          const nameA = a.roomVO?.name || '';
          const nameB = b.roomVO?.name || '';
          return nameA.localeCompare(nameB);
        });
      } else if (this.state.sortType === 'openTime') {
        stages.sort((a, b) => {
          const timeA = a.sessionVO?.startTime || 0;
          const timeB = b.sessionVO?.startTime || 0;
          return timeB - timeA;
        });
      } else if (this.state.sortType === 'closeTime') {
        stages.sort((a, b) => {
          const timeA = a.sessionVO?.endTime || 0;
          const timeB = b.sessionVO?.endTime || 0;
          return timeB - timeA;
        });
      }

      return stages;
    }),
    calculateMinConsumeDiffInFen: computed(() => {
      if (!this.computed.currentStage.value?.sessionVO) return 0;

      const minConsume = this.computed.currentStage.value.sessionVO.minConsume || 0;
      const supermarketFee = this.computed.currentStage.value.sessionVO.supermarketFee || 0;

      if (minConsume === 0 || supermarketFee >= minConsume) {
        return 0;
      }

      return minConsume - supermarketFee;
    }),
    // 新增: 当前排序标签
    currentSortLabel: computed(() => {
      return this.sortOptions.find(option => option.value === this.state.sortType)?.label || '排序方式';
    }),
    // 新增: 筛选选项组
    filterGroups: computed(() => {
      const stages = this.computed.stages.value;

      // 收集所有唯一的区域、类型、状态和支付状态
      const areas = new Set<string>();
      const roomStatuses = new Set<string>();
      const payStatuses = new Set<string>();
      let hasNoAreaStages = false;

      stages.forEach((stage: ExtendedStageVO) => {
        // 收集区域
        if (stage.areaVO?.name) {
          areas.add(stage.areaVO.name);
        } else {
          // 记录是否有没有设置区域的包厢
          hasNoAreaStages = true;
        }

        // 收集包厢状态
        if (stage.roomStatus) {
          roomStatuses.add(stage.roomStatus);
        }

        // 收集支付状态
        if (stage.payStatus) {
          payStatuses.add(stage.payStatus);
        }
      });

      // 构建区域选项数组
      const areaOptions = Array.from(areas).map(area => ({
        label: area,
        value: `area_${area}`
      }));

      // 如果有没有设置区域的包厢，添加"未设置区域"选项
      if (hasNoAreaStages) {
        areaOptions.push({
          label: '未设置区域',
          value: 'area_未设置区域'
        });
      }

      // 将状态转换为显示名称
      const roomStatusLabels: Record<string, string> = {
        [ROOM_STATUS.IDLE]: '空闲',
        [ROOM_STATUS.IN_USE]: '使用中',
        [ROOM_STATUS.FAULT]: '故障',
        [ROOM_STATUS.CLEANING]: '清洁中',
        [ROOM_STATUS.WITH_GUEST]: '带客'
      };

      // 将支付状态转换为显示名称
      const payStatusLabels: Record<string, string> = {
        [SESSION_PAY_STATUS.UNPAID]: '未结',
        [SESSION_PAY_STATUS.PAID]: '已结'
      };

      return [
        {
          title: '区域',
          key: 'area',
          options: areaOptions
        },
        {
          title: '包厢状态',
          key: 'roomStatus',
          options: Array.from(roomStatuses).map(status => ({
            label: roomStatusLabels[status] || status,
            value: `status_${status}`
          }))
        },
        {
          title: '支付状态',
          key: 'payStatus',
          options: Array.from(payStatuses).map(status => ({
            label: payStatusLabels[status] || status,
            value: `pay_${status}`
          }))
        }
      ];
    }),
    // 新增：各状态包厢计数
    idleRoomsCount: computed(() => {
      return this.computed.stages.value.filter(stage => stage.roomStatus === ROOM_STATUS.IDLE).length;
    }),
    inUseRoomsCount: computed(() => {
      return this.computed.stages.value.filter(stage => stage.roomStatus === ROOM_STATUS.IN_USE).length;
    }),
    timeoutRoomsCount: computed(() => {
      return this.computed.stages.value.filter(stage => stage.roomStatus === ROOM_STATUS.IN_USE && stage.tags?.includes(SESSION_TAGS.TIMEOUT)).length;
    }),
    faultRoomsCount: computed(() => {
      return this.computed.stages.value.filter(stage => stage.roomStatus === ROOM_STATUS.FAULT).length;
    }),
    cleaningRoomsCount: computed(() => {
      return this.computed.stages.value.filter(stage => stage.roomStatus === ROOM_STATUS.CLEANING).length;
    }),
    // 添加预订相关的计算属性
    currentBookingCustomerName: computed(() => {
      const stage = this.computed.currentStage.value;
      if (!stage || !stage.bookingVOs || stage.bookingVOs.length === 0) return '';

      // 如果有多个预订，取第一个
      return stage.bookingVOs[0].customerName || '';
    }),

    currentBookingArrivalTime: computed(() => {
      const stage = this.computed.currentStage.value;
      if (!stage || !stage.bookingVOs || stage.bookingVOs.length === 0) return '';

      // 获取第一个预订的预抵时间
      const arrivalTime = stage.bookingVOs[0].arrivalTime;
      if (!arrivalTime) return '';

      // 格式化时间为日期时间
      return formatUnixTimestampToSimple(arrivalTime);
    }),

    hasBookingTag: computed(() => {
      const tags = this.computed.currentStage.value?.tags || [];
      return tags.includes(SESSION_TAGS.BOOKED);
    })
  };

  // 辅助方法
  public helpers: IRealTimeTableHelpers = {
    getAvailableButtons: (stage: ExtendedStageVO | null, showOperations: boolean = true) => {
      return RealTimeTableConverter.getAvailableButtons(stage || this.computed.currentStage.value, showOperations);
    },
    processStageData: (rawData: any[]) => {
      return RealTimeTableConverter.processStageData(rawData);
    }
  };

  // 格式化工具
  public formatters: IRealTimeTableFormatters = {
    formatGenderText: (gender: any) => {
      // 处理各种可能的数据类型
      if (gender === 0 || gender === '0') {
        return '(先生)';
      } else if (gender === 1 || gender === '1') {
        return '(女士)';
      }
      return ''; // 如果性别信息不明确，返回空字符串
    }
  };

  // 动作
  public actions: IRealTimeTableActions = {
    // 数据加载
    fetchStages: async () => {
      this.state.loading = true;
      try {
        const data = await this.interactor.fetchStages();

        // 直接处理为ExtendedStageVO类型
        const processedData = this.helpers.processStageData(data);
        // 设置到store中（setStages会自动恢复选中状态）
        this.stageStore.setStages(processedData as any);

        // 如果有保存的currentStageId但store中没有恢复成功，尝试手动恢复
        if (this.state.currentStageId && !this.stageStore.getCurrentStage) {
          const targetStage = processedData.find(stage => stage.roomVO.id === this.state.currentStageId);
          if (targetStage) {
            this.stageStore.setCurrentStage(targetStage);
            console.log('[RealTimeTable] 手动恢复选中状态:', targetStage.roomVO.name);
          }
        }
      } catch (error) {
        console.error('获取房间数据失败:', error);
      } finally {
        this.state.loading = false;
      }
    },

    refreshRoomData: () => {
      this.stageStore.updateStageCombinedStatus();
    },

    refreshCurrentStage: (stage: ExtendedStageVO) => {
      this.stageStore.updateStageInfo(stage);
    },

    clearAllTimers: () => {
      console.log('[RealTimeTable] 清理所有定时器', this.state.refreshTimers?.length || 0);
      if (this.state.refreshTimers && Array.isArray(this.state.refreshTimers)) {
        this.state.refreshTimers.forEach(timer => {
          console.log('[RealTimeTable] 清理定时器:', timer);
          clearTimeout(timer);
          clearInterval(timer);
        });
        this.state.refreshTimers = [];
      }
    },

    // 房间操作
    handleStageSelect: async (stage: ExtendedStageVO) => {
      if (!stage || !stage.roomVO) return;

      console.log('选中包厢:', stage);

      // 设置当前包厢ID，确保访问id前进行非空判断
      if (stage.roomVO && stage.roomVO.id) {
        this.state.currentStageId = stage.roomVO.id;
        // 保存UI状态
        this.saveUIState();
      }

      // 更新全局状态
      this.stageStore.setCurrentStage(stage);

      // 如果是故障包厢，获取故障原因
      if (stage.roomStatus === ROOM_STATUS.FAULT && stage.roomVO && stage.roomVO.id) {
        this.state.loading = true;
        try {
          const faultReason = await this.interactor.getRoomFaultReason(stage.roomVO.id);
          this.state.faultReason = faultReason;
        } catch (error) {
          console.error('获取故障原因失败:', error);
          this.state.faultReason = '获取故障原因失败';
        } finally {
          this.state.loading = false;
        }
      } else {
        // 非故障包厢，清空故障原因
        this.state.faultReason = '';
      }
    },

    handleSelectedRoomUpdate: (newRoom: RoomVO) => {
      if (this.computed.currentStage.value) {
        // 通过 store 更新当前 stage
        const updatedStage = { ...this.computed.currentStage.value, roomVO: newRoom } as any;
        this.stageStore.setCurrentStage(updatedStage);
        // 同时更新 stages 中的对应数据
        this.stageStore.updateStageInfo(updatedStage);
      }
    },

    handleButtonClick: async (action: string) => {
      if (!this.computed.currentStage.value) {
        ElMessage.warning('请先选择包厢');
        return;
      }
      this.stageStore.setSelectedStage(this.computed.currentStage.value as any);
      console.log('handleButtonClick action:', action);
      const actionHandlers: Record<string, () => Promise<void> | void> = {
        withGuest: this.actions.handleWithGuest,
        openTable: this.actions.handleOpenTable,
        unionTable: () => this.actions.handleRoomOption('attach'),
        cancelAttach: this.actions.handleCancelAttach,
        fault: this.actions.handleFault,
        cancelWithGuest: this.actions.handleCancelWithGuest,
        cancelBooking: this.actions.handleCancelBooking,
        editBooking: this.actions.handleEditBooking,
        bookTableAndOpen: this.actions.handleBookingOpenTable,
        order: this.actions.handleOrderProduct,
        renewTable: this.actions.handleExtendRoom,
        consumptionDetails: this.actions.handleViewOrderDetail,
        mergeTable: () => this.actions.handleRoomOption('merge'),
        changeTable: () => this.actions.handleRoomOption('transfer'),
        swapTable: () => this.actions.handleRoomOption('swap'),
        cancelOrder: this.actions.handleRefund,
        unlockTable: this.actions.handleUnlockRoom,
        lockTable: this.actions.handleLockRoom,
        giftProduct: this.actions.handleGiftProduct,
        giftDuration: this.actions.handleGiftDuration,
        consumptionPrint: this.actions.handlePrint,
        billRestore: this.actions.handleRestoreBill,
        pay: this.actions.handleCheckout,
        finishTiming: this.actions.handleFinishTiming,
        prepaid: this.actions.handlePrepaid,
        closeTable: this.actions.handleCloseRoom,
        removeFault: this.actions.handleUnfaultRoom,
        cleanTable: this.actions.handleCleaningComplete,
        reopenTable: this.actions.handleReopenTable,
        storeWine: this.actions.handleStoreWine,
        bookTable: this.actions.handleCreateBooking
      };

      const handler = actionHandlers[action];
      if (handler) {
        await handler();
      }
    },

    // 对话框操作
    handleCloseRoom: async () => {
      if (!this.computed.currentStage.value?.roomVO?.id || !this.computed.currentStage.value?.sessionVO?.sessionId) {
        ElMessage.warning('请先选择有效的包厢');
        return;
      }

      const sessionId = this.computed.currentStage.value.sessionVO.sessionId;
      const roomId = this.computed.currentStage.value.roomVO.id;
      const roomName = this.computed.currentStage.value.roomVO.name;
      const endTime = this.computed.currentStage.value.sessionVO.endTime || 0;

      // 使用对话框 then 方式处理回调
      DialogManager.open('CloseRoomDialog', {
        sessionId,
        roomId,
        roomName,
        endTime
      })
        .then(result => {
          console.log('[actions] handleCloseRoom result:', result);
          // 执行VOD关台等后续操作
          this.actions.handleCloseRoomSuccess(roomId);
        })
        .catch(error => {
          console.error('关房操作失败:', error);
        });
    },

    handleCloseRoomSuccess: async (roomId: string) => {
      if (!roomId) {
        return;
      }
      try {
        this.interactor.vodCloseRoom(roomId);
        this.actions.fetchStages();
        console.log('[actions] handleCloseRoomSuccess !!!', roomId);
      } catch (error) {
        console.error('[presenter] VOD关台失败:', error);
      }
    },

    handleFault: () => {
      this.state.roomToFault = this.computed.currentStage.value?.roomVO || null;
      this.state.showFaultDialog = true;
      console.log('[actions] handleFault', this.state.roomToFault);
    },

    handleFaultConfirm: async (reason: string) => {
      console.log('[actions] handleFaultConfirm', this.state.roomToFault);
      if (!this.state.roomToFault) return;

      try {
        const success = await this.interactor.faultRoom(this.state.roomToFault.id, reason);
        if (success) {
          if (this.state.roomToFault) {
            this.state.roomToFault.status = 'fault';
          }
          let currentStage = this.computed.currentStage.value;
          if (currentStage) {
            currentStage.roomVO.status = 'fault';
            currentStage.roomStatus = 'fault';
            this.actions.refreshCurrentStage(currentStage as any);
          }
        } else {
          ElMessage({
            message: '标记故障失败',
            type: 'error',
            duration: 1000
          });
        }
      } catch (error) {
        console.error('标记故障操作失败:', error);
        ElMessage({
          message: '标记故障失败，请稍后重试',
          type: 'error',
          duration: 1000
        });
      } finally {
        this.state.roomToFault = null;
      }
    },

    handleUnfaultRoom: async () => {
      if (!this.computed.currentStage.value?.roomVO?.id) return;

      try {
        const success = await this.interactor.unfaultRoom(this.computed.currentStage.value.roomVO.id);
        if (success) {
          if (this.computed.currentStage.value?.roomVO) {
            this.computed.currentStage.value.roomVO.status = 'idle';
          }
          let currentStage = this.computed.currentStage.value;
          if (currentStage) {
            currentStage.roomVO.status = 'idle';
            currentStage.roomStatus = 'idle';
            this.actions.refreshCurrentStage(currentStage as any);
          }
          ElMessage({
            message: '房间已解除故障',
            type: 'success',
            duration: 1000
          });
        } else {
          ElMessage({
            message: '解除故障失败',
            type: 'error',
            duration: 1000
          });
        }
      } catch (error) {
        console.error('解除故障操作失败:', error);
        ElMessage({
          message: '解除故障失败，请稍后重试',
          type: 'error',
          duration: 1000
        });
      }
    },

    // 预订相关
    handleCancelBooking: async () => {
      const currentStage = this.computed.currentStage.value;
      if (!currentStage || !currentStage.bookingVOs?.length) return;

      // 如果有多个预订，跳转到预订管理页面
      if (currentStage.bookingVOs.length > 1) {
        if (!currentStage.roomVO?.id) {
          ElMessage.warning('无效的包厢信息');
          return;
        }

        this.router.push({
          path: '/room/booking',
          query: { roomId: currentStage.roomVO.id, roomName: currentStage.roomVO.name }
        });
        return;
      }

      // 只有一个预订，显示确认对话框
      const booking = currentStage.bookingVOs[0];
      if (!booking?.id || !currentStage.roomVO) {
        ElMessage.warning('包厢或预订信息无效');
        return;
      }

      try {
        // 构建确认信息文本
        const customerName = booking.customerName || '';
        const customerPhone = booking.customerPhone || '';
        const roomName = currentStage.roomVO!.name || '';
        const confirmMessage = `取消预订 "${customerName}"（${customerPhone}）的 ${roomName}包厢吗？`;

        // 添加确认弹窗
        await ElMessageBox.confirm(confirmMessage, '取消包厢预订？', {
          confirmButtonText: '确定取消',
          confirmButtonClass: 'el-button--danger',
          showCancelButton: false
        });

        // 用户确认后执行取消操作
        const success = await this.interactor.cancelBooking(booking.id, '用户取消预订');

        if (success) {
          currentStage.bookingVOs = [];
          this.actions.refreshRoomData();
          ElMessage.success('预订已取消');
        } else {
          ElMessage.error('取消预订失败');
        }
      } catch (error) {
        // 如果是用户取消弹窗，不显示错误
        if (error !== 'cancel' && (error as any)?.message !== 'cancel') {
          console.error('取消预订失败:', error);
          ElMessage.error('取消预订失败，请稍后重试');
        }
      }
    },

    handleEditBooking: async () => {
      if (!this.computed.currentStage.value?.bookingVOs?.[0]) {
        ElMessage.warning('未找到预订信息');
        return;
      }

      // 使用对话框代替路由跳转
      await DialogManager.open(
        'PreTableDialog',
        {
          roomId: this.computed.currentStage.value.roomVO.id,
          mode: 'edit'
        },
        {
          saved: () => {
            ElMessage.success('预订信息已更新');
            // 刷新当前数据
            this.actions.fetchStages();
          },
          close: () => {
            console.log('编辑预订对话框已关闭');
          }
        }
      );
    },

    handleCreateBooking: async () => {
      if (!this.computed.currentStage.value?.roomVO) return;

      // 使用对话框代替路由跳转
      await DialogManager.open('PreTableDialog', {
        roomId: this.computed.currentStage.value.roomVO.id,
        mode: 'add'
      })
        .then(result => {
          console.log('预订成功，结果:', result);
          // 刷新当前数据
          this.actions.fetchStages();
        })
        .catch(error => {
          console.error('预订失败:', error);
        });
    },

    // 开台相关
    handleOpenTable: async () => {
      if (!this.computed.currentStage.value?.roomVO?.id) {
        ElMessage.warning('请先选择有效的包厢');
        return;
      }

      try {
        // 成功后跳转到开台页面
        this.stageStore.setStageSource('realtime');
        this.router.push({
          name: 'room-opentable',
          query: {
            roomId: this.computed.currentStage.value.roomVO.id,
            fetchData: 'true'
          }
        });
      } catch (error) {
        console.error('VOD 开台失败:', error);
        ElMessage.error('VOD 开台失败，请稍后重试');
      }
    },

    handleBookingOpenTable: () => {
      if (!this.computed.currentStage.value?.bookingVOs?.[0]) {
        ElMessage.warning('未找到预订信息');
        return;
      }
      this.stageStore.setStageSource('realtime');
      this.router.push({
        name: 'room-opentable',
        query: {
          roomId: this.computed.currentStage.value.roomVO.id,
          bookingId: this.computed.currentStage.value.bookingVOs[0].id
        }
      });
    },

    handleCancelTable: () => {
      this.state.showCancelTableDialog = true;
    },

    handleCancelTableConfirm: async (form: CancelTableForm) => {
      const roomId = this.computed.currentStage.value?.roomVO?.id;
      if (!roomId) return;

      await this.interactor.vodCloseRoom(roomId);

      // 如果需要调用API取消开台，可以在这里添加
      // await this.interactor.cancelTable(form);
    },

    handleCancelTableClose: () => {
      this.state.showCancelTableDialog = false;
      // 刷新房间数据
      this.actions.fetchStages();
    },

    // 带客相关
    handleWithGuest: async () => {
      const roomId = this.computed.currentStage.value?.roomVO?.id;
      if (!roomId) {
        ElMessage.warning('请先选择有效的包厢');
        return;
      }

      try {
        const success = await this.interactor.withGuest(roomId);

        if (success) {
          const updatedStage = {
            ...this.computed.currentStage.value!,
            roomVO: {
              ...this.computed.currentStage.value!.roomVO,
              status: 'with_guest'
            }
          };

          // 调用VOD开房
          this.interactor.vodOpenRoom(roomId);

          this.stageStore.updateStageInfo(updatedStage as any);
          ElMessage.success('带客成功');
        } else {
          ElMessage.error('带客失败');
        }
      } catch (error) {
        console.error('带客操作失败:', error);
        ElMessage.error('带客失败，请稍后重试');
      }
    },

    handleCancelWithGuest: async () => {
      const roomId = this.computed.currentStage.value?.roomVO?.id;
      if (!roomId) {
        ElMessage.warning('请先选择有效的包厢');
        return;
      }

      try {
        const success = await this.interactor.cancelWithGuest(roomId);

        if (success) {
          const updatedStage = {
            ...this.computed.currentStage.value,
            roomVO: {
              ...this.computed.currentStage.value?.roomVO,
              status: 'idle'
            }
          };

          // 调用VOD关房
          this.interactor.vodCloseRoom(roomId);

          this.stageStore.updateStageInfo(updatedStage as any);
          ElMessage.success('取消带客成功');
        } else {
          ElMessage.error('取消带客失败');
        }
      } catch (error) {
        console.error('取消带客操作失败:', error);
        ElMessage.error('取消带客失败，请稍后重试');
      }
    },

    // 其他操作
    handleViewOrderDetail: () => {
      if (!this.computed.currentStage.value?.sessionVO?.sessionId) return;

      this.router.push({
        name: 'order-room-detail',
        query: { orderId: this.computed.currentStage.value.sessionVO.sessionId }
      });
    },

    handleOrderProduct: () => {
      if (!this.computed.currentStage.value?.roomVO) {
        ElMessage.warning('请先选择包厢');
        return;
      }

      this.router.push({
        name: 'production-index',
        query: {
          roomId: this.computed.currentStage.value.roomVO.id,
          roomName: this.computed.currentStage.value.roomVO.name,
          sessionId: this.computed.currentStage.value.sessionVO?.sessionId,
          areaId: this.computed.currentStage.value.areaVO?.id,
          areaName: this.computed.currentStage.value.areaVO?.name,
          mode: 'add_product_ordering'
        }
      });
    },

    handleCheckout: async () => {
      if (!this.computed.currentStage.value?.sessionVO?.sessionId) return;

      // 使用对话框代替跳转
      await DialogManager.open(
        'OrderPayDialog',
        {
          sessionId: this.computed.currentStage.value.sessionVO.sessionId,
          payType: 'later' // 开台后结
        },
        {
          paySuccess: result => {
            console.log('支付成功:', result);
            // 刷新当前数据
            this.actions.fetchStages();
          },
          payCancel: () => {
            console.log('支付已取消');
            ElMessage.info('已取消结账');
          }
        }
      );
    },

    handleCleaningComplete: async () => {
      // 检查当前选中的 Stage 和必要的 ID
      console.log('handleCleaningComplete', this.computed.currentStage.value);
      const currentStage = this.computed.currentStage.value;
      if (!currentStage?.roomVO?.id) {
        ElMessage.warning('请先选择需要操作的包厢');
        return;
      }

      const roomId = currentStage.roomVO.id;

      try {
        this.state.loading = true; // 可选：添加加载状态
        const success = await this.interactor.cleanRoomFinish(roomId);

        if (success) {
          // 正确方式：调用 fetchStages 重新获取最新数据，或者调用 store action 更新状态
          await this.actions.fetchStages(); // 重新获取所有数据以确保状态最新

          // 如果只想更新单个 stage，可以调用 store 的 action (假设存在 updateStageStatus)
          // this.stageStore.updateStageStatus(roomId, ROOM_STATUS.IDLE);
          // --- 修改结束 ---
          ElMessage.success('清洁完成');
        } else {
          ElMessage.error('操作失败');
        }
      } catch (error) {
        console.error('清洁完成操作失败:', error);
        ElMessage.error('操作失败，请稍后重试');
      } finally {
        this.state.loading = false; // 可选：结束加载状态
      }
    },

    handleLockRoom: async () => {
      if (this.computed.currentStage.value?.roomVO?.id) {
        const success = await this.interactor.lockRoom(this.computed.currentStage.value.roomVO.id);
        if (success) {
          ElMessage.success('包厢已锁定');
          this.computed.currentStage?.value?.tags?.push('locked');
          this.actions.fetchStages();
        } else {
          ElMessage.error('锁定失败');
        }
      }
    },

    handleUnlockRoom: async () => {
      if (this.computed.currentStage.value?.roomVO?.id) {
        const success = await this.interactor.unlockRoom(this.computed.currentStage.value.roomVO.id);
        const currentStage = this.computed.currentStage.value;
        if (success) {
          ElMessage.success('包厢已解锁');
          // 使用 filter 方法创建新数组（不含 'locked'）
          currentStage.tags = currentStage.tags?.filter(tag => tag !== 'locked');
          this.actions.fetchStages();
        } else {
          ElMessage.error('解锁失败');
        }
      }
    },

    handleExtendRoom: () => {
      if (!this.computed.currentStage.value?.roomVO?.id) return;

      this.stageStore.setStageSource('realtime');
      this.router.push({
        name: 'room-opentable',
        query: {
          roomId: this.computed.currentStage.value.roomVO.id,
          mode: 'extend' // 添加模式标识，用于在开台页面区分是续房操作
        }
      });
    },

    handleRefund: () => {
      if (!this.computed.currentStage.value?.sessionVO?.sessionId || !this.computed.currentStage.value?.roomVO?.id) {
        ElMessage.warning('请先选择有效的订单');
        return;
      }

      // 使用DialogManager替代路由跳转
      DialogManager.open('RoomRefundDialog', {
        sessionId: this.computed.currentStage.value.sessionVO.sessionId,
        roomId: this.computed.currentStage.value.roomVO.id
      })
        .then(result => {
          // 处理退单结果
          if (result && result.success) {
            console.log('退单成功，退款金额:', (result.totalAmount / 100).toFixed(2));
            // 刷新当前数据
            this.actions.fetchStages();
          }
        })
        .catch(error => {
          console.error('退单操作取消或发生错误:', error);
        });
    },

    handleFinishTiming: async () => {
      const sessionId = this.computed.currentStage.value?.sessionVO?.sessionId;
      const roomId = this.computed.currentStage.value?.roomVO?.id;
      const roomTypeId = this.computed.currentStage.value?.roomTypeVO?.id;
      const areaId = this.computed.currentStage.value?.areaVO?.id;
      const roomName = this.computed.currentStage.value?.roomVO?.name;
      if (!sessionId || !roomId) return;

      try {
        // 使用对话框代替跳转
        DialogManager.open('FinishTimingDialog', {
          sessionId,
          roomId,
          roomTypeId,
          areaId,
          roomName
        }).then(result => {
          this.actions.fetchStages();
        });
      } catch (error) {
        console.error('结束计时过程中发生错误:', error);
      }
    },

    // 赠送时长
    handleGiftDuration: () => {
      this.state.showGiftDurationDialog = true;
    },

    handleGiftDurationSuccess: async () => {
      // 刷新房间数据
      await this.actions.fetchStages();
    },

    handleGiftDurationCancel: () => {
      this.state.showGiftDurationDialog = false;
    },

    // 赠送商品
    handleGiftProduct: () => {
      // 显示确认对话框
      this.state.showGiftProductConfirmDialog = true;
    },

    // 处理赠送商品确认
    handleGiftProductConfirm: (data: { giftBy: string }) => {
      const sessionId = this.computed.currentStage.value?.sessionVO?.sessionId;
      const roomId = this.computed.currentStage.value?.roomVO?.id;
      const roomName = this.computed.currentStage.value?.roomVO?.name;

      // 关闭确认对话框
      this.state.showGiftProductConfirmDialog = false;

      // 打开赠送商品对话框
      DialogManager.open(
        'AddProductDialog',
        {
          mode: 'gift',
          sessionId,
          roomId,
          roomName,
          operator: data.giftBy
        },
        {
          'gift-success': (result: SessionOperationVOVoOrderVO) => {
            console.log('赠送商品成功', result);

            // 调用 interactor 打印出品单和结账单
            this.interactor.printGiftOrderDocuments(result);

            // 刷新页面数据
            this.actions.fetchStages();
          }
        }
      ).catch(error => {
        console.error('赠送商品失败', error);
      });
    },

    // 取消赠送商品
    handleGiftProductCancel: () => {
      this.state.showGiftProductConfirmDialog = false;
    },

    // 房间选项
    handleRoomOption: (mode: 'swap' | 'attach' | 'transfer' | 'merge') => {
      this.state.currentOptionMode = mode;
      this.state.showRoomOptionDialog = true;
    },

    handleRoomOptionConfirm: async (data: { sourceRoomId: string; targetRoomId: string; mode: string }) => {
      const successMessages = {
        swap: '包厢互换成功',
        attach: '包厢联台成功',
        merge: '包厢并台成功',
        transfer: '包厢换房成功'
      };

      ElMessage({
        message: successMessages[data.mode as keyof typeof successMessages],
        type: 'success',
        duration: 1000
      });

      // 刷新房间据
      await this.actions.fetchStages();
    },

    handleRoomOptionCancel: () => {
      this.state.showRoomOptionDialog = false;
    },

    // 打印
    handlePrint: async () => {
      // 检查是否选择了房间
      if (!this.computed.currentStage.value?.roomVO?.id) {
        ElMessage.warning('请先选择包厢');
        return;
      }

      // 检查房间是否有活跃会话
      if (!this.computed.currentStage.value?.sessionVO?.sessionId) {
        ElMessage.warning('当前包厢没有活跃会话，无法打印消费单据');
        return;
      }

      const sessionId = this.computed.currentStage.value.sessionVO.sessionId;
      const roomId = this.computed.currentStage.value.roomVO.id;

      try {
        console.log('开始消费打印，roomId:', roomId, 'sessionId:', sessionId);
        
        // 调用interactor的消费打印方法
        await this.interactor.printConsumptionBill(sessionId, roomId);
        
        ElMessage.success('消费打印任务已发送');
      } catch (error) {
        console.error('消费打印失败:', error);
        ElMessage.error('消费打印失败，请稍后重试');
      }
    },

    handlePrintClose: () => {
      this.state.showPrintDialog = false;
    },

    // 新增方法: 筛选、排序与视图切换
    handleSearch: (value: string) => {
      this.state.searchText = value;
      // 保存UI状态
      this.saveUIState();
    },

    handleSortCommand: (command: string) => {
      this.state.sortType = command;
      // 保存UI状态
      this.saveUIState();
    },

    handleFilterCommand: (command: string) => {
      const index = this.state.tempFilters.indexOf(command);
      if (index === -1) {
        // 所有筛选条件按类型单选(area_, status_, pay_, type_)
        const prefix = command.split('_')[0] + '_';
        this.state.tempFilters = this.state.tempFilters.filter(f => !f.startsWith(prefix));
        this.state.tempFilters.push(command);
      } else {
        this.state.tempFilters.splice(index, 1);
      }
    },

    clearFilters: () => {
      this.state.tempFilters = [];
      this.state.filters = [];
      this.state.filterPopoverVisible = false;
    },

    applyFilters: () => {
      this.state.filters = [...this.state.tempFilters];
      this.state.filterPopoverVisible = false;
      // 保存UI状态
      this.saveUIState();
    },

    toggleDisplayMode: (mode: string) => {
      this.state.selectedButton = mode;
      this.state.displayMode = mode === 'grid' ? 'grid' : 'list';
      // 保存UI状态
      this.saveUIState();
    },

    handleReopenTable: () => {
      this.state.showReopenRoomDialog = true;
    },

    handleReopenRoomSuccess: async (stage: any) => {
      if (!stage) {
        return;
      }
      const roomId = stage?.roomVO?.id;
      try {
        const endTime = stage?.sessionVO?.endTime;
        this.interactor.vodOpenRoom(roomId, endTime);
        this.actions.fetchStages();
        console.log('[actions] handleCloseRoomSuccess !!!', stage);
      } catch (error) {
        console.error('[presenter] VOD关台失败:', error);
      }
    },

    // 处理取消联台
    handleCancelAttach: () => {
      if (!this.computed.currentStage.value?.roomVO?.id) {
        ElMessage.warning('请先选择包厢');
        return;
      }
      this.state.showCancelAttachDialog = true;
    },

    handleCancelAttachSuccess: async (data: { roomId: string; sessionId: string }) => {
      // 刷新房间数据
      await this.actions.fetchStages();
      // ElMessage.success('取消联台成功');
    },

    handleStoreWine: () => {
      if (!this.computed.currentStage.value?.roomVO?.id) {
        ElMessage.warning('请先选择包厢');
        return;
      }

      const roomId = this.computed.currentStage.value.roomVO.id;
      const roomName = this.computed.currentStage.value.roomVO.name;

      console.log(`为包厢 ${roomName}(${roomId}) 打开存酒对话框`);

      // 使用对话框管理器打开存酒对话框，并传入包厢ID
      DialogManager.open(
        'StoreWineDialog',
        {
          roomId
        },
        {
          confirm: () => {
            ElMessage.success(`存酒成功`);
            // 刷新页面数据
            this.actions.fetchStages();
          },
          'refresh-list': () => {
            console.log('需要刷新酒水列表...');
          }
        }
      );
    },

    // 账单还原
    handleRestoreBill: () => {
      // 检查是否选择了房间
      if (!this.computed.currentStage.value?.roomVO?.id) {
        ElMessage.warning('请先选择包厢');
        return;
      }

      // 检查房间是否有活跃会话
      if (!this.computed.currentStage.value?.sessionVO?.sessionId) {
        ElMessage.warning('当前包厢没有活跃会话');
        return;
      }

      // 通过对话框管理器打开账单详情对话框
      DialogManager.open(
        'BillRestoreDetailDialog',
        {
          sessionId: this.computed.currentStage.value.sessionVO.sessionId
        },
        {
          'restore-success': () => {
            // 账单还原成功后刷新房间数据
            ElMessage.success('账单还原成功');
            this.actions.fetchStages();
          }
        }
      );
    },

    // 预付费
    handlePrepaid: () => {
      return;
    }
  };

  // 组件属性
  private props: {
    searchText?: string;
    sortType?: string;
    filters?: string[];
    showOperations?: boolean;
  } = {};

  constructor(props?: any) {
    // 已经在类声明时初始化了state和refreshTimers数组，无需在这里重复
    if (props) {
      if (props.searchText !== undefined) {
        this.state.searchText = props.searchText;
      }
      if (props.sortType !== undefined) {
        this.state.sortType = props.sortType;
      }
      if (props.filters !== undefined) {
        this.state.filters = props.filters;
      }
      if (props.displayMode !== undefined) {
        this.state.displayMode = props.displayMode;
      }
      if (props.showOperations !== undefined) {
        this.props.showOperations = props.showOperations;
      }
    }

    this.setupLifecycles();
  }

  // 添加更新props的方法
  public setProps(props: any) {
    if (props.searchText !== undefined) {
      this.state.searchText = props.searchText;
    }
    if (props.sortType !== undefined) {
      this.state.sortType = props.sortType;
    }
    if (props.filters !== undefined) {
      this.state.filters = props.filters;
    }
    if (props.displayMode !== undefined) {
      this.state.displayMode = props.displayMode;
    }
    if (props.showOperations !== undefined) {
      this.props.showOperations = props.showOperations;
    }
  }

  // 设置RoomGrid引用
  setRoomGridRef(ref: any) {
    this.roomGridRef = ref;
  }

  // 保存UI状态到本地存储
  private saveUIState() {
    const uiState = {
      searchText: this.state.searchText,
      sortType: this.state.sortType,
      filters: this.state.filters,
      selectedButton: this.state.selectedButton,
      displayMode: this.state.displayMode,
      currentStageId: this.state.currentStageId
    };
    localStorage.setItem('realTimeTable_uiState', JSON.stringify(uiState));
  }

  // 从本地存储恢复UI状态
  private restoreUIState() {
    try {
      const saved = localStorage.getItem('realTimeTable_uiState');
      if (saved) {
        const uiState = JSON.parse(saved);
        this.state.searchText = uiState.searchText || '';
        this.state.sortType = uiState.sortType || 'default';
        this.state.filters = uiState.filters || [];
        this.state.selectedButton = uiState.selectedButton || 'grid';
        this.state.displayMode = uiState.displayMode || 'grid';
        this.state.currentStageId = uiState.currentStageId || null;
        console.log('[RealTimeTable] 恢复UI状态:', uiState);
      }
    } catch (error) {
      console.error('[RealTimeTable] 恢复UI状态失败:', error);
    }
  }

  private setupLifecycles() {
    // 组件激活时
    onActivated(() => {
      // 先恢复UI状态
      this.restoreUIState();
      // 然后获取数据
      this.actions.fetchStages();
    });

    // 组件卸载前
    onBeforeUnmount(() => {
      // 保存UI状态
      this.saveUIState();
      this.actions.clearAllTimers();
    });
  }
}

export function useRealTimeTablePresenter(props?: any): IRealTimeTableViewModel {
  const presenter = new RealTimeTablePresenter(props);
  return presenter;
}
