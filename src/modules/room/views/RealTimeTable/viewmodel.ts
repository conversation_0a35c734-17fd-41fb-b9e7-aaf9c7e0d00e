import { ComputedRef } from 'vue';
import { ExtendedStageVO } from '../../types/extendedStageVO';
import { RoomStageVO, RoomVO } from '@/types/projectobj';
import type { CancelTableForm } from '@/modules/order/types';

// 定义UI状态接口
export interface IRealTimeTableState {
  loading: boolean;
  stages: ExtendedStageVO[];
  currentStageId: string | null;
  searchText: string;
  sortType: string;
  filters: string[];
  displayMode: 'grid' | 'list';
  showCloseRoomDialog: boolean;
  showFaultDialog: boolean;
  roomToFault: RoomVO | null;
  showCancelTableDialog: boolean;
  showGiftDurationDialog: boolean;
  showRoomOptionDialog: boolean;
  showPrintDialog: boolean;
  showFinishTimingDialog: boolean;
  showReopenRoomDialog: boolean;
  showCancelAttachDialog: boolean; // 新增取消联台对话框状态
  showGiftProductConfirmDialog: boolean; // 赠送商品确认对话框
  currentOptionMode: string;
  refreshTimers: number[];
  tempFilters: string[]; // 临时筛选条件
  filterPopoverVisible: boolean; // 筛选弹窗可见状态
  selectedButton: string; // 当前选中的视图模式按钮
  faultReason: string; // 房间故障原因
}

// 定义UI计算属性接口
export interface IRealTimeTableComputed {
  stages: ComputedRef<ExtendedStageVO[]>; // 原始stages
  currentStage: ComputedRef<ExtendedStageVO | null>;
  calculateMinConsumeDiffInFen: ComputedRef<number>;
  filteredStages: ComputedRef<ExtendedStageVO[]>; // 过滤后的stages
  currentStageStatusName: ComputedRef<string>; // 当前选中房间的状态名称
  currentStageTagLabel: ComputedRef<string>; // 当前选中房间的优先级最高的标签
  currentStagePayStatus: ComputedRef<string>; // 当前选中房间的支付状态
  currentStageConsumptionMode: ComputedRef<string>; // 当前选中房间的计费方式
  currentStageTimeRange: ComputedRef<{ start: string; end: string }>; // 修改为对象类型，包含start和end
  currentStageDuration: ComputedRef<string>; // 当前选中房间的时长
  currentStageUsedDuration: ComputedRef<string>; // 已使用时长
  currentStageRemainingDuration: ComputedRef<string>; // 剩余时长
  currentSortLabel: ComputedRef<string>; // 当前排序方式显示文本
  filterGroups: ComputedRef<any[]>; // 筛选选项组
  idleRoomsCount: ComputedRef<number>; // 空闲包厢数量
  inUseRoomsCount: ComputedRef<number>; // 使用中包厢数量
  timeoutRoomsCount: ComputedRef<number>; // 超时包厢数量
  faultRoomsCount: ComputedRef<number>; // 故障包厢数量
  cleaningRoomsCount: ComputedRef<number>; // 清洁中包厢数量
  currentBookingCustomerName: ComputedRef<string>; // 当前预订客户姓名
  currentBookingArrivalTime: ComputedRef<string>; // 当前预订到达时间
  hasBookingTag: ComputedRef<boolean>; // 是否有预订标签
  isInUse: ComputedRef<boolean>; // 是否在使用中
}

// 定义UI动作接口
export interface IRealTimeTableActions {
  // 数据加载
  fetchStages(): Promise<void>;
  refreshRoomData(): void;
  refreshCurrentStage(stage: ExtendedStageVO): void;
  clearAllTimers(): void;

  // 房间操作
  handleStageSelect(stage: ExtendedStageVO): void;
  handleSelectedRoomUpdate(newRoom: RoomVO): void;
  handleButtonClick(action: string): Promise<void>;

  // 对话框操作
  handleCloseRoom(): Promise<void>;
  handleCloseRoomSuccess(stage: any): Promise<void>;
  handleFault(): void;
  handleFaultConfirm(reason: string): Promise<void>;
  handleUnfaultRoom(): Promise<void>;

  // 预订相关
  handleCancelBooking(): Promise<void>;
  handleEditBooking(): void;
  handleCreateBooking(): void;

  // 开台相关
  handleOpenTable(): Promise<void>;
  handleBookingOpenTable(): void;
  handleCancelTable(): void;
  handleCancelTableConfirm(form: CancelTableForm): Promise<void>;
  handleCancelTableClose(): void;

  // 带客相关
  handleWithGuest(): Promise<void>;
  handleCancelWithGuest(): Promise<void>;

  // 锁房相关
  handleLockRoom(): Promise<void>;
  handleUnlockRoom(): Promise<void>;

  // 其他操作
  handleViewOrderDetail(): void;
  handleOrderProduct(): void;
  handleCheckout(): Promise<void>;
  handleFinishTiming(): Promise<void>;
  handleCleaningComplete(): Promise<void>;
  handleLockRoom(): Promise<void>;
  handleUnlockRoom(): Promise<void>;
  handleExtendRoom(): void;
  handleRefund(): void;
  handleRestoreBill(): void;
  handlePrepaid(): void;
  handleStoreWine(): void;

  // 赠送时长
  handleGiftDuration(): void;
  handleGiftDurationSuccess(): Promise<void>;
  handleGiftDurationCancel(): void;

  // 赠送商品
  handleGiftProduct(): void;
  handleGiftProductConfirm(data: { giftBy: string }): void;
  handleGiftProductCancel(): void;

  // 房间选项
  handleRoomOption(mode: 'swap' | 'attach' | 'transfer' | 'merge'): void;
  handleRoomOptionConfirm(data: { sourceRoomId: string; targetRoomId: string; mode: string }): Promise<void>;
  handleRoomOptionCancel(): void;

  // 打印
  handlePrint(): void;
  handlePrintClose(): void;

  // 新增方法: 筛选、排序与视图切换
  handleSearch(value: string): void;
  handleSortCommand(command: string): void;
  handleFilterCommand(command: string): void;
  clearFilters(): void;
  applyFilters(): void;
  toggleDisplayMode(mode: string): void;

  handleReopenTable: () => void;
  handleReopenRoomSuccess: (stage: any) => Promise<void>;

  // 取消联台相关
  handleCancelAttach(): void;
  handleCancelAttachSuccess(data: { roomId: string; sessionId: string }): Promise<void>;
}

// 定义辅助方法接口
export interface IRealTimeTableHelpers {
  getAvailableButtons(stage: ExtendedStageVO | null, showOperations?: boolean): { text: string; action: string }[];
  processStageData(rawData: any[]): ExtendedStageVO[];
}

// 定义格式化方法接口
export interface IRealTimeTableFormatters {
  formatGenderText(gender: any): string;
}

// 组合接口
export interface IRealTimeTableViewModel {
  state: IRealTimeTableState;
  computed: IRealTimeTableComputed;
  actions: IRealTimeTableActions;
  helpers: IRealTimeTableHelpers;
  formatters: IRealTimeTableFormatters;
  sortOptions: { label: string; value: string }[]; // 排序选项
  setProps?: (props: any) => void;
  setRoomGridRef?: (ref: any) => void;
}
