import { RoomApi } from '../../api/room';
import { OrderApi } from '@/modules/order/api/order';
import { BookingApi } from '@/modules/order/api/booking';
import { RoomStageVO, RoomVO } from '@/types/projectobj';
import type { CancelTableForm } from '@/modules/order/types';
import { useStageStore } from '@/stores/stageStore';
import { useVodService } from '@/application/vodService';
import { printingService } from '@/application/printingService';
import type { SessionOperationVOVoOrderVO } from '@/api/autoGenerated/shared/types/order';
export class RealTimeTableInteractor {
  // 获取房间舞台数据
  async fetchStages(): Promise<RoomStageVO[]> {
    try {
      const response = await RoomApi.stageRoom({
        roomId: '',
        typeId: '',
        areaId: '',
        sessionId: ''
      });

      if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.error('获取房间数据失败: 返回的数据不是数组');
        return [];
      }
    } catch (error) {
      console.error('获取房间数据失败:', error);
      throw error;
    }
  }

  // 获取房间故障原因
  async getRoomFaultReason(roomId: string): Promise<string> {
    try {
      const response = await RoomApi.getRoomFaultReason({
        roomId
      });
      console.log('获取房间故障原因', response);
      if (response.code === 0 && response.data) {
        return response.data.faultDescription || '未知故障原因';
      } else {
        console.error('获取房间故障原因失败:', response);
        return '未知故障原因';
      }
    } catch (error) {
      console.error('获取房间故障原因失败:', error);
      return '获取故障原因失败';
    }
  }

  // 取消预订
  async cancelBooking(bookingId: string, reason: string): Promise<boolean> {
    try {
      const response = await BookingApi.cancelBooking(bookingId, reason);
      return response.code === 0;
    } catch (error) {
      console.error('取消预订失败:', error);
      throw error;
    }
  }

  // 带客
  async withGuest(roomId: string): Promise<boolean> {
    try {
      const response = await OrderApi.withGuest({ roomId });
      return response.code === 0;
    } catch (error) {
      console.error('带客操作失败:', error);
      throw error;
    }
  }

  // 取消带客
  async cancelWithGuest(roomId: string): Promise<boolean> {
    try {
      const response = await OrderApi.cancelWithGuest({ roomId });
      return response.code === 0;
    } catch (error) {
      console.error('取消带客操作失败:', error);
      throw error;
    }
  }

  // 清洁完成
  async cleanRoomFinish(roomId: string): Promise<boolean> {
    try {
      const response = await OrderApi.cleanRoomFinish({
        roomId,
        employeeId: 'current-employee-id'
      });
      return response.code === 0;
    } catch (error) {
      console.error('清洁完成操作失败:', error);
      throw error;
    }
  }

  // 标记故障
  async faultRoom(roomId: string, content: string): Promise<boolean> {
    try {
      const response = await OrderApi.faultRoom({
        roomId,
        content
      });
      return response.code === 0;
    } catch (error) {
      console.error('标记故障操作失败:', error);
      throw error;
    }
  }

  // 解除故障
  async unfaultRoom(roomId: string): Promise<boolean> {
    try {
      const response = await OrderApi.unfaultRoom({
        roomId
      });
      return response.code === 0;
    } catch (error) {
      console.error('解除故障操作失败:', error);
      throw error;
    }
  }

  // 取消开台 - 注意：这里假设OrderApi中有cancelOpenTable方法，如果实际API名称不同，需要调整
  async cancelTable(form: CancelTableForm): Promise<boolean> {
    try {
      // 根据原始代码，这里似乎没有实际调用API，只是关闭VOD
      // 如果需要调用API，请替换为正确的API方法
      // const response = await OrderApi.cancelOpenTable(form);
      // return response.code === 0;
      return true;
    } catch (error) {
      console.error('取消开台操作失败:', error);
      throw error;
    }
  }

  // VOD相关操作
  async vodOpenRoom(roomId: string, endTimeSeconds?: number): Promise<void> {
    if (!roomId) return;
    try {
      const vodService = useVodService();
      await vodService.open(roomId, endTimeSeconds);
    } catch (error) {
      console.warn('VOD开台失败:', error);
    }
  }

  async vodCloseRoom(roomId: string): Promise<void> {
    if (!roomId) return;
    try {
      const vodService = useVodService();
      await vodService.close(roomId);
    } catch (error) {
      console.warn('VOD关台失败:', error);
    }
  }
  // 更新房间截止时间
  updateRoomEndTime(roomId: string, endTimeSeconds: number): void {
    if (!roomId) return;
    try {
      const vodService = useVodService();
      vodService.updateEndTime(roomId, endTimeSeconds);
    } catch (error) {
      console.warn('更新房间截止时间失败:', error);
    }
  }

  async lockRoom(roomId: string): Promise<boolean> {
    try {
      const response = await OrderApi.lockRoom({ roomId });
      return response.code === 0;
    } catch (error) {
      console.warn('锁房操作失败:', error);
      throw error;
    }
  }

  async unlockRoom(roomId: string): Promise<boolean> {
    try {
      const response = await OrderApi.unlockRoom({ roomId });
      return response.code === 0;
    } catch (error) {
      console.error('解锁操作失败:', error);
      throw error;
    }
  }

  /**
   * 打印赠送商品的出品单和结账单
   * @param giftOrderData 赠送订单数据，类型为 SessionOperationVOVoOrderVO
   */
  async printGiftOrderDocuments(giftOrderData: SessionOperationVOVoOrderVO): Promise<void> {
    try {
      if (!giftOrderData) {
        console.warn('RealTimeTableInteractor: 赠送订单数据为空，无法打印');
        return;
      }

      console.log('RealTimeTableInteractor: 开始打印赠送商品相关单据:', {
        orderNos: giftOrderData.orderNos,
        sessionId: giftOrderData.sessionId,
        payBillsCount: giftOrderData.payBills?.length || 0,
        orderData: giftOrderData.data
      });

      // 1. 打印出品单
      const orderNosForPrint = this.extractOrderNosForPrint(giftOrderData);
      if (orderNosForPrint.length > 0) {
        console.log('RealTimeTableInteractor: 准备打印赠送商品出品单，订单号:', orderNosForPrint);
        this.printProductionOrderAsync(orderNosForPrint, giftOrderData.data?.sessionId);
      } else {
        console.log('RealTimeTableInteractor: 无有效订单号，跳过出品单打印');
      }

      // 2. 打印结账单
      const payBillId = this.extractPayBillIdForPrint(giftOrderData);
      if (payBillId) {
        console.log('RealTimeTableInteractor: 准备打印赠送商品结账单，billId:', payBillId);
        this.printCheckoutBillAsync(payBillId, giftOrderData.data?.sessionId, orderNosForPrint);
      } else {
        console.log('RealTimeTableInteractor: 无有效支付单ID，跳过结账单打印');
      }

    } catch (error) {
      console.error('RealTimeTableInteractor: 打印赠送商品单据时出错:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 从赠送订单数据中提取用于打印的订单号
   * @param giftOrderData 赠送订单数据
   * @returns 订单号数组
   */
  private extractOrderNosForPrint(giftOrderData: SessionOperationVOVoOrderVO): string[] {
    // 优先使用 orderNos 数组
    if (giftOrderData.orderNos && giftOrderData.orderNos.length > 0) {
      return giftOrderData.orderNos.filter(orderNo => orderNo && orderNo.trim() !== '');
    }

    return [];
  }

  /**
   * 从赠送订单数据中提取用于打印的支付单ID
   * @param giftOrderData 赠送订单数据
   * @returns 支付单ID，如果没有则返回 undefined
   */
  private extractPayBillIdForPrint(giftOrderData: SessionOperationVOVoOrderVO): string | undefined {
    // 从 payBills 数组中获取第一个有效的 billId
    if (giftOrderData.payBills && giftOrderData.payBills.length > 0) {
      for (const payBill of giftOrderData.payBills) {
        if (payBill.billId && payBill.billId.trim() !== '') {
          return payBill.billId;
        }
      }
    }

    // 如果 payBills 中没有找到，尝试从 data.payBillVO 中获取
    if (giftOrderData.data?.payBillVO?.billId) {
      return giftOrderData.data.payBillVO.billId;
    }

    return undefined;
  }

  /**
   * 异步打印出品单（不阻塞调用者）
   * @param orderNos 订单号数组
   * @param sessionId 会话ID
   */
  private printProductionOrderAsync(orderNos: string[], sessionId?: string): void {
    try {
      console.log(`RealTimeTableInteractor: 准备打印出品单 (异步)，订单号: ${orderNos.join(', ')}, 会话ID: ${sessionId || '无'}`);

      // 调用printingService的printProductOutBySessionId方法，不等待结果
      printingService
        .printProductOutBySessionId(sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('RealTimeTableInteractor: 出品单打印任务已发送');
          } else {
            console.error('RealTimeTableInteractor: 出品单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('RealTimeTableInteractor: 打印出品单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('RealTimeTableInteractor: 准备打印出品单时出错:', error);
    }
  }

  /**
   * 异步打印结账单（不阻塞调用者）
   * @param payBillId 结账单ID
   * @param sessionId 会话ID
   * @param orderNos 订单号数组
   */
  private printCheckoutBillAsync(payBillId: string, sessionId?: string, orderNos?: string[]): void {
    try {
      console.log('RealTimeTableInteractor: 准备打印结账单:', payBillId, sessionId, orderNos);

      // 调用打印服务，不等待结果
      printingService
        .printCheckoutBillByPayBillId(payBillId, sessionId, orderNos)
        .then((success: boolean) => {
          if (success) {
            console.log('RealTimeTableInteractor: 结账单打印任务已发送');
          } else {
            console.error('RealTimeTableInteractor: 结账单打印任务发送失败');
          }
        })
        .catch((error: Error) => {
          console.error('RealTimeTableInteractor: 打印结账单过程中发生错误:', error);
        });
    } catch (error) {
      console.error('RealTimeTableInteractor: 准备打印结账单时出错:', error);
    }
  }
}

export function useRealTimeTableInteractor() {
  return new RealTimeTableInteractor();
}
