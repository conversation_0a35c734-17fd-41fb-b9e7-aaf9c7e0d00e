<template>
  <div class="flex flex-row h-full bg-[#f5f5f5]">
    <!-- 左侧包厢展示区 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 顶部操作区域 -->
      <div class="p-[24px] border-b flex justify-between items-center">
        <!-- 左侧标签页 -->
        <div class="flex bg-white rounded-[10px] h-[68px] w-[224px] px-[8px] items-center">
          <router-link
            to="/room/realtimetable"
            class="flex-1 h-[52px] w-[100px] font-medium rounded-[6px] text-white flex items-center justify-center"
            active-class="bg-btn-focus text-white"
            exact>
            实时包厢
          </router-link>
          <router-link
            to="/room/booking"
            class="flex-1 h-[52px] w-[100px] font-medium text-gray-500 rounded-[6px] flex items-center justify-center"
            active-class="bg-btn-focus text-white border-transparent"
            exact>
            预定管理
          </router-link>
        </div>

        <!-- 中间搜索区域 -->
        <div class="flex-1 flex justify-center px-[24px]">
          <el-input v-model="vm.state.searchText" placeholder="搜索包厢" class="custom-search-input" @input="vm.actions.handleSearch">
            <template #prefix>
              <el-icon class="ml-4">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <!-- 右侧筛选区域 -->
        <div class="flex items-center gap-[12px]">
          <!-- 排序下拉菜单 -->
          <el-dropdown @command="vm.actions.handleSortCommand" trigger="click" class="customer-dropdown sort-dropdown w-[152px] h-[68px]">
            <div class="flex flex-col justify-center items-center bg-white rounded-[10px] h-full w-full cursor-pointer shadow-sm">
              <div class="text-xs text-gray-400">可选择</div>
              <div class="flex items-center text-sm">
                {{ vm.computed.currentSortLabel.value }}
                <el-icon class="ml-1">
                  <ArrowDown />
                </el-icon>
              </div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="sort in vm.sortOptions" :key="sort.value" :command="sort.value">
                  <el-icon v-if="vm.state.sortType === sort.value" class="mr-1">
                    <Check />
                  </el-icon>
                  {{ sort.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 筛选弹出框 -->
          <el-popover v-model="vm.state.filterPopoverVisible" placement="bottom-start" :width="400" trigger="click" popper-class="filter-popover">
            <template #reference>
              <div
                class="flex flex-col justify-center items-center bg-white rounded-[10px] h-[68px] w-[152px] cursor-pointer shadow-sm"
                :class="{ 'bg-primary text-[#333]': vm.state.filters.length > 0 }">
                <div class="text-xs text-gray-400" :class="{ 'text-[#333]': vm.state.filters.length > 0 }">可选择</div>
                <div class="flex items-center text-sm">
                  筛选包厢
                  <template v-if="vm.state.filters.length > 0">
                    <span class="ml-1">({{ vm.state.filters.length }})</span>
                  </template>
                  <el-icon class="ml-1">
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
            </template>

            <!-- 自定义筛选面板 -->
            <div class="filter-panel px-2">
              <!-- 区域选择 -->
              <div class="mb-4">
                <div class="text-sm text-gray-600 mb-2">区域</div>
                <div class="grid grid-cols-4 gap-2">
                  <el-button
                    v-for="option in vm.computed.filterGroups.value[0].options"
                    :key="option.value"
                    :type="vm.state.tempFilters.includes(option.value) ? 'primary' : 'default'"
                    @click="vm.actions.handleFilterCommand(option.value)"
                    class="!w-full text-center">
                    {{ option.label }}
                  </el-button>
                </div>
              </div>

              <!-- 包厢状态 -->
              <div class="mb-4">
                <div class="text-sm text-gray-600 mb-2">包厢状态</div>
                <div class="grid grid-cols-3 gap-2">
                  <el-button
                    v-for="option in vm.computed.filterGroups.value[1].options"
                    :key="option.value"
                    :type="vm.state.tempFilters.includes(option.value) ? 'primary' : 'default'"
                    @click="vm.actions.handleFilterCommand(option.value)"
                    class="!w-full text-center !h-auto min-h-[32px]">
                    {{ option.label }}
                  </el-button>
                </div>
              </div>

              <!-- 支付状态 (替换原来的消费模式) -->
              <div class="mb-4">
                <div class="text-sm text-gray-600 mb-2">支付状态</div>
                <div class="grid grid-cols-2 gap-2">
                  <el-button
                    v-for="option in vm.computed.filterGroups.value[2].options"
                    :key="option.value"
                    :type="vm.state.tempFilters.includes(option.value) ? 'primary' : 'default'"
                    @click="vm.actions.handleFilterCommand(option.value)"
                    class="!w-full text-center">
                    {{ option.label }}
                  </el-button>
                </div>
              </div>

              <!-- 底部操作按钮 -->
              <div class="flex justify-between mt-4 pt-4 border-t">
                <el-button @click="vm.actions.clearFilters" class="!w-[120px]">清空筛选</el-button>
                <el-button type="primary" @click="vm.actions.applyFilters" class="!w-[120px]">确定</el-button>
              </div>
            </div>
          </el-popover>

          <div class="flex bg-white rounded-[10px] h-[68px] w-[128px] items-center justify-center">
            <div
              class="flex items-center justify-center !w-[52px] !h-[52px] !rounded-[6px] cursor-pointer"
              :class="vm.state.selectedButton === 'grid' ? 'bg-btn-focus' : 'bg-white'"
              @click="vm.actions.toggleDisplayMode('grid')">
              <img :src="FilterIcon" alt="filter" class="w-[32px] h-[32px]" :class="vm.state.selectedButton === 'grid' ? 'brightness-0 invert' : ''" />
            </div>
            <div
              class="flex items-center justify-center !w-[52px] !h-[52px] !rounded-[6px] cursor-pointer"
              :class="vm.state.selectedButton === 'list' ? 'bg-btn-focus' : 'bg-white'"
              @click="vm.actions.toggleDisplayMode('list')">
              <ListIcon class="text-[24px]" :stroke-color="vm.state.selectedButton === 'list' ? '#FFF' : '#000'"></ListIcon>
              <!-- <img :src="MenuIcon" alt="menu" class="w-[32px] h-[32px]"
                                :class="vm.state.selectedButton === 'list' ? 'brightness-0 invert' : ''" /> -->
            </div>
          </div>
        </div>
      </div>

      <!-- RoomGrid 组件区域 -->
      <div class="flex-1 overflow-auto scrollbar-hide p-[30px]">
        <RoomGrid
          ref="roomGridRef"
          :stages="vm.computed.filteredStages.value"
          :loading="vm.state.loading"
          :display-mode="vm.state.selectedButton === 'grid' ? 'grid' : 'group'"
          :search-text="vm.state.searchText"
          :sort-type="vm.state.sortType"
          :filters="vm.state.filters"
          :selected-stage-id="vm.computed.currentStage.value?.roomVO?.id"
          @select-stage="vm.actions.handleStageSelect" />
      </div>
      <div class="h-[52px] bg-white px-[24px] flex flex-row items-center justify-between border-t border-[#E0E0E0]">
        <div class="flex flex-row items-center gap-[12px]">
          <div class="flex flex-row items-center gap-[4px]">
            <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#FFFFFF]"></span>
            <span class="text-[14px] font-[500]">空闲中 ({{ vm.computed.idleRoomsCount.value }})</span>
          </div>
          <div class="flex flex-row items-center gap-[4px]">
            <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#D5E7F2]"></span>
            <span class="text-[14px] font-[500]">使用中 ({{ vm.computed.inUseRoomsCount.value }})</span>
          </div>
          <div class="flex flex-row items-center gap-[4px]">
            <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#F8DEA7]"></span>
            <span class="text-[14px] font-[500]">超时 ({{ vm.computed.timeoutRoomsCount.value }})</span>
          </div>
          <div class="flex flex-row items-center gap-[4px]">
            <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#DFDEDD]"></span>
            <span class="text-[14px] font-[500]">故障 ({{ vm.computed.faultRoomsCount.value }})</span>
          </div>
          <div class="flex flex-row items-center gap-[4px]">
            <span class="h-[14px] w-[14px] border-[#999] border-[1px] rounded-[2px] bg-[#FFFFFF]"></span>
            <span class="text-[14px] font-[500]">清洁中 ({{ vm.computed.cleaningRoomsCount.value }})</span>
          </div>
        </div>
        <!-- 加入计时器 -->
        <div class="flex flex-row items-center gap-[8px]">
          <el-tooltip content="NATS连接状态 - 点击重连" placement="top">
            <div class="relative cursor-pointer" @click="handleNatsReconnect">
              <div
                class="w-[12px] h-[12px] rounded-full"
                :class="{
                  'bg-green-500': natsStore.isConnected,
                  'bg-red-500': !natsStore.isConnected,
                  'animate-pulse': !natsStore.isConnected
                }"></div>
            </div>
          </el-tooltip>
          <span class="text-[14px] font-[500] min-w-[140px]">{{ timeStore.formattedTime }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧信息面板 -->
    <div class="w-[544px] bg-white border-l flex flex-col">
      <!-- 包厢标题和其他内容 -->
      <div class="flex-1 overflow-y-auto scrollbar-hide">
        <!-- 包厢标题 -->
        <div class="h-[128px] px-[36px]">
          <div class="h-[60px] flex flex-row justify-between items-center mt-[32px]">
            <h2 class="room-title-text">
              {{ (vm.computed.currentStage.value && vm.computed.currentStage.value.roomVO && vm.computed.currentStage.value.roomVO.name) || '--' }}
            </h2>
            <div class="flex flex-row items-end">
              <div class="room-theme-text">
                {{ (vm.computed.currentStage.value && vm.computed.currentStage.value.roomThemeVO && vm.computed.currentStage.value.roomThemeVO.name) || '--' }}
              </div>
              <div class="mx-1">/</div>
              <div class="room-area-text">
                {{ (vm.computed.currentStage.value && vm.computed.currentStage.value.areaVO && vm.computed.currentStage.value.areaVO.name) || '--' }}
              </div>
            </div>
          </div>
          <!-- 联台信息显示 -->
          <div v-if="vm.computed.currentStage.value?.unionInfos && vm.computed.currentStage.value?.unionInfos.length > 0">
            <div class="flex items-center text-[14px] text-[#666]">
              <div class="flex flex-wrap gap-2">
                <div
                  v-for="union in vm.computed.currentStage.value.unionInfos"
                  :key="union.unionId"
                  class="union-item"
                  :class="{ 'union-item-main': union.isMainUnion }">
                  <img v-if="union.isMainUnion" :src="UnionIcon" alt="union" class="union-icon" />
                  <span>{{ union.unionName }}{{ union.isMainUnion ? ' (主)' : '' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 分类标签 -->
        <div class="flex px-[24px] justify-between">
          <div class="info-item-cord">
            <div class="label">类型</div>
            <div class="label-value room-type-info-text">{{ vm.computed.currentStage.value?.roomTypeVO?.name || '--' }}</div>
          </div>
          <div class="info-item-cord">
            <div class="label">状态</div>
            <div class="label-value" :class="{ unpaied: vm.computed.currentStage.value?.payStatus === SESSION_PAY_STATUS.UNPAID }">
              <span>{{ vm.computed.currentStagePayStatus.value }}</span>
            </div>
          </div>
          <!-- 根据是否有预订标签显示不同的信息 -->

          <template v-if="vm.computed.hasBookingTag.value && !vm.computed.isInUse.value">
            <!-- 显示预订人信息 -->
            <div class="info-item-cord">
              <div class="label">预订人</div>
              <div class="label-value">
                {{ vm.computed.currentBookingCustomerName.value || '--' }}
                <span class="text-gray-500 text-sm">{{ vm.formatters.formatGenderText(vm.computed.currentStage.value?.bookingVOs?.[0]?.gender) }}</span>
              </div>
            </div>

            <div class="info-item-cord">
              <div class="label">预抵时间</div>
              <div class="label-value !text-[18px] pt-[4px]">{{ vm.computed.currentBookingArrivalTime.value || '--' }}</div>
            </div>
          </template>
          <template v-else>
            <!-- 显示使用时长信息 -->
            <div class="info-item-cord">
              <div class="label">已使用时长</div>
              <div class="label-value">{{ vm.computed.currentStageUsedDuration.value || '--' }}</div>
            </div>

            <div class="info-item-cord">
              <div class="label">剩余时长</div>
              <div class="label-value">{{ vm.computed.currentStageRemainingDuration.value || '--' }}</div>
            </div>
          </template>
        </div>
        <div class="h-[1px] bg-[#E0E0E0] mb-[24px] mt-[12px]"></div>

        <!-- 预订备注 -->
        <div v-if="vm.computed.hasBookingTag.value && vm.computed.currentStage.value?.bookingVOs?.[0]?.remark" class="px-[24px] my-4">
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="text-[16px] text-gray-800 leading-relaxed">
              <span class="text-sm text-gray-600">预订备注：</span>{{ vm.computed.currentStage.value.bookingVOs[0].remark }}
            </div>
          </div>
        </div>
        <!-- 故障原因显示 -->
        <div
          v-if="vm.computed.currentStage.value && vm.computed.currentStage.value.roomStatus === ROOM_STATUS.FAULT && vm.state.faultReason"
          class="mx-[32px] mb-[24px] p-[16px]">
          <div class="flex items-center mb-2">
            <el-icon class="text-[#F56C6C] mr-2"><WarningFilled /></el-icon>
            <span class="text-[16px] font-medium text-[#F56C6C]">故障原因</span>
          </div>
          <div class="text-[16px] text-[#606266]">{{ vm.state.faultReason }}</div>
        </div>
        <div class="px-[32px] mb-[24px] text-[16px] text-[#000] font-[500]" v-if="vm.computed.currentStage.value?.orderRoomPlanVOs?.[0]?.pricePlanName">
          {{ vm.computed.currentStage.value?.orderRoomPlanVOs?.[0]?.pricePlanName }}
        </div>
        <!-- 包厢费用信息 -->
        <div class="flex flex-row px-[32px]" v-if="vm.computed.currentStage.value?.roomStatus === ROOM_STATUS.IN_USE">
          <div class="flex flex-col gap-1 flex-1">
            <div class="flex flex-row items-baseline justify-between">
              <div class="text-sm text-black">包厢费用：</div>
              <PriceDisplay :amount-in-fen="vm.computed.currentStage.value?.sessionVO?.roomFee" class="font-medium" />
            </div>
            <div class="flex flex-row items-baseline justify-between">
              <div class="text-sm text-black">商品消费：</div>
              <PriceDisplay :amount-in-fen="vm.computed.currentStage.value?.sessionVO?.supermarketFee" class="font-medium" />
            </div>
            <div class="flex flex-row items-baseline justify-between">
              <div class="text-sm text-black">合计消费：</div>
              <PriceDisplay :amount-in-fen="vm.computed.currentStage.value?.sessionVO?.totalFee || 0" class="font-medium" />
            </div>
          </div>
          <div class="w-[64px]"></div>
          <div class="flex flex-col gap-1 flex-1">
            <div class="flex flex-row items-baseline justify-between">
              <div class="text-sm text-black">赠送金额：</div>
              <PriceDisplay :amount-in-fen="vm.computed.currentStage.value?.sessionVO?.giftAmount || 0" class="font-medium" />
            </div>
            <div class="flex flex-row items-baseline justify-between">
              <div class="text-sm text-black">待结合计：</div>
              <PriceDisplay :amount-in-fen="vm.computed.currentStage.value?.sessionVO?.unpaidAmount || 0" class="font-medium" />
            </div>
          </div>
        </div>
        <!-- <div class="h-[1px] bg-[#E0E0E0] mb-[24px] mt-[24px]"></div> -->

        <!-- 客户信息 -->
        <div class="px-[24px]">
          <!-- <div class="info-customer-container px-[12px]">
                        <div class="text-[#000]">客户信息</div>
                        <div class="text-[#999999]">
                            {{ vm.computed.currentStage.value?.sessionVO?.customerTag || '可输入' }}
                        </div>
                    </div> -->
          <!-- 代订人 -->
          <!-- <div v-if="vm.computed.currentStage.value?.bookingVOs?.[0]?.customerName"
                        class="info-customer-container px-[12px] mb-4">
                        <div class="text-[#000]">代订人</div>
                        <div class="text-[#999999]">
                            {{ vm.computed.currentStage.value?.bookingVOs?.[0]?.customerName }}
                        </div>
                    </div> -->
        </div>
      </div>

      <!-- 操作按钮区域 - 固定在底部 -->
      <div class="mt-auto px-[32px] py-6">
        <RoomActionButtons
          :buttons="vm.computed.currentStage.value ? vm.helpers.getAvailableButtons(vm.computed.currentStage.value, props.showOperations) : []"
          @click="vm.actions.handleButtonClick" />
      </div>
    </div>

    <!-- 对话框组件 -->
    <!-- <CloseRoomDialog v-if="vm.computed.currentStage.value?.roomVO" v-model:show="vm.state.showCloseRoomDialog"
            :selected-room="vm.computed.currentStage.value.roomVO"
            @update:selected-room="vm.actions.handleSelectedRoomUpdate"
            @close-success="vm.actions.handleCloseRoomSuccess" /> -->

    <FaultReasonDialog
      v-if="vm.state.showFaultDialog && vm.state.roomToFault"
      v-model:show="vm.state.showFaultDialog"
      v-model:room="vm.state.roomToFault"
      @confirm="vm.actions.handleFaultConfirm" />

    <CancelTableDialog
      v-if="vm.state.showCancelTableDialog && vm.computed.currentStage.value?.sessionVO"
      v-model:visible="vm.state.showCancelTableDialog"
      :session-id="vm.computed.currentStage.value.sessionVO.sessionId"
      :room-id="vm.computed.currentStage.value.roomVO.id"
      @confirm="vm.actions.handleCancelTableConfirm"
      @close="vm.actions.handleCancelTableClose" />

    <GiftDurationDialog
      v-if="vm.state.showGiftDurationDialog && vm.computed.currentStage.value?.roomVO"
      v-model:visible="vm.state.showGiftDurationDialog"
      :room-info="{
        id: vm.computed.currentStage.value.roomVO.id,
        sessionId: vm.computed.currentStage.value.sessionVO?.sessionId || '',
        name: vm.computed.currentStage.value.roomVO.name
      }"
      @success="vm.actions.handleGiftDurationSuccess"
      @cancel="vm.actions.handleGiftDurationCancel" />

    <GiftProductConfirmDialog
      v-if="vm.state.showGiftProductConfirmDialog && vm.computed.currentStage.value?.roomVO"
      v-model:visible="vm.state.showGiftProductConfirmDialog"
      :room-id="vm.computed.currentStage.value.roomVO.id"
      :room-name="vm.computed.currentStage.value.roomVO.name"
      :session-id="vm.computed.currentStage.value.sessionVO?.sessionId || ''"
      @confirm="vm.actions.handleGiftProductConfirm"
      @cancel="vm.actions.handleGiftProductCancel" />

    <RoomOptionDialog
      v-if="vm.state.showRoomOptionDialog && vm.computed.currentStage.value?.roomVO"
      v-model:visible="vm.state.showRoomOptionDialog"
      :source-room-id="vm.computed.currentStage.value.roomVO.id"
      :mode="vm.state.currentOptionMode as `swap` | `attach` | `merge` | `transfer`"
      :loading="vm.state.loading"
      @confirm="vm.actions.handleRoomOptionConfirm"
      @cancel="vm.actions.handleRoomOptionCancel" />

    <OrderDetailPrinter
      v-if="vm.state.showPrintDialog && vm.computed.currentStage.value?.sessionVO"
      v-model:visible="vm.state.showPrintDialog"
      :session-id="vm.computed.currentStage.value.sessionVO.sessionId"
      :room-id="vm.computed.currentStage.value.roomVO.id"
      @close="vm.actions.handlePrintClose" />

    <ReopenRoomDialog
      v-if="vm.state.showReopenRoomDialog && vm.computed.currentStage.value?.roomVO"
      v-model:show="vm.state.showReopenRoomDialog"
      @reopen-success="vm.actions.handleReopenRoomSuccess" />

    <CancelAttachDialog
      v-if="vm.state.showCancelAttachDialog && vm.computed.currentStage.value?.roomVO"
      v-model:visible="vm.state.showCancelAttachDialog"
      :room-id="vm.computed.currentStage.value.roomVO.id"
      :loading="vm.state.loading"
      @confirm="vm.actions.handleCancelAttachSuccess"
      @cancel="() => (vm.state.showCancelAttachDialog = false)" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, PropType } from 'vue';
import { useRouter } from 'vue-router';
import RoomGrid from '../../components/RoomGrid/index.vue';
import CloseRoomDialog from '../../components/CloseRoomDialog.vue';
import FaultReasonDialog from '../../components/FaultReasonDialog.vue';
import CancelTableDialog from '@/modules/order/components/CancelTableDialog.vue';
import GiftDurationDialog from '@/modules/order/components/GiftDurationDialog.vue';
import RoomOptionDialog from '../../components/RoomOptionDialog.vue';
import OrderDetailPrinter from '@/modules/order/components/OrderDetailPrinter.vue';
import { useRealTimeTablePresenter } from './presenter';
import type { IRealTimeTableViewModel } from './viewmodel';
import { useStageStore } from '@/stores/stageStore';
import { useTimeStore } from '@/stores/timeStore';
import { useNatsStore } from '@/stores/natsStore';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import { Search, ArrowDown, Check, Timer, Refresh, WarningFilled } from '@element-plus/icons-vue';
import MenuIcon from '@/assets/icons/menu.svg';
import FilterIcon from '@/assets/icons/filter.svg';
import RoomActionButtons from '../../components/RoomActionButtons.vue';
import { ROOM_STATUS, SESSION_PAY_STATUS } from '../../constants/stageStatus';
import UnionIcon from '@/assets/icons/union.svg';
import ReopenRoomDialog from '../../components/ReopenRoomDialog.vue';
import GiftProductConfirmDialog from '@/modules/order/components/GiftProductConfirmDialog.vue';
import ListIcon from '@/assets/v3/list.vue';
import { ElMessage } from 'element-plus';
import CancelAttachDialog from '../../components/CancelAttachDialog.vue';
import NatsService from '@/services/nats-service';
import NatsListenerService from '@/application/natsListenerService';

defineOptions({
  name: 'RealTimeTable'
});

// 定义props
const props = defineProps({
  searchText: {
    type: String,
    default: ''
  },
  sortType: {
    type: String,
    default: 'name'
  },
  filters: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  showOperations: {
    type: Boolean,
    default: true
  }
});

// 路由相关
const router = useRouter();

// 使用状态存储
const stageStore = useStageStore();
const timeStore = useTimeStore();
const natsStore = useNatsStore();
const natsService = NatsService.getInstance();

// 使用Presenter
const vm: IRealTimeTableViewModel = useRealTimeTablePresenter(props);

// RoomGrid引用
const roomGridRef = ref();

// 设置RoomGrid引用
onMounted(() => {
  if (typeof vm.setRoomGridRef === 'function') {
    vm.setRoomGridRef(roomGridRef);
  }

  // 启动时间计时器，确保时间自动更新
  timeStore.startTimer();

  // 检查NATS连接状态，如果未连接则尝试重连
  setTimeout(async () => {
    if (!natsStore.isConnected) {
      console.log('RealTimeTable - NATS未连接，尝试重连...');
      try {
        const success = await NatsListenerService.triggerReconnect();
        console.log('RealTimeTable - NATS重连结果:', success ? '成功' : '失败');

        // 如果仍然失败，尝试手动重连
        if (!success) {
          const result = await natsService.manualReconnect();
          console.log('RealTimeTable - NATS手动重连结果:', result ? '成功' : '失败');
        }
      } catch (error) {
        console.error('RealTimeTable - NATS重连出错:', error);
      }
    } else {
      console.log('RealTimeTable - NATS已连接');
    }
  }, 1000); // 延迟1秒再检查，确保其他初始化已完成
});

// 手动重连NATS
const handleNatsReconnect = async () => {
  try {
    console.log('尝试重新连接NATS服务器...');

    // 先尝试通过NatsListenerService重连
    const success = await NatsListenerService.triggerReconnect();

    // 如果NatsListenerService重连失败，再尝试直接重连
    if (!success) {
      const result = await natsService.manualReconnect();
      console.log('NATS手动重连结果:', result ? '成功' : '失败');
    } else {
      console.log('NATS服务重连成功');
    }

    // 短暂延迟后检查连接状态
    setTimeout(() => {
      console.log('当前NATS连接状态:', natsStore.connectionStatus, '是否连接:', natsStore.isConnected);
    }, 500);
  } catch (error) {
    console.error('NATS重连出错:', error);
  }
};
</script>

<style scoped>
.room-card-selected {
  transform: scale(1.05);
  z-index: 10;
}

:deep(.filter-popover) {
  padding: 16px;
  max-width: 100%;
  width: 400px !important;
}

/* 自定义搜索框样式 */
:deep(.custom-search-input) {
  height: 68px;
  border-radius: 10px;
}

:deep(.custom-search-input .el-input__wrapper) {
  border-radius: 10px;
  height: 68px;
  padding-left: 20px;
  box-shadow: 0 0 0 1px #e0e0e0 inset;
}

:deep(.custom-search-input .el-input__inner) {
  height: 68px;
  font-size: 16px;
}

/* 筛选面板内的按钮样式 */
:deep(.filter-panel .el-button) {
  padding: 8px 4px;
  height: 56px !important;
  white-space: normal;
  line-height: 1.2;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  margin: 0;
}

/* 筛选面板内的按钮文字样式 */
:deep(.filter-panel .el-button span) {
  display: inline-block;
  text-align: center;
  width: 100%;
  word-break: break-word;
}

/* 确保选中的按钮文字颜色可见 */
:deep(.filter-panel .el-button--primary) {
  color: #333 !important;
  /* 深色文本确保在浅色背景上可见 */
}

/* 排序下拉框样式 */
.sort-dropdown {
  position: relative;
}

/* 添加MiSans字体 */
@font-face {
  font-family: 'MiSans_W';
  src: url('@/assets/fonts/MiSans-Medium.ttf') format('truetype');
  font-weight: 500;
}

@font-face {
  font-family: 'MiSans_W';
  src: url('@/assets/fonts/MiSans-Semibold.ttf') format('truetype');
  font-weight: 600;
}

/* 更新费用信息区域样式 */
.fee-info-container {
  width: 484px;
  height: 193px;
  margin: 0 30px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.info-item-cord {
  height: 110px;
  width: 25%;
  align-items: center;
  justify-content: top;
  display: flex;
  flex-direction: column;
}

.info-item-cord .label {
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #999;
}

.info-item-cord .label-value {
  font-family: 'MiSans_W';
  font-weight: 600;
  font-size: 22px;
  line-height: 150%;
  color: #4c4c4c;
  margin-top: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1.5em * 2); /* 确保最大高度为两行，考虑line-height: 150% */
}

.info-item-cord .label-value.unpaied {
  color: #e23939 !important;
}

/* 时段样式 */
.time-range {
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-start,
.time-end {
  font-family: 'MiSans_W';
  font-weight: 600;
  font-size: 20px;
  line-height: 100%;
  color: #4c4c4c;
}

.time-separator {
  display: inline-block;
  width: 12px;
  height: 1px;
  background-color: #cccccc;
  margin: 2px 4px;
}

.info-customer-container {
  height: 60px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 联台标签样式 */
.union-tag {
  font-size: 12px;
  height: 24px;
  line-height: 24px;
}

.union-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 30px;
  font-size: 12px;
  color: #666;
}

.union-item-main {
  background-color: #f5f5f5;
}

.union-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* 连接状态动画 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 右侧信息面板包厢名称样式 - 限制两行并显示省略号 */
.room-title-text {
  width: 320px;
  font-size: 40px;
  font-weight: 300;
  line-height: 100%;
  letter-spacing: 0%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1em * 2); /* 确保最大高度为两行 */
}

/* 右侧信息面板房间主题样式 - 限制两行并显示省略号 */
.room-theme-text {
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1.2em * 2); /* 确保最大高度为两行 */
}

/* 右侧信息面板区域名称样式 - 限制两行并显示省略号 */
.room-area-text {
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1.2em * 2); /* 确保最大高度为两行 */
}

/* 右侧信息面板类型信息样式 - 限制两行并显示省略号 */
.room-type-info-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1.5em * 2); /* 确保最大高度为两行，考虑line-height: 150% */
}
</style>
