import { RoomStageVO } from '@/types/projectobj';
import { IRealTimeTableState } from './viewmodel';
import { ExtendedStageVO } from '../../types/extendedStageVO';
// 导入统一的状态常量
import { ROOM_STATUS, SESSION_PAY_STATUS, SESSION_TAGS } from '@/modules/room/constants/stageStatus';

export class RealTimeTableConverter {
  // 创建初始状态
  static createInitialState(): IRealTimeTableState {
    return {
      loading: false,
      stages: [],
      currentStageId: null,
      searchText: '',
      showReopenRoomDialog: false,
      showCancelAttachDialog: false,
      showGiftProductConfirmDialog: false,
      sortType: 'type',
      filters: [],
      displayMode: 'grid',
      showCloseRoomDialog: false,
      showFaultDialog: false,
      roomToFault: null,
      showCancelTableDialog: false,
      showGiftDurationDialog: false,
      showRoomOptionDialog: false,
      showPrintDialog: false,
      showFinishTimingDialog: false,
      currentOptionMode: 'swap',
      refreshTimers: [],
      tempFilters: [],
      filterPopoverVisible: false,
      selectedButton: 'grid'
    };
  }

  // 处理从API获取的舞台数据
  static processStageData(rawData: any[]): ExtendedStageVO[] {
    return rawData.map(stage => {
      // 构建ExtendedStageVO对象
      const result = {
        ...stage,
        isHighlighted: false,
        isSelected: false
      } as ExtendedStageVO;
      return result;
    });
  }

  /**
   * 对按钮进行排序
   * @param buttons 按钮数组
   * @returns 排序后的按钮数组
   */
  static sortButtons(buttons: { text: string; action: string }[]): { text: string; action: string }[] {
    // 定义按钮优先级映射
    const priorityMap: { [key: string]: number } = {
      // 未结状态核心操作
      结账: 100,
      点单: 99,
      消费明细: 98,
      锁房: 97,
      解除锁定: 97,

      // 已结状态核心操作
      关房: 95,
      续房: 96,
      结束计时: 94,

      // 空闲状态核心操作
      开台: 90,
      预订开台: 91,

      // 带客状态核心操作
      取消带客: 85,

      // 预订状态核心操作
      编辑预定: 81,
      取消预订: 80,

      // 清洁状态核心操作
      清洁完成: 75,
      重开: 74,

      // 故障状态操作
      解除故障: 70,

      // 其他常用操作
      预订: 60,
      带客: 65,
      联台: 55,
      取消联台: 55,
      故障: 54,
      退单: 53,
      预付费: 52,
      账单还原: 51,
      换房: 50,
      赠送商品: 49,
      赠送时长: 48,
      并房: 47,
      取消开台: 45,
      存酒: 40,
      包厢互换: 39,
      消费打印: 38
    };

    // 按照优先级排序
    return [...buttons].sort((a, b) => {
      const priorityA = priorityMap[a.text] || 0;
      const priorityB = priorityMap[b.text] || 0;
      return priorityB - priorityA; // 降序排列，高优先级的排在前面
    });
  }

  // 获取可用按钮列表
  static getAvailableButtons(stage: ExtendedStageVO | null, showOperations: boolean = true): { text: string; action: string }[] {
    if (!showOperations || !stage) return [];

    const roomStatus = stage.roomStatus || '';
    const payStatus = stage.payStatus || '';
    const tags = stage.tags || [];
    const btnActions: { text: string; action: string }[] = [];

    // 1. 基于房间状态的通用按钮
    switch (roomStatus) {
      case ROOM_STATUS.IDLE:
        btnActions.push({ text: '开台', action: 'openTable' });
        btnActions.push({ text: '联台', action: 'unionTable' });
        btnActions.push({ text: '带客', action: 'withGuest' });
        btnActions.push({ text: '故障', action: 'fault' });
        break;

      case ROOM_STATUS.WITH_GUEST:
        btnActions.push({ text: '开台', action: 'openTable' });
        btnActions.push({ text: '取消带客', action: 'cancelWithGuest' });
        break;

      case ROOM_STATUS.IN_USE:
        // 通用按钮 - 对所有IN_USE状态都可用
        btnActions.push({ text: '消费明细', action: 'consumptionDetails' });
        btnActions.push({ text: '消费打印', action: 'consumptionPrint' });
        btnActions.push({ text: '账单还原', action: 'billRestore' });
        btnActions.push({ text: '存酒', action: 'storeWine' });

        // 联房状态特殊处理 - 新增
        if (tags.includes(SESSION_TAGS.UNION)) {
          btnActions.push({ text: '取消联台', action: 'cancelAttach' });
        }

        // 计时消费状态的特殊处理
        if (tags.includes(SESSION_TAGS.ISTIMECONSUME)) {
          btnActions.push({ text: '结束计时', action: 'finishTiming' });
        }
        // 非计时消费状态的处理
        else {
          // 判断支付状态
          if (payStatus === SESSION_PAY_STATUS.UNPAID) {
            btnActions.push({ text: '结账', action: 'pay' });
            // btnActions.push({ text: '预付费', action: 'prepaid' });
          } else if (payStatus === SESSION_PAY_STATUS.PAID) {
            btnActions.push({ text: '关房', action: 'closeTable' });
          }

          // 非超时状态下的续房操作
          if (!tags.includes(SESSION_TAGS.TIMEOUT) && !tags.includes(SESSION_TAGS.LOCKED)) {
            btnActions.push({ text: '续房', action: 'renewTable' });
          }
        }

        // 超时状态特殊处理
        if (tags.includes(SESSION_TAGS.TIMEOUT)) {
          btnActions.push({ text: '退单', action: 'cancelOrder' });
        }
        // 非超时状态处理
        else {
          // 锁定状态特殊处理
          if (tags.includes(SESSION_TAGS.LOCKED)) {
            btnActions.push({ text: '解除锁定', action: 'unlockTable' });
          } else {
            btnActions.push({ text: '锁房', action: 'lockTable' });
            btnActions.push({ text: '点单', action: 'order' });
            if (!tags.includes(SESSION_TAGS.ISTIMECONSUME)) {
              btnActions.push({ text: '并房', action: 'mergeTable' });
            }
            btnActions.push({ text: '换房', action: 'changeTable' });
            btnActions.push({ text: '包厢互换', action: 'swapTable' });
            btnActions.push({ text: '赠送商品', action: 'giftProduct' });
            if (!tags.includes(SESSION_TAGS.ISTIMECONSUME)) {
              btnActions.push({ text: '赠送时长', action: 'giftDuration' });
            }
            // 不受锁定影响的操作
            btnActions.push({ text: '退单', action: 'cancelOrder' });
          }
        }
        break;

      case ROOM_STATUS.FAULT:
        btnActions.push({ text: '解除故障', action: 'removeFault' });
        break;

      case ROOM_STATUS.CLEANING:
        btnActions.push({ text: '清洁完成', action: 'cleanTable' });
        btnActions.push({ text: '重开', action: 'reopenTable' });
        break;
    }

    // 3. 通用操作 - 对所有状态都可用
    btnActions.push({ text: '预订', action: 'bookTable' });

    // 4. 预订状态特殊操作
    if (tags.includes(SESSION_TAGS.BOOKED)) {
      btnActions.push({ text: '取消预订', action: 'cancelBooking' });
      btnActions.push({ text: '编辑预订', action: 'editBooking' });
      if (stage.roomStatus === ROOM_STATUS.IDLE) {
        btnActions.push({ text: '预订开台', action: 'bookTableAndOpen' });
      }
    }

    // 应用按钮排序逻辑
    return this.sortButtons(btnActions);
  }
}
