import { ref, reactive, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { useStageStore } from '@/stores/stageStore';
import { useProductStore } from '../../store/productStore';
import { OpenTableInteractor } from './interactor';
import { OpenTableConverter } from './converter';
import { now10 } from '@/utils/dateUtils';
import { convertToFen, convertToYuan } from '@/utils/priceUtils';
import { toast } from '@/components/customer/toast';
import DialogManager from '@/utils/dialog';
import type { IOpenTableViewModel, IOpenTableState, IOpenTableComputed, IOpenTableActions } from './viewmodel';
import type { RoomInfoEntity, ProductEntity } from '../../entity/OpenTableEntity';
import type { SessionVO } from '@/api/autoGenerated';
import { useVodService } from '@/application/vodService';
import _ from 'lodash';

import { isOptionalByPlan, isOptionalByCount } from '@/utils/productPackageUtils';
import { validateOptionalGroup, validateAllPackageProducts, applyAutoSelectionsToMarketBill, generateProcessedMarketBillData } from './billUtils';

export class OpenTablePresenter implements IOpenTableViewModel {
  private router = useRouter();
  private route = useRoute();
  private stageStore = useStageStore();
  private productStore = useProductStore();
  private originalMinimum = ref('');

  // 内部状态
  private _state = reactive<IOpenTableState>({
    isLoading: false,
    isCustomMinimumDialogOpen: false,
    activeTab: 'buyout',
    customMinimum: null,
    adjustmentReason: '',
    bookingInfo: null,
    roomBill: null,
    marketBill: null,
    addedProducts: [],
    form: {
      booker: '',
      roomManager: '',
      queueNumber: '',
      customerSource: '',
      customerTag: '',
      consumptionMode: '',
      consumptionTime: '',
      roomName: '',
      settleImmediately: true,
      duration: 0,
      minimumCharge: 0
    },
    stageInfo: null,
    billStartTime: null,
    billSessionId: '',
    billRoomId: '',
    billRoomName: '',
    billRoomTypeVO: null,
    billRoomAreaVO: null,
    buyoutPricePlanVOs: [],
    timePricePlanVOs: [],
    billHolidayVO: null,
    roomTypes: [],
    areas: [],
    baseTimePriceFee: 0,
    // 初始化验证相关状态
    isCartValid: true,
    cartValidationMessages: []
  });

  // 计算属性
  public computed: IOpenTableComputed = {
    processedMarketBill: computed(() => {
      return generateProcessedMarketBillData(this._state.marketBill as any, this._state.addedProducts);
    }),

    minimumConsumptionDifference: computed(() => {
      const marketFee =
        _.sumBy(this.computed.processedMarketBill.value.inOrderProducts, 'totalAmount') +
        _.sumBy(this.computed.processedMarketBill.value.outOrderProducts, 'totalAmount');
      const minimumCharge = convertToFen(parseFloat(this._state.customMinimum || '0'));
      return Math.max(minimumCharge - marketFee, 0);
    }),

    totalBill: computed(() => {
      const roomFee = this._state.roomBill ? this._state.roomBill.totalPrice : 0;
      const sumValid = (arr: any[]) =>
        _.sumBy(
          arr.filter(p => !p.isFree),
          'totalAmount'
        );
      const marketFee = sumValid(this.computed.processedMarketBill.value.inOrderProducts) + sumValid(this.computed.processedMarketBill.value.outOrderProducts);
      const minimumCharge = convertToFen(parseFloat(this._state.customMinimum || '0'));
      const finalMarketFee = Math.max(marketFee, minimumCharge);
      return roomFee + finalMarketFee;
    }),

    roomConsumptionType: computed(() => {
      // console.log('this._state.roomBill', this._state.roomBill)
      // console.log('this._state:', this._state)
      if (this._state.activeTab === 'buyout' && this._state.roomBill) {
        // console.log('this._state.roomBill?.details[0]?.planName', this._state.roomBill?.details[0]?.planName)
        return '买断' + ' / ' + this._state.roomBill?.details[0]?.planName;
      } else {
        // console.log('this._state.roomBill?.details[0]?.planName failed')
        return '';
      }
    }),

    // 验证单品商品是否有效
    isStandardProductsValid: computed(() => {
      // 获取所有标准商品
      const standardProducts = [...(this._state.marketBill?.standardProducts || []), ...(this._state.addedProducts || [])].filter(
        product => !product.isPackage && !product.isOptionalGroup
      );

      // 单品没有复杂验证逻辑，只需确保数量正确（这里默认为有效）
      return true;
    }),

    // 验证可选组商品是否有效
    isOptionalGroupsValid: computed(() => {
      const validationResults = [];

      // 验证付费可选组
      if (this._state.marketBill?.optionalProducts) {
        validationResults.push(validateOptionalGroup(this._state.marketBill.optionalProducts));
      }

      // 验证免费可选组
      if (this._state.marketBill?.optionalFreeProducts) {
        validationResults.push(validateOptionalGroup(this._state.marketBill.optionalFreeProducts));
      }

      // 如果没有可选组，则默认有效
      return validationResults.length === 0 ? true : validationResults.every(result => result);
    }),

    // 验证套餐商品是否有效
    isPackagesValid: computed(() => {
      const allProducts = [...(this._state.marketBill?.standardProducts || []), ...(this._state.addedProducts || [])];
      return validateAllPackageProducts(allProducts as any); // Cast to any for now, ensure ProductEntity matches Product
    }),

    // 所有验证结果的汇总
    isAllCartItemsValid: computed(() => {
      const standardProductsValid = this.computed.isStandardProductsValid.value;
      const optionalGroupsValid = this.computed.isOptionalGroupsValid.value;
      const packagesValid = this.computed.isPackagesValid.value;

      // 所有验证都通过才返回true
      const isAllValid = standardProductsValid && optionalGroupsValid && packagesValid;

      // 更新全局验证状态 - 这行将被移除
      // this._state.isCartValid = isAllValid;

      return isAllValid;
    }),

    // 新增：开台立结按钮是否可用
    isImmediatePayEnabled: computed(() => {
      // console.log('[OpenTable] 计算开台立结按钮是否可用:',this._state);

      const isTimeConsumption = this._state.roomBill?.isTimeConsume;

      if (isTimeConsumption) {
        return false;
      }

      // 计算总账单金额
      const totalBill = this.computed.totalBill.value;

      // 如果总账单为0，不允许立即结算
      if (totalBill <= 0) {
        // console.log('[OpenTable] 总账单为0，禁用开台立结按钮');
        return false;
      }

      // 计算商品费用
      const marketFee =
        _.sumBy(this.computed.processedMarketBill.value.inOrderProducts, 'totalAmount') +
        _.sumBy(this.computed.processedMarketBill.value.outOrderProducts, 'totalAmount');

      // 获取低消金额
      const minimumCharge = convertToFen(parseFloat(this._state.customMinimum || '0'));

      // 如果有低消且商品费用不满低消，不允许立即结算
      if (minimumCharge > 0 && marketFee < minimumCharge) {
        // console.log('[OpenTable] 商品费用不满足低消要求，禁用开台立结按钮');
        return false;
      }

      // 其他情况允许立即结算
      return true;
    })
  };

  // 动作实现
  public actions: IOpenTableActions = {
    goBack: () => {
      this.router.back();
    },
    createOrder: async () => {
      if (!this._state.stageInfo) {
        console.error('缺少必要的房间信息或价格方案');
        return null;
      }

      try {
        this._state.isLoading = true;

        // 使用Converter构建订单数据 - 符合VIPER-VC架构
        const params = OpenTableConverter.convertToOpenOrderRequest({
          roomVO: this._state.stageInfo.roomVO,
          form: this._state.form,
          roomBill: this._state.roomBill!,
          inOrderProducts: this.computed.processedMarketBill.value.inOrderProducts,
          outOrderProducts: this.computed.processedMarketBill.value.outOrderProducts,
          billSessionId: this._state.billSessionId,
          billStartTime: this._state.billStartTime!,
          billRoomTypeVO: this._state.billRoomTypeVO,
          billRoomAreaVO: this._state.billRoomAreaVO,
          customMinimum: this._state.customMinimum,
          isOpenEnd: this._state.form.settleImmediately,
          totalBill: this.computed.totalBill.value,
          bookingId: this._state.stageInfo?.bookingId
        });

        // 通过Interactor处理业务逻辑 - 符合VIPER-VC架构
        const result = this._state.billSessionId ? await OpenTableInteractor.continueBill(params as any) : await OpenTableInteractor.openTable(params as any);

        if (result.success && result.data && result.data.data) {
          this._state.addedProducts = [];
          this.handleOpenSuccess(result.data.data);
          return result.data;
        } else {
          this.handleOpenError(result.error);
          return null;
        }
      } catch (error) {
        this.handleOpenError(error);
        return null;
      } finally {
        this._state.isLoading = false;
      }
    },

    fetchOpenView: async () => {
      const roomId = this._state.stageInfo?.roomVO?.id;
      if (!roomId) {
        console.error('缺少房间ID，无法获取开台视图数据');
        ElMessage.error('缺少房间信息，请返回重新选择包厢');
        return;
      }

      const typeId = this._state.billRoomTypeVO?.id;
      const areaId = this._state.billRoomAreaVO?.id;
      const sessionId = this._state.billSessionId || '';

      // 调试 - 记录请求时间和参数
      // console.log(`======= 开始获取开台视图数据 [${new Date().toLocaleTimeString()}] =======`);
      // console.log('请求参数:', {
      //   roomId,
      //   typeId: typeId || '未指定',
      //   areaId: areaId || '未指定',
      //   sessionId: sessionId || '未指定'
      // });
      // console.log('当前页面状态:', {
      //   activeTab: this._state.activeTab,
      //   billStartTime: this._state.billStartTime ? new Date(this._state.billStartTime * 1000).toLocaleString() : '未设置',
      //   existingBuyoutPlans: this._state.buyoutPricePlanVOs?.length || 0,
      //   existingTimePlans: this._state.timePricePlanVOs?.length || 0
      // });

      this._state.isLoading = true;
      try {
        // console.log('正在获取开台视图数据，参数:', { roomId, typeId, areaId, sessionId })
        const data = await OpenTableInteractor.fetchOpenView({
          roomId,
          sessionId: sessionId,
          typeId,
          areaId
        });

        // console.log('获取到开台视图数据:', data)
        if (data) {
          this.resetPageData();
          if (
            data.currentTime !== undefined && // 先排除 undefined
            (!this._state.billStartTime || data.currentTime > this._state.billStartTime)
          ) {
            this._state.billStartTime = data.currentTime; // 现在类型安全
          }

          // 更新房间基本信息
          this._state.billRoomTypeVO = data.roomTypeVO;
          this._state.billRoomAreaVO = data.areaVO;

          // 更新房间名称（使用roomVO中的名称）
          if (data.roomVO && data.roomVO.name) {
            this._state.billRoomName = data.roomVO.name;
          }

          // 更新价格方案列表
          this._state.buyoutPricePlanVOs = data.buyoutPricePlanVOs || [];
          this._state.timePricePlanVOs = data.timePricePlanVOs || [];

          // 只在服务器返回数据时设置全时段价格
          if (data.baseTimePriceFee !== undefined) {
            this._state.baseTimePriceFee = data.baseTimePriceFee;
          }

          // 更新节假日信息
          if (data.holidayVO) {
            this._state.billHolidayVO = data.holidayVO;
          }

          // 处理预订信息
          if (data.bookingVOs && data.bookingVOs.length > 0) {
            this.handleBookingInfo();
          }
        }
      } catch (error) {
        console.error('获取开台视图数据失败:', error);
        ElMessage.error('获取开台视图数据失败，请检查网络连接');
      } finally {
        this._state.isLoading = false;
      }
    },

    updateBill: bill => {
      console.log('[opentable updatebill] bill before', bill);
      const convertedBill = OpenTableConverter.convertBill(bill);
      console.log('[opentable updatebill] convertedBill', convertedBill);
      if (convertedBill) {
        this._state.roomBill = convertedBill.roombill;
        this._state.marketBill = convertedBill.marketBill;
        this._state.form.consumptionMode = convertedBill.consumptionMode;
        this._state.form.minimumCharge = convertedBill.minimumCharge;
        const start = convertedBill.timeRange?.startTime;
        const end = convertedBill.timeRange?.endTime;
        // console.log('[opentable updatebill] start', start)
        // console.log('[opentable updatebill] end', end)
        // console.log('[opentable updatebill] duration', convertedBill.timeRange?.duration)
        this._state.form.consumptionTime = `${convertedBill.timeRange?.currentDate} ${start} ~ ${end}`;
        this._state.form.duration = convertedBill.timeRange?.duration;
        if (convertedBill.roombill?.minimumCharge) {
          this._state.customMinimum = convertToYuan(convertedBill.roombill.minimumCharge).toString();
        } else {
          this._state.customMinimum = null;
        }

        // 在账单数据更新后立即自动应用默认选择
        this.actions.autoSelectOptionalGroups();
      }
      // console.log('[opentable updatebill] this.state', this._state)
    },

    goToAddProducts: async () => {
      // 添加商品
      this.stageStore.setStageSource('internal');

      try {
        // 使用 DialogManager 打开商品弹窗
        const products = await DialogManager.open('AddProductDialog', {
          roomId: this._state.billRoomId,
          roomName: this._state.billRoomName,
          areaId: this._state.billRoomAreaVO?.id,
          areaName: this._state.billRoomAreaVO?.name,
          sessionId: this._state.billSessionId,
          outOrderProducts: this.computed.processedMarketBill.value.outOrderProducts
        });

        // 处理确认添加商品的结果
        if (products && Array.isArray(products) && products.length > 0) {
          this.actions.handleAddProductConfirm(products);
        }
      } catch (error) {
        // 用户取消或关闭对话框时会进入这里
        this.actions.handleAddProductCancel();
      }
    },

    // 处理添加商品确认
    handleAddProductConfirm: (products: any[]) => {
      // 处理添加商品确认
      if (products && products.length > 0) {
        // console.log('[OpenTable] 接收到商品弹窗数据, 数量:', products.length)

        // 详细记录每个商品的数据
        // products.forEach((product, index) => {
        //   // console.log(`[OpenTable] 接收商品[${index}]: ID=${product.id || ''}, 商品名=${product.productName || '无名称'}, 数量=${product.quantity || 0}`)
        // })

        // 使用OpenTableInteractor和OpenTableConverter处理商品数据
        // 更新商品列表
        this._state.addedProducts = [...products];
        // console.log('[OpenTable] 已设置 addedProducts, 长度:', this._state.addedProducts.length)

        // 关键问题：这里没有设置productStore中的addedProducts
        // 添加设置productStore.addedProducts的代码
        this.productStore.setAddedProducts(products);
        // console.log('[OpenTable] 已设置 productStore.addedProducts, 长度:', this.productStore.addedProducts.length)

        // 更新账单
        this.actions.updateAddedProducts();
      } else {
        console.warn('[OpenTable] 接收到的商品数据为空');
      }
    },

    handleAddProductCancel: () => {
      // 处理添加商品取消
      // console.log('添加商品已取消')
    },

    openCustomMinimumDialog: () => {
      this.originalMinimum.value = this._state.customMinimum || this._state.form?.minimumCharge?.toString() || '0';
      this._state.adjustmentReason = '';
      this._state.isCustomMinimumDialogOpen = true;
    },

    setReason: (reason: string) => {
      this._state.adjustmentReason = reason;
    },

    resetCustomMinimum: () => {
      this._state.customMinimum = this.originalMinimum.value;
      this._state.adjustmentReason = '';
    },

    confirmCustomMinimum: () => {
      if (this._state.form) {
        this._state.form.minimumCharge = convertToFen(parseFloat(this._state.customMinimum!) || 0);
      }
      this._state.isCustomMinimumDialogOpen = false;
    },

    handleRoomTypeAreaUpdate: async (newRoomType, newArea) => {
      this._state.billRoomTypeVO = newRoomType;
      this._state.billRoomAreaVO = newArea;
      await this.actions.fetchOpenView();
    },

    handleClose: (done: () => void) => {
      this.actions.resetCustomMinimum();
      done();
    },

    updateAddedProducts: () => {
      this.handleAddedProducts();
    },

    checkStageInfo: (roomId: string, bookingId: string) => {
      return this.checkStageInfo(roomId, bookingId);
    },

    handleAddedProducts: () => {
      this.handleAddedProducts();
    },

    handleBookingInfo: () => {
      this.handleBookingInfo();
    },

    // 添加缺失的clearDialogCache方法
    clearDialogCache: () => {
      // console.log('Clearing dialog cache')
    },

    // 处理开台立结
    handleImmediatePay: async () => {
      try {
        // console.log("开台立结流程开始")
        // 先验证购物车
        if (!this.actions.validateCart()) {
          return;
        }

        // 设置结算模式为立即结算
        this._state.form.settleImmediately = true;

        // 1. 使用Converter构建开台请求数据
        const openOrderRequest = OpenTableConverter.convertToOpenOrderRequest({
          roomVO: this._state.stageInfo!.roomVO,
          form: this._state.form,
          roomBill: this._state.roomBill!,
          inOrderProducts: this.computed.processedMarketBill.value.inOrderProducts,
          outOrderProducts: this.computed.processedMarketBill.value.outOrderProducts,
          billSessionId: this._state.billSessionId,
          billStartTime: this._state.billStartTime!,
          billRoomTypeVO: this._state.billRoomTypeVO,
          billRoomAreaVO: this._state.billRoomAreaVO,
          customMinimum: this._state.customMinimum,
          isOpenEnd: true, // 开台立结为true
          totalBill: this.computed.totalBill.value,
          bookingId: this._state.stageInfo?.bookingId
        });

        // console.log('[开台立结] openOrderRequest:', openOrderRequest);

        // 2. 轻量适配：对openOrderRequest做简单调整，使其符合OrderPayDialog的预期
        // 计算商品总金额和房间费用
        const productAmount =
          _.sumBy(this.computed.processedMarketBill.value.inOrderProducts, (product: any) => product.totalAmount || 0) +
          _.sumBy(this.computed.processedMarketBill.value.outOrderProducts, (product: any) => product.totalAmount || 0);
        const roomAmount = this._state.roomBill ? this._state.roomBill.totalPrice : 0;

        // 为支付对话框准备订单数据
        const orderData = {
          // 保留openOrderRequest的大部分字段
          ...openOrderRequest,

          // UI显示需要的字段 - 毫秒级时间戳
          startTime: openOrderRequest.startTime * 1000,
          endTime: openOrderRequest.endTime * 1000,

          // 添加支付对话框需要的字段
          roomName: this._state.billRoomName,
          roomFee: roomAmount,
          supermarketFee: productAmount,
          orderNos: [], // 开台立结时尚未生成订单号
          originalConsumptionTime: this._state.form.consumptionTime,

          // 显示信息
          roomInfo: {
            name: this._state.billRoomName,
            startTime: openOrderRequest.startTime * 1000,
            endTime: openOrderRequest.endTime * 1000,
            duration: openOrderRequest.buyMinute,
            fee: roomAmount
          }
        };

        let payType = 'immediate';
        if (orderData.sessionId) {
          payType = 'continuePay';
        }
        // console.log('[open-continue-pay 开台立结] 订单数据:', orderData, payType);

        // 3. 打开支付对话框
        await DialogManager.open(
          'OrderPayDialog',
          {
            sessionId: orderData.sessionId,
            orderData,
            payType: payType
          },
          {
            paySuccess: result => {
              // console.log('支付成功:', result);
              // 开台立结成功后调用VOD开台接口
              if (this._state.stageInfo?.roomVO.id) {
                console.log('开台立结成功 调用 VOD 开台', this._state.stageInfo.roomVO.id);
                const vodService = useVodService();
                vodService.open(this._state.stageInfo.roomVO.id);
              }
              // 支付成功后跳转到房间管理页面
              this.router.push('/room/realtimetable');
            },
            payCancel: () => {
              // console.log('支付已取消');
              ElMessage.info('已取消开台立结');
            }
          }
        );
      } catch (error) {
        console.error('开台立结失败:', error);
        ElMessage.error('开台立结失败');
      }
    },

    // 处理开台后结
    handleLaterPay: async () => {
      // 先验证购物车
      if (!this.actions.validateCart()) {
        return;
      }

      // 设置结算模式为后结账
      this._state.form.settleImmediately = false;

      // 判断是否为续台操作（通过检查是否存在 billSessionId）
      const isContinueOperation = !!this._state.billSessionId;

      // 创建订单
      const result = await this.actions.createOrder();

      if (result) {
        const operationText = isContinueOperation ? '续台' : '开台';
        ElMessage.success(`${operationText}成功`);

        // 成功后统一打印相关单据（开台单/续台单 + 出品单）
        // result是SessionOperationVOVoderpltvvErpManagentApiVoSessionVO类型
        console.log(`${operationText}后结成功，返回数据结构:`, result);

        // 正确获取sessionId和orderNos，使用类型断言和空值检查
        const sessionId = result.data?.sessionId;
        const orderNos = result.orderNos;

        console.log(`${operationText}后结成功，准备统一打印${operationText}相关单据`);
        console.log('会话ID:', sessionId);
        console.log('订单号数组:', orderNos);
        console.log('是否续台操作:', isContinueOperation);

        if (sessionId) {
          try {
            OpenTableInteractor.printOpenTableDocuments(sessionId, orderNos, isContinueOperation);
          } catch (printError) {
            console.error(`打印${operationText}相关单据失败:`, printError);
            // 打印失败不影响开台流程，只记录错误
          }
        } else {
          console.warn(`${operationText}后结成功，但未获取到sessionId，跳过打印`);
        }

        this.router.push('/room/realtimetable');
      }
    },

    // 添加的验证方法
    validateCart: () => {
      // 清空之前的验证消息
      this._state.cartValidationMessages = [];

      // 执行所有验证
      const standardProductsValid = this.actions.validateStandardProducts();
      const optionalGroupsValid = this.actions.validateOptionalGroups();
      const packagesValid = this.actions.validatePackages();

      // 综合验证结果
      const isAllValid = standardProductsValid && optionalGroupsValid && packagesValid;

      // 更新验证状态 - 副作用移到这里
      this._state.isCartValid = isAllValid;

      // 如果验证失败，显示提示
      if (!isAllValid && this._state.cartValidationMessages.length > 0) {
        // 使用Element Plus的消息提示显示第一条错误
        ElMessage.warning(this._state.cartValidationMessages[0]);
      }

      return isAllValid;
    },

    validateStandardProducts: () => {
      // 标准单品商品通常没有复杂验证逻辑，默认为有效
      return true;
    },

    validateOptionalGroups: () => {
      // 验证可选组商品
      return this.computed.isOptionalGroupsValid.value;
    },

    validatePackages: () => {
      // 验证套餐商品
      return this.computed.isPackagesValid.value;
    },

    // 添加自动选择可选组商品的方法
    autoSelectOptionalGroups: () => {
      this._state.marketBill = applyAutoSelectionsToMarketBill(this._state.marketBill as any) as any;

      // 触发市场账单更新事件 - 保留在此处，因为这是presenter层的副作用
      window.dispatchEvent(new CustomEvent('marketBillUpdated'));
    }
  };

  // 获取状态
  public get state(): IOpenTableState {
    return this._state;
  }

  // 私有方法
  private handleOpenSuccess(session: SessionVO) {
    // console.log('开台成功 返回 session结构体：', session)
    this.stageStore.updateStageSession(this._state.stageInfo!.roomVO.id, session);
    if (this._state.stageInfo?.roomVO.id) {
      console.log('开台成功 调用 VOD 开台', this._state.stageInfo.roomVO.id);
      const vodService = useVodService();
      vodService.open(this._state.stageInfo.roomVO.id);
    }
  }

  private handleOpenError(error: any) {
    console.error('开台失败:', error);
    const errorMessage = error?.message || '开台失败';
    ElMessage.error(errorMessage);
  }

  private resetPageData() {
    this._state.roomBill = null;
    this._state.marketBill = null;
    this._state.customMinimum = null;
    this._state.adjustmentReason = '';
    this._state.addedProducts = [];
    this._state.buyoutPricePlanVOs = [];
    this._state.timePricePlanVOs = [];
    this._state.form = {
      ...this._state.form,
      roomName: '',
      consumptionTime: '',
      customerTag: '',
      booker: '',
      roomManager: '',
      customerSource: '',
      queueNumber: '',
      duration: 0
    };
  }

  // 初始化方法
  public initialize() {
    // console.log('OpenTable 初始化开始')
    // console.log('URL 参数:', this.route.query)

    const roomId = this.route.query.roomId as string;
    const bookingId = this.route.query.bookingId as string;
    // 检查并初始化房间信息
    const hasStageInfo = this.checkStageInfo(roomId, bookingId);

    // 如果成功获取到房间信息，加载开台视图数据
    if (hasStageInfo) {
      // console.log('成功获取房间信息，准备加载开台视图数据')
      this.actions.fetchOpenView();
    } else {
      console.error('无法获取房间信息，初始化失败');
      ElMessage.error('无法获取房间信息，请返回选择包房');
    }
  }

  private handleBookingInfo() {
    const bookingId = this.route.query.bookingId;
    if (bookingId && this._state.stageInfo?.bookingVOs) {
      const bookingInfo = this._state.stageInfo.bookingVOs.find(booking => booking.id === bookingId);
      if (bookingInfo) {
        this._state.bookingInfo = bookingInfo;
        // 如果有预订信息，设置相关表单数据
        // 根据当前 activeTab 设置消费模式
        this._state.form.consumptionMode = this._state.activeTab;
        this._state.activeTab = this._state.activeTab;
      }
    }
  }

  private handleAddedProducts() {
    const { getStageSource } = storeToRefs(this.stageStore);
    const { addedProducts } = storeToRefs(this.productStore);
    const source = getStageSource.value;

    if (source === 'internal') {
      if (addedProducts.value.length > 0) {
        // 使用Converter进行数据转换 - 符合VIPER-VC架构模式
        const convertedProducts = _.map(addedProducts.value, product => OpenTableConverter.convertAddedProduct(product));

        this._state.addedProducts = convertedProducts;

        // 清空商品store中的数据
        this.productStore.clearAddedProducts();
      } else {
        // 如果 store 中没有数据，使用当前 state 中的数据
        if (this._state.addedProducts.length > 0) {
          // 使用Converter转换当前state中的数据
          const convertedProducts = _.map(this._state.addedProducts, product => OpenTableConverter.convertAddedProduct(product));

          this._state.addedProducts = convertedProducts;
        } else {
          this._state.addedProducts = [];
        }
      }

      // 初始化可选组的selectedQuantity
      if (this._state.marketBill) {
        // 初始化付费可选组
        if (this._state.marketBill.optionalProducts) {
          (this._state.marketBill.optionalProducts as any).selectedQuantity = 0;
        }

        // 初始化免费可选组
        if (this._state.marketBill.optionalFreeProducts) {
          (this._state.marketBill.optionalFreeProducts as any).selectedQuantity = 0;
        }
      }

      // 触发afterProductsAdded事件，让使用者知道商品已经添加完成
      window.dispatchEvent(new CustomEvent('afterProductsAdded'));

      // console.log('[OpenTable] handleAddedProducts 处理完成, 最终state.addedProducts长度:', this._state.addedProducts.length)
    } else {
      // console.log('[OpenTable] 非内部数据源，跳过处理')
    }
  }

  // 添加 checkStageInfo 方法
  private checkStageInfo(roomId: string, bookingId: string): boolean {
    const currentStage = this.stageStore.getStageByRoomId(roomId);
    // 如果存在当前stage，从ExtendedStageVO创建RoomInfoEntity
    if (currentStage) {
      // 根据RoomInfoEntity的定义创建符合类型的对象
      this._state.stageInfo = {
        roomId: currentStage.roomVO.id, // Ensure roomId is present
        roomVO: currentStage.roomVO,
        roomTypeVO: currentStage.roomTypeVO,
        areaVO: currentStage.areaVO,
        // 只有在存在的情况下才添加可选字段
        ...(currentStage.sessionVO ? { sessionVO: currentStage.sessionVO } : {}),
        bookingVOs: currentStage.bookingVOs || [],
        // 添加pricePlanVOs字段，确保类型兼容
        pricePlanVOs: (currentStage as any).orderPricePlanVOs || [],
        currentTime: Date.now(),
        unionRoomId: currentStage.unionRoomId,
        unionRoomName: currentStage.unionRoomName,
        bookingId: bookingId
      } as RoomInfoEntity;
    } else {
      this._state.stageInfo = null;
    }

    // 如果 stageStore 中没有数据，尝试从 URL 参数获取房间信息
    if (!this._state.stageInfo) {
      const roomIdFromUrl = this.route.query.roomId as string; // Use a different variable name to avoid conflict
      if (roomIdFromUrl) {
        // console.log('从 URL 参数恢复房间信息:', roomIdFromUrl);

        // 创建基本的RoomInfoEntity对象
        const stageInfo: RoomInfoEntity = {
          roomId: roomIdFromUrl, // Add roomId here, use roomIdFromUrl
          roomVO: {
            id: roomIdFromUrl,
            name: (this.route.query.roomName as string) || '',
            deviceIp: '',
            status: 'IDLE',
            areaId: '',
            venueId: '',
            typeId: '',
            state: 0,
            isDisplayed: true,
            version: 0,
            ctime: 0,
            utime: 0,
            areaVO: {
              id: '',
              name: '',
              description: '',
              capacity: 0,
              venueId: '',
              state: 0,
              isDisplayed: true,
              ctime: 0,
              utime: 0,
              version: 0
            },
            bookingVOs: [],
            closeTime: 0,
            color: '',
            openTime: 0,
            consumptionMode: '',
            currentTime: 0,
            displayItems: '',
            highConsumptionAlert: 0,
            holidayVOs: [],
            interiorPhoto: '',
            pricePlanId: '',
            pricePlanVOs: [],
            qrCode: '',
            sessionId: '',
            tag: '',
            themeId: '',
            sequencerIp: ''
          },
          roomTypeVO: {
            id: '',
            name: '',
            venueId: '',
            state: 0,
            isDisplayed: true,
            ctime: 0,
            utime: 0,
            version: 0,
            consumptionMode: '',
            distributionChannel: '',
            highConsumptionAlert: 0,
            photo: '',
            remark: '',
            timeChargeBasePlan: ''
          },
          areaVO: {
            id: '',
            name: '',
            description: '',
            capacity: 0,
            venueId: '',
            state: 0,
            isDisplayed: true,
            ctime: 0,
            utime: 0,
            version: 0
          },
          pricePlanVOs: [], // 必需字段
          currentTime: Date.now()
        };

        // 设置到store中 - 正确转换为RoomStageVO类型结构
        this.stageStore.setCurrentStage({
          roomVO: stageInfo.roomVO,
          roomTypeVO: stageInfo.roomTypeVO,
          areaVO: stageInfo.areaVO,
          roomThemeVO: {
            id: '',
            name: '',
            description: '',
            imageUrl: '',
            venueId: '',
            state: 0,
            isDisplayed: true,
            ctime: 0,
            utime: 0,
            version: 0
          },
          unionRoomId: stageInfo.unionRoomId,
          unionRoomName: stageInfo.unionRoomName,
          bookingVOs: [],
          sessionVO: {
            id: '',
            roomId: '',
            venueId: '',
            startTime: 0,
            endTime: 0,
            duration: 0,
            status: '',
            lowConsumptionAmount: 0,
            lowConsumptionDiffAmount: 0
          } as unknown as SessionVO,
          // -------- 补齐 ExtendedStageVO 必要字段 --------
          unionInfos: [], // 默认空数组
          orderRoomPlanVOs: [] // 默认空数组
        });

        // 设置 stageSource 为 realtime，确保可以加载数据
        this.stageStore.setStageSource('realtime');

        // 设置账单相关信息
        this._state.billRoomId = roomIdFromUrl;
        this._state.billRoomName = stageInfo.roomVO.name || '未知房间';
        if (this.route.query.sessionId) {
          this._state.billSessionId = this.route.query.sessionId as string;
        }

        // 设置初始时间
        this._state.billStartTime = now10();

        this._state.stageInfo = stageInfo;
        return true;
      }

      console.error('未选中任何包房');
      ElMessage.warning('未选中任何包房，请返回选择包房');
      return false;
    }

    // 初始化基本信息
    if (this._state.stageInfo.roomVO) {
      this._state.billRoomId = this._state.stageInfo.roomVO.id;
      this._state.billRoomName = this._state.stageInfo.roomVO.name || '未知房间';
    }
    if (this._state.stageInfo.roomTypeVO) {
      this._state.billRoomTypeVO = this._state.stageInfo.roomTypeVO;
    }
    if (this._state.stageInfo.areaVO) {
      this._state.billRoomAreaVO = this._state.stageInfo.areaVO;
    }
    if (this._state.stageInfo.sessionVO?.sessionId) {
      this._state.billSessionId = this._state.stageInfo.sessionVO.sessionId;
    }
    if (this._state.stageInfo.sessionVO?.endTime) {
      this._state.billStartTime = this._state.stageInfo.sessionVO.endTime;
    } else {
      this._state.billStartTime = now10();
    }

    return true;
  }
}

// 导出组合式函数
export function useOpenTable(): IOpenTableViewModel {
  const presenter = new OpenTablePresenter();
  presenter.initialize();
  return presenter;
}
