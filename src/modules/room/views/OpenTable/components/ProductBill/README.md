# ProductBill 商品账单组件

## 概述

`ProductBill` 是一个专门为 `OpenTable` 开台功能设计的商品账单组件，用于展示商品列表、处理可选组编辑和计算小计。

## 功能特性

- ✅ 商品分组展示（开台方案商品 vs 额外点单商品）
- ✅ 商品列表展示（支持套餐、可选组、普通商品）
- ✅ 可选组商品编辑功能
- ✅ 商品数量、单价、金额显示
- ✅ 分组小计和总计计算
- ✅ 支持添加商品按钮
- ✅ 支持删除单独点单商品（开台方案商品不可删除）
- ✅ 响应式设计，支持移动端

## 使用方法

### 基本用法

```vue
<template>
  <ProductBill
    :tableData="productList"
    @add-products="handleAddProducts"
    @optional-group-updated="handleOptionalGroupUpdate"
    @delete-product="handleDeleteProduct" />
</template>

<script setup lang="ts">
import ProductBill from './components/ProductBill/index.vue';
import type { TableRowItem } from './billUtils';

const productList: TableRowItem[] = [
  // 商品数据
];

const handleAddProducts = () => {
  // 处理添加商品逻辑
};

const handleOptionalGroupUpdate = (updatedGroup: any) => {
  // 处理可选组更新逻辑
};

const handleDeleteProduct = (product: TableRowItem) => {
  // 处理删除商品逻辑
  console.log('删除商品:', product);
};
</script>
```

### Props

| 属性名          | 类型             | 默认值 | 说明                 |
| --------------- | ---------------- | ------ | -------------------- |
| `tableData`     | `TableRowItem[]` | `[]`   | 商品数据列表         |
| `showAddButton` | `boolean`        | `true` | 是否显示添加商品按钮 |

### Events

| 事件名                   | 参数                    | 说明                               |
| ------------------------ | ----------------------- | ---------------------------------- |
| `add-products`           | -                       | 点击添加商品按钮时触发             |
| `optional-group-updated` | `updatedGroup: any`     | 可选组商品更新时触发               |
| `delete-product`         | `product: TableRowItem` | 删除商品时触发（仅限单独点单商品） |

### TableRowItem 数据结构

```typescript
interface TableRowItem {
  id: string;
  productName: string;
  parentId?: string;
  quantity: number;
  selectedQuantity?: number;
  unit: string;
  price: number;
  currentPrice: number;
  totalAmount: number;
  uniqueId: string;
  isDetail?: boolean;
  isOptionalGroup?: boolean;
  isPackage?: boolean;
  isFree?: boolean;
  isInitialOrder?: boolean;
  packageDetail?: unknown;
  optionType?: string;
  type?: string;
  optionCount?: number;
  optionalProducts?: OptionalProduct[];
  details?: string;
  [key: string]: unknown;
}
```

## 样式定制

组件使用了以下CSS类名，可以通过覆盖这些类名来自定义样式：

- `.table-no-padding` - 表格内边距样式
- `.detail-row-class` - 明细行样式
- `.is-package-row` - 套餐行样式
- `.is-optional-group-row` - 可选组行样式

## 依赖组件

- `PriceDisplay` - 价格显示组件
- `PackageDetailItem` - 套餐明细项组件
- `OptionalProductDialog` - 可选组编辑对话框

## 注意事项

1. 确保传入的 `tableData` 数据结构符合 `TableRowItem` 接口定义
2. 商品会根据 `isInitialOrder` 字段自动分组：
   - `isInitialOrder: true` - 开台方案商品（橙色主题）
   - `isInitialOrder: false` - 额外点单商品（蓝色主题）
3. 可选组编辑功能依赖 `OptionalProductDialog` 组件
4. 组件内部处理了表格行合并和样式设置
5. 小计计算会自动排除赠品和明细行，并提供分组小计和总计
6. 删除功能仅对单独点单商品有效（`isInitialOrder: false`），开台方案商品不可删除
7. 删除操作会显示确认对话框，确保用户意图
8. 该组件专门为 OpenTable 业务场景设计，包含了特定的业务逻辑

## 文件结构

```
src/modules/room/views/OpenTable/components/ProductBill/
├── index.vue          # 主组件文件
├── types.ts           # 类型定义
└── README.md          # 使用说明
```

## 架构说明

该组件遵循 VIPER-VC 架构原则：

- **View层**: 负责UI展示和用户交互
- **事件驱动**: 通过事件与父组件通信
- **单一职责**: 专注于商品账单的展示和基本交互
- **就近原则**: 作为业务组件放置在使用它的模块附近
