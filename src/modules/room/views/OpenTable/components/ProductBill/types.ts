// ProductBill组件的类型定义
import type { TableRowItem } from '../../billUtils';

export interface ProductBillProps {
  tableData: TableRowItem[];
  showAddButton?: boolean;
}

export interface ProductBillEmits {
  (e: 'add-products'): void;
  (e: 'optional-group-updated', data: any): void;
  (e: 'delete-product', product: TableRowItem): void;
}

// 重新导出TableRowItem类型，方便外部使用
export type { TableRowItem } from '../../billUtils';
