import { ComputedRef } from 'vue';
import type { RoomBillEntity, MarketBillEntity, FormEntity, ProductEntity, RoomInfoEntity } from '../../entity/OpenTableEntity';
import type { BookingVO, RoomTypeVO, AreaVO, HolidayVO } from '@/types/projectobj';
import type { BuyoutPricePlanVO } from '@/modules/room/entity/buyoutPricePlan';
import type { TimePricePlanVO } from '@/modules/room/entity/timePricePlan';
import type { SessionOperationVOVoderpltvvErpManagentApiVoSessionVO } from '@/api/autoGenerated';
import type { ExtendedStageVO } from '@/modules/room/types/extendedStageVO';

// UI状态接口
export interface IOpenTableState {
  isLoading: boolean;
  isCustomMinimumDialogOpen: boolean;
  activeTab: string;
  customMinimum: string | null;
  adjustmentReason: string;
  bookingInfo: BookingVO | null;
  roomBill: RoomBillEntity | null;
  marketBill: MarketBillEntity | null;
  addedProducts: ProductEntity[];
  form: FormEntity;
  stageInfo: RoomInfoEntity | null;
  billStartTime: number | null;
  billSessionId: string;
  billRoomId: string;
  billRoomName: string;
  billRoomTypeVO: RoomTypeVO | null;
  billRoomAreaVO: AreaVO | null;
  buyoutPricePlanVOs: BuyoutPricePlanVO[];
  timePricePlanVOs: TimePricePlanVO[];
  baseTimePriceFee: number;
  billHolidayVO: HolidayVO | null;
  roomTypes: any[];
  areas: any[];
  isCartValid: boolean;
  cartValidationMessages: string[];
}

// UI计算属性接口
export interface IOpenTableComputed {
  // 处理后的商品账单
  processedMarketBill: ComputedRef<{
    inOrderProducts: ProductEntity[];
    outOrderProducts: ProductEntity[];
  }>;
  // 消费项
  roomConsumptionType: ComputedRef<string>;
  // 最低消费差额
  minimumConsumptionDifference: ComputedRef<number>;
  // 总账单金额
  totalBill: ComputedRef<number>;
  isStandardProductsValid: ComputedRef<boolean>;
  isOptionalGroupsValid: ComputedRef<boolean>;
  isPackagesValid: ComputedRef<boolean>;
  isAllCartItemsValid: ComputedRef<boolean>;
  // 新增：开台立结按钮是否可用
  isImmediatePayEnabled: ComputedRef<boolean>;
}

// UI动作接口
export interface IOpenTableActions {
  // 创建订单
  createOrder(): Promise<SessionOperationVOVoderpltvvErpManagentApiVoSessionVO | null>;
  // 获取开台视图数据
  fetchOpenView(): Promise<void>;
  // 更新账单
  updateBill(bill: any): void;
  // 前往添加商品
  goToAddProducts(): void;
  // 打开自定义低消对话框
  openCustomMinimumDialog(): void;
  // 设置调整原因
  setReason(reason: string): void;
  // 重置自定义低消
  resetCustomMinimum(): void;
  // 确认自定义低消
  confirmCustomMinimum(): void;
  // 处理房间类型和区域更新
  handleRoomTypeAreaUpdate(newRoomType: RoomTypeVO, newArea: AreaVO): Promise<void>;
  // 处理对话框关闭
  handleClose(done: () => void): void;
  updateAddedProducts(): void;
  // 添加新的 actions
  checkStageInfo(roomId: string, bookingId: string): boolean;
  handleAddedProducts(): void;
  handleBookingInfo(): void;
  // 处理添加商品确认
  handleAddProductConfirm(products: any[]): void;
  // 处理添加商品取消
  handleAddProductCancel(): void;
  // 清除对话框缓存
  clearDialogCache(): void;

  // 保留这两个方法作为入口
  handleImmediatePay(): Promise<void>;
  handleLaterPay(): Promise<void>;

  // 返回
  goBack(): void;

  validateCart(): boolean;
  validateStandardProducts(): boolean;
  validateOptionalGroups(): boolean;
  validatePackages(): boolean;

  // 自动选择可选组商品
  autoSelectOptionalGroups(): void;
}

// 总的ViewModel接口
export interface IOpenTableViewModel {
  state: IOpenTableState;
  computed: IOpenTableComputed;
  actions: IOpenTableActions;
  initialize(): void; // 添加初始化方法
}

// 常量配置
export const TABS = [
  { value: 'buyout', label: '买断' },
  { value: 'hourly', label: '买钟' },
  { value: 'verification', label: '核销' }
];
