<template>
  <div class="flex h-full bg-white">
    <!-- Loading遮罩 -->
    <div v-if="vm.state.isLoading" class="fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50">
      <Spinner size="large" color="primary" />
    </div>

    <!-- 左侧内容：包厢信息和计费方式 -->
    <div class="w-[1036px] flex flex-col h-full overflow-hidden border-r">
      <!-- 头部导航 -->
      <div class="m-[30px] m[30px] flex items-center justify-between h-[56px]">
        <div class="flex items-center">
          <button
            class="flex border border-gray-300 rounded-md items-center text-gray-600 w-[106px] h-[56px] justify-center text-[16px] text-[#000]"
            @click="vm.actions.goBack">
            <ArrowLeft class="h-5 w-5 mr-2" />
            返回
          </button>
          <h2 class="text-xl text-red-500 ml-4 text-[32px] font-[400]">开台 / {{ vm.state.billRoomName }}</h2>
        </div>
        <div class="flex items-center">
          <div v-if="vm.state.bookingInfo?.memberCardId" class="ml-2 text-gray-600">卡号：{{ vm.state.bookingInfo?.memberCardId || '' }}</div>
        </div>
      </div>

      <!-- 计费方式 -->
      <div class="mx-[30px] mb-[30px] flex-1 flex flex-col overflow-hidden">
        <!-- 使用div+button替代el-tabs -->
        <div class="mb-[36px] h-[90px] flex justify-between items-center bg-[#F3F3F3] rounded-[14px] px-[32px]">
          <div class="flex space-x-4">
            <button
              class="py-3 px-8 rounded-lg text-lg font-medium transition-colors"
              :class="vm.state.activeTab === 'buyout' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-600'"
              @click="vm.state.activeTab = 'buyout'">
              买断
            </button>
            <button
              class="py-3 px-8 rounded-lg text-lg font-medium transition-colors"
              :class="vm.state.activeTab === 'hourly' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-600'"
              @click="vm.state.activeTab = 'hourly'">
              买钟
            </button>
            <!-- <button class="py-3 px-8 rounded-lg text-lg font-medium transition-colors"
              :class="vm.state.activeTab === 'sales' ? 'bg-red-500 text-white' : 'bg-gray-100 text-gray-600'"
              @click="vm.state.activeTab = 'sales'">
              核销
            </button> -->
          </div>
          <div @click="openBillingAreaSelection" class="flex items-center cursor-pointer justify-center hover:bg-gray-100 active:bg-gray-200 rounded">
            <span class="text-[16px] text-[#666666]">计费方式</span>
            <span class="text-red-500 ml-[16px]">{{ vm.state.billRoomTypeVO?.name || '' }} / {{ vm.state.billRoomAreaVO?.name || '' }}</span>
            <span class="text-gray-400 ml-[8px]">></span>
          </div>
        </div>

        <!-- 根据选中的tab显示不同的计费组件 -->
        <div class="mb-4 overflow-y-auto flex-1">
          <template v-if="vm.state.activeTab === 'buyout'">
            <!-- BuyoutBilling组件 -->
            <BuyoutBilling
              :key="vm.state.activeTab === 'buyout' ? `buyout-${vm.state.buyoutPricePlanVOs?.length || 0}` : 'inactive'"
              :pricePlans="vm.state.buyoutPricePlanVOs"
              :areaId="vm.state.billRoomAreaVO?.id || ''"
              :holidayVO="vm.state.billHolidayVO || undefined"
              :roomInfo="{
                roomTypeId: vm.state.billRoomTypeVO?.id,
                ...(vm.state.stageInfo?.roomVO || {})
              }"
              :isActive="vm.state.activeTab === 'buyout'"
              @updateBill="vm.actions.updateBill"
              :currentTime="vm.state.billStartTime || undefined" />
          </template>
          <template v-else-if="vm.state.activeTab === 'hourly'">
            <HourlyBilling
              :key="vm.state.activeTab === 'hourly' ? `hourly-${vm.state.timePricePlanVOs?.length || 0}` : 'inactive'"
              :pricePlans="vm.state.timePricePlanVOs"
              :areaId="vm.state.billRoomAreaVO?.id || ''"
              :baseTimePriceFee="vm.state.baseTimePriceFee"
              :holidayVO="vm.state.billHolidayVO === null ? undefined : vm.state.billHolidayVO"
              :roomTypeVO="vm.state.billRoomTypeVO || {}"
              :roomInfo="vm.state.stageInfo?.roomVO"
              :isActive="vm.state.activeTab === 'hourly'"
              :currentTime="vm.state.billStartTime || undefined"
              @update-bill="vm.actions.updateBill" />
          </template>
          <template v-else-if="vm.state.activeTab === 'sales'">
            <!-- 按销计费组件 -->
            <div class="p-4 text-center text-gray-400">暂未实现按销计费模式</div>
          </template>
        </div>
      </div>

      <!-- 底部表单区域 -->
      <div class="p-[16px] h-[100px] hidden">
        <div class="flex space-x-4 mb-4">
          <!-- 代订人 -->
          <div class="flex items-center">
            <div>
              <CustomSelector class="w-[160px]" v-model="vm.state.form.booker" label="代订人" :options="[{ value: '', label: '可选择' }]" />
            </div>
          </div>

          <!-- 轮房人 -->
          <div class="flex items-center">
            <div>
              <CustomSelector v-model="vm.state.form.roomManager" label="轮房人" :options="[{ value: '', label: '可选择' }]" />
            </div>
          </div>

          <!-- 排位号码 -->
          <div class="flex items-center">
            <div>
              <CustomSelector v-model="vm.state.form.queueNumber" label="排位号码" :options="[{ value: '', label: '可选择' }]" />
            </div>
          </div>

          <!-- 客户来源 -->
          <div class="flex items-center">
            <div>
              <CustomSelector v-model="vm.state.form.customerSource" label="客户来源" :options="[{ value: '', label: '可选择' }]" />
            </div>
          </div>

          <!-- 客群标签 -->
          <div class="flex items-center w-[424px] items-center border border-gray-300 rounded-md bg-white">
            <div class="custom-selector-label pl-[16px]">客群标签</div>
            <div class="pl-[8px] flex">
              {{ vm.state.form.customerTag || '无' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧内容：账单预览 -->
    <div class="flex-grow flex flex-col h-full overflow-hidden bg-[#F3F3F3]">
      <!-- 账单头部 -->
      <div class="p-[32px]">
        <h3 class="text-[32px] text-[#666666]">账单预览</h3>
      </div>

      <!-- 账单详情 -->
      <div class="flex-grow overflow-y-auto px-[30px]">
        <!-- 包厢账单 -->
        <div class="p-4 bg-white rounded-[16px]">
          <h4 class="text-base font-semibold mb-3 flex items-center justify-between">
            <span class="text-[16px] font-medium text-[#000]">{{ vm.computed.roomConsumptionType }}</span>
            <!-- <div class="flex space-x-2">
              <el-button :icon="Edit" class="border-none" @click="vm.actions.openCustomMinimumDialog">自定义低消</el-button>
            </div> -->
          </h4>

          <!-- 将表格替换为卡片设计 -->
          <div v-if="vm.state.roomBill && vm.state.roomBill.details && vm.state.roomBill.details.length > 0">
            <div v-for="(detail, index) in vm.state.roomBill.details" :key="index" class="mb-3">
              <div class="flex flex-row w-full justify-between">
                <!-- 时长 -->
                <div class="room-bill-card">
                  <div class="room-bill-label">时长</div>
                  <div class="room-bill-value">{{ detail.duration }}分钟</div>
                </div>

                <!-- 时段 -->
                <div class="room-bill-card">
                  <div class="room-bill-label">时段</div>
                  <div class="room-bill-value">{{ detail.startTime }} - {{ detail.endTime }}</div>
                </div>

                <!-- 房费 -->
                <div class="room-bill-card">
                  <div class="room-bill-label">房费</div>
                  <div class="room-bill-value flex justify-center">
                    <PriceDisplay :amountInFen="detail.price" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 无包厢消费时显示提示 -->
          <div v-else class="py-4 text-center text-gray-400">暂无包厢消费</div>

          <div
            v-if="vm.state.roomBill?.details?.length && vm.state.roomBill?.details?.length > 1"
            class="text-right text-sm mt-2 flex justify-end items-baseline">
            <span class="text-[#666]">小计：</span>
            <PriceDisplay :amountInFen="vm.state.roomBill ? vm.state.roomBill.totalPrice : 0" />
          </div>
        </div>

        <!-- 商品账单 -->
        <ProductBill
          :tableData="getTableData()"
          @add-products="vm.actions.goToAddProducts"
          @optional-group-updated="handleOptionalGroupUpdated"
          @delete-product="handleDeleteProduct" />
      </div>

      <!-- 底部按钮区域 -->
      <div class="items-center justify-center pt-[16px]">
        <!-- 功能按钮组 -->
        <!-- <div class="flex space-x-[10px] w-full justify-center px-[16px]">
          <button class="function-btn">会员登录</button>
          <button class="function-btn">商品打折</button>
          <button class="function-btn">备注</button>
          <button class="function-btn">预付款</button>
        </div> -->

        <!-- 结算区域 -->
        <div class="h-[130px] settlement-container bg-white justify-between p-[16px]">
          <div class="flex flex-col justify-center items-start">
            <span class="text-[16px]">待结总计：</span>
            <div class="mt-1">
              <PriceDisplay :amountInFen="vm.computed.totalBill.value" class="price-display-large price-display-white" />
            </div>
          </div>

          <div class="flex">
            <el-tooltip
              :disabled="vm.computed.isAllCartItemsValid.value && vm.computed.isImmediatePayEnabled.value"
              :content="
                !vm.computed.isAllCartItemsValid.value
                  ? '购物车中有要跳转的套餐商品'
                  : vm.computed.totalBill.value <= 0
                    ? '待结金额为0，不能开台立结'
                    : '商品费用不满足低消要求，不能开台立结'
              "
              placement="top">
              <button
                class="btn-black !w-[168px] !h-[90px] text-[24px] !font-[600] !rounded-l-[10px] !rounded-r-[0px]"
                :class="{ 'opacity-50 cursor-not-allowed': !vm.computed.isAllCartItemsValid.value || !vm.computed.isImmediatePayEnabled.value }"
                :disabled="!vm.computed.isAllCartItemsValid.value || !vm.computed.isImmediatePayEnabled.value"
                @click="vm.actions.handleImmediatePay">
                开台立结
              </button>
            </el-tooltip>
            <el-tooltip :disabled="vm.computed.isAllCartItemsValid.value" content="购物车中有要跳转的套餐商品" placement="top">
              <button
                class="h-[90px] bg-[#0000001A] text-[black] text-[24px] font-[600] w-[168px] rounded-tr-[10px] rounded-br-[10px]"
                :class="{ 'opacity-50 cursor-not-allowed': !vm.computed.isAllCartItemsValid.value }"
                :disabled="!vm.computed.isAllCartItemsValid.value"
                @click="vm.actions.handleLaterPay">
                开台后结
              </button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 计费方式选择组件 - 移到根级别 -->
  <RoomTypeAreaSelector
    ref="roomTypeAreaSelector"
    :currentRoomType="vm.state.billRoomTypeVO || { id: '', name: '' }"
    :currentArea="vm.state.billRoomAreaVO || { id: '', name: '' }"
    @update="handleRoomTypeAreaUpdateWrapper" />
</template>

<script setup lang="ts">
import { onActivated, onUnmounted, ref, computed, onMounted, provide, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { ArrowLeft, ArrowRight, Edit, Plus, Minus, CaretRight, Close, InfoFilled, Check, WarningFilled } from '@element-plus/icons-vue';
import { ElDialog, ElButton, ElInput, ElSelect, ElOption, ElTabs, ElTabPane, ElIcon, ElMessage, ElTable, ElTableColumn, ElTooltip } from 'element-plus';
import { sumBy } from 'lodash-es';
import Spinner from '@/components/customer/Spinner.vue';
import CustomSelector from '@/components/customer/CustomSelector.vue';
import PriceDisplay from '@/components/customer/PriceDisplay.vue';
import HourlyBilling from '../../components/HourlyBilling/index.vue';
import BuyoutBilling from '../../components/BuyoutBilling/index.vue';
import RoomTypeAreaSelector from '../../components/RoomTypeAreaSelector.vue';
import PackageDetailItem from '../../components/PackageDetailItem.vue';
import ProductBill from './components/ProductBill/index.vue';

import { useOpenTable } from './presenter';
import type { IOpenTableViewModel } from './viewmodel';
import type { ProductEntity } from '../../entity/OpenTableEntity';
import { useStageStore } from '@/stores/stageStore';
import { useProductStore } from '../../store/productStore';

import { processProductBillData, TableRowItem, generateDetailsText, initializeZeroQuantities } from './billUtils';

import { getNormalizedOptionType } from '@/utils/productPackageUtils';

// 添加类型导入
import { MarketBill, ProcessedMarketBill } from './billUtils';

defineOptions({
  name: 'OpenTable'
});

// 视图模型
const vm: IOpenTableViewModel = useOpenTable();
const route = useRoute();
const stageStore = useStageStore();
const productStore = useProductStore();

// 计费方式区域弹窗 - 不再需要这个状态
// const showBillingAreaDialog = ref(false)
const selectedAreaId = ref('');
const roomTypeAreaSelector = ref<InstanceType<typeof RoomTypeAreaSelector> | null>(null); // 添加对RoomTypeAreaSelector的引用，并指定正确的类型

// 注释或删除不再需要的购物车验证弹窗变量
// const showCartValidationDialog = ref(false)

// 打开计费方式选择对话框
const openBillingAreaSelection = () => {
  roomTypeAreaSelector.value?.openDialog();
};

// 修改RoomTypeAreaSelector update事件处理函数，直接应用更改并关闭对话框
const handleRoomTypeAreaUpdateWrapper = (roomType: { id: string; name: string }, area: { id: string; name: string }) => {
  vm.actions.handleRoomTypeAreaUpdate(roomType as any, area as any);
  // 不再需要关闭showBillingAreaDialog
};

// 生命周期钩子
onActivated(() => {
  // 初始化组件，包含从 URL 参数恢复房间信息的逻辑
  // 这将确保即使在页面刷新后也能正确加载数据
  vm.initialize();

  // 处理商品和预订信息 - 无需再次调用fetchOpenView，因为initialize中已经调用了
  if (vm.state.stageInfo) {
    // 处理商品和预订信息
    vm.actions.handleAddedProducts();
    vm.actions.handleBookingInfo();

    // 确保所有可选组商品的quantity初始化为0
    if (vm.state.marketBill) {
      initializeZeroQuantities(vm.state.marketBill as unknown as MarketBill);
    }

    // 初始化完成后进行购物车验证
    vm.actions.validateCart();
  }

  // 初始化选中的区域ID
  if (vm.state.billRoomAreaVO?.id) {
    selectedAreaId.value = vm.state.billRoomAreaVO.id;
  }

  // 添加事件监听，当商品处理完成后再次初始化数量和验证购物车
  window.addEventListener('afterProductsAdded', handleAfterProductsAdded);
});

onUnmounted(() => {
  stageStore.setStageSource(null);
  window.removeEventListener('afterProductsAdded', handleAfterProductsAdded);
});

// 产品添加后的处理函数
const handleAfterProductsAdded = () => {
  // 初始化数量
  if (vm.state.marketBill) {
    initializeZeroQuantities(vm.state.marketBill as unknown as MarketBill);
  }

  // 验证购物车
  nextTick(() => {
    vm.actions.validateCart();
  });
};

// 如果存在房间ID参数，则重新初始化
onMounted(() => {
  if (route.params.roomId) {
    vm.initialize();
  }

  // 添加对initializeZeroQuantities的调用
  nextTick(() => {
    if (vm.state.marketBill) {
      initializeZeroQuantities(vm.state.marketBill as unknown as MarketBill);
    }

    // 验证购物车
    vm.actions.validateCart();
  });
});

// 获取表格数据，合并订单内和订单外商品
const getTableData = (): TableRowItem[] => {
  return processProductBillData(
    (vm.state.marketBill || {}) as unknown as MarketBill,
    vm.computed.processedMarketBill as unknown as ProcessedMarketBill,
    vm.actions.autoSelectOptionalGroups
  );
};

// 根据父项ID查找对应商品
const findParentItem = (parentId: string): any => {
  if (!parentId) {
    return undefined;
  }

  if (!vm.state.marketBill) {
    return undefined;
  }

  // 在标准商品中查找
  if (vm.state.marketBill.standardProducts) {
    const parent = vm.state.marketBill.standardProducts.find(item => item.id === parentId);
    if (parent) {
      return parent;
    }
  }

  // 在免费商品中查找
  if (vm.state.marketBill.freeProducts) {
    const parent = vm.state.marketBill.freeProducts.find(item => item.id === parentId);
    if (parent) {
      return parent;
    }
  }

  // 在账单外商品中查找
  if (vm.computed.processedMarketBill && vm.computed.processedMarketBill.value.outOrderProducts) {
    const parent = vm.computed.processedMarketBill.value.outOrderProducts.find(item => item.id === parentId);
    if (parent) {
      return parent;
    }
  }

  // 在所有表格数据中查找（可能是可选组或其他特殊商品）
  const allItems = getTableData();
  const parentInAllItems = allItems.find(item => item.id === parentId);

  if (parentInAllItems) {
    return parentInAllItems;
  }

  return undefined;
};

// 处理可选组更新事件
const handleOptionalGroupUpdated = (updatedGroup: any) => {
  try {
    if (!updatedGroup || !updatedGroup.optionalProducts) {
      ElMessage.error('无效的可选组数据');
      return;
    }

    // 在主表原始数据中找到对应的行
    const tableData = getTableData();
    const originalItem = tableData.find((item: any) => item.id === updatedGroup.id);
    if (!originalItem) {
      ElMessage.error('未找到原始数据项');
      return;
    }

    // 计算实际选中的商品数量 - 使用统一逻辑：选择数量 × 规格数量
    const totalSelectedQuantity = updatedGroup.optionalProducts.reduce((sum: number, product: any) => {
      const selectedQuantity = product.tempQuantity || 0;
      const unitCount = product.count || 1;
      return sum + selectedQuantity * unitCount;
    }, 0);
    // 更新已选数量字段
    originalItem.selectedQuantity = totalSelectedQuantity;

    // 更新原始数据中的可选商品数据
    originalItem.optionalProducts = updatedGroup.optionalProducts.map((product: any) => {
      const productData = {
        ...product,
        // 将tempQuantity更新到quantity字段
        quantity: product.tempQuantity || 0
      };

      // 获取选择模式
      const optionType = getNormalizedOptionType(updatedGroup);

      // 保留count字段用于统一的价格计算逻辑
      // 不再删除count字段，因为bycount和byplan都需要使用count进行计算

      return productData;
    });

    // 更新其他必要字段
    if (originalItem.details) {
      originalItem.details = generateDetailsText(originalItem);
    }

    // 计算总金额 - 使用统一逻辑：单价 × 选择数量 × 规格数量
    if (!originalItem.isFree && originalItem.optionalProducts) {
      originalItem.totalAmount = originalItem.optionalProducts.reduce((sum: number, product: any) => {
        const selectedQuantity = product.quantity || 0;
        const unitCount = product.count || 1;
        const unitPrice = product.price || 0;
        const productTotal = unitPrice * selectedQuantity * unitCount;

        // 调试日志
        if (selectedQuantity > 0) {
          console.log(`[💰index.vue价格计算] "${product.name}": ${selectedQuantity}数量 × ${unitCount}规格 × ${unitPrice}分/个 = ${productTotal}分`);
        }

        return sum + productTotal;
      }, 0);

      console.log(`[💰index.vue总价格] 可选组总金额: ${originalItem.totalAmount}分`);
    }

    // 更新marketBill数据
    if (vm.state.marketBill) {
      // 更新付费可选组
      if (originalItem.uniqueId === 'optional-group' && vm.state.marketBill.optionalProducts) {
        // 使用类型断言避免类型错误
        const optionalProducts = vm.state.marketBill.optionalProducts as any;

        // 更新选择的商品
        optionalProducts.products = originalItem.optionalProducts;

        // 关键：更新selectedQuantity字段
        optionalProducts.selectedQuantity = totalSelectedQuantity;
        // 确保每个产品都有正确的金额计算 - 使用统一逻辑
        if (optionalProducts.products) {
          optionalProducts.products.forEach((product: any) => {
            const selectedQuantity = product.quantity || 0;
            const unitCount = product.count || 1;
            const unitPrice = product.price || 0;
            product.totalAmount = unitPrice * selectedQuantity * unitCount;
          });
        }
      }

      // 更新免费可选组
      if (originalItem.uniqueId === 'optional-free-group' && vm.state.marketBill.optionalFreeProducts) {
        // 使用类型断言避免类型错误
        const optionalFreeProducts = vm.state.marketBill.optionalFreeProducts as any;

        // 更新选择的商品
        optionalFreeProducts.products = originalItem.optionalProducts;

        // 关键：更新selectedQuantity字段
        optionalFreeProducts.selectedQuantity = totalSelectedQuantity;
        // 免费商品的金额总是0
        if (optionalFreeProducts.products) {
          optionalFreeProducts.products.forEach((product: any) => {
            product.totalAmount = 0;
          });
        }
      }
    }

    // 立即触发视图更新
    nextTick(() => {
      // 重新处理商品数据
      vm.actions.handleAddedProducts();
      // 验证购物车
      vm.actions.validateCart();
    });
  } catch (error) {
    console.error('处理可选组确认出错:', error);
    ElMessage.error('更新可选组数据失败');
  }
};

// 对vm.actions.goToAddProducts方法进行包装，确保添加商品时不重置已有选中商品数量
const originalGoToAddProducts = vm.actions.goToAddProducts;
vm.actions.goToAddProducts = function () {
  // 保存当前已选商品的数量信息
  const currentState = {
    optionalProducts: null as any,
    optionalFreeProducts: null as any
  };

  // 如果存在可选组商品，保存其状态
  if (vm.state.marketBill?.optionalProducts?.products) {
    currentState.optionalProducts = {
      products: vm.state.marketBill.optionalProducts.products.map((p: any) => ({
        id: p.id,
        quantity: p.quantity || 0
      })),
      selectedQuantity: (vm.state.marketBill.optionalProducts as any).selectedQuantity || 0
    };
  }

  // 如果存在可选免费组商品，保存其状态
  if (vm.state.marketBill?.optionalFreeProducts?.products) {
    currentState.optionalFreeProducts = {
      products: vm.state.marketBill.optionalFreeProducts.products.map((p: any) => ({
        id: p.id,
        quantity: p.quantity || 0
      })),
      selectedQuantity: (vm.state.marketBill.optionalFreeProducts as any).selectedQuantity || 0
    };
  }

  // 调用原始方法
  originalGoToAddProducts.apply(vm.actions);

  // 添加一个事件监听器，在商品添加完成后恢复可选组商品的数量
  const restoreOptionalGroupState = () => {
    // 恢复可选组商品数量
    if (currentState.optionalProducts && vm.state.marketBill?.optionalProducts?.products) {
      // 恢复每个商品的数量
      currentState.optionalProducts.products.forEach((savedProduct: any) => {
        const product = vm.state.marketBill!.optionalProducts!.products.find((p: any) => p.id === savedProduct.id);
        if (product) {
          product.quantity = savedProduct.quantity;
        }
      });

      // 恢复已选总数量
      (vm.state.marketBill.optionalProducts as any).selectedQuantity = currentState.optionalProducts.selectedQuantity;
    }

    // 恢复免费可选组商品数量
    if (currentState.optionalFreeProducts && vm.state.marketBill?.optionalFreeProducts?.products) {
      // 恢复每个商品的数量
      currentState.optionalFreeProducts.products.forEach((savedProduct: any) => {
        const product = vm.state.marketBill!.optionalFreeProducts!.products.find((p: any) => p.id === savedProduct.id);
        if (product) {
          product.quantity = savedProduct.quantity;
        }
      });

      // 恢复已选总数量
      (vm.state.marketBill.optionalFreeProducts as any).selectedQuantity = currentState.optionalFreeProducts.selectedQuantity;
    }

    // 移除事件监听器
    window.removeEventListener('afterProductsAdded', restoreOptionalGroupState);
  };

  // 添加事件监听
  window.addEventListener('afterProductsAdded', restoreOptionalGroupState);
};

// 处理删除商品事件
const handleDeleteProduct = (product: TableRowItem) => {
  try {
    // 根据商品的 uniqueId 判断商品来源并删除
    if (product.uniqueId.startsWith('outorder-')) {
      // 这是从购物车添加的商品，需要从 vm.state.addedProducts 中删除
      if (vm.state.addedProducts && vm.state.addedProducts.length > 0) {
        // 使用商品 id 来查找并删除
        const productIndex = vm.state.addedProducts.findIndex((item: any) => item.id === product.id);

        if (productIndex >= 0) {
          // 直接从原始数据源删除，让计算属性自动更新
          vm.state.addedProducts.splice(productIndex, 1);

          // 立即触发视图更新
          nextTick(() => {
            // 验证购物车
            vm.actions.validateCart();
          });

          // 移除成功提示消息，因为这是UI层面的删除操作
        } else {
          console.warn('无法找到要删除的商品:', product);
        }
      }
    } else {
      console.warn('该商品不能删除:', product);
    }
  } catch (error) {
    console.error('删除商品失败:', error);
  }
};
</script>
<style scoped>
/* 功能按钮 */
.function-btn {
  width: 100%;
  height: 64px;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  padding: 20px 18px;
  font-size: 16px;
  color: #1f2937;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

/* 结算区域容器 */
.settlement-container {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 28px;
}

/* 价格显示区域 */
.price-section {
  width: 440px;
  height: 100%;
  display: flex;
  border-radius: 16px;
  align-items: center;
  gap: 8px;
}

.price-display-large {
  font-size: 30px;
  font-weight: bold;
}

.price-display-white {
  color: #c04646;
}

.settlement-container .settle-btn {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* 设置明细行样式 */
:deep(.detail-row-class) {
  border-top: none !important;
  font-size: 14px;
}

:deep(.detail-row-class .cell) {
  padding: 0 !important;
  /* 移除padding，让PackageDetailItem组件控制内部间距 */
}

/* 添加套餐商品与明细间的视觉连接 */
:deep(.is-package-row + .detail-row-class) {
  border-top: none !important;
}

/* 删除明细行的hover效果 */
:deep(.detail-row-class):hover td {
  background-color: #f9f9f9 !important;
}

/* 确保明细行不会出现hover高亮效果 */
:deep(.el-table__body tr.detail-row-class:hover > td.el-table__cell) {
  background-color: #f9f9f9 !important;
}

/* 添加可选组的样式 */
:deep(.is-optional-group-row) {
  background-color: #fff !important;
}

/* 确保可选组和套餐表格行的底部边框不显示 */
:deep(.is-package-row td),
:deep(.is-optional-group-row td) {
  border-bottom: none !important;
}

/* 修正其他列样式 */
:deep(.el-table .cell) {
  word-break: normal !important;
  white-space: normal !important;
  /* 允许必要时文本换行 */
  line-height: 1.5 !important;
}

.room-bill-card {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.room-bill-label {
  font-size: 16px;
  font-weight: 400;
  color: #666;
}

.room-bill-value {
  font-size: 20px;
  font-weight: 500;
  color: #4c4c4c;
}

:deep(.price-unit) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.price-integer) {
  font-size: 20px;
  font-weight: 500;
}

:deep(.price-decimal) {
  font-size: 16px;
  font-weight: 500;
}
</style>
