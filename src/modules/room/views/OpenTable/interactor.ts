import { RoomApi } from '../../api/room';
import { OrderApi } from '@/modules/order/api/order';
import type { SessionOperationVOVoderpltvvErpManagentApiVoSessionVO } from '@/api/autoGenerated';
import type { OpenOrderRequest } from '@/modules/order/api/order';
import { printingService } from '@/application/printingService';
import type { RoomInfoEntity } from '@/modules/room/entity/OpenTableEntity';

export class OpenTableInteractor {
  /**
   * 获取开台视图数据
   */
  static async fetchOpenView(params: { roomId: string; sessionId: string; typeId?: string; areaId?: string }): Promise<RoomInfoEntity | null> {
    try {
      // 验证参数
      if (!params.roomId) {
        console.error('获取房间信息失败: 缺少roomId参数');
        return null;
      }

      console.log('调用RoomApi.openViewRoom获取房间信息，参数:', params);
      // @ts-ignore
      const response = await RoomApi.openViewRoom(params);

      if (response.code === 0 && response.data) {
        console.log('成功获取房间信息:', response.data);
        // @ts-ignore
        return response.data;
      }

      console.error('获取房间信息失败:', response.message, '响应代码:', response.code);
      return null;
    } catch (error) {
      console.error('获取房间信息异常:', error);
      return null;
    }
  }

  /**
   * 开台操作
   */
  static async openTable(params: OpenOrderRequest): Promise<{
    success: boolean;
    data?: SessionOperationVOVoderpltvvErpManagentApiVoSessionVO;
    error?: string;
  }> {
    try {
      const response = await OrderApi.openOrder(params);
      if (response.code === 0) {
        return {
          success: true,
          data: response.data
        };
      }
      return {
        success: false,
        error: response.message
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '开台失败'
      };
    }
  }

  /**
   * 续台操作
   */
  static async continueBill(params: OpenOrderRequest): Promise<{
    success: boolean;
    data?: SessionOperationVOVoderpltvvErpManagentApiVoSessionVO;
    error?: string;
  }> {
    try {
      const response = await OrderApi.openContinue(params);
      if (response.code === 0) {
        return {
          success: true,
          data: response.data
        };
      }
      return {
        success: false,
        error: response.message
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '续台失败'
      };
    }
  }

  /**
   * 打印开台单
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选)
   * @returns 打印结果
   */
  static async printSessionOrder(sessionId: string, orderNos?: string[]): Promise<boolean> {
    try {
      console.log('调用打印服务打印开台单，会话ID:', sessionId);
      // 调用printingService打印开台单
      const result = await printingService.printSessionOrderBySessionId(sessionId, orderNos);

      if (result) {
        console.log('开台单打印成功');
      } else {
        console.error('打印服务返回失败结果');
      }

      return result;
    } catch (error) {
      console.error('打印开台单失败:', error);
      return false;
    }
  }

  /**
   * 统一处理开台成功后的完整打印流程
   * 包括：开台单/续台单 + 出品单（如果有商品）
   * @param sessionId 会话ID
   * @param orderNos 订单号数组(可选，如果有则打印出品单)
   * @param isContinueOperation 是否为续台操作，可选参数
   */
  static printOpenTableDocuments(sessionId: string, orderNos?: string[], isContinueOperation?: boolean): void {
    try {
      if (!sessionId) {
        console.warn('OpenTableInteractor: 没有可打印单据的sessionId');
        return;
      }

      console.log('OpenTableInteractor: 开始统一打印开台相关单据, 会话ID:', sessionId, '订单号:', orderNos, '是否续台操作:', isContinueOperation);

      // 1. 根据是否为续台操作决定打印开台单还是续台单
      try {
        if (isContinueOperation) {
          console.log('OpenTableInteractor: 续台操作，准备打印续台单, 会话ID:', sessionId);
          printingService.printRoomExtensionBySessionId(sessionId, orderNos);
        } else {
          console.log('OpenTableInteractor: 开台操作，准备打印开台单, 会话ID:', sessionId);
          printingService.printSessionOrderBySessionId(sessionId, orderNos);
        }
      } catch (printError) {
        console.error('OpenTableInteractor: 打印单据时发生错误:', printError);
      }

      // 2. 打印出品单 - 如果有订单号（说明有商品）
      if (orderNos && orderNos.length > 0) {
        try {
          console.log('OpenTableInteractor: 准备打印出品单, 订单号:', orderNos, '会话ID:', sessionId);
          printingService.printProductOutBySessionId(sessionId, orderNos);
        } catch (printError) {
          console.error('OpenTableInteractor: 打印出品单时发生错误:', printError);
          // 出品单打印失败不影响整体结果
        }
      } else {
        console.log('OpenTableInteractor: 无订单号，跳过出品单打印');
      }

      console.log('OpenTableInteractor: 开台相关单据打印流程完成');
    } catch (error) {
      console.error('OpenTableInteractor: 统一打印开台单据时出错:', error);
    }
  }
}
