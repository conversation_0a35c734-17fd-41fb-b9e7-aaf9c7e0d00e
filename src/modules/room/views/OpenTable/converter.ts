import type { OpenOrderRequestEntity, ProductEntity, RoomBillEntity, FormEntity } from '../../entity/OpenTableEntity';
import { toUnixTimestamp, calculateEndTime } from '@/utils/dateUtils';
import { convertToFen } from '@/utils/priceUtils';
import type { RoomVO } from '@/types/projectobj';
import type { SessionOrderData } from '@/domains/prints/session-order/models/session-order-data';
import type { RoomInfo, ProductInfo as PrintProductInfo } from '@/domains/prints/shared/models/base-models';
import type { RoomPackageInfo, ReservationInfo } from '@/domains/prints/session-order/models/session-order-data';
import _ from 'lodash';

/**
 * OpenTable 视图模型转换层
 * 负责视图数据与实体数据之间的转换
 */
export class OpenTableConverter {
  /**
   * 展开可选组商品，将组内商品提取为平级结构
   * @param products 原始商品列表
   * @returns 展开后的平级商品列表
   */
  static flattenGroupProducts(products: ProductEntity[]): ProductEntity[] {
    const result: ProductEntity[] = [];

    products.forEach(product => {
      // 如果不是商品组，直接添加
      if (!product.isGroup) {
        result.push(product);
        return;
      }

      // 如果是商品组且有packageProductInfo，展开其中的商品
      if (product.isGroup && product.packageProductInfo && Array.isArray(product.packageProductInfo)) {
        product.packageProductInfo.forEach(subProduct => {
          // 使用统一逻辑计算数量：选择数量 × 规格数量
          const selectedQuantity = subProduct.quantity || 1; // 组的选择数量
          const unitCount = subProduct.count || 1; // 子商品的规格数量（每份包含多少个商品）
          const finalQuantity = selectedQuantity * unitCount; // 实际商品数量

          // 使用统一逻辑计算价格：单价 × 选择数量 × 规格数量
          const unitPrice = subProduct.price || 0;
          const totalAmount = unitPrice * selectedQuantity * unitCount;
          const payPrice = unitPrice;
          const payAmount = totalAmount;

          // 创建展开后的商品项
          const flattenedProduct: ProductEntity = {
            id: subProduct.id,
            productName: subProduct.name || '',
            name: subProduct.name || '',
            flavors: product.flavors || '',
            quantity: finalQuantity, // 实际商品数量（选择数量 × 规格数量）
            count: unitCount, // 保留规格数量字段
            unit: subProduct.unit || product.unit || '个',
            price: unitPrice, // 单价
            currentPrice: unitPrice, // 当前价格
            payPrice: payPrice, // 支付单价
            payAmount: product.isFree ? 0 : payAmount, // 如果外层是免费的，payAmount为0
            originalPrice: unitPrice, // 原价
            originalAmount: unitPrice * finalQuantity, // 原始总额
            totalAmount: product.isFree ? 0 : totalAmount, // 如果外层是免费的，totalAmount为0
            details: product.details || '',
            isFree: product.isFree || false, // 使用外层的isFree
            isGift: product.isGift || false, // 使用外层的isGift
            isInitialOrder: product.isInitialOrder || false,
            isPackage: false, // 展开后的商品不再是套餐
            isGroup: false, // 展开后的商品不再是组
            packageDetail: undefined,
            packageProductInfo: undefined
          };

          result.push(flattenedProduct);
        });
      }
    });

    return result;
  }

  /**
   * 构建开台请求参数
   * @param params 开台所需基础参数
   * @returns 开台请求实体
   */
  static convertToOpenOrderRequest(params: {
    roomVO: RoomVO;
    form: FormEntity;
    roomBill: RoomBillEntity;
    inOrderProducts: ProductEntity[];
    outOrderProducts: ProductEntity[];
    billSessionId: string;
    billStartTime: number;
    billRoomTypeVO: any;
    billRoomAreaVO: any;
    customMinimum: string | null;
    isOpenEnd: boolean;
    totalBill: number;
    bookingId?: string;
  }): OpenOrderRequestEntity {
    const {
      roomVO,
      form,
      roomBill,
      inOrderProducts,
      outOrderProducts,
      billSessionId,
      billStartTime,
      billRoomTypeVO,
      billRoomAreaVO,
      customMinimum,
      isOpenEnd,
      totalBill,
      bookingId
    } = params;

    // 处理时间
    const [startDate, startTime] = form.consumptionTime.split(' ~ ')[0].split(' ');
    const { endDate, endTime } = calculateEndTime(startDate, startTime, form.duration);
    const startTimestamp = toUnixTimestamp(`${startDate} ${startTime}:00`);
    const endTimestamp = toUnixTimestamp(`${endDate} ${endTime}:00`);

    console.log('[convertToOpenOrderRequest] roomBill', roomBill);

    // 构建房间计划
    const orderRoomPlanVOS = _.map(roomBill.details, (detail, index) => {
      // 获取前一个时间段的结束日期，用于处理连续时间段
      let previousEndDate = startDate;
      if (index > 0 && roomBill.details[index - 1]) {
        // 计算前一个时间段的结束日期
        const prevDetail = roomBill.details[index - 1];
        const prevResult = calculateEndTime(
          index === 1 ? startDate : previousEndDate, // 第一个时间段使用startDate，后续使用累积的previousEndDate
          prevDetail.startTime,
          prevDetail.duration
        );
        previousEndDate = prevResult.endDate;
      }

      // 使用previousEndDate确保连续时间段正确处理跨天情况
      const { endDate, endTime } = calculateEndTime(previousEndDate, detail.startTime, detail.duration);

      const detailStartTimestamp = toUnixTimestamp(`${previousEndDate} ${detail.startTime}:00`);
      const detailEndTimestamp = toUnixTimestamp(`${endDate} ${endTime}:00`);

      return {
        roomId: roomVO.id,
        roomName: roomVO.name,
        pricePlanId: detail.id,
        pricePlanName: detail.planName,
        startTime: detailStartTimestamp,
        endTime: detailEndTimestamp,
        duration: detail.duration,
        payAmount: Math.round(detail.price),
        originalPayAmount: Math.round(detail.price),
        isTimeConsume: detail.isTimeConsume
      };
    });
    // console.log('[OpenTableConverter] roomBill', roomBill)
    console.log('[OpenTableConverter] inOrderProducts：', inOrderProducts);

    // 展开可选组商品为平级结构
    const flattenedInOrderProducts = this.flattenGroupProducts(inOrderProducts);
    console.log('[OpenTableConverter] flattenedInOrderProducts：', flattenedInOrderProducts);

    const inOrderProductInfos = flattenedInOrderProducts
      .filter(item => item.quantity > 0)
      ?.map(item => {
        // 检查是否为套餐商品（展开后的商品不再是套餐，所以isPackage应该为false）
        const isPackage = item.isPackage;

        // 构建商品基础信息
        const productInfo = {
          ...item,
          // 展开后的商品都是普通商品，使用productId
          productId: item.id,
          packageId: isPackage ? item.id : ''
        };
        console.log('[OpenTableConverter] inOrderProductInfos productInfo', productInfo);
        return productInfo;
      });

    // 处理账单外商品 - 同样需要处理套餐商品
    const outOrderProductInfos = outOrderProducts?.map(item => {
      // 检查是否为套餐商品
      const isPackage = !!item.isPackage;

      // 构建商品基础信息
      const productInfo = {
        ...item,
        // 套餐商品处理：将id设置给packageId，将productId设置为空字符串
        productId: isPackage ? '' : item.id,
        packageId: isPackage ? item.id : ''
      };

      // 处理套餐商品的packageProductInfo
      if (isPackage) {
        // 确保套餐信息存在
        if (item.packageDetail) {
          // 如果packageDetail是字符串，尝试解析
          if (typeof item.packageDetail === 'string') {
            try {
              const detailObj = JSON.parse(item.packageDetail);

              // 优先从selectedProducts中提取套餐明细
              if (detailObj.selectedProducts && Array.isArray(detailObj.selectedProducts)) {
                productInfo.packageProductInfo = detailObj.selectedProducts.map((selectedProduct: any) => ({
                  id: selectedProduct.id,
                  count: selectedProduct.count || 1,
                  price: selectedProduct.currentPrice || 0,
                  name: selectedProduct.name || ''
                }));
                console.log(`[convertToOpenOrderRequest] 从selectedProducts中提取的packageProductInfo:`, productInfo.packageProductInfo);
              }
              // 如果没有selectedProducts，尝试从packageProducts中提取
              else if (detailObj.packageProducts) {
                // 如果packageProducts是字符串，解析为数组
                if (typeof detailObj.packageProducts === 'string') {
                  try {
                    productInfo.packageProductInfo = JSON.parse(detailObj.packageProducts);
                  } catch (e) {
                    console.error('解析packageProducts字符串失败:', e);
                    productInfo.packageProductInfo = [];
                  }
                } else {
                  // 如果已经是数组，直接使用
                  productInfo.packageProductInfo = detailObj.packageProducts;
                }
              }
            } catch (e) {
              console.error('解析packageDetail出错:', e);
            }
          } else if (typeof item.packageDetail === 'object') {
            // 如果packageDetail是对象，优先从selectedProducts中提取
            if (item.packageDetail.selectedProducts && Array.isArray(item.packageDetail.selectedProducts)) {
              productInfo.packageProductInfo = item.packageDetail.selectedProducts.map((selectedProduct: any) => ({
                id: selectedProduct.id,
                count: selectedProduct.count || 1,
                price: selectedProduct.currentPrice || 0,
                name: selectedProduct.name || ''
              }));
              console.log(`[convertToOpenOrderRequest] 从对象selectedProducts中提取的packageProductInfo:`, productInfo.packageProductInfo);
            }
            // 如果没有selectedProducts，尝试从packageProducts中提取
            else if (item.packageDetail.packageProducts) {
              if (typeof item.packageDetail.packageProducts === 'string') {
                try {
                  productInfo.packageProductInfo = JSON.parse(item.packageDetail.packageProducts);
                } catch (e) {
                  console.error('解析packageProducts字符串失败:', e);
                  productInfo.packageProductInfo = [];
                }
              } else {
                // 如果已经是数组，直接使用
                productInfo.packageProductInfo = item.packageDetail.packageProducts;
              }
            }
          }
        }
      }

      return productInfo;
    });

    // 在构建最终API请求前，根据isOpenEnd决定packageProductInfo格式
    const prepareForApiCall = (products: any[], isOpenEnd: boolean) => {
      if (!products) return [];

      return products.map(product => {
        const result = { ...product };

        // 检查是否为套餐商品
        if (product.packageId && product.packageProductInfo) {
          let packageInfoArray: any[] = [];

          // 先统一转换为数组对象格式
          if (Array.isArray(product.packageProductInfo)) {
            // 保留数组中每个对象的完整信息
            packageInfoArray = product.packageProductInfo.map((item: any) => ({
              id: item.id,
              count: item.count || 1,
              price: item.price || 0,
              name: item.name || ''
            }));
          } else if (typeof product.packageProductInfo === 'object') {
            // 如果是单个对象，转换为数组格式
            packageInfoArray = [
              {
                id: product.packageProductInfo.id,
                count: product.packageProductInfo.count || 1,
                price: product.packageProductInfo.price || 0,
                name: product.packageProductInfo.name || ''
              }
            ];
          } else if (typeof product.packageProductInfo === 'string') {
            // 如果是字符串，尝试解析为数组对象
            try {
              const parsed = JSON.parse(product.packageProductInfo);
              if (Array.isArray(parsed)) {
                packageInfoArray = parsed.map((item: any) => ({
                  id: item.id,
                  count: item.count || 1,
                  price: item.price || 0,
                  name: item.name || ''
                }));
              } else if (parsed && typeof parsed === 'object') {
                packageInfoArray = [
                  {
                    id: parsed.id,
                    count: parsed.count || 1,
                    price: parsed.price || 0,
                    name: parsed.name || ''
                  }
                ];
              }
            } catch (e) {
              console.error('处理packageProductInfo字符串失败:', e);
              packageInfoArray = [];
            }
          }

          // 根据isOpenEnd决定最终格式
          if (isOpenEnd) {
            // 开台立结：保持数组对象格式
            result.packageProductInfo = packageInfoArray;
            console.log(`[prepareForApiCall] 开台立结 - 套餐商品 ${product.productName} 的 packageProductInfo (数组):`, result.packageProductInfo);
          } else {
            // 开台后结：转换为字符串格式
            result.packageProductInfo = JSON.stringify(packageInfoArray);
            console.log(`[prepareForApiCall] 开台后结 - 套餐商品 ${product.productName} 的 packageProductInfo (字符串):`, result.packageProductInfo);
          }
        }

        return result;
      });
    };
    // console.log('[OpenTableConverter] startTime', startTimestamp)
    // console.log('[OpenTableConverter] endTime', endTimestamp)
    // 构建开台请求实体
    return {
      roomId: roomVO.id,
      sessionId: billSessionId,
      startTime: startTimestamp,
      endTime: endTimestamp,
      consumptionMode: form.consumptionMode,
      selectedAreaId: billRoomAreaVO?.id,
      selectedRoomTypeId: billRoomTypeVO?.id,
      buyMinute: form.duration || 0,
      payAmount: totalBill,
      originalAmount: totalBill,
      isOpenTableSettled: isOpenEnd,
      minimumCharge: convertToFen(parseFloat(customMinimum || '0')),
      currentTime: billStartTime,
      orderRoomPlanVOS: orderRoomPlanVOS as any[],
      inOrderProductInfos: prepareForApiCall(inOrderProductInfos, isOpenEnd) as any,
      outOrderProductInfos: prepareForApiCall(outOrderProductInfos, isOpenEnd) as any,
      roomVO,
      bookingId: bookingId || '',
      venueId: roomVO.venueId,
      pricePlanId: '', // 在Presenter中设置
      pricePlanName: '', // 在Presenter中设置
      timeChargeMode: '' // 在Presenter中设置
    };
  }

  /**
   * 转换商品数据
   * @param product 商品实体
   * @returns 转换后的商品数据
   */
  static convertProduct(product: ProductEntity) {
    const originalPrice = product.currentPrice || product.originalPrice || product.price || 0; // 原价
    const realPrice = product.price !== undefined && product.price !== null ? product.price : originalPrice; // 真实计价
    const quantity = Number(product.quantity || product.count || 0);

    const isPackage = !!product.isPackage;

    const isFree = realPrice === 0 || product.isFree;
    return {
      id: product.id,
      productName: product.name || product.productName,
      flavors: product.flavors || '',
      quantity: quantity,
      unit: product.unit,
      price: realPrice,
      originalPrice: originalPrice,
      currentPrice: originalPrice,
      payPrice: realPrice,
      originalAmount: quantity * Number(originalPrice),
      payAmount: isFree ? 0 : quantity * Number(realPrice),
      totalAmount: isFree ? 0 : quantity * Number(realPrice),
      isPackage: isPackage,
      isGroup: product.isGroup || false, // 保留isGroup字段
      packageDetail: product.packageDetail,
      packageProductInfo: product.packageProductInfo,
      details: product.details || '',
      isFree: isFree,
      isGift: isFree,
      isInitialOrder: true // 从marketBill中获取的商品标记为开台时的商品
    };
  }

  /**
   * 转换从商品弹窗返回的商品数据为ProductEntity
   * 将Store中的商品数据转换为统一的ProductEntity格式
   * @param product 商品数据
   * @returns 转换后的ProductEntity
   */
  static convertAddedProduct(product: any): ProductEntity {
    console.log('[OpenTableConverter] convertAddedProduct', product);
    // 提取关键字段，添加兜底值
    const quantity = product.quantity || 0;
    // 调整获取price的顺序，优先使用currentPrice
    const currentPrice = product.currentPrice || product.payPrice || product.price || 0;
    const productId = product.id || product.productId || '';
    const productName = product.productName || product.name || '';

    // console.log(`[OpenTableConverter] 提取的关键字段: ID=${productId}, 名称=${productName}, 数量=${quantity}, 单价=${currentPrice}`)

    // 检查是否为套餐
    const isPackage = !!product.isPackage;
    if (isPackage) {
      // console.log(`[OpenTableConverter] 检测到套餐商品: ${productName}，处理套餐属性`)
    }

    const result = {
      id: productId,
      productName: productName,
      name: productName,
      flavors: product.flavors || '',
      quantity: quantity,
      unit: product.unit || '',
      price: currentPrice, // 保持与currentPrice一致
      currentPrice: currentPrice, // 确保使用currentPrice
      payPrice: currentPrice, // 新增: 单价用于结算
      payAmount: quantity * currentPrice, // 新增: 小计金额
      originalPrice: currentPrice,
      originalAmount: quantity * currentPrice,
      totalAmount: quantity * currentPrice,
      details: product.details || '',
      isFree: currentPrice === 0 || product.isFree || false,
      // 套餐相关属性
      isPackage: isPackage,
      isGroup: product.isGroup || false, // 保留isGroup字段
      packageDetail: product.packageDetail || null,
      // 现有的套餐信息字段
      packageProductInfo: product.packageProductInfo || null,
      // 手动添加的商品不是例点
      isInitialOrder: false
    };

    // console.log('[OpenTableConverter] 转换后的结果:', result)
    return result;
  }

  /**
   * 转换账单数据，将原始后端格式转换为前端可用格式
   * @param bill 账单数据
   * @returns 转换后的账单数据
   */
  static convertBill(bill: any) {
    if (!bill) return null;

    // 删除调试日志

    // 创建optionalProducts对象，确保设置selectedQuantity为0
    const optionalProducts = bill.marketBill?.optionalProducts
      ? {
          ...bill.marketBill.optionalProducts,
          selectedQuantity: 0 // 明确设置为0
        }
      : { products: [], selectedQuantity: 0 };

    // 创建optionalFreeProducts对象，确保设置selectedQuantity为0
    const optionalFreeProducts = bill.marketBill?.optionalFreeProducts
      ? {
          ...bill.marketBill.optionalFreeProducts,
          selectedQuantity: 0 // 明确设置为0
        }
      : { products: [], selectedQuantity: 0 };

    // 为所有订单内商品添加isInitialOrder=true标记
    const standardProducts = (bill.marketBill?.standardProducts || []).map((product: any) => ({
      ...product,
      isInitialOrder: true
    }));

    const freeProducts = (bill.marketBill?.freeProducts || []).map((product: any) => ({
      ...product,
      isInitialOrder: true
    }));

    // 给可选组商品也添加标记
    if (optionalProducts.products && optionalProducts.products.length > 0) {
      // 为付费可选组商品补充 currentPrice / price 字段，保证下游计算可用
      optionalProducts.products = optionalProducts.products.map((product: any) => {
        // 如果 currentPrice 为空，则尝试回填为 price 或 originalPrice
        const filledCurrentPrice =
          product.currentPrice !== undefined && product.currentPrice !== null && product.currentPrice > 0
            ? product.currentPrice
            : product.price && product.price > 0
              ? product.price
              : product.originalPrice || 0;

        return {
          ...product,
          // 标记为开台方案商品
          isInitialOrder: true,
          // price 对于付费商品保持不变；若后端未返回则使用 filledCurrentPrice 兜底
          price: product.price !== undefined ? product.price : filledCurrentPrice,
          currentPrice: filledCurrentPrice
        };
      });
    }

    if (optionalFreeProducts.products && optionalFreeProducts.products.length > 0) {
      // 免费可选组：price 保持 0，currentPrice 填充为原价（若后端未提供）
      optionalFreeProducts.products = optionalFreeProducts.products.map((product: any) => {
        const filledCurrentPrice =
          product.currentPrice !== undefined && product.currentPrice !== null && product.currentPrice > 0
            ? product.currentPrice
            : product.originalPrice || product.price || 0;

        return {
          ...product,
          isInitialOrder: true,
          // 免费赠品 price 为 0（后端有就用后端值）
          price: product.price !== undefined ? product.price : 0,
          currentPrice: filledCurrentPrice
        };
      });
    }

    // 创建返回结果
    const result = {
      roombill: {
        details: bill.roombill.details,
        totalPrice: bill.roombill.totalPrice,
        minimumCharge: bill.roombill.minimumCharge,
        isTimeConsume: bill.isTimeConsume
      },
      marketBill: {
        standardProducts: standardProducts,
        freeProducts: freeProducts,
        optionalProducts: optionalProducts,
        optionalFreeProducts: optionalFreeProducts
      },
      consumptionMode: bill.consumptionMode,
      minimumCharge: bill.minimumCharge,
      timeRange: bill.timeRange
    };

    // 删除调试日志结束

    return result;
  }
}
