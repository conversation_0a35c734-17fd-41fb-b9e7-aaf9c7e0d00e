import { BookingApi } from '@/modules/order/api/booking';
import { useStageStore } from '@/stores/stageStore';
import { ElMessage } from 'element-plus';
import { StageVO } from '@/modules/room/types/stageVO';
import { AddBookingParams, UpdateBookingParams, BookingEntity } from './entity';
import { PreTableConverter } from './converter';
import type { BookingVO } from '@/types/projectobj';

/**
 * 预订业务交互层 - 负责处理业务逻辑
 */
export class PreTableInteractor {
  private stageStore = useStageStore();

  /**
   * 获取包厢列表
   * @returns 包厢列表
   */
  getStages(): StageVO[] {
    return this.stageStore.stages;
  }

  /**
   * 根据房间ID获取包厢信息
   * @param roomId 房间ID
   * @returns 包厢信息
   */
  getStageByRoomId(roomId: string): StageVO | undefined {
    return this.stageStore.getStageByRoomId(roomId);
  }

  /**
   * 更新包厢信息
   * @param stage 包厢信息
   */
  updateStageInfo(stage: StageVO): void {
    this.stageStore.updateStageInfo(stage);
  }

  /**
   * 添加预订
   * @param params 添加预订参数
   * @returns 预订ID
   */
  async addBooking(params: AddBookingParams): Promise<string> {
    try {
      const res = await BookingApi.addBooking(params);

      if (res.code === 0) {
        return res.data.id;
      } else {
        throw new Error(res.message || '预订失败');
      }
    } catch (error) {
      console.error('添加预订失败:', error);
      throw error;
    }
  }

  /**
   * 更新预订
   * @param params 更新预订参数
   */
  async updateBooking(params: UpdateBookingParams): Promise<void> {
    try {
      const res = await BookingApi.updateBooking(params);

      if (res.code !== 0) {
        throw new Error(res.message || '修改失败');
      }
    } catch (error) {
      console.error('更新预订失败:', error);
      throw error;
    }
  }

  /**
   * 根据预订ID查找预订所在的包厢
   * @param bookingId 预订ID
   * @returns 包厢信息
   */
  private findStageByBookingId(bookingId: string): StageVO | undefined {
    const stages = this.getStages();
    return stages.find(stage => stage.bookingVOs && stage.bookingVOs.some(booking => booking.id === bookingId));
  }

  /**
   * 保存预订
   * @param params 预订参数
   * @param isEditMode 是否为编辑模式
   * @param bookingId 预订ID (仅编辑模式需要)
   * @returns 是否保存成功
   */
  async saveBooking(params: AddBookingParams | UpdateBookingParams, isEditMode: boolean, bookingId?: string): Promise<boolean> {
    try {
      let resultId: string;
      console.log('[saveBooking] params:', params);

      // 获取目标包厢信息（在API调用前获取，确保roomName正确）
      const targetStage = this.getStageByRoomId(params.roomId);
      if (!targetStage) {
        console.error('[saveBooking] 未找到目标包厢:', params.roomId);
        return false;
      }

      if (isEditMode && bookingId) {
        // 编辑模式：构建更新参数，包含新的roomName
        const updateParams: UpdateBookingParams = {
          ...(params as UpdateBookingParams),
          roomName: targetStage.roomVO.name // 使用新包厢的名称
        };
        await this.updateBooking(updateParams);
        resultId = bookingId;
      } else {
        resultId = await this.addBooking(params as AddBookingParams);
      }

      // 创建新的预订实体
      const newBooking = PreTableConverter.createBookingEntity(params, resultId, targetStage.roomVO.name);

      if (isEditMode && bookingId) {
        // 编辑模式：查找原预订所在的包厢
        const originalStage = this.findStageByBookingId(bookingId);
        console.log('[saveBooking] originalStage:', originalStage);

        if (originalStage && originalStage.roomVO.id !== params.roomId) {
          // 房间发生变化：先从原房间移除，再在新房间添加
          console.log('[saveBooking] 房间发生变化，从', originalStage.roomVO.name, '移动到', targetStage.roomVO.name);

          // 从原房间移除预订
          const updatedOriginalStage: StageVO = {
            ...originalStage,
            bookingVOs: (originalStage.bookingVOs || []).filter(b => b.id !== bookingId)
          };
          this.updateStageInfo(updatedOriginalStage);

          // 在新房间添加预订
          const updatedTargetStage: StageVO = {
            ...targetStage,
            bookingVOs: [...(targetStage.bookingVOs || []), newBooking]
          };
          this.updateStageInfo(updatedTargetStage);
        } else {
          // 房间未变化：直接更新预订
          console.log('[saveBooking] 房间未变化，直接更新预订');
          const updatedStage: StageVO = {
            ...targetStage,
            bookingVOs: (targetStage.bookingVOs || []).map(b => (b.id === bookingId ? newBooking : b))
          };
          this.updateStageInfo(updatedStage);
        }
      } else {
        // 新增模式：直接添加到目标房间
        console.log('[saveBooking] 新增模式，添加到房间:', targetStage.roomVO.name);
        const updatedStage: StageVO = {
          ...targetStage,
          bookingVOs: [...(targetStage.bookingVOs || []), newBooking]
        };
        this.updateStageInfo(updatedStage);
      }

      ElMessage.success(isEditMode ? '修改成功' : '预订成功');
      return true;
    } catch (error) {
      console.error('[saveBooking] error:', error);
      const errorMsg = error instanceof Error ? error.message : '操作失败';
      ElMessage.error(errorMsg);
      return false;
    }
  }

  /**
   * 表单验证
   * @param customerName 客人姓名
   * @param phone 电话号码
   * @param arrivalDateTime 预抵时间
   * @param selectedStages 选择的包厢
   * @returns 验证结果
   */
  validateForm(customerName: string, phone: string, arrivalDateTime: string, selectedStages: string[]): boolean {
    // 表单验证
    if (!customerName && !phone) {
      ElMessage.warning('请输入客人姓名或手机号码');
      return false;
    }
    if (!arrivalDateTime) {
      ElMessage.warning('请选择预抵时间');
      return false;
    }
    if (selectedStages.length === 0) {
      ElMessage.warning('请选择包厢');
      return false;
    }
    return true;
  }

  /**
   * 根据ID获取预订详情
   * @param bookingId 预订ID
   * @returns 预订实体
   */
  async getBookingById(bookingId: string): Promise<BookingEntity | null> {
    try {
      // 首先尝试从本地缓存获取
      let booking: BookingVO | undefined;

      // 遍历所有stage寻找booking
      for (const stage of this.getStages()) {
        if (stage.bookingVOs) {
          booking = stage.bookingVOs.find(b => b.id === bookingId);
          if (booking) break;
        }
      }

      // 如果本地没有找到，尝试从API获取
      if (!booking) {
        // 使用查询API，查询指定ID的预订
        const response = await BookingApi.queryBookings({
          id: bookingId,
          pageNum: 1,
          pageSize: 1
        });

        if (response.code === 0 && response.data && response.data.length > 0) {
          booking = response.data[0];
        }
      }

      // 如果找到预订，将其转换为实体
      if (booking) {
        return {
          id: booking.id,
          venueId: booking.venueId || '',
          roomId: booking.roomId || '',
          roomName: booking.roomName || '',
          customerName: booking.customerName,
          customerPhone: booking.customerPhone,
          customerSource: booking.customerSource,
          gender: booking.gender,
          memberCard: booking.memberCard || '',
          memberCardId: booking.memberCardId || '',
          openTablePlan: booking.openTablePlan || '',
          remark: booking.remark || '',
          arrivalTime: booking.arrivalTime,
          ctime: booking.ctime,
          utime: booking.utime || 0,
          state: booking.state || 0,
          status: booking.status || 0,
          version: booking.version || 0
        };
      }

      console.warn(`未找到ID为 ${bookingId} 的预订`);
      return null;
    } catch (error) {
      console.error('获取预订详情失败:', error);
      ElMessage.error(`获取预订详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return null;
    }
  }

  /**
   * 显示错误消息
   * @param message 错误消息
   */
  showErrorMessage(message: string): void {
    ElMessage.error(message);
  }
}
