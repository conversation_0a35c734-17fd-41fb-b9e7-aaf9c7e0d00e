import { ref, reactive, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { IPreTableViewModel, IPreTableFormState, IPreTableState, IPreTableActions, IPreTableComputed, IPreTableDialogResult } from './viewmodel';
import { PreTableInteractor } from './interactor';
import { PreTableConverter } from './converter';
import { StageVO } from '@/modules/room/types/stageVO';
import { ExtendedStageVO } from '@/modules/room/types/extendedStageVO';
import { useStageStore } from '@/stores/stageStore';

/**
 * 预订Presenter - 负责视图状态管理和事件处理
 */
export class PreTablePresenter implements IPreTableViewModel {
  // 私有成员
  private router = useRouter();
  private route = useRoute();
  private interactor = new PreTableInteractor();
  private emit: any = null;
  private stageStore = useStageStore();

  // 实现表单状态
  public formState: IPreTableFormState = reactive({
    customerName: '',
    gender: '0',
    phone: '',
    memberCard: '',
    customerSource: '',
    arrivalDateTime: '',
    arrivalTime: '',
    billingPlan: 'hourly',
    remark: ''
  });

  // 实现附加状态
  public state: IPreTableState = reactive({
    customerSources: [
      { label: '线上预订', value: 'online' },
      { label: '电话预订', value: 'phone' },
      { label: '现场预订', value: 'onsite' }
    ],
    remarkTags: ['老客到店', '新客到店', '企业团建', '生日客户'],
    selectedStages: [],
    stages: [],
    isEditMode: false,
    bookingId: '',
    // 快捷选项
    dateTimeShortcuts: [
      {
        text: '今天',
        value: () => {
          return new Date();
        }
      },
      {
        text: '明天',
        value: () => {
          const date = new Date();
          date.setDate(date.getDate() + 1);
          return date;
        }
      },
      {
        text: '后天',
        value: () => {
          const date = new Date();
          date.setDate(date.getDate() + 2);
          return date;
        }
      }
    ],
    // 默认时间设置为当天12:00
    defaultTime: new Date(2000, 1, 1, 12, 0, 0),
    // 对话框可见性
    visible: true,
    // 包厢搜索和筛选
    searchText: '',
    filterStatus: 'default'
  });

  // 实现计算属性
  public computed: IPreTableComputed = {
    // 是否可以保存
    canSave: computed(() => {
      return (!!this.formState.customerName || !!this.formState.phone) && !!this.formState.arrivalDateTime && this.state.selectedStages.length > 0;
    }),

    // 过滤后的包厢列表 - 保留原实现但进行安全增强
    filteredStages: computed(() => {
      console.log('计算 filteredStages，源数据长度:', this.state.stages.length);

      if (!this.state.stages || this.state.stages.length === 0) {
        console.warn('警告: stages 数据为空');
        return [];
      }

      let result = [...this.state.stages];

      // 在过滤前先验证每个 stage 对象是否完整
      result = result.filter(stage => {
        // 确保 stage 不为空并且有 roomVO 属性
        if (!stage || !stage.roomVO) {
          console.warn('警告: 发现不完整的 stage 对象', stage);
          return false;
        }

        // 确保 roomVO 有 id 属性
        if (!stage.roomVO.id) {
          console.warn('警告: 发现没有 id 的 roomVO', stage.roomVO);
          return false;
        }

        return true;
      });

      // 文本搜索过滤
      if (this.state.searchText) {
        const searchText = this.state.searchText.toLowerCase();
        result = result.filter(stage => {
          // 这里我们已经确保了 stage.roomVO 一定存在
          const roomName = stage.roomVO.name || '';
          const typeName = stage.roomVO.roomTypeVO?.name || '';

          return roomName.toLowerCase().includes(searchText) || typeName.toLowerCase().includes(searchText);
        });
      }

      // 状态过滤
      if (this.state.filterStatus && this.state.filterStatus !== 'default') {
        if (this.state.filterStatus === 'timeout') {
          // 超时筛选
          result = result.filter(stage => this.isRoomTimeout(stage));
        } else {
          // 根据房间状态筛选
          result = result.filter(stage => stage.roomVO.status === this.state.filterStatus);
        }
      }

      console.log('过滤后的包厢列表:', result.length, '所有数据都有效');
      return result;
    }),

    // 当前选中包厢的名称
    selectedRoomName: computed(() => {
      if (this.state.selectedStages.length === 0) {
        return '';
      }

      const roomId = this.state.selectedStages[0];
      const selectedRoom = this.state.stages.find(stage => stage?.roomVO?.id === roomId);

      return selectedRoom?.roomVO?.name || '';
    })
  };

  // 实现行为
  public actions: IPreTableActions = {
    // 切换包厢选择
    toggleStage: (roomId: string) => {
      if (this.state.selectedStages.includes(roomId)) {
        this.state.selectedStages = [];
      } else {
        this.state.selectedStages = [roomId];
      }
    },

    // 添加备注标签
    addRemarkTag: (tag: string) => {
      this.formState.remark = this.formState.remark ? `${this.formState.remark} ${tag}` : tag;
    },

    // 保存预订
    handleSave: async () => {
      // 在保存前合并日期和时间
      if (this.formState.arrivalDateTime && this.formState.arrivalTime) {
        this.formState.arrivalDateTime = this.combineDateAndTime(this.formState.arrivalDateTime, this.formState.arrivalTime);
        console.log('合并后的预抵时间:', this.formState.arrivalDateTime);
      }

      // 验证预抵时间不能早于当前时间
      const now = new Date();
      const arrivalTime = this.formState.arrivalDateTime ? new Date(this.formState.arrivalDateTime) : null;

      if (arrivalTime && arrivalTime.getTime() < now.getTime()) {
        // 显示错误消息
        this.interactor.showErrorMessage('预抵时间不能早于当前时间');
        return;
      }

      // 验证表单
      if (!this.interactor.validateForm(this.formState.customerName, this.formState.phone, this.formState.arrivalDateTime, this.state.selectedStages)) {
        return;
      }

      try {
        const roomId = this.state.selectedStages[0];
        let params;

        if (this.state.isEditMode) {
          params = PreTableConverter.viewModelToUpdateParams(this.formState, roomId, this.state.bookingId);
        } else {
          params = PreTableConverter.viewModelToAddParams(this.formState, roomId);
        }

        const success = await this.interactor.saveBooking(params, this.state.isEditMode, this.state.bookingId);

        if (success) {
          // 对话框模式下，通过确认事件返回结果
          if (this.emit) {
            this.actions.handleConfirm();
          } else {
            this.router.back();
          }
        }
      } catch (error) {
        console.error('保存失败:', error);
      }
    },

    // 返回上级
    handleBack: () => {
      if (this.emit) {
        this.actions.handleCancel();
      } else {
        this.router.back();
      }
    },

    // 查找会员卡
    searchMemberCard: () => {
      // TODO: 实现会员卡查询功能
      console.log('搜索会员卡:', this.formState.memberCard);
    },

    // 确认对话框
    handleConfirm: () => {
      if (this.emit) {
        // 创建对话框结果
        const result: IPreTableDialogResult = {
          success: true,
          bookingData: {
            roomId: this.state.selectedStages[0],
            formData: { ...this.formState },
            bookingId: this.state.bookingId
          }
        };
        // 触发确认事件
        this.emit('confirm', result);
        this.state.visible = false;
      }
    },

    // 取消对话框
    handleCancel: () => {
      if (this.emit) {
        this.emit('cancel');
        this.state.visible = false;
      }
    }
  };

  /**
   * 设置事件发射器
   * @param emitter 事件发射器
   */
  public setEmitter(emitter: any): void {
    this.emit = emitter;
  }

  /**
   * 合并日期和时间
   * @param date 日期字符串 YYYY-MM-DD
   * @param time 时间字符串 HH:mm
   * @returns 合并后的日期时间字符串
   */
  private combineDateAndTime(date: string, time: string): string {
    if (!date) return '';
    if (!time) return date;

    return `${date} ${time}`;
  }

  /**
   * 初始化数据
   */
  public async initialize(dialogData?: any): Promise<void> {
    try {
      // 尝试从 stageStore 获取数据
      console.log('从 stageStore 获取包厢数据');
      if (this.stageStore.stages && this.stageStore.stages.length > 0) {
        console.log('从 stageStore 获取到数据，长度:', this.stageStore.stages.length);

        // 对获取到的数据进行验证，确保每个 stage 都有完整的 roomVO 属性
        const validStages = this.stageStore.stages.filter(stage => {
          const isValid = stage && stage.roomVO && stage.roomVO.id;
          if (!isValid) {
            console.warn('发现无效的 stage 数据:', stage);
          }
          return isValid;
        });

        console.log(`数据验证后，有效数据长度: ${validStages.length}/${this.stageStore.stages.length}`);
        this.state.stages = validStages;
      } else {
        console.log('stageStore 中没有数据，尝试通过 interactor 获取');
        // 如果 stageStore 中没有数据，则通过 interactor 获取
        this.state.stages = this.interactor.getStages();
        console.log('通过 interactor 获取到数据长度:', this.state.stages.length);
      }

      // 最后再次确认数据是否有效
      if (!this.state.stages || this.state.stages.length === 0) {
        console.error('严重错误: 所有数据源都无法提供有效数据');
        // 至少提供一个空数组，避免渲染错误
        this.state.stages = [];
      }
    } catch (error) {
      console.error('获取包厢数据出错:', error);
      // 加载测试数据作为备选
    }

    let roomId, mode, bookingId;

    // 直接从 dialogData 获取参数，更明确输出调试信息
    console.log('传入的 dialogData:', dialogData);
    console.log('dialogData 类型:', typeof dialogData);
    console.log('dialogData 包含的属性:', dialogData ? Object.keys(dialogData) : '无');

    // 区分对话框模式和页面模式
    if (dialogData && typeof dialogData === 'object') {
      // 对话框模式，从传入的参数获取数据
      roomId = dialogData.roomId;
      mode = dialogData.mode;
      bookingId = dialogData.bookingId;
      console.log('从 dialogData 提取的参数: roomId =', roomId, ', mode =', mode, ', bookingId =', bookingId);
    } else {
      // 页面模式，从路由获取参数
      const routeQuery = this.route.query;
      roomId = routeQuery.roomId;
      mode = routeQuery.mode;
      bookingId = routeQuery.bookingId;
      console.log('从路由提取的参数: roomId =', roomId, ', mode =', mode, ', bookingId =', bookingId);
    }

    console.log('处理参数：roomId =', roomId, ', mode =', mode, ', bookingId =', bookingId);

    if (roomId) {
      this.state.selectedStages = [roomId as string];
      console.log('已选择包厢:', this.state.selectedStages);

      // 如果有选中的包厢，可以尝试设置当前包厢信息
      const selectedStage = this.state.stages.find(stage => stage.roomVO?.id === roomId);
      if (selectedStage) {
        console.log('找到选中的包厢:', selectedStage.roomVO?.name);
      } else {
        console.warn('未找到ID为', roomId, '的包厢');
      }
    }

    // 如果是编辑模式
    if (mode === 'edit') {
      try {
        this.state.isEditMode = true;

        // 如果提供了 bookingId，直接使用
        if (bookingId) {
          this.state.bookingId = bookingId as string;

          // 通过 interactor 获取预订详情
          const bookingData = await this.interactor.getBookingById(this.state.bookingId);
          if (bookingData) {
            // 使用转换器将实体数据转换为表单状态
            Object.assign(this.formState, PreTableConverter.entityToViewModel(bookingData));
            console.log('通过ID加载编辑模式数据成功');

            // 如果没有 roomId，从booking数据中获取
            if (!roomId && bookingData.roomId) {
              this.state.selectedStages = [bookingData.roomId];
              console.log('从预订数据中设置房间ID:', bookingData.roomId);
            }
          }
        }
        // 如果没有 bookingId 但有 roomId，从 stage 中获取预订信息
        else if (roomId) {
          const selectedStage = this.interactor.getStageByRoomId(roomId as string);
          if (selectedStage?.bookingVOs?.[0]) {
            const booking = selectedStage.bookingVOs[0];
            this.state.bookingId = booking.id;

            // 使用转换器将实体数据转换为表单状态
            Object.assign(this.formState, PreTableConverter.entityToViewModel(booking));
            console.log('从Stage加载编辑模式数据成功');
          }
        }
      } catch (error) {
        console.error('加载编辑模式数据出错:', error);
      }
    }

    // 设置默认时间为当前时间的下一个整点
    if (!this.formState.arrivalTime) {
      const now = new Date();
      now.setHours(now.getHours() + 1);
      now.setMinutes(0);
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      this.formState.arrivalTime = `${hours}:${minutes}`;
      console.log('设置默认时间:', this.formState.arrivalTime);
    }

    // 如果有预抵时间，正确设置时间部分
    if (this.formState.arrivalDateTime) {
      try {
        // 检查arrivalDateTime是否包含时间部分
        const fullDate = new Date(this.formState.arrivalDateTime);
        if (!isNaN(fullDate.getTime())) {
          // 如果是有效的日期时间，提取时间部分
          const hours = fullDate.getHours().toString().padStart(2, '0');
          const minutes = fullDate.getMinutes().toString().padStart(2, '0');
          // 只有当没有明确设置arrivalTime时才设置
          if (!this.formState.arrivalTime) {
            this.formState.arrivalTime = `${hours}:${minutes}`;
            console.log('从日期时间提取时间部分:', this.formState.arrivalTime);
          }
        }
      } catch (err) {
        console.error('解析预抵时间出错:', err);
      }
    }

    console.log('初始化 PreTable 完成, 当前表单状态:', {
      arrivalDateTime: this.formState.arrivalDateTime,
      arrivalTime: this.formState.arrivalTime
    });
  }

  /**
   * 判断包厢是否超时
   * @param stage 包厢数据
   * @returns 是否超时
   */
  public isRoomTimeout(stage: ExtendedStageVO): boolean {
    // 安全检查
    if (!stage) {
      console.warn('isRoomTimeout: stage 参数为空');
      return false;
    }

    // 简化判断逻辑，直接使用服务端提供的标记
    try {
      // 1. 优先使用直接的超时标记
      if ((stage as any)?.isTimeout === true) {
        return true;
      }

      // 2. 检查session的超时标记
      if (stage.sessionVO) {
        if ((stage.sessionVO as any)?.isTimeout === true) {
          return true;
        }
      }

      // 3. 兼容之前的判断方式
      const sessionTags = (stage as any)?.sessionTags;
      if (Array.isArray(sessionTags) && sessionTags.includes('timeout')) {
        return true;
      }

      if ((stage as any)?.timeoutFlag) {
        return true;
      }
    } catch (err) {
      console.error('检查超时状态出错:', err);
    }

    // 默认不超时
    return false;
  }
}

/**
 * 导出组合式函数
 * @returns 预订视图模型
 */
export function usePreTable(): IPreTableViewModel & {
  initialize: (data?: any) => Promise<void>;
  setEmitter: (emitter: any) => void;
  isRoomTimeout: (stage: StageVO) => boolean;
} {
  const presenter = new PreTablePresenter();
  return {
    ...presenter,
    initialize: presenter.initialize.bind(presenter),
    setEmitter: presenter.setEmitter.bind(presenter),
    isRoomTimeout: presenter.isRoomTimeout.bind(presenter)
  };
}
