import { BookingVO as ProjectBookingVO } from '@/types/projectobj';

/**
 * 预订实体数据模型
 */
export interface BookingEntity {
  /** 预订ID */
  id: string;
  /** 场馆ID */
  venueId: string;
  /** 房间ID */
  roomId: string;
  /** 房间名称 */
  roomName: string;
  /** 客户名称 */
  customerName: string;
  /** 客户电话 */
  customerPhone: string;
  /** 客户来源 */
  customerSource: string;
  /** 性别 0-男 1-女 */
  gender: string;
  /** 会员卡号 */
  memberCard: string;
  /** 会员卡ID */
  memberCardId: string;
  /** 开台方案 */
  openTablePlan: string;
  /** 备注 */
  remark: string;
  /** 预抵时间(Unix时间戳) */
  arrivalTime: number;
  /** 创建时间 */
  ctime: number;
  /** 更新时间 */
  utime: number;
  /** 状态 */
  state: number;
  /** 订单状态 0-未使用 1-已使用 2-已取消 */
  status: number;
  /** 版本号 */
  version: number;
}

/**
 * 添加预订请求参数
 */
export interface AddBookingParams {
  /** 场馆ID */
  venueId: string;
  /** 房间ID */
  roomId: string;
  /** 客户名称 */
  customerName: string;
  /** 客户电话 */
  customerPhone: string;
  /** 客户来源 */
  customerSource: string;
  /** 性别 0-男 1-女 */
  gender: string;
  /** 会员卡号 */
  memberCard?: string;
  /** 会员卡ID */
  memberCardId?: string;
  /** 开台方案 */
  openTablePlan?: string;
  /** 备注 */
  remark?: string;
  /** 预抵时间(Unix时间戳) */
  arrivalTime: number;
}

/**
 * 更新预订请求参数
 */
export interface UpdateBookingParams extends AddBookingParams {
  /** 预订ID */
  id: string;
  /** 房间名称 */
  roomName?: string;
}

/**
 * API响应数据类型
 */
export interface ApiResponse<T> {
  /** 状态码 */
  code: number;
  /** 响应数据 */
  data: T;
  /** 错误信息 */
  error?: string;
  /** 提示信息 */
  message?: string;
}

// 确保与项目中的BookingVO类型兼容
export type BookingVO = ProjectBookingVO;
