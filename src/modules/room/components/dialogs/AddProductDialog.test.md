# AddProductDialog 数量调整问题修复

## 问题描述

1. **数量为0时商品仍然显示**：当商品数量调整为0时，商品应该从购物车中移除，但之前的逻辑存在问题
2. **多行商品数量调整相互影响**：多个商品的数量调整可能会相互影响，不符合预期

## 修复内容

### 1. 优化数量变更处理逻辑

- 修改 `handleQuantityChange` 方法，确保数量为0或负数时直接从购物车移除商品
- 添加数据验证，防止无效输入
- 添加重复更新检查，避免不必要的操作

### 2. 添加购物车数据过滤

- 在 `processedCartItems` 的 watch 中添加过滤逻辑，只显示数量大于0的商品
- 确保明细行也只为有效商品生成

### 3. 添加自动清理机制

- 创建 `cleanupCartItems` 函数，自动清理数量为0的商品
- 添加购物车变化监听，自动触发清理

### 4. 优化用户交互

- 允许数量输入框的最小值为0，符合购物车删除商品的交互逻辑
- 添加防抖机制，避免频繁的数量变更事件
- 为每个输入框添加唯一key，确保组件独立性

## 测试步骤

### 测试场景1：数量为0时商品移除

1. 打开商品点单弹窗
2. 添加多个商品到购物车
3. 将其中一个商品的数量调整为0
4. **预期结果**：该商品应该从购物车中消失

### 测试场景2：多商品数量调整独立性

1. 添加至少2个不同商品到购物车
2. 调整第一个商品的数量
3. 观察第二个商品的数量是否受影响
4. **预期结果**：各商品数量调整应该相互独立

### 测试场景3：数量输入验证

1. 尝试在数量输入框中输入0
2. **预期结果**：商品应该从购物车中移除
3. 尝试输入负数
4. **预期结果**：数量会被设置为0，然后商品被移除

### 测试场景4：购物车状态一致性

1. 进行多次数量调整操作
2. 检查购物车中是否存在数量为0的商品
3. **预期结果**：购物车中不应该有数量为0的商品

## 技术实现要点

1. **数据一致性**：确保 `vm.state.cartItems` 和 `processedCartItems` 的数据一致
2. **事件防抖**：使用 lodash debounce 避免频繁触发数量变更事件
3. **组件隔离**：为每个数量输入框添加唯一key，确保组件状态独立
4. **自动清理**：通过 watch 监听和手动清理双重保障，确保无效数据被及时清理

## 代码变更总结

- 修改 `handleQuantityChange` 方法逻辑
- 添加 `cleanupCartItems` 清理函数
- 添加 `debouncedQuantityChange` 防抖函数
- 优化购物车数据过滤和监听
- 更新模板中的事件绑定和组件配置
