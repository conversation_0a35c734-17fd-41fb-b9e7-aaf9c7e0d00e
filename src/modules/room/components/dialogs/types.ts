/**
 * AddProductDialog 组件的类型定义
 * 明确定义所有数据结构，避免类型模糊和兜底处理
 */

// ==================== API 返回的原始数据类型 ====================

/**
 * API返回的商品数据结构 (ProductVO)
 */
export interface ApiProductVO {
  id: string;
  name: string;
  currentPrice: number; // 以分为单位
  unit: string;
  flavors?: string;
  isSoldOut: boolean;
  isPromotion?: boolean;
  calculateInventory?: boolean;
  lowStockThreshold?: number;
}

/**
 * API返回的套餐数据结构 (ProductPackageVO)
 */
export interface ApiProductPackageVO {
  id: string;
  name: string;
  currentPrice: number; // 以分为单位
  isSoldOut: boolean;
  isPromotion?: boolean;
  // 套餐商品列表 - JSON字符串
  packageProducts?: string;
  // 可选组列表 - JSON字符串
  optionalGroups?: string;
  // 套餐内商品详情
  productVOList?: ApiProductVO[];
}

/**
 * API返回的查询结果结构 (ProductOrPackageRVO)
 */
export interface ApiProductOrPackageRVO {
  productVOs?: ApiProductVO[];
  productPackageVOs?: ApiProductPackageVO[];
}

// ==================== 基础购物车项类型 ====================

/**
 * 基础购物车项目类型
 */
export interface ICartItem {
  id: string;
  name: string;
  currentPrice: number;
  quantity: number;
  flavors?: string;
  unit: string;
  isPackage: boolean;
  // 新增：是否有可选组
  hasOptionalGroups?: boolean;
  // 新增：可选组套餐的唯一标识
  packageUniqueId?: string;
  packageDetail?: {
    packageProducts: string; // JSON字符串
    optionalGroups?: string; // JSON字符串，可选
    productVOList: any[]; // 商品列表
    detailString: string; // 套餐明细展示字符串
    // 🔥 新增：结构化的已选商品列表（用于动态计算明细）
    selectedProducts?: Array<{
      id: string;
      name: string;
      count: number;
      currentPrice?: number;
    }>;
  };
}

// ==================== 套餐相关类型 ====================

/**
 * 套餐内商品项
 */
export interface PackageProductItem {
  id: string;
  name: string;
  quantity: number;
  unit?: string;
  currentPrice?: number;
}

/**
 * 套餐明细信息
 */
export interface PackageDetail {
  // 套餐商品列表的JSON字符串
  packageProducts: string;
  // 可选组的JSON字符串（可选）
  optionalGroups?: string;
  // 套餐内商品详情
  productVOList: ApiProductVO[];
  // 格式化后的明细展示字符串
  detailString: string;
  // 🔥 新增：结构化的已选商品列表（用于动态计算明细）
  selectedProducts?: Array<{
    id: string;
    name: string;
    count: number;
    currentPrice?: number;
  }>;
}

// ==================== 可选组相关类型 ====================

/**
 * 可选组中的商品项
 */
export interface OptionalProductItem {
  id: string;
  name: string;
  quantity: number;
  unit?: string;
  currentPrice?: number;
}

/**
 * 可选组类型枚举
 */
export type OptionType = 'ByCount' | 'ByPlan' | 'None';

// ==================== 购物车相关类型 ====================

/**
 * 扩展的购物车项目类型
 * 基于标准ICartItem，添加可选组和明细行支持
 */
export interface ExtendedCartItem extends ICartItem {
  // ========== 明细行相关 ==========
  /** 是否是明细行 */
  isDetail?: boolean;
  /** 父项ID（明细行专用） */
  parentId?: string;

  // ========== 可选组相关 ==========
  /** 是否是可选组商品 */
  isOptionalGroup?: boolean;
  /** 可选组中的商品列表 */
  optionalProducts?: OptionalProductItem[];
  /** 可选组类型 */
  optionType?: OptionType;
  /** 可选组商品数量限制 */
  optionCount?: number;
  /** 已选择的商品数量 */
  selectedQuantity?: number;
  /** 是否是免费商品 */
  isFree?: boolean;
  /** 明细文本（用于显示已选择的商品） */
  details?: string;
}

// ==================== 商品列表相关类型 ====================

/**
 * 商品分类
 */
export interface ProductCategory {
  id: string | null;
  name: string;
  count: string | number;
  isActive: boolean;
  type: 'all' | 'package' | 'product';
  isPackage: boolean;
}

/**
 * 页面显示的商品项
 */
export interface DisplayProduct {
  id: string;
  name: string;
  stock: number;
  tag: string;
  currentPrice: number; // 以分为单位
  unit: string;
  isPackage: boolean;
  isSoldOut: boolean;
  quantity: number; // 用户输入的数量
  // 套餐相关
  packageProducts?: PackageProductItem[];
  // 可选组相关
  optionalGroups?: any[];
  productVOList?: ApiProductVO[];
  hasOptionalGroups?: boolean;
}

// ==================== 数据转换相关类型 ====================

/**
 * 商品转换为购物车项的参数
 */
export interface ProductToCartItemParams {
  id: string;
  name?: string;
  productName?: string;
  currentPrice?: number;
  price?: number;
  quantity: number;
  flavors?: string;
  unit?: string;
  isPackage?: boolean;
  // 套餐相关
  packageProducts?: PackageProductItem[];
  packageDetail?: any;
  // 可选组相关
  isOptionalGroup?: boolean;
  optionalProducts?: OptionalProductItem[];
  optionType?: string;
  optionCount?: number;
  selectedQuantity?: number;
  isFree?: boolean;
}

// ==================== 组件Props类型 ====================

/**
 * AddProductDialog组件的Props
 */
export interface AddProductDialogProps {
  modelValue: boolean;
  roomId?: string;
  roomName?: string;
  areaId?: string;
  areaName?: string;
  sessionId?: string;
  outOrderProducts?: any[];
  mode?: 'normal' | 'gift';
  operator?: string;
  venueId?: string;
  memberId?: string;
}

/**
 * AddProductDialog组件的Emits
 */
export interface AddProductDialogEmits {
  'update:modelValue': (value: boolean) => void;
  confirm: (cartItems: ExtendedCartItem[]) => void;
  cancel: () => void;
  'gift-success': (data: any) => void;
}

// ==================== 明细行相关类型 ====================

/**
 * PackageDetailItem组件的Props
 */
export interface PackageDetailItemProps {
  row: ExtendedCartItem;
  isDetail: boolean;
  parentId?: string;
  parent?: ICartItem;
}

// ==================== 工具函数返回类型 ====================

/**
 * 数据转换结果
 */
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * 套餐明细解析结果
 */
export interface PackageDetailParseResult {
  success: boolean;
  detailString?: string;
  items?: PackageProductItem[];
  error?: string;
}

/**
 * 可选组明细解析结果
 */
export interface OptionalGroupDetailParseResult {
  success: boolean;
  detailString?: string;
  selectedProducts?: OptionalProductItem[];
  selectedCount?: number;
  error?: string;
}
