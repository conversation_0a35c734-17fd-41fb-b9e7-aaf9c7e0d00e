<template>
  <div v-if="isDetail" class="package-detail-expanded-item flex items-center">
    <!-- 套餐明细逻辑 -->
    <template v-if="parentId && parent">
      <div class="w-full px-0">
        <div class="flex flex-col">
          <div class="flex items-center">
            <span class="text-gray-600">{{ row.productName }}</span>
            <span v-if="row.isFree" class="text-xs text-green-500 ml-1">(赠品)</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="!parentId">
      <!-- 未找到父项ID的情况 -->
      <div class="w-full px-0 text-gray-600">
        {{ compactDetailsText }}
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import type { ProductEntity } from '../entity/OpenTableEntity';

interface DetailItem {
  name?: string;
  productName?: string;
  quantity?: number | string;
  [key: string]: any;
}

const props = defineProps({
  // 行数据
  row: {
    type: Object,
    required: true
  },
  // 是否为详情行
  isDetail: {
    type: Boolean,
    default: false
  },
  // 父项ID
  parentId: {
    type: String,
    default: ''
  },
  // 父项数据
  parent: {
    type: Object as () => ProductEntity | undefined,
    default: undefined
  }
});

// 获取紧凑的详情文本（类似于"新动啤酒x24，时令水果x1"的格式）
const compactDetailsText = computed(() => {
  if (!props.row || !props.row.productName) return '';

  try {
    // 如果是传递整个套餐明细，直接显示
    if (props.row.packageDetail) {
      let detailText = '';
      if (typeof props.row.packageDetail === 'string') {
        try {
          const detail = JSON.parse(props.row.packageDetail);
          detailText = detail.detailString || '';
        } catch (e) {
          detailText = props.row.packageDetail;
        }
      } else if (typeof props.row.packageDetail === 'object') {
        detailText = props.row.packageDetail.detailString || '';
      }
      return detailText;
    }

    return props.row.productName;
  } catch (error) {
    console.error('解析套餐详情失败:', error);
    return '套餐详情';
  }
});

// 获取套餐明细显示文本
const getPackageDetailsText = (): string => {
  if (!props.parent?.packageDetail) return '';

  // 尝试获取套餐详情字符串
  if (typeof props.parent.packageDetail === 'string') {
    try {
      const detail = JSON.parse(props.parent.packageDetail);
      return detail.detailString || '';
    } catch (e) {
      return props.parent.packageDetail as string;
    }
  }

  // 如果是对象，直接获取detailString
  if (typeof props.parent.packageDetail === 'object') {
    return props.parent.packageDetail.detailString || '';
  }

  return '';
};
</script>

<style lang="scss" scoped>
.package-detail-expanded-item {
  background-color: #f9f9f9;
  min-height: 32px;
  width: 100%;
  padding-left: 20px;
  display: flex;
  align-items: center;
}
</style>
