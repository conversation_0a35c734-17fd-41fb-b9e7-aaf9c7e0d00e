<template>
  <AppDialog
    class="!w-[1080px]"
    :show-header="false"
    :ui-type="DialogUIType.CARD"
    v-model="dialogVisible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    @close="handleCancel">
    <!-- 操作说明 -->

    <div class="flex h-full">
      <!-- 源包厢信息 -->
      <div class="w-1/2">
        <div class="flex items-center pt-[24px] ml-[32px] mb-[24px]">
          <h1 class="text-[#E9223A] text-[28px]">{{ dialogTitle }}</h1>
          <el-tooltip effect="light" :content="operationDescription" placement="top">
            <el-icon class="text-gray-400 cursor-pointer text-[20px] ml-[8px]">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <div class="flex flex-col items-center">
          <h3 class="mb-4 text-[16px] text-[#999]">原包厢</h3>
          <div class="flex items-center justify-between w-[400px] h-[64px] bg-gray-100 rounded-md p-[24px] mb-[20px]">
            <span>原包厢</span>
            <span>{{ sourceStage?.roomVO?.name }}</span>
          </div>
          <div class="flex items-center justify-between w-[354px] mb-[20px]">
            <span class="text-[#4C4C4C] text-[16px]">包厢类型</span>
            <span class="text-[#000] font-bold text-[16px]">{{ sourceStage?.roomTypeVO?.name }}</span>
          </div>
          <div class="flex items-center justify-between w-[354px] mb-[20px]">
            <span class="text-[#4C4C4C] text-[16px]">包厢状态</span>
            <span class="text-[#000] font-bold text-[16px]">{{ getCombinedStatusName(sourceStage) }}</span>
          </div>
          <div class="flex items-center justify-between w-[354px] mb-[20px]">
            <span class="text-[#4C4C4C] text-[16px]">开台时间</span>
            <span class="text-[#000] font-bold text-[16px]">{{ formatUnixTimestamp(sourceStage?.sessionVO?.startTime) }}</span>
          </div>
          <div class="flex items-center justify-between w-[354px] mb-[20px]">
            <span class="text-[#4C4C4C] text-[16px]">结束时间</span>
            <span class="text-[#000] font-bold text-[16px]">{{ formatUnixTimestamp(sourceStage?.sessionVO?.endTime) }}</span>
          </div>
          <div class="flex items-center justify-between w-[354px] mb-[20px]">
            <span class="text-[#4C4C4C] text-[16px]">使用时长</span>
            <span class="text-[#000] font-bold text-[16px]">{{ calculateDuration(sourceStage?.sessionVO?.startTime || 0, currentTime) }}</span>
          </div>
          <div class="flex items-center justify-between w-[354px] mb-[20px]">
            <span class="text-[#4C4C4C] text-[16px]">剩余时长</span>
            <span class="text-[#000] font-bold text-[16px]">{{ calculateRemainingDuration(sourceStage) }}</span>
          </div>
        </div>
      </div>

      <div class="flex flex-col items-center h-full">
        <div class="w-[2px] bg-[#F3F3F3] h-[148px]"></div>
        <div class="flex items-center justify-center w-[24px] h-[24px] border border-[#BBB] rounded-full">
          <el-icon class="text-[#AAA] font-bold"><Switch /></el-icon>
        </div>
        <div class="w-[2px] bg-[#F3F3F3] flex-1"></div>
      </div>
      <!-- 目标包厢选择 -->
      <div class="w-1/2 flex flex-col items-center">
        <div class="w-full flex flex-col items-center mt-[88px]">
          <h3 class="mb-4 text-[16px] text-[#999]">新包厢</h3>
          <el-select v-model="selectedTargetStageId" class="option-selector w-[400px]" :placeholder="targetRoomPlaceholder" :loading="loading">
            <el-option v-for="stage in availableStages" :key="stage.roomVO.id" :label="stage.roomVO.name" :value="stage.roomVO.id">
              <div class="flex justify-between items-center">
                <span>{{ stage.roomVO.name }}</span>
                <div class="flex items-center gap-2">
                  <span class="text-gray-500 text-sm">{{ stage.roomVO.roomTypeVO?.name }}</span>
                  <span class="text-gray-500 text-sm">{{ getCombinedStatusName(stage) }}</span>
                </div>
              </div>
            </el-option>
          </el-select>

          <!-- 目标包厢信息 -->
          <div v-if="targetStage" class="mt-4">
            <div class="flex items-center justify-between w-[354px] mb-[20px]">
              <span class="text-[#4C4C4C] text-[16px]">包厢类型</span>
              <span class="text-[#000] font-bold text-[16px]">{{ targetStage.roomTypeVO?.name }}</span>
            </div>
            <div class="flex items-center justify-between w-[354px] mb-[20px]">
              <span class="text-[#4C4C4C] text-[16px]">包厢状态</span>
              <span class="text-[#000] font-bold text-[16px]">{{ getCombinedStatusName(targetStage) }}</span>
            </div>
            <div class="flex items-center justify-between w-[354px] mb-[20px]">
              <span class="text-[#4C4C4C] text-[16px]">开台时间</span>
              <span class="text-[#000] font-bold text-[16px]">{{ formatUnixTimestamp(targetStage.sessionVO?.startTime) }}</span>
            </div>
            <div class="flex items-center justify-between w-[354px] mb-[20px]">
              <span class="text-[#4C4C4C] text-[16px]">结束时间</span>
              <span class="text-[#000] font-bold text-[16px]">{{ formatUnixTimestamp(targetStage.sessionVO?.endTime) }}</span>
            </div>
            <div class="flex items-center justify-between w-[354px] mb-[20px]">
              <span class="text-[#4C4C4C] text-[16px]">使用时长</span>
              <span class="text-[#000] font-bold text-[16px]">{{ calculateDuration(targetStage.sessionVO?.startTime || 0, currentTime) }}</span>
            </div>
            <div class="flex items-center justify-between w-[354px] mb-[20px]">
              <span class="text-[#4C4C4C] text-[16px]">剩余时长</span>
              <span class="text-[#000] font-bold text-[16px]">{{ calculateRemainingDuration(targetStage) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end border-t pt-[24px] pr-[12px]">
        <el-button class="btn-black" :loading="loading" :disabled="!canExecute" @click="handleConfirm">
          {{ confirmButtonText }}
        </el-button>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { formatUnixTimestamp } from '@/utils/dateUtils';
import { ExtendedStageVO } from '@/modules/room/types/extendedStageVO';
import { useStageStore } from '@/stores/stageStore';
import { OrderApi } from '@/modules/order/api/order';
import type { BookingVO } from '@/types/projectobj';
import { useTimeStore } from '@/stores/timeStore';
import { useVodService } from '@/application/vodService';
import { ROOM_STATUS, SESSION_TAGS, SESSION_PAY_STATUS, getCombinedStatusName } from '@/modules/room/constants/stageStatus';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { DialogUIType } from '@/types/dialog';
const stageStore = useStageStore();
const timeStore = useTimeStore();
const currentTime = computed(() => Math.floor(timeStore.getCorrectedTime().getTime() / 1000));

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
    default: false
  },
  sourceRoomId: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String as () => 'swap' | 'attach' | 'merge' | 'transfer',
    required: true
  }
});

// Emits
const emit = defineEmits(['confirm', 'cancel', 'update:visible']);

// 类型定义
interface StageVO {
  roomVO: {
    id: string;
    name: string;
    roomTypeVO?: {
      name: string;
    };
    tags?: string[];
    deviceIp?: string;
  };
  sessionVO?: {
    sessionId: string;
    startTime: number;
    endTime: number;
  };
  combinedStatus: string;
  bookingVOs?: BookingVO[];
  areaVO?: {
    capacity: number;
    ctime: number;
    description: string;
    id: string;
    isDisplayed: boolean;
    name: string;
    state: number;
    utime: number;
    venueId: string;
    version: number;
  };
  unionRoomName?: string;
  roomTypeVO?: {
    name: string;
  };
}

// 常量配置
const OPERATION_CONFIG = {
  swap: {
    title: '包厢互换',
    description: '使用中的A房间和使用中的B房间换订单信息',
    buttonText: '确认互换',
    placeholder: '请选择要互换的包厢'
  },
  attach: {
    title: '包厢联台',
    description: '空闲的房间和使用中的房间联台，订单（Session）共用',
    buttonText: '确认联台',
    placeholder: '请选择要联台的包厢'
  },
  merge: {
    title: '包厢并台',
    description: '使用中的A包厢和使用中的B包厢并台，A包间变为空闲，A房间原有的订单挂在B房间下',
    buttonText: '确认并台',
    placeholder: '请选择要并台的包厢'
  },
  transfer: {
    title: '包厢换房',
    description: '使用中的房间和空闲的房间交换',
    buttonText: '确认换房',
    placeholder: '请选择要转移到的空闲包厢'
  }
};

// 计算属性
const dialogTitle = computed(() => OPERATION_CONFIG[props.mode].title);
const operationDescription = computed(() => OPERATION_CONFIG[props.mode].description);
const confirmButtonText = computed(() => OPERATION_CONFIG[props.mode].buttonText);
const targetRoomPlaceholder = computed(() => OPERATION_CONFIG[props.mode].placeholder);

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: val => !val && emit('cancel')
});
const selectedTargetStageId = ref('');

// 获取源包厢和目标包厢
const sourceStage = computed(() => stageStore.getStageByRoomId(props.sourceRoomId));
const targetStage = computed(() => (selectedTargetStageId.value ? stageStore.getStageByRoomId(selectedTargetStageId.value) : null));

// 可用包厢列表
const availableStages = computed(() => {
  return stageStore.getStages().filter(stage => {
    if (stage.roomVO.id === props.sourceRoomId) return false;
    console.log('availableStages', props.mode, stage);
    switch (props.mode) {
      case 'swap':
        return isValidForSwap(stage);
      case 'attach':
        return isValidForAttach(stage);
      case 'merge':
        return isValidForMerge(stage);
      case 'transfer':
        return isValidForTransfer(stage);
    }
  });
});

// 验证是否可以执行操作
const canExecute = computed(() => {
  if (!sourceStage.value || !targetStage.value) {
    console.log('Stage missing:', { source: sourceStage.value, target: targetStage.value });
    return false;
  }

  console.log('Operation validation:', {
    mode: props.mode,
    sourceLocked: hasLockedOrLinkedTag(sourceStage.value),
    targetLocked: hasLockedOrLinkedTag(targetStage.value)
  });

  switch (props.mode) {
    case 'swap':
      return (
        sourceStage.value.roomStatus === ROOM_STATUS.IN_USE &&
        targetStage.value.roomStatus === ROOM_STATUS.IN_USE &&
        !hasLockedOrLinkedTag(sourceStage.value) &&
        !hasLockedOrLinkedTag(targetStage.value)
      );
    case 'attach':
      return (
        sourceStage.value.roomStatus === ROOM_STATUS.IDLE &&
        targetStage.value.roomStatus === ROOM_STATUS.IN_USE &&
        !hasLockedTag(sourceStage.value) &&
        !hasLockedTag(targetStage.value)
      );
    case 'merge':
      return (
        sourceStage.value.roomStatus === ROOM_STATUS.IN_USE &&
        !isStageTimeOut(sourceStage.value) &&
        targetStage.value.roomStatus === ROOM_STATUS.IN_USE &&
        !isStageTimeOut(targetStage.value) &&
        !hasLockedOrLinkedTag(sourceStage.value) &&
        !hasLockedOrLinkedTag(targetStage.value) &&
        !hasTimingTag(sourceStage.value) &&
        !hasTimingTag(targetStage.value)
      );
    case 'transfer':
      return (
        sourceStage.value.roomStatus === ROOM_STATUS.IN_USE && targetStage.value.roomStatus === ROOM_STATUS.IDLE && !hasLockedOrLinkedTag(sourceStage.value)
      );
    default:
      return false;
  }
});

// 辅助方法
const calculateDuration = (startTime: number, endTime: number) => {
  console.log('startTime', startTime, 'endTime', endTime);
  if (!startTime || !endTime) return '-';
  const duration = Math.floor((endTime - startTime) / 60); // 转换为分钟
  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  return `${hours}小时${minutes}分钟`;
};

const getBookingInfo = (booking: BookingVO | undefined) => {
  if (!booking) return '-';
  return `${booking.customerName} ${booking.customerPhone}`;
};

const hasLockedOrLinkedTag = (stage: ExtendedStageVO) => {
  return stage.tags?.includes(SESSION_TAGS.LOCKED) || stage.tags?.includes(SESSION_TAGS.UNION);
};

const isStageTimeOut = (stage: ExtendedStageVO) => {
  return stage.tags?.includes(SESSION_TAGS.TIMEOUT);
};

const hasLockedTag = (stage: ExtendedStageVO) => {
  return stage.tags?.includes(SESSION_TAGS.LOCKED);
};

// 新增: 判断是否包含"计时"标签（isTimeConsume 或 timing）
const hasTimingTag = (stage: ExtendedStageVO) => {
  return stage.tags?.includes(SESSION_TAGS.TIMING) || stage.tags?.includes(SESSION_TAGS.ISTIMECONSUME);
};

// 验证方法
const isValidForSwap = (stage: ExtendedStageVO) => {
  return stage.roomStatus === ROOM_STATUS.IN_USE && !hasLockedOrLinkedTag(stage);
};

const isValidForAttach = (stage: ExtendedStageVO) => {
  return stage.roomStatus === ROOM_STATUS.IN_USE && !hasLockedTag(stage);
};

const isValidForMerge = (stage: ExtendedStageVO) => {
  return stage.roomStatus === ROOM_STATUS.IN_USE && !hasLockedOrLinkedTag(stage) && !hasTimingTag(stage);
};

const isValidForTransfer = (stage: ExtendedStageVO) => {
  return stage.roomStatus === ROOM_STATUS.IDLE && !hasLockedOrLinkedTag(stage);
};

// 处理方法
const handleCancel = () => {
  selectedTargetStageId.value = '';
  emit('cancel');
};

const handleConfirm = async () => {
  if (!canExecute.value) return;

  try {
    const params = buildRequestParams();

    switch (props.mode) {
      case 'swap':
        await OrderApi.swapRoom(params as any);
        break;
      case 'attach':
        await OrderApi.attachRoom(params as any);
        break;
      case 'merge':
        await OrderApi.mergeRoom(params as any);
        break;
      case 'transfer':
        await OrderApi.transferRoom(params as any);
        break;
    }

    handleVodCommand(props.mode);

    emit('confirm', {
      sourceRoomId: props.sourceRoomId,
      targetRoomId: selectedTargetStageId.value,
      mode: props.mode
    });
    handleCancel();
  } catch (error) {
    console.error('操作失败:', error);
  }
};

const handleVodCommand = async (mode: string) => {
  console.log('handleVodCommand', mode, ', source deviceIp:', sourceStage.value?.roomVO.deviceIp, ', target deviceIp:', targetStage.value?.roomVO.deviceIp);
  if (!sourceStage.value?.roomVO.deviceIp) return;
  const vodService = useVodService();
  switch (mode) {
    case 'swap':
      break;
    case 'attach':
      console.log('attch, 包厢联台, 打开：', sourceStage.value.roomVO.deviceIp);
      await vodService.open(sourceStage.value.roomVO.id);
      break;
    case 'merge':
      console.log('merge, 包厢并台, 关闭：', sourceStage.value.roomVO.deviceIp);
      await vodService.close(sourceStage.value.roomVO.id);
      break;
    case 'transfer':
      console.log('transfer, 包厢换房, 切换：', sourceStage.value.roomVO.deviceIp, targetStage.value?.roomVO.deviceIp);
      if (targetStage.value?.roomVO.deviceIp) {
        await vodService.switchRoom(sourceStage.value.roomVO.id, targetStage.value.roomVO.id);
      }
      break;
  }
};

// 构建请求参数
const buildRequestParams = () => {
  const baseParams = {
    roomId: props.sourceRoomId,
    sessionId: sourceStage.value?.sessionVO?.sessionId
  };
  const targetParams = {
    roomId: selectedTargetStageId.value,
    sessionId: targetStage.value?.sessionVO?.sessionId
  };

  switch (props.mode) {
    case 'swap':
      return {
        sessionVOOpeningA: baseParams,
        sessionVOOpeningB: targetParams
      };
    case 'attach':
      return {
        sessionVOIdle: baseParams,
        sessionVOOpening: targetParams
      };
    case 'merge':
      return {
        sessionVOOpeningA: baseParams,
        sessionVOOpeningB: targetParams
      };
    case 'transfer':
      return {
        sessionVOOpening: baseParams,
        sessionVOIdle: targetParams
      };
  }
};

// 新增：计算剩余时长的方法，计时房间返回 "-"
const calculateRemainingDuration = (stage: ExtendedStageVO | null) => {
  if (!stage) return '-';
  if (hasTimingTag(stage)) return '-';
  return calculateDuration(currentTime.value, stage.sessionVO?.endTime || 0);
};
</script>

<style scoped>
:deep(.option-selector .el-select__wrapper) {
  height: 64px;
}
</style>
