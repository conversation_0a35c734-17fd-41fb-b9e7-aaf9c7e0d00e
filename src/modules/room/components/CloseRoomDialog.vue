<template>
  <AppDialog v-model="dialogVisible" width="564px" align-center class="h-[320px] close-room-dialog">
    <!-- 自定义标题 -->
    <template #header>
      <div class="el-dialog__title">
        <span class="room-name-title">{{ computedTitle }}</span>
      </div>
    </template>
    <div class="flex items-center justify-center text-[20px] font-medium text-[#000]">
      {{ confirmText }}
    </div>
    <template #footer>
      <div class="flex justify-center gap-2">
        <el-button class="btn-black" type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
      </div>
    </template>
  </AppDialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { OrderApi } from '@/modules/order/api/order';
import { useStageStore } from '@/stores/stageStore';
import { useTimeStore } from '@/stores/timeStore';
import { ROOM_STATUS, SESSION_PAY_STATUS, SESSION_TAGS } from '../constants/stageStatus';
import AppDialog from '@/components/Dialog/core/AppDialog.vue';
import { now10 } from '@/utils/dateUtils';

// 定义props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  visible: {
    type: Boolean,
    default: false
  },
  sessionId: {
    type: String,
    default: ''
  },
  roomId: {
    type: String,
    default: ''
  },
  roomName: {
    type: String,
    default: ''
  },
  endTime: {
    type: Number,
    default: 0
  }
});

// 定义emits
const emit = defineEmits(['update:modelValue', 'update:visible', 'success', 'close']);

const stageStore = useStageStore();
const timeStore = useTimeStore();
const loading = ref(false);

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue || props.visible,
  set: val => {
    emit('update:modelValue', val);
    emit('update:visible', val);
  }
});

// 计算属性：处理标题显示
const computedTitle = computed(() => {
  const name = props.roomName || '--';
  return `${name} 关房`;
});

// 直接使用传入的endTime和now10()比较来判断是否提前关房
const isAfterCloseTime = computed(() => {
  // 如果没有结束时间，默认为提前关房
  if (!props.endTime) return false;

  // 比较当前时间与结束时间
  const currentTime = now10();
  return currentTime >= props.endTime;
});

const confirmText = computed(() => {
  return isAfterCloseTime.value ? `已到结束时间，确认关房` : `未到结束时间，确认提前关房?`;
});

const handleConfirm = async () => {
  if (!props.roomId || !props.sessionId) {
    ElMessage.warning('无效的包厢信息');
    return;
  }

  loading.value = true;
  try {
    const response = await OrderApi.closeRoom({
      sessionId: props.sessionId,
      roomId: props.roomId
    });

    // 通过stageStore获取对应的stage
    let stage = stageStore.getStageByRoomId(props.roomId);
    if (!stage) {
      ElMessage.warning('未找到包厢信息');
      loading.value = false;
      return { success: false };
    }

    let updatedStage = stage;

    if (response.code === 0) {
      if (stage.payStatus === SESSION_PAY_STATUS.PAID) {
        updatedStage = {
          ...stage,
          roomVO: {
            ...stage.roomVO,
            status: ROOM_STATUS.CLEANING
          },
          sessionVO: null as any // 使用 as any 规避类型错误
        };
      } else {
        if (stage.payStatus === SESSION_PAY_STATUS.UNPAID) {
          updatedStage = {
            ...stage,
            roomVO: {
              ...stage.roomVO,
              status: ROOM_STATUS.IDLE
            },
            tags: [SESSION_TAGS.TIMEOUT]
          };
        }
      }

      stageStore.updateStageInfo(updatedStage);

      ElMessage({
        message: '关房成功',
        type: 'success'
      });

      // 关闭对话框
      dialogVisible.value = false;

      // 触发成功事件，同时返回结果
      emit('success', updatedStage);
      return { success: true, data: updatedStage };
    } else {
      ElMessage({
        message: `关房失败: ${response.message}`,
        type: 'error'
      });
      return { success: false, message: response.message };
    }
  } catch (error) {
    console.error('关房失败:', error);
    ElMessage({
      message: '关房失败，请稍后重试',
      type: 'error'
    });
    return { success: false, error };
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
:deep(.close-room-dialog .el-dialog__body) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 包厢名称标题样式 - 限制两行并显示省略号 */
.room-name-title {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  padding-right: 32px;
  line-height: 1.2;
  max-height: calc(1.2em * 1); /* 确保最大高度为两行 */
}

/* 确保dialog标题容器有足够的宽度和高度 */
:deep(.close-room-dialog .el-dialog__title) {
  max-width: 480px; /* 为省略号留出空间 */
  line-height: 1.2;
}
</style>
