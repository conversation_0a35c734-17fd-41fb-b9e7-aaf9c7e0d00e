/**
 * PriceModel.ts
 * 简洁的价格计算模型，处理包括跨天在内的价格计算
 */

import * as TimeModel from './TimeModel';
import type { PriceTypeKey, PriceDetail } from './viewmodel';
import type { TimePricePlanVO } from '@/modules/room/entity/timePricePlan';

/**
 * 价格选项接口
 */
export interface PriceOption {
  priceType: PriceTypeKey; // 价格类型
  planName: string; // 方案名称
  price: number; // 价格（元/小时）
  priority: number; // 优先级（数字越小优先级越高）
  timeRange: {
    // 适用时间范围
    start: string; // 开始时间 "HH:MM"
    end: string; // 结束时间 "HH:MM"
  };
  segmentId?: string; // 价格段唯一标识
}

/**
 * 价格段接口 - 不重叠的时间段价格信息
 */
export interface PriceSegment {
  startTime: string; // 开始时间 "HH:MM"
  endTime: string; // 结束时间 "HH:MM"
  durationMinutes: number; // 持续时间(分钟)
  priceType: PriceTypeKey; // 价格类型
  planName: string; // 方案名称
  pricePerMinute: number; // 每分钟价格（元）
  hourlyPrice: number; // 每小时价格（元）
  priority: number; // 优先级（数字越小优先级越高）
  segmentId: string; // 价格段唯一标识，用于标识同一价格选项
}

/**
 * 价格计算结果接口
 */
export interface PriceCalculationResult {
  totalPrice: number; // 总价格（元）
  details: PriceDetail[]; // 价格明细
}

/**
 * 生成价格段唯一标识符
 * @param timeRange 时间范围
 * @param priceType 价格类型
 * @param planName 方案名称
 * @returns 唯一标识符
 */
function generateSegmentId(timeRange: { start: string; end: string }, priceType: PriceTypeKey, planName: string): string {
  return `${timeRange.start}-${timeRange.end}|${priceType}|${planName}`;
}

/**
 * 将时间段和价格选项组合，生成价格段
 * @param timeSegment 时间段
 * @param priceOptions 价格选项列表
 * @param fallbackHourlyPrice 回退价格（当没有匹配的价格选项时使用）
 * @returns 带价格信息的时间段
 */
export function createPriceSegment(timeSegment: TimeModel.TimeSegment, priceOptions: PriceOption[], fallbackHourlyPrice: number = 100): PriceSegment {
  // 找出适用于此时间段的所有价格选项
  const applicablePriceOptions = priceOptions.filter(option => {
    // 全天时间(00:00-00:00)适用于所有时间段
    if (TimeModel.isFullDayRange(option.timeRange.start, option.timeRange.end)) {
      return true;
    }

    // 检查时间段是否与价格选项的时间范围有重叠
    const segmentStartMinute = TimeModel.timeToMinutes(timeSegment.startTime);
    const segmentEndMinute = TimeModel.timeToMinutes(timeSegment.endTime);
    const optionStartMinute = TimeModel.timeToMinutes(option.timeRange.start);
    const optionEndMinute = TimeModel.timeToMinutes(option.timeRange.end);

    return TimeModel.hasTimeRangeOverlap(segmentStartMinute, segmentEndMinute, optionStartMinute, optionEndMinute);
  });

  // 如果没有适用的价格选项，使用回退价格
  if (applicablePriceOptions.length === 0) {
    const fallbackTimeRange = { start: '00:00', end: '00:00' };
    const fallbackId = generateSegmentId(fallbackTimeRange, 'baseRoomFee', '兜底价格');

    return {
      ...timeSegment,
      priceType: 'baseRoomFee',
      planName: '兜底价格',
      pricePerMinute: fallbackHourlyPrice / 60,
      hourlyPrice: fallbackHourlyPrice,
      priority: 999, // 优先级最低
      segmentId: fallbackId
    };
  }

  // 按优先级排序
  applicablePriceOptions.sort((a, b) => a.priority - b.priority);

  // 使用优先级最高的价格选项
  const bestOption = applicablePriceOptions[0];

  // 生成或使用价格段ID
  const segmentId = bestOption.segmentId || generateSegmentId(bestOption.timeRange, bestOption.priceType, bestOption.planName);

  return {
    ...timeSegment,
    priceType: bestOption.priceType,
    planName: bestOption.planName,
    pricePerMinute: bestOption.price / 60,
    hourlyPrice: bestOption.price,
    priority: bestOption.priority,
    segmentId // 添加唯一标识
  };
}

/**
 * 计算价格段与预订时间段的交集
 * @param priceSegment 价格段
 * @param bookingStart 预订开始时间 "HH:MM"
 * @param bookingEnd 预订结束时间 "HH:MM"
 * @returns 价格明细（如果没有交集则返回null）
 */
export function calculateIntersectionPrice(priceSegment: PriceSegment, bookingStart: string, bookingEnd: string): PriceDetail | null {
  // 转换为分钟表示
  const segmentStartMinute = TimeModel.timeToMinutes(priceSegment.startTime);
  const segmentEndMinute = TimeModel.timeToMinutes(priceSegment.endTime);
  const bookingStartMinute = TimeModel.timeToMinutes(bookingStart);
  const bookingEndMinute = TimeModel.timeToMinutes(bookingEnd);

  // 判断是否跨天
  const segmentRange = TimeModel.normalizeTimeRange(segmentStartMinute, segmentEndMinute);
  const bookingRange = TimeModel.normalizeTimeRange(bookingStartMinute, bookingEndMinute);

  console.log(
    `[hourly] 计算交集: 价格段=${priceSegment.startTime}-${priceSegment.endTime}(跨天:${segmentRange.isCrossDay}), 预订=${bookingStart}-${bookingEnd}(跨天:${bookingRange.isCrossDay})`
  );

  // 计算交集
  const intersection = TimeModel.getTimeRangeIntersection(segmentStartMinute, segmentEndMinute, bookingStartMinute, bookingEndMinute);

  // 如果没有交集，返回null
  if (!intersection) {
    console.log(`[hourly] 没有交集`);
    return null;
  }

  // 验证交集计算的合理性
  if (intersection.duration <= 0) {
    console.log(`[hourly] 警告: 计算出的交集时长不合理: ${intersection.duration}分钟`);
    return null;
  }

  // 记录详细日志
  // console.log(
  //   `[hourly] 找到交集: ${TimeModel.minutesToTime(intersection.startMinute)}-${TimeModel.minutesToTime(intersection.endMinute)}, 时长=${intersection.duration}分钟`
  // );

  // 验证交集时间是否在时间段范围内
  const intersectionStartTime = TimeModel.minutesToTime(intersection.startMinute);
  const intersectionEndTime = TimeModel.minutesToTime(intersection.endMinute);

  // 比较价格段与交集的开始时间
  // if (!segmentRange.isCrossDay && !bookingRange.isCrossDay) {
  //   // 简单情况：都不跨天
  //   if (intersection.startMinute < segmentStartMinute || intersection.endMinute > segmentEndMinute) {
  //     console.log(`[hourly] 警告: 交集 ${intersectionStartTime}-${intersectionEndTime} 超出价格段范围 ${priceSegment.startTime}-${priceSegment.endTime}`);
  //   }
  // }

  // 计算价格
  const price = Math.round(intersection.duration * priceSegment.pricePerMinute);

  // 创建价格明细
  return {
    startTime: intersectionStartTime,
    endTime: intersectionEndTime,
    duration: intersection.duration,
    price,
    planName: priceSegment.planName,
    priceType: priceSegment.priceType,
    priority: priceSegment.priority,
    segmentId: priceSegment.segmentId // 添加段ID用于合并
  };
}

/**
 * 将原始价格选项转换为标准格式
 * @param rawOptions 原始价格选项
 * @param selectedPriceOption 选择的价格类型
 * @returns 标准化的价格选项列表
 */
export function normalizeRawPriceOptions(
  rawOptions: Array<{
    planId: string;
    planName: string;
    timeRange: { start: string; end: string };
    prices: Record<PriceTypeKey, number>;
  }>,
  selectedPriceOption: PriceTypeKey
): PriceOption[] {
  const result: PriceOption[] = [];

  // 处理每个原始选项
  rawOptions.forEach(option => {
    // 标准化时间范围（将全天的各种表示统一为00:00-00:00）
    const timeRange = {
      start: option.timeRange.start,
      end: option.timeRange.end
    };

    // 全天时间的标准化
    if (
      (timeRange.start === '00:00' && timeRange.end === '00:00') ||
      (timeRange.start === '00:00' && timeRange.end === '23:59') ||
      (timeRange.start === '00:00' && timeRange.end === '24:00')
    ) {
      timeRange.start = '00:00';
      timeRange.end = '00:00';
    }

    // 优先添加选中的价格类型
    if (option.prices[selectedPriceOption] > 0) {
      const segmentId = generateSegmentId(timeRange, selectedPriceOption, option.planName);
      result.push({
        priceType: selectedPriceOption,
        planName: option.planName,
        price: option.prices[selectedPriceOption],
        priority: 0, // 选中类型优先级最高
        timeRange,
        segmentId
      });
    }
    // 添加其他有效价格类型
    else {
      const priceTypes: PriceTypeKey[] = ['baseRoomFee', 'birthdayFee', 'activityFee', 'groupBuyFee'];

      for (const type of priceTypes) {
        if (option.prices[type] > 0) {
          const segmentId = generateSegmentId(timeRange, type, option.planName);
          result.push({
            priceType: type,
            planName: option.planName,
            price: option.prices[type],
            priority: type === 'baseRoomFee' ? 1 : 2, // 基础价格优先级次之
            timeRange,
            segmentId
          });
        }
      }
    }
  });

  return result;
}

/**
 * 共享的核心时间段生成和价格计算逻辑
 * @param startMinute 开始时间（分钟）
 * @param endMinute 结束时间（分钟）或者null（如果是根据金额计算）
 * @param priceOptions 价格选项列表
 * @param targetAmount 目标金额（如果是根据金额计算）
 * @param fallbackHourlyPrice 回退价格
 * @returns 处理结果包含生成的时间段、价格段等
 */
export function generateTimeAndPriceSegments(
  startMinute: number,
  endMinute: number | null,
  priceOptions: PriceOption[],
  targetAmount: number | null = null,
  fallbackHourlyPrice: number = 100
) {
  // 确定是按时间还是按金额计算
  const isAmountCalculation = targetAmount !== null && endMinute === null;
  const startTime = TimeModel.minutesToTime(startMinute);

  let maxHours = 24; // 默认最大预测时长

  // 如果是按金额计算，估算需要的时间
  if (isAmountCalculation && targetAmount) {
    // 估算需要的小时数（使用最低可能的每小时价格进行估算）
    const lowestHourlyPrice = priceOptions.reduce((lowest, option) => Math.min(lowest, option.price), fallbackHourlyPrice);

    // 保守估计所需的小时数，增加20%余量确保足够
    const estimatedHours = Math.ceil((targetAmount / lowestHourlyPrice) * 1.2);
    maxHours = Math.min(estimatedHours, 24); // 最多预测24小时以避免过度计算

    console.log(`[hourly] 估算需要约 ${estimatedHours} 小时，使用最低小时价格 ${lowestHourlyPrice}元/小时`);
  }

  // 1. 收集所有分界时间点
  const timePoints = new Set<number>();

  // 添加开始时间
  timePoints.add(startMinute);

  // 如果是按时间计算，则添加结束时间
  if (!isAmountCalculation && endMinute !== null) {
    timePoints.add(endMinute);

    // 判断是否跨天
    const bookingRange = TimeModel.normalizeTimeRange(startMinute, endMinute);
    const isCrossDay = bookingRange.isCrossDay;

    console.log('[hourly] 检测到预订时间:', startTime, '-', TimeModel.minutesToTime(endMinute), isCrossDay ? '(跨天)' : '');
  }

  // 添加价格选项的时间点和必要的时间点
  // 添加午夜点，便于处理跨天
  timePoints.add(0);
  timePoints.add(TimeModel.MINUTES_PER_DAY);

  // 添加所有价格选项的时间点
  priceOptions.forEach(option => {
    const optionStartMinute = TimeModel.timeToMinutes(option.timeRange.start);
    const optionEndMinute = TimeModel.timeToMinutes(option.timeRange.end);

    // 如果是按金额计算，则在预估范围内添加多天的时间点
    if (isAmountCalculation) {
      // 计算一天内可能出现的所有此类时间点
      for (let day = 0; day < Math.ceil(maxHours / 24) + 1; day++) {
        const dayOffset = day * TimeModel.MINUTES_PER_DAY;

        // 只添加在预估时间范围内的点
        if (startMinute + dayOffset <= startMinute + maxHours * 60) {
          timePoints.add(optionStartMinute + dayOffset);
          timePoints.add(optionEndMinute + dayOffset);
        }
      }

      // 添加每整点时间，而不是每小时都添加
      for (let hour = 0; hour <= maxHours; hour++) {
        const hourMinute = startMinute + hour * 60;
        timePoints.add(hourMinute);
      }
    } else {
      // 按时间计算时只添加普通时间点
      timePoints.add(optionStartMinute);
      timePoints.add(optionEndMinute);
    }
  });

  // 2. 处理时间点和生成时间段 - 考虑是否跨天
  const timeSegments: TimeModel.TimeSegment[] = [];

  // 确定是否是跨天预订（仅对固定时间段有效）
  const isCrossDay = !isAmountCalculation && endMinute !== null && TimeModel.normalizeTimeRange(startMinute, endMinute).isCrossDay;

  if (!isAmountCalculation && isCrossDay) {
    // 跨天预订 - 使用统一的方法处理
    console.log('[hourly] 跨天预订处理 - 确保连续的时间段');

    // 1. 准备所有的时间点，确保包含午夜点
    if (!timePoints.has(0)) timePoints.add(0);
    if (!timePoints.has(TimeModel.MINUTES_PER_DAY)) timePoints.add(TimeModel.MINUTES_PER_DAY);

    // 2. 创建规范化的时间点列表 - 将所有时间点转换到一个连续的时间线上
    // 第一段：从预订开始到午夜
    const day1Points = Array.from(timePoints)
      .filter(m => m >= startMinute && m <= TimeModel.MINUTES_PER_DAY)
      .sort((a, b) => a - b);

    // 第二段：从午夜到预订结束
    const day2Points = Array.from(timePoints)
      .filter(m => m >= 0 && m <= (endMinute as number))
      .sort((a, b) => a - b);

    // 确保午夜点存在于两个数组中
    if (!day1Points.includes(TimeModel.MINUTES_PER_DAY)) {
      day1Points.push(TimeModel.MINUTES_PER_DAY);
    }

    if (!day2Points.includes(0)) {
      day2Points.unshift(0);
    }

    console.log(
      '[hourly] 第一天时间点:',
      day1Points.map(m => TimeModel.minutesToTime(m % TimeModel.MINUTES_PER_DAY))
    );
    console.log(
      '[hourly] 第二天时间点:',
      day2Points.map(m => TimeModel.minutesToTime(m))
    );

    // 3. 生成时间段
    // 第一天的时间段（从预订开始到午夜）
    for (let i = 0; i < day1Points.length - 1; i++) {
      const segmentStart = day1Points[i];
      const segmentEnd = day1Points[i + 1];

      if (segmentEnd <= segmentStart) continue; // 跳过无效段

      timeSegments.push({
        startTime: TimeModel.minutesToTime(segmentStart),
        endTime: TimeModel.minutesToTime(segmentEnd % TimeModel.MINUTES_PER_DAY), // 处理午夜24:00 -> 00:00
        durationMinutes: segmentEnd - segmentStart
      });
    }

    // 第二天的时间段（从午夜到预订结束）
    for (let i = 0; i < day2Points.length - 1; i++) {
      const segmentStart = day2Points[i];
      const segmentEnd = day2Points[i + 1];

      if (segmentEnd <= segmentStart) continue; // 跳过无效段

      timeSegments.push({
        startTime: TimeModel.minutesToTime(segmentStart),
        endTime: TimeModel.minutesToTime(segmentEnd),
        durationMinutes: segmentEnd - segmentStart
      });
    }
  } else {
    // 根据不同计算模式处理时间点
    let sortedTimePoints: number[];

    if (isAmountCalculation) {
      // 按金额计算时，生成完整的时间序列直到预估最大时间
      const estimatedEndMinute = startMinute + maxHours * 60;

      // 获取所有时间点并按照发生顺序排序
      sortedTimePoints = Array.from(timePoints)
        .filter(m => m >= startMinute && m <= estimatedEndMinute)
        .sort((a, b) => a - b);
    } else {
      // 按时间计算时正常处理
      sortedTimePoints = Array.from(timePoints).sort((a, b) => a - b);
    }

    console.log('[hourly] 收集到的时间点:', sortedTimePoints);

    console.log(
      '[hourly] 收集到的时间点:',
      sortedTimePoints.slice(0, 20).map(m => {
        const day = Math.floor((m - startMinute) / TimeModel.MINUTES_PER_DAY);
        const normalizedMinute = m % TimeModel.MINUTES_PER_DAY;
        return TimeModel.minutesToTime(normalizedMinute) + (day > 0 ? `(+${day})` : '');
      }) + (sortedTimePoints.length > 20 ? '...(总计:' + sortedTimePoints.length + '个)' : '')
    );

    // 生成时间段
    for (let i = 0; i < sortedTimePoints.length - 1; i++) {
      const segmentStart = sortedTimePoints[i];
      const segmentEnd = sortedTimePoints[i + 1];

      // 确保时间段有效（持续时间大于0）
      if (segmentEnd <= segmentStart) continue;

      // 计算持续时间
      const durationMinutes = segmentEnd - segmentStart;

      if (durationMinutes <= 0) {
        console.log(
          `[hourly] 警告: 跳过无效时间段 ${TimeModel.minutesToTime(segmentStart % TimeModel.MINUTES_PER_DAY)}-${TimeModel.minutesToTime(segmentEnd % TimeModel.MINUTES_PER_DAY)}, 持续时间=${durationMinutes}`
        );
        continue;
      }

      // 转换为时间字符串 - 处理跨天情况
      const segmentStartTime = TimeModel.minutesToTime(segmentStart % TimeModel.MINUTES_PER_DAY);
      const segmentEndTime = TimeModel.minutesToTime(segmentEnd % TimeModel.MINUTES_PER_DAY);

      timeSegments.push({
        startTime: segmentStartTime,
        endTime: segmentEndTime,
        durationMinutes
      });
    }
  }

  // 验证时间段是否连续（确保一个的结束是下一个的开始）
  for (let i = 0; i < timeSegments.length - 1; i++) {
    const currentSegment = timeSegments[i];
    const nextSegment = timeSegments[i + 1];

    // 检查是否连续 - 特殊处理午夜情况 "00:00" 和 "24:00"
    if (currentSegment.endTime !== nextSegment.startTime && !(currentSegment.endTime === '00:00' && nextSegment.startTime === '00:00')) {
      console.log(`[hourly] 警告: 时间段不连续 - ${currentSegment.endTime} 不等于 ${nextSegment.startTime}`);
    }
  }

  console.log('[hourly] 生成的时间段:', JSON.stringify(timeSegments));

  // 3. 为每个时间段找到最佳价格
  const priceSegments = timeSegments.map(segment => createPriceSegment(segment, priceOptions, fallbackHourlyPrice));

  return {
    timeSegments,
    priceSegments,
    isCrossDay: isCrossDay || false
  };
}

/**
 * 合并价格明细，根据segmentId而不仅仅是相邻关系，并且保持原始顺序
 * @param priceDetails 价格明细数组
 * @returns 合并后的价格明细数组
 */
export function mergeAdjacentPriceDetails(priceDetails: PriceDetail[]): PriceDetail[] {
  console.log('[hourly] 合并价格明细:', JSON.stringify(priceDetails));

  if (priceDetails.length <= 1) {
    return [...priceDetails];
  }

  // 不分组，直接线性扫描，保持原始顺序
  const results: PriceDetail[] = [];
  let currentDetail = { ...priceDetails[0] };

  for (let i = 1; i < priceDetails.length; i++) {
    const nextDetail = priceDetails[i];
    const currentSegmentId = currentDetail.segmentId || `${currentDetail.priceType}|${currentDetail.planName}|${currentDetail.priority}`;
    const nextSegmentId = nextDetail.segmentId || `${nextDetail.priceType}|${nextDetail.planName}|${nextDetail.priority}`;

    // 如果是相同segmentId且时间相邻，则合并
    if (currentSegmentId === nextSegmentId && currentDetail.endTime === nextDetail.startTime) {
      // 合并相邻的相同segmentId时段
      currentDetail.endTime = nextDetail.endTime;
      currentDetail.duration += nextDetail.duration;
      currentDetail.price += nextDetail.price;
      console.log(
        `[hourly] 合并相同segmentId(${currentSegmentId})的时段: ${currentDetail.startTime}-${currentDetail.endTime}, 总时长=${currentDetail.duration}分钟, 总价=${currentDetail.price}元`
      );
    } else {
      // 不是相同的segmentId或不相邻，保存当前结果并开始新的
      results.push(currentDetail);
      currentDetail = { ...nextDetail };

      if (currentSegmentId === nextSegmentId) {
        console.log(`[hourly] 相同segmentId但时段不相邻，无法合并: 当前=${results[results.length - 1].endTime}, 下一个=${currentDetail.startTime}`);
      }
    }
  }

  // 添加最后处理的结果
  results.push(currentDetail);

  return results;
}

/**
 * 按时间顺序排序价格详情，特别处理跨天情况
 * @param priceDetails 价格明细数组
 * @param bookingStart 预订开始时间 "HH:MM"
 * @param bookingEnd 预订结束时间 "HH:MM"
 * @returns 排序后的价格明细数组
 */
export function sortPriceDetails(priceDetails: PriceDetail[], bookingStart: string, bookingEnd: string): PriceDetail[] {
  return [...priceDetails].sort((a, b) => {
    // 处理跨天预订的特殊排序
    const bookingIsCrossDay = TimeModel.normalizeTimeStringRange(bookingStart, bookingEnd).isCrossDay;
    const aMinutes = TimeModel.timeToMinutes(a.startTime);
    const bMinutes = TimeModel.timeToMinutes(b.startTime);

    if (bookingIsCrossDay) {
      // 跨天预订情况下的排序逻辑
      const bookingStartMinute = TimeModel.timeToMinutes(bookingStart);

      // 判断是否在第一天还是第二天
      const aIsFirstDay = aMinutes >= bookingStartMinute;
      const bIsFirstDay = bMinutes >= bookingStartMinute;

      if (aIsFirstDay && !bIsFirstDay) {
        // a在第一天，b在第二天，a应该排前面
        return -1;
      }
      if (!aIsFirstDay && bIsFirstDay) {
        // a在第二天，b在第一天，b应该排前面
        return 1;
      }

      // 同一天内部排序
      return aMinutes - bMinutes;
    } else {
      // 非跨天预订正常排序
      // 如果开始时间不同，按开始时间排序
      if (aMinutes !== bMinutes) {
        return aMinutes - bMinutes;
      }

      // 如果开始时间相同，按结束时间排序
      const aEndMinutes = TimeModel.timeToMinutes(a.endTime);
      const bEndMinutes = TimeModel.timeToMinutes(b.endTime);
      return aEndMinutes - bEndMinutes;
    }
  });
}

/**
 * 计算预订时间段的总价格
 * @param bookingStart 预订开始时间 "HH:MM"
 * @param bookingEnd 预订结束时间 "HH:MM"
 * @param priceOptions 价格选项列表
 * @param fallbackHourlyPrice 回退价格（当没有匹配的价格选项时使用）
 * @returns 价格计算结果
 */
export function calculatePrice(
  bookingStart: string,
  bookingEnd: string,
  priceOptions: PriceOption[],
  fallbackHourlyPrice: number = 100
): PriceCalculationResult {
  // 转换时间为分钟
  const startMinute = TimeModel.timeToMinutes(bookingStart);
  const endMinute = TimeModel.timeToMinutes(bookingEnd);

  // 使用共享逻辑生成时间段和价格段
  const { priceSegments } = generateTimeAndPriceSegments(startMinute, endMinute, priceOptions, null, fallbackHourlyPrice);

  // console.log('[hourly] calculatePrice created priceSegments:', JSON.stringify(priceSegments));

  // 计算预订时间段与价格段的交集
  const priceDetails: PriceDetail[] = [];
  let totalPrice = 0;

  // 记录已经处理过的时间段，避免重复计算
  const processedTimeRanges = new Set<string>();

  priceSegments.forEach(segment => {
    const intersectionDetail = calculateIntersectionPrice(segment, bookingStart, bookingEnd);

    if (intersectionDetail) {
      // 生成一个唯一的时间范围标识符
      const timeRangeKey = `${intersectionDetail.startTime}-${intersectionDetail.endTime}`;

      // 检查是否已经处理过这个时间范围
      if (!processedTimeRanges.has(timeRangeKey)) {
        processedTimeRanges.add(timeRangeKey);
        priceDetails.push(intersectionDetail);
        totalPrice += intersectionDetail.price;
        // console.log(`[hourly] 添加价格段: ${timeRangeKey}, 时长=${intersectionDetail.duration}分钟, 价格=${intersectionDetail.price}元`);
      } else {
        console.log(`[hourly] 跳过重复的价格段: ${timeRangeKey}`);
      }
    }
  });

  // console.log('[hourly] calculatePrice final priceDetails:', JSON.stringify(priceDetails));

  // 按时间顺序排序价格详情
  const sortedDetails = sortPriceDetails(priceDetails, bookingStart, bookingEnd);

  // 检查并合并相邻的相同价格类型段
  const mergedDetails = mergeAdjacentPriceDetails(sortedDetails);

  // 重新计算总价
  totalPrice = mergedDetails.reduce((sum, detail) => sum + detail.price, 0);

  // console.log('[hourly] 最终合并后价格明细:', JSON.stringify(mergedDetails));

  return {
    totalPrice,
    details: mergedDetails
  };
}

/**
 * 由价格估算持续时间
 * @param startTime 开始时间 "HH:MM"
 * @param targetAmount 目标金额（元）
 * @param priceOptions 价格选项列表
 * @param fallbackHourlyPrice 回退价格
 * @returns 价格计算结果
 */
export function calculateDurationByAmount(
  startTime: string,
  targetAmount: number,
  priceOptions: PriceOption[],
  fallbackHourlyPrice: number = 100
): PriceCalculationResult {
  console.log(`[hourly] 根据价格估算时长: 开始时间=${startTime}, 目标金额=${targetAmount}元`);

  // 转换时间为分钟
  const startMinute = TimeModel.timeToMinutes(startTime);

  // 使用共享逻辑生成时间段和价格段
  const { priceSegments } = generateTimeAndPriceSegments(startMinute, null, priceOptions, targetAmount, fallbackHourlyPrice);

  // 逐段累加价格，直到达到或超过目标金额
  const resultDetails: PriceDetail[] = [];
  let accumulatedPrice = 0;

  for (const segment of priceSegments) {
    if (accumulatedPrice >= targetAmount) {
      break;
    }

    // 此段能消费的金额
    const segmentMaxPrice = segment.durationMinutes * segment.pricePerMinute;
    const remainingAmount = targetAmount - accumulatedPrice;

    console.log(`[hourly] 处理价格段: ${segment.startTime}-${segment.endTime}, 段最大金额=${segmentMaxPrice}元, 剩余目标金额=${remainingAmount}元`);

    // 如果此段价格不足以达到目标，全部计入
    if (segmentMaxPrice <= remainingAmount) {
      accumulatedPrice += segmentMaxPrice;

      resultDetails.push({
        startTime: segment.startTime,
        endTime: segment.endTime,
        duration: segment.durationMinutes,
        price: Math.round(segmentMaxPrice),
        planName: segment.planName,
        priceType: segment.priceType,
        priority: segment.priority,
        segmentId: segment.segmentId
      });

      console.log(`[hourly] 全部计入价格段: ${segment.startTime}-${segment.endTime}, 累计金额=${accumulatedPrice}元`);
    }
    // 否则，只计入部分
    else {
      // 计算能消费的分钟数 - 改进舍入计算，避免超出太多
      // 通过targetAmount控制最终结果，而不是通过affordableMinutes
      const exactMinutes = remainingAmount / segment.pricePerMinute;
      // 一般情况下向上取整，但如果差距很小就四舍五入
      const affordableMinutes = exactMinutes % 1 > 0.95 ? Math.round(exactMinutes) : Math.floor(exactMinutes + 0.5); // 使用floor+0.5代替round，更精确控制

      // 直接使用目标剩余金额，避免二次舍入误差
      const actualPrice = Math.round(remainingAmount);

      accumulatedPrice += actualPrice;

      // 计算部分段的结束时间
      const partialEndMinute = TimeModel.timeToMinutes(segment.startTime) + affordableMinutes;
      const endTime = TimeModel.minutesToTime(partialEndMinute % TimeModel.MINUTES_PER_DAY);

      resultDetails.push({
        startTime: segment.startTime,
        endTime,
        duration: affordableMinutes,
        price: actualPrice,
        planName: segment.planName,
        priceType: segment.priceType,
        priority: segment.priority,
        segmentId: segment.segmentId
      });

      console.log(
        `[hourly] 部分计入价格段: ${segment.startTime}-${endTime}, 时长=${affordableMinutes}分钟, 实际金额=${actualPrice}元(原始=${remainingAmount}元), 累计金额=${accumulatedPrice}元`
      );
      break; // 已达到目标金额，结束循环
    }
  }

  // 检查并合并相邻的相同价格类型段
  const mergedDetails = mergeAdjacentPriceDetails(resultDetails);

  // 重新计算总价
  const totalPrice = mergedDetails.reduce((sum, detail) => sum + detail.price, 0);

  // 检查总价是否与目标金额一致，不一致时发出警告
  if (totalPrice !== targetAmount) {
    console.log(`[hourly] 注意: 最终计算金额(${totalPrice}元)与目标金额(${targetAmount}元)相差${Math.abs(totalPrice - targetAmount)}元`);
  }

  console.log('[hourly] 最终价格明细:', JSON.stringify(mergedDetails));

  return {
    totalPrice,
    details: mergedDetails
  };
}
