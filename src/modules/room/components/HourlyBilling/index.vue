<template>
  <div v-if="vm" class="hourly-billing-container">
    <div class="flex h-full">
      <!-- 左侧：计费方式选择 -->
      <div class="w-[300px] mr-[48px] h-full border border-gray-200 rounded-[10px]">
        <!-- 时段价格表格 - 符合设计稿样式 -->
        <div class="option-selector border-b border-gray-200 mt-[10px] pb-[10px]">
          <el-select v-model="vm.state.selectedPriceOption" class="w-full" placeholder="请选择价格方案" @change="vm.actions.onPriceOptionChange">
            <el-option v-for="option in vm.computed.mergedPriceOptions.value" :key="option.value" :label="option.label" :value="option.value" />
          </el-select>
        </div>

        <div class="price-plan-content">
          <!-- 时段价格列表 -->
          <div v-if="vm.state.selectedPriceOption">
            <template
              v-if="
                vm.computed.selectedPriceDetails && vm.computed.selectedPriceDetails.value && Object.keys(vm.computed.selectedPriceDetails.value).length > 0
              ">
              <div v-for="{ time, prices } in sortedPriceSlots" :key="time" class="price-slot-item" :class="{ 'current-time-slot': isCurrentTimeSlot(time) }">
                <span>{{ formatTimeRange(time) }} :</span>
                <span class="price-value"> {{ getPriceValue(prices, vm.state.selectedPriceOption).toFixed(2) }}<span class="unit">/小时</span> </span>
              </div>
            </template>
            <div v-else class="text-center text-gray-500 py-4">暂无时段价格数据</div>
          </div>
        </div>
      </div>
      <div class="flex-1">
        <el-tabs v-model="vm.state.billingType" stretch>
          <el-tab-pane label="时长" name="duration"></el-tab-pane>
          <el-tab-pane label="金额" name="amount"></el-tab-pane>
          <el-tab-pane label="计时" name="timer"></el-tab-pane>
          <el-tab-pane label="结束时间" name="endTime"></el-tab-pane>
        </el-tabs>

        <!-- 计时模式 -->
        <div v-if="vm.state && vm.state.billingType === 'timer'" class="mt-6">
          <div class="flex items-center w-[333px] h-[80px] border rounded-md bg-gray-100 text-[20px] text-[#aaa] justify-between items-center px-[16px]">
            <h3 class="text-base font-semibold">开始时间</h3>
            <span type="text" disabled>
              {{ formatTime(Number(vm.state.currentTime)) }}
            </span>
          </div>
        </div>

        <!-- 时长模式 -->
        <div v-if="vm.state && vm.state.billingType === 'duration'" class="mt-6">
          <div class="mb-3">
            <LabeledInputWithUnit
              class="w-[320px] rounded-md"
              v-model="vm.state.customDuration"
              label="时长"
              unit="分钟"
              placeholder="可输入"
              ref="durationInputRef"
              @blur="handleCustomDurationComplete"
              @enter="handleCustomDurationComplete"
              @input="handleCustomDurationInput" />
          </div>
          <!-- 时长限制提示信息 -->
          <div v-if="showDurationLimitWarning" class="mb-3 text-red-500 text-sm">时长最多开24h</div>
          <div class="grid grid-cols-6 gap-4 mt-4">
            <button
              v-for="duration in durationOptions"
              :key="duration.value"
              @click="handleDurationOptionClick(duration.value)"
              :class="getDurationButtonClass(duration)">
              {{ duration.label }}
            </button>
          </div>
        </div>

        <!-- 金额模式 -->
        <div v-if="vm.state && vm.state.billingType === 'amount'" class="mt-6">
          <LabeledInputWithUnit
            class="w-[320px] rounded-md"
            v-model="vm.state.inputAmount"
            label="金额"
            unit="元"
            placeholder="请输入金额"
            @blur="handleAmountComplete"
            @enter="handleAmountComplete"
            @input="handleAmountInput" />
          <p v-if="vm.computed.bill.value && vm.computed.bill.value.details && vm.computed.bill.value.details.length" class="mt-[24px] text-sm text-gray-700">
            当前金额可购买:
            <span class="font-bold">{{ vm.computed.bill.value.details.reduce((sum, item) => sum + item.duration, 0) }}</span> 分钟
          </p>
          <p v-if="vm.state && vm.state.eightHourPrice" :class="getAmountLimitClass()" class="mt-[24px] text-sm">
            购买时长不能超过24小时，价格上限为: {{ (vm.state.eightHourPrice || 0).toFixed(2) }} 元
          </p>
        </div>

        <!-- 结束时间模式 -->
        <div v-if="vm.state && vm.state.billingType === 'endTime'" class="mt-6">
          <div class="mb-2">
            <div class="option-time flex items-center mt-1">
              <el-time-select
                v-model="vm.state.endTimeForPicker"
                class="w-[320px]"
                start="00:00"
                step="00:15"
                end="23:45"
                placeholder="请选择结束时间"
                @change="vm.actions.updateEndTimeFromPicker" />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：价格方案与时段价格 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHourlyBillingPresenter } from './presenter';
import { formatYuan } from '@/utils/priceUtils';
import type { IHourlyBillingViewModel, PriceDetail } from './viewmodel';
import type { TimePricePlanVO } from '@/modules/room/entity/timePricePlan';
import LabeledInputWithUnit from '@/components/input/LabeledInputWithUnit.vue';
import { ref, watch, nextTick, computed } from 'vue';
import type { BillingTypeValue } from './viewmodel';
import { ElTabs, ElTabPane, ElSelect, ElOption, ElCollapse, ElCollapseItem, ElTimeSelect } from 'element-plus';

const props = defineProps({
  pricePlans: {
    type: Array as () => TimePricePlanVO[],
    required: true
  },
  areaId: {
    type: String,
    required: true
  },
  holidayVO: {
    type: Object,
    default: null
  },
  isOpenEnd: {
    type: Boolean,
    default: false
  },
  roomTypeVO: {
    type: Object,
    default: null
  },
  roomInfo: {
    type: Object,
    default: null
  },
  currentTime: {
    type: Number,
    default: () => Math.floor(Date.now() / 1000)
  },
  isActive: {
    type: Boolean,
    default: false
  },
  baseTimePriceFee: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update-bill']);

// 使用Presenter创建视图模型
const vm: IHourlyBillingViewModel = useHourlyBillingPresenter(props, emit);

// 计费方式选项
const billingTypes = [
  { label: '计时', value: 'timer' as BillingTypeValue },
  { label: '时长', value: 'duration' as BillingTypeValue },
  { label: '金额', value: 'amount' as BillingTypeValue },
  { label: '结束时间', value: 'endTime' as BillingTypeValue }
];

// 时长选项
const durationOptions = [
  { label: '30分钟', value: 30 as number | 'custom' },
  { label: '1小时', value: 60 as number | 'custom' },
  { label: '1.5小时', value: 90 as number | 'custom' },
  { label: '2小时', value: 120 as number | 'custom' },
  { label: '2.5小时', value: 150 as number | 'custom' },
  { label: '3小时', value: 180 as number | 'custom' },
  { label: '4小时', value: 240 as number | 'custom' },
  { label: '5小时', value: 300 as number | 'custom' },
  { label: '6小时', value: 360 as number | 'custom' },
  { label: '7小时', value: 420 as number | 'custom' },
  { label: '8小时', value: 480 as number | 'custom' },
  { label: '自定义', value: 'custom' as number | 'custom' }
];

// 添加时长限制警告状态
const showDurationLimitWarning = ref(false);

// 最大时长限制（24小时 = 1440分钟）
const MAX_DURATION_MINUTES = 1440;

// 监听tab变化
watch(
  () => vm.state.billingType,
  newType => {
    if (vm.actions && vm.actions.changeBillingType) {
      vm.actions.changeBillingType(newType);
    }
  }
);

// 时长输入框引用
const durationInputRef = ref<any>(null);

// 处理自定义时长输入
function handleCustomDurationInput(event: Event) {
  if (vm && vm.state) {
    let inputDuration = Number(vm.state.customDuration);
    let wasLimited = false;

    // 检查是否超过最大时长限制
    if (inputDuration > MAX_DURATION_MINUTES) {
      inputDuration = MAX_DURATION_MINUTES;
      vm.state.customDuration = String(MAX_DURATION_MINUTES);
      wasLimited = true;
    }

    // 显示或隐藏警告信息
    showDurationLimitWarning.value = wasLimited;
    if (wasLimited) {
      // 3秒后隐藏警告信息
      setTimeout(() => {
        showDurationLimitWarning.value = false;
      }, 3000);
    }

    // 检查输入的时长是否匹配预设选项
    const matchedOption = durationOptions.find(option => typeof option.value === 'number' && option.value === inputDuration);

    if (matchedOption) {
      // 如果匹配到预设选项，选中该选项
      vm.state.selectedDuration = matchedOption.value as number;
      vm.state.isCustomDuration = false;
      // 调用actions中的方法来更新计算
      if (vm.actions && vm.actions.onCustomDurationComplete) {
        vm.actions.onCustomDurationComplete();
      }
    } else {
      // 如果不匹配任何预设选项，选中"自定义"选项
      vm.state.isCustomDuration = true;
      // 如果输入的是有效数字，更新selectedDuration
      if (!isNaN(inputDuration) && inputDuration > 0) {
        vm.state.selectedDuration = inputDuration;
      }
    }
  }
}

// 处理自定义时长完成
function handleCustomDurationComplete() {
  if (vm && vm.actions && vm.actions.onCustomDurationComplete) {
    vm.actions.onCustomDurationComplete();
  }
}

// 处理时长选项点击
function handleDurationOptionClick(value: number | 'custom') {
  if (vm && vm.state) {
    if (value === 'custom') {
      // 如果点击的是"自定义"选项
      vm.state.isCustomDuration = true;

      // 聚焦到输入框
      nextTick(() => {
        if (durationInputRef.value) {
          // 尝试获取内部的输入元素并聚焦
          const inputElement = durationInputRef.value.$el.querySelector('input');
          if (inputElement) {
            inputElement.focus();
          } else {
            // 如果无法直接获取输入元素，尝试调用组件的focus方法
            if (typeof durationInputRef.value.focus === 'function') {
              durationInputRef.value.focus();
            }
          }
        }
      });
    } else if (typeof value === 'number') {
      // 如果点击的是数值选项
      vm.state.selectedDuration = value;
      vm.state.isCustomDuration = false;
      vm.state.customDuration = String(value);

      // 调用actions中的方法来更新计算
      if (vm.actions && vm.actions.onCustomDurationComplete) {
        vm.actions.onCustomDurationComplete();
      }
    }
  }
}

// 获取时长按钮的样式类
function getDurationButtonClass(duration: any) {
  const baseClass = 'py-2 rounded-[40px] text-center text-sm w-[100px] h-[52px]';

  if (duration.value === 'custom') {
    return [baseClass, vm.state && vm.state.isCustomDuration ? 'bg-red-500 text-white' : 'bg-gray-100'];
  } else {
    return [baseClass, vm.state && vm.state.selectedDuration === duration.value ? 'bg-red-500 text-white' : 'bg-gray-100'];
  }
}

// 获取金额限制的样式类
function getAmountLimitClass() {
  if (vm.state && vm.state.isOverEightHours) {
    return 'text-red-500';
  }
  return 'text-gray-500';
}

// 处理金额输入完成
function handleAmountComplete() {
  if (vm && vm.actions && vm.actions.onAmountComplete) {
    vm.actions.onAmountComplete();
  }
}

function updateSelectedPriceOption(event: Event) {
  const target = event.target as HTMLSelectElement;
  if (vm && vm.state) {
    vm.state.selectedPriceOption = target.value as any;
    if (vm.actions && vm.actions.onPriceOptionChange) {
      vm.actions.onPriceOptionChange();
    }
  }
}

// 格式化时间显示
function formatTime(timestamp: number): string {
  console.log('formatTime函数输入:', timestamp, typeof timestamp);

  if (!timestamp || isNaN(timestamp)) {
    console.error('无效的时间戳:', timestamp);
    return '00:00';
  }

  try {
    const date = new Date(timestamp * 1000); // 将秒转换为毫秒
    console.log('formatTime创建的日期对象:', date);

    if (isNaN(date.getTime())) {
      console.error('创建的日期无效');
      return '00:00';
    }

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  } catch (e) {
    console.error('格式化时间出错:', e);
    return '00:00';
  }
}

// 格式化日期时间为输入框格式
function formatDateTimeForInput(timestamp: number): string {
  const date = new Date(timestamp * 1000);
  return date.toISOString().slice(0, 16);
}

// 计算默认结束时间（当前时间 + 1小时）
function calculateDefaultEndTime(): number {
  return Math.floor(Date.now() / 1000) + 3600;
}

// 添加判断当前时段的方法
function isCurrentTimeSlot(timeRange: string): boolean {
  if (!timeRange) return false;

  const now = new Date();
  const currentHour = now.getHours();
  const currentMinutes = now.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinutes;

  // 处理分隔符格式: "HH:MM - HH:MM"
  const parts = timeRange.split(' - ');
  if (parts.length !== 2) return false;

  const [startTime, endTime] = parts;
  const startParts = startTime.split(':');
  const endParts = endTime.split(':');

  if (startParts.length !== 2 || endParts.length !== 2) return false;

  const startHour = parseInt(startParts[0], 10);
  const startMinute = parseInt(startParts[1], 10);
  const endHour = parseInt(endParts[0], 10);
  const endMinute = parseInt(endParts[1], 10);

  if (isNaN(startHour) || isNaN(startMinute) || isNaN(endHour) || isNaN(endMinute)) return false;

  const startTimeInMinutes = startHour * 60 + startMinute;
  let endTimeInMinutes = endHour * 60 + endMinute;

  // 处理跨午夜的情况 (例如 22:00 - 02:00)
  if (endTimeInMinutes <= startTimeInMinutes) {
    endTimeInMinutes += 24 * 60; // 加24小时

    // 如果当前时间小于起始时间，说明已经过了午夜，需要调整当前时间
    if (currentTimeInMinutes < startTimeInMinutes) {
      return currentTimeInMinutes + 24 * 60 >= startTimeInMinutes && currentTimeInMinutes + 24 * 60 < endTimeInMinutes;
    } else {
      return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes < endTimeInMinutes;
    }
  } else {
    return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes < endTimeInMinutes;
  }
}

// 添加格式化时间段的方法
const formatTimeRange = (timeRange: string) => {
  if (!timeRange) return '';
  // 保持时间范围格式为 "HH:MM - HH:MM"
  return timeRange;
};

function getPriceValue(prices: Record<string, number>, priceOption: string): number {
  // 直接返回价格值，如果不存在则返回0
  return prices[priceOption] || 0;
}

function handleAmountInput(event: Event) {
  const target = event.target as HTMLInputElement;
  if (vm && vm.state) {
    vm.state.inputAmount = target.value;
    if (vm.actions && vm.actions.onAmountInput) {
      vm.actions.onAmountInput();
    }
  }
}

// 对时段价格列表进行排序的计算属性
const sortedPriceSlots = computed(() => {
  if (!vm.computed || !vm.computed.selectedPriceDetails || !vm.computed.selectedPriceDetails.value) {
    return [];
  }

  const priceDetails = vm.computed.selectedPriceDetails.value;
  const slots = Object.entries(priceDetails).map(([time, prices]) => ({
    time,
    prices,
    startTime: parseStartTime(time)
  }));

  // 分离全时段价格和正常时段 (支持不同格式)
  const normalSlots = slots.filter(slot => slot.time !== '00:00 - 00:00' && slot.time !== '00:00-00:00');
  const fallbackSlots = slots.filter(slot => slot.time === '00:00 - 00:00' || slot.time === '00:00-00:00');

  // 对正常时段按开始时间排序（从小到大：06:00 在前，23:00 在后）
  normalSlots.sort((a, b) => a.startTime - b.startTime);

  // 全时段价格放在最后
  return [...normalSlots, ...fallbackSlots];
});

// 解析时间字符串为分钟数（用于排序）
const parseStartTime = (timeRange: string): number => {
  if (!timeRange) return 0;

  // 处理不同的分隔符格式: "HH:MM - HH:MM" 或 "HH:MM-HH:MM"
  let startTimeStr = '';
  if (timeRange.includes(' - ')) {
    startTimeStr = timeRange.split(' - ')[0];
  } else if (timeRange.includes('-')) {
    startTimeStr = timeRange.split('-')[0];
  } else {
    console.warn('Unknown time format:', timeRange);
    return 0;
  }

  startTimeStr = startTimeStr.trim();
  const timeParts = startTimeStr.split(':');

  if (timeParts.length !== 2) {
    console.warn('Invalid time format:', startTimeStr);
    return 0;
  }

  const [hours, minutes] = timeParts.map(Number);

  if (isNaN(hours) || isNaN(minutes)) {
    console.warn('Invalid time values:', hours, minutes);
    return 0;
  }

  return hours * 60 + minutes;
};
</script>

<style scoped>
.hourly-billing-container {
  max-width: 100%;
  min-height: 500px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
}

:deep(.el-tabs__item) {
  font-size: 20px;
  width: 25%;
  /* 设置每个标签为25%宽度，确保4个标签平均分布 */
  text-align: center;
  /* 文字居中 */
  padding: 0;
  /* 移除默认的padding */
  color: #666;
}

:deep(.el-tabs__item.is-active) {
  color: #e53e3e;
}

:deep(.el-tabs__nav) {
  width: 100%;
  /* 确保导航栏占满整个宽度 */
  display: flex;
  /* 使用flex布局 */
  height: 60px;
}

:deep(.el-tabs__header) {
  width: 100%;
  /* 确保头部区域占满整个宽度 */
  margin-bottom: 15px;
  /* 可以调整这个值来改变标签栏与内容区的间距 */
}

.price-plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
  color: #e53e3e;
  font-size: 16px;
  font-weight: 500;
}

.price-plan-content {
  padding: 0;
}

.price-slot-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.price-slot-item:last-child {
  border-bottom: none;
}

.price-value {
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.unit {
  font-size: 12px;
  color: #999;
  font-weight: normal;
  margin-left: 2px;
}

.current-time-slot {
  color: #e53e3e;
  font-weight: 500;
}

.current-time-slot .price-value {
  color: #e53e3e;
}

.current-time-slot .unit {
  color: #e53e3e;
}

:deep(.option-selector .el-select__wrapper) {
  border: none;
  box-shadow: none;
}

:deep(.option-time .el-select__wrapper) {
  height: 80px;
  width: 320px;
  font-size: 20px;
}
</style>
