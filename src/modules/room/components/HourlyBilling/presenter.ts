import { reactive, computed, watch, onMounted } from 'vue';
import type { TimePricePlanVO } from '@/modules/room/entity/timePricePlan';
import { convertToYuan, convertToFen, formatYuan } from '@/utils/priceUtils';
import { HourlyBillingConverter } from './converter';
import { useHourlyBillingInteractor } from './interactor';
import type {
  IHourlyBillingViewModel,
  BillingTypeOption,
  BillingTypeValue,
  PriceTypeKey,
  IHourlyBillingState,
  IHourlyBillingComputed,
  IHourlyBillingActions,
  TimeSlotPrice,
  PriceOption,
  PriceDetail
} from './viewmodel';
import type { Price } from './types';
import { HourlyBillingInteractor } from './interactor';
import { PriceCalculator } from './PriceCalculator';
import * as TimeModel from './TimeModel';
import * as PriceModel from './PriceModel';
import { TimeSegment } from './types';

// 定义FormattedTimeSegment，补充缺失的类型
interface FormattedTimeSegment extends PriceDetail {
  day?: number;
  date?: string;
}

export class HourlyBillingPresenter implements IHourlyBillingViewModel {
  // 公共属性定义
  public billingTypes: BillingTypeOption[] = [
    { value: 'duration', label: '时长' },
    { value: 'amount', label: '金额' },
    { value: 'endTime', label: '结束时间' },
    { value: 'timer', label: '计时' }
  ];

  public quickDurations: (number | 'custom')[] = [30, 60, 120, 180, 240, 300, 360, 420, 480, 'custom'];

  public formatYuan = formatYuan;
  public formatDuration = HourlyBillingConverter.formatDuration;

  // 私有属性
  private previousValidOptions: string[] = [];
  private interactor = useHourlyBillingInteractor();

  // 组件状态
  public state: IHourlyBillingState = reactive({} as IHourlyBillingState);

  // 计算属性
  public computed: IHourlyBillingComputed = {} as IHourlyBillingComputed;

  // 动作
  public actions: IHourlyBillingActions = {} as IHourlyBillingActions;

  constructor(
    public props: {
      pricePlans: TimePricePlanVO[];
      areaId: string;
      holidayVO: any;
      isOpenEnd: boolean;
      roomTypeVO: any;
      roomInfo: any;
      currentTime: number | null;
      isActive: boolean;
      baseTimePriceFee?: number; // 添加兜底价格字段
    },
    public emit: (event: 'update-bill', ...args: any[]) => void
  ) {
    // 初始化state
    const currentTimeValue = props.currentTime ?? Math.floor(Date.now() / 1000);

    this.state = reactive(HourlyBillingConverter.createInitialState(currentTimeValue, props.isOpenEnd ?? false));

    // 初始化computed
    this.initializeComputed();

    // 初始化actions
    this.initializeActions();

    // 设置观察者和初始化组件
    this.setupWatchers();
    this.initializeComponent();
  }

  // 初始化计算属性
  private initializeComputed() {
    this.computed = {
      basePriceForArea: computed(() => {
        // 使用interactor获取基础价格
        const currentTime = this.props.currentTime ?? Math.floor(Date.now() / 1000);
        const startTime = this.state.startTime;
        const selectedDuration = this.state.selectedDuration;

        return this.interactor.getBasePriceForArea(
          this.props.roomTypeVO,
          this.props.areaId,
          this.props.pricePlans || [],
          startTime,
          selectedDuration,
          this.state.businessHours,
          currentTime
        );
      }),

      validPriceOptions: computed(() => {
        const currentTime = this.props.currentTime ?? Math.floor(Date.now() / 1000);
        const startTime = this.state.startTime;
        const selectedDuration = this.state.selectedDuration;
        const roomTypeId = this.props.roomTypeVO?.id || '';

        return this.interactor.getValidPriceOptions(
          this.props.pricePlans || [],
          this.props.areaId,
          this.props.holidayVO,
          roomTypeId,
          startTime,
          selectedDuration,
          this.state.businessHours,
          currentTime
        );
      }),

      allTimeSlotPrices: computed(() => {
        const currentTime = this.props.currentTime ?? Math.floor(Date.now() / 1000);
        const roomTypeId = this.props.roomTypeVO?.id || '';

        // 获取所有时段价格，包括不参与计算的
        return this.interactor.getAllTimeSlotPrices(
          this.props.pricePlans || [],
          this.props.areaId,
          this.props.holidayVO,
          roomTypeId,
          currentTime,
          this.props.baseTimePriceFee || 0
        );
      }),

      mergedPriceOptions: computed(() => {
        const priceTypes = ['baseRoomFee', 'activityFee', 'birthdayFee', 'groupBuyFee'] as PriceTypeKey[];
        return priceTypes.map(type => ({
          label: HourlyBillingConverter.getPriceTypeLabel(type),
          value: type
        }));
      }),

      selectedPriceDetails: computed(() => {
        const result: Record<string, Record<string, number>> = {};

        // 使用allTimeSlotPrices来构建时段价格显示数据
        this.computed.allTimeSlotPrices.value.forEach(slot => {
          const timeKey = `${slot.timeRange.start}-${slot.timeRange.end}`;

          // 确保timeKey下的对象存在
          if (!result[timeKey]) {
            result[timeKey] = {
              baseRoomFee: 0,
              birthdayFee: 0,
              activityFee: 0,
              groupBuyFee: 0
            };
          }

          // 直接使用原始价格数据，不再进行转换
          // allTimeSlotPrices已经将价格从分转换为元
          Object.keys(slot.prices).forEach(priceType => {
            const price = slot.prices[priceType as PriceTypeKey] || 0;
            if (price > 0) {
              result[timeKey][priceType] = price;
            }
          });
        });
        return result;
      }),

      currentProductList: computed(() => {
        // 获取当前有效的价格选项
        const validOptions = this.computed.validPriceOptions.value || [];
        return this.interactor.getCurrentProductList(validOptions, this.state.selectedPriceOption, this.props.pricePlans || []);
      }),

      currentApplicablePlan: computed(() => {
        const currentTime = this.props.currentTime ?? Math.floor(Date.now() / 1000);
        return this.interactor.getCurrentApplicablePlan(this.props.pricePlans || [], currentTime);
      }),

      bill: computed(() => {
        if (this.state.billingType === 'amount') {
          // 根据金额计算价格
          const amount = parseFloat(this.state.inputAmount || '0');
          if (amount <= 0) {
            return { totalPrice: 0, details: [] };
          }

          // 预处理价格数据，与其他模式统一使用prepareValidPrices
          const validPrices = this.getProcessedPrices();

          // 直接使用PriceCalculator.calculatePriceDetailsFromAmount进行计算
          // 传递预处理好的价格数据
          return this.interactor.calculatePriceDetailsFromAmount(
            amount,
            this.state.startTime,
            this.getStartTimestamp(),
            [], // 不再需要原始的validPriceOptions
            this.state.selectedPriceOption,
            this.computed.basePriceForArea.value,
            this.props.baseTimePriceFee || 0,
            this.props.pricePlans || [],
            this.props.areaId,
            this.props.holidayVO,
            this.props.roomTypeVO?.id || '',
            validPrices // 传递预处理好的价格数据
          );
        } else if (this.state.billingType === 'endTime') {
          // 根据结束时间计算价格
          if (!this.state.endTime || this.state.selectedDuration <= 0) {
            return { totalPrice: 0, details: [] };
          }

          // 修改为使用开始时间+计算出的时长方式
          // 计算结束时间，与duration模式保持一致
          const endTime = this.interactor.addMinutesToTime(this.state.startTime, this.state.selectedDuration);

          // 预处理价格数据
          const validPrices = this.getProcessedPrices();

          // 使用开始时间和计算出的endTime进行价格计算
          return PriceCalculator.calculatePriceByDuration(this.state.startTime, endTime, validPrices, this.props.baseTimePriceFee || 0);
        } else if (this.state.billingType === 'timer') {
          // 计时模式
          console.log('计时模式');
          // 预处理价格数据
          const validPrices = this.getProcessedPrices();
          if (validPrices && validPrices.length > 0) {
            return { totalPrice: 0, details: [] };
          }
          // 预处理价格数据，与其他模式统一使用prepareValidPrices
          // const validPrices = this.getProcessedPrices();

          // // 使用interactor调用，传递预处理好的价格数据
          // return this.interactor.calculatePriceDetailsForTimer(
          //   this.state.startTime,
          //   this.getStartTimestamp(),
          //   [], // 不再需要原始的validPriceOptions
          //   this.state.selectedPriceOption,
          //   this.computed.basePriceForArea.value,
          //   this.props.baseTimePriceFee || 0,
          //   this.props.pricePlans || [],
          //   this.props.areaId,
          //   this.props.holidayVO,
          //   this.props.roomTypeVO?.id || '',
          //   validPrices // 传递预处理好的价格数据
          // );
        } else {
          // 根据时长计算价格（默认模式）
          const duration = this.state.selectedDuration;
          if (duration <= 0) {
            return { totalPrice: 0, details: [] };
          }

          // 计算结束时间
          const endTime = this.interactor.addMinutesToTime(this.state.startTime, duration);

          // 预处理价格数据
          const validPrices = this.getProcessedPrices();

          // 调用价格计算方法
          return PriceCalculator.calculatePriceByDuration(this.state.startTime, endTime, validPrices, this.props.baseTimePriceFee || 0);
        }
      })
    };
  }

  // 初始化动作
  private initializeActions() {
    this.actions = {
      changeBillingType: (type: BillingTypeValue) => {
        this.state.billingType = type;

        // 重置相关状态
        if (type === 'timer') {
          this.updateTimerBill();
          return;
        } else if (type === 'duration') {
          this.state.inputAmount = '';
          this.state.endTime = null;
        } else if (type === 'amount') {
          this.state.endTime = null;
        } else if (type === 'endTime') {
          this.state.inputAmount = '';
          // 如果切换到结束时间模式，初始化默认结束时间
          this.actions.initDefaultEndTime();
        }
        // 触发价格更新
        this.updateBill();
      },

      selectDuration: (duration: number | 'custom') => {
        if (duration === 'custom') {
          this.state.isCustomDuration = true;
        } else {
          this.state.selectedDuration = duration;
          this.state.customDuration = String(duration);
          this.state.isCustomDuration = false;

          // 触发价格更新
          this.updateBill();
        }
      },

      onCustomDurationComplete: () => {
        const customDuration = parseInt(this.state.customDuration);
        if (!isNaN(customDuration) && customDuration > 0) {
          this.state.selectedDuration = customDuration;

          // 触发价格更新
          this.updateBill();
        }
      },

      onAmountInput: () => {
        // 处理金额输入时的逻辑
        this.validateAmount();
      },

      onAmountComplete: () => {
        // 处理金额输入完成时的逻辑
        this.validateAmount();
        this.updateBill();
      },

      onPriceOptionChange: () => {
        // 处理价格选项变化
        this.updateBill();
      },

      calculateDurationAndPrice: () => {
        // 根据结束时间计算时长和价格
        if (this.state.endTime) {
          // 处理HH:MM格式的时间
          if (typeof this.state.endTime === 'string' && this.state.endTime.includes(':')) {
            const [startHour, startMinute] = this.state.startTime.split(':').map(Number);
            const [endHour, endMinute] = this.state.endTime.split(':').map(Number);

            // 创建时间对象以计算时间差
            const now = new Date();
            const startDate = new Date(now);
            startDate.setHours(startHour, startMinute, 0, 0);

            const endDate = new Date(now);
            endDate.setHours(endHour, endMinute, 0, 0);

            // 如果结束时间早于开始时间，则假设是第二天
            if (endDate < startDate) {
              endDate.setDate(endDate.getDate() + 1);
            }

            // 计算时间差（分钟）
            const durationInMinutes = Math.ceil((endDate.getTime() - startDate.getTime()) / (60 * 1000));

            if (durationInMinutes > 0) {
              this.state.selectedDuration = durationInMinutes;
              this.state.customDuration = String(durationInMinutes);

              // 触发价格更新
              this.updateBill();
            }
          }
          // 原有逻辑，处理时间戳
          else {
            const endTimestamp = Number(this.state.endTime);
            const startTimestamp = this.getStartTimestamp();

            if (endTimestamp > startTimestamp) {
              const durationInSeconds = endTimestamp - startTimestamp;
              const durationInMinutes = Math.ceil(durationInSeconds / 60);

              this.state.selectedDuration = durationInMinutes;
              this.state.customDuration = String(durationInMinutes);

              // 触发价格更新
              this.updateBill();
            }
          }
        }
      },

      // 新增 updateEndTimeFromPicker 方法
      updateEndTimeFromPicker: (selectedTime: string) => {
        if (this.state) {
          // 更新state中的选择器时间
          this.state.endTimeForPicker = selectedTime;

          // 获取当前时间
          const currentDate = new Date();

          // 获取开始时间和结束时间
          const [startHour, startMinute] = this.state.startTime.split(':').map(Number);
          const [endHour, endMinute] = selectedTime.split(':').map(Number);

          // 创建开始和结束的Date对象
          const startDate = new Date(currentDate);
          startDate.setHours(startHour, startMinute, 0, 0);

          const endDate = new Date(currentDate);
          endDate.setHours(endHour, endMinute, 0, 0);

          // 计算时长（分钟）
          let durationMs;

          // 判断是否跨天（结束时间早于开始时间）
          if (endDate < startDate) {
            // 跨天情况：结束时间加一天
            const nextDayEndDate = new Date(endDate);
            nextDayEndDate.setDate(nextDayEndDate.getDate() + 1);
            durationMs = nextDayEndDate.getTime() - startDate.getTime();
          } else {
            // 同一天情况
            durationMs = endDate.getTime() - startDate.getTime();
          }

          const durationMinutes = Math.floor(durationMs / (60 * 1000));

          // 更新选中的时长
          this.state.selectedDuration = durationMinutes;
          this.state.customDuration = String(durationMinutes);

          // 更新state中的endTime（HH:MM格式）
          this.state.endTime = selectedTime;

          // 确保触发账单更新
          this.actions.onCustomDurationComplete();
        }
      },

      // 新增 initDefaultEndTime 方法
      initDefaultEndTime: () => {
        if (this.state) {
          // 获取当前时间
          const now = new Date();

          // 计算1小时后的时间
          const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);

          // 向上取整到最近的15分钟
          const minutes = oneHourLater.getMinutes();
          const roundedMinutes = Math.ceil(minutes / 15) * 15;
          oneHourLater.setMinutes(roundedMinutes);
          oneHourLater.setSeconds(0);

          // 格式化为HH:MM格式
          const endHour = oneHourLater.getHours();
          const endMinute = oneHourLater.getMinutes();
          const endTimeStr = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

          // 设置选择器的值
          this.state.endTimeForPicker = endTimeStr;

          // 设置结束时间
          this.state.endTime = endTimeStr;

          // 计算时长（从开始时间到选择的结束时间）
          const startTimeArr = this.state.startTime.split(':').map(Number);
          const [startHour, startMinute] = startTimeArr;

          const startDate = new Date(now);
          startDate.setHours(startHour, startMinute, 0, 0);

          // 计算时长（分钟）
          let durationMs;

          // 判断是否跨天（结束时间早于开始时间）
          if (oneHourLater < startDate) {
            // 跨天情况：结束时间加一天
            const nextDayEndDate = new Date(oneHourLater);
            nextDayEndDate.setDate(nextDayEndDate.getDate() + 1);
            durationMs = nextDayEndDate.getTime() - startDate.getTime();
          } else {
            // 同一天情况
            durationMs = oneHourLater.getTime() - startDate.getTime();
          }

          const durationMinutes = Math.floor(durationMs / (60 * 1000));

          // 更新选中的时长
          this.state.selectedDuration = durationMinutes;
          this.state.customDuration = String(durationMinutes);

          // 确保触发账单更新
          this.actions.onCustomDurationComplete();
        }
      }
    };
  }

  // 设置观察者
  private setupWatchers() {
    // 监听价格选项变化
    watch(
      () => this.state.selectedPriceOption,
      () => {
        this.updateBill();
      }
    );

    // 监听时长变化
    watch(
      () => this.state.selectedDuration,
      () => {
        if (this.state.billingType === 'duration') {
          this.updateBill();
        }
      }
    );
  }

  // 初始化组件
  private initializeComponent() {
    // 异步更新价格，确保组件已完全挂载
    setTimeout(() => {
      this.updateBill();
    }, 0);
  }

  // 获取开始时间戳
  private getStartTimestamp(): number {
    const currentDate = new Date((this.props.currentTime ?? Math.floor(Date.now() / 1000)) * 1000);
    const [hours, minutes] = this.state.startTime.split(':').map(Number);

    currentDate.setHours(hours, minutes, 0, 0);
    return Math.floor(currentDate.getTime() / 1000);
  }

  // 计算24小时的价格限制
  private calculateEightHourPrice(): number {
    try {
      // 计算24小时（1440分钟）的价格
      const endTime = this.interactor.addMinutesToTime(this.state.startTime, 1440);
      const validPrices = this.getProcessedPrices();
      const result = PriceCalculator.calculatePriceByDuration(
        this.state.startTime, 
        endTime, 
        validPrices, 
        this.props.baseTimePriceFee || 0
      );
      return result.totalPrice;
    } catch (error) {
      console.error('计算24小时价格出错:', error);
      return 0;
    }
  }

  // 验证金额
  private validateAmount() {
    if (!this.state.inputAmount) return;

    const amount = parseFloat(this.state.inputAmount);
    if (isNaN(amount) || amount <= 0) {
      this.state.inputAmount = '';
      return;
    }

    // 计算24小时价格限制
    const eightHourPrice = this.calculateEightHourPrice();
    this.state.eightHourPrice = eightHourPrice;

    // 检查24小时价格限制
    if (eightHourPrice > 0 && amount > eightHourPrice) {
      this.state.isOverEightHours = true;
      // 自动将输入框的值修改为计算出的24小时价格
      this.state.inputAmount = eightHourPrice.toFixed(2);
    } else {
      this.state.isOverEightHours = false;
    }
  }

  private updateTimerBill() {
    const billUpdateEvent = {
      consumptionMode: 'timeCharge',
      isTimeConsume: true,
      minimumCharge: this.state.customMinimum ? convertToFen(this.state.customMinimum) : 0,
      roombill: {
        totalPrice: 0, // 将元转为分
        details: [
          {
            planName: '计时模式',
            startTime: this.state.startTime,
            endTime: 0,
            duration: 0,
            price: 0,
            isTimeConsume: true
          }
        ],
        minimumCharge: this.state.customMinimum ? convertToFen(this.state.customMinimum) : 0
      },
      marketBill: null,
      timeRange: {
        currentDate: HourlyBillingConverter.getCurrentDate(this.props.currentTime ?? Math.floor(Date.now() / 1000)),
        startTime: this.state.startTime,
        endTime: this.state.endTime,
        duration: 0
      }
    };
    this.emit('update-bill', billUpdateEvent);
  }

  // 更新账单
  private updateBill() {
    const priceResult = this.computed.bill.value;
    if (!priceResult || priceResult.totalPrice <= 0 || priceResult.details.length === 0) return;

    // 计算总时长 - 对于金额计算模式，需要从明细中计算总时长
    let totalDuration = this.state.selectedDuration; // 默认使用选中时长

    // 如果是金额计算模式，需要从价格明细中计算总时长
    if (this.state.billingType === 'amount') {
      totalDuration = priceResult.details.reduce((sum, detail) => sum + detail.duration, 0);
      // 同步更新state中的时长，保持一致性
      this.state.selectedDuration = totalDuration;
      this.state.customDuration = String(totalDuration);
    }

    // 获取结束时间
    const startTimeArr = this.state.startTime.split(':').map(Number);
    const startMinutes = startTimeArr[0] * 60 + startTimeArr[1];
    const endMinutes = startMinutes + totalDuration; // 使用计算出的总时长
    const endHours = Math.floor(endMinutes / 60) % 24;
    const endMins = endMinutes % 60;
    const formattedEndTime = `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;

    // 将价格从元转回分
    const convertedDetails = priceResult.details.map((detail: PriceDetail) => ({
      ...detail,
      price: convertToFen(detail.price) // 将元转为分
    }));

    // 构建更新事件
    const billUpdateEvent = {
      consumptionMode: 'timeCharge',
      minimumCharge: this.state.customMinimum ? convertToFen(this.state.customMinimum) : 0,
      roombill: {
        totalPrice: convertToFen(priceResult.totalPrice), // 将元转为分
        details: convertedDetails,
        minimumCharge: this.state.customMinimum ? convertToFen(this.state.customMinimum) : 0
      },
      marketBill: this.computed.currentProductList.value,
      timeRange: {
        currentDate: HourlyBillingConverter.getCurrentDate(this.props.currentTime ?? Math.floor(Date.now() / 1000)),
        startTime: this.state.startTime,
        endTime: formattedEndTime,
        duration: totalDuration // 使用计算出的总时长
      }
    };

    // 发出更新事件
    this.emit('update-bill', billUpdateEvent);
  }

  /**
   * 获取处理后的价格数据
   */
  private getProcessedPrices(): Price[] {
    // 使用预处理函数获取价格
    return this.prepareValidPrices(
      this.computed.validPriceOptions.value,
      this.state.selectedPriceOption,
      this.computed.basePriceForArea.value,
      this.props.baseTimePriceFee || 0,
      this.props.pricePlans || [],
      this.props.areaId,
      this.props.holidayVO,
      this.props.roomTypeVO?.id || ''
    );
  }

  /**
   * 准备有效的价格数据，用于新的计算方法
   */
  private prepareValidPrices(
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePriceForArea: { baseRoomFee: number; birthdayFee: number; activityFee: number; groupBuyFee: number },
    baseTimePriceFee: number = 0,
    pricePlans: TimePricePlanVO[] = [],
    areaId: string = '',
    holidayVO: any = null,
    roomTypeId: string = ''
  ): Price[] {
    const currentTime = this.props.currentTime ?? Math.floor(Date.now() / 1000);
    // 获取时段价格
    const allTimeSlotPrices = this.interactor.getAllTimeSlotPrices(
      pricePlans,
      areaId,
      holidayVO,
      roomTypeId,
      currentTime,
      baseTimePriceFee // 传递原始值，在interactor里处理
    );

    // 筛选出与用户选择类型匹配的价格项
    const matchingPrices = allTimeSlotPrices.filter(price => {
      return price.prices[selectedPriceOption] > 0;
    });

    // 如果没有匹配的价格项，使用所有价格项
    const pricesToUse = matchingPrices.length > 0 ? matchingPrices : allTimeSlotPrices;

    // 转换为Price格式，保留原始价格类型
    const result = [];

    // 遍历每个价格项
    for (const price of pricesToUse) {
      // 对于每个价格项，分析它的价格类型
      const priceTypes: PriceTypeKey[] = ['baseRoomFee', 'birthdayFee', 'activityFee', 'groupBuyFee'];

      // 优先添加选中的价格类型
      if (price.prices[selectedPriceOption] > 0) {
        result.push({
          priceType: selectedPriceOption,
          planName: price.planName || '买钟',
          price: price.prices[selectedPriceOption],
          hourCount: 1,
          priority: 0, // 选中类型优先级最高
          datePlanType: 'date',
          timeSlots: [
            {
              startTime: price.timeRange.start,
              endTime: price.timeRange.end
            }
          ]
        });
      }
      // 如果选中类型不存在或为0，且这是非匹配价格项的情况
      else if (matchingPrices.length === 0) {
        // 添加其他有效价格类型（价格大于0）
        for (const type of priceTypes) {
          if (price.prices[type] > 0) {
            result.push({
              priceType: type,
              planName: price.planName || '买钟',
              price: price.prices[type],
              hourCount: 1,
              priority: type === 'baseRoomFee' ? 1 : 2, // 基础价格优先级次之
              datePlanType: 'date',
              timeSlots: [
                {
                  startTime: price.timeRange.start,
                  endTime: price.timeRange.end
                }
              ]
            });
          }
        }
      }
    }

    return result;
  }

  /**
   * 计算价格
   * 基于循环时间模型重构，不再在午夜00:00处强制切割
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param date 日期
   * @param businessHours 营业时间配置
   * @returns 计算结果
   */
  calculatePriceByDuration(
    startTime: string,
    endTime: string,
    date: Date = new Date(),
    businessHours?: { openTime: string; closeTime: string; isOvernight: boolean }
  ): { price: number; formattedSegments: FormattedTimeSegment[] } {
    // 获取营业时间配置
    const bHours = businessHours || this.getBusinessHoursConfig();

    // 检查是否为全天时间范围
    const isFullDay = TimeModel.isFullDayRange(startTime, endTime);

    // 直接使用循环时间模型进行计算
    const validPrices = this.getProcessedPrices();
    const result = PriceCalculator.calculatePriceByDuration(
      startTime,
      isFullDay ? TimeModel.addMinutesToTime(startTime, 24 * 60) : endTime, // 如果是全天，使用明确的24小时时长
      validPrices,
      this.props.baseTimePriceFee || 0
    );

    // 格式化时段明细，处理日期
    const formattedSegments = result.details.map(detail => {
      // 检查是否跨天 - 使用循环时间模型
      const segmentStart = detail.startTime;
      const segmentEnd = detail.endTime;
      const timeRange = TimeModel.normalizeTimeStringRange(segmentStart, segmentEnd);
      const isCrossDay = timeRange.isCrossDay;

      // 根据时间段确定日期
      const segmentDate = this.formatDateForSegment(date, segmentStart);

      return {
        ...detail,
        // 如果是跨天时间段且结束时间在开始时间之前，标记为次日
        day: isCrossDay && TimeModel.timeToMinutes(segmentEnd) < TimeModel.timeToMinutes(segmentStart) ? 1 : 0,
        date: segmentDate
      };
    });

    return {
      price: result.totalPrice,
      formattedSegments
    };
  }

  /**
   * 获取当前的营业时间配置
   * 在实际应用中，这个方法可以从全局配置或API中获取营业时间配置
   * @returns 营业时间配置
   */
  getBusinessHoursConfig(): { openTime: string; closeTime: string; isOvernight: boolean } {
    // 默认使用06:00-次日06:00作为营业时间
    // 在实际应用中，这里应该从配置或API中获取
    return {
      openTime: '06:00',
      closeTime: '06:00',
      isOvernight: true
    };
  }

  /**
   * 格式化时间段的日期
   */
  private formatDateForSegment(baseDate: Date, startTime: string): string {
    const date = new Date(baseDate);

    // 返回格式化的日期 YYYY-MM-DD
    return date.toISOString().split('T')[0];
  }
}

// 自定义Hook
export function useHourlyBillingPresenter(props: any, emit: any): IHourlyBillingViewModel {
  return new HourlyBillingPresenter(props, emit);
}
