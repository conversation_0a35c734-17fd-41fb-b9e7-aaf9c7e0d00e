# 价格计算算法设计文档

## 1. 当前问题分析

现有价格计算算法存在以下几个主要问题：

### 1.1 跨天处理机制繁琐

- 使用 `next:` 前缀标记次日时间，增加了算法复杂度
- 跨天判断逻辑分散在多处，容易出错
- **在 23:59/00:00 处强制切割时间段，增加了不必要的复杂性**

### 1.2 价格应用不准确

- 跨天后次日时间段没有正确应用对应时段价格
- 存在将整个次日使用同一价格计算的问题
- 部分场景下，23:59 之后的计算会出现问题

### 1.3 算法复杂度高

- 为处理各种边缘情况，添加了大量特殊逻辑
- 代码可读性和可维护性差
- 调试和问题排查困难

## 2. 新算法设计

### 2.1 核心设计原则

1. **简化时间表示**：不使用 `next:` 前缀，改用内部分钟表示法
2. **统一跨天处理**：仅通过时间大小比较判断跨天（若 endTime < startTime 则为跨天）
3. **连续时间区间**：将时间统一转换为连续的分钟区间，**不在午夜00:00处强制切割**
4. **循环时间观念**：将24小时视为一个循环，通过简单的模运算处理跨天情况
5. **完全分段计算**：只在价格真正变化的时间点进行分段，确保每个时间点使用正确的价格
6. **明确价格匹配规则**：简化价格匹配规则，保持一致性

### 2.2 数据结构设计

```typescript
// 分钟表示的时间段
interface MinuteTimeRange {
  startMinute: number; // 开始时间（分钟表示）
  endMinute: number; // 结束时间（分钟表示，可能超过1440表示跨天）
  duration: number; // 持续时间（分钟）
}

// 价格段
interface PriceRange {
  startMinute: number; // 开始时间（分钟表示）
  endMinute: number; // 结束时间（分钟表示，可能超过1440表示跨天）
  pricePerMinute: number; // 每分钟价格
  priceType: string; // 价格类型
  planName: string; // 价格方案名称
  priority: number; // 优先级
}

// 最终价格明细
interface PriceSegment {
  startTime: string; // 开始时间（HH:MM格式）
  endTime: string; // 结束时间（HH:MM格式）
  duration: number; // 持续时间（分钟）
  price: number; // 价格
  priceType: string; // 价格类型
  planName: string; // 价格方案名称
}
```

### 2.3 算法流程

#### 2.3.1 主要流程图

```
┌─────────────────────┐
│ 1. 转换时间为分钟表示 │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│ 2. 统一处理跨天情况  │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│ 3. 生成价格变化点    │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│ 4. 找出交集时间段    │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│ 5. 计算每个段的价格  │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│ 6. 转换回时间字符串  │
└──────────┬──────────┘
           ↓
┌─────────────────────┐
│ 7. 返回计算结果      │
└─────────────────────┘
```

#### 2.3.2 详细算法步骤

1. **转换时间为分钟表示**

   ```typescript
   // 将HH:MM格式转换为分钟数（0-1439）
   function timeToMinutes(time: string): number {
     const [hours, minutes] = time.split(':').map(Number);
     return hours * 60 + minutes;
   }
   ```

2. **统一处理跨天情况**

   ```typescript
   function normalizeTimeRange(startMinute: number, endMinute: number): MinuteTimeRange {
     // 如果结束时间小于开始时间，认为是跨天（简化判断）
     if (endMinute < startMinute) {
       // 将结束时间加上一天的分钟数
       endMinute += 24 * 60;
     }

     return {
       startMinute,
       endMinute,
       duration: endMinute - startMinute
     };
   }
   ```

3. **生成价格变化点**

   ```typescript
   function generatePriceChangePoints(validPrices: Price[]): number[] {
     // 收集所有时间点（价格变化的点）
     const timePoints = new Set<number>();

     validPrices.forEach(price => {
       price.timeSlots.forEach(slot => {
         // 添加原始分钟值
         let startMinute = timeToMinutes(slot.startTime);
         let endMinute = timeToMinutes(slot.endTime);

         timePoints.add(startMinute);
         timePoints.add(endMinute);
       });
     });

     // 转换为数组并排序
     return Array.from(timePoints).sort((a, b) => a - b);
   }
   ```

4. **基于价格变化点生成时间段**

   ```typescript
   function generatePriceRanges(validPrices: Price[], changePoints: number[]): PriceRange[] {
     const priceRanges: PriceRange[] = [];

     // 创建所有相邻变化点之间的时间段
     for (let i = 0; i < changePoints.length - 1; i++) {
       const startMinute = changePoints[i];
       const endMinute = changePoints[i + 1];

       // 如果是同一时间点，跳过
       if (startMinute === endMinute) continue;

       // 找出适用于该时间段的最佳价格
       const bestPrice = findBestPrice(startMinute, endMinute, validPrices);

       if (bestPrice) {
         priceRanges.push({
           startMinute,
           endMinute,
           pricePerMinute: bestPrice.price / (bestPrice.hourCount * 60),
           priceType: bestPrice.priceType,
           planName: bestPrice.planName,
           priority: bestPrice.priority
         });
       }
     }

     return priceRanges;
   }
   ```

5. **查找时间段对应的最佳价格**

   ```typescript
   function findBestPrice(startMinute: number, endMinute: number, validPrices: Price[]): Price | null {
     // 找出适用于该时间段的所有价格
     const applicablePrices = validPrices.filter(price => {
       return price.timeSlots.some(slot => {
         let slotStart = timeToMinutes(slot.startTime);
         let slotEnd = timeToMinutes(slot.endTime);

         // 处理跨天的价格段
         if (slotEnd <= slotStart) {
           slotEnd += 24 * 60;
         }

         // 检查循环匹配（考虑一天中的任何时段）
         const normalizedStart = startMinute % (24 * 60);
         const normalizedEnd = endMinute % (24 * 60);

         // 时间段可能跨天，进行两次检查
         return (
           isTimeRangeOverlap(normalizedStart, normalizedEnd, slotStart, slotEnd) ||
           isTimeRangeOverlap(normalizedStart + 24 * 60, normalizedEnd + 24 * 60, slotStart, slotEnd)
         );
       });
     });

     // 按优先级和价格排序
     applicablePrices.sort((a, b) => {
       // 特定时段优先于全天时段
       const aIsAllDay = a.timeSlots.some(slot => slot.startTime === '00:00' && slot.endTime === '23:59');
       const bIsAllDay = b.timeSlots.some(slot => slot.startTime === '00:00' && slot.endTime === '23:59');

       if (aIsAllDay !== bIsAllDay) {
         return aIsAllDay ? 1 : -1;
       }

       // 按优先级和价格排序
       return a.priority !== b.priority ? a.priority - b.priority : a.price - b.price;
     });

     return applicablePrices.length > 0 ? applicablePrices[0] : null;
   }
   ```

6. **处理预定时间段与价格段的交集**

   ```typescript
   function findIntersectionSegments(bookingRange: MinuteTimeRange, priceRanges: PriceRange[]): PriceRange[] {
     // 将价格区间视为循环，找出所有交集
     const intersections: PriceRange[] = [];

     // 处理预订时间范围内的所有价格段
     for (let i = 0; i < priceRanges.length; i++) {
       const range = priceRanges[i];
       let rangeStart = range.startMinute;
       let rangeEnd = range.endMinute;

       // 处理循环时间（将价格段重复两次以覆盖可能的跨天情况）
       for (let cycle = 0; cycle < 2; cycle++) {
         const cycleStart = rangeStart + cycle * 24 * 60;
         const cycleEnd = rangeEnd + cycle * 24 * 60;

         // 检查是否有交集
         if (cycleStart < bookingRange.endMinute && bookingRange.startMinute < cycleEnd) {
           // 计算交集
           const intersectStart = Math.max(cycleStart, bookingRange.startMinute);
           const intersectEnd = Math.min(cycleEnd, bookingRange.endMinute);

           // 添加交集区间
           intersections.push({
             ...range,
             startMinute: intersectStart,
             endMinute: intersectEnd
           });
         }
       }
     }

     // 按时间排序
     return intersections.sort((a, b) => a.startMinute - b.startMinute);
   }
   ```

7. **计算价格并转换回时间字符串**
   ```typescript
   function calculateSegmentPrices(intersections: PriceRange[]): PriceSegment[] {
     return intersections.map(segment => {
       // 计算价格
       const price = Math.round(segment.pricePerMinute * (segment.endMinute - segment.startMinute));

       // 将分钟转回时间字符串
       const startTime = minutesToTimeString(segment.startMinute % (24 * 60));
       const endTime = minutesToTimeString(segment.endMinute % (24 * 60));

       return {
         startTime,
         endTime,
         duration: segment.endMinute - segment.startMinute,
         price,
         priceType: segment.priceType,
         planName: segment.planName
       };
     });
   }
   ```

### 2.4 新算法的优势

1. **简化的跨天处理**：不使用特殊前缀，只通过分钟值大小关系判断跨天
2. **连续的时间表示**：使用统一的分钟表示法，不在午夜强制切割时间段
3. **循环时间模型**：将24小时视为循环，使处理更加直观一致
4. **准确的价格应用**：每个时间段使用正确的价格，不存在次日使用错误价格的问题
5. **更高的可维护性**：算法逻辑清晰，代码结构简单，易于理解和维护
6. **更少的特殊处理**：减少了特殊情况的处理，提高了代码的稳定性

### 2.5 循环时间模型中的全天时间表示

在传统时间处理中，常用 `00:00-23:59` 来表示一整天。这种表示法是基于非循环的时间观念，将23:59视为"一天的结束"，而00:00视为"下一天的开始"。然而，这种做法与循环时间模型的核心理念相悖。

在纯粹的循环时间模型中，应该：

1. **没有特殊边界点**：不应存在强制切割点
2. **时间是连续循环的**：00:00点并非特殊，只是循环中的一点
3. **全天表示应简洁一致**：应表达24小时的完整循环

### 全天时间的正确表示

在本算法中，全天时间应采用以下表示方法：

1. **00:00-00:00**：表示一个完整的24小时循环

   - 当检测到起止时间相同但指代全天时，算法应识别为24小时循环
   - 可通过 `isFullDayRange` 辅助函数判断

2. **使用明确标志**：
   - 通过 `isAllDay: true` 属性直接标明
   - 避免依赖特定时间点暗示概念

这种表示方法：

- 更符合循环时间的概念
- 避免了人为引入特殊时间点
- 使算法处理更加简洁统一

### 与"23:59"表示法的区别

| 属性       | 23:59表示法      | 循环时间表示法     |
| ---------- | ---------------- | ------------------ |
| 概念清晰度 | 依赖传统日历观念 | 纯粹的循环时间概念 |
| 边界处理   | 需特殊处理23:59  | 无特殊边界点       |
| 算法复杂度 | 可能引入边界问题 | 更简洁一致         |
| 时间精度   | 丢失了1分钟      | 完整覆盖24小时     |

## 3. 实现示例

### 3.1 基于分钟的价格计算示例

```typescript
/**
 * 按照预订的时间段计算价格
 */
function calculatePriceByDuration(startTime: string, endTime: string, validPrices: Price[]): PriceDetails {
  // 1. 转换为分钟表示
  const startMinute = timeToMinutes(startTime);
  const endMinute = timeToMinutes(endTime);

  // 2. 处理跨天情况 - 简单明了的判断
  const bookingRange = normalizeTimeRange(startMinute, endMinute);

  // 3. 生成价格变化点并创建价格段
  const changePoints = generatePriceChangePoints(validPrices);
  const priceRanges = generatePriceRanges(validPrices, changePoints);

  // 4. 找出与预订时间有交集的时间段
  const intersections = findIntersectionSegments(bookingRange, priceRanges);

  // 5. 计算每个交集段的价格
  const priceSegments = calculateSegmentPrices(intersections);

  // 6. 计算总价
  const totalPrice = priceSegments.reduce((sum, segment) => sum + segment.price, 0);

  // 7. 返回结果
  return {
    totalPrice,
    details: priceSegments
  };
}
```

### 3.2 金额反推时长示例

```typescript
/**
 * 根据金额反推可预订的时长和价格明细
 */
function calculatePriceDetailsFromAmount(amount: number, startTime: string, validPrices: Price[]): PriceDetails {
  // 1. 转换开始时间为分钟表示
  const startMinute = timeToMinutes(startTime);

  // 2. 生成价格变化点并创建价格段
  const changePoints = generatePriceChangePoints(validPrices);
  const priceRanges = generatePriceRanges(validPrices, changePoints);

  // 3. 按照循环排序，确保正确处理跨天情况
  const relevantRanges = sortPriceRangesFromStartTime(priceRanges, startMinute);

  // 4. 逐段消费金额
  const priceSegments: PriceSegment[] = [];
  let remainingAmount = amount;
  let currentMinute = startMinute;

  for (const range of relevantRanges) {
    if (remainingAmount <= 0) break;

    // 获取当前段的起始分钟（可能跨多个循环）
    let rangeStartMinute = range.startMinute;
    while (rangeStartMinute < currentMinute) {
      rangeStartMinute += 24 * 60; // 向后推一天
    }

    // 结束分钟也相应推后
    const rangeEndMinute = rangeStartMinute + (range.endMinute - range.startMinute);

    // 确保开始时间不早于当前时间
    const effectiveStartMinute = Math.max(currentMinute, rangeStartMinute);

    // 计算可用时长和最大可消费金额
    const availableDuration = rangeEndMinute - effectiveStartMinute;
    const maxPrice = availableDuration * range.pricePerMinute;

    // 计算本次可消费的金额和时长
    const consumedAmount = Math.min(remainingAmount, maxPrice);
    const consumedDuration = Math.floor(consumedAmount / range.pricePerMinute);

    if (consumedDuration > 0) {
      // 计算结束时间
      const endMinute = effectiveStartMinute + consumedDuration;

      // 添加价格段
      priceSegments.push({
        startTime: minutesToTimeString(effectiveStartMinute % (24 * 60)),
        endTime: minutesToTimeString(endMinute % (24 * 60)),
        duration: consumedDuration,
        price: Math.round(consumedDuration * range.pricePerMinute),
        priceType: range.priceType,
        planName: range.planName
      });

      // 更新剩余金额和当前时间
      remainingAmount -= consumedDuration * range.pricePerMinute;
      currentMinute = endMinute;
    }
  }

  // 5. 计算总价
  const totalPrice = priceSegments.reduce((sum, segment) => sum + segment.price, 0);

  // 6. 返回结果
  return {
    totalPrice,
    details: priceSegments
  };
}
```

## 4. 具体场景测试

### 4.1 跨天计算场景

假设我们有以下价格方案：

- 10:00-11:00: 每小时60元
- 11:00-15:00: 每小时90元
- 15:00-18:00: 每小时60元
- 18:00-22:00: 每小时120元
- 全天全时段价格: 每小时80元

**情景一：从晚上到次日早上**  
预订时间：19:48-10:00（注意：这里直接表示为跨天，不用添加前缀）

正确计算过程（作为连续时间段处理）：

1. 19:48-22:00: 使用"18:00-22:00"价格，132分钟 × 2元/分钟 = 264元
2. 22:00-10:00: 使用全时段价格，720分钟 × 1.33元/分钟 = 959元

总价：264 + 959 = 1223元

**情景二：从下午到次日下午**  
预订时间：15:00-15:00（跨天24小时）

正确计算过程（连续计算，按实际价格变化点分段）：

1. 15:00-18:00: 使用"15:00-18:00"价格，180分钟 × 1元/分钟 = 180元
2. 18:00-22:00: 使用"18:00-22:00"价格，240分钟 × 2元/分钟 = 480元
3. 22:00-10:00: 使用全时段价格，720分钟 × 1.33元/分钟 = 959元
4. 10:00-11:00: 使用"10:00-11:00"价格，60分钟 × 1元/分钟 = 60元
5. 11:00-15:00: 使用"11:00-15:00"价格，240分钟 × 1.5元/分钟 = 360元

总价：180 + 480 + 959 + 60 + 360 = 2039元

### 4.2 金额反推场景

**情景：1000元能预订多长时间**  
开始时间：19:48

反推计算过程（按连续的价格段计算）：

1. 19:48-22:00: 可用132分钟，花费264元，剩余736元
2. 22:00-07:12: 可用552分钟，花费736元，剩余0元

总时长：132 + 552 = 684分钟（11小时24分钟）  
结束时间：次日07:12  
总价：1000元

## 5. 迁移建议

1. **分阶段实施**：

   - 先实现核心时间处理工具，保持接口不变
   - 逐步替换依赖该逻辑的计算方法
   - 全面测试所有场景，特别是跨天情况

2. **单元测试覆盖**：

   - 编写全面的单元测试
   - 重点测试跨天场景和循环时间处理
   - 确保与旧算法结果一致性

3. **兼容旧版本**：
   - 可以提供同时兼容next:前缀的转换函数
   - 实现结果格式转换，以支持现有UI展示
   - 逐步移除处理next:前缀的代码

## 6. 改进实现总结

我们已成功对时间处理模块进行了以下改进：

### 6.1 全天时间表示的规范化

1. **00:00-00:00表示全天**：

   - 移除了所有使用23:59表示一天结束的代码
   - 采用00:00-00:00的循环表示法表达全天
   - 添加了`isFullDayRange`函数识别全天时间范围

2. **增强了时间处理功能**：

   - 添加了`normalizeTimeString`处理特殊值如24:00
   - 添加了`getMinutesDifferenceHandlingFullDay`正确计算全天持续时间

3. **更新了默认值**：
   - 所有使用`billingTimeEnd ?? '23:59'`的代码都更新为`billingTimeEnd ?? '00:00'`
   - 在时间验证逻辑中添加了全天检测

### 6.2 计算逻辑的优化

1. **计算方法增强**：

   - 在`calculatePriceByDuration`中增加全天时间检测
   - 将全天转换为明确的24小时时长处理
   - 确保`presenter.ts`中正确处理全天情况

2. **移除特殊时间点依赖**：
   - 不再使用23:59作为任何特殊边界
   - 算法内部完全基于循环时间模型
   - 逻辑更加一致和可维护

### 6.3 代码结构的改进

1. **添加辅助函数**：

   - 在`CircularTimeUtils`中集中提供时间处理功能
   - 在`TimeUtils`中提供更高级封装

2. **简化业务逻辑**：

   - `TimeRangeValidator`使用全天检测简化判断
   - 营业时间判断逻辑更加清晰

3. **文档更新**：
   - 添加全天时间表示的理论说明
   - 对比了不同表示方法的优缺点

这些改进使算法更加符合循环时间模型的设计理念，完全避免了对特殊边界点的依赖，简化了计算逻辑，并提高了代码的清晰度和一致性
