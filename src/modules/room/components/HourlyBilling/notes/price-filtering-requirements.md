# HourlyBilling组件时段价格筛选需求

## 1. 需求概述

为HourlyBilling组件实现时段价格筛选功能，支持多种计费方式和价格类型选择。添加全时段价格机制，优化价格方案筛选逻辑，确保合理的价格计算流程。

## 2. 数据结构

### 2.1 核心数据结构

- **价格方案 (TimePricePlanVO)**

  - 包含时间配置、价格配置、房型和区域信息
  - 支持按日期或星期配置

- **价格配置项 (PriceSettingItem)**

  - 包含基础价格、节假日价格、区域价格等

- **时段价格选项 (PriceOption)**

  - 包含ID、标签、价格和适用时间范围

- **全时段价格 (baseTimePriceFee)**
  - 用于没有匹配到有效价格方案时的基础定价

## 3. 功能需求

### 3.1 价格方案筛选

- **日期/星期筛选**：

  - 仅根据日期或星期匹配筛选价格方案
  - **不根据时间范围**筛选方案，即使时间不在范围内也显示方案

- **筛选优先级**：
  - 房型匹配 + 区域匹配 > 仅区域价格 > 仅房型匹配 > 无匹配

### 3.2 价格类型处理

- **基础价格类型**：
  - 基础房费 (`PriceSettingItem.type=base`)
- **特殊价格类型**：

  - 生日价格、活动价格、团购价格等非基础房费类型

- **价格优先级**：
  - 节假日价格+区域价格 > 节假日价格 > 区域价格 > 基础价格

### 3.3 时段重叠处理

- **多时段包含开始时间时**：
  - 优先选择时间范围较小的更精细时段
  - 例如：开始时间14:00同时在"全天方案"和"下午时段"中，优先使用"下午时段"

### 3.4 价格计算连续性

- **时段连续处理**：

  - 根据起始时间确定初始时段
  - 使用该时段价格直到时段结束
  - 以上一时段的结束时间作为下一段的起始时间
  - 继续选择下一适用时段

- **时段边界**：
  - 如果开始时间恰好是某个时段的结束时间，不计入该时段

### 3.5 价格回退机制

- **特殊价格类型回退**：

  - 首先尝试使用所选价格类型
  - 如不存在，回退到基础房费
  - 如基础房费也不存在，使用全时段价格

- **全时段价格应用场景**：
  - 没有匹配到任何有效时段
  - 特定时段既没有所选价格类型也没有基础房费
  - 计算期间存在没有任何有效时段的空档期

## 4. 实现逻辑

### 4.1 基本流程

1. 根据日期/星期筛选有效价格方案
2. 根据房型和区域进行优先级筛选
3. 按照用户选择的价格类型筛选价格
4. 按时间顺序处理每个时段的价格计算
5. 按照价格回退机制处理不存在的价格类型
6. 汇总各时段价格得出总价

### 4.2 价格计算示例

**配置示例**:

- 全天方案(00:00-23:59): 基础房费100元/小时
- 早间方案(06:00-10:00): 基础房费80元/小时，活动价格70元/小时
- 中午方案(10:00-14:00): 基础房费120元/小时，生日价格100元/小时
- 下午方案(14:00-18:00): 基础房费130元/小时
- 晚间方案(18:00-22:00): 基础房费150元/小时
- 全时段价格: 50元/小时

**场景1**: 用户9:00开始，使用6小时，选择"基础房费"

- 9:00-10:00: 早间方案，80元/小时，共80元
- 10:00-14:00: 中午方案，120元/小时，共480元
- 14:00-15:00: 下午方案，130元/小时，共130元
- 总计: 690元

**场景2**: 用户9:00开始，使用6小时，选择"生日价格"

- 9:00-10:00: 早间方案没有生日价格，使用基础房费80元/小时，共80元
- 10:00-14:00: 中午方案，生日价格100元/小时，共400元
- 14:00-15:00: 下午方案没有生日价格，使用基础房费130元/小时，共130元
- 总计: 610元

## 5. UI交互

- 显示所有价格类型选项供用户选择
- 显示每个时段的价格信息
- 按照选择的计费方式（时长、金额、结束时间）计算价格
- 提供全时段价格作为保底定价策略

## 6. 特别说明

1. 时段价格显示不过滤可用性，所有可能的时段价格都显示
2. 选定特殊价格类型但部分时段不存在该类型时，采用基础房费作为第一级兜底方案
3. 时段计费以开始时间所在时段为起点，不中断计费直到时段结束
4. 全时段价格作为最后的价格保障，只在无法找到任何适用价格时使用
