# HourlyBilling组件时段价格筛选流程图

## 1. 价格方案筛选流程

```mermaid
flowchart TD
    A[输入：时间、房型、区域等条件] --> B[根据日期/星期筛选价格方案]
    B --> C{是否找到符合条件的方案?}
    C -- 是 --> D[按优先级排序: 房型+区域>仅区域>仅房型]
    C -- 否 --> E[使用全时段价格]
    D --> F[提取所有时段价格信息]
    F --> G[返回有效的价格方案和时段]
```

## 2. 价格类型选择与计算流程

```mermaid
flowchart TD
    A[选择价格类型: 基础房费/生日价格等] --> B[按时间顺序处理各时段]
    B --> C{当前时段有所选价格类型?}
    C -- 是 --> D[使用所选价格类型]
    C -- 否 --> E{当前时段有基础房费?}
    E -- 是 --> F[使用基础房费]
    E -- 否 --> G[使用全时段价格]
    D --> H[计算当前时段价格]
    F --> H
    G --> H
    H --> I{还有下一时段?}
    I -- 是 --> J[以上一时段结束时间作为起点]
    I -- 否 --> K[汇总所有时段价格得出总价]
    J --> B
```

## 3. 价格优先级选择逻辑

```mermaid
flowchart TD
    A[获取价格配置] --> B{有节假日+区域价格?}
    B -- 是 --> C[使用节假日+区域价格]
    B -- 否 --> D{有节假日价格?}
    D -- 是 --> E[使用节假日价格]
    D -- 否 --> F{有区域价格?}
    F -- 是 --> G[使用区域价格]
    F -- 否 --> H[使用基础价格]
    C --> I[返回最终价格]
    E --> I
    G --> I
    H --> I
```

## 4. 时段重叠处理逻辑

```mermaid
flowchart TD
    A[找到包含开始时间的所有时段] --> B[按时间范围大小排序]
    B --> C{多个时段包含开始时间?}
    C -- 是 --> D[选择时间范围最小的时段]
    C -- 否 --> E[使用唯一匹配的时段]
    D --> F[返回选定时段]
    E --> F
```

## 5. 整体价格计算流程

```mermaid
flowchart TD
    A[开始] --> B[获取用户输入: 开始时间、时长、价格类型]
    B --> C[根据日期/星期筛选价格方案]
    C --> D[按照房型和区域优先级排序方案]
    D --> E[获取所有时段价格信息]
    E --> F[确定开始时间所在初始时段]
    F --> G[初始化价格计算]
    G --> H{处理当前时段}
    H --> I{有所选价格类型?}
    I -- 是 --> J[使用所选价格类型]
    I -- 否 --> K{有基础房费?}
    K -- 是 --> L[使用基础房费]
    K -- 否 --> M[使用全时段价格]
    J --> N[计算当前时段价格]
    L --> N
    M --> N
    N --> O{还有未处理时间?}
    O -- 是 --> P[以当前时段结束时间为下一段起点]
    O -- 否 --> Q[汇总价格返回结果]
    P --> H
```

这些流程图详细说明了HourlyBilling组件中时段价格筛选和计算的关键逻辑，包括价格方案筛选、价格类型选择、优先级处理、时段重叠处理和整体计算流程。
