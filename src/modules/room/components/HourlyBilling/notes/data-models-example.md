# HourlyBilling组件数据模型示例

本文档通过具体示例展示HourlyBilling组件中的数据模型和数据流转过程。

## 1. 输入数据模型

### 1.1 价格方案示例

```typescript
// 价格方案列表
const pricePlans: TimePricePlanVO[] = [
  // 方案1: 工作日全天价格
  {
    id: 'plan1',
    name: '工作日全天方案',
    isEnabled: true,
    timeConfig: {
      timeType: 'weekday',
      weekdayConfig: {
        weeks: [1, 2, 3, 4, 5] // 周一至周五
      },
      billingTimeStart: '00:00',
      billingTimeEnd: '23:59'
    },
    roomTypeConfig: {
      roomTypes: [{ id: 'room1', name: '标准房' }]
    },
    areaConfig: {
      areas: [{ id: 'area1', name: '普通区' }]
    },
    priceConfigList: [
      {
        name: '基础房费',
        type: 'base',
        price: 10000, // 100元/小时 (单位:分)
        holidayPrice: [
          {
            holidayId: 'holiday1',
            price: 15000, // 150元/小时
            areaPrice: [{ areaId: 'area1', price: 18000 }] // 180元/小时
          }
        ],
        areaPrice: [{ areaId: 'area1', price: 12000 }] // 120元/小时
      },
      {
        name: '生日价格',
        type: 'birthday',
        price: 13000, // 130元/小时
        areaPrice: [{ areaId: 'area1', price: 15000 }] // 150元/小时
      }
    ]
  },

  // 方案2: 早上时段价格
  {
    id: 'plan2',
    name: '早间时段方案',
    isEnabled: true,
    timeConfig: {
      timeType: 'weekday',
      weekdayConfig: {
        weeks: [1, 2, 3, 4, 5]
      },
      billingTimeStart: '09:00',
      billingTimeEnd: '12:00'
    },
    roomTypeConfig: {
      roomTypes: [{ id: 'room1', name: '标准房' }]
    },
    areaConfig: {
      areas: [{ id: 'area1', name: '普通区' }]
    },
    priceConfigList: [
      {
        name: '活动价格',
        type: 'activity',
        price: 8000, // 80元/小时
        areaPrice: [{ areaId: 'area1', price: 9000 }] // 90元/小时
      }
    ]
  },

  // 方案3: 下午时段价格
  {
    id: 'plan3',
    name: '下午时段方案',
    isEnabled: true,
    timeConfig: {
      timeType: 'weekday',
      weekdayConfig: {
        weeks: [1, 2, 3, 4, 5]
      },
      billingTimeStart: '12:00',
      billingTimeEnd: '18:00'
    },
    roomTypeConfig: {
      roomTypes: [{ id: 'room1', name: '标准房' }]
    },
    areaConfig: {
      areas: [{ id: 'area1', name: '普通区' }]
    },
    priceConfigList: [
      {
        name: '基础房费',
        type: 'base',
        price: 12000, // 120元/小时
        areaPrice: [{ areaId: 'area1', price: 13000 }] // 130元/小时
      }
    ]
  }
];

// 当前条件
const currentConditions = {
  currentTime: 1642498800, // 周二 14:00
  startTime: '14:00',
  selectedDuration: 240, // 4小时
  areaId: 'area1',
  roomTypeId: 'room1',
  holidayVO: { id: 'holiday1' },
  baseTimePriceFee: 5000 // 50元/小时（全时段价格，单位:分）
};
```

### 1.2 价格类型选项

```typescript
// 价格类型下拉选项
const priceTypeOptions = [
  { label: '基础房费', value: 'baseRoomFee' },
  { label: '活动价格', value: 'activityFee' },
  { label: '生日价格', value: 'birthdayFee' },
  { label: '团购价格', value: 'groupBuyFee' }
];

// 用户选择的价格类型
const selectedPriceOption = 'baseRoomFee';
```

## 2. 数据处理中间结果

### 2.1 根据日期/星期筛选后的方案

```typescript
// 根据日期/星期筛选结果
const filteredPlans = [
  // 方案1: 工作日全天方案
  {
    id: 'plan1',
    name: '工作日全天方案'
    // ...其他属性
  },
  // 方案3: 下午时段方案
  {
    id: 'plan3',
    name: '下午时段方案'
    // ...其他属性
  }
];
```

### 2.2 按照房型和区域优先级排序后的方案

```typescript
// 按优先级排序后的方案
const prioritizedPlans = [
  // 方案3: 下午时段方案（优先级高：时间范围更小）
  {
    id: 'plan3',
    name: '下午时段方案'
    // ...其他属性
  },
  // 方案1: 工作日全天方案
  {
    id: 'plan1',
    name: '工作日全天方案'
    // ...其他属性
  }
];
```

### 2.3 时段价格选项

```typescript
// 时段价格选项
const timeSlotPrices = [
  {
    planId: 'plan3',
    planName: '下午时段方案',
    timeRange: { start: '12:00', end: '18:00' },
    price: 130, // 元/小时 (区域价格)
    priceType: 'baseRoomFee'
  },
  {
    planId: 'plan1',
    planName: '工作日全天方案',
    timeRange: { start: '00:00', end: '23:59' },
    price: 180, // 元/小时 (节假日+区域价格)
    priceType: 'baseRoomFee'
  }
];
```

## 3. 价格计算过程

### 3.1 选择"基础房费"时的计算

```typescript
// 用户选择"基础房费"，计算过程
const baseRoomFeeCalculation = {
  steps: [
    {
      timeRange: { start: '14:00', end: '18:00' },
      duration: 240, // 4小时
      planName: '下午时段方案',
      pricePerHour: 130, // 元/小时
      subtotal: 520 // 元
    }
  ],
  totalPrice: 520 // 元
};
```

### 3.2 选择"生日价格"时的计算

```typescript
// 用户选择"生日价格"，计算过程
const birthdayFeeCalculation = {
  steps: [
    {
      timeRange: { start: '14:00', end: '18:00' },
      duration: 240, // 4小时
      planName: '下午时段方案',
      // 下午时段没有生日价格，回退到基础房费
      priceType: 'baseRoomFee',
      pricePerHour: 130, // 元/小时
      subtotal: 520 // 元
    }
  ],
  totalPrice: 520 // 元
};
```

### 3.3 复杂场景示例：跨多个时段

```typescript
// 场景：用户9:00开始，使用6小时，选择"生日价格"
const complexScenarioCalculation = {
  steps: [
    {
      timeRange: { start: '09:00', end: '12:00' },
      duration: 180, // 3小时
      planName: '早间时段方案',
      // 早间时段没有生日价格，回退到基础房费
      priceType: 'baseRoomFee',
      pricePerHour: 80, // 元/小时
      subtotal: 240 // 元
    },
    {
      timeRange: { start: '12:00', end: '15:00' },
      duration: 180, // 3小时
      planName: '下午时段方案',
      // 下午时段没有生日价格，回退到基础房费
      priceType: 'baseRoomFee',
      pricePerHour: 130, // 元/小时
      subtotal: 390 // 元
    }
  ],
  totalPrice: 630 // 元
};
```

## 4. 最终输出

### 4.1 价格计算结果

```typescript
// 最终输出结果示例
const finalResult = {
  totalPrice: 520, // 元
  details: [
    {
      planName: '下午时段方案',
      startTime: '14:00',
      endTime: '18:00',
      duration: 240, // 分钟
      price: 520 // 元
    }
  ],
  timeRange: {
    currentDate: '2022-01-18',
    startTime: '14:00',
    endTime: '18:00',
    duration: 240
  }
};
```

### 4.2 UI展示元素

```typescript
// 时段价格表格数据
const timePriceTableData = {
  '12:00-18:00': {
    baseRoomFee: 130,
    birthdayFee: 0, // 无此价格类型，显示0
    activityFee: 0, // 无此价格类型，显示0
    groupBuyFee: 0 // 无此价格类型，显示0
  },
  '00:00-23:59': {
    baseRoomFee: 180,
    birthdayFee: 150,
    activityFee: 0, // 无此价格类型，显示0
    groupBuyFee: 0 // 无此价格类型，显示0
  }
};
```

这些数据模型示例展示了HourlyBilling组件中从输入数据到最终计算结果的完整数据流转过程，帮助理解时段价格筛选和计算的实现机制。
