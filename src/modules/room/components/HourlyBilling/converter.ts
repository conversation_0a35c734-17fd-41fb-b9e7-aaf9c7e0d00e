import type { PricePlanVO } from '@/types/projectobj';
import { convertToYuan, convertToFen } from '@/utils/priceUtils';
import type {
  IHourlyBillingState,
  PriceOption,
  ProcessedProductList,
  PriceTypeKey,
  PriceDetail
} from './viewmodel';

export class HourlyBillingConverter {
  /**
   * 创建初始UI状态
   */
  static createInitialState(currentTime: number, isOpenEnd: boolean): IHourlyBillingState {
    console.log('-----Converter.createInitialState调试-----');
    console.log('输入的currentTime:', currentTime);
    console.log('currentTime类型:', typeof currentTime);
    
    const formattedTime = this.formatTimeFromDateTime(currentTime);
    console.log('格式化后的时间(HH:MM):', formattedTime);
    
    const state: IHourlyBillingState = {
      billingType: 'duration' as const,
      selectedDuration: 0, // 默认0分钟
      customDuration: '0',
      isCustomDuration: false,
      selectedPriceOption: 'baseRoomFee' as const,
      startTime: formattedTime,
      endTime: null,
      endTimeForPicker: '', // 初始化为空字符串
      inputAmount: '',
      customMinimum: null,
      calculatedDuration: 0,
      eightHourPrice: 0,
      isOverEightHours: false,
      endHour: 0,
      endMinute: 0,
      businessHours: {
        openTime: "06:00",
        closeTime: "06:00"
      },
      currentTime: currentTime // 原样存储传入的时间戳
    };
    
    console.log('创建的state:', state);
    console.log('state中的currentTime:', state.currentTime);
    console.log('-----createInitialState调试结束-----');
    
    return state;
  }

  /**
   * 从DateTime时间戳格式化时间
   */
  static formatTimeFromDateTime(dateTimeString: number | null): string {
    if (!dateTimeString) return '00:00';
    const date = new Date(dateTimeString * 1000);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }

  /**
   * 获取价格类型的标签
   */
  static getPriceTypeLabel(priceType: string): string {
    switch (priceType) {
      case 'baseRoomFee': return '基础房费';
      case 'activityFee': return '活动价格';
      case 'birthdayFee': return '生日价格';
      case 'groupBuyFee': return '团购价格';
      case 'memberPrice': return '会员价格';
      default: return priceType;
    }
  }

  /**
   * 格式化时长
   */
  static formatDuration(minutes: number): string {
    if (minutes < 60) return `${minutes}分钟`;
    const hours = Math.floor(minutes / 60);
    return `${hours}小时`;
  }

  /**
   * 解析时间字符串为分钟数
   */
  static parseTime(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * 格式化时间
   */
  static formatTime(date: Date): string {
    return date.toTimeString().slice(0, 5);
  }

  /**
   * 获取当前日期字符串
   */
  static getCurrentDate(timestamp: number): string {
    const date = new Date(timestamp * 1000);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }

  /**
   * 从价格对象中提取价格
   */
  static getPrices(priceObj: any): Record<PriceTypeKey, number> {
    return {
      baseRoomFee: priceObj.baseRoomFee || 0,
      activityFee: priceObj.activityFee || 0,
      birthdayFee: priceObj.birthdayFee || 0,
      groupBuyFee: priceObj.groupBuyFee || 0,
      memberPrice: priceObj.memberPrice || 0
    };
  }

  /**
   * 处理商品列表
   */
  static processProductList(planProducts: any[], pricePlanSubProductVO: any[]): ProcessedProductList {
    const result: ProcessedProductList = {
      standardProducts: [],
      optionalProducts: { type: 'ByPlan', count: 0, products: [] },
      freeProducts: [],
      optionalFreeProducts: { type: 'ByPlan', count: 0, products: [] }
    };

    if (!planProducts || !planProducts.length) {
      return result;
    }

    const processProducts = (products: any[], isOptional: boolean = false, isFree: boolean = false) => {
      return products.map(product => {
        const productDetails = pricePlanSubProductVO.find(p => p.id === product.id);
        if (!productDetails) return null;

        const _productCount = product.count || 0;
        console.log('product', product)

        return {
          id: product.id,
          name: productDetails.name,
          flavor: '',
          quantity: isOptional ? 0 : _productCount,
          unit: productDetails.unit,
          price: isFree ? 0 : productDetails.currentPrice,
          totalAmount: isFree ? 0 : (productDetails.currentPrice * (isOptional ? 0 : _productCount))
        };
      }).filter(p => p !== null) as any[];
    };

    planProducts.forEach(group => {
      if (group.billType === 'paid' && !group.optionType) {
        result.standardProducts = processProducts(group.products);
      } else if (group.billType === 'paid' && group.optionType) {
        result.optionalProducts = {
          type: group.optionType === 'by_plan' ? 'ByPlan' : 'ByCount',
          count: group.count || 0,
          optionCount: group.count || 0,
          products: processProducts(group.products, true)
        };
      } else if (group.billType === 'free' && !group.optionType) {
        result.freeProducts = processProducts(group.products, false, true);
      } else if (group.billType === 'free' && group.optionType) {
        result.optionalFreeProducts = {
          type: group.optionType === 'by_plan' ? 'ByPlan' : 'ByCount',
          count: group.count || 0,
          optionCount: group.count || 0,
          products: processProducts(group.products, true, true)
        };
      }
    });

    return result;
  }

  /**
   * 将金额转换为价格详情
   */
  static calculatePriceDetailsFromAmount(
    amount: number,
    startTime: string,
    currentTime: number,
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePriceForArea: Record<string, number>
  ): {
    totalPrice: number;
    calculatedDuration: number;
    details: PriceDetail[];
    endTime: string;
  } {
    // amount 输入为元，需要转换为分进行计算
    const amountInFen = convertToFen(amount);
    
    let remainingAmount = amountInFen;
    let totalDuration = 0;
    const details: PriceDetail[] = [];
    let currentTimeObj = new Date(currentTime * 1000);

    const getApplicablePlan = (time: Date): PriceOption => {
      const timeslotPlan = validPriceOptions.find(option => {
        const planStart = new Date(`2000-01-01T${option.timeRanges[0].start}`);
        let planEnd = new Date(`2000-01-01T${option.timeRanges[0].end}`);
        if (planEnd <= planStart) {
          planEnd.setDate(planEnd.getDate() + 1);
        }
        return time >= planStart && time < planEnd;
      });

      if (timeslotPlan) {
        return timeslotPlan;
      }

      // 如果没有找到适用的方案，使用基础价格
      return {
        id: 'base',
        label: '基础价格',
        value: 'base_default',
        prices: basePriceForArea as Record<PriceTypeKey, number>,
        timeRanges: [{ start: '00:00', end: '00:00' }]
      };
    };

    const getNextTimeslotStart = (time: Date): Date | null => {
      const nextSlot = validPriceOptions.find(option => {
        const slotStart = new Date(`2000-01-01T${option.timeRanges[0].start}`);
        return slotStart > time;
      });
      return nextSlot ? new Date(`2000-01-01T${nextSlot.timeRanges[0].start}`) : null;
    };

    while (remainingAmount > 0 && totalDuration < 1440) { // 最多计算24小时
      const currentPlan = getApplicablePlan(currentTimeObj);
      const hourlyRate = currentPlan.prices[selectedPriceOption] || currentPlan.prices.baseRoomFee;

      let planEndTime: Date;
      if (currentPlan.label === '基础价格') {
        const nextSlotStart = getNextTimeslotStart(currentTimeObj);
        planEndTime = nextSlotStart || new Date(currentTimeObj.getTime() + 24 * 60 * 60 * 1000);
      } else {
        planEndTime = new Date(`2000-01-01T${currentPlan.timeRanges[0].end}`);
        if (planEndTime <= currentTimeObj) {
          planEndTime.setDate(planEndTime.getDate() + 1);
        }
      }

      const maxDurationInPlan = (planEndTime.getTime() - currentTimeObj.getTime()) / 60000;
      const affordableDuration = Math.min((remainingAmount / hourlyRate) * 60, maxDurationInPlan);
      const actualDuration = Math.min(affordableDuration, 1440 - totalDuration);

      const segmentPrice = (actualDuration / 60) * hourlyRate;
      remainingAmount -= segmentPrice;
      totalDuration += actualDuration;

      const endTime = new Date(currentTimeObj.getTime() + actualDuration * 60000);
      
      details.push({
        id: currentPlan.id,
        planName: currentPlan.label,
        startTime: this.formatTime(currentTimeObj),
        endTime: this.formatTime(endTime),
        duration: Math.round(actualDuration),
        price: segmentPrice
      });

      currentTimeObj = endTime;
    }

    const calculatedEndTime = this.formatTime(currentTimeObj);

    return {
      totalPrice: amountInFen,
      calculatedDuration: Math.round(totalDuration),
      details,
      endTime: calculatedEndTime
    };
  }

  /**
   * 根据时长/结束时间计算价格详情
   */
  static calculatePriceByDuration(
    startTime: string,
    duration: number,
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePriceForArea: Record<string, number>
  ): {
    totalPrice: number;
    details: PriceDetail[];
    calculatedEndTime: string;
  } {
    let totalPrice = 0;
    const details: PriceDetail[] = [];
    
    // 创建开始和结束时间对象
    const startDateTime = new Date(`2000-01-01T${startTime}`);
    const endDateTime = new Date(startDateTime.getTime() + duration * 60000);
    const calculatedEndTime = this.formatTime(endDateTime);

    // 创建时间点数组，包含开始时间、结束时间和所有价格方案的开始/结束时间
    const timePoints = [startDateTime, endDateTime];
    validPriceOptions.forEach(option => {
      option.timeRanges.forEach(range => {
        timePoints.push(new Date(`2000-01-01T${range.start}`));
        timePoints.push(new Date(`2000-01-01T${range.end}`));
      });
    });

    // 对时间点进行排序和去重，只保留起止时间范围内的点
    const uniqueTimePoints = Array.from(new Set(timePoints))
      .sort((a, b) => a.getTime() - b.getTime())
      .filter(time => time >= startDateTime && time <= endDateTime);

    // 遍历时间段
    for (let i = 0; i < uniqueTimePoints.length - 1; i++) {
      const segmentStart = uniqueTimePoints[i];
      const segmentEnd = uniqueTimePoints[i + 1];
      const segmentDuration = (segmentEnd.getTime() - segmentStart.getTime()) / 60000; // 分钟

      if (segmentDuration > 0) {
        // 查找此时间段内适用的最低价格
        let lowestPrice = Infinity;
        let bestPlan: PriceOption | null = null;

        for (const option of validPriceOptions) {
          const planStart = new Date(`2000-01-01T${option.timeRanges[0].start}`);
          let planEnd = new Date(`2000-01-01T${option.timeRanges[0].end}`);

          if (planEnd <= planStart) {
            planEnd.setDate(planEnd.getDate() + 1);
          }

          if (segmentStart >= planStart && segmentEnd <= planEnd) {
            const price = (option.prices[selectedPriceOption] || option.prices.baseRoomFee) * (segmentDuration / 60);
            if (price < lowestPrice) {
              lowestPrice = price;
              bestPlan = option;
            }
          }
        }

        if (bestPlan) {
          totalPrice += lowestPrice;
          details.push({
            id: bestPlan.id,
            planName: bestPlan.label,
            startTime: this.formatTime(segmentStart),
            endTime: this.formatTime(segmentEnd),
            duration: Math.round(segmentDuration),
            price: lowestPrice
          });
        } else {
          // 如果没有适用的价格方案，使用基础价格
          const selectedPrice = basePriceForArea[selectedPriceOption] || basePriceForArea.baseRoomFee;
          const basePrice = selectedPrice * (segmentDuration / 60);
          totalPrice += basePrice;
          
          details.push({
            id: 'base',
            planName: '基础价格',
            startTime: this.formatTime(segmentStart),
            endTime: this.formatTime(segmentEnd),
            duration: Math.round(segmentDuration),
            price: basePrice
          });
        }
      }
    }

    return {
      totalPrice,
      details,
      calculatedEndTime
    };
  }

  /**
   * 计算24小时的价格上限
   */
  static calculateEightHourPrice(
    startTime: string,
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePriceForArea: Record<string, number>
  ): number {
    const result = this.calculatePriceByDuration(
      startTime, 
      1440, // 24小时 = 1440分钟
      validPriceOptions,
      selectedPriceOption,
      basePriceForArea
    );
    
    return result.totalPrice;
  }
}