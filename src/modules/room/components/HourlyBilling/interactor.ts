import { PriceCalculator } from './PriceCalculator';
import { PricingRules } from './PricingRules';
import * as TimeModel from './TimeModel';
import * as PriceModel from './PriceModel';
import type { TimePricePlanVO } from '@/modules/room/entity/timePricePlan';
import type { PriceOption, PriceTypeKey, TimeSlotPrice, PriceDetail, ProcessedProductList } from './viewmodel';
import type { Price } from './types';
import { useDeviceStore } from '@/stores/deviceStore';
import { ProductManager } from './ProductManager';
import { TimeRangeValidator } from './TimeRangeValidator';

/**
 * 小时计费业务交互层
 * 简化版实现，使用TimeModel和PriceModel
 */
export class HourlyBillingInteractor {
  /**
   * 判断时间是否在范围内
   * 注意：此方法实际是对TimeRangeValidator.isTimeInRange的封装
   */
  isTimeInRange(
    checkStart: string,
    duration: number,
    plan: TimePricePlanVO,
    businessHours: { openTime: string; closeTime: string },
    currentTime: number,
    checkTimeRange: boolean = true
  ): boolean {
    // 使用TimeRangeValidator类
    return TimeRangeValidator.isTimeInRange(checkStart, duration, plan, businessHours, currentTime, checkTimeRange);
  }

  /**
   * 获取默认价格方案
   */
  getDefaultPrice(
    pricePlans: TimePricePlanVO[],
    startTime: string,
    duration: number,
    businessHours: { openTime: string; closeTime: string },
    currentTime: number
  ): TimePricePlanVO | undefined {
    // 过滤出可用的价格方案
    const filteredPlans = pricePlans.filter(plan => {
      const isValidPlan = this.isTimeInRange(startTime, duration, plan, businessHours, currentTime, false);
      return isValidPlan && plan.isEnabled;
    });

    return filteredPlans[0];
  }

  /**
   * 获取特定区域的基础价格
   */
  getBasePriceForArea(
    roomTypeVO: any,
    areaId: string,
    pricePlans: TimePricePlanVO[],
    startTime: string,
    selectedDuration: number,
    businessHours: { openTime: string; closeTime: string },
    currentTime: number
  ): {
    baseRoomFee: number;
    birthdayFee: number;
    activityFee: number;
    groupBuyFee: number;
    memberPrice: number;
  } {
    try {
      // 获取默认价格方案
      const defaultPlan = this.getDefaultPrice(pricePlans, startTime, selectedDuration, businessHours, currentTime);

      if (!defaultPlan || !defaultPlan.priceConfigList) {
        return {
          baseRoomFee: 0,
          birthdayFee: 0,
          activityFee: 0,
          groupBuyFee: 0
        };
      }

      // 从priceConfigList中提取价格
      // 在priceConfigList中找到基础房费
      const basePrice = defaultPlan.priceConfigList.find(item => item.type === 'base')?.price || 0;
      const birthdayPrice = defaultPlan.priceConfigList.find(item => item.type === 'birthday')?.price || 0;
      const activityPrice = defaultPlan.priceConfigList.find(item => item.type === 'activity')?.price || 0;
      const groupPrice = defaultPlan.priceConfigList.find(item => item.type === 'group')?.price || 0;

      // 按分转换为元
      return {
        baseRoomFee: basePrice / 100,
        birthdayFee: birthdayPrice / 100,
        activityFee: activityPrice / 100,
        groupBuyFee: groupPrice / 100,
        memberPrice: 0
      };
    } catch (error) {
      console.error('[HourlyBillingInteractor] getBasePriceForArea error:', error);
      return {
        baseRoomFee: 0,
        birthdayFee: 0,
        activityFee: 0,
        groupBuyFee: 0,
        memberPrice: 0
      };
    }
  }

  /**
   * 获取有效的价格选项
   * @param pricePlans 价格方案
   * @param areaId 区域ID
   * @param holidayVO 节假日
   * @param roomTypeId 房间类型ID
   * @param startTime 开始时间
   * @param selectedDuration 选中的时长
   * @param businessHours 营业时间
   * @param currentTime 当前时间
   */
  getValidPriceOptions(
    pricePlans: TimePricePlanVO[],
    areaId: string,
    holidayVO: any,
    roomTypeId: string,
    startTime: string,
    selectedDuration: number,
    businessHours: { openTime: string; closeTime: string },
    currentTime: number
  ): PriceOption[] {
    if (!pricePlans || pricePlans.length === 0) {
      return [];
    }

    // 过滤出有效的价格方案，只根据日期/星期筛选
    const filteredPlans = pricePlans.filter(plan => {
      // 首先检查方案是否启用
      if (!plan.isEnabled) {
        return false;
      }

      const currentDate = new Date(currentTime * 1000);
      const currentDay = currentDate.getDay() || 7; // 转换为 1-7

      // 检查日期类型匹配
      if (plan.timeConfig?.timeType === 'date') {
        // 判断是否在日期范围内
        const start = new Date(plan.timeConfig.dateConfig?.dayStart || '');
        const end = new Date(plan.timeConfig.dateConfig?.dayEnd || '');
        const isValid = currentDate >= start && currentDate <= end;
        return isValid;
      } else if (plan.timeConfig?.timeType === 'weekday') {
        // 判断是否在周期内
        const allowedWeeks = plan.timeConfig.weekdayConfig?.weeks || [];
        const isValid = allowedWeeks.includes(currentDay);
        return isValid;
      }

      // 如果没有时间配置，视为有效
      return true;
    });

    if (filteredPlans.length === 0) {
      return [];
    }

    // 选择优先级最高的价格方案
    // 1. 同时匹配房间类型和区域的方案
    // 2. 只匹配房间类型的方案
    // 3. 默认方案
    // 优先选择包含当前时间的方案

    // 定义一个函数来检查方案时间段是否包含当前时间
    const isPlanTimeContainingCurrentTime = (plan: TimePricePlanVO): boolean => {
      const timeDetails = PricingRules.getPlanTimeDetails(startTime, currentTime, plan);
      if (!timeDetails) return false;

      const { timeStart, timeEnd } = timeDetails;
      const currentTimeMinutes = TimeModel.timeToMinutes(startTime);
      const startMinutes = TimeModel.timeToMinutes(timeStart);
      const endMinutes = TimeModel.timeToMinutes(timeEnd);

      // 处理跨天的情况
      if (endMinutes <= startMinutes) {
        // 如 22:00-06:00
        return currentTimeMinutes >= startMinutes || currentTimeMinutes < endMinutes;
      } else {
        return currentTimeMinutes >= startMinutes && currentTimeMinutes < endMinutes;
      }
    };

    // 选择目标价格方案
    let targetPlan: TimePricePlanVO | undefined;

    // 优先级1：同时匹配房间类型、区域和当前时间的方案
    targetPlan = filteredPlans.find(
      plan => PricingRules.isRoomTypeInPlan(plan, roomTypeId) && PricingRules.isAreaInPlan(plan, areaId) && isPlanTimeContainingCurrentTime(plan)
    );

    // 优先级2：如果没找到，尝试匹配房间类型和区域的方案
    if (!targetPlan) {
      targetPlan = filteredPlans.find(plan => PricingRules.isRoomTypeInPlan(plan, roomTypeId) && PricingRules.isAreaInPlan(plan, areaId));
    }

    // 优先级3：如果还没找到，尝试匹配房间类型的方案
    if (!targetPlan) {
      targetPlan = filteredPlans.find(plan => PricingRules.isRoomTypeInPlan(plan, roomTypeId));
    }

    // 优先级4：如果还没找到，使用第一个有效方案
    if (!targetPlan) {
      targetPlan = filteredPlans[0];
    }

    if (!targetPlan || !targetPlan.priceConfigList) {
      return [];
    }

    // 转换为价格选项格式
    const priceOptions: PriceOption[] = [];

    // 获取方案时间范围
    const timeDetails = PricingRules.getPlanTimeDetails('00:00', currentTime, targetPlan);
    if (!timeDetails) {
      return [];
    }
    const { timeStart, timeEnd } = timeDetails;

    // 处理并添加各种价格类型
    // 基础价格
    const baseConfig = targetPlan.priceConfigList.find(item => item.type === 'base');
    if (baseConfig) {
      // 使用PricingRules获取最终价格
      const finalBasePrice = PricingRules.getFinalPrice(baseConfig, holidayVO, areaId);

      priceOptions.push({
        id: 'base',
        label: '基础房费',
        value: 'baseRoomFee',
        prices: {
          baseRoomFee: finalBasePrice,
          birthdayFee: 0,
          activityFee: 0,
          groupBuyFee: 0
        },
        timeRanges: [{ start: timeStart, end: timeEnd }]
      });
    }

    // 生日价格
    const birthdayConfig = targetPlan.priceConfigList.find(item => item.type === 'birthday');
    if (birthdayConfig) {
      // 使用PricingRules获取最终价格
      const finalBirthdayPrice = PricingRules.getFinalPrice(birthdayConfig, holidayVO, areaId);

      priceOptions.push({
        id: 'birthday',
        label: '生日价格',
        value: 'birthdayFee',
        prices: {
          baseRoomFee: 0,
          birthdayFee: finalBirthdayPrice,
          activityFee: 0,
          groupBuyFee: 0
        },
        timeRanges: [{ start: timeStart, end: timeEnd }]
      });
    }

    // 活动价格
    const activityConfig = targetPlan.priceConfigList.find(item => item.type === 'activity');
    if (activityConfig) {
      // 使用PricingRules获取最终价格
      const finalActivityPrice = PricingRules.getFinalPrice(activityConfig, holidayVO, areaId);

      priceOptions.push({
        id: 'activity',
        label: '活动价格',
        value: 'activityFee',
        prices: {
          baseRoomFee: 0,
          birthdayFee: 0,
          activityFee: finalActivityPrice,
          groupBuyFee: 0
        },
        timeRanges: [{ start: timeStart, end: timeEnd }]
      });
    }

    // 团购价格
    const groupConfig = targetPlan.priceConfigList.find(item => item.type === 'group');
    if (groupConfig) {
      // 使用PricingRules获取最终价格
      const finalGroupPrice = PricingRules.getFinalPrice(groupConfig, holidayVO, areaId);

      priceOptions.push({
        id: 'group',
        label: '团购价格',
        value: 'groupBuyFee',
        prices: {
          baseRoomFee: 0,
          birthdayFee: 0,
          activityFee: 0,
          groupBuyFee: finalGroupPrice
        },
        timeRanges: [{ start: timeStart, end: timeEnd }]
      });
    }

    return priceOptions;
  }

  /**
   * 获取当前应用的产品列表
   */
  getCurrentProductList(validPriceOptions: PriceOption[], selectedPriceOption: PriceTypeKey, pricePlans: TimePricePlanVO[]) {
    return ProductManager.getCurrentProductList(validPriceOptions, selectedPriceOption, pricePlans);
  }

  /**
   * 获取当前适用的价格方案
   */
  getCurrentApplicablePlan(pricePlans: TimePricePlanVO[], currentTime: number): TimePricePlanVO | undefined {
    return ProductManager.getCurrentApplicablePlan(pricePlans, currentTime);
  }

  /**
   * 获取当前时段的价格详情
   */
  getPriceDetailsForTimeSlot(selectedPriceOption: PriceTypeKey, validPriceOptions: PriceOption[], pricePlans: TimePricePlanVO[]) {
    return PricingRules.getPriceDetailsForTimeSlot(selectedPriceOption, validPriceOptions, pricePlans);
  }

  /**
   * 获取所有时段价格（包括不参与计算的）
   */
  getAllTimeSlotPrices(
    pricePlans: TimePricePlanVO[],
    areaId: string,
    holidayVO: any,
    roomTypeId: string,
    currentTime: number,
    baseTimePriceFee: number // 单位：分
  ): TimeSlotPrice[] {
    const result: TimeSlotPrice[] = [];
    console.log('[HourlyBillingInteractor] getAllTimeSlotPrices pricePlans', pricePlans, baseTimePriceFee);
    // 如果没有价格方案但有全时段价格，直接返回全时段价格
    if ((!pricePlans || pricePlans.length === 0) && baseTimePriceFee > 0) {
      result.push({
        planId: 'base',
        planName: '全时段价格',
        timeRange: { start: '00:00', end: '00:00' },
        prices: {
          baseRoomFee: baseTimePriceFee / 100,
          birthdayFee: baseTimePriceFee / 100,
          activityFee: baseTimePriceFee / 100,
          groupBuyFee: baseTimePriceFee / 100,
          memberPrice: baseTimePriceFee / 100
        }
      });
      return result;
    }

    // 如果既没有价格方案也没有全时段价格，返回空数组
    if (!pricePlans || pricePlans.length === 0) {
      return [];
    }

    // 获取门店营业时间
    const venue = useDeviceStore().venue;
    const openTime = venue?.startHours || '06:00';
    const closeTime = venue?.endHours || '06:00'; // 默认营业到次日6点

    // 确保时间戳格式正确 - 如果是毫秒，转换为秒
    const normalizedCurrentTime = currentTime > 9999999999 ? Math.floor(currentTime / 1000) : currentTime;

    // 过滤出有效的价格方案 - 只按日期或星期过滤，不按时间段过滤
    const filteredPlans = pricePlans.filter(plan => {
      // 首先检查方案是否启用
      if (!plan.isEnabled) {
        return false;
      }

      const currentDate = new Date(normalizedCurrentTime * 1000);
      const currentDay = currentDate.getDay() || 7; // 转换为 1-7

      // 检查日期类型匹配
      if (plan.timeConfig?.timeType === 'date') {
        // 判断是否在日期范围内
        const start = new Date(plan.timeConfig.dateConfig?.dayStart || '');
        const end = new Date(plan.timeConfig.dateConfig?.dayEnd || '');
        const isValid = currentDate >= start && currentDate <= end;
        return isValid;
      } else if (plan.timeConfig?.timeType === 'weekday') {
        // 判断是否在周期内
        const allowedWeeks = plan.timeConfig.weekdayConfig?.weeks || [];
        const isValid = allowedWeeks.includes(currentDay);
        return isValid;
      }

      // 如果没有时间配置，视为有效
      return true;
    });

    // 按相同规则挑选最终生效的价格方案，保证展示与计算一致
    const isPlanTimeContainingCurrentTime = (plan: TimePricePlanVO): boolean => {
      const timeDetails = PricingRules.getPlanTimeDetails('00:00', normalizedCurrentTime, plan);
      if (!timeDetails) return false;

      const { timeStart, timeEnd } = timeDetails;
      const nowDate = new Date(normalizedCurrentTime * 1000);
      const nowMinutes = nowDate.getHours() * 60 + nowDate.getMinutes();
      const startMinutes = TimeModel.timeToMinutes(timeStart);
      const endMinutes = TimeModel.timeToMinutes(timeEnd);

      if (endMinutes <= startMinutes) {
        // 跨天段 22:00-06:00
        return nowMinutes >= startMinutes || nowMinutes < endMinutes;
      }
      return nowMinutes >= startMinutes && nowMinutes < endMinutes;
    };

    let targetPlan: TimePricePlanVO | undefined = filteredPlans.find(
      p => PricingRules.isRoomTypeInPlan(p, roomTypeId) && PricingRules.isAreaInPlan(p, areaId) && isPlanTimeContainingCurrentTime(p)
    );

    if (!targetPlan) {
      targetPlan = filteredPlans.find(p => PricingRules.isRoomTypeInPlan(p, roomTypeId) && PricingRules.isAreaInPlan(p, areaId));
    }
    if (!targetPlan) {
      targetPlan = filteredPlans.find(p => PricingRules.isRoomTypeInPlan(p, roomTypeId));
    }
    if (!targetPlan) {
      targetPlan = filteredPlans[0];
    }

    // 显示所有符合房间类型和区域匹配的方案价格
    const plansForProcessing = filteredPlans.filter(plan => {
      const roomTypeMatch = PricingRules.isRoomTypeInPlan(plan, roomTypeId);
      const areaMatch = PricingRules.isAreaInPlan(plan, areaId);
      return roomTypeMatch && areaMatch;
    });

    // 处理符合条件的价格方案
    plansForProcessing.forEach(plan => {
      if (!plan.priceConfigList) {
        return;
      }

      // 获取方案时间范围
      const timeDetails = PricingRules.getPlanTimeDetails('00:00', normalizedCurrentTime, plan);
      if (!timeDetails) {
        return;
      }

      const { timeStart, timeEnd } = timeDetails;

      // 处理基础价格配置
      const baseConfig = plan.priceConfigList.find(item => item.type === 'base');
      let finalBasePrice = 0; // 存储最终基础价格，用于回退

      if (baseConfig) {
        // 使用PricingRules获取最终价格
        finalBasePrice = PricingRules.getFinalPrice(baseConfig, holidayVO, areaId);

        // 添加基础价格方案
        result.push({
          planId: plan.id ?? '',
          planName: plan.name ?? '',
          timeRange: { start: timeStart, end: timeEnd },
          prices: {
            baseRoomFee: finalBasePrice,
            birthdayFee: 0,
            activityFee: 0,
            groupBuyFee: 0
          }
        });
      }

      // 如果有基础价格，处理其他价格类型
      if (finalBasePrice > 0) {
        // 处理其他价格类型（生日、活动、团购）
        ['birthday', 'activity', 'group'].forEach(type => {
          const config = plan.priceConfigList?.find(item => item.type === type);

          if (config) {
            // 处理特殊价格类型存在的情况
            // 使用PricingRules获取最终价格
            const finalSpecialPrice = PricingRules.getFinalPrice(config, holidayVO, areaId);

            // 添加特殊价格方案
            result.push({
              planId: plan.id ?? '',
              planName: plan.name ?? '',
              timeRange: { start: timeStart, end: timeEnd },
              prices: {
                baseRoomFee: 0, // 不显示基础价格
                birthdayFee: type === 'birthday' ? finalSpecialPrice : 0,
                activityFee: type === 'activity' ? finalSpecialPrice : 0,
                groupBuyFee: type === 'group' ? finalSpecialPrice : 0
              }
            });
          } else {
            // 处理特殊价格类型不存在的情况，使用基础价格作为回退

            // 添加使用基础价格回退的方案
            result.push({
              planId: plan.id ?? '',
              planName: plan.name ?? '',
              timeRange: { start: timeStart, end: timeEnd },
              prices: {
                baseRoomFee: 0, // 不显示基础价格
                birthdayFee: type === 'birthday' ? finalBasePrice : 0,
                activityFee: type === 'activity' ? finalBasePrice : 0,
                groupBuyFee: type === 'group' ? finalBasePrice : 0
              },
              fallbackPrice: true // 标记为回退价格
            });
          }
        });
      }
    });

    // 添加全时段价格
    if (baseTimePriceFee > 0) {
      result.push({
        planId: 'base',
        planName: '全时段价格',
        timeRange: { start: '00:00', end: '00:00' },
        prices: {
          baseRoomFee: baseTimePriceFee / 100,
          birthdayFee: baseTimePriceFee / 100, // 使用相同价格作为所有价格类型的兜底
          activityFee: baseTimePriceFee / 100, // 使用相同价格作为所有价格类型的兜底
          groupBuyFee: baseTimePriceFee / 100 // 使用相同价格作为所有价格类型的兜底
        }
      });
    }

    // 过滤掉所有价格为0的方案
    const filteredResult = result.filter(item => {
      // 确保至少有一个价格类型的价格大于0
      const hasNonZeroPrice = item.prices.baseRoomFee > 0 || item.prices.birthdayFee > 0 || item.prices.activityFee > 0 || item.prices.groupBuyFee > 0;
      return hasNonZeroPrice;
    });

    return filteredResult;
  }

  /**
   * 根据金额反算价格和时长
   */
  calculatePriceDetailsFromAmount(
    amount: number,
    startTime: string,
    currentTime: number,
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePrice: { baseRoomFee: number; birthdayFee: number; activityFee: number; groupBuyFee: number },
    baseTimePriceFee: number = 0,
    pricePlans: TimePricePlanVO[] = [],
    areaId: string = '',
    holidayVO: any = null,
    roomTypeId: string = '',
    providedValidPrices?: Price[]
  ): { totalPrice: number; details: PriceDetail[] } {
    return PriceCalculator.calculatePriceDetailsFromAmount(
      amount,
      startTime,
      currentTime,
      validPriceOptions,
      selectedPriceOption,
      basePrice,
      baseTimePriceFee,
      pricePlans,
      areaId,
      holidayVO,
      roomTypeId,
      providedValidPrices
    );
  }

  /**
   * 根据结束时间反算价格和时长
   */
  calculatePriceDetailsFromEndTime(
    startTime: string,
    endTime: string | null,
    currentTime: number,
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePrice: { baseRoomFee: number; birthdayFee: number; activityFee: number; groupBuyFee: number },
    baseTimePriceFee: number = 0,
    pricePlans: TimePricePlanVO[] = [],
    areaId: string = '',
    holidayVO: any = null,
    roomTypeId: string = ''
  ): { totalPrice: number; details: PriceDetail[] } {
    return PriceCalculator.calculatePriceDetailsFromEndTime(
      startTime,
      endTime,
      currentTime,
      validPriceOptions,
      selectedPriceOption,
      basePrice,
      baseTimePriceFee,
      pricePlans,
      areaId,
      holidayVO,
      roomTypeId
    );
  }

  /**
   * 计时模式下的价格计算
   */
  calculatePriceDetailsForTimer(
    startTime: string,
    currentTime: number,
    validPriceOptions: PriceOption[],
    selectedPriceOption: PriceTypeKey,
    basePrice: { baseRoomFee: number; birthdayFee: number; activityFee: number; groupBuyFee: number },
    baseTimePriceFee: number = 0,
    pricePlans: TimePricePlanVO[] = [],
    areaId: string = '',
    holidayVO: any = null,
    roomTypeId: string = '',
    providedValidPrices?: Price[]
  ): { totalPrice: number; details: PriceDetail[] } {
    return PriceCalculator.calculatePriceDetailsForTimer(
      startTime,
      currentTime,
      validPriceOptions,
      selectedPriceOption,
      basePrice,
      baseTimePriceFee,
      pricePlans,
      areaId,
      holidayVO,
      roomTypeId,
      providedValidPrices
    );
  }

  /**
   * 将时间字符串转换为分钟数
   */
  private parseTimeToMinutes(time: string): number {
    return TimeModel.timeToMinutes(time);
  }

  /**
   * 向时间字符串添加分钟数
   */
  public addMinutesToTime(time: string, minutes: number): string {
    return TimeModel.addMinutesToTime(time, minutes);
  }
}

export function useHourlyBillingInteractor(): HourlyBillingInteractor {
  return new HourlyBillingInteractor();
}
