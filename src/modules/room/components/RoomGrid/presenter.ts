import { computed, reactive, ComputedRef } from 'vue';
import { StageVO } from '../../types/stageVO';
import { ExtendedStageVO } from '../../types/extendedStageVO';
import { useStageStore } from '@/stores/stageStore';

import { RoomGridConverter } from './converter';
import { useRoomGridInteractor } from './interactor';
import { IRoomGridViewModel, IRoomGridComputed, DISPLAY_MODES } from './viewmodel';

// 从stageStatus导入常量和函数
import { ROOM_STATUS, SESSION_PAY_STATUS, SESSION_TAGS } from '../../constants/stageStatus';

import { convertToYuan } from '@/utils/priceUtils';

// 导入图标组件
import { LockIcon, BirthdayIcon, ChangyinIcon, UnionIcon, TimingIcon, BookedIcon, TimeIcon } from '../icons';

export class RoomGridPresenter implements IRoomGridViewModel {
  private stageStore = useStageStore();
  private interactor = useRoomGridInteractor();

  // 状态管理
  public state = reactive(RoomGridConverter.createInitialState());

  // 计算属性
  public computed: IRoomGridComputed = {
    // 处理后的房间数据
    processedStages: computed<ExtendedStageVO[]>(() => {
      // 直接从 stageStore 获取数据（避免重新处理，保持状态一致性）
      const storeStages = this.stageStore.getStages();

      // 应用过滤条件
      let filteredStages = storeStages;

      // 1. 应用搜索文本过滤
      if (this.state.searchText) {
        const searchText = this.state.searchText.toLowerCase();
        filteredStages = filteredStages.filter(stage => {
          // 搜索房间名称
          if (stage.roomVO?.name?.toLowerCase().includes(searchText)) {
            return true;
          }
          // 搜索房间类型
          if (stage.roomTypeVO?.name?.toLowerCase().includes(searchText)) {
            return true;
          }
          // 搜索区域名称
          if (stage.areaVO?.name?.toLowerCase().includes(searchText)) {
            return true;
          }
          // 搜索客户标签
          if (stage.sessionVO?.customerTag?.toLowerCase().includes(searchText)) {
            return true;
          }
          return false;
        });
      }

      // 2. 应用过滤条件
      if (this.state.filters && this.state.filters.length > 0) {
        filteredStages = filteredStages.filter(stage => {
          let matched = true;

          for (const filter of this.state.filters || []) {
            // 区域筛选
            if (filter.startsWith('area_')) {
              const areaName = filter.split('area_')[1];

              // 如果过滤条件是"未设置区域"
              if (areaName === '未设置区域') {
                // 包厢没有设置区域时匹配
                if (stage.areaVO?.name) {
                  matched = false;
                  break;
                }
              } else {
                // 正常区域名称比较
                if (stage.areaVO?.name?.toLowerCase() !== areaName.toLowerCase()) {
                  matched = false;
                  break;
                }
              }
            }
            // 类型筛选
            else if (filter.startsWith('type_')) {
              const typeName = filter.split('type_')[1];
              if (stage.roomTypeVO?.name?.toLowerCase() !== typeName) {
                matched = false;
                break;
              }
            }
            // 状态筛选 - 使用roomStatus
            else if (filter.startsWith('status_')) {
              const status = filter.split('status_')[1];
              if (stage.roomStatus?.toLowerCase() !== status) {
                matched = false;
                break;
              }
            }
            // 支付状态筛选
            else if (filter.startsWith('pay_')) {
              const payStatus = filter.split('pay_')[1];
              if (stage.payStatus?.toLowerCase() !== payStatus) {
                matched = false;
                break;
              }
            }
          }

          return matched;
        });
      }

      // 3. 应用自定义过滤函数
      if (typeof this.state.filterFn === 'function') {
        filteredStages = filteredStages.filter(this.state.filterFn);
      }

      // 返回过滤后的数据
      return filteredStages;
    }),

    // 状态数量统计
    statusCounts: computed(() => {
      return RoomGridConverter.calculateStatusCounts(this.computed.processedStages.value);
    }),

    // 分组数据
    groupedStages: computed(() => {
      if (this.state.displayMode !== DISPLAY_MODES.GROUP) {
        return {};
      }

      return RoomGridConverter.groupStagesByArea(this.computed.processedStages.value);
    }),

    // 区域列表
    allAreas: computed(() => {
      return RoomGridConverter.getAllAreas(this.computed.processedStages.value);
    }),

    // 判断是否为计时模式
    isTimeConsumeMode: computed(() => (stage: ExtendedStageVO) => {
      return stage?.tags?.includes(SESSION_TAGS.ISTIMECONSUME) || false;
    })
  };

  constructor() {
    // 从store获取当前选中的stage
    const currentStage = computed(() => this.stageStore.getCurrentStage);
    // 适配类型
    this.state.currentStage = currentStage.value as ExtendedStageVO | null;
  }

  // 动作实现
  public actions = {
    // 设置显示模式
    setDisplayMode: (mode: 'grid' | 'group'): void => {
      this.state.displayMode = mode;
    },

    // 获取分组数据
    getGroupedStages: (): Record<string, ExtendedStageVO[]> => {
      return this.computed.groupedStages.value;
    },

    // 获取区域名称
    getAreaName: (areaId: string): string => {
      const area = this.computed.allAreas.value.find((a: { id: string; name: string }) => a.id === areaId);
      return area ? area.name : '未知区域';
    },

    // 选中状态相关
    isSelected: (stage: ExtendedStageVO): boolean => {
      if (!this.state.currentStage || !stage?.roomVO?.id) return false;
      return this.state.currentStage.roomVO?.id === stage.roomVO.id;
    },

    setSelectedStage: (stage: ExtendedStageVO | null): void => {
      this.state.currentStage = stage;
      // 选中时同步到store
      if (stage) {
        // 转换类型并同步到store
        this.stageStore.setCurrentStage(stage as any);
      }
    },

    // UI展示辅助方法

    // 获取房间状态样式
    getRoomStatusClass: (stage: ExtendedStageVO): string => {
      // 根据房间状态返回对应的样式
      const roomStatus = stage?.roomStatus || ROOM_STATUS.IDLE;
      const payStatus = stage?.payStatus || SESSION_PAY_STATUS.UNPAID;
      const tags = stage?.tags || [];

      if (roomStatus === ROOM_STATUS.IN_USE && tags.includes(SESSION_TAGS.TIMEOUT)) {
        return 'status-timeout';
      }

      // 状态样式映射
      const statusClasses: Record<string, string> = {
        [ROOM_STATUS.IDLE]: 'status-idle',
        [ROOM_STATUS.IN_USE]: 'status-in-use',
        [ROOM_STATUS.FAULT]: 'status-fault',
        [ROOM_STATUS.CLEANING]: 'status-cleaning',
        [ROOM_STATUS.WITH_GUEST]: 'status-with-guest'
      };

      return statusClasses[roomStatus] || 'status-idle';
    },

    // 获取房间卡片样式
    getCardStyle: (stage: ExtendedStageVO): Record<string, string> => {
      const roomStatus = stage?.roomStatus || ROOM_STATUS.IDLE;
      const payStatus = stage?.payStatus || SESSION_PAY_STATUS.UNPAID;
      const tags = stage?.tags || [];
      let backgroundColor = '#FFFFFF';
      if (roomStatus === ROOM_STATUS.IN_USE) {
        if (tags.includes(SESSION_TAGS.TIMEOUT)) {
          backgroundColor = '#F8DEA7';
        } else {
          backgroundColor = '#D5E7F2';
        }
      }
      if (roomStatus === ROOM_STATUS.FAULT) {
        backgroundColor = '#DFDEDD';
      }
      if (roomStatus === ROOM_STATUS.CLEANING) {
        backgroundColor = '#FFFFFF';
      }
      return {
        backgroundColor: backgroundColor
      };
    },

    // 获取时间范围
    getTimeRange: (stage: ExtendedStageVO): string => {
      if (!stage?.sessionVO) return '';

      // 将秒级时间戳转换为毫秒级时间戳
      const startTime = stage.sessionVO.startTime ? new Date(stage.sessionVO.startTime * 1000) : null;
      const endTime = stage.sessionVO.endTime ? new Date(stage.sessionVO.endTime * 1000) : null;

      if (!startTime) return '';

      const formatTime = (date: Date) => {
        return date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0');
      };

      // 如果是计时模式，只显示开始时间
      if (stage.tags && stage.tags.includes(SESSION_TAGS.ISTIMECONSUME)) {
        return `${startTime ? formatTime(startTime) : ''} - 🕙`;
      }

      return `${startTime ? formatTime(startTime) : ''} - ${endTime ? formatTime(endTime) : ''}`;
    },

    // 获取房间标签
    getRoomTag: (stage: ExtendedStageVO): string[] | null => {
      if (!stage?.tags || !Array.isArray(stage.tags)) return null;
      return stage.tags;
    },

    // 获取标签图标 - 修改为支持多标签
    getTagIcons: (tags: string[] | null): any[] => {
      if (!tags || !Array.isArray(tags) || tags.length === 0) return [];

      // 标签对应的图标组件映射
      const tagComponents: Record<string, any> = {
        [SESSION_TAGS.LOCKED]: LockIcon,
        [SESSION_TAGS.BIRTHDAY]: BirthdayIcon,
        [SESSION_TAGS.CHANGYIN]: ChangyinIcon,
        [SESSION_TAGS.UNION]: UnionIcon
      };

      // 过滤掉超时标签和无效标签，返回有效的图标组件
      return tags
        .filter(tag => tag !== SESSION_TAGS.TIMEOUT && tag !== SESSION_TAGS.ISTIMECONSUME) // 过滤掉超时和计时标签
        .map(tag => tagComponents[tag])
        .filter(icon => icon !== undefined && icon !== null);
    },

    // 获取标签图标容器样式类
    getTagIconsContainerClass: (tags: string[] | null): string => {
      if (!tags || !Array.isArray(tags)) return '';

      // 过滤掉超时标签
      const validTags = tags.filter(tag => tag !== SESSION_TAGS.TIMEOUT);

      if (validTags.length > 3) {
        return 'grid-layout'; // 应用2x2网格布局
      } else {
        return 'horizontal-layout'; // 应用水平布局
      }
    },

    // 保留getTagIcon方法以向后兼容
    getTagIcon: (tag: string | null): any => {
      if (!tag) return null;

      const tagComponents: Record<string, any> = {
        [SESSION_TAGS.LOCKED]: LockIcon,
        [SESSION_TAGS.BIRTHDAY]: BirthdayIcon,
        [SESSION_TAGS.CHANGYIN]: ChangyinIcon,
        [SESSION_TAGS.ISTIMECONSUME]: TimeIcon
      };

      return tagComponents[tag] || null;
    },

    // 获取支付状态文本
    getPayStatusText: (stage: ExtendedStageVO): string => {
      if (!stage?.sessionVO) return '';

      const isPaid = stage.sessionVO.payStatus === SESSION_PAY_STATUS.PAID;
      return isPaid ? '已结: ' : '未结: ';
    },

    // 获取金额
    getAmount: (stage: ExtendedStageVO): string => {
      if (!stage?.sessionVO?.totalFee) return '0.00';

      const payStatus = stage.sessionVO.payStatus;
      // 使用未结金额或总金额
      const amount = payStatus === SESSION_PAY_STATUS.UNPAID ? stage.sessionVO.unpaidAmount : stage.sessionVO.totalFee;

      // 格式化金额为两位小数
      return convertToYuan(amount).toFixed(2);
    },

    // 判断是否未结账
    isUnpaid: (stage: ExtendedStageVO): boolean => {
      if (!stage?.sessionVO) return false;
      return stage.sessionVO.payStatus === SESSION_PAY_STATUS.UNPAID;
    },

    // 获取价格样式类
    getPriceClass: (stage: ExtendedStageVO): string => {
      if (!stage?.sessionVO) return '';

      const isPaid = stage.sessionVO.payStatus === SESSION_PAY_STATUS.PAID;
      return isPaid ? 'paid' : 'unpaid';
    },

    // 获取会话标记
    getSessionMark: (stage: ExtendedStageVO): string | null => {
      if (!stage?.sessionVO) return null;

      const sessionStatus = stage.sessionVO.status || '';

      // 根据会话状态判断标记
      if (sessionStatus.includes('union')) return SESSION_TAGS.UNION;
      if (sessionStatus.includes('timing')) return SESSION_TAGS.TIMING;
      if (sessionStatus.includes('booked')) return SESSION_TAGS.BOOKED;

      return null;
    },

    // 获取标记图标
    getMarkIcon: (mark: string | null): any => {
      if (!mark) return null;

      const markComponents: Record<string, any> = {
        [SESSION_TAGS.UNION]: UnionIcon,
        [SESSION_TAGS.TIMING]: TimingIcon,
        [SESSION_TAGS.BOOKED]: BookedIcon,
        [SESSION_TAGS.ISTIMECONSUME]: TimeIcon
      };

      return markComponents[mark] || null;
    },

    // 获取标记文本
    getMarkText: (mark: string | null): string => {
      if (!mark) return '';

      const markTexts: Record<string, string> = {
        [SESSION_TAGS.UNION]: '并台',
        [SESSION_TAGS.TIMING]: '计时',
        [SESSION_TAGS.BOOKED]: '预订',
        [SESSION_TAGS.ISTIMECONSUME]: '计时'
      };

      return markTexts[mark] || '';
    }
  };
}

export function useRoomGridPresenter(props: { stages?: StageVO[]; loading?: boolean; displayMode?: 'grid' | 'group' }): IRoomGridViewModel {
  const presenter = new RoomGridPresenter();
  const stageStore = useStageStore();

  // 初始化状态
  // 如果传入了stages，就使用传入的stages
  if (props.stages && Array.isArray(props.stages)) {
    // 使用全局stageStore，将传入的stages保存到store中
    stageStore.setStages(props.stages as any);
  }

  presenter.state.loading = props.loading ?? false;
  if (props.displayMode) {
    presenter.state.displayMode = props.displayMode as 'grid' | 'group';
  }

  return presenter;
}
