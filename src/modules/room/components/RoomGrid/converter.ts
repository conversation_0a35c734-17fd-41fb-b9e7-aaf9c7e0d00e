import { StageVO } from '../../types/stageVO';
import { ExtendedStageVO } from '../../types/extendedStageVO';
import { ROOM_STATUS } from '@/modules/room/constants/stageStatus';
import { IRoomGridState } from './viewmodel';

export class RoomGridConverter {
  // 创建初始状态
  static createInitialState(): IRoomGridState {
    return {
      stages: [],
      isProcessing: false,
      loading: false,
      currentStage: null,
      displayMode: 'grid',
      searchText: '',
      sortType: 'default',
      filters: []
    };
  }

  // 处理房间数据 - 将StageVO转换为ExtendedStageVO
  static processStages(stages: StageVO[]): ExtendedStageVO[] {
    // 确保stages是一个有效的数组
    if (!Array.isArray(stages)) {
      return [];
    }

    // 克隆并扩展原始数据
    try {
      const extendedStages = stages.map(stage => {
        if (!stage) return stage as ExtendedStageVO;

        // 创建扩展对象
        const extendedStage: ExtendedStageVO = { ...stage };

        // 添加combinedStatus属性
        if (extendedStage.roomVO) {
          const roomStatus = extendedStage.roomVO.status || ROOM_STATUS.IDLE;
          extendedStage.combinedStatus = roomStatus;
          extendedStage.roomStatus = roomStatus;
        }

        // 添加支付状态
        if (extendedStage.sessionVO) {
          extendedStage.payStatus = extendedStage.sessionVO.payStatus;
        }

        // 解析标签
        if (extendedStage.roomVO && extendedStage.roomVO.tag) {
          try {
            extendedStage.tags = JSON.parse(extendedStage.roomVO.tag);
          } catch {
            // 如果不是JSON格式，尝试用逗号分隔
            extendedStage.tags = extendedStage.roomVO.tag.split(',');
          }
        } else {
          extendedStage.tags = [];
        }

        return extendedStage;
      });

      return extendedStages;
    } catch (error) {
      // 发生错误时，尝试返回转换后的数据
      console.error('[RoomGridConverter.processStages] 处理出错:', error);
      return stages as ExtendedStageVO[];
    }
  }

  // 计算各状态的房间数量
  static calculateStatusCounts(stages: ExtendedStageVO[]): Record<string, number> {
    if (!Array.isArray(stages) || stages.length === 0) {
      return {};
    }

    // 只考虑有效的stage（有roomVO的对象）
    const validStages = stages.filter(stage => stage && stage.roomVO);

    // 如果没有有效的stage，返回空对象
    if (validStages.length === 0) {
      return {};
    }

    // 统计各状态数量
    const counts: Record<string, number> = {};

    validStages.forEach(stage => {
      // 使用combinedStatus或roomVO.status
      const status = stage.combinedStatus || stage.roomVO?.status || ROOM_STATUS.IDLE;

      if (!counts[status]) {
        counts[status] = 0;
      }
      counts[status]++;
    });

    return counts;
  }

  // 按区域分组
  static groupStagesByArea(stages: ExtendedStageVO[]): Record<string, ExtendedStageVO[]> {
    if (!Array.isArray(stages) || stages.length === 0) {
      return {};
    }

    const groupedStages: Record<string, ExtendedStageVO[]> = {};

    stages.forEach(stage => {
      if (!stage) return;

      // 如果包厢没有设置区域，归类到"未设置区域"组
      let areaId: string;
      if (stage.areaVO && stage.areaVO.id) {
        areaId = stage.areaVO.id;
      } else {
        // 为没有设置区域的包厢创建一个特殊分组
        areaId = 'no-area';
      }

      if (!groupedStages[areaId]) {
        groupedStages[areaId] = [];
      }

      groupedStages[areaId].push(stage);
    });

    return groupedStages;
  }

  // 获取所有区域
  static getAllAreas(stages: ExtendedStageVO[]): { id: string; name: string }[] {
    if (!Array.isArray(stages) || stages.length === 0) {
      return [];
    }

    const areasMap = new Map<string, { id: string; name: string }>();
    let hasNoAreaStages = false;

    stages.forEach(stage => {
      if (!stage) return;

      if (stage.areaVO && stage.areaVO.id && stage.areaVO.name) {
        // 直接从stage.areaVO获取区域ID和名称
        const areaId = stage.areaVO.id;
        const areaName = stage.areaVO.name;

        if (!areasMap.has(areaId)) {
          areasMap.set(areaId, { id: areaId, name: areaName });
        }
      } else {
        // 记录是否有没有设置区域的包厢
        hasNoAreaStages = true;
      }
    });

    // 如果有没有设置区域的包厢，添加"未设置区域"分组
    if (hasNoAreaStages) {
      areasMap.set('no-area', { id: 'no-area', name: '未设置区域' });
    }

    return Array.from(areasMap.values());
  }
}
