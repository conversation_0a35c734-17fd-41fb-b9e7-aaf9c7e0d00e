<template>
  <div>
    <!-- 网格视图模式 -->
    <div v-if="displayMode === 'grid'" class="box-grid">
      <!-- 主要内容区域 -->
      <template v-if="processedStages.length > 0">
        <div
          v-for="(stage, index) in processedStages"
          :key="index"
          class="box-grid-item"
          :class="[vm.actions.getRoomStatusClass(stage), { 'is-selected': vm.actions.isSelected(stage) }]"
          :style="vm.actions.getCardStyle(stage)"
          @click="handleStageClick(stage)">
          <!-- 房间号和类型 -->
          <div class="room-header px-[12px] mt-[12px]">
            <div class="w-[132px] h-[52px]">
              <h3 v-if="stage?.roomVO?.name" class="room-name-text">
                {{ stage.roomVO.name }}
              </h3>
              <!-- 时间信息 -->
              <div class="text-[16px] font-[300] text-[#666] whitespace-nowrap">
                <template v-if="vm.computed.isTimeConsumeMode.value(stage)">
                  <span class="inline-flex items-center"> {{ vm.actions.getTimeRange(stage).split(' - ')[0] }} - <TimeIcon class="tag-icon ml-[4px]" /> </span>
                </template>
                <template v-else>
                  {{ vm.actions.getTimeRange(stage) }}
                </template>
              </div>
            </div>
            <!-- 右上角 图标和文字区域 -->
            <div class="w-[44px] h-[52px] flex flex-col items-end justify-start">
              <!-- 图标和文字水平布局 -->
              <div class="flex items-center gap-[4px]">
                <!-- 标签图标区域（左侧） -->
                <div v-if="vm.actions.getRoomTag(stage)" class="tag-icons-container-inline">
                  <div :class="vm.actions.getTagIconsContainerClass(vm.actions.getRoomTag(stage))">
                    <component v-for="(icon, iconIndex) in vm.actions.getTagIcons(vm.actions.getRoomTag(stage))" :key="iconIndex" :is="icon" class="tag-icon" />
                  </div>
                </div>

                <!-- 状态文字区域（右侧） -->
                <div v-if="stage?.roomVO?.status === ROOM_STATUS.WITH_GUEST">
                  <span class="w-[30px] h-[30px] bg-[#000] rounded-[4px] flex items-center justify-center text-[16px] text-white">带</span>
                </div>
                <div v-else-if="stage?.roomVO?.status === ROOM_STATUS.CLEANING">
                  <span class="w-[30px] h-[30px] bg-[#000] rounded-[4px] flex items-center justify-center text-[16px] text-white">扫</span>
                </div>
                <div v-else-if="stage?.tags?.includes(SESSION_TAGS.BOOKED)">
                  <span class="w-[30px] h-[30px] bg-[#000] rounded-[4px] flex items-center justify-center text-[16px] text-white">订</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 金额信息 -->
          <div class="price-info px-[12px] px-[12px]" :class="vm.actions.getPriceClass(stage)">
            <div
              v-if="stage?.roomStatus === ROOM_STATUS.IN_USE"
              class="flex items-baseline gap-[8px]"
              :class="{ 'text-unpaid': vm.actions.isUnpaid(stage), 'text-paid': !vm.actions.isUnpaid(stage) }">
              <span>
                {{ vm.actions.getPayStatusText(stage) }}
              </span>
              <span class="amount-container">
                <template v-if="!vm.computed.isTimeConsumeMode.value(stage)">
                  <span class="currency">¥</span>
                  <span class="amount-integer">{{ vm.actions.getAmount(stage).split('.')[0] }}</span>
                  <span class="amount-decimal">.{{ vm.actions.getAmount(stage).split('.')[1] }}</span>
                </template>
                <template v-else>
                  <span class="amount-integer">-</span>
                </template>
              </span>
            </div>
          </div>
          <!-- <div class="element-divider"></div> -->
          <div class="flex flex-row justify-between px-[12px] pb-[12px]">
            <span v-if="stage?.roomTypeVO?.name" class="room-type-text">{{ stage.roomTypeVO.name }}</span>
            <!-- 房间标记区域: 右下角 -->
            <div v-if="vm.actions.getSessionMark(stage)" class="mark-info">
              <component :is="vm.actions.getMarkIcon(vm.actions.getSessionMark(stage))" />
              <span>{{ vm.actions.getMarkText(vm.actions.getSessionMark(stage)) }}</span>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="col-span-full text-center py-8 text-gray-500 w-full">暂无数据</div>
      </template>
    </div>

    <!-- 分组视图模式 -->
    <div v-else-if="displayMode === 'group'">
      <div v-for="(areaId, areaIndex) in Object.keys(groupedStages)" :key="areaIndex" class="mb-8">
        <!-- 区域标题 -->
        <h2 class="text-xl font-semibold mb-4 border-b pb-2">
          {{ vm.actions.getAreaName(areaId) }}
        </h2>

        <!-- 区域内房间网格 -->
        <div class="box-grid">
          <div
            v-for="(stage, index) in groupedStages[areaId]"
            :key="index"
            class="box-grid-item"
            :class="[vm.actions.getRoomStatusClass(stage), { 'is-selected': vm.actions.isSelected(stage) }]"
            :style="vm.actions.getCardStyle(stage)"
            @click="handleStageClick(stage)">
            <!-- 房间号和类型 -->
            <div class="room-header px-[12px] mt-[12px]">
              <div class="w-[132px] h-[52px]">
                <h3 v-if="stage?.roomVO?.name" class="room-name-text">
                  {{ stage.roomVO.name }}
                </h3>
                <!-- 时间信息 -->
                <div class="text-[16px] font-[300] text-[#666] whitespace-nowrap">
                  <template v-if="vm.computed.isTimeConsumeMode.value(stage)">
                    <span class="inline-flex items-center">
                      {{ vm.actions.getTimeRange(stage).split(' - ')[0] }} - <TimeIcon class="w-[12px] h-[12px]" />
                    </span>
                  </template>
                  <template v-else>
                    {{ vm.actions.getTimeRange(stage) }}
                  </template>
                </div>
              </div>
              <!-- 右上角 图标和文字区域 -->
              <div class="w-[44px] h-[52px] flex flex-col items-end justify-start">
                <!-- 图标和文字水平布局 -->
                <div class="flex items-center gap-[4px]">
                  <!-- 标签图标区域（左侧） -->
                  <div v-if="vm.actions.getRoomTag(stage)" class="tag-icons-container-inline">
                    <div :class="vm.actions.getTagIconsContainerClass(vm.actions.getRoomTag(stage))">
                      <component
                        v-for="(icon, iconIndex) in vm.actions.getTagIcons(vm.actions.getRoomTag(stage))"
                        :key="iconIndex"
                        :is="icon"
                        class="tag-icon" />
                    </div>
                  </div>

                  <!-- 状态文字区域（右侧） -->
                  <div v-if="stage?.roomVO?.status === ROOM_STATUS.WITH_GUEST">
                    <span class="w-[30px] h-[30px] bg-[#000] rounded-[4px] flex items-center justify-center text-[16px] text-white">带</span>
                  </div>
                  <div v-else-if="stage?.roomVO?.status === ROOM_STATUS.CLEANING">
                    <span class="w-[30px] h-[30px] bg-[#000] rounded-[4px] flex items-center justify-center text-[16px] text-white">扫</span>
                  </div>
                  <div v-else-if="stage?.tags?.includes(SESSION_TAGS.BOOKED)">
                    <span class="w-[30px] h-[30px] bg-[#000] rounded-[4px] flex items-center justify-center text-[16px] text-white">订</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 金额信息 -->
            <div class="price-info px-[12px] p-[12px]" :class="vm.actions.getPriceClass(stage)">
              <div
                v-if="stage?.roomStatus === ROOM_STATUS.IN_USE"
                class="flex items-baseline gap-[8px]"
                :class="{ 'text-unpaid': vm.actions.isUnpaid(stage), 'text-paid': !vm.actions.isUnpaid(stage) }">
                <span>
                  {{ vm.actions.getPayStatusText(stage) }}
                </span>
                <span class="amount-container">
                  <template v-if="!vm.computed.isTimeConsumeMode.value(stage)">
                    <span class="currency">¥</span>
                    <span class="amount-integer">{{ vm.actions.getAmount(stage).split('.')[0] }}</span>
                    <span class="amount-decimal">.{{ vm.actions.getAmount(stage).split('.')[1] }}</span>
                  </template>
                  <template v-else>
                    <span class="amount-integer">-</span>
                  </template>
                </span>
              </div>
            </div>
            <div class="flex flex-row justify-between px-[12px] pb-[12px]">
              <span v-if="stage?.roomTypeVO?.name" class="room-type-text">{{ stage.roomTypeVO.name }}</span>
              <!-- 房间标记区域: 右下角 -->
              <div v-if="vm.actions.getSessionMark(stage)" class="mark-info">
                <component :is="vm.actions.getMarkIcon(vm.actions.getSessionMark(stage))" />
                <span>{{ vm.actions.getMarkText(vm.actions.getSessionMark(stage)) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoomGridPresenter } from './presenter';
import { StageVO } from '../../types/stageVO';
import { ExtendedStageVO } from '../../types/extendedStageVO';
import { DISPLAY_MODES } from './viewmodel';
import { ROOM_STATUS, SESSION_TAGS } from '@/modules/room/constants/stageStatus';
import { useStageStore } from '@/stores/stageStore';
import { TimeIcon } from '../icons';
import TimingIcon from '../icons/TimingIcon.vue';

// 定义组件属性
const props = defineProps<{
  stages?: ExtendedStageVO[]; // 修改为ExtendedStageVO类型
  loading?: boolean; // 加载状态
  displayMode?: 'grid' | 'group'; // 显示模式
  selectedStageId?: string; // 选中的房间ID
  groupLabels?: Record<string, string>; // 可选的分组标签
  searchText?: string; // 搜索文本
  sortType?: string; // 排序类型
  filters?: string[]; // 过滤条件数组
}>();

// 定义事件
const emit = defineEmits<{
  (event: 'select-stage', stage: ExtendedStageVO): void;
  (event: 'update:selectedStageId', id: string): void; // 支持v-model
}>();

// 使用Presenter
const vm = useRoomGridPresenter({
  // 不再传递 stages 属性，直接从 stageStore 获取
  loading: props.loading,
  displayMode: props.displayMode
});

// 获取处理后的数据
const processedStages = computed(() => {
  // 优先使用props传入的stages
  if (props.stages && Array.isArray(props.stages)) {
    console.log(`[RoomGrid] 使用传入的stages数据: ${props.stages.length}个房间`);
    return props.stages;
  }

  // 如果没有传入stages，则使用storeStore中的数据
  const result = vm.computed.processedStages.value;
  console.log(`[RoomGrid] 使用store中的stages数据: ${Array.isArray(result) ? result.length : 0}个房间`);
  return Array.isArray(result) ? result : [];
});

// 获取分组后的数据
const groupedStages = computed(() => {
  return vm.actions.getGroupedStages();
});

// 当前显示模式
const displayMode = computed(() => props.displayMode || DISPLAY_MODES.GRID);

// 处理点击房间
function handleStageClick(stage: ExtendedStageVO) {
  if (!stage?.roomVO) return;

  // 从 stageStore 获取正确的对象（确保使用最新状态）
  const stageStore = useStageStore();
  const correctStage = stageStore.getStages().find(s => s.roomVO?.id === stage.roomVO.id) || stage;

  // 触发选择事件
  emit('select-stage', correctStage);

  // 支持v-model
  if (correctStage.roomVO.id) {
    emit('update:selectedStageId', correctStage.roomVO.id);
  }

  // 更新内部状态
  vm.actions.setSelectedStage(correctStage);
}

// 监听属性变化并更新Presenter状态
watch(
  () => props.stages,
  newStages => {
    vm.state.stages = newStages || [];
    // 数据更新后，如果有选中的包房，滚动到该包房
    nextTick(() => {
      if (vm.state.currentStage?.roomVO?.id) {
        scrollToSelectedRoom();
      }
    });
  },
  { immediate: true }
);

watch(
  () => props.loading,
  newLoading => {
    vm.state.loading = newLoading ?? false;
  },
  { immediate: true }
);

watch(
  () => props.displayMode,
  newDisplayMode => {
    if (newDisplayMode) {
      vm.actions.setDisplayMode(newDisplayMode);
    }
  },
  { immediate: true }
);

// 监听搜索文本变化
watch(
  () => props.searchText,
  newSearchText => {
    vm.state.searchText = newSearchText || '';
  },
  { immediate: true }
);

// 监听排序类型变化
watch(
  () => props.sortType,
  newSortType => {
    vm.state.sortType = newSortType || 'default';
  },
  { immediate: true }
);

// 监听过滤条件变化
watch(
  () => props.filters,
  newFilters => {
    vm.state.filters = newFilters || [];
  },
  { immediate: true }
);

// 监听selectedStageId变化
watch(
  () => props.selectedStageId,
  newId => {
    if (newId && Array.isArray(props.stages)) {
      // 找到对应的Stage
      const selectedStage = props.stages.find(s => s?.roomVO?.id === newId);
      if (selectedStage) {
        vm.actions.setSelectedStage(selectedStage as ExtendedStageVO);
      }
    }
  },
  { immediate: true }
);

// 添加滚动到选中包房的方法
function scrollToSelectedRoom() {
  const selectedElement = document.querySelector(`.box-grid-item.is-selected`);
  if (selectedElement) {
    selectedElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    });
  }
}
</script>

<style scoped>
.box-grid {
  display: grid;
  width: 100%;
  grid-template-columns: repeat(auto-fill, minmax(210px, 1fr));
  gap: 31.5px;
}

.box-grid-item {
  position: relative;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  min-height: 140px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 210px;
  height: 198px;
  border-radius: 16px;
}

.box-grid-item:hover {
  border: 1px solid #ccc;
  box-shadow: 0 12px 12px rgba(0, 0, 0, 0.1);
}

.box-grid-item:active {
  border: 1px solid #ccc;
  box-shadow: 0 12px 12px rgba(0, 0, 0, 0.1);
}

/* 选中状态样式 */
.box-grid-item.is-selected {
  border: 2px solid #ccc;
  box-shadow: 0 12px 12px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.room-number {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.room-type {
  font-size: 12px;
  color: #6b7280;
  font-weight: normal;
}

.time-info {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 10px;
}

.price-info {
  font-size: 14px;
  font-weight: 500;
  margin-top: auto;
  margin-bottom: 4px;
}

/* 未结和已结的字体颜色 */
.text-unpaid {
  color: #e23939;
}

.text-paid {
  color: #000000;
}

/* 金额容器样式 */
.amount-container {
  display: flex;
  align-items: baseline;
}

/* 人民币符号样式 */
.currency {
  font-size: 14px;
  font-weight: 400;
  margin-right: 2px;
}

/* 整数部分样式 */
.amount-integer {
  font-size: 22px;
  font-weight: 600;
  line-height: 1;
}

/* 添加金额颜色控制 */
.text-paid .amount-integer {
  color: #4c4c4c; /* 4C颜色 - 已结算 */
}

.text-unpaid .amount-integer {
  color: #e23939; /* 浅红色 - 未结算 */
}

/* 小数部分样式 */
.amount-decimal {
  font-size: 14px;
  font-weight: 400;
}

.tag-icon {
  width: 20px;
  height: 20px;
}

.tag-icons-container {
  position: absolute;
  top: 12px;
  right: 12px;
}

.tag-icons-container-inline {
  display: flex;
  align-items: center;
  justify-content: center;
}

.horizontal-layout {
  display: flex;
  flex-direction: row;
  gap: 4px;
}

.grid-layout {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 4px;
}

.mark-info {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

/* 按照不同房间状态定义的样式类 */
.room-status-idle {
  background-color: #ffffff;
}

.room-status-in_use {
  background-color: #d5e7f2;
  /* 修改为蓝色 */
}

/* 将 timeout 更新为 after_use，并修改颜色 */
.room-status-after_use {
  background-color: #f8dea7;
}

.room-status-fault {
  background-color: #dfdedd;
}

.room-status-cleaning {
  background-color: #ffffff;
  /* 更改为白色背景 */
}

/* 将 guest 更新为 with_guest */
.room-status-with_guest {
  background-color: #ffffff;
}

.element-divider {
  width: 100%;
  height: 1px;
  background: #0000000d;
}

/* 房间名称样式 - 限制两行并显示省略号 */
.room-name-text {
  font-size: 24px;
  font-weight: 300;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1.2em * 2); /* 确保最大高度为两行 */
}

/* 房间类型样式 - 限制两行并显示省略号 */
.room-type-text {
  font-size: 14px;
  color: #666;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  max-height: calc(1.2em * 2); /* 确保最大高度为两行 */
}
</style>
