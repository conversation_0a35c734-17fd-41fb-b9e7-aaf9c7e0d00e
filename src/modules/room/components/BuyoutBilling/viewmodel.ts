import { ComputedRef } from 'vue';
import type { BuyoutPricePlanVO } from '@/modules/room/entity/buyoutPricePlan';

// 定义买断方案选项接口
export interface BuyoutOption {
  label: string;
  name: string;
  priceDisplay: string;
  timeRange: string;
  selected: boolean;
  plan: BuyoutPricePlanVO;
  disabled: boolean;
  isAvailable: boolean;
  disabledReason?: string; // 不可点击的原因
}

// 定义处理产品接口
export interface ProcessedProduct {
  id: string;
  name: string;
  flavor: string;
  quantity: number;
  unit: string;
  price: number;
  totalAmount: number;
  isSoldOut?: boolean;
  currentPrice: number;
  isFree: boolean;
}

// 定义可选产品列表接口
export interface ProcessedProductList {
  standardProducts: ProcessedProduct[];
  optionalProducts: {
    type: 'ByPlan' | 'ByCount';
    count: number;
    products: ProcessedProduct[];
  };
  freeProducts: ProcessedProduct[];
  optionalFreeProducts: {
    type: 'ByPlan' | 'ByCount';
    count: number;
    products: ProcessedProduct[];
  };
}

// 定义时间范围接口
export interface TimeRange {
  currentDate: string;
  startTime: string;
  endTime: string;
  duration: number;
}

// 定义房费账单接口
export interface RoomBill {
  totalPrice: number;
  minimumCharge: number;
  duration: number;
  timeRange: TimeRange;
  details: {
    id: string;
    planName: string;
    startTime: string;
    endTime: string;
    duration: number;
    price: number;
  }[];
}

// UI状态接口
export interface IBuyoutBillingState {
  selectedPlan: BuyoutPricePlanVO | null;
}

// UI计算属性接口
export interface IBuyoutBillingComputed {
  buyoutOptions: ComputedRef<BuyoutOption[]>;
}

// UI动作接口
export interface IBuyoutBillingActions {
  selectBuyoutOption(option: BuyoutOption): void;
}

// 组合接口
export interface IBuyoutBillingViewModel {
  state: IBuyoutBillingState;
  computed: IBuyoutBillingComputed;
  actions: IBuyoutBillingActions;
}
