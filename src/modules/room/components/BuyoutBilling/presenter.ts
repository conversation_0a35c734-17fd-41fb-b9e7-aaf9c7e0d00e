import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import type {
  IBuyoutBillingViewModel,
  IBuyoutBillingState,
  IBuyoutBillingComputed,
  IBuyoutBillingActions,
  BuyoutOption,
  ProcessedProductList,
  TimeRange,
  RoomBill
} from './viewmodel';
import { BuyoutBillingConverter } from './converter';
import { useBuyoutBillingInteractor } from './interactor';
import type { BuyoutPricePlanVO } from '@/modules/room/entity/buyoutPricePlan';
import { formatYuan } from '@/utils/priceUtils';

export class BuyoutBillingPresenter implements IBuyoutBillingViewModel {
  // 注入依赖
  private interactor = useBuyoutBillingInteractor();

  // 状态
  public state = reactive<IBuyoutBillingState>(BuyoutBillingConverter.createInitialState());

  // 响应式数据源
  private _pricePlans = ref<BuyoutPricePlanVO[]>([]);
  private areaId: string = '';
  private holidayVO: any = undefined;
  private roomInfo: any = {};
  private currentTime: number = 0;
  private isActive: boolean = false;
  private instanceId: number = Date.now(); // 用于区分实例的ID

  // 事件
  private emit: (event: 'updateBill', ...args: any[]) => void;

  constructor(props: {
    pricePlans: BuyoutPricePlanVO[];
    areaId: string;
    holidayVO?: any;
    roomInfo: any;
    currentTime?: number;
    isActive?: boolean;
    emit: (event: 'updateBill', ...args: any[]) => void;
  }) {
    // 初始化响应式数据
    this._pricePlans.value = props.pricePlans || [];
    this.areaId = props.areaId;
    this.holidayVO = props.holidayVO;
    this.roomInfo = props.roomInfo;
    this.currentTime = props.currentTime || Date.now() / 1000;
    this.isActive = props.isActive || false;
    this.emit = props.emit;
    // console.log('[BuyoutBillingPresenter] props:', props);
    // 添加对props.pricePlans的监听
    watch(
      () => props.pricePlans,
      newPlans => {
        this._pricePlans.value = newPlans || [];
      },
      { immediate: true, deep: true }
    );

    this.setupLifecycle();
  }

  // 生命周期管理
  private setupLifecycle(): void {
    onMounted(() => {
      // 如果有可点击选项，自动选择第一个
      const clickableOptions = this.computed.buyoutOptions.value.filter(option => !option.disabled);
      if (clickableOptions.length > 0) {
        this.actions.selectBuyoutOption(clickableOptions[0]);
      }
    });

    // 监听价格方案变化，重新选择可用的买断选项
    watch(
      () => this._pricePlans.value,
      newPricePlans => {
        if (!this.isActive) {
          return;
        }

        // 计算可点击的选项
        setTimeout(() => {
          const clickableOptions = this.computed.buyoutOptions.value.filter(option => !option.disabled);
          // 如果有可点击的买断选项，选择第一个
          if (clickableOptions.length > 0) {
            this.actions.selectBuyoutOption(clickableOptions[0]);
          }
        }, 0);
      },
      { immediate: true, deep: true }
    );

    // 监听isActive状态变化
    watch(
      () => this.isActive,
      newActive => {
        // 当组件变为激活状态时，重新处理选项
        if (newActive && this._pricePlans.value.length > 0) {
          // 强制触发buyoutOptions计算属性重新计算
          setTimeout(() => {
            const clickableOptions = this.computed.buyoutOptions.value.filter(option => !option.disabled);
            if (clickableOptions.length > 0) {
              this.actions.selectBuyoutOption(clickableOptions[0]);
            }
          }, 0);
        }
      },
      { immediate: true }
    );
  }

  // 检查房间类型是否匹配
  private isRoomTypeMatched(plan: BuyoutPricePlanVO): boolean {
    // 如果方案没有指定房间类型，默认匹配所有
    if (!plan.roomTypeConfig || !plan.roomTypeConfig.roomTypes || plan.roomTypeConfig.roomTypes.length === 0) {
      return true;
    }

    // 如果当前房间信息中没有房间类型，查找替代属性
    let roomTypeId = this.roomInfo?.roomTypeId;

    // 如果没有roomTypeId，尝试typeId
    if (!roomTypeId && this.roomInfo?.typeId) {
      roomTypeId = this.roomInfo.typeId;
    }

    if (!roomTypeId) {
      return false;
    }

    // 检查当前房间类型是否在方案支持的房间类型列表中
    return plan.roomTypeConfig.roomTypes.some(type => type.id === roomTypeId);
  }

  // 检查区域是否匹配
  private isAreaMatched(plan: BuyoutPricePlanVO): boolean {
    // 如果方案没有指定区域，默认匹配所有
    if (!plan.areaConfig || !plan.areaConfig.areas || plan.areaConfig.areas.length === 0) {
      return true;
    }

    // 检查当前区域是否在方案支持的区域列表中
    return plan.areaConfig.areas.some(area => area.id === this.areaId);
  }

  // 计算属性
  public computed: IBuyoutBillingComputed = {
    buyoutOptions: computed((): BuyoutOption[] => {
      // console.log('[BuyoutBillingPresenter] buyoutOptions:', this._pricePlans.value);
      const filteredOptions = this._pricePlans.value
        // 筛选可显示的方案 - 移除 isWithinAvailableTime 的判断
        .filter(plan => {
          // 1. 方案必须启用
          const enabled = plan.isEnabled !== false;
          // 2. 检查日期或星期是否在范围内
          const dateRangeValid = BuyoutBillingConverter.isWithinDateRange(plan, this.currentTime);
          // 3. 检查房间类型是否匹配
          const roomTypeValid = this.isRoomTypeMatched(plan);
          // 4. 检查区域是否匹配
          const areaValid = this.isAreaMatched(plan);
          // 移除 timeValid 判断: const timeValid = BuyoutBillingConverter.isWithinAvailableTime(plan, this.currentTime);

          const result = enabled && dateRangeValid && roomTypeValid && areaValid; // <--- 恢复原来的过滤条件
          // console.log('[BuyoutBillingPresenter] buyoutOptions:', plan, result);
          return result;
        })
        .map(plan => {
          const { baseRoomFee } = this.interactor.calculateRoomPrice(plan, this.areaId, this.holidayVO);

          // 检查方案是否在可用时间段内
          const isTimeAvailable = BuyoutBillingConverter.isWithinAvailableTime(plan, this.currentTime);
          // 检查方案是否可点击（考虑 advanceDisableDuration）
          const isClickable = BuyoutBillingConverter.isPlanClickable(plan, this.currentTime);

          // 方案是否禁用：如果当前时间不在可用时间段内，或者因为 advanceDisableDuration 而不可点击
          const isDisabled = !isTimeAvailable || !isClickable;

          const displayInfo = BuyoutBillingConverter.formatDisplayLabel(plan, baseRoomFee);

          return {
            label: displayInfo.fullLabel,
            name: displayInfo.name,
            priceDisplay: displayInfo.priceDisplay,
            timeRange: BuyoutBillingConverter.formatTimeRange(plan),
            selected: plan.id === this.state.selectedPlan?.id,
            plan: plan,
            disabled: isDisabled, // <--- 更新 disabled 逻辑
            isAvailable: true, // 通过 filter 的都视为 available
            // 禁用原因：如果被禁用，显示可用时间范围
            disabledReason: isDisabled ? `可用时间 ${plan.timeConfig?.availableTimeStart || '00:00'}-${plan.timeConfig?.availableTimeEnd || '23:59'}` : ''
          };
        })
        .sort((a, b) => {
          // 可点击的排在前面，不可点击的排在后面
          if (!a.disabled && b.disabled) return -1;
          if (a.disabled && !b.disabled) return 1;
          // 如果可点击状态相同，按价格排序 - 获取基础价格
          const getBasePrice = (plan: BuyoutPricePlanVO) => {
            const baseConfig = plan.priceConfigList?.find(c => c.type === 'base');
            return baseConfig ? baseConfig.price : 0;
          };
          return getBasePrice(b.plan) - getBasePrice(a.plan);
        });

      // console.log('[BuyoutBillingPresenter] buyoutOptions:', filteredOptions);
      return filteredOptions;
    })
  };

  // 动作
  public actions: IBuyoutBillingActions = {
    selectBuyoutOption: (option: BuyoutOption) => {
      if (!this.isActive || option.disabled) {
        return;
      }

      this.state.selectedPlan = option.plan;

      let planProducts;
      try {
        planProducts = option.plan.planProducts
          ? typeof option.plan.planProducts === 'string'
            ? JSON.parse(option.plan.planProducts)
            : option.plan.planProducts
          : null;
      } catch (e) {
        console.error('解析商品数据失败:', e);
        planProducts = null;
      }
      console.log('[BuyoutBillingPresenter] 1 planProducts', planProducts);
      const processedProducts = BuyoutBillingConverter.processProductList(planProducts, option.plan.pricePlanSubProductVO || []);
      console.log('[BuyoutBillingPresenter] 2 processedProducts', processedProducts);
      const roomBill = this.interactor.processRoomBill(this.state.selectedPlan, this.currentTime, this.areaId, this.holidayVO);

      if (this.isActive) {
        this.emit('updateBill', {
          consumptionMode: 'buyout',
          minimumCharge: roomBill?.minimumCharge || 0,
          timeRange: roomBill?.timeRange,
          roombill: roomBill,
          marketBill: processedProducts
        });
      }
    }
  };
}

export function useBuyoutBillingPresenter(props: {
  pricePlans: BuyoutPricePlanVO[];
  areaId: string;
  holidayVO?: any;
  roomInfo: any;
  currentTime?: number;
  isActive?: boolean;
  emit: (event: 'updateBill', ...args: any[]) => void;
}): IBuyoutBillingViewModel {
  return new BuyoutBillingPresenter(props);
}
