import { RoomVO, BookingVO ,AreaVO, RoomTypeVO, RoomThemeVO, OrderRoomPlanVO} from '@/types/projectobj';
import type { SessionVO } from '@/api/autoGenerated';

export interface StageVO {
  roomVO: RoomVO;
  roomTypeVO: RoomTypeVO;
  roomThemeVO: RoomThemeVO;
  orderRoomPlanVOs:OrderRoomPlanVO[];
  areaVO: AreaVO;
  unionRoomId?: string;
  unionRoomName?: string;
  sessionVO: SessionVO;
  bookingVOs: BookingVO[];
} 