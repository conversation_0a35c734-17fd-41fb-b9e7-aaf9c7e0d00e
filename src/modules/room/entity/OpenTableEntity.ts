import type { SessionVO, OrderRoomPlanVO, PricePlanVO, RoomTypeVO, RoomVO, AreaVO, HolidayVO, BookingVO, BaseProductInfo } from '@/types/projectobj';
import type { BuyoutPricePlanVO } from './buyoutPricePlan';
import type { TimePricePlanVO } from './timePricePlan';

// 开台基础信息实体
export interface OpenTableBaseInfo {
  roomId: string;
  sessionId: string;
  startTime: number;
  endTime: number;
  consumptionMode: string;
  selectedAreaId: string;
  selectedRoomTypeId: string;
  buyMinute: number;
  payAmount: number;
  originalAmount: number;
  isOpenTableSettled: boolean;
  minimumCharge: number;
  currentTime: number;
}

// 房间账单实体
export interface RoomBillEntity {
  details: Array<{
    id: string;
    planName: string;
    startTime: string;
    endTime: string;
    duration: number;
    price: number;
    isTimeConsume: boolean;
  }>;
  totalPrice: number;
  minimumCharge?: number;
  isTimeConsume: boolean;
}

// 商品账单实体
export interface MarketBillEntity {
  standardProducts: Array<ProductEntity>;
  optionalProducts?: {
    products: Array<ProductEntity>;
  };
  freeProducts: Array<ProductEntity>;
  optionalFreeProducts?: {
    products: Array<ProductEntity>;
  };
}

// 商品实体
export interface ProductEntity {
  id: string;
  name?: string;
  productName?: string;
  flavors?: string;
  quantity: number;
  unit: string;
  price: number;
  currentPrice: number;
  // 原价 & 原价金额（以分为单位）
  originalPrice?: number;
  originalAmount?: number;
  totalAmount: number;
  details?: string;
  isFree?: boolean;
  isInitialOrder?: boolean;
  // 套餐相关属性
  isPackage?: boolean;
  isGroup?: boolean; // 标识是否为商品组（可选组的组合商品）
  packageDetail?: any;
  // 套餐商品ID和商品ID
  packageId?: string;
  productId?: string;
  // 原有套餐信息 - 可能需要更充分利用
  packageProductInfo?: Array<BaseProductInfo> | string;
  // 添加索引签名，以匹配billUtils.ts中的Product接口
  [key: string]: unknown;
}

// 开台请求实体
export interface OpenOrderRequestEntity {
  roomId: string;
  sessionId: string;
  startTime: number;
  endTime: number;
  consumptionMode: string;
  selectedAreaId: string;
  selectedRoomTypeId: string;
  buyMinute: number;
  payAmount: number;
  originalAmount: number;
  isOpenTableSettled: boolean;
  minimumCharge: number;
  currentTime: number;
  orderRoomPlanVOS: OrderRoomPlanVO[];
  inOrderProductInfos: ProductEntity[];
  outOrderProductInfos: ProductEntity[];
  roomVO: RoomVO;
  bookingId?: string | string[];
  venueId: string;
  pricePlanId: string;
  pricePlanName: string;
  timeChargeMode: string;
}

// 房间信息实体
export interface RoomInfoEntity {
  roomId: string;
  roomVO: RoomVO;
  roomTypeVO: RoomTypeVO;
  areaVO: AreaVO;
  sessionVO?: SessionVO;
  bookingVOs?: BookingVO[];
  holidayVO?: HolidayVO;
  pricePlanVOs: PricePlanVO[];
  buyoutPricePlanVOs?: BuyoutPricePlanVO[];
  timePricePlanVOs?: TimePricePlanVO[];
  baseTimePriceFee?: number;
  currentTime: number;
  unionRoomId?: string;
  unionRoomName?: string;
  bookingId?: string;
}

// 表单实体
export interface FormEntity {
  minimumCharge: number;
  consumptionMode: string;
  roomName: string;
  consumptionTime: string;
  customerTag: string;
  booker: string;
  roomManager: string;
  customerSource: string;
  queueNumber: string;
  settleImmediately: boolean;
  duration: number;
}
