import { receiptBuilder } from '../receipt-builder';

import { OrderDetailData } from '../order-detail/models/order-detail-data';
import { SessionOrderData } from '../session-order/models/session-order-data';
import { ProductionOrderData } from '../production-order/models/production-order-data';
import { ShiftReportData } from '../shift-report/models/shift-report-data';

import { ReceiptText, ReceiptElementType } from '../shared/models/receipt-model';
import type { RoomInfo } from '../shared/models/base-models';

// Jest 测试函数类型声明
declare const describe: (name: string, fn: () => void) => void;
declare const test: (name: string, fn: () => void | Promise<void>) => void;
declare const expect: any;

describe('ReceiptBuilder Facade', () => {
  // 消费结账单测试
  test('should build order detail receipt correctly', () => {
    // 准备测试数据
    const orderDetailData: OrderDetailData = {
      shopName: '测试门店',
      payBillId: 'OD12345',
      roomInfo: {
        name: 'VIP包房1'
      } as RoomInfo,
      roomType: '大包',
      duration: 150, // 2小时30分钟
      startTime: '2023-07-01 14:00:00',
      endTime: '2023-07-01 16:30:00',
      checkoutTime: '2023-07-01 16:35:00',
      products: [
        {
          productName: '茶水',
          price: 50,
          payPrice: 50,
          quantity: 2,
          unit: '壶',
          totalAmount: 100
        },
        {
          productName: '果盘',
          price: 88,
          payPrice: 88,
          quantity: 1,
          unit: '份',
          totalAmount: 88
        }
      ],
      productFeeTotal: 188,
      roomFeeTotal: 250,
      totalReceivable: 458,
      paymentMethods: [
        {
          method: '微信',
          amount: 458
        }
      ],
      paymentAmount: 458,
      cashierName: '收银员小李',
      printTime: '2023-07-01 16:35:00'
    };

    // 调用测试方法
    const receipt = receiptBuilder.buildOrderDetailReceipt(orderDetailData, '1234567890');

    // 验证结果
    expect(receipt).toBeDefined();
    expect(receipt.elements.length).toBeGreaterThan(0); // 确保有元素生成
    expect(receipt.elements[0].type).toBe(ReceiptElementType.TEXT);
    expect((receipt.elements[0] as ReceiptText).content).toBe('消费结账单'); // 标题
  });

  // 开台单测试
  test('should build session order receipt correctly', () => {
    // 准备测试数据
    const sessionOrderData: SessionOrderData = {
      shopName: '测试门店',
      roomInfo: {
        name: 'VIP包房1'
      } as RoomInfo,
      sessionId: 'S12345',
      reservationInfo: {
        memberCardNo: 'M54321',
        memberName: '李四',
        memberPhone: '13900139000'
      },
      openTime: '2023-07-01 14:00:00',
      startTime: '2023-07-01 14:00:00',
      endTime: '2023-07-01 16:00:00',
      roomPackages: [
        {
          planName: '2小时欢唱套餐',
          duration: 120,
          originalPrice: 198,
          actualPrice: 198
        }
      ],
      roomFeeTotal: 198,
      products: [],
      productFeeTotal: 0,
      prePayBalance: 300,
      cashierName: '前台小王',
      printTime: '2023-07-01 14:05:00'
    };

    // 调用测试方法
    const receipt = receiptBuilder.buildSessionOrderReceipt(sessionOrderData, '1234567890');

    // 验证结果
    expect(receipt).toBeDefined();
    expect(receipt.elements.length).toBeGreaterThan(0); // 确保有元素生成
    expect(receipt.elements[0].type).toBe(ReceiptElementType.TEXT);
    expect((receipt.elements[0] as ReceiptText).content).toBe('开台单'); // 标题
  });

  // 出品单测试
  test('should build production order receipt correctly', () => {
    // 准备测试数据
    const productionOrderData: ProductionOrderData = {
      roomInfo: {
        name: 'VIP包房1'
      } as RoomInfo,
      employeeName: '服务员小红',
      orderTime: '2023-07-01 15:30:00',
      orderNo: 'PO12345',
      sessionId: 'S12345',
      products: [
        {
          productName: '果盘',
          flavors: '少糖,多冰',
          price: 88,
          quantity: 1,
          unit: '份'
        },
        {
          productName: '啤酒',
          flavors: '',
          price: 30,
          quantity: 6,
          unit: '瓶'
        }
      ]
    };

    // 调用测试方法
    const receipt = receiptBuilder.buildProductionOrderReceipt(productionOrderData);

    // 验证结果
    expect(receipt).toBeDefined();
    expect(receipt.elements.length).toBeGreaterThan(0); // 确保有元素生成
    expect(receipt.elements[1].type).toBe(ReceiptElementType.TEXT);
    expect((receipt.elements[1] as ReceiptText).content).toBe('出品单'); // 标题
  });

  // 交班单测试
  test('should build shift report receipt correctly', () => {
    // 准备测试数据
    const shiftReportData: ShiftReportData = {
      shiftTime: '2023-07-01 16:00:00',
      employee: '收银员小李',
      orderCount: 25,
      businessData: {
        receivable: 5280.5
      },
      paymentMethods: [
        {
          method: '现金',
          amount: 2000
        },
        {
          method: '微信',
          amount: 2280.5
        },
        {
          method: '支付宝',
          amount: 1000
        }
      ],
      shiftId: 'SH20230701001'
    };

    // 调用测试方法
    const receipt = receiptBuilder.buildShiftReportReceipt(shiftReportData);

    // 验证结果
    expect(receipt).toBeDefined();
    expect(receipt.elements.length).toBeGreaterThan(0); // 确保有元素生成
    expect(receipt.elements[0].type).toBe(ReceiptElementType.TEXT);
    expect((receipt.elements[0] as ReceiptText).content).toBe('交班单'); // 标题
  });
}); 