import { 
  RoomInfo, 
  ExtendedProductInfo, 
  GiftProductInfo,
  PaymentDetail,
  BaseRoomPackage
} from '../../shared/models/base-models';

/**
 * 房间方案信息
 * 描述房间消费方案
 */
export interface RoomPackageItem extends BaseRoomPackage {
  // 可以添加消费结账单特有的房间方案属性
}

/**
 * 消费明细数据实体
 * 用于生成顾客消费明细账单的数据模型
 */
export interface OrderDetailData {
  shopName?: string; // 店铺名称
  roomInfo?: RoomInfo; // 包厢信息
  roomType?: string; // 包厢类型（如：小包、中包、大包）
  payBillId?: string; // 结账单号
  sessionId?: string; // 开台单号
  duration?: number; // 消费时长（分钟）
  checkoutTime?: string; // 结账时间
  startTime?: string; // 消费开始时间
  endTime?: string; // 消费结束时间
  
  // 包厢消费信息
  roomPackages?: RoomPackageItem[]; // 房间方案信息
  roomFeeTotal?: number; // 包厢消费总计
  
  // 商品消费信息
  products?: ExtendedProductInfo[]; // 普通商品（包括可能的套餐子商品）
  productFeeTotal?: number; // 商品消费总计
  
  // 赠送商品信息
  giftProducts?: GiftProductInfo[]; // 赠送商品
  giftProductTotal?: number; // 赠送小计
  
  // 账单金额信息
  totalReceivable?: number; // 应收金额
  merchantGift?: number; // 商家赠送金额
  feeToProduct?: number; // 计费抵商品金额
  memberDiscount?: number; // 会员优惠金额
  merchantDiscount?: number; // 商家优惠金额
  couponDiscount?: number; // 优惠券金额
  roundDown?: number; // 抹零金额
  paymentAmount?: number; // 付款金额
  paymentMethods?: PaymentDetail[]; // 支付方式明细
  changeAmount?: number; // 找零金额
  
  // 其他信息
  printTime?: string; // 打单时间
  discountBy?: string; // 优惠人
  cashierName?: string; // 收银员
  rotationPerson?: string; // 轮房人
  adjustReason?: string; // 调整原因
} 