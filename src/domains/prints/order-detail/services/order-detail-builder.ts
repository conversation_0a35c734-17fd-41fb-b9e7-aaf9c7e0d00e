import { Receipt } from '../../shared/models/receipt-model';
import { TextAlignment, FontSize } from '../../shared/models/receipt-model';
import { ReceiptFactory } from '../../shared/utils/receipt-factory';
import { OrderDetailData } from '../models/order-detail-data';
import { formatDateTime } from '@/utils/dateUtils';
import { formatYuan } from '@/utils/priceUtils';

/**
 * 消费结账单构建器
 * 负责将消费结账单数据转换为结构化的票据表示
 */
export class OrderDetailBuilder {
  /**
   * 构建消费明细票据
   * @param data 订单详情数据实体
   */
  build(data: OrderDetailData, venueId: string): Receipt {
    const receipt: Receipt = { elements: [] };
    const elements = receipt.elements;

    // 标题
    elements.push(
      ReceiptFactory.text('消费结账单', {
        align: TextAlignment.CENTER,
        bold: true,
        fontSize: FontSize.DOUBLE,
      })
    );
    elements.push(
      ReceiptFactory.text('\n', {
        fontSize: FontSize.NORMAL,
        bold: false,
      })
    );

    // 基本信息
    elements.push(
      ReceiptFactory.text(`店铺名称: ${data.shopName || '--'}`, {
        fontSize: FontSize.SMALL,
        align: TextAlignment.LEFT,
      })
    );
    elements.push(
      ReceiptFactory.text(`包厢名称: ${data.roomInfo?.name || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    if (data.roomType) {
      elements.push(
        ReceiptFactory.text(`包厢类型: ${data.roomType}`, {
          fontSize: FontSize.SMALL,
        })
      );
    }
    elements.push(
      ReceiptFactory.text(`结账单号: ${data.payBillId || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`开台单号: ${data.sessionId || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`消费时长: ${data.duration || 0} 分钟`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`结账时间: ${data.checkoutTime ? formatDateTime(data.checkoutTime) : '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`消费开始时间: ${data.startTime ? formatDateTime(data.startTime) : '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`消费结束时间: ${data.endTime ? formatDateTime(data.endTime) : '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );

    elements.push(ReceiptFactory.line());

    // 包厢消费信息
    elements.push(
      ReceiptFactory.text('包厢消费信息', {
        bold: true,
        align: TextAlignment.CENTER,
      })
    );

    // 包厢表格头
    elements.push(
      ReceiptFactory.tableRow([
        { text: '项目', width: 30, align: TextAlignment.LEFT },
        { text: '时长', width: 10, align: TextAlignment.RIGHT },
        { text: '原价', width: 10, align: TextAlignment.RIGHT },
        { text: '现价', width: 10, align: TextAlignment.RIGHT },
      ])
    );

    // 房间方案数据行
    if (data.roomPackages?.length) {
      const roomPackageRows = data.roomPackages.map(item => [
        { text: item.planName, width: 30 },
        { text: `${item.duration}`, width: 10, align: TextAlignment.RIGHT },
        { text: formatYuan(item.originalPrice), width: 10, align: TextAlignment.RIGHT },
        { text: formatYuan(item.actualPrice), width: 10, align: TextAlignment.RIGHT },
      ]);
      elements.push(ReceiptFactory.table(roomPackageRows));
    }

    elements.push(ReceiptFactory.line());
    elements.push(
      ReceiptFactory.text(`包厢消费总计: ${formatYuan(data.roomFeeTotal || 0)}`, {
        align: TextAlignment.RIGHT,
        bold: true,
      })
    );

    elements.push(ReceiptFactory.line());

    // 商品消费信息
    if (data.products?.length) {
      elements.push(
        ReceiptFactory.text('商品消费信息', {
          align: TextAlignment.CENTER,
          bold: true,
        })
      );

      // 商品表头
      elements.push(
        ReceiptFactory.tableRow([
          { text: '名称', width: 22 },
          { text: '数量', width: 8, align: TextAlignment.RIGHT },
          { text: '原价', width: 8, align: TextAlignment.RIGHT },
          { text: '现价', width: 8, align: TextAlignment.RIGHT },
          { text: '现价小计', width: 14, align: TextAlignment.RIGHT },
        ])
      );

      // 商品数据行
      for (const item of data.products) {
        elements.push(
          ReceiptFactory.tableRow([
            { text: item.productName, width: 22 },
            { text: `${item.quantity}${item.unit || ''}`, width: 8, align: TextAlignment.RIGHT },
            { text: formatYuan(item.price), width: 8, align: TextAlignment.RIGHT },
            { text: formatYuan(item.payPrice), width: 8, align: TextAlignment.RIGHT },
            { text: formatYuan(item.totalAmount), width: 14, align: TextAlignment.RIGHT },
          ])
        );

        // 子商品行（如果有）
        if (item.subProducts?.length) {
          for (const subItem of item.subProducts) {
            elements.push(
              ReceiptFactory.tableRow([
                { text: `    ${subItem.productName}`, width: 22 }, // 缩进表示层级关系
                { text: `${subItem.quantity}${subItem.unit || ''}`, width: 8, align: TextAlignment.RIGHT },
                { text: '', width: 8, align: TextAlignment.RIGHT }, // 留空，因为子商品价格已包含在主商品中
                { text: '', width: 8, align: TextAlignment.RIGHT }, // 留空
                { text: '', width: 14, align: TextAlignment.RIGHT }, // 留空
              ])
            );
          }
        }
      }

      elements.push(ReceiptFactory.line());
      elements.push(
        ReceiptFactory.text(`商品消费: ${formatYuan(data.productFeeTotal || 0)}`, {
          align: TextAlignment.RIGHT,
        })
      );
    }

    elements.push(ReceiptFactory.line());

    // 赠送商品信息
    if (data.giftProducts?.length) {
      elements.push(
        ReceiptFactory.tableRow([
          { text: '名称', width: 26, align: TextAlignment.LEFT },
          { text: '数量', width: 12, align: TextAlignment.RIGHT },
          { text: '原价', width: 12, align: TextAlignment.RIGHT },
          { text: '备注', width: 10, align: TextAlignment.RIGHT },
        ])
      );

      // 赠品数据行
      for (const item of data.giftProducts) {
        elements.push(
          ReceiptFactory.tableRow([
            { text: item.productName, width: 26 },
            { text: `${item.quantity}${item.unit || ''}`, width: 12, align: TextAlignment.RIGHT },
            { text: formatYuan(item.price), width: 12, align: TextAlignment.RIGHT },
            { text: item.remark || '赠送', width: 10, align: TextAlignment.RIGHT },
          ])
        );

        // 子商品行（如果有）
        if (item.subProducts?.length) {
          for (const subItem of item.subProducts) {
            elements.push(
              ReceiptFactory.tableRow([
                { text: `    ${subItem.productName}`, width: 26 }, // 缩进表示层级关系
                { text: `${subItem.quantity}${subItem.unit || ''}`, width: 12, align: TextAlignment.RIGHT },
                { text: '', width: 12, align: TextAlignment.RIGHT }, // 留空，因为子商品价格已包含在主商品中
                { text: '', width: 10, align: TextAlignment.RIGHT }, // 留空
              ])
            );
          }
        }
      }

      elements.push(ReceiptFactory.line());
      elements.push(
        ReceiptFactory.text(`赠送小计: ${formatYuan(data.giftProductTotal || 0)}`, {
          align: TextAlignment.RIGHT,
        })
      );
    }

    elements.push(ReceiptFactory.line());
    elements.push(
      ReceiptFactory.text(`商品消费总计: ${formatYuan(data.productFeeTotal || 0)}`, {
        align: TextAlignment.RIGHT,
        bold: true,
      })
    );

    // 结算信息
    elements.push(ReceiptFactory.line());
    elements.push(
      ReceiptFactory.text(`应收: ${formatYuan(data.totalReceivable || 0)}`, {
        align: TextAlignment.LEFT,
      })
    );
    elements.push(
      ReceiptFactory.text(`商家赠送: ${formatYuan(data.merchantGift || 0)}`, {
        align: TextAlignment.LEFT,
      })
    );
    // elements.push(
    //   ReceiptFactory.text(`计费抵商品: ${formatYuan(data.feeToProduct || 0)}`, {
    //     align: TextAlignment.LEFT,
    //   })
    // );
    // elements.push(
    //   ReceiptFactory.text(`会员优惠: ${formatYuan(data.memberDiscount || 0)}`, {
    //     align: TextAlignment.LEFT,
    //   })
    // );
    elements.push(
      ReceiptFactory.text(`商家优惠: ${formatYuan(data.merchantDiscount || 0)}`, {
        align: TextAlignment.LEFT,
      })
    );
    // elements.push(
    //   ReceiptFactory.text(`优惠券: ${formatYuan(data.couponDiscount || 0)}`, {
    //     align: TextAlignment.LEFT,
    //   })
    // );
    elements.push(
      ReceiptFactory.text(`抹零: ${formatYuan(data.roundDown || 0)}`, {
        align: TextAlignment.LEFT,
      })
    );

    // 付款信息
    let paymentMethodsText = '';
    if (data.paymentMethods && data.paymentMethods.length > 0) {
      const methodTexts = data.paymentMethods.map(p => `${p.method}：${formatYuan(p.amount)}`);
      paymentMethodsText = `（${methodTexts.join('，')}）`;
    }
    elements.push(
      ReceiptFactory.text(`付款金额: ${formatYuan(data.paymentAmount || 0)}${paymentMethodsText}`, {
        align: TextAlignment.LEFT,
      })
    );
    // elements.push(
    //   ReceiptFactory.text(`找零: ${formatYuan(data.changeAmount || 0)}`, {
    //     align: TextAlignment.LEFT,
    //   })
    // );

    // 底部信息
    elements.push(ReceiptFactory.emptyLine());
    elements.push(
      ReceiptFactory.text(`打单时间: ${data.printTime ? formatDateTime(data.printTime) : formatDateTime(new Date().toISOString())}`, {
        align: TextAlignment.LEFT,
      })
    );
    if (data.discountBy) {
      elements.push(
        ReceiptFactory.text(`优惠人: ${data.discountBy}`, {
          align: TextAlignment.LEFT,
        })
      );
    }
    elements.push(
      ReceiptFactory.text(`收银员: ${data.cashierName || '--'}`, {
        align: TextAlignment.LEFT,
      })
    );
    // if (data.rotationPerson) {
    //   elements.push(
    //     ReceiptFactory.text(`轮房人: ${data.rotationPerson}`, {
    //       align: TextAlignment.LEFT,
    //     })
    //   );
    // }
    elements.push(
      ReceiptFactory.text(`调整原因: ${data.adjustReason || '无'}`, {
        align: TextAlignment.LEFT,
      })
    );

    // 编号
    elements.push(ReceiptFactory.emptyLine());
    elements.push(
      ReceiptFactory.text(`No.${venueId || '--'}`, {
        align: TextAlignment.CENTER,
      })
    );

    return receipt;
  }
}

// 导出单例
export const orderDetailBuilder = new OrderDetailBuilder(); 