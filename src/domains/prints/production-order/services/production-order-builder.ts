import { Receipt } from '../../shared/models/receipt-model';
import { TextAlignment, FontSize } from '../../shared/models/receipt-model';
import { ReceiptFactory } from '../../shared/utils/receipt-factory';
import { ProductionOrderData } from '../models/production-order-data';
import { formatDateTime } from '@/utils/dateUtils';
import { formatYuan } from '@/utils/priceUtils';

/**
 * 出品单构建器
 * 负责将出品单数据转换为结构化的票据表示
 */
export class ProductionOrderBuilder {
  /**
   * 构建出品单票据
   * @param data 出品单数据实体
   */
  build(data: ProductionOrderData, venueId: string): Receipt {
    const receipt: Receipt = { elements: [] };
    const elements = receipt.elements;

    // 标题
    elements.push(ReceiptFactory.emptyLine());
    elements.push(
      ReceiptFactory.text('出品单', {
        align: TextAlignment.CENTER,
        bold: true,
        fontSize: FontSize.DOUBLE,
      })
    );
    elements.push(
      ReceiptFactory.text('\n', {
        fontSize: FontSize.NORMAL,
        bold: false,
      })
    );
    elements.push(ReceiptFactory.emptyLine());

    // 基本信息
    elements.push(
      ReceiptFactory.text(`包厢名称：${data.roomInfo?.name || '--'}`, {
        fontSize: FontSize.SMALL,
        align: TextAlignment.LEFT,
      })
    );
    elements.push(
      ReceiptFactory.text(`点单人：${data.employeeName || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`点单时间：${formatDateTime(data.orderTime)}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`出品单号：${data.productionOrderNo || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`订单号：${data.orderNo || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );
    elements.push(
      ReceiptFactory.text(`开台单号：${data.sessionId || '--'}`, {
        fontSize: FontSize.SMALL,
      })
    );

    elements.push(ReceiptFactory.line());

    // 商品信息标题
    elements.push(
      ReceiptFactory.text('商品信息', {
        align: TextAlignment.CENTER,
      })
    );

    // 商品信息表格头
    elements.push(
      ReceiptFactory.tableRow([
        { text: '名称', width: 40, align: TextAlignment.LEFT },
        { text: '口味', width: 20, align: TextAlignment.RIGHT },
        { text: '单价', width: 20, align: TextAlignment.RIGHT },
        { text: '数量', width: 20, align: TextAlignment.RIGHT },
      ])
    );

    // 商品数据行
    if (data.products?.length) {
      // 遍历所有商品
      data.products.forEach((item) => {
        // 主商品行
        elements.push(
          ReceiptFactory.tableRow([
            { text: item.productName, width: 40, align: TextAlignment.LEFT },
            { text: item.flavors || '--', width: 20, align: TextAlignment.RIGHT },
            { text: formatYuan(item.price), width: 20, align: TextAlignment.RIGHT },
            { text: `${item.quantity}${item.unit || '份'}`, width: 20, align: TextAlignment.RIGHT },
          ])
        );

        // 如果是套餐，显示子商品
        if (item.subProducts && item.subProducts.length > 0) {
          item.subProducts.forEach(subItem => {
            elements.push(
              ReceiptFactory.tableRow([
                { text: `    ${subItem.productName}`, width: 40, align: TextAlignment.LEFT },
                { text: '', width: 20, align: TextAlignment.RIGHT },
                { text: `${subItem.quantity}${subItem.unit || ''}`, width: 20, align: TextAlignment.RIGHT },
              ])
            );
          });
        }
      });
    }

    elements.push(ReceiptFactory.line());
    elements.push(ReceiptFactory.emptyLine());

    // 底部信息
    if (data.producerName) {
      elements.push(
        ReceiptFactory.text(`出品人：${data.producerName}`, {
          align: TextAlignment.LEFT,
          fontSize: FontSize.SMALL,
        })
      );
    }
    
    if (data.productionTime) {
      elements.push(
        ReceiptFactory.text(`出品时间：${formatDateTime(data.productionTime)}`, {
          align: TextAlignment.LEFT,
          fontSize: FontSize.SMALL,
        })
      );
    } else {
      elements.push(
        ReceiptFactory.text(`打单时间：${formatDateTime(new Date().toISOString())}`, {
          align: TextAlignment.LEFT,
          fontSize: FontSize.SMALL,
        })
      );
    }
    
    elements.push(
      ReceiptFactory.text(`No.${venueId}`, {
        align: TextAlignment.CENTER,
      })
    );

    return receipt;
  }
}

// 导出单例
export const productionOrderBuilder = new ProductionOrderBuilder();