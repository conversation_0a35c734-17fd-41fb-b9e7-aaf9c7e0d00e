/**
 * 打印服务共享基础数据模型
 * 定义多种票据共用的基础数据结构
 */

/**
 * 商品信息接口
 * 描述订单中商品的基本信息
 */
export interface ProductInfo {
  productName: string;
  flavors?: string; // 口味，可选
  price: number; // 单价
  payPrice: number; // 支付价/现价
  quantity: number;
  unit: string;
  totalAmount: number; // 小计
}

/**
 * 套餐子商品信息
 * 描述套餐内包含的子商品
 */
export interface PackageSubProductInfo {
  productName: string; // 商品名称
  quantity: number; // 数量
  unit: string; // 单位
}

/**
 * 商品信息接口（扩展）
 * 带有可选子商品列表
 */
export interface ExtendedProductInfo extends ProductInfo {
  subProducts?: PackageSubProductInfo[]; // 套餐子商品
}

/**
 * 房间信息接口
 * 描述订单关联的房间信息
 */
export interface RoomInfo {
  name?: string;
}

/**
 * 会话信息接口
 * 描述开台会话的详细信息
 */
export interface SessionInfo {
  sessionId?: string;
  duration?: number; // 单位：秒
  startTime?: number; // Unix timestamp
  endTime?: number; // Unix timestamp
  roomFee?: number;
  totalFee?: number;
  unpaidAmount?: number;
  prePayBalance?: number;
  employeeName?: string;
}

/**
 * 赠品信息
 * 描述赠送商品信息
 */
export interface GiftProductInfo {
  productName: string; // 商品名称
  quantity: number; // 数量
  unit: string; // 单位
  price: number; // 原价
  remark?: string; // 备注，例如"赠送"
  /** 套餐子商品 */
  subProducts?: PackageSubProductInfo[];
}

/**
 * 支付方式明细
 * 描述具体的支付方式和金额
 */
export interface PaymentDetail {
  method: string; // 支付方式名称
  amount: number; // 金额
}

/**
 * 房间方案信息基础接口
 * 描述房间消费方案
 */
export interface BaseRoomPackage {
  planName: string; // 方案名称
  duration: number; // 时长（分钟）
  originalPrice: number; // 原价
  actualPrice: number; // 现价
} 