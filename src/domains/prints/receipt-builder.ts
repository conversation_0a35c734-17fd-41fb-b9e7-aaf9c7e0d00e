import { Receipt } from './shared/models/receipt-model';
import { orderDetailBuilder } from './order-detail/services/order-detail-builder';
import { sessionOrderBuilder } from './session-order/services/session-order-builder';
import { productionOrderBuilder } from './production-order/services/production-order-builder';
import { shiftReportBuilder } from './shift-report/services/shift-report-builder';

// 重新导出所有子域数据类型，方便外部使用
import { OrderDetailData } from './order-detail/models/order-detail-data';
import { SessionOrderData } from './session-order/models/session-order-data';
import { ProductionOrderData } from './production-order/models/production-order-data';
import { ShiftReportData } from './shift-report/models/shift-report-data';

/**
 * 票据构建器门面类
 * 整合所有子域的票据构建器，提供统一的入口
 */
export class ReceiptBuilder {
  /**
   * 构建消费明细票据
   * @param data 订单详情数据实体
   */
  buildOrderDetailReceipt(data: OrderDetailData, venueId: string): Receipt {
    return orderDetailBuilder.build(data, venueId);
  }

  /**
   * 构建开台单票据
   * @param data 开台单数据实体
   */
  buildSessionOrderReceipt(data: SessionOrderData, venueId: string): Receipt {
    return sessionOrderBuilder.build(data, venueId);
  }

  /**
   * 构建续房单票据
   * @param data 续房单数据实体（与开台单数据结构相同）
   * @param venueId 场馆ID
   */
  buildRoomExtensionReceipt(data: SessionOrderData, venueId: string): Receipt {
    return sessionOrderBuilder.build(data, venueId, '续房单');
  }

  /**
   * 构建出品单票据
   * @param data 出品单数据实体
   */
  buildProductionOrderReceipt(data: ProductionOrderData, venueId: string): Receipt {
    return productionOrderBuilder.build(data, venueId);
  }

  /**
   * 构建交班单票据
   * @param data 交班单数据实体
   */
  buildShiftReportReceipt(data: ShiftReportData, venueId: string): Receipt {
    return shiftReportBuilder.build(data, venueId);
  }
}

// 导出单例
export const receiptBuilder = new ReceiptBuilder(); 