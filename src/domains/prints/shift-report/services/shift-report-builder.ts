import { Receipt } from '../../shared/models/receipt-model';
import { TextAlignment, FontSize } from '../../shared/models/receipt-model';
import { ReceiptFactory } from '../../shared/utils/receipt-factory';
import { ShiftReportData } from '../models/shift-report-data';
import { formatDateTime } from '@/utils/dateUtils';
import { formatYuan } from '@/utils/priceUtils';

/**
 * 交班单构建器
 * 负责将交班单数据转换为结构化的票据表示
 */
export class ShiftReportBuilder {
  /**
   * 构建交班单票据
   * @param data 交班单数据实体
   */
  build(data: ShiftReportData, venueId: string): Receipt {
    const receipt: Receipt = { elements: [] };
    const elements = receipt.elements;

    // 标题
    this.buildTitle(elements);
    
    // 基本信息
    this.buildBasicInfo(elements, data);
    
    // 营业数据
    this.buildBusinessData(elements, data);
    
    // 支付方式
    this.buildPaymentInfo(elements, data);
    
    // 异常支付和还原账单
    this.buildAbnormalData(elements, data);
    
    // 底部信息
    this.buildFooter(elements, venueId);

    return receipt;
  }

  /**
   * 构建标题
   */
  private buildTitle(elements: any[]) {
    elements.push(
      ReceiptFactory.text('交班单', {
        align: TextAlignment.CENTER,
        bold: true,
        fontSize: FontSize.DOUBLE,
      })
    );
    elements.push(ReceiptFactory.emptyLine());
  }

  /**
   * 构建基本信息
   */
  private buildBasicInfo(elements: any[], data: ShiftReportData) {
    const printTime = data.printTime || data.shiftTime;
    
    elements.push(
      ReceiptFactory.text(`打单时间: ${formatDateTime(printTime)}`, {
        align: TextAlignment.LEFT,
      })
    );
    elements.push(
      ReceiptFactory.text(`交班时间: ${formatDateTime(data.shiftTime)}`)
    );
    elements.push(
      ReceiptFactory.text(`交班员工: ${data.employee || '--'}`)
    );
    elements.push(
      ReceiptFactory.text(`交班金额: ${this.formatAmount(data.shiftAmount)}`)
    );
    elements.push(
      ReceiptFactory.text(`已结账单数: ${data.settledOrderCount ?? '--'}`)
    );
    elements.push(
      ReceiptFactory.text(`开台数: ${data.openTableCount ?? '--'}`)
    );
    elements.push(
      ReceiptFactory.text(`未结订单数: ${data.unsettledOrderCount ?? '--'}`)
    );
    
    elements.push(ReceiptFactory.emptyLine());
  }

  /**
   * 构建营业数据
   */
  private buildBusinessData(elements: any[], data: ShiftReportData) {
    const businessData = data.businessData;
    if (!businessData) return;

    const businessItems = [
      { label: '营业应收', value: businessData.receivable },
      { label: '营业实收', value: businessData.actualReceived },
      // { label: '营业净收', value: businessData.netReceived },
      { label: '商家优惠', value: businessData.merchantDiscount },
      // { label: '会员优惠', value: businessData.memberDiscount },
      { label: '包厢实收', value: businessData.roomActualReceived },
      { label: '商品实收', value: businessData.productActualReceived },
      { label: '办卡张数', value: businessData.cardCount, isCount: true },
      { label: '办卡金额', value: businessData.cardAmount },
      { label: '充值金额', value: businessData.rechargeAmount },
      { label: '充值赠送', value: businessData.rechargeBonusAmount },
      // { label: '补卡金额', value: businessData.cardReplacementAmount },
      // { label: '续期费', value: businessData.renewalFee },
      // { label: '冲账应收', value: businessData.chargeOffReceivable },
      // { label: '冲账实收', value: businessData.chargeOffActualReceived },
      // { label: '计费抵商品', value: businessData.billDeductionForProducts },
      // { label: '商品抵房费', value: businessData.productDeductionForRoom },
      // { label: '优惠券优惠', value: businessData.couponDiscount },
      // { label: '抹零金额', value: businessData.roundingAmount },
      { label: '员工商品赠送', value: businessData.employeeProductGift },
    ];

    businessItems.forEach(item => {
      if (item.value !== undefined && item.value !== null) {
        const displayValue = item.isCount ? 
          item.value.toString() : 
          this.formatAmount(item.value);
        elements.push(
          ReceiptFactory.text(`${item.label}: ${displayValue}`)
        );
      }
    });

    elements.push(ReceiptFactory.emptyLine());
  }

  /**
   * 构建支付方式
   */
  private buildPaymentInfo(elements: any[], data: ShiftReportData) {
    if (!data.paymentInfo?.length) return;

    // 支付信息标题
    elements.push(
      ReceiptFactory.text('支付信息', {
        align: TextAlignment.CENTER,
        bold: true,
      })
    );
    
    // 表头
    elements.push(
      ReceiptFactory.tableRow([
        { text: '支付类型', width: 24 },
        { text: '实收金额', width: 24, align: TextAlignment.RIGHT },
      ])
    );
    
    // 分隔线
    elements.push(
      ReceiptFactory.text('-'.repeat(48))
    );

    // 支付数据
    data.paymentInfo.forEach(payment => {
      elements.push(
        ReceiptFactory.tableRow([
          { text: payment.method || '--', width: 24 },
          { text: this.formatAmount(payment.amount), width: 24, align: TextAlignment.RIGHT },
        ])
      );
    });

    elements.push(ReceiptFactory.emptyLine());
  }

  /**
   * 构建异常数据
   */
  private buildAbnormalData(elements: any[], data: ShiftReportData) {
    if (data.abnormalPayment) {
      elements.push(
        ReceiptFactory.text(`异常支付流水金额: ${this.formatAmount(data.abnormalPayment.amount)}`)
      );
      elements.push(
        ReceiptFactory.text(`异常支付流水数: ${data.abnormalPayment.count ?? '--'}`)
      );
    }
    
    if (data.restoredOrderCount !== undefined && data.restoredOrderCount !== null) {
      elements.push(
        ReceiptFactory.text(`还原账单数: ${data.restoredOrderCount}`)
      );
    }
  }

  /**
   * 构建底部信息
   */
  private buildFooter(elements: any[], venueId: string) {
    elements.push(ReceiptFactory.emptyLine());
    elements.push(
      ReceiptFactory.text(`No.${venueId}`, {
        align: TextAlignment.CENTER,
      })
    );
  }

  /**
   * 格式化金额显示
   */
  private formatAmount(amount?: number): string {
    return amount !== undefined && amount !== null ? formatYuan(amount) : '--';
  }
}

// 导出单例
export const shiftReportBuilder = new ShiftReportBuilder(); 