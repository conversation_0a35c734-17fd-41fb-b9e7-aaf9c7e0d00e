import type { PaymentDetail } from '../../shared/models/base-models';

/**
 * 营业数据汇总信息
 * 描述交班单中的营业数据摘要
 */
export interface BusinessSummary {
  receivable?: number; // 营业应收
  actualReceived?: number; // 营业实收
  netReceived?: number; // 营业净收
  merchantDiscount?: number; // 商家优惠
  memberDiscount?: number; // 会员优惠
  roomActualReceived?: number; // 包厢实收
  productActualReceived?: number; // 商品实收
  cardCount?: number; // 办卡张数
  cardAmount?: number; // 办卡金额
  rechargeAmount?: number; // 充值金额
  rechargeBonusAmount?: number; // 充值赠送
  cardReplacementAmount?: number; // 补卡金额
  renewalFee?: number; // 续期费
  chargeOffReceivable?: number; // 冲账应收
  chargeOffActualReceived?: number; // 冲账实收
  billDeductionForProducts?: number; // 计费抵商品
  productDeductionForRoom?: number; // 商品抵房费
  couponDiscount?: number; // 优惠券优惠
  roundingAmount?: number; // 抹零金额
  employeeProductGift?: number; // 员工商品赠送
}

/**
 * 异常支付数据
 * 描述交班单中的异常支付信息
 */
export interface AbnormalPaymentData {
  amount?: number; // 异常支付流水金额
  count?: number; // 异常支付流水数
}

/**
 * 交班单数据实体
 * 用于生成员工交班单的数据模型
 */
export interface ShiftReportData {
  shiftTime: string; // 交班/打单时间 格式化后的时间 或 timestamp
  printTime?: string; // 打单时间（如果与交班时间不同）
  employee?: string; // 交班员工
  shiftAmount?: number; // 交班金额
  settledOrderCount?: number; // 已结账单数
  openTableCount?: number; // 开台数
  unsettledOrderCount?: number; // 未结订单数
  businessData?: BusinessSummary; // 营业数据汇总
  paymentInfo?: PaymentDetail[]; // 支付信息
  abnormalPayment?: AbnormalPaymentData; // 异常支付数据
  restoredOrderCount?: number; // 还原账单数
  shiftId?: string; // 交班单ID
} 