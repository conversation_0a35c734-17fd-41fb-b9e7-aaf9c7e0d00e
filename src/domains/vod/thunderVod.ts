import axios from 'axios'
import { IVod, VodResponse, IVodBridge, IVodBridgeCallback } from './vodInterface'
import CryptoJS from 'crypto-js' // 需要安装: npm install crypto-js
import { VodConfig } from './vodConfig'

interface RoomInfo {
  room_mac: string
  room_ip: string
  room_name: string
}

// 使用VodResponse作为标准响应类型
type ThunderVodResponse = VodResponse

export class ThunderVod implements IVod {
  private config: VodConfig
  private baseUrl: string
  private callbacks: Map<string, { resolve: (value: any) => void, reject: (reason: any) => void }> = new Map()
  private callbackIdCounter: number = 0

  constructor(config: VodConfig) {
    this.config = config
    this.baseUrl = `http://${config.host}:${config.port}`
    console.log('ThunderVod constructor, config:', config, 'baseUrl:', this.baseUrl)
    this.initCallbackHandler()
  }

  updateVodConfig(config: VodConfig) {
    this.config = config
    this.baseUrl = `http://${config.host}:${config.port}`
    console.log('ThunderVod updateVodConfig, config:', config, 'baseUrl:', this.baseUrl)
  }

  private initCallbackHandler() {
    console.log('initCallbackHandler')
    // 注册全局回调函数
    if (!window.vodBridgeCallback) {

      const vodBridgeCallback = (callbackId: string, responseText: string) => {
        console.log('接收到回调', callbackId, responseText)
        const callback = this.callbacks.get(callbackId)
        if (callback) {
          try {
            const response = JSON.parse(responseText)
            callback.resolve(response)
          } catch (error) {
            callback.reject(error)
          }
          // 完成后删除回调
          this.callbacks.delete(callbackId)
        }
      }
      window.vodBridgeCallback = vodBridgeCallback

      try {
        console.log("window.vodBridge", window.vodBridge)
        if (window.vodBridge && window.vodBridge.registerCallback) {
          window.vodBridge.registerCallback(vodBridgeCallback)
        }
      } catch (error) {
        console.error('registerCallback error:', error)
      }
    }
  }

  private generateSign(params: Record<string, any>): string {
    // 按ASCII升序排列参数
    const sortedKeys = Object.keys(params).sort()
    let signStr = ''

    // 构建签名字符串
    for (const key of sortedKeys) {
      if (params[key] !== undefined && params[key] !== null) {
        signStr += `${key}=${params[key]}&`
      }
    }

    // 添加appkey
    signStr = signStr.slice(0, -1) + this.config.appkey
    console.log('signStr', signStr)

    // MD5加密
    return CryptoJS.MD5(signStr).toString()
  }

  private async request<T>(
    method: 'get' | 'post',
    endpoint: string,
    params: Record<string, any>,
    withSign: boolean = true,
    body?: Record<string, any>
  ): Promise<T> {
    const stime = Math.floor(Date.now() / 1000)
    // 使用索引签名允许添加动态属性
    const requestParams: Record<string, any> = {
      ...params,
      appid: this.config.appid,
      stime
    }

    if (withSign) {
      const sign = this.generateSign(requestParams)
      requestParams.sign = sign
    }

    // 构建查询字符串 - 将所有值转为字符串
    const stringParams: Record<string, string> = {}
    for (const key in requestParams) {
      stringParams[key] = String(requestParams[key])
    }

    const queryString = new URLSearchParams(stringParams).toString()
    const url = this.baseUrl + endpoint

    return new Promise<T>((resolve, reject) => {
      try {
        // 生成回调ID
        const callbackId = `vod_cb_${Date.now()}_${this.callbackIdCounter++}`

        // 存储回调
        this.callbacks.set(callbackId, { resolve, reject })

        // 调用原生接口
        console.log('发送请求', method, url, queryString, body ? JSON.stringify(body) : '', callbackId)
        window.vodBridge.request(
          method,
          url,
          queryString,
          body ? JSON.stringify(body) : '',
          callbackId
        )
      } catch (error) {
        console.error(`VOD API Error (${endpoint}):`, error)
        reject(error)
      }
    })
  }

  // 实现getKtvType方法
  async getKtvType(): Promise<string> {
    try {
      const response = await this.getKtvInfo()
      // 使用类型断言来访问ktv_type属性
      const result = response.result as { ktv_type?: string } | undefined
      return result?.ktv_type || 'unknown'
    } catch (error) {
      console.error('获取KTV类型失败:', error)
      return 'unknown'
    }
  }

  // 实现接口方法
  async checkNewErp(): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('get', '/erp/newerp', {})
  }

  async getKtvInfo(): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('get', '/erp/ktvinfo', {})
  }

  async getRoomState(roomIp: string): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('get', '/erp/roomstate', {
      room_ip: roomIp
    })
  }

  async openRoom(roomIp: string): Promise<ThunderVodResponse> {
    console.log('openRoom', roomIp)
    return this.request<ThunderVodResponse>('post', '/erp/openroom', {
      room_ip: roomIp
    })
  }

  async closeRoom(roomIp: string): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('post', '/erp/closeroom', {
      room_ip: roomIp
    })
  }

  async switchRoom(srcIp: string, desIp: string): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('post', '/erp/switchroom', {
      src_ip: srcIp,
      des_ip: desIp
    })
  }

  /**
   * 到时提醒/超时提醒
   * @param roomIp 包房IP
   * @param type 超时类型 1:到时提醒 5:已经超时
   * @param time 还差多长时间（分钟）到时（"0":已到时）
   * @returns 
   */
  async timeout(roomIp: string, type: 1 | 5, time: string): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('post', '/erp/timeout', {
      room_ip: roomIp,
      type,
      time
    })
  }

  async sendCaption(message: string, roomIps: string[]): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>(
      'post',
      '/erp/caption',
      {},
      true,
      {
        message,
        room_ip: roomIps
      }
    )
  }

  async cancelCall(roomIp: string): Promise<ThunderVodResponse> {
    return this.request<ThunderVodResponse>('post', '/erp/cancelcall', {
      room_ip: roomIp
    })
  }

  async getRoomList(): Promise<ThunderVodResponse> {
    console.log('thunder VOD getRoomList')
    return this.request<ThunderVodResponse>('get', '/room/roomlist', {}, false)
  }
}