<script setup lang="ts">
import { RouterView, useRoute, useRouter } from 'vue-router';
import { onMounted, onUnmounted, watch, nextTick, ref, computed } from 'vue';
import NatsService from '@/services/nats-service';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore, DeviceInfo } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import NatsListenerService from '@/application/natsListenerService';
import DialogContainer from '@/components/Dialog/core/DialogContainer.vue';
// @ts-ignore - 忽略NProgress的类型错误
import NProgress from 'nprogress';
import { useEscapeKey } from '@/composables/useEscapeKey';
import { useVersionCheck } from '@/composables/useVersionCheck';
// import { ElNotification } from 'element-plus'; // 暂时移除 ElNotification
import dayjs from 'dayjs';
// 引入开发工具栏组件
import StagewiseDevToolbar from '@/components/dev/StagewiseToolbar.vue';

const route = useRoute();
const router = useRouter();
const nats = NatsService.getInstance();
const userStore = useUserStore();
const deviceStore = useDeviceStore() as any;
const venueStore = useVenueStore();

// 使用ESC键钩子函数
const { exitDialogVisible } = useEscapeKey();

let deviceInfo: DeviceInfo | null = null;

// 使用版本检测 composable，获取更多版本信息
const { isUpdateDetected, currentVersion, currentReleaseTimestamp, latestVersion, latestReleaseTimestamp } = useVersionCheck({
  onUpdateDetected: (currentVersion, newVersion, currentTimestamp, newTimestamp) => {
    // 只在控制台打印，不显示通知
    console.log(`[App] 检测到新版本！当前版本: ${currentVersion} (${currentTimestamp || 'N/A'}), 最新版本: ${newVersion} (${newTimestamp || 'N/A'})`);
    // 暂时注释掉 ElNotification
    /*
    ElNotification({
      title: '发现新版本',
      message: `发现新版本，建议刷新页面以确保功能正常。点击可立即刷新。`,
      type: 'warning',
      duration: 0,
      showClose: true,
      customClass: 'version-update-notification',
      onClick: () => {
        window.location.reload();
      },
      onClose: () => {
        console.log('用户关闭了版本更新提示');
      }
    });
    */
  },
  checkInterval: 15 * 60 * 1000,
  checkInDev: false
});

// 格式化时间戳
const formattedCurrentTimestamp = computed(() => {
  if (currentReleaseTimestamp.value && currentReleaseTimestamp.value !== '') {
    try {
      return dayjs(currentReleaseTimestamp.value).format('YYYY-MM-DD HH:mm:ss');
    } catch (e) {
      return currentReleaseTimestamp.value;
    }
  }
  return 'N/A';
});

// 检查登录和授权状态
const checkAuthStatus = async () => {
  const token = userStore.token;
  const venueId = venueStore.venueId;
  console.log('checkAuthStatus: ', venueId, venueStore.venue, deviceStore.cashierMachine?.grantIp, deviceStore.ipAddress);
  // 如果没有venues，跳转到授权页
  if (!venueId || !venueStore.venue || deviceStore.cashierMachine?.grantIp !== deviceStore.ipAddress) {
    console.log('No venues found, redirecting to auth page');
    await router.push('/auth');
    return false;
  }

  // 如果没有token，跳转到登录页
  if (!token) {
    console.log('No token found, redirecting to login page');
    await router.push('/login');
    return false;
  }

  return true;
};

// 应用初始化
const initApp = async () => {
  console.log('Starting app initialization...');

  // 启动进度条
  NProgress.start();

  // 初始化设备信息
  console.log('Device info initialized:', deviceInfo);

  if (deviceInfo && deviceInfo.deviceId && deviceInfo.macAddress && deviceInfo.ipAddress) {
    console.log('Valid device info, continuing initialization...');
    try {
      // 初始化用户状态
      await userStore.initUserState();
      console.log('User state initialized');

      // 检查认证状态
      const authStatus = await checkAuthStatus();
      console.log('Auth status checked:', authStatus);
    } catch (error) {
      console.error('Error during app initialization:', error);
    } finally {
      // 完成进度条
      NProgress.done();
    }
  } else {
    console.log('Invalid device info, skipping initialization');
    // 完成进度条
    NProgress.done();
  }
};

// 处理URL参数
const handleUrlParams = async () => {
  // 尝试从 URL 直接解析参数
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.has('deviceId') && urlParams.has('macAddress') && urlParams.has('ipAddress')) {
    deviceInfo = {
      deviceId: urlParams.get('deviceId') as string,
      macAddress: urlParams.get('macAddress') as string,
      ipAddress: urlParams.get('ipAddress') as string,
      location: urlParams.get('location') as string,
      deviceType: urlParams.get('deviceType') as string,
      appVersion: urlParams.get('appVersion') as string,
      appVersionCode: urlParams.get('appVersionCode') as string
    };
    console.log('Found params in URL, manually initializing..., deviceInfo:', deviceInfo, urlParams.get('appVersionCode'));
    deviceStore.initDeviceInfo(deviceInfo);
    await initApp();
    return true;
  }
  return false;
};

// 使用 watch 监听路由参数变化
watch(
  () => route.query,
  async (newQuery, oldQuery) => {
    // 使用 nextTick 等待 DOM 更新
    await nextTick();

    console.log('Route query changed:', {
      newQuery,
      oldQuery,
      hasKeys: Object.keys(newQuery).length > 0,
      fullPath: route.fullPath // 添加完整路径日志
    });

    // 确保查询参数非空且包含必要的参数
    if (newQuery && typeof newQuery === 'object' && 'deviceId' in newQuery && 'macAddress' in newQuery && 'ipAddress' in newQuery) {
      console.log('Valid query params detected, initializing app...');
      deviceInfo = {
        deviceId: newQuery.deviceId as string,
        macAddress: newQuery.macAddress as string,
        ipAddress: newQuery.ipAddress as string,
        location: newQuery.location as string,
        deviceType: newQuery.deviceType as string,
        appVersion: newQuery.appVersion as string
      };
      // @ts-ignore
      deviceStore.initDeviceInfo(deviceInfo);
      await initApp();
    } else {
      console.log('Waiting for valid query params...', {
        hasQuery: !!newQuery,
        queryType: typeof newQuery,
        params: newQuery
      });
    }
  },
  {
    immediate: true,
    deep: true
  }
);

// 退出系统的处理逻辑
const handleSystemExit = () => {
  console.log('正在执行退出系统操作...');
  // 这里可以添加需要执行的清理操作

  // 如果在Android WebView环境中，则调用退出方法
  if (window.webViewBridge && typeof window.webViewBridge.exitApp === 'function') {
    window.webViewBridge.exitApp();
  }
};

onMounted(async () => {
  // 添加版本和发布时间信息的日志输出
  console.log(`[App Info] 当前版本: ${currentVersion.value}, 发布时间: ${formattedCurrentTimestamp.value}`);

  // 如果检测到新版本，也在控制台输出
  if (isUpdateDetected.value && latestVersion.value) {
    const formattedLatestTimestamp = latestReleaseTimestamp.value ? dayjs(latestReleaseTimestamp.value).format('YYYY-MM-DD HH:mm:ss') : 'N/A';
    console.log(`[App Info] 发现新版本: ${latestVersion.value}, 发布时间: ${formattedLatestTimestamp}`);
  }

  // 启动进度条
  NProgress.start();

  console.log('App component mounted, initial route:', {
    fullPath: route.fullPath,
    query: route.query,
    params: route.params
  });

  // 等待下一个 tick
  await nextTick();

  if (route.query && Object.keys(route.query).length > 0) {
    console.log('Query params available on mount, initializing app...');
    await initApp();
  } else {
    // 尝试从 URL 直接解析参数
    await handleUrlParams();
  }

  // 调用应用层的 NATS 监听服务
  NatsListenerService.startListening();

  // 将userStore暴露到window对象，以便Android WebView可以调用clearToken方法
  // @ts-ignore
  window.userStore = userStore;

  // 完成进度条
  NProgress.done();
});

onUnmounted(() => {
  console.log('Cleaning up NATS service...');
  nats.cleanup();
  // @ts-ignore
  deviceStore.clearDeviceInfo();
  // 移除window对象上的userStore引用
  // @ts-ignore
  window.userStore = undefined;
});

// 定义webViewBridge接口类型以支持TypeScript类型检查
declare global {
  interface Window {
    webViewBridge?: {
      reloadWebView: () => void;
      exitApp: () => void;
    };
    userStore?: any;
  }
}
</script>

<template>
  <div class="app-layout">
    <!-- 当视口宽度小于最小宽度时，这个容器会保持固定宽度 -->
    <div class="min-width-container">
      <RouterView />
    </div>

    <!-- 全局对话框容器 -->
    <DialogContainer />

    <!-- 开发工具栏组件 -->
    <StagewiseDevToolbar />
  </div>
</template>

<style>
/* 全局样式 */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
}

#app {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-layout {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 当视口宽度小于最小宽度时，添加水平滚动条 */
@media screen and (max-width: var(--app-min-width)) {
  .app-layout {
    overflow-x: auto;
  }
}

.min-width-container {
  min-width: var(--app-min-width);
  width: 100%;
  margin: 0 auto;
}

@media screen and (max-width: var(--app-min-width)) {
  .min-width-container {
    width: var(--app-min-width);
  }
}

/* 暂时保留样式，以备将来重新启用 */
.version-update-notification {
  cursor: pointer;
}
</style>
