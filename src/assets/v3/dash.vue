<template>
  <svg
    class="icon"
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M14 9C14 7.89543 14.8954 7 16 7H20C21.1046 7 22 7.89543 22 9H14Z" :fill="(fillColor || color) ? effectiveFillColor : '#999999'"/>
<rect x="9" y="9" width="18" height="3" :fill="(fillColor || color) ? effectiveFillColor : '#999999'"/>
<path d="M25.5342 28.5H10.4658L9.62207 15H12.6279L13.2842 25.5H22.7158L23.3721 15H26.3779L25.5342 28.5Z" :fill="(fillColor || color) ? effectiveFillColor : '#999999'"/>
  </svg>
</template>

<script>
export default {
  name: 'dash',
  props: {
    color: {
      type: String,
      default: ''
    },
    fillColor: {
      type: String,
      default: ''
    },
    strokeColor: {
      type: String,
      default: ''
    },
    weight: {
      type: [Number, String],
      default: 1
    }
  },
  computed: {
    effectiveFillColor() {
      return this.fillColor || this.color || '';
    },
    effectiveStrokeColor() {
      return this.strokeColor || this.color || '';
    }
  }
}
</script>

<style scoped>
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
</style>
