<template>
  <svg
    class="icon"
    viewBox="0 0 56 56"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_18526_159320)">
<path d="M6.22217 7.77777L10.5889 8.80681C12.6961 9.3034 14.1851 11.1841 14.1851 13.3491V34.2222C14.1851 35.9404 15.578 37.3333 17.2962 37.3333H40.4444C42.1626 37.3333 43.5555 35.9404 43.5555 34.2222V17.457C43.5555 15.7388 42.1626 14.3459 40.4444 14.3459H24" :stroke="(strokeColor || color) ? effectiveStrokeColor : 'black'" :stroke-width="weight" stroke-linecap="square"/>
<circle cx="17.5" cy="46.5" r="3.5" :fill="(fillColor || color) ? effectiveFillColor : 'black'"/>
<circle cx="38.5" cy="46.5" r="3.5" :fill="(fillColor || color) ? effectiveFillColor : 'black'"/>
</g>
<defs>
<clipPath id="clip0_18526_159320">
<rect width="56" height="56" :fill="(fillColor || color) ? effectiveFillColor : 'white'"/>
</clipPath>
</defs>
  </svg>
</template>

<script>
export default {
  name: 'cart',
  props: {
    color: {
      type: String,
      default: ''
    },
    fillColor: {
      type: String,
      default: ''
    },
    strokeColor: {
      type: String,
      default: ''
    },
    weight: {
      type: [Number, String],
      default: 4
    }
  },
  computed: {
    effectiveFillColor() {
      return this.fillColor || this.color || '';
    },
    effectiveStrokeColor() {
      return this.strokeColor || this.color || '';
    }
  }
}
</script>

<style scoped>
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
</style>
