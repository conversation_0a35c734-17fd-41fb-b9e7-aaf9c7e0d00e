<template>
  <svg
    class="icon"
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M50.9629 13.0371L13.037 50.963" :stroke="(strokeColor || color) ? effectiveStrokeColor : 'black'" :stroke-width="weight" stroke-linecap="round"/>
<path d="M50.9629 50.9629L13.037 13.037" :stroke="(strokeColor || color) ? effectiveStrokeColor : 'black'" :stroke-width="weight" stroke-linecap="round"/>
  </svg>
</template>

<script>
export default {
  name: 'close',
  props: {
    color: {
      type: String,
      default: ''
    },
    fillColor: {
      type: String,
      default: ''
    },
    strokeColor: {
      type: String,
      default: ''
    },
    weight: {
      type: [Number, String],
      default: 2
    }
  },
  computed: {
    effectiveFillColor() {
      return this.fillColor || this.color || '';
    },
    effectiveStrokeColor() {
      return this.strokeColor || this.color || '';
    }
  }
}
</script>

<style scoped>
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
</style>
