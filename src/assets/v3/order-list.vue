<template>
  <svg
    class="icon"
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="23" y="23" width="18" height="4" :fill="(fillColor || color) ? effectiveFillColor : 'black'"/>
<rect x="23" y="37" width="12" height="4" :fill="(fillColor || color) ? effectiveFillColor : 'black'"/>
<path d="M42.3135 10.9995C43.2759 10.9995 44.1184 10.9976 44.8115 11.0542C45.528 11.1128 46.2597 11.2437 46.9688 11.605L47.1621 11.7085C48.1187 12.2445 48.8971 13.0529 49.3965 14.0327L49.5215 14.3003C49.7891 14.9249 49.896 15.5633 49.9473 16.1899C50.0039 16.883 50.002 17.7257 50.002 18.688V45.311C50.002 46.2733 50.0039 47.1161 49.9473 47.8091C49.8961 48.4357 49.7891 49.0741 49.5215 49.6987L49.3965 49.9663C48.8972 50.9463 48.1187 51.7545 47.1621 52.2905L46.9688 52.3941C46.2596 52.7554 45.528 52.8863 44.8115 52.9448C44.1184 53.0015 43.276 52.9995 42.3135 52.9995H21.6904C20.728 52.9995 19.8855 53.0015 19.1924 52.9448C18.5656 52.8936 17.9274 52.7867 17.3027 52.5191L17.0352 52.3941C16.0553 51.8947 15.247 51.1163 14.7109 50.1597L14.6074 49.9663C14.2463 49.2573 14.1152 48.5255 14.0566 47.8091C14.0001 47.1161 14.002 46.2733 14.002 45.311V18.688C14.002 17.7257 14 16.883 14.0566 16.1899C14.1152 15.4736 14.2462 14.7417 14.6074 14.0327C15.1401 12.9876 15.99 12.1375 17.0352 11.605C17.7442 11.2437 18.476 11.1128 19.1924 11.0542C19.8855 10.9976 20.728 10.9995 21.6904 10.9995H42.3135ZM21.6904 14.9995C20.6621 14.9995 20.011 15.0013 19.5186 15.0415C19.0501 15.0798 18.9031 15.1432 18.8516 15.1694C18.559 15.3185 18.321 15.5566 18.1719 15.8491C18.1457 15.9005 18.0823 16.0475 18.044 16.5161C18.0037 17.0085 18.002 17.6599 18.002 18.688V45.311C18.002 46.339 18.0038 46.9905 18.044 47.4829C18.0822 47.9508 18.1456 48.0982 18.1719 48.1499L18.2315 48.2573C18.3809 48.5009 18.5955 48.6991 18.8516 48.8296L18.9111 48.856C18.996 48.8866 19.1667 48.9288 19.5186 48.9575C20.011 48.9978 20.6621 48.9995 21.6904 48.9995H42.3135C43.3418 48.9995 43.9929 48.9978 44.4854 48.9575C44.9544 48.9192 45.101 48.8558 45.1524 48.8296L45.2598 48.77C45.5034 48.6206 45.7016 48.406 45.832 48.1499L45.8584 48.0903C45.889 48.0053 45.9313 47.8342 45.96 47.4829C46.0002 46.9905 46.002 46.339 46.002 45.311V18.688C46.002 17.6599 46.0002 17.0085 45.96 16.5161C45.9312 16.1646 45.889 15.9936 45.8584 15.9087L45.832 15.8491C45.7015 15.5932 45.5033 15.3784 45.2598 15.229L45.1524 15.1694C45.1008 15.1432 44.9538 15.0798 44.4854 15.0415C43.9929 15.0013 43.3418 14.9995 42.3135 14.9995H21.6904Z" :fill="(fillColor || color) ? effectiveFillColor : 'black'"/>
  </svg>
</template>

<script>
export default {
  name: 'order-list',
  props: {
    color: {
      type: String,
      default: ''
    },
    fillColor: {
      type: String,
      default: ''
    },
    strokeColor: {
      type: String,
      default: ''
    },
    weight: {
      type: [Number, String],
      default: 1
    }
  },
  computed: {
    effectiveFillColor() {
      return this.fillColor || this.color || '';
    },
    effectiveStrokeColor() {
      return this.strokeColor || this.color || '';
    }
  }
}
</script>

<style scoped>
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
</style>
