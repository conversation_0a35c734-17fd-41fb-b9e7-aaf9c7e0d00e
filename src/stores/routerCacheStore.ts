// composables/useCache.ts
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import type { RouteRecordNormalized } from 'vue-router';

export function useCache() {
  const route = useRoute();
  const cachedViews = ref(new Set<string>());

  const getComponentName = (record: RouteRecordNormalized): string | undefined => {
    // 优先使用路由 meta 中定义的 componentName
    const metaComponentName = record.meta?.componentName as string;
    if (metaComponentName) {
      // console.log('keepalive Found meta componentName:', {
      //   path: record.path,
      //   componentName: metaComponentName
      // })
      return metaComponentName;
    }

    // 如果没有 meta.componentName，则使用路由名称
    const routeName = record.name?.toString();
    if (routeName) {
      // console.log('keepalive Using route name as fallback:', {
      //   path: record.path,
      //   routeName
      // })
      return routeName;
    }

    // console.log('keepalive No component name found for path:', record.path)
    return undefined;
  };

  const updateCache = () => {
    // console.log('keepalive Updating cache for route:', route.path)

    // 获取当前匹配的路由记录
    const matchedRoutes = route.matched;
    // console.log('keepalive Matched routes:', matchedRoutes.map(r => r.path))

    // 从最深层的路由开始处理
    for (let i = matchedRoutes.length - 1; i >= 0; i--) {
      const record = matchedRoutes[i];
      const componentName = getComponentName(record);

      if (record.meta?.keepAlive && componentName) {
        cachedViews.value.add(componentName);
        console.log('keepalive Added to cache:', {
          path: record.path,
          componentName,
          meta: record.meta
        });
      } else {
        // console.log('keepalive Skipped caching:', {
        //   path: record.path,
        //   hasKeepAlive: record.meta?.keepAlive,
        //   componentName
        // })
      }
    }

    // console.log('keepalive Current cached views:', Array.from(cachedViews.value))
  };

  // 转换为数组供keep-alive使用
  const cachedViewsList = computed(() => {
    const views = Array.from(cachedViews.value);
    // console.log('keepalive Computed cached views:', views)
    return views;
  });

  // 手动控制缓存
  const addCache = (name: string) => {
    if (name) {
      cachedViews.value.add(name);
      // console.log('keepalive Manually added to cache:', name)
      // console.log('keepalive Current cached views:', Array.from(cachedViews.value))
    }
  };

  const removeCache = (name: string) => {
    if (name) {
      cachedViews.value.delete(name);
      // console.log('keepalive Removed from cache:', name)
      // console.log('keepalive Current cached views:', Array.from(cachedViews.value))
    }
  };

  return {
    cachedViews: cachedViewsList,
    updateCache,
    addCache,
    removeCache
  };
}
