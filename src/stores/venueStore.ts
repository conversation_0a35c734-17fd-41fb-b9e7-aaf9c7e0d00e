import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { VenueVO } from '@/api/autoGenerated/shared/types/venue';
import type { VodSettingsVO } from '@/api/autoGenerated/shared/types/other';

export const useVenueStore = defineStore(
  'venue',
  () => {
    // 状态
    const venueId = ref('');
    const venue = ref<VenueVO | null>(null);
    const vodSettings = ref<VodSettingsVO | null>(null);
    const businessHours = ref({
      openTime: '',
      closeTime: ''
    });

    // 方法
    function setVenueId(newVenueId: string) {
      venueId.value = newVenueId || '';
      console.log('setVenueId', venueId.value);
    }

    function setVenue(venueInfo: VenueVO | null) {
      venue.value = venueInfo;
      console.log('setVenue', venue.value);
      setBusinessHours(venue.value?.startHours || '', venue.value?.endHours || '');
    }

    function setVodSettings(settings: VodSettingsVO | null) {
      vodSettings.value = settings;
      console.log('setVodSettings', vodSettings.value);
    }

    function setBusinessHours(open: string, close: string) {
      businessHours.value = {
        openTime: open,
        closeTime: close
      };
      console.log('businessHours', businessHours.value);
    }

    function clearVenueInfo() {
      venueId.value = '';
      venue.value = null;
      vodSettings.value = null;
      businessHours.value = {
        openTime: '',
        closeTime: ''
      };
    }

    return {
      // 状态
      venueId,
      venue,
      vodSettings,
      businessHours,
      // 方法
      setVenueId,
      setVenue,
      setVodSettings,
      setBusinessHours,
      clearVenueInfo
    };
  },
  {
    persist: {
      storage: localStorage,
      paths: ['venueId', 'venue', 'vodSettings', 'businessHours']
    }
  }
);
