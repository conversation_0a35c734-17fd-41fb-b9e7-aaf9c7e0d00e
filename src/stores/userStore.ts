import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getToken, setToken, removeToken, getUserInfoStorage, setUserInfoStorage, removeUserInfoStorage } from '@/utils/authUtils';
import { login, logout } from '@/api/user';
import { EmployeeVO } from '@/types/projectobj';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
// 导入RUM用户信息设置函数
import { setRumUser } from '@/utils/rumConfig';

export interface UserInfo {
  name: string;
  phone: string;
  unionId: string;
  employee: EmployeeVO;
  avatar: string;
}

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken() || '');
  const userInfo = ref<UserInfo>({
    name: '',
    phone: '',
    unionId: '',
    avatar: '',
    employee: {} as Employee<PERSON>
  });

  // 设置 RUM 用户信息的帮助函数
  const updateRumUserInfo = () => {
    if (userInfo.value?.employee?.id && userInfo.value?.name) {
      setRumUser(userInfo.value.name, {
        tags: `userId=${userInfo.value.employee.id}`,
        userRole: userInfo.value.employee?.employeeGroup || 'defaultRole'
      });
    }
  };

  // 初始化用户状态
  const initUserState = () => {
    // 从本地存储恢复token和用户信息
    const savedToken = getToken();
    const savedUserInfo = getUserInfoStorage();

    if (savedToken) {
      token.value = savedToken;
    }
    if (savedUserInfo) {
      userInfo.value = savedUserInfo;

      // 设置RUM用户信息
      updateRumUserInfo();
    }
  };

  // 初始化调用
  initUserState();

  // 设置Token
  const setUserToken = (newToken: string) => {
    token.value = newToken;
    setToken(newToken);
  };

  // 清除Token方法 - 由Android WebView调用
  const clearToken = () => {
    token.value = '';
    removeToken();
    console.log('Token已被Android WebView清除');
  };

  // 更新设置用户信息方法
  const setUserInfo = (info: Partial<UserInfo>) => {
    userInfo.value = { ...userInfo.value, ...info };
    setUserInfoStorage(userInfo.value);

    // 设置RUM用户信息
    updateRumUserInfo();
  };

  const setEmployee = (newEmployee: Partial<EmployeeVO>) => {
    userInfo.value.employee = { ...userInfo.value.employee, ...newEmployee };

    // 更新RUM用户信息
    updateRumUserInfo();
  };

  // 登录方法
  const loginAction = async (loginData: any) => {
    try {
      const { data } = await login(loginData);
      setUserToken(data.token);
      return data;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  };

  // 登出方法
  const logoutAction = async () => {
    try {
      // await logout()
      resetUserState();

      // 清空venueStore中的数据
      const venueStore = useVenueStore();
      venueStore.clearVenueInfo();
    } catch (error) {
      console.error('登出失败:', error);
      throw error;
    }
  };

  // 更新重置用户状态方法
  const resetUserState = () => {
    token.value = '';
    userInfo.value = {
      name: '',
      phone: '',
      unionId: '',
      avatar: '',
      employee: {} as EmployeeVO
    };
    removeToken();
    removeUserInfoStorage();
  };

  // 检查登录状态
  const isLoggedIn = () => {
    return !!token.value;
  };

  return {
    initUserState,
    token,
    userInfo,
    setUserToken,
    clearToken, // 暴露新的clearToken方法
    setUserInfo,
    setEmployee,
    loginAction,
    logoutAction,
    resetUserState,
    isLoggedIn
  };
});
