import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { SessionVO } from '@/api/autoGenerated';
import { ExtendedStageVO, UnionInfo } from '@/modules/room/types/extendedStageVO';
import { ROOM_STATUS, SESSION_PAY_STATUS, SESSION_TAGS } from '@/modules/room/constants/stageStatus';
import { now10 } from '@/utils/dateUtils';
// 导入API服务和转换器
import { RoomApi } from '@/modules/room/api/room';
import { RealTimeTableConverter } from '@/modules/room/views/RealTimeTable/converter';
// 用于节流刷新的时间间隔（毫秒）
const REFRESH_THROTTLE_MS = 1500;
import { useVodService } from '@/application/vodService';

// 定义时序操作项接口
interface ScheduleItem {
  sessionId: string;
  endTime: number; // 截止时间（秒）
  operation: 'timeout';
  executeTime: number; // 执行时间（毫秒）
}

// Stage时序管理器
class StageScheduleManager {
  // 存储会话的定时器，key是sessionId
  private sessionTimers: Map<string, number[]> = new Map();
  // 记录会话的截止时间，key是sessionId
  private sessionEndTimes: Map<string, number> = new Map();

  // 保存引用以便访问stageStore的方法
  private getStageBySessionId: (sessionId: string) => ExtendedStageVO | undefined;
  private updateStageCombinedStatus: () => void;

  constructor(getStageBySessionId: (sessionId: string) => ExtendedStageVO | undefined, updateStageCombinedStatus: () => void) {
    this.getStageBySessionId = getStageBySessionId;
    this.updateStageCombinedStatus = updateStageCombinedStatus;
  }

  // 更新会话截止时间
  updateSessionEndTime(sessionId: string, endTime: number): void {
    const currentEndTime = this.sessionEndTimes.get(sessionId);
    // console.log('[StageScheduleManager] updateSessionEndTime', sessionId, endTime, currentEndTime);
    // 如果截止时间变化，清空定时器并重新计划
    if (currentEndTime !== endTime) {
      this.sessionEndTimes.set(sessionId, endTime);
      this.clearSessionTimers(sessionId);
      this.scheduleTasks(sessionId, endTime);
    }
  }

  // 清空会话的所有定时器
  clearSessionTimers(sessionId: string): void {
    const timers = this.sessionTimers.get(sessionId) || [];
    timers.forEach(timer => window.clearTimeout(timer));
    this.sessionTimers.set(sessionId, []);
  }

  // 清空所有定时器
  clearAllTimers(): void {
    for (const sessionId of this.sessionTimers.keys()) {
      this.clearSessionTimers(sessionId);
    }
    this.sessionTimers.clear();
    this.sessionEndTimes.clear();
  }

  // 安排会话的超时任务
  scheduleTasks(sessionId: string, endTimeSeconds: number): void {
    // 转换为毫秒
    const endTimeMs = endTimeSeconds * 1000;
    const now = Date.now();
    const timeRemaining = endTimeMs - now + 30 * 1000;

    if (timeRemaining <= 0) return;

    const timers: number[] = [];

    // 设置超时标记
    const timer = window.setTimeout(() => {
      this.updateStageCombinedStatus();
    }, timeRemaining);

    timers.push(timer);

    // 保存该会话的定时器
    const existingTimers = this.sessionTimers.get(sessionId) || [];
    this.sessionTimers.set(sessionId, [...existingTimers, ...timers]);
    // console.log('[StageScheduleManager] scheduleTasks', sessionId, timers, endTimeSeconds);
  }

  // 检查并同步所有阶段的定时器
  syncAllStages(stages: ExtendedStageVO[]): void {
    // console.log('[StageScheduleManager] 同步所有包厢定时器');

    // 记录当前有效的sessionId
    const activeSessionIds = new Set<string>();
    const vodService = useVodService();
    // 遍历所有stage，检查和更新定时器
    stages.forEach(stage => {
      const roomId = stage.roomVO.id;
      if (stage.sessionVO?.sessionId && stage.sessionVO?.endTime) {
        const sessionId = stage.sessionVO.sessionId;
        // console.log('[StageScheduleManager] 同步所有包厢定时器', stage, stage.roomVO.name, stage.sessionVO.endTime);
        //计时的包厢，不计入计时器
        if (!stage.tags?.includes(SESSION_TAGS.ISTIMECONSUME) && stage.sessionVO.endTime) {
          const endTime = Number(stage.sessionVO.endTime);
          vodService.updateEndTime(roomId, endTime); //调用VOD服务更新截止时间
          // 记录这个sessionId是活跃的
          activeSessionIds.add(sessionId);

          // 如果已过期，标记为超时
          const now = now10();
          if (endTime < now) {
            if (!stage.tags?.includes(SESSION_TAGS.TIMEOUT)) {
              stage.tags = stage.tags || [];
              stage.tags.push(SESSION_TAGS.TIMEOUT);
            }
          } else {
            // 未过期，更新/创建定时器
            this.updateSessionEndTime(sessionId, endTime);
          }
        }
      } else {
        if (stage.roomStatus === ROOM_STATUS.CLEANING) {
          vodService.clearRoomTimers(roomId);
        }
      }
    });

    // 清理无效的定时器（对应的stage已不存在）
    for (const sessionId of this.sessionTimers.keys()) {
      if (!activeSessionIds.has(sessionId)) {
        // console.log(`[StageScheduleManager] 清理无效定时器 - 会话ID: ${sessionId}`);
        this.clearSessionTimers(sessionId);
        this.sessionEndTimes.delete(sessionId);
      }
    }
  }
}

export const useStageStore = defineStore('stage', () => {
  const stages = ref<ExtendedStageVO[]>([]);
  const currentStage = ref<ExtendedStageVO | null>(null);
  const stageSource = ref<'realtime' | 'internal' | null>(null);
  // 缓存联台信息的Map
  const unionInfoMap = ref<Map<string, UnionInfo[]>>(new Map());
  // 添加最后刷新时间和是否正在刷新的状态
  const lastRefreshTime = ref<number>(0);
  const isRefreshing = ref<boolean>(false);
  // 创建时序管理器
  let scheduleManager: StageScheduleManager;

  const getCurrentStage = computed(() => currentStage.value);

  function setCurrentStage(stage: ExtendedStageVO | null) {
    currentStage.value = stage;
  }

  function updateStageCombinedStatus() {
    stages.value = stages.value.map(stage => {
      const tags = getStageTags(stage);
      const unionInfos = tags.includes(SESSION_TAGS.UNION) ? getUnionInfo(stage) : [];

      // 始终返回新对象，确保响应式系统捕获变化
      return {
        ...stage,
        roomStatus: stage.roomVO?.status || '',
        payStatus: stage.sessionVO?.payStatus || '',
        tags: [...tags], // 创建新数组
        unionInfos: [...unionInfos] // 创建新数组
      } as ExtendedStageVO;
    });
  }

  function getUnionInfo(stage: ExtendedStageVO): UnionInfo[] {
    // 如果没有联台ID，返回空数组
    if (!stage.unionRoomId) {
      return [];
    }

    // 检查缓存中是否已存在联台信息
    if (unionInfoMap.value.has(stage.unionRoomId)) {
      return unionInfoMap.value.get(stage.unionRoomId) || [];
    }

    // 找到所有具有相同unionRoomId的房间
    const unionRooms = stages.value.filter(s => s.unionRoomId === stage.unionRoomId);

    // 生成联台信息
    const unionInfos: UnionInfo[] = unionRooms.map(unionRoom => ({
      unionId: unionRoom.roomVO.id,
      unionName: unionRoom.roomVO.name,
      isMainUnion: unionRoom.roomVO.id === stage.unionRoomId // 主房间的ID与unionRoomId相同
    }));

    // 缓存结果
    unionInfoMap.value.set(stage.unionRoomId, unionInfos);

    return unionInfos;
  }

  function setStages(newStages: ExtendedStageVO[]) {
    // 保存当前选中的包厢ID
    const currentSelectedRoomId = currentStage.value?.roomVO?.id;

    // 清空联台信息缓存
    unionInfoMap.value.clear();
    stages.value = newStages;
    updateStageCombinedStatus();

    // 如果之前有选中的包厢，尝试从新数据中找到并恢复选中状态
    if (currentSelectedRoomId) {
      const restoredStage = stages.value.find(stage => stage.roomVO.id === currentSelectedRoomId);
      if (restoredStage) {
        currentStage.value = restoredStage;
        console.log('[stageStore] 恢复选中状态:', restoredStage.roomVO.name);
      } else {
        console.log('[stageStore] 未找到之前选中的包厢，清空选中状态');
        currentStage.value = null;
      }
    }

    // 同步所有stage的定时器
    scheduleManager.syncAllStages(stages.value);
  }

  function getStages() {
    return stages.value;
  }

  function getStageTags(stage: ExtendedStageVO) {
    const sessionEndTime = stage.sessionVO?.endTime;
    const now = now10();
    const tags: string[] = [];
    if (stage.sessionVO?.isTimeConsume) {
      tags.push(SESSION_TAGS.ISTIMECONSUME);
    } else if (sessionEndTime && sessionEndTime < now) {
      tags.push(SESSION_TAGS.TIMEOUT);
    }

    // 优化预订标签逻辑：当天的显示，不是当天的话，未来6个小时要来的，也显示
    if (stage.bookingVOs && stage.bookingVOs.length > 0) {
      // 筛选出当天或未来6小时内的预订
      const validBookings = stage.bookingVOs.filter(booking => {
        if (!booking.arrivalTime) return false;

        const arrivalTime = booking.arrivalTime;
        const sixHoursLater = now + 6 * 60 * 60; // 6小时 = 6 * 60 * 60秒

        // 检查是否为当天的预订
        const arrivalDate = new Date(arrivalTime * 1000);
        const nowDate = new Date(now * 1000);
        const isSameDay =
          arrivalDate.getFullYear() === nowDate.getFullYear() && arrivalDate.getMonth() === nowDate.getMonth() && arrivalDate.getDate() === nowDate.getDate();

        // 当天的显示，或者未来6小时内的预订也显示
        const isWithinSixHours = arrivalTime >= now && arrivalTime <= sixHoursLater;

        const result = isSameDay || isWithinSixHours;
        // console.log(
        //   '[stageStore] getStageTags',
        //   stage.roomVO.name,
        //   '结果:',
        //   result,
        //   '同一天:',
        //   isSameDay,
        //   '6小时内:',
        //   isWithinSixHours,
        //   '到达时间:',
        //   new Date(arrivalTime * 1000).toLocaleString(),
        //   '当前时间:',
        //   new Date(now * 1000).toLocaleString(),
        //   '6小时后:',
        //   new Date(sixHoursLater * 1000).toLocaleString()
        // );
        return result;
      });

      // console.log('[stageStore] getStageTags', validBookings);
      if (validBookings.length > 0) {
        tags.push(SESSION_TAGS.BOOKED);
      }
    }

    if (stage.unionRoomId) {
      tags.push(SESSION_TAGS.UNION);
    }
    if (stage.roomVO.tag.includes(SESSION_TAGS.LOCKED)) {
      tags.push(SESSION_TAGS.LOCKED);
    }
    return tags;
  }

  function setSelectedStage(stage: ExtendedStageVO | null) {
    currentStage.value = stage;
  }

  // 更新舞台信息
  function updateStageInfo(updatedStage: Partial<ExtendedStageVO>) {
    const index = stages.value.findIndex(stage => stage.roomVO.id === updatedStage.roomVO?.id);
    if (index !== -1) {
      stages.value[index] = { ...stages.value[index], ...updatedStage };
    }
    if (currentStage.value && currentStage.value.roomVO.id === updatedStage.roomVO?.id) {
      currentStage.value = { ...currentStage.value, ...updatedStage };
    }
    updateStageCombinedStatus();

    // 如果更新包含session信息和endTime，更新定时器
    if (updatedStage.sessionVO?.sessionId && updatedStage.sessionVO?.endTime) {
      scheduleManager.updateSessionEndTime(updatedStage.sessionVO.sessionId, Number(updatedStage.sessionVO.endTime));
    }
  }

  function updateStageByRoomId(roomId: string, updatedStage: Partial<ExtendedStageVO>) {
    const stage = getStageByRoomId(roomId);
    if (stage) {
      updateStageInfo({ ...stage, ...updatedStage } as Partial<ExtendedStageVO>);
    }
  }

  function setStageSource(source: 'realtime' | 'internal' | null) {
    stageSource.value = source;
  }

  function clearStageSource() {
    stageSource.value = null;
  }

  function getStageByRoomId(roomId: string) {
    // console.log('[dialog option] getStageByRoomId', roomId, stages.value)
    return stages.value.find(stage => stage.roomVO.id === roomId);
  }

  function updateStageOrderStatus(roomId: string, status: string) {
    const stage = getStageByRoomId(roomId);
    if (stage) {
      stage.sessionVO.status = status;
      stage.payStatus = status; // 同时更新扩展属性
      updateStageCombinedStatus(); // 更新组合状态
    }
  }

  function getStageBySessionId(sessionId: string) {
    return stages.value.find(stage => stage.sessionVO?.sessionId === sessionId);
  }

  function updateSessionPaied(sessionId: string) {
    const stage = getStageBySessionId(sessionId);
    if (stage) {
      stage.sessionVO.status = SESSION_PAY_STATUS.PAID;
      stage.payStatus = SESSION_PAY_STATUS.PAID; // 同时更新扩展属性
      updateStageCombinedStatus(); // 更新组合状态
    }
  }

  function updateStageSession(roomId: string, session: Partial<SessionVO>) {
    const stage = getStageByRoomId(roomId);
    if (stage) {
      stage.sessionVO = { ...stage.sessionVO, ...session };

      // 如果更新包含sessionId和endTime，更新定时器
      if (stage.sessionVO.sessionId && stage.sessionVO.endTime) {
        scheduleManager.updateSessionEndTime(stage.sessionVO.sessionId, Number(stage.sessionVO.endTime));
      }

      updateStageCombinedStatus();
    }
  }

  /**
   * 从NATS消息触发的刷新包厢状态操作
   * 该方法包含节流逻辑，防止频繁刷新
   * @param force 是否强制刷新，忽略节流
   * @returns Promise<boolean> 是否执行了刷新操作
   */
  async function refreshStagesFromNats(force: boolean = false): Promise<boolean> {
    const now = Date.now();
    // 如果正在刷新或者距离上次刷新时间不足阈值且不是强制刷新，则跳过
    if (isRefreshing.value || (!force && now - lastRefreshTime.value < REFRESH_THROTTLE_MS)) {
      console.log('NATS触发刷新被节流或跳过，距上次刷新:', now - lastRefreshTime.value, 'ms');
      return false;
    }

    try {
      console.log('NATS消息触发包厢状态刷新');
      isRefreshing.value = true;

      // 调用API获取最新的包厢数据
      const result = await RoomApi.stageRoom({
        roomId: '',
        typeId: '',
        areaId: '',
        sessionId: ''
      });

      if (result && result.data) {
        // 使用RealTimeTableConverter处理API返回的数据，转换为ExtendedStageVO类型
        const processedData = RealTimeTableConverter.processStageData(result.data);

        // 更新包厢数据
        setStages(processedData);
        lastRefreshTime.value = Date.now();
        console.log('NATS触发的包厢刷新完成');
        return true;
      }
      return false;
    } catch (error) {
      console.error('NATS触发的包厢刷新失败:', error);
      return false;
    } finally {
      isRefreshing.value = false;
    }
  }

  // 初始化时序管理器
  scheduleManager = new StageScheduleManager(getStageBySessionId, updateStageCombinedStatus);

  const getStageSource = computed(() => stageSource.value);

  return {
    stages,
    setStages,
    getStages,
    currentStage,
    getCurrentStage,
    getStageBySessionId,
    setCurrentStage,
    setSelectedStage,
    updateStageInfo,
    stageSource,
    setStageSource,
    getStageByRoomId,
    updateStageOrderStatus,
    updateStageByRoomId,
    updateStageSession,
    updateStageCombinedStatus,
    updateSessionPaied,
    getStageSource,
    clearStageSource,
    // 新增方法
    refreshStagesFromNats,
    // 时序管理器相关方法
    clearAllTimers: () => scheduleManager.clearAllTimers()
  };
});
