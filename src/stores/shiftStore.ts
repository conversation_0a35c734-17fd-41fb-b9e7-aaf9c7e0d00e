import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

/**
 * 交班历史页面筛选条件
 */
interface ShiftHistoryFilter {
  dateRange: [number, number] | null;
  searchText: string;
}

/**
 * 页面进入来源
 */
type PageSource = 'tab-switch' | 'detail-return' | 'direct';

export const useShiftStore = defineStore('shift', () => {
  // 交班历史页面的筛选条件
  const historyFilter = ref<ShiftHistoryFilter>({
    dateRange: null,
    searchText: ''
  });

  // 页面进入来源
  const pageSource = ref<PageSource>('direct');

  // 获取默认日期范围（过去7天）
  const getDefaultDateRange = (): [number, number] => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 7);

    // 设置开始时间为当天00:00:00
    startDate.setHours(0, 0, 0, 0);

    // 设置结束时间为当天23:59:59
    endDate.setHours(23, 59, 59, 999);

    return [startDate.getTime(), endDate.getTime()];
  };

  // 计算属性：是否应该使用保存的筛选条件
  const shouldUseStoredFilter = computed(() => {
    return pageSource.value === 'detail-return';
  });

  // 计算属性：获取有效的日期范围（如果没有保存的或来源是tab切换，则使用默认值）
  const effectiveDateRange = computed((): [number, number] => {
    if (pageSource.value === 'tab-switch' || !historyFilter.value.dateRange) {
      return getDefaultDateRange();
    }
    return historyFilter.value.dateRange;
  });

  // Actions
  const actions = {
    /**
     * 设置页面进入来源
     */
    setPageSource(source: PageSource) {
      pageSource.value = source;
    },

    /**
     * 保存交班历史筛选条件
     */
    saveHistoryFilter(filter: ShiftHistoryFilter) {
      historyFilter.value = { ...filter };
    },

    /**
     * 清除交班历史筛选条件
     */
    clearHistoryFilter() {
      historyFilter.value = {
        dateRange: null,
        searchText: ''
      };
    },

    /**
     * 从tab切换进入交班历史页面
     */
    enterHistoryFromTab() {
      pageSource.value = 'tab-switch';
      // tab切换时重置筛选条件
      actions.clearHistoryFilter();
    },

    /**
     * 从详情页面返回交班历史页面
     */
    enterHistoryFromDetail() {
      pageSource.value = 'detail-return';
      // 从详情返回时保持之前的筛选条件
    },

    /**
     * 直接进入交班历史页面
     */
    enterHistoryDirect() {
      pageSource.value = 'direct';
    }
  };

  return {
    // State
    historyFilter,
    pageSource,

    // Getters
    shouldUseStoredFilter,
    effectiveDateRange,

    // Actions
    ...actions
  };
});

export type { ShiftHistoryFilter, PageSource };
