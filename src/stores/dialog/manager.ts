// =====================================================================
// 对话框管理器 - 系统核心文件
// =====================================================================
/**
 * 对话框管理器
 *
 * 本模块负责：
 * 1. 提供统一的对话框管理接口
 * 2. 支持同步和异步对话框的打开和关闭
 * 3. 支持Promise风格的对话框调用
 * 4. 支持对话框队列管理
 */

import { defineStore } from 'pinia';
import { ref, markRaw, shallowRef } from 'vue';
import type { Component } from 'vue';
import { getDialogComponent, getDialogConfig } from '@/registry/dialog';
import type { DialogConfig } from '@/types/dialog';

// 对话框实例类型
interface DialogInstance {
  id: string;
  name: string;
  component: Component | null;
  props: Record<string, any>;
  events?: Record<string, (...args: any[]) => void>;
  resolve?: (value: any) => void;
  reject?: (reason?: any) => void;
}

/**
 * 对话框管理器Store
 */
export const useDialogStore = defineStore('dialogManager', () => {
  // 当前打开的对话框队列
  const dialogQueue = ref<DialogInstance[]>([]);

  // 对话框计数器（用于生成唯一ID）
  let dialogCounter = 0;

  /**
   * 生成唯一对话框ID
   */
  function generateDialogId(name: string): string {
    return `${name}_${++dialogCounter}`;
  }

  /**
   * 打开对话框
   * @param options 对话框选项
   * @returns Promise 对话框结果
   */
  async function openDialog(options: {
    name: string;
    props?: Record<string, any>;
    events?: Record<string, (...args: any[]) => void>;
    singleton?: boolean; // 是否单例模式
  }) {
    const { name, props = {}, events = {}, singleton = false } = options;

    // 获取对话框配置
    const config = getDialogConfig(name);
    if (!config) {
      console.error(`对话框 ${name} 未注册`);
      return Promise.reject(new Error(`对话框 ${name} 未注册`));
    }

    // 提取UI相关配置属性
    const {
      title,
      width,
      height,
      fullscreen,
      top,
      modal,
      lockScroll,
      closeOnClickModal,
      closeOnPressEscape,
      showClose,
      center,
      destroyOnClose,
      showHeader,
      showFooter,
      customClass,
      level,
      uiType
    } = config;

    // 合并默认属性和自定义属性
    const mergedProps = {
      // UI相关配置属性
      title,
      width,
      height,
      fullscreen,
      top,
      modal,
      lockScroll,
      closeOnClickModal,
      closeOnPressEscape,
      showClose,
      center,
      destroyOnClose,
      showHeader,
      showFooter,
      customClass,
      level,
      uiType,
      // 默认props
      ...(config.defaultProps || {}),
      // 用户传入的props (优先级最高)
      ...props,
      // 默认设置visible为true
      visible: true
    };

    try {
      // 获取对话框组件
      const componentResult = getDialogComponent(name);

      let component: Component | null = null;

      // 处理同步或异步组件
      if (componentResult instanceof Promise) {
        // 异步组件
        try {
          const asyncModule = (await componentResult) as any;

          // 处理可能的 ES 模块导出
          // 确保处理各种模块格式
          if (asyncModule) {
            if (asyncModule.default) {
              // ES 模块
              component = markRaw(asyncModule.default);
            } else if (typeof asyncModule === 'function' || typeof asyncModule === 'object') {
              // CommonJS 模块或直接导出的组件
              component = markRaw(asyncModule);
            } else {
              console.error(`异步组件格式不支持:`, asyncModule);
              return Promise.reject(new Error(`异步组件 ${name} 格式不支持`));
            }
          } else {
            console.error(`异步组件加载失败，结果为空`);
            return Promise.reject(new Error(`异步组件 ${name} 加载失败，结果为空`));
          }
        } catch (loadError) {
          console.error(`加载异步组件失败:`, loadError);
          return Promise.reject(new Error(`加载异步组件 ${name} 失败: ${loadError}`));
        }
      } else {
        // 同步组件
        component = componentResult;
      }

      if (!component) {
        console.error(`对话框组件 ${name} 加载失败，组件为空`);
        return Promise.reject(new Error(`对话框组件 ${name} 加载失败，组件为空`));
      }

      // 创建对话框实例
      const dialogId = generateDialogId(name);

      // 创建并返回Promise
      return new Promise((resolve, reject) => {
        // 添加到对话框队列
        dialogQueue.value.push({
          id: dialogId,
          name,
          component,
          props: mergedProps,
          events: {
            ...events,
            // 添加关闭事件处理
            close: () => {
              closeDialog(dialogId);
            },
            // 添加确认事件处理，将数据传递给resolve
            confirm: (data: any) => {
              closeDialog(dialogId, data);
            },
            // 添加取消事件处理
            cancel: () => {
              closeDialog(dialogId);
            }
          },
          resolve,
          reject
        });
      });
    } catch (error) {
      console.error(`打开对话框 ${name} 失败:`, error);
      return Promise.reject(error);
    }
  }

  /**
   * 关闭对话框
   * @param dialogId 对话框ID
   * @param result 对话框结果
   */
  function closeDialog(dialogId: string, result?: any) {
    const index = dialogQueue.value.findIndex(d => d.id === dialogId);
    if (index > -1) {
      const dialog = dialogQueue.value[index];
      // 调用resolve回调
      dialog.resolve?.(result);
      // 从队列中移除
      dialogQueue.value.splice(index, 1);
    }
  }

  /**
   * 关闭所有对话框
   */
  function closeAllDialogs() {
    dialogQueue.value.forEach(dialog => {
      dialog.resolve?.(undefined);
    });
    dialogQueue.value = [];
  }

  /**
   * 根据名称关闭对话框
   * @param name 对话框名称
   * @param result 对话框结果
   */
  function closeDialogByName(name: string, result?: any) {
    const dialog = dialogQueue.value.find(d => d.name === name);
    if (dialog) {
      closeDialog(dialog.id, result);
    }
  }

  return {
    dialogQueue,
    openDialog,
    closeDialog,
    closeAllDialogs,
    closeDialogByName
  };
});
