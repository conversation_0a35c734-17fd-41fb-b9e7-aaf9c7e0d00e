import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { CashierMachineVO } from '@/api/autoGenerated/shared/types/other';
import type { VenueVO } from '@/api/autoGenerated/shared/types/venue';

export interface DeviceInfo {
  macAddress?: string;
  ipAddress?: string;
  deviceId?: string;
  location?: string;
  deviceType?: string;
  appVersion?: string;
  appVersionCode?: string;
}

function generateClientId(deviceInfo: DeviceInfo): string {
  // 优先使用 MAC地址+IP组合
  if (deviceInfo.macAddress && deviceInfo.ipAddress) {
    return `${deviceInfo.macAddress}_${deviceInfo.ipAddress}`.replace(/:/g, '-');
  }

  // 其次使用 deviceId
  if (deviceInfo.deviceId) {
    return `device_${deviceInfo.deviceId}`;
  }

  // 如果都没有,生成随机ID
  return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export const useDeviceStore = defineStore(
  'device',
  () => {
    // 状态
    const deviceId = ref('');
    const macAddress = ref('');
    const ipAddress = ref('');
    const clientId = ref('');
    const deviceType = ref('');
    const location = ref('');
    const appVersion = ref('');
    const appVersionCode = ref('');
    const cashierMachine = ref<CashierMachineVO | null>(null);

    // 方法
    function setDeviceId(newDeviceId: string) {
      deviceId.value = newDeviceId || '';
      console.log('setDeviceId', deviceId.value);
    }

    function setMacAddress(newMacAddress: string) {
      macAddress.value = newMacAddress || '';
      console.log('setMacAddress', macAddress.value);
    }

    function setIpAddress(newIpAddress: string) {
      ipAddress.value = newIpAddress || '';
      console.log('setIpAddress', ipAddress.value);
    }

    function setLocation(newLocation: string) {
      location.value = newLocation || '';
      console.log('setLocation', location.value);
    }

    function setDeviceType(newDeviceType: string) {
      deviceType.value = newDeviceType || '';
      console.log('setDeviceType', deviceType.value);
    }

    function setAppVersion(newAppVersion: string) {
      appVersion.value = newAppVersion || '';
      console.log('setAppVersion', appVersion.value);
    }

    function setAppVersionCode(newAppVersionCode: string) {
      appVersionCode.value = newAppVersionCode || '';
      console.log('setAppVersionCode', appVersionCode.value);
    }

    function getDeviceInfo() {
      return {
        deviceId: deviceId.value,
        macAddress: macAddress.value,
        ipAddress: ipAddress.value,
        location: location.value,
        deviceType: deviceType.value,
        appVersion: appVersion.value,
        appVersionCode: appVersionCode.value
      };
    }

    function initDeviceInfo(info: DeviceInfo) {
      deviceId.value = info.deviceId || '';
      macAddress.value = info.macAddress || '';
      ipAddress.value = info.ipAddress || '';
      location.value = info.location || '';
      deviceType.value = info.deviceType || '';
      clientId.value = generateClientId(info);
      appVersion.value = info.appVersion || '';
      appVersionCode.value = info.appVersionCode || '';
    }

    function clearDeviceInfo() {
      deviceId.value = '';
      macAddress.value = '';
      ipAddress.value = '';
      clientId.value = '';
      appVersion.value = '';
      appVersionCode.value = '';
    }

    function setCashierMachine(machine: CashierMachineVO | null) {
      cashierMachine.value = machine;
      console.log('setCashierMachine', cashierMachine.value);
    }

    function clearAuthInfo() {
      cashierMachine.value = null;
    }

    return {
      // 状态
      deviceId,
      macAddress,
      ipAddress,
      clientId,
      deviceType,
      location,
      appVersion,
      appVersionCode,
      cashierMachine,
      // 方法
      setDeviceId,
      setMacAddress,
      setIpAddress,
      setLocation,
      setDeviceType,
      setAppVersion,
      setAppVersionCode,
      getDeviceInfo,
      initDeviceInfo,
      clearDeviceInfo,
      setCashierMachine,
      clearAuthInfo
    };
  },
  {
    persist: {
      storage: localStorage,
      paths: ['deviceId', 'macAddress', 'ipAddress', 'clientId', 'deviceType', 'location', 'cashierMachine', 'appVersion', 'appVersionCode']
    }
  }
);
