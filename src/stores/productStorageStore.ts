import { defineStore } from 'pinia';
import { ref, computed, readonly } from 'vue';
import { ElMessage } from 'element-plus';
import { postApiWineStorageSettingQuery } from '@/api/autoGenerated/defaultVersion/wineStorageSetting';
import type { WineStorageSettingVO, QueryWineStorageSettingReqDto } from '@/api/autoGenerated/shared/types/storage';
import { useVenueStore } from './venueStore';
import { useUserStore } from './userStore';

/**
 * 存酒配置Store
 * 管理门店存酒相关的配置信息
 */
export const useProductStorageStore = defineStore('productStorage', () => {
  // 状态
  const settings = ref<WineStorageSettingVO | null>(null);
  const loading = ref(false);

  // 计算属性
  const isLoaded = computed(() => settings.value !== null);

  const isLoading = computed(() => loading.value);

  // 获取存储天数
  const storageDays = computed(() => settings.value?.storageDays || 30);

  // 获取续存次数
  const renewalTimes = computed(() => settings.value?.renewalTimes || 3);

  // 获取续存天数
  const renewalDays = computed(() => settings.value?.renewalDays || 30);

  // 获取客户通知天数
  const customerNotificationDays = computed(() => settings.value?.customerNotificationDays || 30);

  // 获取商户通知天数
  const merchantNotificationDays = computed(() => settings.value?.merchantNotificationDays || 30);

  // 是否自动报废
  const autoConfiscate = computed(() => settings.value?.autoConfiscate || false);

  // 过期取酒限制天数
  const overdueWithdrawalLimit = computed(() => settings.value?.overdueWithdrawalLimit || 31);

  // 自动报废天数
  const autoConfiscateDays = computed(() => settings.value?.autoConfiscateDays || 0);

  // 商户到期提醒人员列表
  const merchantExpirationReminder = computed(() => {
    if (!settings.value?.merchantExpirationReminder) return [];
    try {
      return JSON.parse(settings.value.merchantExpirationReminder);
    } catch {
      return [];
    }
  });

  // 代理商到期提醒人员列表
  const agentExpirationReminder = computed(() => {
    if (!settings.value?.agentExpirationReminder) return [];
    try {
      return JSON.parse(settings.value.agentExpirationReminder);
    } catch {
      return [];
    }
  });

  // 充公仓库列表
  const confiscateWarehouse = computed(() => {
    if (!settings.value?.confiscateWarehouse) return [];
    try {
      return JSON.parse(settings.value.confiscateWarehouse);
    } catch {
      return [];
    }
  });

  /**
   * 获取存酒配置
   * @param forceRefresh 是否强制刷新
   */
  const fetchSettings = async (forceRefresh = false): Promise<WineStorageSettingVO | null> => {
    // 如果正在加载中，返回当前设置
    if (loading.value) {
      return settings.value;
    }

    loading.value = true;

    try {
      const venueStore = useVenueStore();
      const userStore = useUserStore();

      const venueId = venueStore.venueId;
      if (!venueId) {
        console.warn('门店ID不存在，无法获取存酒配置');
        return null;
      }

      const requestParams: QueryWineStorageSettingReqDto = {
        venueId
      };

      const response = await postApiWineStorageSettingQuery(requestParams);

      if (response.data && response.data.length > 0) {
        // 取第一个配置（通常一个门店只有一个配置）
        settings.value = response.data[0];
        console.log('存酒配置获取成功:', settings.value);
        return settings.value;
      } else {
        console.warn('获取存酒配置失败或配置不存在');
        // 设置默认配置
        setDefaultSettings(venueId);
        return settings.value;
      }
    } catch (error) {
      console.error('获取存酒配置时发生错误:', error);
      ElMessage.error('获取存酒配置失败');

      // 发生错误时使用默认配置
      const venueStore = useVenueStore();
      setDefaultSettings(venueStore.venueId || '');
      return settings.value;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 设置默认配置
   */
  const setDefaultSettings = (venueId: string) => {
    settings.value = {
      id: '',
      venueId,
      storageDays: 30,
      renewalTimes: 3,
      renewalDays: 20, // 修正默认值为20天
      customerNotificationDays: 30,
      merchantNotificationDays: 30,
      merchantExpirationReminder: '[]',
      agentExpirationReminder: '[]',
      autoConfiscate: false,
      overdueWithdrawalLimit: 31,
      autoConfiscateDays: 0,
      confiscateWarehouse: '[]',
      ctime: Date.now(),
      utime: Date.now(),
      state: 0,
      version: 0
    };
    console.log('使用默认存酒配置');
  };

  /**
   * 刷新配置
   */
  const refreshSettings = () => {
    return fetchSettings(true);
  };

  /**
   * 清除缓存
   */
  const clearCache = () => {
    settings.value = null;
  };

  /**
   * 初始化配置（如果尚未加载）
   */
  const ensureSettings = async (): Promise<WineStorageSettingVO | null> => {
    if (!settings.value) {
      return await fetchSettings();
    }
    return settings.value;
  };

  /**
   * 根据配置计算过期日期（Unix时间戳）
   * @param storageTime 存储时间（Unix时间戳，秒）
   * @returns 过期时间（Unix时间戳，秒）
   */
  const calculateExpireTime = (storageTime: number): number => {
    const storageDuration = storageDays.value * 24 * 60 * 60; // 转换为秒
    return storageTime + storageDuration;
  };

  /**
   * 检查是否可以续存
   * @param currentRenewalCount 当前已续存次数
   * @returns 是否可以续存
   */
  const canRenew = (currentRenewalCount: number): boolean => {
    return currentRenewalCount < renewalTimes.value;
  };

  /**
   * 计算续存后的过期时间
   * @param currentExpireTime 当前过期时间（Unix时间戳，秒）
   * @returns 续存后的过期时间（Unix时间戳，秒）
   */
  const calculateRenewExpireTime = (currentExpireTime: number): number => {
    const renewalDuration = renewalDays.value * 24 * 60 * 60; // 转换为秒
    return currentExpireTime + renewalDuration;
  };

  /**
   * 检查是否超过取酒限制
   * @param expireTime 过期时间（Unix时间戳，秒）
   * @returns 是否超过取酒限制
   */
  const isOverWithdrawalLimit = (expireTime: number): boolean => {
    const now = Math.floor(Date.now() / 1000);
    const overdueDays = Math.floor((now - expireTime) / (24 * 60 * 60));
    return overdueDays > overdueWithdrawalLimit.value;
  };

  return {
    // 状态
    settings: readonly(settings),
    loading: readonly(loading),

    // 计算属性
    isLoaded,
    isLoading,
    storageDays,
    renewalTimes,
    renewalDays,
    customerNotificationDays,
    merchantNotificationDays,
    autoConfiscate,
    overdueWithdrawalLimit,
    autoConfiscateDays,
    merchantExpirationReminder,
    agentExpirationReminder,
    confiscateWarehouse,

    // 方法
    fetchSettings,
    refreshSettings,
    clearCache,
    ensureSettings,
    calculateExpireTime,
    canRenew,
    calculateRenewExpireTime,
    isOverWithdrawalLimit
  };
});

export type ProductStorageStore = ReturnType<typeof useProductStorageStore>;
