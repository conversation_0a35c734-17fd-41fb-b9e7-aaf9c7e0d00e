import {
  Receipt,
  ReceiptElement,
  ReceiptElementType,
  ReceiptText,
  ReceiptTable,
  ReceiptLine,
  ReceiptEmptyLine,
  TextAlignment,
  FontSize,
} from '@/domains/prints/shared/models/receipt-model';
import { PrintCommands } from './printCommands';

/**
 * 模拟热敏打印机输出的样式配置
 */
interface ConsolePrinterStyle {
  width: number; // 热敏纸宽度（字符数）
  bold: (text: string) => string; // 加粗文本处理函数
  doubleSize: (text: string) => string; // 双倍大小文本处理函数
  doubleHeight: (text: string) => string; // 双倍高度文本处理函数
  doubleWidth: (text: string) => string; // 双倍宽度文本处理函数
}

/**
 * 热敏打印机模拟器类
 * 负责将Receipt对象转换为控制台可显示的格式，模拟热敏打印机输出效果
 */
export class ThermoPrinterSimulator {
  /**
   * 模拟打印Receipt到控制台
   * @param receipt Receipt对象
   * @param logOutput 是否实际输出到控制台，false时仅返回输出内容（用于测试）
   * @returns 如果logOutput为false，返回所有输出行的数组
   */
  simulatePrint(receipt: Receipt, logOutput: boolean = true): string[] | void {
    // 定义控制台样式
    const style: ConsolePrinterStyle = {
      width: PrintCommands.TOTAL_WIDTH,
      bold: (text: string) => `\x1b[1m${text}\x1b[0m`, // 控制台加粗
      doubleSize: (text: string) => `\x1b[1m${text}\x1b[0m`, // 简单模拟双倍大小（使用加粗）
      doubleHeight: (text: string) => `\x1b[1m${text}\x1b[0m`, // 简单模拟双倍高度（使用加粗）
      doubleWidth: (text: string) => `\x1b[1m${text}\x1b[0m`, // 简单模拟双倍宽度（使用加粗）
    };

    // 收集所有输出行
    const outputLines: string[] = [];

    // 打印机边框头部
    outputLines.push('\n\n');
    outputLines.push('┌' + '─'.repeat(style.width) + '┐');
    outputLines.push('│' + ' '.repeat(style.width) + '│');
    outputLines.push('│' + ' '.repeat(Math.max(0, (style.width - 14) / 2)) + '模拟热敏打印机' + ' '.repeat(Math.max(0, (style.width - 14) / 2)) + '│');
    outputLines.push('│' + ' '.repeat(style.width) + '│');
    outputLines.push('├' + '─'.repeat(style.width) + '┤');

    // 处理每个票据元素
    for (const element of receipt.elements) {
      const elementLines = this.renderElementToLines(element, style);
      outputLines.push(...elementLines);
    }

    // 打印机边框尾部
    outputLines.push('│' + ' '.repeat(style.width) + '│');
    outputLines.push('│' + ' '.repeat(Math.max(0, (style.width - 16) / 2)) + '打印完成，已切纸' + ' '.repeat(Math.max(0, (style.width - 16) / 2)) + '│');
    outputLines.push('│' + ' '.repeat(style.width) + '│');
    outputLines.push('└' + '─'.repeat(style.width) + '┘');
    outputLines.push('\n\n');

    // 根据logOutput参数决定是输出到控制台还是返回结果
    if (logOutput) {
      outputLines.forEach(line => console.log(line));
      return;
    } else {
      return outputLines;
    }
  }

  /**
   * 将单个票据元素渲染成行数组
   * @param element 票据元素
   * @param style 样式配置
   * @returns 渲染后的行数组
   */
  private renderElementToLines(element: ReceiptElement, style: ConsolePrinterStyle): string[] {
    switch (element.type) {
      case ReceiptElementType.TEXT:
        return this.renderTextToLines(element as ReceiptText, style);
      case ReceiptElementType.TABLE:
        // 对于表格，使用PrintCommands.table生成的文本，确保与实际打印一致
        return this.renderTableUsingPrintCommands(element as ReceiptTable, style);
      case ReceiptElementType.LINE:
        return this.renderLineToLines(element as ReceiptLine, style);
      case ReceiptElementType.EMPTY_LINE:
        return this.renderEmptyLineToLines(element as ReceiptEmptyLine, style);
      default:
        console.warn('未知的票据元素类型:', element.type);
        return [];
    }
  }

  /**
   * 将文本元素渲染成行数组
   * @param element 文本元素
   * @param style 样式配置
   * @returns 渲染后的行数组
   */
  private renderTextToLines(element: ReceiptText, style: ConsolePrinterStyle): string[] {
    let content = element.content;
    if (content.endsWith('\n')) {
      content = content.slice(0, -1);
    }

    // 应用样式
    if (element.bold) {
      content = style.bold(content);
    }

    if (element.fontSize) {
      switch (element.fontSize) {
        case FontSize.DOUBLE:
          content = style.doubleSize(content);
          break;
        case FontSize.DOUBLE_HEIGHT:
          content = style.doubleHeight(content);
          break;
        case FontSize.DOUBLE_WIDTH:
          content = style.doubleWidth(content);
          break;
      }
    }

    // 应用对齐方式
    let paddedContent = content;
    const contentWidth = this.getPlainTextWidth(content);
    
    if (element.align === TextAlignment.CENTER) {
      // 确保 leftPadding 不小于 0
      const leftPadding = Math.max(0, Math.floor((style.width - contentWidth) / 2));
      paddedContent = ' '.repeat(leftPadding) + content;
    } else if (element.align === TextAlignment.RIGHT) {
      // 确保 leftPadding 不小于 0
      const leftPadding = Math.max(0, style.width - contentWidth);
      paddedContent = ' '.repeat(leftPadding) + content;
    }

    return ['│' + this.padOrTruncateText(paddedContent, style.width) + '│'];
  }

  /**
   * 使用PrintCommands.table生成表格文本，确保与实际打印一致
   */
  private renderTableUsingPrintCommands(element: ReceiptTable, style: ConsolePrinterStyle): string[] {
    const lines: string[] = [];
    
    // 为每一行生成PrintCommand
    for (const row of element.columns) {
      // 转换为PrintCommands.table需要的格式
      const tableRow = row.map(column => ({
        text: column.text,
        width: column.width,
        align: column.align === TextAlignment.RIGHT ? 'right' as const :
               column.align === TextAlignment.CENTER ? 'center' as const :
               'left' as const
      }));
      
      // 使用PrintCommands.table生成命令数组
      const printCommands = PrintCommands.table(tableRow);
      
      // 提取文本内容（现在应该只有一个文本命令）
      let rowText = '';
      for (const command of printCommands) {
        if (command.type === 'text') {
          rowText += command.data as string;
        }
        // 忽略其他类型的命令（如对齐命令）
      }
      
      // 处理换行并添加边框
      if (rowText) {
        // 移除末尾的换行符，因为我们要自己添加边框
        const cleanRowText = rowText.endsWith('\n') ? rowText.slice(0, -1) : rowText;
        lines.push('│' + this.padOrTruncateText(cleanRowText, style.width) + '│');
      }
    }
    
    return lines;
  }

  /**
   * 将线条元素渲染成行数组
   * @param element 线条元素
   * @param style 样式配置
   * @returns 渲染后的行数组
   */
  private renderLineToLines(element: ReceiptLine, style: ConsolePrinterStyle): string[] {
    const char = element.char || '-';
    const length = element.length || style.width;
    return ['│' + char.repeat(length) + ' '.repeat(Math.max(0, style.width - length)) + '│'];
  }

  /**
   * 将空行元素渲染成行数组
   * @param element 空行元素
   * @param style 样式配置
   * @returns 渲染后的行数组
   */
  private renderEmptyLineToLines(element: ReceiptEmptyLine, style: ConsolePrinterStyle): string[] {
    const lines: string[] = [];
    const linesCount = element.lines || 1;
    
    for (let i = 0; i < linesCount; i++) {
      lines.push('│' + ' '.repeat(style.width) + '│');
    }
    
    return lines;
  }

  /**
   * 获取纯文本宽度（去除样式控制字符）
   * @param text 包含样式的文本
   * @returns 纯文本宽度
   */
  private getPlainTextWidth(text: string): number {
    // 移除ANSI控制字符
    const plainText = text.replace(/\x1b\[\d+m/g, '');
    return PrintCommands.getStringWidth(plainText);
  }

  /**
   * 填充或截断文本到指定宽度
   * @param text 文本
   * @param width 目标宽度
   * @returns 处理后的文本
   */
  private padOrTruncateText(text: string, width: number): string {
    const plainTextWidth = this.getPlainTextWidth(text);
    
    if (plainTextWidth < width) {
      return text + ' '.repeat(width - plainTextWidth);
    } else if (plainTextWidth > width) {
      // 截断文本是复杂的，特别是带有样式的文本，这里简化处理
      // 实际项目中可能需要更复杂的逻辑
      return text;
    }
    
    return text;
  }
}

// 导出单例
export const thermoPrinterSimulator = new ThermoPrinterSimulator(); 