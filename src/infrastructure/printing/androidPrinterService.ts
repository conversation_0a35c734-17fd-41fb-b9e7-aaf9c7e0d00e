import { PrintCommand, PrinterConfig } from './printerTypes';
import { toast } from '@/components/customer/toast';

/**
 * Android打印服务 - 封装异步回调为同步接口
 */
export class AndroidPrinterService {
  private static instance: AndroidPrinterService;
  private config: PrinterConfig = {
    ip: '',
    port: 9100
  };
  private callbacks: Map<string, { resolve: (value: boolean) => void, reject: (reason: any) => void }> = new Map();
  private callbackIdCounter: number = 0;

  private constructor() {
    this.initCallbackHandler();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AndroidPrinterService {
    if (!AndroidPrinterService.instance) {
      AndroidPrinterService.instance = new AndroidPrinterService();
    }
    return AndroidPrinterService.instance;
  }

  /**
   * 初始化回调处理器
   */
  private initCallbackHandler() {
    if (!window.printBridgeCallback) {
      const printBridgeCallback = (callbackId: string, responseText: string) => {
        console.log('接收到打印回调', callbackId, responseText);
        const callback = this.callbacks.get(callbackId);
        if (callback) {
          try {
            const response = JSON.parse(responseText);
            if (response.success) {
              callback.resolve(true);
            } else {
              callback.reject(new Error(response.error || '打印失败'));
            }
          } catch (error) {
            callback.reject(error);
          }
          // 完成后删除回调
          this.callbacks.delete(callbackId);
        }
      };
      window.printBridgeCallback = printBridgeCallback;
    }
  }

  /**
   * 配置打印机
   */
  public configure(config: Partial<PrinterConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 发送打印命令到打印机（同步接口）
   * @param commands 打印命令数组
   * @returns 是否打印成功
   */
  public async print(commands: PrintCommand[]): Promise<boolean> {
    try {
      if (!window.AndroidPrinter) {
        throw new Error('Android打印服务不可用');
      }

      const commandsJson = JSON.stringify(commands);
      console.log('发送异步打印命令:', commandsJson);
      
      return new Promise<boolean>((resolve, reject) => {
        // 生成回调ID
        const callbackId = `print_cb_${Date.now()}_${this.callbackIdCounter++}`;
        
        // 存储回调
        this.callbacks.set(callbackId, { resolve, reject });

        // 设置超时
        const timeout = setTimeout(() => {
          this.callbacks.delete(callbackId);
          reject(new Error('打印超时'));
        }, 30000); // 30秒超时

        // 重写resolve和reject以清除超时
        const originalResolve = resolve;
        const originalReject = reject;
        
        this.callbacks.set(callbackId, {
          resolve: (value: boolean) => {
            clearTimeout(timeout);
            originalResolve(value);
          },
          reject: (reason: any) => {
            clearTimeout(timeout);
            originalReject(reason);
          }
        });

        // 调用Android接口
        window.AndroidPrinter.rawPrintAsync(
          this.config.ip,
          this.config.port,
          commandsJson,
          callbackId
        );
      });
    } catch (error) {
      console.error('打印失败:', error);
      toast({ 
        title: '打印错误', 
        description: error instanceof Error ? error.message : '打印失败' 
      });
      return false;
    }
  }

  /**
   * 扫描网络打印机
   * @returns 打印机列表JSON字符串
   */
  public scanPrinters(): string {
    try {
      if (!window.AndroidPrinter?.scanPrinters) {
        throw new Error('Android打印机扫描服务不可用');
      }
      return window.AndroidPrinter.scanPrinters();
    } catch (error) {
      console.error('扫描打印机失败:', error);
      return '[]';
    }
  }

  /**
   * 测试打印
   * @param ip 打印机IP
   * @param port 打印机端口
   * @returns 是否打印成功
   */
  public testPrint(ip: string, port: number): boolean {
    try {
      if (!window.AndroidPrinter?.printTest) {
        throw new Error('Android打印测试服务不可用');
      }
      return window.AndroidPrinter.printTest(ip, port);
    } catch (error) {
      console.error('测试打印失败:', error);
      return false;
    }
  }
}

// TypeScript 类型声明
declare global {
  interface Window {
    AndroidPrinter?: {
      rawPrint(ip: string, port: number, commandsJson: string): boolean;
      rawPrintAsync(ip: string, port: number, commandsJson: string, callbackId: string): void;
      scanPrinters(): string;
      printTest(ip: string, port: number): boolean;
    };
    printBridgeCallback?: (callbackId: string, responseText: string) => void;
  }
}

// 导出打印服务单例
export const androidPrinterService = AndroidPrinterService.getInstance(); 