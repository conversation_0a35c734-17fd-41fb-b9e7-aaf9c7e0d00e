import { PrintCommand, PrinterConfig } from './printerTypes';
import { toast } from '@/components/customer/toast';

/**
 * 打印机服务 - 负责与物理打印机的通信
 */
export class PrinterService {
  private static instance: PrinterService;
  private config: PrinterConfig = {
    ip: '',
    port: 9100
  };

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): PrinterService {
    if (!PrinterService.instance) {
      PrinterService.instance = new PrinterService();
    }
    return PrinterService.instance;
  }

  /**
   * 配置打印机
   */
  public configure(config: Partial<PrinterConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 发送打印命令到打印机
   * @param commands 打印命令数组
   * @returns 是否打印成功
   */
  public async print(commands: PrintCommand[]): Promise<boolean> {
    try {
      if (!window.AndroidPrinter) {
        throw new Error('打印服务不可用');
      }

      const commandsJson = JSON.stringify(commands);
      console.log('打印命令:', commandsJson);
      
      return await window.AndroidPrinter.rawPrint(
        this.config.ip,
        this.config.port,
        commandsJson
      );
    } catch (error) {
      console.error('打印失败:', error);
      toast({ 
        title: '打印错误', 
        description: error instanceof Error ? error.message : '打印失败' 
      });
      return false;
    }
  }
}

// TypeScript 类型声明
declare global {
  interface Window {
    AndroidPrinter?: {
      rawPrint(ip: string, port: number, commandsJson: string): Promise<boolean>;
      rawPrintAsync?(ip: string, port: number, commandsJson: string, callbackId: string): void;
      scanPrinters?(): string;
      printTest?(ip: string, port: number): boolean;
    };
  }
}

// 导出打印服务单例
export const printerService = PrinterService.getInstance();

/**
 * 配置打印机快捷方法
 */
export function configurePrinter(config: Partial<PrinterConfig>): void {
  printerService.configure(config);
} 