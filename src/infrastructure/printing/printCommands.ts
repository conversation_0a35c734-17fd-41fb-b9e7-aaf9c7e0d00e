import { PrintCommand } from './printerTypes';

/**
 * 打印命令工具类
 * 提供 ESC/POS 打印命令的常量和辅助方法
 */
export class PrintCommands {
  static readonly TOTAL_WIDTH = 48; // 标准的48字符宽度
  static readonly SEPARATOR = ' ';  // 列分隔符

  // ESC/POS 命令常量
  static INIT = [0x1B, 0x40]; // ESC @，‌初始化打印机，清空缓冲区，恢复默认设置
  static ALIGN_LEFT = [0x1B, 0x61, 0x00]; // ESC a 0
  static ALIGN_CENTER = [0x1B, 0x61, 0x01]; // ESC a 1
  static ALIGN_RIGHT = [0x1B, 0x61, 0x02]; // ESC a 2
  static BOLD_ON = [0x1B, 0x45, 0x01]; // ESC E 1
  static BOLD_OFF = [0x1B, 0x45, 0x00]; // ESC E 0
  static FONT_SMALL = [0x1B, 0x4D, 0x01]; // ESC M 1
  static FONT_NORMAL = [0x1B, 0x4D, 0x00]; // ESC M 0
  static LINE_FEED = [0x0A]; // LF，换行，走纸一行
  static CUT_PAPER = [0x1D, 0x56, 0x41, 0x00]; // GS V A 0
  static CHINESE_MODE = [0x1C, 0x26]; // FS &

  static FONT_SIZE_NORMAL = [0x1D, 0x21, 0x00]; // GS ! 0 正常大小
  static FONT_SIZE_DOUBLE = [0x1D, 0x21, 0x11]; // GS ! 17 宽高都放大一倍
  static FONT_SIZE_DOUBLE_HEIGHT = [0x1D, 0x21, 0x01]; // GS ! 1 仅高度放大一倍
  static FONT_SIZE_DOUBLE_WIDTH = [0x1D, 0x21, 0x10]; // GS ! 16 仅宽度放大一倍

  static EMPTY_LINE = [0x0A, 0x0A]; // 两个换行符实现空行

  // ESC/POS 水平制表符命令
  static HORIZONTAL_TAB = [0x09]; // HT command

  /**
   * 创建命令类型的打印命令
   */
  static command(cmd: number[]): PrintCommand {
    return { type: 'command', data: cmd };
  }

  /**
   * 创建文本类型的打印命令
   */
  static text(content: string): PrintCommand {
    return { type: 'text', data: content };
  }

  /**
   * 创建分隔线命令
   */
  static line(char: string = '-', length: number = this.TOTAL_WIDTH): PrintCommand {
    return this.text(char.repeat(length) + '\n');
  }

  /**
   * 创建表格打印命令 - 使用ESC/POS水平制表符实现可靠的列对齐
   */
  static table(columns: Array<{ text: string, width: number, align?: 'left' | 'right' | 'center' }>): PrintCommand[] {
    const commands: PrintCommand[] = [];
    
    // 确保为左对齐模式
    commands.push(this.command(this.ALIGN_LEFT));
    
    // 计算每列的实际字符宽度
    const totalPercentage = columns.reduce((sum, col) => sum + col.width, 0);
    const columnWidths: number[] = [];
    const tabPositions: number[] = [];
    
    let currentPosition = 0;
    for (let i = 0; i < columns.length; i++) {
      const columnWidth = Math.floor((columns[i].width / totalPercentage) * this.TOTAL_WIDTH);
      columnWidths.push(columnWidth);
      
      if (i > 0) {
        tabPositions.push(currentPosition);
      }
      currentPosition += columnWidth;
    }
    
    // 构建表格行
    let rowText = '';
    
    for (let i = 0; i < columns.length; i++) {
      const column = columns[i];
      const width = columnWidths[i];
      let text = column.text;
      
      // 确保文本不超过列宽
      const textWidth = this.getStringWidth(text);
      if (textWidth > width) {
        text = this.truncateText(text, width);
      }
      
      // 第一列直接添加，其他列使用制表符分隔
      if (i === 0) {
        // 第一列：根据对齐方式处理，保持原有逻辑
        if (column.align === 'right') {
          const actualTextWidth = this.getStringWidth(text);
          const spaces = Math.max(0, width - actualTextWidth);
          rowText += ' '.repeat(spaces) + text;
        } else if (column.align === 'center') {
          const actualTextWidth = this.getStringWidth(text);
          const totalSpaces = Math.max(0, width - actualTextWidth);
          const leftSpaces = Math.floor(totalSpaces / 2);
          rowText += ' '.repeat(leftSpaces) + text;
        } else {
          // 左对齐
          rowText += text;
        }
        // 确保第一列填满指定宽度
        const currentWidth = this.getStringWidth(rowText);
        if (currentWidth < width) {
          rowText += ' '.repeat(width - currentWidth);
        }
      } else {
        // 其他列：使用制表符跳转到指定位置
        rowText += '\t';
        
        // 修复：对于右对齐，我们需要考虑列的实际边界
        if (column.align === 'right') {
          // 计算从制表符位置到列右边界的空间，然后放置文本
          const actualTextWidth = this.getStringWidth(text);
          const availableWidth = width;
          const spaces = Math.max(0, availableWidth - actualTextWidth);
          rowText += ' '.repeat(spaces) + text;
        } else if (column.align === 'center') {
          // 居中对齐
          const actualTextWidth = this.getStringWidth(text);
          const totalSpaces = Math.max(0, width - actualTextWidth);
          const leftSpaces = Math.floor(totalSpaces / 2);
          rowText += ' '.repeat(leftSpaces) + text;
        } else {
          // 左对齐
          rowText += text;
        }
      }
    }
    
    // 输出完整的行文本
    commands.push(this.text(rowText));
    commands.push(this.command(this.LINE_FEED));
    
    return commands;
  }

  /**
   * 截断文本到指定宽度
   */
  static truncateText(text: string, maxWidth: number): string {
    let width = 0;
    let result = '';
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const charWidth = this.getCharWidth(char);
      
      if (width + charWidth > maxWidth) {
        break;
      }
      
      width += charWidth;
      result += char;
    }
    
    return result;
  }

  /**
   * 获取单个字符的显示宽度
   */
  static getCharWidth(char: string): number {
    const code = char.charCodeAt(0);
    // 处理中文字符和全角字符
    if (code > 127 || (code >= 0xFF00 && code <= 0xFFEF)) {
      return 2;
    } else {
      return 1;
    }
  }

  /**
   * 获取字符串显示宽度
   */
  static getStringWidth(str: string): number {
    let width = 0;
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      // 处理中文字符和全角字符
      if (code > 127 || (code >= 0xFF00 && code <= 0xFFEF)) {
        width += 2;
      } else {
        width += 1;
      }
    }
    return width;
  }

  /**
   * 创建空行命令
   */
  static emptyLine(lines: number = 1): PrintCommand {
    return { 
      type: 'command', 
      data: Array(lines).fill(0x0A).flat() 
    };
  }
} 