import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from 'vue';
import ErpInputNumber from '../input/ErpInputNumber.vue';
import { ElInputNumber, ElCard, ElSpace, ElRow, ElCol, ElDivider } from 'element-plus';

// 定义 Story 类型
type Story = StoryObj<typeof meta>;

// 更多关于如何使用StoriesArgs类型的信息: https://storybook.js.org/docs/vue/writing-stories/args
const meta = {
  title: '输入组件/ErpInputNumber 数字输入框',
  component: ErpInputNumber,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
### ErpInputNumber 数字输入框

ErpInputNumber是基于Element Plus的数字输入框组件，提供了自定义样式和主题支持。组件实现了与设计图匹配的UI效果，特别是按钮的圆形样式和颜色主题。

#### 设计说明
本组件采用了样式分离的设计模式：
- **基础样式**：所有通用样式已迁移到全局样式文件中 \`src/style/element-plus/index.scss\`
  - 圆形边框设计 (border-radius: 200px)
  - 居中显示的数字
  - 不同尺寸的按钮布局
  - 控件位置（两侧/右侧）的样式
- **主题样式**：仅在组件内部保留不同主题颜色的按钮背景色设置
  - 各种主题的按钮颜色
  - 禁用状态的样式
- **自定义图标**：使用项目中的 IconPlus 和 IconMinus 组件替代默认的增减图标
  - 自定义SVG图标
  - 完美匹配设计规范

#### 最新设计特性
- **圆形外观**：组件采用了圆形轮廓设计，符合最新UI设计规范
- **虚线边框**：使用1px虚线边框(rgba(0, 0, 0, 0.1))，提供更精致的视觉效果
- **图标与数字间距**：优化了图标和数字之间的间距和对齐关系
- **标准尺寸**：标准模式下宽度为100px，高度为44px，提供统一的视觉体验

这种分离设计有几个优点：
1. 组件代码更加轻量，专注于业务逻辑
2. 样式全局统一，避免重复定义
3. 方便集中管理和维护样式
4. 更好的性能表现
5. 可以方便地使用自定义图标

#### 基本用法
\`\`\`vue
<erp-input-number v-model="value" />
\`\`\`

#### 主题类型
组件支持两种主题样式：
- default(默认) - 黑色按钮 (#000000)
- primary(主色) - 红色按钮 (#E23939)

\`\`\`vue
<erp-input-number v-model="value" type="primary" />
\`\`\`

#### 简化模式
组件支持简化模式，仅显示一个加号图标按钮，功能类似普通按钮：

\`\`\`vue
<erp-input-number v-model="value" simpleMode />
\`\`\`

在简化模式下：
- 点击按钮会增加关联值（根据step属性设置的步长）
- 会自动检查最大值限制
- 可以通过@click事件监听点击行为
- 支持disabled属性禁用按钮
- **不仅限于增加数值**：可以通过@click事件执行任意自定义操作，如打开对话框、显示消息等

\`\`\`vue
<!-- 自定义点击事件 -->
<erp-input-number 
  v-model="value" 
  simpleMode 
  @click="() => { 
    $message.success('添加成功!');
    openDialog();
  }" 
/>
\`\`\`

#### 不同尺寸
组件支持三种尺寸：
- small(小)
- default(默认)
- large(大)

\`\`\`vue
<erp-input-number v-model="value" size="small" />
<erp-input-number v-model="value" />
<erp-input-number v-model="value" size="large" />
\`\`\`

#### 按钮位置
支持两种控制按钮的位置：
- 默认(两侧)
- 右侧(controls-position="right")

\`\`\`vue
<erp-input-number v-model="value" />
<erp-input-number v-model="value" controls-position="right" />
\`\`\`
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      description: '组件主题类型',
      control: 'select',
      options: ['default', 'primary'],
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'default' },
        category: '主题'
      }
    },
    mode: {
      description: '使用模式：normal-普通模式，cart-购物车模式（允许减到0删除商品）',
      control: 'select',
      options: ['normal', 'cart'],
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'normal' },
        category: '模式'
      }
    },
    simpleMode: {
      description: '简化模式，仅显示加号图标按钮',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: '模式'
      }
    },
    size: {
      description: '组件尺寸',
      control: 'select',
      options: ['small', 'default', 'large'],
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'default' },
        category: '尺寸'
      }
    },
    min: {
      description: '允许的最小值',
      control: { type: 'number' },
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: '-Infinity' },
        category: '限制'
      }
    },
    max: {
      description: '允许的最大值',
      control: { type: 'number' },
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: 'Infinity' },
        category: '限制'
      }
    },
    step: {
      description: '步数',
      control: { type: 'number' },
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: '1' },
        category: '功能'
      }
    },
    controls: {
      description: '是否显示控制按钮',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
        category: '显示'
      }
    },
    controlsPosition: {
      description: '控制按钮的位置',
      control: 'select',
      options: ['', 'right'],
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '空字符串(两侧)' },
        category: '显示'
      }
    },
    disabled: {
      description: '是否禁用',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: '状态'
      }
    },
    stepStrictly: {
      description: '是否严格按照步数递增',
      control: 'boolean',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: '功能'
      }
    },
    precision: {
      description: '数值精度',
      control: { type: 'number' },
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: 'undefined' },
        category: '显示'
      }
    },
    modelValue: {
      description: '绑定值',
      control: { type: 'number' },
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: '0' },
        category: '数据'
      }
    }
  }
} as Meta;

export default meta;

// 基础用法
export const Basic: Story = {
  render: args => ({
    components: { ErpInputNumber, ElCard },
    setup() {
      const value = ref(1);
      return { args, value };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">基础用法</h3>
        <el-card shadow="never" class="p-4">
          <erp-input-number 
            v-model="value" 
            v-bind="args"
          />
          <div class="mt-2 text-sm text-gray-500">当前值: {{ value }}</div>
        </el-card>
      </div>
    `
  }),
  args: {
    type: 'default',
    size: 'default',
    min: 1,
    max: 10,
    step: 1,
    controls: true,
    disabled: false
  }
};

// 所有主题
export const AllThemes: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol },
    setup() {
      const values = {
        default: ref(1),
        primary: ref(1)
      };
      return { values };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">主题类型</h3>
        <el-card shadow="never" class="p-4">
          <el-row :gutter="20">
            <el-col :span="24" :md="12" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">Default (黑色)</div>
              <erp-input-number v-model="values.default" type="default" />
            </el-col>
            <el-col :span="24" :md="12" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">Primary (红色)</div>
              <erp-input-number v-model="values.primary" type="primary" />
            </el-col>
          </el-row>
        </el-card>
      </div>
    `
  })
};

// 不同尺寸
export const Sizes: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol },
    setup() {
      const valueSmall = ref(1);
      const valueDefault = ref(1);
      const valueLarge = ref(1);
      return { valueSmall, valueDefault, valueLarge };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">不同尺寸</h3>
        <el-card shadow="never" class="p-4 !w-[600px]">
          <el-row :gutter="20">
            <el-col :span="24" :md="8" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">小尺寸 (small)</div>
              <erp-input-number v-model="valueSmall" size="small" />
            </el-col>
            <el-col :span="24" :md="8" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">默认尺寸 (default)</div>
              <erp-input-number v-model="valueDefault" />
            </el-col>
            <el-col :span="24" :md="8" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">大尺寸 (large)</div>
              <erp-input-number v-model="valueLarge" size="large" />
            </el-col>
          </el-row>
        </el-card>
      </div>
    `
  })
};

// 控件位置
export const ControlsPosition: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol },
    setup() {
      const valueDefault = ref(1);
      const valueRight = ref(1);
      return { valueDefault, valueRight };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">控件位置</h3>
        <el-card shadow="never" class="p-4">
          <el-row :gutter="20">
            <el-col :span="24" :md="12" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">默认位置</div>
              <erp-input-number v-model="valueDefault" />
            </el-col>
            <el-col :span="24" :md="12" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">右侧位置</div>
              <erp-input-number v-model="valueRight" controls-position="right" />
            </el-col>
          </el-row>
        </el-card>
      </div>
    `
  })
};

// 禁用状态
export const Disabled: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol },
    setup() {
      const values = {
        default: ref(1),
        primary: ref(1)
      };
      return { values };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">禁用状态</h3>
        <el-card shadow="never" class="p-4">
          <el-row :gutter="20">
            <el-col :span="24" :md="6" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">默认主题</div>
              <erp-input-number v-model="values.default" type="default" disabled />
            </el-col>
            <el-col :span="24" :md="6" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">主要主题</div>
              <erp-input-number v-model="values.primary" type="primary" disabled />
            </el-col>
            <el-col :span="24" :md="6" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">简化模式 (默认)</div>
              <erp-input-number v-model="values.default" type="default" simpleMode disabled />
            </el-col>
            <el-col :span="24" :md="6" class="mb-4">
              <div class="text-sm text-gray-500 mb-1">简化模式 (主要)</div>
              <erp-input-number v-model="values.primary" type="primary" simpleMode disabled />
            </el-col>
          </el-row>
        </el-card>
      </div>
    `
  })
};

// 购物车示例
export const ShoppingCartExample: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol, ElDivider },
    setup() {
      const items = [
        { id: 1, name: '商品 A', price: 35.0, quantity: ref(1), type: 'default' },
        { id: 2, name: '商品 B', price: 16.0, quantity: ref(2), type: 'primary' },
        { id: 3, name: '商品 C', price: 15.0, quantity: ref(0), type: 'default', simpleMode: true }
      ];

      const calcTotal = () => {
        return items.reduce((total, item) => total + item.price * item.quantity.value, 0).toFixed(2);
      };

      const handleQuantityChange = (item: any, newValue: number) => {
        if (newValue === 0) {
          console.log(`删除商品: ${item.name}`);
          // 在实际应用中，这里会从购物车中移除商品
        }
      };

      return { items, calcTotal, handleQuantityChange };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">购物车示例（支持减到0删除）</h3>
        <el-card shadow="never" class="p-4 !w-[600px]">
          <div class="bg-gray-100 p-4 rounded-lg">
            <div v-for="item in items" :key="item.id" class="flex justify-between items-center mb-3 pb-3 border-b border-gray-200 last:border-0 last:mb-0 last:pb-0">
              <div>
                <div class="font-medium">{{ item.name }}</div>
                <div class="text-red-500">¥{{ item.price.toFixed(2) }}</div>
              </div>
              <erp-input-number 
                v-model="item.quantity.value" 
                :type="item.type" 
                mode="cart"
                :max="10"
                :simpleMode="item.simpleMode"
                @change="handleQuantityChange(item, $event)"
              />
            </div>
            <el-divider />
            <div class="flex justify-between items-center">
              <div class="font-medium">总计:</div>
              <div class="text-lg font-bold text-red-500">¥{{ calcTotal() }}</div>
            </div>
            <div class="text-xs text-gray-500 mt-2">提示：将数量减到0可以删除商品</div>
          </div>
        </el-card>
      </div>
    `
  })
};

// 简化模式示例
export const SimpleModeExamples: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol },
    setup() {
      const value = ref(0);

      const handleClick = () => {
        console.log('按钮被点击');
      };

      const showMessage = () => {
        alert('添加商品成功!');
      };

      return { value, handleClick, showMessage };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">简化模式示例</h3>
        <el-card shadow="never" class="p-4">
          <p class="text-sm text-gray-500 mb-4">简化模式下只显示一个加号按钮，点击时会增加值，同时支持自定义点击事件。这种模式特别适合用在表格、列表等需要简洁UI的场景。</p>
          
          <el-row :gutter="20">
            <el-col :span="24" :md="8" class="mb-4">
              <div class="text-sm font-medium mb-2">默认主题</div>
              <div class="flex items-center">
                <erp-input-number v-model="value" type="default" simpleMode />
                <span class="ml-2">当前值: {{ value }}</span>
              </div>
            </el-col>
            <el-col :span="24" :md="8" class="mb-4">
              <div class="text-sm font-medium mb-2">主要主题</div>
              <div class="flex items-center">
                <erp-input-number v-model="value" type="primary" simpleMode />
                <span class="ml-2">当前值: {{ value }}</span>
              </div>
            </el-col>
            <el-col :span="24" :md="8" class="mb-4">
              <div class="text-sm font-medium mb-2">自定义点击事件</div>
              <div class="flex items-center">
                <erp-input-number v-model="value" type="primary" simpleMode @click="showMessage" />
                <span class="ml-2">点击弹出提示框</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    `
  })
};

// 模式对比示例
export const ModeComparison: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol, ElDivider },
    setup() {
      const normalValue = ref(1);
      const cartValue = ref(1);
      return { normalValue, cartValue };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">模式对比</h3>
        <el-card shadow="never" class="p-4 !w-[600px]">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="text-sm font-medium mb-2">普通模式 (normal)</div>
              <div class="flex items-center mb-2">
                <erp-input-number v-model="normalValue" mode="normal" :min="1" />
                <span class="ml-2">当前值: {{ normalValue }}</span>
              </div>
              <div class="text-xs text-gray-500">最小值为1，不能减到0</div>
            </el-col>
            <el-col :span="12">
              <div class="text-sm font-medium mb-2">购物车模式 (cart)</div>
              <div class="flex items-center mb-2">
                <erp-input-number v-model="cartValue" mode="cart" />
                <span class="ml-2">当前值: {{ cartValue }}</span>
              </div>
              <div class="text-xs text-gray-500">可以减到0，用于删除商品</div>
            </el-col>
          </el-row>
          
          <el-divider />
          
          <div class="text-sm text-gray-600">
            <p class="mb-2"><strong>使用场景：</strong></p>
            <ul class="list-disc list-inside space-y-1">
              <li><strong>normal模式</strong>：适用于一般的数量输入，如库存设置、参数配置等</li>
              <li><strong>cart模式</strong>：适用于购物车场景，允许通过减到0来删除商品</li>
            </ul>
          </div>
        </el-card>
      </div>
    `
  })
};

// 设计规范示例
export const DesignSpecExample: Story = {
  render: () => ({
    components: { ErpInputNumber, ElCard, ElSpace, ElRow, ElCol, ElDivider },
    setup() {
      const value = ref(1);
      return { value };
    },
    template: `
      <div class="p-4">
        <h3 class="font-medium mb-2">设计规范示例</h3>
        <el-card shadow="never" class="p-4 !w-[600px]">
          <p class="text-sm text-gray-500 mb-4">此组件按照最新设计规范实现，具有以下视觉特点：</p>
          
          <div class="bg-gray-50 p-6 rounded-lg mb-4">
            <div class="flex flex-col items-center">
              <erp-input-number v-model="value" type="default" />
              
              <el-divider />
              
              <div class="grid grid-cols-2 gap-4 w-full text-sm">
                <div class="text-gray-500">整体宽度</div>
                <div class="font-medium">100px</div>
                
                <div class="text-gray-500">整体高度</div>
                <div class="font-medium">44px</div>
                
                <div class="text-gray-500">边框</div>
                <div class="font-medium">1px dashed rgba(0, 0, 0, 0.1)</div>
                
                <div class="text-gray-500">边框圆角</div>
                <div class="font-medium">border-radius: 200px (圆形)</div>
                
                <div class="text-gray-500">图标颜色</div>
                <div class="font-medium">黑色 (#000000)</div>
                
                <div class="text-gray-500">内部字体</div>
                <div class="font-medium">16px 600 weight</div>
                
                <div class="text-gray-500">内边距</div>
                <div class="font-medium">6px</div>
              </div>
            </div>
          </div>
          
          <div class="text-xs text-gray-400">注：组件尺寸和样式严格遵循设计规范，确保在不同场景下视觉统一。</div>
        </el-card>
      </div>
    `
  })
};
