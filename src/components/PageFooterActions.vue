<template>
  <div class="absolute z-[10] bottom-0 left-0 right-0 py-3 px-6 flex justify-end items-center h-[100px] shrink-0 bg-[#FFF] border-t border-[#E5E5E5]">
    <!-- 右侧操作按钮 -->
    <div class="flex gap-4 justify-end">
      <slot>
        <!-- 默认按钮通过插槽传入 -->
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
// 无需任何props和方法，纯UI组件
</script>

<style scoped>
/* 样式可以根据需要调整 */
</style>
