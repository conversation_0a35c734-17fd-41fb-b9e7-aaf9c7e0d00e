<template>
  <div class="order-button-container items-center" :class="{ 'w-full': fullWidth }">
    <!-- 两按钮布局 -->
    <button class="price-button" :class="[primaryButtonClass]">
      <div class="price-content">
        <div class="price-label">{{ label }}:</div>
        <div class="price-amount">
          <PriceDisplay :amount-in-fen="amount" class="price-display-large" />
        </div>
      </div>
    </button>
    <button class="primary-button" :class="[primaryButtonClass]" @click="handlePrimaryClick" :disabled="props.disabled">
      {{ primaryText }}
    </button>
    <button v-if="secondaryText" class="secondary-button" :class="[secondaryButtonClass]" @click="handleSecondaryClick" :disabled="props.disabled">
      {{ secondaryText }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import PriceDisplay from '../customer/PriceDisplay.vue';

interface Props {
  // 金额，单位为分
  amount?: number;
  // 标签文本，例如"待结总计"、"预估总计"等
  label?: string;
  // 主按钮文本
  primaryText: string;
  // 次要按钮文本，不传则不显示
  secondaryText?: string;
  // 是否全宽显示
  fullWidth?: boolean;
  // 主按钮自定义类名
  primaryButtonClass?: string;
  // 次要按钮自定义类名
  secondaryButtonClass?: string;
  // 主、次按钮统一禁用状态控制
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  amount: 0,
  label: '预估总计',
  primaryText: '立即下单',
  fullWidth: true,
  primaryButtonClass: '',
  secondaryButtonClass: '',
  disabled: false
});

const emit = defineEmits<{
  (e: 'primaryClick'): void;
  (e: 'secondaryClick'): void;
}>();

const handlePrimaryClick = () => {
  if (props.disabled) return;
  emit('primaryClick');
};

const handleSecondaryClick = () => {
  if (props.disabled) return;
  emit('secondaryClick');
};

const formattedPrice = computed(() => Math.floor(props.amount / 100).toString());
</script>

<style scoped>
.order-button-container {
  display: flex;
  height: 110px;
}

.order-button-container.full-width {
  width: 100%;
}

.price-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  flex: 1;
  height: 100%;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 16px;
}

.price-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.price-label {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 500;
  text-align: left;
}

.price-amount {
  display: flex;
  align-items: baseline;
  margin-top: 4px;
}

.price-integer {
  font-size: 42px;
  font-weight: bold;
  line-height: 1;
}

.price-decimal {
  font-size: 16px;
  opacity: 0.8;
  margin-left: 2px;
}

.action-text {
  font-size: 24px;
  font-weight: 500;
}

.primary-button {
  width: 152px;
  height: 72px;
  background-color: #e23939;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  color: white;
  border: none;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primary-button:hover {
  background-color: #c23333;
}

.secondary-button {
  width: 152px;
  height: 72px;
  background-color: white;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  color: #333;
  border: none;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

/* 悬停效果 */
.price-button:hover,
.secondary-button:hover {
  opacity: 0.9;
}

/* 禁用状态样式 */
.primary-button:disabled,
.secondary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.primary-button:disabled:hover,
.secondary-button:disabled:hover {
  opacity: 0.5;
  background-color: inherit;
}

/* 无加载样式 */
</style>
