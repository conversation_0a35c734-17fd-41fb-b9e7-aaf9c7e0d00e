<template>
  <!-- 此组件不需要渲染任何内容，只负责初始化工具栏 -->
  <div class="stagewise-toolbar-container"></div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 记录工具栏是否已初始化
const initialized = ref(false);

// 判断当前是否为开发环境
const isDevelopment = import.meta.env.MODE === 'development';

// 仅在客户端和开发环境中动态导入和初始化工具栏
if (isDevelopment && typeof window !== 'undefined') {
  // 使用动态导入确保SSR兼容性
  import('@stagewise/toolbar')
    .then(({ initToolbar }) => {
      if (!initialized.value) {
        console.log('[Stagewise] 正在初始化开发工具栏...');
        initToolbar({
          plugins: []
        });
        initialized.value = true;
      }
    })
    .catch(error => {
      console.error('[Stagewise] 工具栏初始化失败:', error);
    });
}
</script>

<style scoped>
.stagewise-toolbar-container {
  display: none;
}
</style>
