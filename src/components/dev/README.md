# 开发工具组件

## Stagewise 工具栏

Stagewise 是一个浏览器工具栏，可以连接前端 UI 到代码 AI 代理。它允许开发者在网页应用中选择元素、留下评论，并让 AI 代理根据这些上下文信息进行代码修改。

### 功能特点

- 允许在浏览器中直接选择 UI 元素
- 能够对 UI 元素添加注释和修改建议
- 将这些信息传递给代码编辑器中的 AI 代理
- 仅在开发模式下可用，生产环境不会包含

### 集成方式

本项目已集成 `@stagewise/toolbar-vue` 和 `@stagewise/toolbar` 包。相关文件位于：

- `/src/components/dev/StagewiseToolbar.vue` - 工具栏组件
- 该组件已在 `App.vue` 中导入和使用，但仅在开发环境中渲染

### 技术细节

- 工具栏使用动态导入确保不影响生产构建
- 通过检查 `import.meta.env.MODE === 'development'` 确保仅在开发环境中启用
- 配置了空插件数组，可根据需要添加自定义插件

### 使用方法

开发者无需做任何特殊操作，只要在开发环境中运行项目，工具栏将自动加载。

```bash
# 启动开发服务器
pnpm dev
```

工具栏将出现在浏览器界面中，并允许开发者选择和注释 UI 元素。

### 注意事项

- 此工具仅用于开发目的，不会包含在生产构建中
- 如果遇到任何与工具栏相关的问题，请检查控制台日志中的错误信息
