<template>
  <div
    :class="[
      'erp-input-number',
      `erp-input-number--${type}`,
      size ? `erp-input-number--${size}` : '',
      disabled ? 'is-disabled' : '',
      simpleMode ? 'erp-input-number--simple' : '',
      !simpleMode && innerValue === 0 && !hasClicked && safeMin === 0 && mode !== 'cart' ? 'erp-input-number--zero-state' : ''
    ]">
    <!-- 简化模式：仅显示加号图标 -->
    <div v-if="props.simpleMode" class="erp-input-number-simple" @click="handleSimpleClick">
      <div class="icon-container">
        <IconPlusCircle />
      </div>
    </div>

    <!-- 零值状态：用于非简化模式但值为0且最小值为0且非购物车模式时，显示类似简化模式的UI -->
    <div
      v-else-if="!props.simpleMode && innerValue === 0 && !hasClicked && safeMin === 0 && props.mode !== 'cart'"
      class="erp-input-number-zero-state"
      @click="handleZeroStateClick">
      <div class="icon-container">
        <IconPlusCircle />
      </div>
    </div>

    <!-- 普通模式：完整的数字输入框 -->
    <el-input-number
      v-else
      v-model="innerValue"
      :min="safeMin"
      :max="max"
      :step="actualStep"
      :step-strictly="stepStrictly"
      :precision="actualPrecision"
      :size="size"
      :disabled="disabled"
      :controls="controls"
      :controls-position="controlsPosition"
      :name="name"
      :label="label"
      :placeholder="placeholder"
      :id="id"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur">
      <template #decrease-icon>
        <div class="icon-container" :class="{ 'is-button-disabled': isAtMinValue }">
          <IconMinus />
        </div>
      </template>
      <template #increase-icon>
        <div class="icon-container" :class="{ 'is-button-disabled': isAtMaxValue }">
          <IconPlus />
        </div>
      </template>
    </el-input-number>
  </div>
</template>

<script setup lang="ts">
/**
 * ErpInputNumber 组件
 *
 * 样式说明：
 * 1. 基础样式: 所有通用样式（按钮形状、居中文本等）已经在全局样式文件 src/style/element-plus/index.scss 中定义
 * 2. 主题样式: 仅支持default（黑色）和primary（红色）两种主题
 * 3. 自定义图标: 使用自定义的IconPlus和IconMinus组件替换默认图标
 * 4. 简化模式: 当设置为true时，组件仅显示一个加号图标，功能类似普通按钮
 * 5. 小数支持: 通过enableDecimal开启小数输入支持，并可通过precision设置小数位数，默认1位小数，step默认为0.1
 * 6. 零值状态: 当非简化模式但值为0时，自动显示只有加号的UI，点击后才显示完整输入框
 * 7. 使用模式:
 *    - normal: 普通模式，遵循设置的min值限制
 *    - cart: 购物车模式，允许减到0（用于删除商品），不显示零值状态
 *
 * 使用此组件时，不需要关心样式细节，只需通过 type 属性选择合适的主题即可。
 */
import { ref, watch, computed } from 'vue';
import { ElInputNumber } from 'element-plus';
import IconPlus from './IconInputPlus.vue';
import IconPlusCircle from './IconInputPlusCircle.vue';
import IconMinus from './IconInputMinus.vue';

type InputNumberSize = 'large' | 'default' | 'small';
type InputNumberControlsPosition = '' | 'right';
type ThemeType = 'default' | 'primary';

const props = defineProps({
  // 值和v-model绑定
  modelValue: {
    type: Number,
    default: 0
  },
  // 主题类型
  type: {
    type: String as () => ThemeType,
    default: 'default',
    validator: (val: string) => ['default', 'primary'].includes(val)
  },
  // 使用模式 - 用于区分不同的使用场景
  mode: {
    type: String as () => 'normal' | 'cart',
    default: 'normal',
    validator: (val: string) => ['normal', 'cart'].includes(val)
  },
  // 简化模式 - 仅显示加号图标，功能类似普通按钮
  simpleMode: {
    type: Boolean,
    default: false
  },
  // 是否启用小数输入
  enableDecimal: {
    type: Boolean,
    default: false
  },
  // 临时开关 - 强制禁用小数支持
  forceDisableDecimal: {
    type: Boolean,
    default: true
  },
  // Element Plus原有属性
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: Infinity
  },
  step: {
    type: Number,
    default: 1
  },
  stepStrictly: {
    type: Boolean,
    default: false
  },
  precision: {
    type: Number,
    default: undefined
  },
  size: {
    type: String as () => InputNumberSize,
    default: 'default',
    validator: (val: string) => ['large', 'default', 'small'].includes(val)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  controls: {
    type: Boolean,
    default: true
  },
  controlsPosition: {
    type: String as () => InputNumberControlsPosition,
    default: '',
    validator: (val: string) => ['', 'right'].includes(val)
  },
  name: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: undefined
  }
});

const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur', 'click', 'delete-item']);

// 内部值
const innerValue = ref(props.modelValue);

// 跟踪用户是否已点击过零值状态
const hasClicked = ref(false);

// 确保min值合法的计算属性
const safeMin = computed(() => {
  // 在购物车模式下，允许减到0（用于删除）
  if (props.mode === 'cart') {
    return 0;
  }
  return Math.min(props.min, props.max);
});

// 计算是否在最小值
const isAtMinValue = computed(() => {
  return innerValue.value <= safeMin.value;
});

// 计算是否在最大值
const isAtMaxValue = computed(() => {
  return innerValue.value >= props.max;
});

// 计算实际的精度
const actualPrecision = computed(() => {
  // 如果临时开关开启，则强制禁用小数
  if (props.forceDisableDecimal) {
    return 0;
  }
  // 如果启用小数且设置了精度，使用设置的精度
  if (props.enableDecimal && props.precision !== undefined) {
    return props.precision;
  }
  // 如果启用小数但没设置精度，默认为1位小数
  else if (props.enableDecimal) {
    return 1;
  }
  // 如果未启用小数，则精度为0
  return 0;
});

// 计算实际的step值
const actualStep = computed(() => {
  // 如果临时开关开启，则强制使用整数步长
  if (props.forceDisableDecimal) {
    return Math.max(1, Math.round(props.step));
  }
  // 如果启用小数且没有明确设置step，默认为0.1
  if (props.enableDecimal && props.step === 1) {
    return 0.1;
  }
  // 否则使用设置的step值
  return props.step;
});

// 监听外部值变化
watch(
  () => props.modelValue,
  val => {
    innerValue.value = val;
    // 当值变为0且最小值为0且非购物车模式时，重置hasClicked标志
    if (val === 0 && !props.simpleMode && safeMin.value === 0 && props.mode !== 'cart') {
      hasClicked.value = false;
    }
  }
);

// 监听内部值变化
watch(
  () => innerValue.value,
  (val, oldVal) => {
    // 在购物车模式下，如果值将要变为0，直接触发删除事件而不更新数值
    if (props.mode === 'cart' && val === 0 && oldVal > 0) {
      console.log('🛒 购物车模式：检测到数量变为0，触发删除事件');
      // 恢复到原来的值，避免UI显示0
      innerValue.value = oldVal;
      // 触发删除事件，让父组件处理删除逻辑
      emit('delete-item');
      return;
    }

    emit('update:modelValue', val);
    // 当值变为非0且非购物车模式时，设置hasClicked标志
    if (val !== 0 && !props.simpleMode && safeMin.value === 0 && props.mode !== 'cart') {
      hasClicked.value = true;
    }
  }
);

// 处理事件
const handleChange = (val: number | undefined) => {
  emit('change', val);
};

const handleFocus = (event: FocusEvent) => {
  emit('focus', event);
};

const handleBlur = (event: FocusEvent) => {
  emit('blur', event);
};

// 简化模式下的点击处理
const handleSimpleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    // 简化模式下点击时增加值
    const newValue = (props.modelValue || 0) + actualStep.value;
    // 检查是否超出最大值
    if (newValue <= props.max) {
      // 根据是否强制禁用小数来格式化值
      let formattedValue;
      if (props.forceDisableDecimal) {
        formattedValue = Math.floor(newValue);
      } else {
        formattedValue = props.enableDecimal ? Number(newValue.toFixed(actualPrecision.value)) : Math.floor(newValue);
      }

      emit('update:modelValue', formattedValue);
      emit('change', formattedValue);
    }
    // 触发点击事件
    emit('click', event);
  }
};

// 零值状态下的点击处理（非simpleMode但值为0）
const handleZeroStateClick = (event: MouseEvent) => {
  if (!props.disabled) {
    // 设置hasClicked为true，以便显示完整输入框
    hasClicked.value = true;

    // 行为与简化模式相同，点击时增加值
    const newValue = actualStep.value;

    // 检查是否超出最大值
    if (newValue <= props.max) {
      // 根据是否强制禁用小数来格式化值
      let formattedValue;
      if (props.forceDisableDecimal) {
        formattedValue = Math.floor(newValue);
      } else {
        formattedValue = props.enableDecimal ? Number(newValue.toFixed(actualPrecision.value)) : Math.floor(newValue);
      }

      emit('update:modelValue', formattedValue);
      emit('change', formattedValue);
    }

    // 触发点击事件
    emit('click', event);
  }
};
</script>

<style lang="scss" scoped>
.erp-input-number {
  display: inline-flex;
  align-items: center;

  /* 默认状态 - 黑色按钮 */
  &--default {
    :deep(.el-input-number .el-input-number__decrease),
    :deep(.el-input-number .el-input-number__increase),
    .erp-input-number-simple .icon-container,
    .erp-input-number-zero-state .icon-container {
      background-color: transparent !important;
      color: #000000 !important;
      /* 黑色 - 设置SVG图标颜色 */
    }
  }

  /* Primary主题 - 红色按钮 */
  &--primary {
    :deep(.el-input-number .el-input-number__decrease),
    :deep(.el-input-number .el-input-number__increase),
    .erp-input-number-simple .icon-container,
    .erp-input-number-zero-state .icon-container {
      background-color: transparent !important;
      color: #e23939 !important;
      /* 红色 - 设置SVG图标颜色 */
    }
  }

  /* 禁用状态 */
  &.is-disabled {
    :deep(.el-input-number .el-input-number__decrease),
    :deep(.el-input-number .el-input-number__increase),
    .erp-input-number-simple .icon-container,
    .erp-input-number-zero-state .icon-container {
      opacity: 0.2 !important;
    }

    .erp-input-number-simple,
    .erp-input-number-zero-state {
      cursor: not-allowed !important;
    }
  }

  /* 按钮禁用状态 */
  :deep(.is-button-disabled) {
    opacity: 0.2 !important; /* 调整为设计规范中的0.2透明度 */
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  /* 简化模式样式 */
  &--simple {
    width: 40px;
    height: 40px;
  }

  /* 零值状态样式（非简化模式但值为0） */
  &--zero-state {
    width: 40px;
    height: 40px;
  }

  /* 简化模式下的样式 */
  .erp-input-number-simple {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s;

    &:hover {
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  /* 零值状态下的样式（和简化模式样式相同） */
  .erp-input-number-zero-state {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    background-color: #ffffff !important;

    &:hover {
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  /* 图标容器 */
  .icon-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    width: 100% !important;
    position: relative !important;
    line-height: 0 !important;
  }

  /* 按照MasterGo设计规范调整的样式 */
  :deep(.el-input-number) {
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto !important;
    align-items: center !important;
    border-radius: 200px !important; /* 圆形边框 */
    padding: 0 6px !important;
    width: 100px !important; /* 设计规定的宽度 */
    height: 44px !important; /* 设计规定的高度 */
    border: 1px solid rgba(0, 0, 0, 0.1) !important; /* 线边框 */
    background-color: #ffffff !important; /* 白色背景 */

    /* 重置所有可能影响布局的元素样式 */
    * {
      box-sizing: border-box !important;
    }

    /* 设置减号按钮在左侧固定位置 */
    .el-input-number__decrease {
      position: absolute !important;
      left: 6px !important; /* 设计中指定的6px */
      top: 50% !important; /* 改为相对于父元素50%的位置 */
      transform: translateY(-50%) !important; /* 使用transform将元素向上偏移自身高度的50% */
      margin: 0 !important;
      z-index: 1 !important;
    }

    /* 设置加号按钮在右侧固定位置 */
    .el-input-number__increase {
      position: absolute !important;
      right: 6px !important; /* 确保与左侧对称 */
      top: 50% !important; /* 改为相对于父元素50%的位置 */
      transform: translateY(-50%) !important; /* 使用transform将元素向上偏移自身高度的50% */
      margin: 0 !important;
      z-index: 1 !important;
    }

    /* 控制按钮样式 */
    .el-input-number__decrease,
    .el-input-number__increase {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      visibility: visible !important;
      opacity: 1 !important;
      height: 28px !important; /* 按设计指定的高度 */
      width: 28px !important; /* 按设计指定的宽度 */
      padding: 0 !important;
      background-color: transparent !important;
      border: none !important;
    }

    /* 设置中间数字框的样式 */
    .el-input {
      width: 32px !important; /* 按设计指定的宽度32px */
      flex: 0 0 32px !important; /* 固定宽度不拉伸 */
      height: 28px !important; /* 按设计指定的高度 */
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      position: relative !important;
      z-index: 0 !important;
      margin: 0 auto !important; /* 确保居中 */
    }

    .el-input__wrapper {
      background-color: transparent !important;
      box-shadow: none !important;
      padding: 0 !important;
      height: 28px !important;
      line-height: 28px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .el-input__inner {
      text-align: center !important;
      font-size: 16px !important;
      font-weight: 600 !important; /* MiSans-Semibold */
      height: 28px !important;
      line-height: 28px !important;
      padding: 0 !important;
      color: #000000 !important;
      width: 100% !important;
      min-width: 24px !important;
    }

    /* 优化按钮内的图标容器 */
    .icon-container {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      height: 100% !important;
      width: 100% !important;

      /* 确保SVG图标居中 */
      svg {
        display: block !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 28px !important;
        height: 28px !important;
      }
    }

    /* 为不同尺寸下的展示做特殊处理 */
    &.el-input-number--small {
      height: 36px !important;
      width: 90px !important;

      .el-input-number__decrease {
        top: 50% !important; /* 改为居中定位 */
        transform: translateY(-50%) !important;
        height: 24px !important;
        width: 24px !important;
      }

      .el-input-number__increase {
        top: 50% !important; /* 改为居中定位 */
        transform: translateY(-50%) !important;
        height: 24px !important;
        width: 24px !important;
      }

      .el-input {
        width: 28px !important;
        flex: 0 0 28px !important;
        height: 24px !important;
      }

      .el-input__inner {
        font-size: 14px !important;
        height: 24px !important;
        line-height: 24px !important;
      }

      .icon-container svg {
        display: block !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 28px !important;
        height: 28px !important;
      }
    }

    &.el-input-number--large {
      height: 52px !important;
      width: 120px !important;

      .el-input-number__decrease {
        top: 50% !important; /* 改为居中定位 */
        transform: translateY(-50%) !important;
        height: 32px !important;
        width: 32px !important;
      }

      .el-input-number__increase {
        top: 50% !important; /* 改为居中定位 */
        transform: translateY(-50%) !important;
        height: 32px !important;
        width: 32px !important;
      }

      .el-input {
        width: 36px !important;
        flex: 0 0 36px !important;
        height: 32px !important;
      }

      .el-input__inner {
        font-size: 18px !important;
        height: 32px !important;
        line-height: 32px !important;
      }

      .icon-container svg {
        display: block !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 28px !important;
        height: 28px !important;
      }
    }

    /* 覆盖默认的Element Plus样式 */
    &.is-without-controls .el-input__inner {
      text-align: center !important;
      padding: 0 !important;
    }

    &.is-controls-right {
      .el-input__wrapper {
        padding: 0 !important;
      }

      .el-input-number__increase {
        top: 25% !important; /* 调整为父元素的25%位置 */
        transform: translateY(-50%) !important;
        right: 8px !important;
        height: 16px !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
      }

      .el-input-number__decrease {
        top: 75% !important; /* 调整为父元素的75%位置 */
        transform: translateY(-50%) !important;
        right: 8px !important;
        height: 16px !important;
      }
    }
  }
}
</style>
