<template>
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style="min-width: 28px; min-height: 28px; max-width: 28px; max-height: 28px; display: block">
    <circle cx="14" cy="14" r="10" fill="currentColor" />
    <path d="M18 14H10" stroke="white" stroke-width="2" stroke-linecap="round" />
    <path d="M14 10L14 18" stroke="white" stroke-width="2" stroke-linecap="round" />
  </svg>
</template>

<script setup lang="ts">
// 不需要处理颜色逻辑，由外部控制
</script>
