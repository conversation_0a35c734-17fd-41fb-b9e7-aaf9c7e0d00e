<template>
  <div class="sidebar-container h-screen flex flex-col items-center py-4 w-[140px] bg-white border-r-2 border-gray-100" role="navigation" aria-label="主导航">
    <!-- Logo -->
    <div class="mb-6">
      <slot name="logo">
        <div class="p-2 rounded">
          <img :src="logoIcon" alt="Logo" class="w-[60px] h-[60px]" />
        </div>
      </slot>
    </div>

    <!-- Menu Items -->
    <div class="flex flex-col items-center flex-grow space-y-2">
      <!-- 菜单项 -->
      <div
        v-for="item in menuItems"
        :key="item.path"
        :class="['sidebar-item flex flex-col items-center justify-center mb-2 w-[100px]', isActive(item.path) ? 'active-item' : '']"
        @click="handleMenuClick(item.path)"
        role="menuitem"
        :aria-current="isActive(item.path) ? 'page' : undefined"
        :title="item.title">
        <div class="mb-1 flex items-center justify-center">
          <component
            :is="item.icon"
            class="w-[40px] h-[40px]"
            :color="isActive(item.path) ? '#E23939' : '#00000099'"
            :fillColor="isActive(item.path) ? '#E23939' : '#00000099'"
            :strokeColor="isActive(item.path) ? '#E23939' : '#00000099'"
            style="font-size: 28px"
            aria-hidden="true" />
        </div>
        <span class="text-[16px] font-medium" :class="{ 'text-[#E23939]': isActive(item.path), 'text-[#00000099]': !isActive(item.path) }">{{
          item.title
        }}</span>
      </div>
    </div>

    <!-- Bottom Icons -->
    <div class="mt-auto flex flex-col items-center space-y-4 mb-4 w-full" role="menu" aria-label="用户操作">
      <slot name="user-info">
        <!-- 通知图标 -->
        <NotificationPopover
          v-model="popoverVisible"
          :notifications="popoverNotifications"
          title="呼叫通知"
          :closeOnClickOutside="true"
          @item-click="handlePopoverItemClick"
          @view-all="goToNotificationList"
          @hide="onPopoverHide"
          @show="onPopoverShow">
          <template #reference>
            <div
              class="cursor-pointer sidebar-icon relative"
              :class="{ 'active-icon': isActive('/notifications') || popoverVisible }"
              @click="handleNotifyClick"
              role="menuitem"
              aria-label="呼叫通知"
              :aria-current="isActive('/notifications') ? 'page' : undefined"
              title="呼叫通知">
              <el-badge :value="unreadNotifyCount" :max="99" :hidden="unreadNotifyCount === 0" class="item">
                <NotifyIcon class="text-[28px]" :color="isActive('/notifications') || popoverVisible ? '#E23939' : '#00000099'" />
              </el-badge>
            </div>
          </template>
        </NotificationPopover>

        <!-- 设置图标 -->
        <div
          class="cursor-pointer sidebar-icon"
          :class="{ 'active-icon': isActive('/settings') }"
          @click="handleMenuClick('/settings')"
          role="menuitem"
          aria-label="设置"
          :aria-current="isActive('/settings') ? 'page' : undefined">
          <SettingIcon class="text-[24px]" :color="isActive('/settings') ? '#E23939' : '#00000099'" />
        </div>

        <!-- 用户头像 Popover -->
        <el-popover
          :visible="userPopoverVisible"
          placement="right-end"
          :width="200"
          trigger="click"
          :offset="10"
          popper-class="user-avatar-popover"
          @update:visible="userPopoverVisible = $event">
          <template #reference>
            <div
              class="cursor-pointer sidebar-avatar flex items-center justify-center flex-col"
              :class="{ 'active-icon': isActive('/user') || userPopoverVisible }"
              role="menuitem"
              aria-label="用户信息"
              :aria-current="isActive('/user') ? 'page' : undefined">
              <div class="avatar-container">
                <img :src="currentUserAvatar" alt="用户头像" class="avatar-image" />
              </div>
              <div v-if="userName" class="user-popover-venue">{{ userName }}</div>
            </div>
          </template>

          <!-- Popover 内容 -->
          <div class="user-popover-content">
            <!-- 用户信息头部 -->
            <div class="user-popover-header">
              <div class="user-popover-avatar">
                <img :src="currentUserAvatar" alt="用户头像" class="popover-avatar-image" />
              </div>
              <div class="user-popover-info">
                <div class="user-popover-name">{{ userName }}</div>
                <div v-if="venueName" class="line-clamp-2">{{ venueName }}</div>
              </div>
            </div>

            <div class="user-popover-divider"></div>

            <!-- 退出登录 -->
            <div class="user-popover-item logout" @click="handleLogout">
              <el-icon>
                <SwitchButton />
              </el-icon>
              <span>退出登录</span>
            </div>
          </div>
        </el-popover>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { MenuItem } from '@/utils/constant/menuConfig';
import { useNotificationStore } from '@/modules/notification/stores/notificationStore';
import { useUserStore } from '@/stores/userStore';
import { useVenueStore } from '@/stores/venueStore';
import type { RoomNotifyPayload } from '@/modules/notification/types/notification';
import NotifyIcon from '@/assets/v3/notify.vue';
import SettingIcon from '@/assets/v3/settings.vue';
import { SwitchButton } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
// @ts-ignore - 组件使用 script setup 自动导出
import NotificationPopover from '@/components/Notification/NotificationPopover.vue';

// 导入图标
import logoIcon from '@/assets/logo.svg';
import userAvatar from '@/assets/v2/login/avater.png';

// 初始化stores
const notificationStore = useNotificationStore();
const userStore = useUserStore();
const venueStore = useVenueStore();
const popoverVisible = ref(false);
const userPopoverVisible = ref(false);

// 获取未读通知数量和Popover显示的通知
const unreadNotifyCount = computed(() => notificationStore.unreadCount);
const popoverNotifications = computed(() => notificationStore.popoverNotifications);

// 计算当前用户头像 - 优先使用 userStore 中的头像，没有则使用默认头像
const currentUserAvatar = computed(() => {
  return userStore.userInfo.avatar || props.userAvatar || userAvatar;
});

// 用户信息
const userName = computed(() => userStore.userInfo?.name || '未登录');
const venueName = computed(() => venueStore.venue?.name || '');

// 监听通知内容更新
watch(
  popoverNotifications,
  () => {
    // 只要有未读通知，总是显示弹窗 (呼叫按钮高优先级)
    if (unreadNotifyCount.value > 0) {
      popoverVisible.value = true;
      console.log('[Notification] 通知内容更新，存在未读通知，显示弹窗');
    }
  },
  { deep: true }
);

// 初始化检查通知
nextTick(async () => {
  // 先获取通知数据
  const hasUnreadNotifications = await notificationStore.fetchAndUpdateUnreadNotifications();
  // 如果有未读通知，显示弹窗
  if (hasUnreadNotifications && !popoverVisible.value) {
    console.log('[Notification] 初始化检查发现未读通知:', unreadNotifyCount.value);
    popoverVisible.value = true;
    console.log('[Notification] 初始化后设置popover显示，当前值:', popoverVisible.value);
  }
});

// 定义组件属性
// @ts-ignore - Vue宏, linter无法识别
const props = defineProps({
  // Logo URL
  logoUrl: {
    type: String,
    default: ''
  },
  // 菜单项
  menuItems: {
    type: Array as () => MenuItem[],
    required: true
  },
  // 用户头像
  userAvatar: {
    type: String,
    default: userAvatar
  },
  // 用户名
  userName: {
    type: String,
    default: '未登录'
  },
  // 当前活动路径
  activePath: {
    type: String,
    default: ''
  }
});

// 判断是否为外部头像URL
const isExternalAvatar = computed(() => {
  return props.userAvatar !== userAvatar;
});

// 事件
// @ts-ignore - Vue宏, linter无法识别
const emit = defineEmits(['menu-click', 'update:activePath', 'avatar-click']);

const route = useRoute();
const router = useRouter();

// 判断菜单项是否激活
const isActive = (path: string) => {
  if (props.activePath) {
    return props.activePath === path;
  }

  // 如果没有提供 activePath，则根据当前路由判断
  const currentPath = route.path;
  const firstLevelPath = '/' + currentPath.split('/')[1];
  return path === firstLevelPath;
};

// 处理菜单点击
const handleMenuClick = (path: string) => {
  emit('menu-click', path);

  // 更新活动路径
  emit('update:activePath', path);

  // 导航到路由，不再检查路由是否存在
  router.push(path).catch(err => {
    console.warn(`导航到 ${path} 失败:`, err);
  });
};

// 处理用户头像点击
const handleUserAvatarClick = (event: MouseEvent) => {
  // 发出头像点击事件
  emit('avatar-click', event);
};

// 处理退出登录
const handleLogout = async () => {
  try {
    await userStore.logoutAction();
    userPopoverVisible.value = false;
    router.push('/login');
  } catch (error) {
    console.error('退出登录失败:', error);
  }
};

// 处理通知图标点击 - 直接跳转到通知列表页面
const handleNotifyClick = () => {
  // 直接导航到通知列表页面
  router.push('/notifications/list');
};

// 格式化时间戳
const formatTimestamp = (timestamp: number): string => {
  return dayjs(timestamp).format('MM-DD HH:mm');
};

// 处理Popover中通知项点击
const handlePopoverItemClick = (item: RoomNotifyPayload) => {
  // 标记通知为已读
  notificationStore.markAsDeal(item.id);

  // 跳转到通知列表页并高亮该通知
  router.push({
    path: '/notifications/list',
    query: { highlight: item.id }
  });

  // 关闭Popover
  popoverVisible.value = false;
};

// 跳转到通知列表页
const goToNotificationList = () => {
  router.push('/notifications/list');
  popoverVisible.value = false;
};

// 监听Popover事件
const onPopoverHide = () => {
  console.log('[Notification] Popover隐藏事件触发');
};

const onPopoverShow = () => {
  console.log('[Notification] Popover显示事件触发');
};
</script>

<style scoped>
.sidebar-container {
  border-right: 1px solid rgba(230, 230, 230, 1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

/* 菜单项样式 */
.sidebar-item {
  position: relative;
  width: 100px;
  height: 92px;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 8px;
  background-color: #fff;
}

.sidebar-item:hover {
  background-color: #f0f0f0;
}

.active-item {
  width: 100px;
  height: 92px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background-color: #f3f3f3;
}

/* 移除菜单项底部分割线 */
.sidebar-item::after {
  display: none;
}

/* 激活状态下的图标变为红色 */
.icon-red {
  filter: invert(32%) sepia(72%) saturate(2526%) hue-rotate(341deg) brightness(94%) contrast(86%);
}

/* 底部图标样式 */
.sidebar-icon {
  border-radius: 8px;
  width: 100px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.sidebar-icon:hover {
  background-color: #f0f0f0;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border-color: #e0e0e0;
}

/* 头像容器样式 */
.avatar-container {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* 头像样式 */
.sidebar-avatar {
  border-radius: 8px;
  width: 100px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.sidebar-avatar:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border-color: #e0e0e0;
}

/* 添加active状态样式 */
.active-icon {
  background-color: #f3f3f3;
  border-color: #f3f3f3;
}

/* 通知徽章样式 */
:deep(.el-badge__content) {
  background-color: #e23939;
}

/* 移除不需要的样式 */
.icon-circle,
.avatar-circle {
  display: none;
}

/* 用户 Popover 样式 */
.user-popover-content {
  padding: 0;
}

.user-popover-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f8f9fa;
}

.user-popover-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 12px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popover-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.user-popover-info {
  flex: 1;
}

.user-popover-name {
  font-weight: 600;
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
}

.user-popover-venue {
  font-size: 13px;
  color: #666;
}

.user-popover-divider {
  height: 1px;
  background-color: #eee;
  margin: 0;
}

.user-popover-item {
  padding: 14px 16px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
}

.user-popover-item .el-icon {
  margin-right: 10px;
  font-size: 18px;
}

.user-popover-item:hover {
  background-color: #f5f5f5;
}

.user-popover-item.logout {
  color: #e74c3c;
  font-weight: 500;
}

.user-popover-item.logout .el-icon {
  color: #e74c3c;
}

.user-popover-item.logout:hover {
  background-color: #fee;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .sidebar-container {
    width: 80px;
  }

  .sidebar-item {
    width: 70px;
    height: 70px;
  }

  .active-item {
    width: 70px;
    height: 70px;
  }

  .sidebar-item span {
    display: none;
  }
}
</style>

<!-- 全局样式 -->
<style>
.user-avatar-popover {
  padding: 0 !important;
}

.user-avatar-popover .el-popover__content {
  padding: 0 !important;
}
</style>
