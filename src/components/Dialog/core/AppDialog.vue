<template>
  <el-dialog
    align-center
    v-model="dialogVisible"
    :title="uiType === DialogUIType.LARGE ? undefined : title"
    :width="width"
    :close-on-click-modal="closeOnClickModal"
    :style="height ? `--el-dialog-height: ${height}` : ''"
    :show-close="showHeader && showClose"
    :destroy-on-close="destroyOnClose"
    :before-close="handleClose"
    @closed="handleClosed"
    :class="[
      dialogClasses,
      'app-dialog',
      `dialog-ui-${uiType}`,
      { 'dialog-fullscreen': fullscreen },
      { square: cardType === 'square' },
      { normal: cardType === 'normal' },
      { tall: cardType === 'tall' },
      { small: cardType === 'small' },
      { 'no-header': !showHeader },
      customClass
    ]"
    :fullscreen="fullscreen"
    :modal="modal"
    :close-on-press-escape="closeOnPressEscape">
    <!-- 当showHeader为false且showClose为true时，添加独立的关闭按钮 -->
    <div v-if="!showHeader && showClose" class="standalone-close-btn" @click="handleCloseBtn">
      <ClosePopup alt="关闭" style="font-size: 24px" />
    </div>

    <!-- 1. 自定义头部，覆盖el-dialog的头部，根据showHeader属性控制是否显示 -->
    <template #header v-if="showHeader && $slots.header">
      <slot name="header"></slot>
    </template>

    <!-- 当showHeader为false时，提供一个空的header以便完全隐藏原生header -->
    <template #header v-if="!showHeader">
      <!-- 空白header以替换原生header -->
      <div class="empty-header"></div>
    </template>

    <!-- 2. 正文内容区域，添加uiType相关的类名 -->
    <div class="dialog-content" :class="[`dialog-content-${uiType}`, { 'no-header': !showHeader }]">
      <slot></slot>
    </div>

    <!-- 3. 自定义底部，传递给el-dialog的底部 -->
    <template #footer v-if="showFooter">
      <slot name="footer">
        <!-- 默认按钮，根据uiType使用不同样式 -->
        <div :class="{ 'footer-center': uiType === DialogUIType.CARD, 'footer-right': uiType !== DialogUIType.CARD }">
          <el-button v-if="cancelText" @click="handleClose">{{ cancelText }}</el-button>
          <el-button v-if="confirmText" type="primary" @click="$emit('confirm')">{{ confirmText }}</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, useAttrs, onMounted } from 'vue';
import { DialogLevel, DialogUIType } from '@/types/dialog';
import ClosePopup from '@/assets/v3/close_popup.vue';
// 定义props，支持 modelValue 和 visible 两种方式
const props = defineProps({
  // 添加debug模式
  debug: {
    type: Boolean,
    default: false
  },
  // 添加 cardType 属性
  cardType: {
    type: String,
    default: 'normal', // 默认为normal
    validator(val: string) {
      return ['normal', 'square', 'tall', 'small'].includes(val);
    }
  },
  // 兼容旧版本的 visible 属性
  visible: {
    type: Boolean,
    default: false
  },
  // 新版本使用 modelValue 属性
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: '50%'
  },
  height: {
    type: [String, Number],
    default: undefined
  },
  showClose: {
    type: Boolean,
    default: true
  },
  destroyOnClose: {
    type: Boolean,
    default: true
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确定'
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 是否全屏显示
  fullscreen: {
    type: Boolean,
    default: false
  },
  // 是否显示遮罩层
  modal: {
    type: Boolean,
    default: true
  },
  // 是否可以通过点击模态层关闭对话框
  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  // 是否可以通过按下 ESC 关闭对话框
  closeOnPressEscape: {
    type: Boolean,
    default: false
  },
  // 是否显示底部
  showFooter: {
    type: Boolean,
    default: true
  },
  // 是否显示头部
  // 设置为false时会完全隐藏原生header区域，并调整内容区域的上边距
  showHeader: {
    type: Boolean,
    default: true
  },
  // 弹窗级别，用于区分不同层级的弹窗
  level: {
    type: Number,
    default: DialogLevel.PRIMARY,
    validator(val: number) {
      return [DialogLevel.PRIMARY, DialogLevel.SECONDARY, DialogLevel.TERTIARY].includes(val);
    }
  },
  // 弹窗UI类型，决定弹窗的布局和样式
  uiType: {
    type: String,
    default: DialogUIType.DEFAULT,
    validator(val: DialogUIType) {
      return [DialogUIType.DEFAULT, DialogUIType.LARGE, DialogUIType.CARD, DialogUIType.CUSTOM].includes(val);
    }
  },
  contentTitle: {
    type: String,
    default: ''
  },
  // 添加自定义类名属性
  customClass: {
    type: String,
    default: ''
  }
});

// 定义事件，同时支持 update:visible 和 update:modelValue
const emit = defineEmits(['update:visible', 'update:modelValue', 'close', 'closed', 'confirm']);

// 对话框可见状态，优先使用 modelValue，如果没有则使用 visible
const dialogVisible = ref(props.modelValue || props.visible);

// 监听 modelValue 属性变化
watch(
  () => props.modelValue,
  val => {
    dialogVisible.value = val;
  }
);

// 监听 visible 属性变化（向后兼容）
watch(
  () => props.visible,
  val => {
    // 只有当 modelValue 未定义或为 false 时才使用 visible
    if (props.modelValue === false) {
      dialogVisible.value = val;
    }
  }
);

// 处理关闭前的回调
const handleClose = (done: () => void) => {
  emit('close');
  if (done) {
    done();
  }
};

// 处理自定义关闭按钮点击事件
const handleCloseBtn = () => {
  emit('close');
  dialogVisible.value = false;
  emit('update:modelValue', false);
  emit('update:visible', false);
};

// 处理关闭后的回调
const handleClosed = () => {
  emit('closed');
};

// 计算弹窗的CSS类名
const dialogClasses = computed(() => {
  return {
    [`dialog-level-${props.level}`]: true
  };
});
</script>

<style scoped>
/* 弹窗级别样式 */
.dialog-level-1 {
  z-index: 2000;
}

.dialog-level-2 {
  z-index: 2100;
}

.dialog-level-3 {
  z-index: 2200;
}

/* UI类型样式 */
.dialog-ui-default {
  /* 默认UI样式 */
}

/* 独立关闭按钮样式 */
.standalone-close-btn {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  z-index: 2300 !important;
  cursor: pointer !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  transition: filter 0.3s ease !important;
}

.standalone-close-btn:hover {
  filter: brightness(0) saturate(100%) invert(15%) sepia(100%) saturate(10000%) hue-rotate(0deg) !important;
}

.standalone-close-btn .close-icon {
  width: 32px !important;
  height: 32px !important;
}

/* 内容标题样式 */
.content-title {
  padding: 20px;
  font-size: 24px;
  font-weight: normal;
}

/* 空白头部样式 */
.empty-header {
  height: 0;
  padding: 0;
  margin: 0;
  display: none !important;
}

/* 全屏样式 */
.dialog-fullscreen {
  /* 全屏样式 */
}
</style>
