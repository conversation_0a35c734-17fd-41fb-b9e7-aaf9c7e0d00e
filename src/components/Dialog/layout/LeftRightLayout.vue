<template>
  <div class="left-right-layout" :class="{ 'has-gap': gap }">
    <!-- 左侧内容 -->
    <div class="left-panel" :class="leftClass">
      <div v-if="leftTitle" class="panel-title px-[36px] py-[20px] text-[#E9223A] text-[28px] font-400 leading-normal">
        {{ leftTitle }}
      </div>
      <div class="panel-content">
        <slot name="left"></slot>
      </div>
      <div v-if="$slots['left-footer']" class="panel-footer">
        <slot name="left-footer"></slot>
      </div>
    </div>

    <!-- 右侧内容 -->
    <div class="right-panel" :class="rightClass">
      <div v-if="rightTitle" class="panel-title py-[20px] text-[#000] text-[28px] font-400 leading-normal pl-[24px] pb-[16px]">{{ rightTitle }}</div>
      <div class="panel-content">
        <slot name="right"></slot>
      </div>
      <div v-if="$slots['right-footer']" class="panel-footer">
        <slot name="right-footer"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  // 面板之间是否有间隔
  gap: {
    type: Boolean,
    default: false
  },
  // 左侧自定义样式
  leftClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 右侧自定义样式
  rightClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 左侧标题
  leftTitle: {
    type: String,
    default: ''
  },
  // 右侧标题
  rightTitle: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.left-right-layout {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.left-panel,
.right-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 使用flex比例代替固定宽度 */
.left-panel {
  flex: 3;
  background-color: #fff;
  /* 保持最小宽度 */
}

.right-panel {
  flex: 2;
  background: rgba(0, 0, 0, 0.04);
  /* 保持最小宽度 */
}

/* 内容区域 - 可滚动 */
.panel-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  padding-left: 24px;
  padding-right: 24px;
  /* 移除固定高度计算，使用flex布局自动计算 */
}

/* 底部操作栏 - 固定在底部 */
.panel-footer {
  height: 112px;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 10;
  flex-shrink: 0; /* 防止底部栏被压缩 */
}

/* 间距控制 */
.has-gap {
  gap: 8px;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}
</style>
