<template>
  <div class="notification-popover-wrapper">
    <el-popover
      ref="popoverRef"
      :visible="visible"
      @update:visible="onVisibleChange"
      placement="right"
      :width="300"
      trigger="manual"
      popper-class="notification-custom-popover"
      :show-arrow="false"
      :persistent="false"
      :teleported="true"
      :offset="24"
      @hide="onPopoverHide"
      @show="onPopoverShow">
      <template #reference>
        <slot name="reference"></slot>
      </template>

      <!-- Popover内容 -->
      <div class="notification-popover-content">
        <ul v-if="notifications.length > 0" class="notification-list">
          <li v-for="item in displayNotifications" :key="item.id" class="notification-item flex items-center justify-between">
            <div class="flex items-center flex-1">
              <div class="flex notification-icon mr-3 w-[64px] h-[64px] bg-gray-100 rounded-md items-center justify-center">
                <NotifyIcon class="text-[32px]" :color="'#000'" />
              </div>
              <div class="notification-content flex-1">
                <div class="text-[20px] font-medium">{{ item.roomNumber }}: {{ item.message }}</div>
                <div class="text-[16px] text-gray-500">{{ formatTimestamp(item.timestamp) }}</div>
              </div>
            </div>
            <el-button class="btn-default w-[110px] h-[53px] rounded-md text-[16px]" @click="handleProcessItem(item)" :loading="processingIds.includes(item.id)"
              >处理</el-button
            >
          </li>
        </ul>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue';
import dayjs from 'dayjs';
import NotifyIcon from '@/assets/v3/notify.vue';
import { useNotificationStore } from '@/modules/notification/stores/notificationStore';
import { getCurrentInstance } from 'vue';

// 获取用户信息
const app = getCurrentInstance();
const userStore = app?.appContext.config.globalProperties.$userStore;

// 通知store
const notificationStore = useNotificationStore();

// 正在处理中的通知ID
const processingIds = ref([]);

// 定义组件属性
const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: '通知'
  },
  // 弹窗可见性
  modelValue: {
    type: Boolean,
    default: false
  },
  // 通知列表
  notifications: {
    type: Array,
    default: () => []
  },
  // 自动关闭时间(毫秒)，0表示不自动关闭
  autoCloseDelay: {
    type: Number,
    default: 0
  },
  // 是否在点击外部区域时关闭
  closeOnClickOutside: {
    type: Boolean,
    default: true
  }
});

// 事件
const emit = defineEmits(['update:modelValue', 'item-click', 'view-all', 'hide', 'show']);

// 维护内部可见性状态
const visible = ref(props.modelValue);

// 只显示最新的5条通知
const displayNotifications = computed(() => {
  return props.notifications.slice(0, 5);
});

// 是否需要向上移动（条件性应用transform）
const shouldTranslateUp = computed(() => {
  return props.notifications.length > 3;
});

// 监听外部value改变
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    // 设置popover的class，根据通知数量添加has-many-items类
    if (newVal) {
      nextTick(() => {
        const popover = document.querySelector('.notification-custom-popover');
        if (popover) {
          if (props.notifications.length > 3) {
            popover.classList.add('has-many-items');
          } else {
            popover.classList.remove('has-many-items');
          }
        }
      });
    }
  }
);

// 处理可见性变化
const onVisibleChange = val => {
  visible.value = val;
  emit('update:modelValue', val);

  // 当Popover显示时，检查通知数量并设置class
  if (val) {
    nextTick(() => {
      const popover = document.querySelector('.notification-custom-popover');
      if (popover) {
        if (props.notifications.length > 3) {
          popover.classList.add('has-many-items');
        } else {
          popover.classList.remove('has-many-items');
        }
      }
    });
  }
};

// 监听通知列表变化，当有新通知时显示弹窗
watch(
  () => props.notifications,
  (newNotifications, oldNotifications) => {
    if (!oldNotifications) return;

    // 检查是否有新通知，通过比较长度和第一个ID
    const hasNewNotifications =
      newNotifications.length > oldNotifications.length ||
      (newNotifications.length > 0 && oldNotifications.length > 0 && newNotifications[0].id !== oldNotifications[0].id);

    if (hasNewNotifications && newNotifications.length > 0) {
      // 有新通知时显示弹窗
      visible.value = true;
      emit('update:modelValue', true);

      // 如果设置了自动关闭，则启动定时器
      if (props.autoCloseDelay > 0) {
        startAutoCloseTimer();
      }
    }
  },
  { deep: true }
);

// 关闭弹窗
const closePopover = () => {
  visible.value = false;
  emit('update:modelValue', false);
  emit('hide');
};

// 处理通知项
const handleItemClick = item => {
  emit('item-click', item);
  visible.value = false;
  emit('update:modelValue', false);
};

// 处理服务呼叫
const handleProcessItem = async item => {
  // 添加到处理中状态
  processingIds.value.push(item.id);

  try {
    // 调用处理接口
    const employeeId = userStore?.userInfo?.id || '';
    const success = await notificationStore.processServiceCall(item.id, employeeId);

    if (success) {
      // 处理成功后，使用新接口更新未读通知列表
      await notificationStore.fetchAndUpdateUnreadNotifications();

      // 如果没有更多未读通知，关闭弹窗
      if (notificationStore.unreadCount === 0) {
        visible.value = false;
        emit('update:modelValue', false);
      }
    }
  } catch (error) {
    console.error('处理呼叫通知失败:', error);
  } finally {
    // 从处理中状态移除
    processingIds.value = processingIds.value.filter(id => id !== item.id);
  }
};

// 弹窗事件监听
const onPopoverHide = () => {
  emit('hide');
};

const onPopoverShow = () => {
  emit('show');
};

// 格式化时间戳
const formatTimestamp = timestamp => {
  return dayjs(timestamp).format('MM-DD HH:mm');
};

// 自动关闭定时器
let autoCloseTimer = null;

const startAutoCloseTimer = () => {
  // 清除之前的定时器
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer);
  }

  // 设置新的定时器
  autoCloseTimer = window.setTimeout(() => {
    visible.value = false;
    emit('update:modelValue', false);
  }, props.autoCloseDelay);
};

// 向父组件公开方法
defineExpose({
  close: closePopover,
  show: () => {
    visible.value = true;
    emit('update:modelValue', true);
  }
});
</script>

<style>
/* 全局样式确保能覆盖teleported元素 */
.notification-custom-popover.el-popper.is-light {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

/* 条件性地应用transform，根据shouldTranslateUp计算属性 */
.notification-custom-popover.el-popper.is-light.has-many-items {
  transform: translateY(-24px) !important;
}

.notification-custom-popover .el-popper__arrow {
  display: none !important;
}
</style>

<style scoped>
.notification-popover-content {
  padding: 0;
}

.notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  width: 440px;
  height: 90px;
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none !important;
  margin-bottom: 8px;
}

.notification-item:last-child {
  margin-bottom: 0;
}
</style>
