import axios, { type AxiosRequestHeaders, type InternalAxiosRequestConfig } from 'axios';
import { useTimeStore } from '@/stores/timeStore';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { getToken } from './authUtils';
import { useVenueStore } from '@/stores/venueStore';
import { redirectTo } from './routerUtils';

// 设备类型常量
const DEVICE_TYPE = 'PC_WEB';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 30 * 1000
});

// 创建自定义错误类，避免显示"Error:"前缀
class ApiError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ApiError';
  }

  toString() {
    return this.message;
  }
}

// request拦截器
service.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // 记录请求开始时间
    config.metadata = { startTime: Date.now() };

    const deviceStore = useDeviceStore();
    const venueStore = useVenueStore();
    const token = getToken();

    // 设置通用请求头
    if (!config.headers) {
      config.headers = new axios.AxiosHeaders();
    }

    const location = localStorage.getItem('location');
    if (location) {
      config.headers.set('location', location);
    }

    if (token) {
      config.headers.set('Authorization', `Bearer ${token}`);
    }

    const userStore = useUserStore();

    // 处理客户端类型和MAC地址
    const clientType = import.meta.env.VITE_CLIENT_TYPE;
    console.log('[Request] clientType:', clientType);
    const macAddress = deviceStore.macAddress ? (clientType === 'pad' ? `pad:${deviceStore.macAddress}` : deviceStore.macAddress) : '';

    // 添加公共 header
    if (macAddress) {
      config.headers.set('X-Mac', macAddress);
    }

    if (userStore.userInfo?.employee?.id) {
      config.headers.set('X-Employee-Id', userStore.userInfo.employee.id);
    }

    if (venueStore.venueId) {
      config.headers.set('X-Venue-Id', venueStore.venueId);
    }

    // 添加通用参数（保持body中的现有参数）
    const commonParams = {
      venueId: venueStore.venueId || '',
      grantIp: deviceStore.ipAddress || '',
      mac: macAddress,
      deviceType: deviceStore.deviceType || '',
      appVersionCode: deviceStore.appVersionCode || '',
      employeeId: userStore.userInfo?.employee?.id
    };

    // 根据请求方法添加参数
    if (config.method?.toLowerCase() === 'post') {
      config.data = {
        ...config.data,
        ...commonParams
      };
    } else {
      config.params = {
        ...config.params,
        ...commonParams
      };
    }

    // 调试输出
    console.debug('Request config:', {
      url: config.url,
      method: config.method,
      headers: Object.fromEntries(config.headers),
      params: config.params,
      data: config.data
    });

    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// response 拦截器
service.interceptors.response.use(
  response => {
    const timeStore = useTimeStore();
    const endTime = Date.now();
    const startTime = response.config.metadata?.startTime;

    // 处理服务器时间同步
    const serverTime = response.data?.serverTime
      ? String(response.data.serverTime).length === 10
        ? Number(response.data.serverTime) * 1000
        : response.data.serverTime
      : null;

    if (serverTime && startTime) {
      timeStore.updateServerTime(serverTime, startTime, endTime);
      console.debug('Time synced:', {
        serverTime: new Date(Number(serverTime)).toISOString(),
        offset: timeStore.timeOffset,
        rtt: endTime - startTime
      });
    }

    // 处理响应数据
    if (response.data.code === 0) {
      return response.data;
    } else {
      // 处理特定错误码
      switch (response.data.code) {
        case 401:
        case 403:
          const userStore = useUserStore();
          userStore.resetUserState();

          // 使用动态获取的router实例
          redirectTo('/login');
          break;
        case 405:
          const venueStore = useVenueStore();
          venueStore.clearVenueInfo();

          redirectTo('/auth');
          break;
      }
      // 使用自定义错误类，避免显示"Error:"前缀
      return Promise.reject(new ApiError(response.data.message || 'Error'));
    }
  },
  error => {
    console.error('Response error:', error);
    return Promise.reject(error);
  }
);

export default service;
