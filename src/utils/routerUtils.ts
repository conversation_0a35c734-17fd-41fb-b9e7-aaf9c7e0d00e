/**
 * 路由工具函数
 * 提供跨应用类型（client/pad）的统一路由操作
 *
 * @example
 * ```typescript
 * // 基本重定向
 * await redirectTo('/login');
 *
 * // replace方式重定向（不留历史记录）
 * await redirectTo('/auth', true);
 *
 * // 带查询参数的重定向
 * await redirectTo('/login', false, { redirect: '/dashboard' });
 *
 * // 获取当前路由
 * const route = await getCurrentRoute();
 * console.log(route.path, route.fullPath);
 * ```
 */

import type { Router } from 'vue-router';

// 缓存router实例，避免重复动态导入
let routerInstance: Router | null = null;
let routerPromise: Promise<Router> | null = null;

/**
 * 获取当前应用的router实例（带缓存）
 * @returns Promise<Router>
 */
export function getRouterInstance(): Promise<Router> {
  // 如果已经有缓存的实例，直接返回
  if (routerInstance) {
    return Promise.resolve(routerInstance);
  }

  // 如果正在加载中，返回现有的Promise
  if (routerPromise) {
    return routerPromise;
  }

  // 根据应用类型动态导入router
  const isPadApp = import.meta.env.VITE_CLIENT_TYPE === 'pad';

  routerPromise = (isPadApp ? import('@/apps/client-pad/router') : import('@/router'))
    .then(module => {
      routerInstance = module.default;
      return routerInstance;
    })
    .catch(err => {
      // 重置Promise，允许重试
      routerPromise = null;
      throw err;
    });

  return routerPromise;
}

/**
 * 统一的路由重定向函数
 * @param path 要重定向到的路径
 * @param replace 是否使用replace方式跳转，默认false
 * @param query 查询参数，可选
 */
export async function redirectTo(path: string, replace = false, query?: Record<string, any>) {
  try {
    const router = await getRouterInstance();
    const targetRoute = { path, query };

    // 避免重复重定向
    if (router.currentRoute.value.path !== path) {
      if (replace) {
        await router.replace(targetRoute);
      } else {
        await router.push(targetRoute);
      }
    }
  } catch (err) {
    console.error('路由重定向失败，使用location降级:', err);
    // 降级处理，直接使用location跳转
    if (window.location.pathname !== path) {
      const url = new URL(path, window.location.origin);
      if (query) {
        Object.entries(query).forEach(([key, value]) => {
          url.searchParams.set(key, String(value));
        });
      }
      if (replace) {
        window.location.replace(url.href);
      } else {
        window.location.href = url.href;
      }
    }
  }
}

/**
 * 获取当前路由信息
 * @returns Promise<{path: string, fullPath: string}>
 */
export async function getCurrentRoute() {
  try {
    const router = await getRouterInstance();
    return {
      path: router.currentRoute.value.path,
      fullPath: router.currentRoute.value.fullPath
    };
  } catch (err) {
    console.error('获取当前路由失败，使用location降级:', err);
    return {
      path: window.location.pathname,
      fullPath: window.location.href
    };
  }
}

// 预加载router实例，避免首次使用时的延迟
getRouterInstance().catch(err => {
  console.warn('预加载router失败，将在需要时重试:', err);
});
