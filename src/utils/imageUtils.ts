/**
 * 图片工具函数
 * 提供图片相关的通用处理功能
 */

// 默认的无图片SVG（base64编码），显示"无图片"文字
const DEFAULT_NO_IMAGE_BASE64 =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgMzAwIDMwMCI+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjMwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9IiNmM2YzZjMiLz48dGV4dCB4PSIxNTAiIHk9IjE1MCIgZm9udC1zaXplPSIzNiIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBhbGlnbm1lbnQtYmFzZWxpbmU9Im1pZGRsZSI+5peg5Zu+54mHPC90ZXh0Pjwvc3ZnPg==';

/**
 * 处理图片加载错误的函数
 *
 * @param event 图片错误事件
 * @param fallbackUrl 备选图片URL
 * @param finalFallback 最终备选图片（默认为base64编码的SVG）
 */
export function handleImageError(event: Event, fallbackUrl: string = '/default-product.png', finalFallback: string = DEFAULT_NO_IMAGE_BASE64): void {
  const target = event.target as HTMLImageElement;

  // 如果当前图片是备选图片且加载失败，使用base64兜底
  if (target.src.includes(fallbackUrl)) {
    target.src = finalFallback;
    // 防止无限循环
    target.onerror = null;
  } else {
    // 尝试加载备选图片
    target.src = fallbackUrl;
  }
}

/**
 * 获取商品图片URL
 *
 * @param imageUrl 原始图片URL
 * @param defaultUrl 默认图片URL
 * @returns 有效的图片URL
 */
export function getProductImageUrl(imageUrl?: string, defaultUrl: string = '/default-product.png'): string {
  return imageUrl || defaultUrl;
}
