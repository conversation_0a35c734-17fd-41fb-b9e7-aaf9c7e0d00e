/**
 * 表单验证工具
 */

// 手机号正则表达式：1开头，第二位为3-9，总共11位数字
const MOBILE_REGEX = /^1[3-9]\d{9}$/;

/**
 * 验证手机号格式
 * @param mobile 手机号
 * @returns 验证结果对象，包含是否有效和错误信息
 */
export const validateMobile = (mobile: string): { isValid: boolean; message: string } => {
  if (!mobile) {
    return {
      isValid: false,
      message: '请输入手机号'
    };
  }

  if (!MOBILE_REGEX.test(mobile)) {
    return {
      isValid: false,
      message: '请输入正确的手机号格式'
    };
  }

  return {
    isValid: true,
    message: ''
  };
};

/**
 * 验证是否为空
 * @param value 值
 * @param fieldName 字段名称
 * @returns 验证结果
 */
export const validateRequired = (value: string, fieldName: string): { isValid: boolean; message: string } => {
  if (!value || value.trim() === '') {
    return {
      isValid: false,
      message: `请输入${fieldName}`
    };
  }

  return {
    isValid: true,
    message: ''
  };
};

/**
 * 验证字符串长度
 * @param value 值
 * @param minLength 最小长度
 * @param maxLength 最大长度
 * @param fieldName 字段名称
 * @returns 验证结果
 */
export const validateLength = (value: string, minLength: number, maxLength: number, fieldName: string): { isValid: boolean; message: string } => {
  if (!value) {
    return {
      isValid: false,
      message: `请输入${fieldName}`
    };
  }

  if (value.length < minLength) {
    return {
      isValid: false,
      message: `${fieldName}至少需要${minLength}个字符`
    };
  }

  if (value.length > maxLength) {
    return {
      isValid: false,
      message: `${fieldName}不能超过${maxLength}个字符`
    };
  }

  return {
    isValid: true,
    message: ''
  };
};

/**
 * 验证邮箱格式
 * @param email 邮箱
 * @returns 验证结果
 */
export const validateEmail = (email: string): { isValid: boolean; message: string } => {
  if (!email) {
    return {
      isValid: false,
      message: '请输入邮箱'
    };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      message: '请输入正确的邮箱格式'
    };
  }

  return {
    isValid: true,
    message: ''
  };
};
