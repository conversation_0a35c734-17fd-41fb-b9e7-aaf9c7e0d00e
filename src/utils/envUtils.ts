/**
 * 环境判断工具
 * - 生产环境：merp.ktvsky.com
 * - 预览环境：merpdev-stage.ktvsky.com
 * - 开发环境：localhost或127.0.0.1
 */

// 环境相关常量
const ENV = {
  HOSTS: {
    PROD: 'merp.ktvsky.com',
    PREVIEW: 'merpdev-stage.ktvsky.com',
    DEV: ['localhost', '127.0.0.1']
  },
  HOSTS_PAD: {
    PROD: 'merp-pad-prod.ktvsky.com',
    PREVIEW: 'merp-pad-pre.ktvsky.com',
    DEV: ['localhost', '127.0.0.1']
  },
  LOG_PREFIX: '[ENV]'
};

/**
 * 获取当前系统环境信息
 */
export interface EnvInfo {
  /** 当前主机名 */
  hostname: string;
  /** 是否为开发环境 */
  isDev: boolean;
  /** 是否为生产环境 */
  isProd: boolean;
  /** 是否为预览环境 */
  isPreview: boolean;
  /** 是否应该启用监控/特性（生产或预览环境） */
  shouldEnableMonitor: boolean;
}

/**
 * 环境判断工具
 */
export const EnvironmentUtils = {
  /**
   * 获取当前环境信息
   * @returns 环境信息对象
   */
  getEnvInfo(): EnvInfo {
    const isPad = import.meta.env.VITE_CLIENT_TYPE === 'pad';
    const hostname = window.location.hostname;
    const isDev = ENV.HOSTS.DEV.includes(hostname) || hostname.startsWith('192.168.');
    const isProd = hostname === (isPad ? ENV.HOSTS_PAD.PROD : ENV.HOSTS.PROD);
    const isPreview = hostname === (isPad ? ENV.HOSTS_PAD.PREVIEW : ENV.HOSTS.PREVIEW);
    
    return {
      hostname,
      isDev,
      isProd,
      isPreview,
      // 是否应该启用监控（生产或预览环境）
      shouldEnableMonitor: isProd || isPreview
    };
  },
  
  /**
   * 执行仅在非开发环境下的操作
   * @param callback 要执行的回调函数
   * @param logPrefix 日志前缀
   */
  runInProdOrPreview(callback: () => void, logPrefix: string = ENV.LOG_PREFIX): void {
    const env = this.getEnvInfo();
    if (env.isDev) {
      console.info(`${logPrefix} 本地开发环境，不执行操作`);
      return;
    }
    
    if (env.shouldEnableMonitor) {
      try {
        callback();
        console.info(`${logPrefix} 操作执行成功，当前环境: ${env.isProd ? '生产环境' : '预览环境'}`);
      } catch (error) {
        console.error(`${logPrefix} 操作执行失败`, error);
      }
    }
  },
  
  /**
   * 快捷方法：判断当前是否为开发环境
   */
  isDevelopment(): boolean {
    return this.getEnvInfo().isDev;
  },
  showVConsole(): boolean {
    const isDevOrPreview = this.isDevelopment() || this.isPreview();
    const isShowVConsole = import.meta.env.VITE_ENABLE_VCONSOLE === 'true';
    console.log('isDevOrPreview', isDevOrPreview, 'isShowVConsole', isShowVConsole);
    return isDevOrPreview && isShowVConsole;
  },
  
  /**
   * 快捷方法：判断当前是否为生产环境
   */
  isProduction(): boolean {
    return this.getEnvInfo().isProd;
  },
  
  /**
   * 快捷方法：判断当前是否为预览环境
   */
  isPreview(): boolean {
    return this.getEnvInfo().isPreview;
  },
  
  /**
   * 快捷方法：判断当前是否应该启用监控
   */
  shouldEnableMonitor(): boolean {
    return this.getEnvInfo().shouldEnableMonitor;
  }
}; 