import { OptionalGroupViewModel } from '@/modules/production/components/packageDialog/presenter';

// 统一的已选商品结构
export interface SelectedProductInfo {
  id: string;
  name: string;
  count: number;
  currentPrice?: number;
  groupIndex?: number;
  productIndex?: number;
}

/**
 * 计算套餐已选商品明细
 * 1. 默认商品直接取 count
 * 2. by_count 组: 商品总数 = selected_count，受 optionCount 限制
 * 3. by_plan 组: 商品总数 = 商品.count × selected_count，受 optionCount 限制
 *
 * 注意：optionCount 限制
 * - by_count: 总数量不能超过 optionCount
 * - by_plan: 选中商品种类数不能超过 optionCount
 *
 * @param packageData   套餐packageData（包含默认商品列表）
 * @param optionalGroups 当前解析后的可选组数据
 * @returns 已选商品数组 (去除未选)
 */
export function computeSelectedPackageProducts(packageData: any, optionalGroups: OptionalGroupViewModel[]): SelectedProductInfo[] {
  const result: SelectedProductInfo[] = [];

  if (!packageData) return result;

  // 1. 默认商品（不受 optionCount 限制）
  if (packageData.packageProducts) {
    try {
      const defaults = JSON.parse(packageData.packageProducts);
      const voList = packageData.productVOList || [];
      defaults.forEach((p: any) => {
        const info = voList.find((v: any) => v.id === p.id);
        result.push({
          id: p.id,
          name: p.name || info?.name || `商品(${p.id})`,
          count: p.count,
          currentPrice: p.currentPrice || info?.currentPrice
        });
      });
    } catch (_) {
      /* ignore */
    }
  }

  // 2. 可选组（受 optionCount 限制）
  optionalGroups.forEach((group, gIdx) => {
    // 验证当前组的选择是否符合 optionCount 限制
    if (group.optionType === 'by_count') {
      // by_count: 验证总数量
      const totalSelected = group.products.reduce((sum, prod) => {
        return sum + (prod.count || 1) * (prod.selected_count || 0);
      }, 0);

      if (totalSelected > group.optionCount) {
        console.warn(`可选组 "${group.name}" 选择数量 ${totalSelected} 超过限制 ${group.optionCount}`);
      }
    } else if (group.optionType === 'by_plan') {
      // by_plan: 验证选中商品种类数
      const selectedTypes = group.products.filter(prod => (prod.selected_count || 0) > 0).length;

      if (selectedTypes > group.optionCount) {
        console.warn(`可选组 "${group.name}" 选择种类数 ${selectedTypes} 超过限制 ${group.optionCount}`);
      }
    }

    // 添加已选商品到结果
    group.products.forEach((prod, pIdx) => {
      const sel = prod.selected_count || 0;
      if (sel > 0) {
        const totalCount = (prod.count || 1) * sel; // 按方案需乘基数
        const info = (packageData.productVOList || []).find((v: any) => v.id === prod.id);
        result.push({
          id: prod.id,
          name: prod.name || info?.name || `商品(${prod.id})`,
          count: totalCount,
          currentPrice: prod.currentPrice || info?.currentPrice,
          groupIndex: gIdx,
          productIndex: pIdx
        });
      }
    });
  });

  return result;
}
