/**
 * 商品套餐可选组默认选择工具函数
 */

/**
 * 用于默认选择逻辑的商品接口
 * (简化版，仅包含排序所需字段)
 */
export interface ProductInPackageDefaultSelection {
  id: string;
  salesVolume: number; // 销量
  profitRate: number; // 利润率
  createdAt: string; // 创建时间 (ISO 8601 格式字符串)
  // name?: string; // 名称，可选，非排序必须
}

/**
 * 用于默认选择逻辑的方案接口
 * (简化版)
 */
export interface PlanInPackageDefaultSelection {
  id: string;
  isRecommended?: boolean; // 是否推荐
  products: ProductInPackageDefaultSelection[]; // 方案内包含的商品
  // name?: string; // 名称，可选，非排序必须
}

/**
 * 套餐商品项基础接口
 */
export interface PackageProductItem {
  id: string;
  name: string;
  count?: number;
  selected_count?: number;
  currentPrice?: number;
  [key: string]: any;
}

/**
 * 可选组基础接口
 */
export interface OptionalGroup {
  name: string;
  optionType: string;
  optionCount: number;
  products: PackageProductItem[];
  [key: string]: any;
}

/**
 * 为 "by_count" 类型的可选组获取默认选中的商品ID列表
 * 规则:
 * 1. 按销量降序
 * 2. 销量相同按利润率降序
 * 3. 利润率相同按创建时间降序 (新优先)
 * @param options 可选商品列表
 * @param count 需要选择的数量
 * @returns 默认选中的商品ID数组
 */
export function getDefaultByCountSelections(options: ProductInPackageDefaultSelection[], count: number): string[] {
  if (!options || options.length === 0 || count <= 0) {
    return [];
  }

  const sortedProducts = [...options].sort((a, b) => {
    if (a.salesVolume !== b.salesVolume) {
      return b.salesVolume - a.salesVolume;
    }
    if (a.profitRate !== b.profitRate) {
      return b.profitRate - a.profitRate;
    }
    try {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } catch (e) {
      // 如果日期格式错误，则退回到不比较创建时间
      return 0;
    }
  });

  return sortedProducts.slice(0, count).map(p => p.id);
}

/**
 * 为 "by_plan" 类型的可选组获取默认选中的方案ID
 * 规则:
 * 1. 优先选择标记为 "推荐" (isRecommended: true) 的方案
 * 2. 若无推荐，选择平均销量最高的方案
 * 3. 若仍无法确定 (如无销量数据或多个方案平均销量相同)，选择列表中的第一个方案
 * @param plans 可选方案列表
 * @returns 默认选中的方案ID，如果无法确定则返回 undefined
 */
export function getDefaultByPlanSelection(plans: PlanInPackageDefaultSelection[]): string | undefined {
  if (!plans || plans.length === 0) {
    return undefined;
  }

  // 1. 查找推荐方案
  const recommendedPlan = plans.find(p => p.isRecommended);
  if (recommendedPlan) {
    return recommendedPlan.id;
  }

  // 2. 计算每个方案的平均销量
  if (plans.every(plan => plan.products && plan.products.every(p => typeof p.salesVolume === 'number'))) {
    const planWithAvgSales = plans.map(plan => {
      const totalSales = plan.products.reduce((sum, p) => sum + (p.salesVolume || 0), 0);
      const avgSales = plan.products.length > 0 ? totalSales / plan.products.length : 0;
      return { planId: plan.id, avgSales };
    });

    // 排序并返回销量最高的方案ID
    if (planWithAvgSales.length > 0) {
      planWithAvgSales.sort((a, b) => b.avgSales - a.avgSales);
      if (planWithAvgSales[0].avgSales > 0) {
        // 仅当有实际销量时才依据销量选择
        return planWithAvgSales[0].planId;
      }
    }
  }

  // 3. 如果以上都无法确定，选择第一个方案
  return plans[0].id;
}

/**
 * 判断可选组类型是否为按数量选择
 * @param group 可选组对象
 * @returns boolean 是否为按数量选择模式
 */
export function isOptionalByCount(group: OptionalGroup | any): boolean {
  if (!group) return false;

  const optionType = group.optionType || group.type || '';
  return optionType.toLowerCase().includes('count') || optionType.toLowerCase() === 'bycount' || optionType.toLowerCase() === 'by_count';
}

/**
 * 判断可选组类型是否为按方案选择
 * @param group 可选组对象
 * @returns boolean 是否为按方案选择模式
 */
export function isOptionalByPlan(group: OptionalGroup | any): boolean {
  if (!group) return false;

  const optionType = group.optionType || group.type || '';
  return optionType.toLowerCase().includes('plan') || optionType.toLowerCase() === 'byplan' || optionType.toLowerCase() === 'by_plan';
}

/**
 * 标准化optionType，统一转为大写形式
 * @param group 可选组对象
 * @returns string 标准化后的optionType
 */
export function getNormalizedOptionType(group: any): string {
  if (!group) return '';

  // 首先获取optionType，然后转换为大写
  let optionType = group.optionType || group.type || '';

  // 处理小写形式转大写
  if (optionType.toLowerCase() === 'by_plan' || optionType.toLowerCase() === 'byplan' || optionType.toLowerCase() === 'byplan') {
    return 'ByPlan';
  } else if (optionType.toLowerCase() === 'by_count' || optionType.toLowerCase() === 'bycount' || optionType.toLowerCase() === 'bycount') {
    return 'ByCount';
  }

  // 如果是undefined或者空字符串，但count存在，则使用count判断
  if (!optionType && group.count) {
    // 如果count=1，通常是ByPlan模式
    if (group.count === 1) {
      return 'ByPlan';
    } else {
      return 'ByCount';
    }
  }

  // 默认返回ByCount
  return 'ByCount';
}

/**
 * 应用默认选择到可选组商品
 * @param group 可选组对象
 * @returns OptionalGroup 应用了默认选择的可选组对象
 */
export function applyDefaultSelections(group: OptionalGroup): OptionalGroup {
  if (!group || !group.products || group.products.length === 0) {
    return group;
  }

  // 创建结果副本，避免修改原始对象
  const result = { ...group, products: [...group.products] };

  // 获取目标选择数量
  const targetCount = result.optionCount || 0;
  if (targetCount <= 0) return result;

  // 获取标准化的选择模式
  const optionType = getNormalizedOptionType(result);

  // 修改为简单地选择第一个商品，不再按价格排序
  if (optionType === 'ByPlan') {
    // 按方案选择模式，选择前N种商品，每种商品1个
    const validProducts = result.products.filter(p => p.id);

    // 选择前targetCount种商品，每种商品选1个
    for (let i = 0; i < Math.min(targetCount, validProducts.length); i++) {
      const productId = validProducts[i].id;
      const productIndex = result.products.findIndex(p => p.id === productId);
      if (productIndex >= 0) {
        result.products[productIndex].selected_count = 1;
      }
    }
  } else {
    // 按数量选择模式，选择第一个商品并填满需要的数量
    if (result.products.length > 0) {
      const firstProduct = result.products[0];
      // 将全部数量分配给第一个商品
      firstProduct.selected_count = targetCount;
    }
  }

  return result;
}

/**
 * 验证by_count类型可选组的选择是否有效
 * - 总数必须大于等于optionCount，不强制要求精确等于
 *
 * @param group 可选组数据
 * @returns 选择是否有效
 */
export function validateByCountSelections(group: OptionalGroup): boolean {
  if (!isOptionalByCount(group)) return false;

  // 计算已选总数
  const totalSelected = group.products.reduce((sum, product) => {
    const count = product.count || 1;
    const selectedCount = product.selected_count || 0;
    return sum + count * selectedCount;
  }, 0);

  // 总数必须大于等于要求数量
  return totalSelected >= group.optionCount;
}

/**
 * 验证by_plan类型可选组的选择是否有效
 * - 必须选择exactly optionCount个不同商品
 *
 * @param group 可选组数据
 * @returns 选择是否有效
 */
export function validateByPlanSelections(group: OptionalGroup): boolean {
  if (!isOptionalByPlan(group)) return false;

  // 计算已选商品数量
  const selectedCount = group.products.filter(product => product.selected_count && product.selected_count > 0).length;

  // 必须精确等于要求数量
  return selectedCount === group.optionCount;
}

/**
 * 计算by_count类型可选组中商品的最大可选数量
 * 为第一个商品提供更宽松的限制，其他商品保持严格限制
 *
 * @param group 可选组数据
 * @param productIndex 商品索引
 * @param currentProduct 当前要计算的商品
 * @returns 最大可选数量
 */
export function getMaxAllowedCountForProduct(group: OptionalGroup, productIndex: number, currentProduct: PackageProductItem): number {
  if (!isOptionalByCount(group)) return 0;

  // 计算除当前商品外已选的总数
  const currentTotalWithoutSelf = group.products.reduce((sum, p, idx) => {
    if (idx === productIndex) return sum;
    return sum + (p.count || 1) * (p.selected_count || 0);
  }, 0);

  // 对于第一个商品，提供更宽松的限制
  if (productIndex === 0) {
    // 计算当前是否已经满足最低要求
    const currentTotalWithSelf = currentTotalWithoutSelf + (currentProduct.count || 1) * (currentProduct.selected_count || 0);

    // 如果已经满足最低要求，允许选择更多
    if (currentTotalWithSelf >= group.optionCount) {
      return 99; // 较大的数字作为实际上的"无限制"
    }
  }

  // 计算剩余可选数量并转换为当前商品数量单位
  const remainingCount = group.optionCount - currentTotalWithoutSelf;
  return Math.max(0, Math.ceil(remainingCount / (currentProduct.count || 1)));
}

/**
 * 将marketBill中的可选组格式转换为通用OptionalGroup格式
 * @param group 从marketBill中获取的可选组对象
 * @returns OptionalGroup 转换后的OptionalGroup格式
 */
export function convertMarketBillOptionalGroupToStandard(group: any): OptionalGroup | null {
  if (!group || !group.products) return null;

  return {
    name: group.name || '',
    optionType: group.optionType || group.type || 'by_count',
    optionCount: group.count || 0,
    products: group.products.map((product: any) => ({
      id: product.id,
      name: product.name || product.productName,
      count: 1, // 默认每个商品的计数单位为1
      selected_count: product.quantity || 0,
      currentPrice: product.price || 0,
      // 保留其他属性
      ...product
    }))
  };
}

/**
 * 将标准OptionalGroup格式应用回marketBill可选组格式
 * @param marketBillGroup marketBill中的可选组对象
 * @param standardGroup 标准OptionalGroup格式的可选组对象
 * @returns 更新后的marketBill可选组对象
 */
export function applyStandardSelectionsToMarketBill(marketBillGroup: any, standardGroup: OptionalGroup): any {
  if (!marketBillGroup || !marketBillGroup.products || !standardGroup || !standardGroup.products) {
    return marketBillGroup;
  }

  // 创建ID到选择数量的映射
  const idToSelectedCount = new Map();
  standardGroup.products.forEach(product => {
    idToSelectedCount.set(product.id, product.selected_count || 0);
  });

  // 更新marketBill的可选组商品
  const updatedProducts = marketBillGroup.products.map((product: any) => {
    if (idToSelectedCount.has(product.id)) {
      return {
        ...product,
        quantity: idToSelectedCount.get(product.id),
        tempQuantity: idToSelectedCount.get(product.id) // 同时更新tempQuantity以支持编辑弹窗
      };
    }
    return product;
  });

  // 更新整个可选组对象
  const updatedGroup = {
    ...marketBillGroup,
    products: updatedProducts
  };

  // 计算总选择数量
  const optionType = getNormalizedOptionType(updatedGroup);
  let selectedQuantity = 0;

  if (optionType === 'ByPlan') {
    // 按方案选择：统计选中的商品种类数
    selectedQuantity = updatedProducts.filter((p: any) => (p.quantity || 0) > 0).length;
  } else {
    // 按数量选择：统计选中的商品总数量
    selectedQuantity = updatedProducts.reduce((sum: number, p: any) => sum + (p.quantity || 0), 0);
  }

  // 更新selectedQuantity字段
  updatedGroup.selectedQuantity = selectedQuantity;

  return updatedGroup;
}

/**
 * 整合的一键应用默认选择功能
 * @param marketBillGroup marketBill中的可选组对象
 * @returns 应用了默认选择的marketBill可选组对象
 */
export function autoSelectOptionalGroup(marketBillGroup: any): any {
  if (!marketBillGroup || !marketBillGroup.products) return marketBillGroup;

  // 1. 转换为标准格式
  const standardGroup = convertMarketBillOptionalGroupToStandard(marketBillGroup);
  if (!standardGroup) return marketBillGroup;

  // 2. 应用默认选择
  const standardGroupWithSelections = applyDefaultSelections(standardGroup);

  // 3. 转换回marketBill格式
  return applyStandardSelectionsToMarketBill(marketBillGroup, standardGroupWithSelections);
}
