/**
 * viewport缩放工具
 * 用于小屏幕设备自动缩放页面，保持完整布局
 */

/**
 * 设置viewport缩放，使得960px的设计稿在任何屏幕上都完整显示
 * @param {number} designWidth 设计稿宽度，默认为960px
 * @param {number} minScale 最小缩放比例，防止过度缩小
 */
export function setupViewportScale(designWidth: number = 960, minScale: number = 0.25): void {
  // 初始设置
  setViewport(designWidth, minScale);

  // 窗口大小变化时重新设置
  window.addEventListener('resize', () => {
    setViewport(designWidth, minScale);
  });
}

/**
 * 设置viewport的内容
 * @param {number} designWidth 设计稿宽度
 * @param {number} minScale 最小缩放比例
 */
function setViewport(designWidth: number, minScale: number): void {
  // 计算缩放比例
  let scale = window.innerWidth / designWidth;

  // 限制最小缩放比例
  scale = Math.max(scale, minScale);

  // 查找或创建viewport meta标签
  let viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
  if (!viewport) {
    viewport = document.createElement('meta') as HTMLMetaElement;
    viewport.name = 'viewport';
    document.head.appendChild(viewport);
  }

  // 设置viewport内容
  viewport.content = `width=${designWidth}, initial-scale=${scale}, maximum-scale=${scale}, user-scalable=no`;
}

/**
 * 禁用viewport缩放，恢复标准响应式行为
 */
export function disableViewportScale(): void {
  const viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
  if (viewport) {
    viewport.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
  }
}

/**
 * 自动检测设备尺寸并决定是否应用viewport缩放
 * @param {number} breakpoint 触发viewport缩放的断点宽度，默认为960
 * @param {number} designWidth 设计稿宽度，默认为960px
 * @param {number} minScale 最小缩放比例，防止过度缩小
 */
export function autoViewportScale(breakpoint: number = 960, designWidth: number = 960, minScale: number = 0.25): void {
  const checkWidth = (): void => {
    if (window.innerWidth < breakpoint) {
      setViewport(designWidth, minScale);
    } else {
      disableViewportScale();
    }
  };

  // 初始检查
  checkWidth();

  // 窗口大小变化时重新检查
  window.addEventListener('resize', checkWidth);
}
