/**
 * 将分转换为元
 * @param fen 分为单位的金额
 * @returns 元为单位的金额
 */
export const convertToYuan = (fen: any): number => {
  // console.log('convertToYuan fen:', fen, typeof fen)
  if (typeof fen !== 'number') {
    console.error('convertToYuan fen:', fen, typeof fen);
    return fen || 0;
  }
  return fen / 100;
};

/**
 * 将元转换为分
 * @param yuan 元为单位的金额
 * @returns 分为单位的金额
 */
export const convertToFen = (yuan: any): number => {
  if (typeof yuan !== 'number') {
    console.error('convertToFen yuan:', yuan, typeof yuan);
    return yuan || 0;
  }
  return Math.round(yuan * 100);
};

/**
 * 将分转换为元并格式化为字符串(保留2位小数)
 * @param fen 分为单位的金额
 * @returns 格式化后的金额字符串
 */
export const formatYuan = (fen: any): string => {
  if (fen === undefined || fen === null) {
    return '--';
  }
  if (typeof fen !== 'number') {
    console.error('formatYuan fen:', fen, typeof fen);
    return fen || '';
  }
  return (fen / 100).toFixed(2);
};

/**
 * 将分转换为元并添加货币符号
 * @param fen 分为单位的金额
 * @param symbol 货币符号，默认为 ¥
 * @returns 带货币符号的金额字符串
 */
export const formatYuanWithSymbol = (fen: any, symbol: string = '¥'): string => {
  if (typeof fen !== 'number') {
    // console.error('formatYuanWithSymbol fen:', fen, typeof fen)
    return fen || '';
  }
  return `${symbol}${formatYuan(fen)}`;
};

/**
 * 折扣抹分计算工具：先整数相乘再除100，最后Math.floor，避免浮点误差
 * @param amountFen   原始金额（分）
 * @param percent     折扣率 0-100，整数
 * @returns 折扣后金额（分，已抹分）
 */
export const floorDiscount = (amountFen: number, percent: number): number => {
  if (typeof amountFen !== 'number' || typeof percent !== 'number') {
    console.error('floorDiscount 参数类型错误', amountFen, percent);
    return amountFen;
  }
  return Math.floor((amountFen * percent) / 100);
};
