import request from "@/utils/request";

// 请求参数类型
export interface GetAppUpgradeByVersionReqDto {
  /** 客户端类型 */
  clientType: string;
  /** 环境类型 */
  environment: string;
  /** 当前版本号 */
  versionCode: number;
}

// 升级包信息类型
export interface AppUpgradeVO {
  /** 升级包ID */
  id?: string;
  /** 客户端类型 */
  clientType?: string;
  /** 环境类型 */
  environment?: string;
  /** 版本号 */
  versionCode?: number;
  /** 版本名称 */
  versionName?: string;
  /** 升级标题 */
  upgradeTitle?: string;
  /** 升级内容 */
  upgradeContent?: string;
  /** 下载地址 */
  downloadUrl?: string;
  /** 文件大小（字节） */
  fileSize?: number;
  /** 文件MD5 */
  fileMd5?: string;
  /** 是否强制升级 */
  forceUpgrade?: boolean;
  /** 状态：0-启用，1-禁用 */
  state?: number;
  /** 是否启用 */
  isEnabled?: boolean;
  /** 是否禁用 */
  isDisabled?: boolean;
  /** 创建时间（毫秒时间戳） */
  createTime?: number;
  /** 更新时间（毫秒时间戳） */
  updateTime?: number;
  /** 最新激活的H5版本URL */
  latestH5Url?: string;
  /** 最新激活的H5版本标签 */
  latestH5Tag?: string;
}

// 版本检查响应类型
export interface VersionCheckVO {
  /** 是否有升级 */
  hasUpgrade: boolean;
  /** 当前版本信息 */
  currentVersion?: AppUpgradeVO;
  /** 最新版本信息 */
  latestVersion?: AppUpgradeVO;
}

// API 响应类型
export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// Mock 函数保留供调试使用

/**
 * Mock 版本检查数据 (仅供调试使用，生产环境请勿调用)
 */
export function getMockVersionCheckData(body: GetAppUpgradeByVersionReqDto): VersionCheckVO {
  const { clientType, environment, versionCode } = body;
  
  console.log('[Mock] 收到版本检查请求:', { clientType, environment, versionCode });

  // 模拟当前版本信息
  const currentVersion: AppUpgradeVO = {
    id: 'current-version-001',
    clientType,
    environment,
    versionCode,
    versionName: '1.0.' + versionCode,
    upgradeTitle: '当前版本',
    upgradeContent: '当前正在使用的版本',
    downloadUrl: '',
    fileSize: 0,
    fileMd5: '',
    forceUpgrade: false,
    state: 0,
    isEnabled: true,
    isDisabled: false,
    createTime: Date.now() - 86400000, // 1天前
    updateTime: Date.now() - 86400000,
    latestH5Url: 'http://h5.example.com/v1.0.' + versionCode,
    latestH5Tag: 'v1.0.' + versionCode
  };

  // 根据不同场景返回不同的 mock 数据
  // 可以通过修改这里的逻辑来测试不同场景
  
  // 场景1: 有普通升级 (versionCode < 10)
  if (versionCode < 10) {
    const latestVersion: AppUpgradeVO = {
      id: 'latest-version-002',
      clientType,
      environment,
      versionCode: versionCode + 3, // 模拟新版本号
      versionName: '1.0.' + (versionCode + 3),
      upgradeTitle: '发现新版本 v1.0.' + (versionCode + 3),
      upgradeContent: `
        <h4>本次更新内容：</h4>
        <ul>
          <li>修复了多个已知问题</li>
          <li>优化了用户体验</li>
          <li>新增了版本自动升级功能</li>
          <li>提升了系统稳定性</li>
        </ul>
        <p><strong>建议立即更新以获得更好的使用体验</strong></p>
      `,
      downloadUrl: getMockDownloadUrl(clientType),
      fileSize: 52428800, // 50MB
      fileMd5: 'abc123def456789mock',
      forceUpgrade: false, // 普通升级
      state: 0,
      isEnabled: true,
      isDisabled: false,
      createTime: Date.now() - 3600000, // 1小时前
      updateTime: Date.now() - 3600000,
      latestH5Url: 'http://h5.example.com/v1.0.' + (versionCode + 3),
      latestH5Tag: 'v1.0.' + (versionCode + 3)
    };

    return {
      hasUpgrade: true,
      currentVersion,
      latestVersion
    };
  }
  
  // 场景2: 强制升级 (versionCode >= 10 && versionCode < 20)
  else if (versionCode >= 10 && versionCode < 20) {
    const latestVersion: AppUpgradeVO = {
      id: 'force-upgrade-003',
      clientType,
      environment,
      versionCode: versionCode + 5,
      versionName: '2.0.' + (versionCode - 10),
      upgradeTitle: '重要安全更新 - 必须升级',
      upgradeContent: `
        <h4 style="color: #f56c6c;">⚠️ 重要安全更新</h4>
        <ul>
          <li><strong>修复了重要安全漏洞</strong></li>
          <li>更新了加密算法</li>
          <li>优化了数据传输安全</li>
          <li>升级了第三方依赖库</li>
        </ul>
        <p style="color: #f56c6c;"><strong>此版本为强制更新，必须升级后才能继续使用</strong></p>
      `,
      downloadUrl: getMockDownloadUrl(clientType),
      fileSize: 67108864, // 64MB
      fileMd5: 'force123upgrade456mock',
      forceUpgrade: true, // 强制升级
      state: 0,
      isEnabled: true,
      isDisabled: false,
      createTime: Date.now() - 1800000, // 30分钟前
      updateTime: Date.now() - 1800000,
      latestH5Url: 'http://h5.example.com/v2.0.' + (versionCode - 10),
      latestH5Tag: 'v2.0.' + (versionCode - 10)
    };

    return {
      hasUpgrade: true,
      currentVersion,
      latestVersion
    };
  }
  
  // 场景3: 无升级 (versionCode >= 20)
  else {
    return {
      hasUpgrade: false,
      currentVersion,
      latestVersion: undefined
    };
  }
}

/**
 * 获取 Mock 下载地址
 */
function getMockDownloadUrl(clientType: string): string {
  switch (clientType) {
    case 'CASHIER_ANDROID':
      return 'https://docerp.ktvsky.com/assets/downloads/Thunder%20ERP%20Android%201.0.4.apk';
    case 'CASHIER_WINDOWS':
      return 'https://docerp.ktvsky.com/assets/downloads/Thunder%20ERP%20Setup%201.0.4-b1.20250625.exe';
    case 'MOBILE_ORDER':
      return 'https://docerp.ktvsky.com/assets/downloads/Thunder%20ERP%20Mobile%201.0.4.apk';
    default:
      return 'https://docerp.ktvsky.com/assets/downloads/Thunder%20ERP%20Setup%201.0.4-b1.20250625.exe';
  }
}

/**
 * 检查应用版本升级
 * @param body 请求参数
 * @param options 请求选项
 * @returns 版本检查结果
 */
export async function checkAppUpgradeVersion(
  body: GetAppUpgradeByVersionReqDto,
  options?: { [key: string]: any }
): Promise<VersionCheckVO> {
  
  // 真实 API 调用
  const response = await request<VersionCheckVO>('/api/app-upgrade/version', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
  return response.data;
}