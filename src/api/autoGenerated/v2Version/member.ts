import request from "@/utils/request";
import * as Types from "../shared/types";

/** 获取会员卡等级列表 获取指定门店可用的会员卡等级列表 GET /api/v2/member/card-levels */
export async function getApiV2MemberCardLevels(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getApiV2MemberCardLevelsParams,
  options?: { [key: string]: any }
) {
  return request<Types.CardLevelsVO>('/api/v2/member/card-levels', {
    method: 'GET',
    params: {
      ...params
    },
    ...(options || {})
  });
}

/** 注册会员 注册新会员，创建会员信息并关联会员卡 POST /api/v2/member/register */
export async function postApiV2MemberRegister (body: Types.MemberRegisterDTO, options?: { [key: string]: any }) {
  return request<Types.RegisterMemberVO>('/api/v2/member/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 通过实体卡注册会员 通过实体卡注册会员，验证实体卡有效性并创建会员信息 POST /api/v2/member/register/physical-card */
export async function postApiV2MemberRegisterPhysicalCard (body: Types.PhysicalCardRegisterDTO, options?: { [key: string]: any }) {
  return request<Types.RegisterMemberVO>('/api/v2/member/register/physical-card', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 验证会员信息 验证会员信息的有效性，如手机号是否已注册等 POST /api/v2/member/verify */
export async function postApiV2MemberVerify (body: Types.MemberVerifyDTO, options?: { [key: string]: any }) {
  return request<Types.VerifyResultVO>('/api/v2/member/verify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
