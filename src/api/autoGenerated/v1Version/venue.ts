import request from "@/utils/request";
import * as Types from "../shared/types";

/** 审核门店 商务审核门店 POST /api/v1/venue/audit */
export async function postApiV1VenueAudit (body: Types.AuditVenueReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v1/venue/audit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 激活门店授权 使用授权码激活门店授权 POST /api/v1/venue/auth/activate */
export async function postApiV1VenueAuthActivate (body: Types.ActivateAuthReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v1/venue/auth/activate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 生成门店授权码 为指定门店生成授权码 POST /api/v1/venue/auth/generate */
export async function postApiV1VenueAuthGenerate (body: Types.GenerateAuthReqDto, options?: { [key: string]: any }) {
  return request<string>('/api/v1/venue/auth/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
