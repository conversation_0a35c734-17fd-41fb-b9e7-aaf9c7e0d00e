import request from "@/utils/request";
import * as Types from "../shared/types";

/** 会员开卡 会员开卡 POST /api/v3/member/open-card */
export async function postApiV3MemberOpenCard (body: Types.V3OpenCardReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberRechargeBillVO[]>('/api/v3/member/open-card', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 乐刷支付回调 乐刷支付回调 POST /api/v3/member/pay/callback */
export async function postApiV3MemberPayCallback (body: string, options?: { [key: string]: any }) {
  return request<any>('/api/v3/member/pay/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 会员支付查询 会员支付查询 POST /api/v3/member/pay/query */
export async function postApiV3MemberPayQuery (body: Types.V3QueryMemberPayQueryReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberRechargeBillVO>('/api/v3/member/pay/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 会员充值账单查询 会员充值账单查询 POST /api/v3/member/recharge */
export async function postApiV3MemberRecharge (body: Types.V3QueryMemberRechargeReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberRechargeBillExVO[]>('/api/v3/member/recharge', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 乐刷退款回调 乐刷退款回调 POST /api/v3/member/refund/callback */
export async function postApiV3MemberRefundCallback (body: string, options?: { [key: string]: any }) {
  return request<any>('/api/v3/member/refund/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员 查询会员 POST /api/v3/member-recharge-bill/query */
export async function postApiV3MemberRechargeBillQuery (body: Types.QueryMemberReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberRechargeBillExVO[]>('/api/v3/member-recharge-bill/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 冻结 冻结 POST /api/v3/member-card/frozen */
export async function postApiV3MemberCardFrozen (body: Types.V3FrozenMemberCardReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v3/member-card/frozen', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员列表 查询会员列表 POST /api/v3/member-card/list */
export async function postApiV3MemberCardList (body: Types.V3QueryMemberListReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberVO[]>('/api/v3/member-card/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员 查询会员 POST /api/v3/member-card/query */
export async function postApiV3MemberCardQuery (body: Types.V3QueryMemberListReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberVO[]>('/api/v3/member-card/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 续卡 续卡 POST /api/v3/member-card/renew */
export async function postApiV3MemberCardRenew (body: Types.V3RenewMemberCardReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v3/member-card/renew', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员列表-手机号查询 查询会员列表-手机号查询 POST /api/v3/member-card/search */
export async function postApiV3MemberCardSearch (body: Types.V3QueryMemberListPhoneReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberVO[]>('/api/v3/member-card/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 解冻 解冻 POST /api/v3/member-card/unfrozen */
export async function postApiV3MemberCardUnfrozen (body: Types.V3UnfrozenMemberCardReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v3/member-card/unfrozen', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新会员 更新会员 POST /api/v3/member-card/update */
export async function postApiV3MemberCardUpdate (body: Types.UpdateMemberReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberVO>('/api/v3/member-card/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加会员卡操作记录 添加会员卡操作记录 POST /api/v3/member-card-operation/add */
export async function postApiV3MemberCardOperationAdd (body: Types.AddMemberCardOperationReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberCardOperationVO>('/api/v3/member-card-operation/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除会员卡操作记录 删除会员卡操作记录 POST /api/v3/member-card-operation/delete */
export async function postApiV3MemberCardOperationOpenApiDelete (body: Types.DeleteMemberCardOperationReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v3/member-card-operation/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员卡操作记录列表 查询会员卡操作记录列表 POST /api/v3/member-card-operation/list */
export async function postApiV3MemberCardOperationList (body: Types.QueryMemberCardOperationListReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoMemberCardOperationVO>('/api/v3/member-card-operation/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员卡操作记录列表 查询会员卡操作记录列表 POST /api/v3/member-card-operation/page */
export async function postApiV3MemberCardOperationPage (body: Types.QueryMemberCardOperationReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoMemberCardOperationVO>('/api/v3/member-card-operation/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员卡操作记录 查询会员卡操作记录 POST /api/v3/member-card-operation/query */
export async function postApiV3MemberCardOperationQuery (body: Types.QueryMemberCardOperationReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberCardOperationVO[]>('/api/v3/member-card-operation/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新会员卡操作记录 更新会员卡操作记录 POST /api/v3/member-card-operation/update */
export async function postApiV3MemberCardOperationUpdate (body: Types.UpdateMemberCardOperationReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberCardOperationVO>('/api/v3/member-card-operation/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据ID查询会员卡消费记录 根据ID查询会员卡消费记录 GET /api/v3/member-card-consume/${param0} */
export async function getApiV3MemberCardConsumeId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getApiV3MemberCardConsumeIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<Types.MemberCardConsumeVO>(`/api/v3/member-card-consume/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {})
  });
}

/** 查询会员卡消费记录列表 查询会员卡消费记录列表 POST /api/v3/member-card-consume/list */
export async function postApiV3MemberCardConsumeList (body: Types.QueryMemberCardConsumeReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberCardConsumeUnionVO[]>('/api/v3/member-card-consume/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询会员卡消费记录列表 查询会员卡消费记录列表 POST /api/v3/member-card-consume/list/old */
export async function postApiV3MemberCardConsumeListOld (body: Types.QueryMemberCardConsumeReqDto, options?: { [key: string]: any }) {
  return request<Types.MemberCardConsumeVO[]>('/api/v3/member-card-consume/list/old', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 分页查询会员卡消费记录 分页查询会员卡消费记录 POST /api/v3/member-card-consume/page */
export async function postApiV3MemberCardConsumePage (body: Types.QueryMemberCardConsumeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoMemberCardConsumeVO>('/api/v3/member-card-consume/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
