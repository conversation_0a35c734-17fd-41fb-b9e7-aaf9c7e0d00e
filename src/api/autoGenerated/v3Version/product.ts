import request from "@/utils/request";
import * as Types from "../shared/types";

/** 查询产品 查询产品 POST /api/v3/product/query */
export async function postApiV3ProductQuery (body: Types.V3QueryProductReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductVO[]>('/api/v3/product/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
