import request from "@/utils/request";
import * as Types from "../shared/types";

/** 新增呼叫消息 新增呼叫消息 POST /api/v3/call/add */
export async function postApiV3CallAdd (body: Types.V3AddCallReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO>('/api/v3/call/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 取消呼叫消息 取消呼叫消息 POST /api/v3/call/cancel */
export async function postApiV3CallCancel (body: Types.V3CancelCallReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO>('/api/v3/call/cancel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 处理呼叫消息 处理呼叫消息 POST /api/v3/call/deal */
export async function postApiV3CallDeal (body: Types.V3DealCallReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO>('/api/v3/call/deal', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询呼叫消息列表 查询呼叫消息列表 POST /api/v3/call/list */
export async function postApiV3CallList (body: Types.V3ListCallLastReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO[]>('/api/v3/call/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询呼叫类型列表 查询呼叫类型列表 POST /api/v3/call/types/list */
export async function postApiV3CallTypesList (body: Types.V3ListCallTypesReqDto, options?: { [key: string]: any }) {
  return request<Types.CallTypesVO[]>('/api/v3/call/types/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询未处理的呼叫消息列表 查询未处理的呼叫消息列表 POST /api/v3/call/unprocessed/list */
export async function postApiV3CallUnprocessedList (body: Types.V3ListCallUnprocessedReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO[]>('/api/v3/call/unprocessed/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
