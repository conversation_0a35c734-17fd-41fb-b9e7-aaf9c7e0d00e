import request from "@/utils/request";
import * as Types from "../shared/types";

/** 点单-后付 点单-后付 POST /api/v3/order/additional-order */
export async function postApiV3OrderAdditionalOrder (body: Types.V3AddOrderAdditionalReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderVO>('/api/v3/order/additional-order', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 点单-立结 点单-立结 POST /api/v3/order/additional-order-pay */
export async function postApiV3OrderAdditionalOrderPay (body: Types.V3AddOrderAdditionalPayReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderVO>('/api/v3/order/additional-order-pay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 清扫完成 清扫完成 POST /api/v3/order/clean-room-finish */
export async function postApiV3OrderCleanRoomFinish (body: Types.V3QueryOrderCleanRoomFinishReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/clean-room-finish', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 关房 关房 POST /api/v3/order/close-room */
export async function postApiV3OrderCloseRoom (body: Types.V3AddOrderCloseRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/close-room', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 结束时长消费 结束时长消费 POST /api/v3/order/end-time-consume */
export async function postApiV3OrderEndTimeConsume (body: Types.V3EndTimeConsumeReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/end-time-consume', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 商品金额计算 商品金额计算 POST /api/v3/order/fee-calculate */
export async function postApiV3OrderFeeCalculate (body: Types.V3QueryOrderPayCalculateReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderPayCalculateResultVO[]>('/api/v3/order/fee-calculate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 赠送商品 赠送商品 POST /api/v3/order/gift-product */
export async function postApiV3OrderGiftProduct (body: Types.V3OrderGiftProductReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderVO>('/api/v3/order/gift-product', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 赠送时长 赠送时长 POST /api/v3/order/gift-time */
export async function postApiV3OrderGiftTime (body: Types.V3OrderGiftTimeReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderVO>('/api/v3/order/gift-time', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 解锁房 解锁房 POST /api/v3/order/lock-room */
export async function postApiV3OrderLockRoom (body: Types.V3UnlockRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/lock-room', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 并房 并房 POST /api/v3/order/merge-room */
export async function postApiV3OrderMergeRoom (body: Types.V3MergeRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/merge-room', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 小程序支付 小程序支付 POST /api/v3/order/mini-app-pay */
export async function postApiV3OrderMiniAppPay (body: Types.V3MiniAppPayReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderVO>('/api/v3/order/mini-app-pay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 开台-后付 开台-后付 POST /api/v3/order/open */
export async function postApiV3OrderOpen (body: Types.V3AddOrderOpenReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/open', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 续房-后付 续房-后付 POST /api/v3/order/open-continue */
export async function postApiV3OrderOpenContinue (body: Types.V3AddOrderOpenContinueReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/open-continue', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 续房-立结 续房-立结 POST /api/v3/order/open-continue-pay */
export async function postApiV3OrderOpenContinuePay (body: Types.V3AddOrderOpenContinuePayReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/open-continue-pay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 开台-立结 开台-立结 POST /api/v3/order/open-pay */
export async function postApiV3OrderOpenPay (body: Types.V3AddOrderOpenPayReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/open-pay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 重开 重开 POST /api/v3/order/order-reopen */
export async function postApiV3OrderOrderReopen (body: Types.V3OrderReopenReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/order-reopen', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 支付-后付 支付-后付 POST /api/v3/order/pay */
export async function postApiV3OrderPay (body: Types.V3QueryOrderPayReqDto, options?: { [key: string]: any }) {
  return request<Types.PayResultVO>('/api/v3/order/pay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 乐刷支付回调 乐刷支付回调 POST /api/v3/order/pay/callback */
export async function postApiV3OrderPayCallback (body: Types.V3QueryOrderPayReqDto, options?: { [key: string]: any }) {
  return request<Types.PayResultVO>('/api/v3/order/pay/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 乐刷支付查询 乐刷支付查询 POST /api/v3/order/pay/query */
export async function postApiV3OrderPayQuery (body: Types.V3QueryOrderPayQueryReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderPayQueryResultVO>('/api/v3/order/pay/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 商品销售统计 商品销售统计 POST /api/v3/order/query/product/sales */
export async function postApiV3OrderQueryProductSales (body: Types.V3OrderQueryProductSalesReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderProductSalesVO[]>('/api/v3/order/query/product/sales', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 退款 退款 POST /api/v3/order/refund-do */
export async function postApiV3OrderRefundDo (body: Types.V3QueryOrderRefundReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderRefundInfoVO[]>('/api/v3/order/refund-do', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 退款视图 退款视图 POST /api/v3/order/refund-view */
export async function postApiV3OrderRefundView (body: Types.V3QueryOrderRefundViewReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderRefundViewVO>('/api/v3/order/refund-view', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 乐刷退款回调 乐刷退款回调 POST /api/v3/order/refund/callback */
export async function postApiV3OrderRefundCallback (body: Types.V3QueryOrderPayReqDto, options?: { [key: string]: any }) {
  return request<Types.PayResultVO>('/api/v3/order/refund/callback', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 退房费 退房费 POST /api/v3/order/room-fee-refund-do */
export async function postApiV3OrderRoomFeeRefundDo (body: Types.V3QueryOrderRoomFeeRefundReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderRefundInfoVO[]>('/api/v3/order/room-fee-refund-do', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 互换包房 互换包房 POST /api/v3/order/swap-room */
export async function postApiV3OrderSwapRoom (body: Types.V3SwapRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/swap-room', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 转台 转台 POST /api/v3/order/transfer-room */
export async function postApiV3OrderTransferRoom (body: Types.V3TransferRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/order/transfer-room', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
