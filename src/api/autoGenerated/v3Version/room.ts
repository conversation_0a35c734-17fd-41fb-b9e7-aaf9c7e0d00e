import request from "@/utils/request";
import * as Types from "../shared/types";

/** 获取房间故障 获取房间故障 POST /api/v3/room/fault */
export async function postApiV3RoomFault (body: Types.V3QueryRoomFaultReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomFaultVO>('/api/v3/room/fault', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取房间信息 获取房间信息 POST /api/v3/room/info/qr-code */
export async function postApiV3RoomInfoQrCode (body: Types.V3QueryRoomInfoByQRCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.SessionVO>('/api/v3/room/info/qr-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取房间开放视图 获取房间开放视图 POST /api/v3/room/open-view */
export async function postApiV3RoomOpenView (body: Types.QueryRoomStageReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomVO>('/api/v3/room/open-view', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取房间开放视图 获取房间开放视图 POST /api/v3/room/stage */
export async function postApiV3RoomStage (body: Types.V3QueryRoomStageReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomVO>('/api/v3/room/stage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
