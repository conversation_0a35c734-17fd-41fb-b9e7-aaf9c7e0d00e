import request from "@/utils/request";
import * as Types from "../shared/types";

/** 查询班次报告营收报告 查询班次报告营收报告 POST /api/v3/shift-report/get-income-daily */
export async function postApiV3ShiftReportGetIncomeDaily (body: Types.QueryShiftReportGetIncomeDailyReqDto, options?: { [key: string]: any }) {
  return request<Types.ShiftReportDaily[]>('/api/v3/shift-report/get-income-daily', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 账单详情 账单详情 POST /api/v3/shift-report/bill/detail */
export async function postApiV3ShiftReportBillDetail (body: Types.QueryShiftReportBillDetailReqDto, options?: { [key: string]: any }) {
  return request<Types.ShiftReportBillDetailVO>('/api/v3/shift-report/bill/detail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 交班 交班 POST /api/v3/shift-report/handover */
export async function postApiV3ShiftReportHandover (body: Types.V3ShiftReportHandOverReqDto, options?: { [key: string]: any }) {
  return request<string[]>('/api/v3/shift-report/handover', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 交班记录详情 交班记录详情 POST /api/v3/shift-report/handover/detail */
export async function postApiV3ShiftReportHandoverDetail (body: Types.V3QueryShiftReportHandOverDetailReqDto, options?: { [key: string]: any }) {
  return request<Types.ShiftReportDaily[]>('/api/v3/shift-report/handover/detail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 交班历史 交班历史 POST /api/v3/shift-report/handover/history */
export async function postApiV3ShiftReportHandoverHistory (body: Types.QueryShiftHandoverFormReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoShiftHandoverFormVO>('/api/v3/shift-report/handover/history', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
