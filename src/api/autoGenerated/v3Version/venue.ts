import request from "@/utils/request";
import * as Types from "../shared/types";

/** 查询业务报表 查询业务报表 POST /api/v3/venue/query-business-report */
export async function postApiV3VenueQueryBusinessReport (body: Types.V3QueryBuisinessReportReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueBusinessReportVO>('/api/v3/venue/query-business-report', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
