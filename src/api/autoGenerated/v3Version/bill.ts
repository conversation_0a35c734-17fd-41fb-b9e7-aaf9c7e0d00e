import request from "@/utils/request";
import * as Types from "../shared/types";

/** 账单还原 账单还原 POST /api/v3/bill/bill-back */
export async function postApiV3BillBillBack (body: Types.V3BillBackReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/v3/bill/bill-back', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 账单还原视图 账单还原视图 POST /api/v3/bill/bill-back-view */
export async function postApiV3BillBillBackView (body: Types.V3QueryBillBackViewReqDto, options?: { [key: string]: any }) {
  return request<Types.PayBillUnionVO>('/api/v3/bill/bill-back-view', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 账单查询 账单查询 POST /api/v3/bill/query */
export async function postApiV3BillQuery (body: Types.V3BillQueryReqDto, options?: { [key: string]: any }) {
  return request<Types.PayBillExtVO[]>('/api/v3/bill/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 根据sessionId查询账单 根据sessionId查询账单 POST /api/v3/bill/query-by-session */
export async function postApiV3BillQueryBySession (body: Types.V3BillQueryBySessionReqDto, options?: { [key: string]: any }) {
  return request<Types.PayBillVO[]>('/api/v3/bill/query-by-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
