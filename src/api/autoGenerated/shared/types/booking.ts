// booking相关类型定义

export type AddBookingReqDto = {
    /** 预抵时间 */
    arrivalTime?: number;
    /** 客户名称 */
    customerName?: string;
    /** 客户电话 */
    customerPhone?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 性别 0 男 1 女 3 未知 */
    gender?: string;
    /** 会员卡 */
    memberCard?: string;
    /** 会员卡id */
    memberCardId?: string;
    /** 开台方案 */
    openTablePlan?: string;
    /** 备注 */
    remark?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 门店ID */
    venueId?: string;
  };

export type BookingVO = {
    /** 预抵时间 */
    arrivalTime?: number;
    /** 创建时间 */
    ctime?: number;
    /** 客户名称 */
    customerName?: string;
    /** 客户电话 */
    customerPhone?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 性别 0 男 1 女 3 未知 */
    gender?: string;
    /** ID */
    id?: string;
    /** 会员卡 */
    memberCard?: string;
    /** 会员卡id */
    memberCardId?: string;
    /** 开台方案 */
    openTablePlan?: string;
    /** 备注 */
    remark?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 状态 */
    state?: number;
    /** 状态 0 未使用 1 已使用 2 已取消 */
    status?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type CancelBookingReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 取消原因 */
    cancelReason?: string;
  };

export type DeleteBookingReqDto = {
    /** ID */
    id?: string;
  };

export type PageVOArrayVoBookingVO = {
    data?: BookingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type QueryBookingReqDto = {
    /** 预抵时间 */
    arrivalTime?: number;
    /** 预抵时间-结束 */
    arrivalTimeEnd?: number;
    /** 预抵时间-开始 */
    arrivalTimeStart?: number;
    /** 创建时间 */
    createTime?: number;
    /** 创建时间-结束 */
    createTimeEnd?: number;
    /** 创建时间-开始 */
    createTimeStart?: number;
    /** 客户名称 */
    customerName?: string;
    /** 客户电话 */
    customerPhone?: string;
    /** 客户电话或名称 */
    customerPhoneOrName?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 性别 0 男 1 女 3 未知 */
    gender?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 会员卡 */
    memberCard?: string;
    /** 会员卡id */
    memberCardId?: string;
    /** 开台方案 */
    openTablePlan?: string;
    /** 排序 */
    orderBy?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间ID列表 */
    roomIds?: string[];
    /** 房间名称 */
    roomName?: string;
    /** 状态 0 未开始 1 进行中 2 已结束 */
    status?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ResultArrayVoBookingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: BookingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoBookingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: BookingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoBookingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoBookingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type SearchBookingReqDto = {
    /** 预抵时间 */
    arrivalTime?: number;
    /** 预抵时间-结束 */
    arrivalTimeEnd?: number;
    /** 预抵时间-开始 */
    arrivalTimeStart?: number;
    /** 创建时间 */
    createTime?: number;
    /** 创建时间-结束 */
    createTimeEnd?: number;
    /** 创建时间-开始 */
    createTimeStart?: number;
    /** 客户名称 */
    customerName?: string;
    /** 客户电话 */
    customerPhone?: string;
    /** 客户电话或名称 */
    customerPhoneOrName?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 性别 0 男 1 女 3 未知 */
    gender?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 会员卡 */
    memberCard?: string;
    /** 会员卡id */
    memberCardId?: string;
    /** 开台方案 */
    openTablePlan?: string;
    /** 排序 */
    orderBy?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间ID列表 */
    roomIds?: string[];
    /** 房间名称 */
    roomName?: string;
    /** 状态 0 未开始 1 进行中 2 已结束 */
    status?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateBookingReqDto = {
    /** 预抵时间 */
    arrivalTime?: number;
    /** 客户名称 */
    customerName?: string;
    /** 客户电话 */
    customerPhone?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 性别 0 男 1 女 3 未知 */
    gender?: string;
    /** ID */
    id?: string;
    /** 会员卡 */
    memberCard?: string;
    /** 会员卡id */
    memberCardId?: string;
    /** 开台方案 */
    openTablePlan?: string;
    /** 备注 */
    remark?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 门店ID */
    venueId?: string;
  };
