// price相关类型定义
import { AreaConfig, ChannelConfig, BuyoutTimeConfig, DateConfig, WeekdayConfig } from './other';
import { ProductPlan, ProductVO, ProductPackageVO, PricePlanUnionProductVO } from './product';
import { RoomTypeConfig } from './room';

export type AddDouyinGroupBuyingPlanReqDto = {
    /** 绑定状态 */
    bindingStatus?: string;
    /** 计划ID */
    planId?: string;
    /** 计划名称 */
    planName?: string;
    /** 价格 */
    price?: number;
    /** 产品信息(JSON) */
    productInfo?: string;
  };

export type AddOrderPricePlanReqDto = {
    /** 买钟时长 */
    buyMinute?: number;
    /** 消费模式 */
    consumptionMode?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单ID */
    orderNo?: string;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 买钟类型 */
    timeChargeMode?: string;
    /** 买钟价格类型 */
    timeChargeType?: string;
  };

export type AddPricePlanReqDto = {
    /** 活动价格 */
    activityFee?: number;
    /** 提前禁用时长 */
    advanceDisableDuration?: number;
    /** 区域会员价格 */
    areaMemberPrices?: string;
    /** 区域价格 */
    areaPrices?: string;
    /** 基础房费 */
    baseRoomFee?: number;
    /** 生日价格 */
    birthdayFee?: number;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 消费时间段 */
    consumptionTimeSlots?: string;
    /** 结束日期 */
    dayEnd?: string;
    /** 开始日期 */
    dayStart?: string;
    /** 买钟优惠时长 */
    discountDuration?: number;
    /** 优惠模式 */
    discountMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 买断持续时长 */
    duration?: number;
    /** 示例产品 */
    exampleProducts?: string;
    /** 免费产品 */
    freeProducts?: string;
    /** 赠送时长 */
    giftDuration?: number;
    /** 团购价格 */
    groupBuyFee?: number;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrices?: string;
    /** 结束时间 */
    hourMinuteEnd?: string;
    /** 开始时间 */
    hourMinuteStart?: string;
    /** 是否指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 多余部分是否计入房费 */
    isExcessIncluded?: boolean;
    /** 累计最高可抵扣金额 */
    maxDeductibleAmount?: number;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 超市消费最低金额 */
    minimumConsumption?: number;
    /** 价格方案名称 */
    name?: string;
    /** 可选产品 */
    optionalProducts?: string;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: string;
    /** 房费可抵扣的商品分类 */
    roomChargeGoods?: string;
    /** 房间类型 */
    roomType?: string;
    /** 选中的投放区域 */
    selectedAreas?: string;
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间类型 */
    timeType?: string;
    /** 门店id */
    venueId?: string;
    /** 星期 */
    weeks?: string;
  };

export type AddPriceSchemeReqDto = {
    /** 区域会员价格 */
    areaMemberPrice?: number;
    /** 区域价格 */
    areaPrice?: number;
    /** 买赠方案 */
    buyAndGiftScheme?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 消费时段 */
    consumptionTimeSlot?: string;
    /** 默认包含项目 */
    defaultItems?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 免费项目 */
    freeItems?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrice?: number;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 价格方案名称 */
    name?: string;
    /** 可选项目 */
    optionalItems?: string;
    /** 房间类型 */
    roomType?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
  };

export type AddStatisticsCategoryReqDto = {
    /** 统计分类名称 */
    name?: string;
    /** 价格方案ids */
    pricePlanIds?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AllPricePlansReqDto = {
    /** 是否启用，可选 */
    enabled?: boolean;
    /** 包厢类型ID，可选 */
    roomTypeId?: string;
    /** 门店ID，可选 */
    venueId?: string;
  };

export type AllPricePlansVO = {
    /** 基准钟点价(单位：分) */
    baseTimePrice?: number;
    /** 买断价格方案列表 */
    buyoutPricePlans?: BuyoutPricePlanVO[];
    /** 计时价格方案列表 */
    timePricePlans?: TimePricePlanVO[];
  };

export type AreaPrice = {
    /** 区域ID */
    areaId?: string;
    /** 区域价格 */
    price?: number;
  };

export type BuyoutPricePlanVO = {
    advanceDisableDuration?: number;
    areaConfig?: AreaConfig;
    channelConfig?: ChannelConfig;
    createTime?: number;
    duration?: number;
    giftPlan?: string;
    hasMinimumCharge?: boolean;
    id?: string;
    isAreaSpecified?: boolean;
    isEnabled?: boolean;
    isExcessIncluded?: boolean;
    minimumCharge?: number;
    name?: string;
    planPic?: string;
    planProducts?: ProductPlan[];
    priceConfigList?: PriceSettingItem[];
    /** PlanProducts，方案内商品 */
    pricePlanSubProductVO?: ProductVO[];
    /** PlanProducts，方案内商品套餐 */
    productPackageVOs?: ProductPackageVO[];
    roomTypeConfig?: RoomTypeConfig;
    state?: number;
    statisticsCategory?: string;
    supportVipDiscount?: boolean;
    supportsPoints?: boolean;
    timeConfig?: BuyoutTimeConfig;
    updateTime?: number;
    venueId?: string;
    version?: number;
  };

export type CalculatePriceVO = {
    /** 货币 */
    currency?: string;
    /** 描述 */
    description?: string;
    /** 价格 */
    price?: number;
  };

export type CalculateTimePriceReqDto = {
    /** 区域ID */
    areaId?: string;
    /** 检查时间戳 */
    checkTime?: number;
    /** 使用时长（分钟） */
    duration?: number;
    /** 节假日ID */
    holidayId?: string;
    /** 方案ID */
    id?: string;
    /** 价格类型 */
    priceType?: string;
  };

export type CreateBuyoutPricePlanReqDto = {
    /** 提前禁用时长 */
    advanceDisableDuration: number;
    /** 区域ID数组 */
    areaConfig?: string[];
    /** 渠道ID数组 */
    channelConfig?: string[];
    /** 时长 */
    duration: number;
    /** 赠品方案 */
    giftPlan?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 方案ID，可选 */
    id?: string;
    /** 是否指定区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 是否包含超出部分 */
    isExcessIncluded?: boolean;
    /** 最低消费 */
    minimumCharge?: number;
    /** 方案名称 */
    name: string;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: ProductPlan[];
    /** 价格配置列表 */
    priceConfigList: PriceSettingItem[];
    /** 房间类型ID数组 */
    roomTypeConfig: string[];
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间配置 */
    timeConfig: BuyoutTimeConfig;
    /** 门店ID */
    venueId: string;
  };

export type CreateTimePricePlanReqDto = {
    /** 区域ID数组 */
    areaConfig?: string[];
    /** 渠道ID数组 */
    channelConfig?: string[];
    /** 消费赠券 */
    giftPlan?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 可选，如果不提供则自动生成 */
    id?: string;
    /** 是否指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: ProductPlan[];
    /** 价格配置列表 */
    priceConfigList?: PriceSettingItem[];
    /** 房间类型ID数组 */
    roomTypeConfig?: string[];
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间配置 */
    timeConfig?: TimePriceTimeConfig;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteBuyoutPricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type DeleteOrderPricePlanReqDto = {
    /** ID */
    id?: string;
  };

export type DeletePricePlanReqDto = {
    /** ID */
    id?: string;
  };

export type DeletePriceSchemeReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteTimePricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type DisableBuyoutPricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type DisableTimePricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type DouyinGroupBuyingPlanVO = {
    /** 绑定状态 */
    bindingStatus?: string;
    /** 创建时间 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 计划ID */
    planId?: string;
    /** 计划名称 */
    planName?: string;
    /** 价格 */
    price?: number;
    /** 产品信息(JSON) */
    productInfo?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type EnableBuyoutPricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type EnableTimePricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type GetBuyoutPricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type GetTimePricePlanReqDto = {
    /** 方案ID */
    id?: string;
  };

export type HolidayPrice = {
    /** 节假日下的区域价格配置 */
    areaPrice?: AreaPrice[];
    /** 节假日ID */
    holidayId?: string;
    /** 节假日价格 */
    price?: number;
  };

export type ListBuyoutPricePlansReqDto = {
    /** 是否启用，可选 */
    enabled?: boolean;
    /** 页码，可选 */
    pageNum?: number;
    /** 每页大小，可选 */
    pageSize?: number;
    /** 门店ID，可选 */
    venueId?: string;
  };

export type ListTimePricePlansReqDto = {
    /** 是否启用，可选 */
    enabled?: boolean;
    /** 页码，可选 */
    pageNum?: number;
    /** 每页大小，可选 */
    pageSize?: number;
    /** 门店ID，可选 */
    venueId?: string;
  };

export type OrderPriceBaseVO = {
    activityFee?: number;
    baseRoomFee?: number;
    birthdayFee?: number;
    groupBuyFee?: number;
    id?: string;
    minimumCharge?: number;
  };

export type OrderPricePlanVO = {
    /** 买钟时长 */
    buyMinute?: number;
    /** 消费模式 */
    consumptionMode?: string;
    /** 创建时间 */
    ctime?: number;
    /** 结束时间-真实结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单ID */
    orderNo?: string;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 房间ID */
    roomId?: string;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 开始时间-真实开始时间 */
    startTime?: number;
    /** 状态 */
    state?: number;
    /** 买钟类型 */
    timeChargeMode?: string;
    /** 买钟价格类型 */
    timeChargeType?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type PageVOArrayVoBuyoutPricePlanVO = {
    data?: BuyoutPricePlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoPricePlanVO = {
    data?: PricePlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoOrderPricePlanVO = {
    data?: OrderPricePlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPriceSchemeVO = {
    data?: PriceSchemeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PricePlanUnionVO = {
    /** 账单类型 */
    billType?: string;
    /** 商品名称 */
    name?: string;
    /** 选项数量 */
    optionCount?: number;
    /** 选项类型 */
    optionType?: string;
    /** 商品列表 */
    products?: PricePlanUnionProductVO[];
  };

export type PricePlanVO = {
    /** 活动价格 */
    activityFee?: number;
    /** 提前禁用时长 */
    advanceDisableDuration?: number;
    /** 区域会员价格 */
    areaMemberPrices?: string;
    /** 区域价格 */
    areaPrices?: string;
    /** 基础房费 */
    baseRoomFee?: number;
    /** 生日价格 */
    birthdayFee?: number;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 消费时间段 */
    consumptionTimeSlots?: string;
    /** 创建时间 */
    ctime?: number;
    /** 结束日期 */
    dayEnd?: string;
    /** 开始日期 */
    dayStart?: string;
    /** 买钟优惠时长 */
    discountDuration?: number;
    /** 优惠模式 */
    discountMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 买断持续时长 */
    duration?: number;
    /** 示例产品 */
    exampleProducts?: string;
    /** 免费产品 */
    freeProducts?: string;
    /** 赠送时长 */
    giftDuration?: number;
    /** 团购价格 */
    groupBuyFee?: number;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrices?: string;
    /** 结束时间 */
    hourMinuteEnd?: string;
    /** 开始时间 */
    hourMinuteStart?: string;
    /** ID */
    id?: string;
    /** 是否指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 多余部分是否计入房费 */
    isExcessIncluded?: boolean;
    /** 累计最高可抵扣金额 */
    maxDeductibleAmount?: number;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 超市消费最低金额 */
    minimumConsumption?: number;
    /** 价格方案名称 */
    name?: string;
    /** 可选产品 */
    optionalProducts?: string;
    orderPriceBaseVO?: OrderPriceBaseVO;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: string;
    planProductsPricePlanUnionVOs?: PricePlanUnionVO[];
    /** PlanProducts，方案内商品 */
    pricePlanSubProductVO?: ProductVO[];
    /** PlanProducts，方案内商品套餐 */
    productPackageVOs?: ProductPackageVO[];
    /** 房费可抵扣的商品分类 */
    roomChargeGoods?: string;
    /** 房间类型 */
    roomType?: string;
    /** 选中的投放区域 */
    selectedAreas?: string;
    /** 状态 */
    state?: number;
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间类型 */
    timeType?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店id */
    venueId?: string;
    /** 版本 */
    version?: number;
    /** 星期 */
    weeks?: string;
  };

export type PriceSchemeVO = {
    /** 区域会员价格 */
    areaMemberPrice?: number;
    /** 区域价格 */
    areaPrice?: number;
    /** 买赠方案 */
    buyAndGiftScheme?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 消费时段 */
    consumptionTimeSlot?: string;
    /** 创建时间 */
    ctime?: number;
    /** 默认包含项目 */
    defaultItems?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 免费项目 */
    freeItems?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrice?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 价格方案名称 */
    name?: string;
    /** 可选项目 */
    optionalItems?: string;
    /** 房间类型 */
    roomType?: string;
    /** 状态 */
    state?: number;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PriceSettingItem = {
    /** 区域价格配置 */
    areaPrice?: AreaPrice[];
    /** 节假日价格配置 */
    holidayPrice?: HolidayPrice[];
    /** 价格类型名称，例如 "基础", "活动", "基础会员" */
    name?: string;
    /** 默认价格 (非节假日，非区域价格) */
    price?: number;
    /** 价格类型标识，例如 "base"(基础), "birthday"(生日), "group"(团体), "activity"(活动) */
    type?: string;
  };

export type QueryDouyinGroupBuyingPlanReqDto = {
    /** 绑定状态 */
    bindingStatus?: string;
    /** 唯一id */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 计划ID */
    planId?: string;
    /** 计划名称 */
    planName?: string;
    /** 价格 */
    price?: number;
  };

export type QueryOrderPricePlanReqDto = {
    /** 消费模式 */
    consumptionMode?: string;
    /** ID */
    id?: string;
    /** 订单ID */
    orderNo?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 买钟类型 */
    timeChargeMode?: string;
    /** 买钟价格类型 */
    timeChargeType?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryPricePlanReqDto = {
    /** 活动价格 */
    activityFee?: number;
    /** 提前禁用时长 */
    advanceDisableDuration?: number;
    /** 基础房费 */
    baseRoomFee?: number;
    /** 生日价格 */
    birthdayFee?: number;
    /** 消费模式 */
    consumptionMode?: string;
    /** 买钟优惠时长 */
    discountDuration?: number;
    /** 优惠模式 */
    discountMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 买断持续时长 */
    duration?: number;
    /** 赠送时长 */
    giftDuration?: number;
    /** 团购价格 */
    groupBuyFee?: number;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** ID */
    id?: string;
    /** 是否指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 多余部分是否计入房费 */
    isExcessIncluded?: boolean;
    /** 累计最高可抵扣金额 */
    maxDeductibleAmount?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 超市消费最低金额 */
    minimumConsumption?: number;
    /** 价格方案名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间类型 */
    roomType?: string;
    /** 房间类型模糊查询 */
    roomTypeLike?: string;
    /** 场次ID */
    sessionId?: string;
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间类型 */
    timeType?: string;
    /** 门店id */
    venueId?: string;
  };

export type QueryPriceSchemeReqDto = {
    /** 区域会员价格 */
    areaMemberPrice?: number;
    /** 区域价格 */
    areaPrice?: number;
    /** 消费模式 */
    consumptionMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrice?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 价格方案名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间类型 */
    roomType?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
  };

export type QueryStatisticsCategoryReqDto = {
    /** ID */
    id?: string;
    /** 统计分类名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 价格方案ids */
    pricePlanIds?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoPricePlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PricePlanVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoOrderPricePlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderPricePlanVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPriceSchemeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PriceSchemeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoPricePlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PricePlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOrderPricePlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderPricePlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoPricePlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoPricePlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoOrderPricePlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoOrderPricePlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPriceSchemeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPriceSchemeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPriceSchemeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PriceSchemeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type SetBaseTimePriceReqDto = {
    /** 基准钟点价（分） */
    basePrice: number;
    /** 包厢类型ID */
    roomTypeID: string;
  };

export type StatisticsCategoryVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 统计分类名称 */
    name?: string;
    /** 价格方案ids */
    pricePlanIds?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type TimePricePlanVO = {
    areaConfig?: AreaConfig;
    channelConfig?: ChannelConfig;
    /** 创建时间 */
    createTime?: number;
    giftPlan?: string;
    id?: string;
    isAreaSpecified?: boolean;
    isEnabled?: boolean;
    /** 方案图片 */
    planPic?: string;
    planProducts?: ProductPlan[];
    /** 价格配置列表 */
    priceConfigList?: PriceSettingItem[];
    /** PlanProducts，方案内商品 */
    pricePlanSubProductVO?: ProductVO[];
    /** PlanProducts，方案内商品套餐 */
    productPackageVOs?: ProductPackageVO[];
    roomTypeConfig?: RoomTypeConfig;
    /** 状态 */
    state?: number;
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持会员折扣 */
    supportVipDiscount?: boolean;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    timeConfig?: TimePriceTimeConfig;
    /** 更新时间 */
    updateTime?: number;
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type TimePriceTimeConfig = {
    /** 计费时段结束时间 (HH:mm) */
    billingTimeEnd?: string;
    /** 计费时段开始时间 (HH:mm) */
    billingTimeStart?: string;
    /** 日期配置，TimeType为 "date" 时使用 */
    dateConfig?: DateConfig;
    /** 时间类型: "weekday", "date" */
    timeType?: string;
    /** 星期配置，TimeType为 "weekday" 时使用 */
    weekdayConfig?: WeekdayConfig;
  };

export type UpdateBuyoutPricePlanReqDto = {
    /** 提前禁用时长 */
    advanceDisableDuration?: number;
    /** 区域ID数组 */
    areaConfig?: string[];
    /** 渠道ID数组 */
    channelConfig?: string[];
    /** 时长 */
    duration?: number;
    /** 赠品方案 */
    giftPlan?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 方案ID */
    id: string;
    /** 是否指定区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 是否包含超出部分 */
    isExcessIncluded?: boolean;
    /** 最低消费 */
    minimumCharge?: number;
    /** 方案名称 */
    name: string;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: ProductPlan[];
    /** 价格配置列表 */
    priceConfigList?: PriceSettingItem[];
    /** 房间类型ID数组 */
    roomTypeConfig: string[];
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间配置 */
    timeConfig: BuyoutTimeConfig;
  };

export type UpdateDouyinGroupBuyingPlanReqDto = {
    /** 绑定状态 */
    bindingStatus?: string;
    /** 唯一id */
    id?: string;
    /** 计划ID */
    planId?: string;
    /** 计划名称 */
    planName?: string;
    /** 价格 */
    price?: number;
    /** 产品信息(JSON) */
    productInfo?: string;
  };

export type UpdateOrderPricePlanReqDto = {
    /** 买钟时长 */
    buyMinute?: number;
    /** 消费模式 */
    consumptionMode?: string;
    /** ID */
    id?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单ID */
    orderNo?: string;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 买钟类型 */
    timeChargeMode?: string;
    /** 买钟价格类型 */
    timeChargeType?: string;
  };

export type UpdatePricePlanReqDto = {
    /** 活动价格 */
    activityFee?: number;
    /** 提前禁用时长 */
    advanceDisableDuration?: number;
    /** 区域会员价格 */
    areaMemberPrices?: string;
    /** 区域价格 */
    areaPrices?: string;
    /** 基础房费 */
    baseRoomFee?: number;
    /** 生日价格 */
    birthdayFee?: number;
    /** 买赠方案 */
    buyGiftPlan?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 消费时间段 */
    consumptionTimeSlots?: string;
    /** 结束日期 */
    dayEnd?: string;
    /** 开始日期 */
    dayStart?: string;
    /** 买钟优惠时长 */
    discountDuration?: number;
    /** 优惠模式 */
    discountMode?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 买断持续时长 */
    duration?: number;
    /** 示例产品 */
    exampleProducts?: string;
    /** 免费产品 */
    freeProducts?: string;
    /** 赠送时长 */
    giftDuration?: number;
    /** 团购价格 */
    groupBuyFee?: number;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrices?: string;
    /** 结束时间 */
    hourMinuteEnd?: string;
    /** 开始时间 */
    hourMinuteStart?: string;
    /** ID */
    id?: string;
    /** 是否指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 多余部分是否计入房费 */
    isExcessIncluded?: boolean;
    /** 累计最高可抵扣金额 */
    maxDeductibleAmount?: number;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 超市消费最低金额 */
    minimumConsumption?: number;
    /** 价格方案名称 */
    name?: string;
    /** 可选产品 */
    optionalProducts?: string;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: string;
    /** 房费可抵扣的商品分类 */
    roomChargeGoods?: string;
    /** 房间类型 */
    roomType?: string;
    /** 选中的投放区域 */
    selectedAreas?: string;
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间类型 */
    timeType?: string;
    /** 门店id */
    venueId?: string;
    /** 星期 */
    weeks?: string;
  };

export type UpdatePriceSchemeReqDto = {
    /** 区域会员价格 */
    areaMemberPrice?: number;
    /** 区域价格 */
    areaPrice?: number;
    /** 买赠方案 */
    buyAndGiftScheme?: string;
    /** 消费模式 */
    consumptionMode?: string;
    /** 消费时段 */
    consumptionTimeSlot?: string;
    /** 默认包含项目 */
    defaultItems?: string;
    /** 分销渠道 */
    distributionChannel?: string;
    /** 免费项目 */
    freeItems?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 节假日价格 */
    holidayPrice?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 会员价格 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 价格方案名称 */
    name?: string;
    /** 可选项目 */
    optionalItems?: string;
    /** 房间类型 */
    roomType?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
  };

export type UpdateStatisticsCategoryReqDto = {
    /** ID */
    id?: string;
    /** 统计分类名称 */
    name?: string;
    /** 价格方案ids */
    pricePlanIds?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateTimePricePlanReqDto = {
    /** 区域ID数组 */
    areaConfig?: string[];
    /** 渠道ID数组 */
    channelConfig?: string[];
    /** 消费赠券 */
    giftPlan?: string;
    /** 是否有最低消费 */
    hasMinimumCharge?: boolean;
    /** 方案ID */
    id?: string;
    /** 是否指定投放区域 */
    isAreaSpecified?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 方案图片 */
    planPic?: string;
    /** 方案内商品 */
    planProducts?: ProductPlan[];
    /** 价格配置列表 */
    priceConfigList?: PriceSettingItem[];
    /** 房间类型ID数组 */
    roomTypeConfig?: string[];
    /** 统计分类 */
    statisticsCategory?: string;
    /** 是否支持积分 */
    supportsPoints?: boolean;
    /** 时间配置 */
    timeConfig?: TimePriceTimeConfig;
  };
