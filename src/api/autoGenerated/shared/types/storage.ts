// storage相关类型定义

export type AddWineStorageReqDto = {
    /** 过期日期 */
    expirationDate?: number;
    /** 处理人 */
    handler?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 电话号码 */
    phoneNumber?: string;
  };

export type AddWineStorageSettingReqDto = {
    /** 代理商到期提醒 */
    agentExpirationReminder?: string;
    /** 是否自动没收 */
    autoConfiscate?: boolean;
    /** 过期多少天后自动充公 */
    autoConfiscateDays?: number;
    /** 充公仓库 */
    confiscateWarehouse?: string;
    /** 客户通知天数 */
    customerNotificationDays?: number;
    /** 商户到期提醒 */
    merchantExpirationReminder?: string;
    /** 商户通知天数 */
    merchantNotificationDays?: number;
    /** 逾期取酒限制 */
    overdueWithdrawalLimit?: number;
    /** 续期天数 */
    renewalDays?: number;
    /** 续期次数 */
    renewalTimes?: number;
    /** 存储天数 */
    storageDays?: number;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteWineStorageReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteWineStorageSettingReqDto = {
    /** ID */
    id?: string;
  };

export type GetStorageStatisticsReqDto = {
    /** 客户ID */
    customerId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 是否仅线下存酒 */
    offlineOnly?: boolean;
    /** 仅查询剩余 */
    onlyRemaining?: boolean;
    /** 页码 */
    pageNum?: number;
    /** 页大小 */
    pageSize?: number;
    /** 产品类型 */
    productType?: string;
    /** 搜索文本 */
    searchText?: string;
    /** 开始时间 */
    startTime?: number;
    /** 统计类型: summary(汇总统计)、byProduct(按商品统计) */
    statType?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 存放包厢ID */
    storageRoomId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type ProductStorageVO = {
    /** 批量操作时间 */
    batchTime?: number;
    /** 创建时间 */
    ctime?: number;
    /** 客户ID */
    customerId?: string;
    /** 客户姓名 */
    customerName?: string;
    /** 到期剩余天数 */
    daysToExpire?: number;
    /** 到期时间 */
    expireTime?: number;
    /** ID */
    id?: string;
    /** 是否批量操作的一部分 */
    isBatch?: number;
    /** 是否已过期 */
    isExpired?: boolean;
    /** 是否即将到期 */
    isExpiringSoon?: boolean;
    /** 最后操作时间 */
    lastOperationTime?: number;
    /** 会员卡号 */
    memberCardNo?: string;
    /** 是否仅线下存酒 */
    offlineOnly?: boolean;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 存酒单号 */
    orderNo?: string;
    /** 父订单号 */
    parentOrderNo?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品类型 */
    productType?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 数量 */
    quantity?: number;
    /** 剩余数量 */
    remainingQty?: number;
    /** 剩余比例(0-100) */
    remainingRatio?: number;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 状态码(stored/partial/withdrawn/discarded) */
    statusCode?: string;
    /** 状态名称(已存/部分支取/已取完/已报废) */
    statusName?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 存放包厢ID */
    storageRoomId?: string;
    /** 存放包厢名称 */
    storageRoomName?: string;
    /** 存入时间 */
    storageTime?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type QueryWineStorageReqDto = {
    /** 过期日期 */
    expirationDate?: number;
    /** 处理人 */
    handler?: string;
    /** ID */
    id?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 电话号码 */
    phoneNumber?: string;
  };

export type QueryWineStorageSettingReqDto = {
    /** 代理商到期提醒 */
    agentExpirationReminder?: string;
    /** 是否自动没收 */
    autoConfiscate?: boolean;
    /** 过期多少天后自动充公 */
    autoConfiscateDays?: number;
    /** 充公仓库 */
    confiscateWarehouse?: string;
    /** 客户通知天数 */
    customerNotificationDays?: number;
    /** ID */
    id?: string;
    /** 商户到期提醒 */
    merchantExpirationReminder?: string;
    /** 商户通知天数 */
    merchantNotificationDays?: number;
    /** 逾期取酒限制 */
    overdueWithdrawalLimit?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 续期天数 */
    renewalDays?: number;
    /** 续期次数 */
    renewalTimes?: number;
    /** 存储天数 */
    storageDays?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ResultArrayVoProductStorageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStorageVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoWineStorageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WineStorageVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductStorageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStorageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoWineStorageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WineStorageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type StorageStatisticsVO = {
    /** 客户数量 */
    customerCount?: number;
    /** 即将过期数量(30天内) */
    expiringSoonCount?: number;
    /** 今日存入 */
    todayStorage?: number;
    /** 今日取出 */
    todayWithdraw?: number;
    /** 剩余总数 */
    totalRemaining?: number;
    /** 存酒总数 */
    totalStorage?: number;
    /** 取酒总数 */
    totalWithdraw?: number;
  };

export type UnifiedStorageItemDto = {
    /** 到期时间 */
    expireTime?: number;
    /** 是否仅线下存酒 */
    offlineOnly?: boolean;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 产品规格 */
    productSpec?: string;
    /** 产品类型 */
    productType?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 数量 */
    quantity?: number;
    /** 备注 */
    remark?: string;
    /** 存放位置 */
    storageLocation?: string;
    /** 存放包厢ID */
    storageRoomId?: string;
  };

export type UpdateWineStorageReqDto = {
    /** 过期日期 */
    expirationDate?: number;
    /** 处理人 */
    handler?: string;
    /** ID */
    id?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 电话号码 */
    phoneNumber?: string;
  };

export type UpdateWineStorageSettingReqDto = {
    /** 代理商到期提醒 */
    agentExpirationReminder?: string;
    /** 是否自动没收 */
    autoConfiscate?: boolean;
    /** 过期多少天后自动充公 */
    autoConfiscateDays?: number;
    /** 充公仓库 */
    confiscateWarehouse?: string;
    /** 客户通知天数 */
    customerNotificationDays?: number;
    /** ID */
    id?: string;
    /** 商户到期提醒 */
    merchantExpirationReminder?: string;
    /** 商户通知天数 */
    merchantNotificationDays?: number;
    /** 逾期取酒限制 */
    overdueWithdrawalLimit?: number;
    /** 续期天数 */
    renewalDays?: number;
    /** 续期次数 */
    renewalTimes?: number;
    /** 存储天数 */
    storageDays?: number;
    /** 门店ID */
    venueId?: string;
  };

export type WineStorageSettingVO = {
    /** 代理商到期提醒 */
    agentExpirationReminder?: string;
    /** 是否自动没收 */
    autoConfiscate?: boolean;
    /** 过期多少天后自动充公 */
    autoConfiscateDays?: number;
    /** 充公仓库 */
    confiscateWarehouse?: string;
    /** 创建时间 */
    ctime?: number;
    /** 客户通知天数 */
    customerNotificationDays?: number;
    /** ID */
    id?: string;
    /** 商户到期提醒 */
    merchantExpirationReminder?: string;
    /** 商户通知天数 */
    merchantNotificationDays?: number;
    /** 逾期取酒限制 */
    overdueWithdrawalLimit?: number;
    /** 续期天数 */
    renewalDays?: number;
    /** 续期次数 */
    renewalTimes?: number;
    /** 状态 */
    state?: number;
    /** 存储天数 */
    storageDays?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type WineStorageVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 过期日期 */
    expirationDate?: number;
    /** 处理人 */
    handler?: string;
    /** ID */
    id?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 房间ID */
    roomId?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };
