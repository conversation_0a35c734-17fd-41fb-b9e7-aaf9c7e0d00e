// result相关类型定义
import { VenueAndEmployeeVO, LoginVenueAndEmployeeVO } from './employee';
import { VenueShouyinGrantGzhQRVO, VenueVO } from './venue';
import { ERPUserVO } from './user';
import { OrderProductVO, PointsExchangeVO, RecipeVO } from './product';
import { OrderRoomPlanVO, OrderVO } from './order';
import { PayBillVO, SessionVO, RegisterMemberVO } from './member';
import { WechatMiniappJSPayInfo, LshSimplePayInfo, AsExampleVO, BirthdayGreetingVO, CallMessageVO, CallTypesVO, CashierMachineVO, CheckoutPrintRecordVO, CommissionPlanVO, CommissionVO, CommonRemarkVO, ConstructionAssistanceVO, ConsumptionCashbackVO, CreditAccountVO, CreditUnitVO, CustomerGroupVO, CustomerSourceVO, CustomerTagVO, DataCleanupVO, AreaVO, HolidayVO, FlavorVO, HistoricalRecordVO, IngredientTypeVO, MarketServiceVO, MerchantServiceVO, NotificationSettingVO, OnlineOperationSettingsVO, OpenTablePrintRecordVO, OperationSettingsVO, PhoneBlacklistVO, PosMachineVO, PrintTemplateVO, RechargePackageVO, RedemptionRecordVO, RewardVO, RouterVO, SalesPerformanceVO, SequencerVO, ServiceRewardSettingsVO, SmsServiceVO, StatisticsPeriodVO, SubtitleInfoVO, TurnoverDataVO, TvScreenActivityVO, VodSettingsVO, VoucherVO, WarehouseVO, AppUpgradeVO, CashierMachineBindInfoVO, DailyPaiedSessionVO, SendSmsCodeVO, WithdrawableItemsVO, WXEventVO } from './other';
import { ProductStorageVO, WineStorageSettingVO, StorageStatisticsVO } from './storage';
import { DouyinGroupBuyingPlanVO, StatisticsCategoryVO } from './price';

export type LoginMinAppResultVO = {
    /** 手机号 */
    phone?: string;
    /** token */
    token?: string;
    /** unionid */
    unionid?: string;
    /** 门店和员工 */
    venueAndEmployeeVOs?: VenueAndEmployeeVO[];
    /** 关注码 */
    venueShouyinGrantGzhQRVO?: VenueShouyinGrantGzhQRVO;
  };

export type LoginResultVO = {
    /** 用户信息 */
    erpUserVO?: ERPUserVO;
    /** token */
    token?: string;
    /** 门店列表 */
    venues?: LoginVenueAndEmployeeVO[];
  };

export type OrderPayCalculateResultVO = {
    orderProductVOs?: OrderProductVO[];
    orderRoomPlanVOs?: OrderRoomPlanVO[];
    orderVO?: OrderVO;
    payBillVO?: PayBillVO;
  };

export type OrderPayQueryResultVO = {
    payBillVO?: PayBillVO;
  };

export type PayResult = {
    err_code?: string;
    err_msg?: string;
    order_id?: string;
    pay?: PayResultInfo;
    pay_amount?: number;
    pay_id?: string;
    pay_time?: number;
    pay_type?: string;
    refund_id?: string;
    status?: string;
  };

export type PayResultInfo = {
    pay_id?: string;
    pay_url?: string;
  };

export type PayResultVO = {
    /** BillId 账单ID */
    billId?: string;
    /** ErrCode 错误码 */
    errCode?: string;
    /** ErrMsg 错误信息 */
    errMsg?: string;
    /** PayInfo 支付信息 */
    jSPayInfo?: WechatMiniappJSPayInfo;
    /** LshSimplePayInfo 乐刷简易支付信息 */
    lshSimplePayInfo?: LshSimplePayInfo;
    /** PayId 支付ID */
    payId?: string;
    /** PayIds 支付ID列表 */
    payIds?: string[];
  };

export type ProductStorageAddResultVO = {
    /** 提示信息 */
    message?: string;
    /** 存酒单信息（type=multiple时有值） */
    orderInfo?: any;
    /** 存酒记录信息（type=single时有值） */
    storageInfo?: ProductStorageVO;
    /** 是否成功 */
    success?: boolean;
    /** 类型：single-单个商品，multiple-多个商品 */
    type?: string;
  };

export type QRLoginResultVO = {
    /** 登录密钥 */
    login_key?: string;
    /** 二维码图片 */
    qr_code?: string;
    /** 场景值 */
    scene_str?: string;
  };

export type ResultAny = {
    attachments?: Record<string, any>;
    code?: number;
    data?: any;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayString = {
    attachments?: Record<string, any>;
    code?: number;
    data?: string[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoAsExampleVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AsExampleVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoBirthdayGreetingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: BirthdayGreetingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCallMessageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CallMessageVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCallTypesVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CallTypesVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCashierMachineVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CashierMachineVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCheckoutPrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CheckoutPrintRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCommissionPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CommissionPlanVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCommissionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CommissionVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCommonRemarkVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CommonRemarkVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoConstructionAssistanceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ConstructionAssistanceVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoConsumptionCashbackVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ConsumptionCashbackVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCreditAccountVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CreditAccountVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCreditUnitVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CreditUnitVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCustomerGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CustomerGroupVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCustomerSourceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CustomerSourceVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoCustomerTagVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CustomerTagVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoDataCleanupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: DataCleanupVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoAreaVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AreaVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoHolidayVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: HolidayVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoSessionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SessionVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoDouyinGroupBuyingPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: DouyinGroupBuyingPlanVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoFlavorVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: FlavorVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoHistoricalRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: HistoricalRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoIngredientTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: IngredientTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMarketServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MarketServiceVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMerchantServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MerchantServiceVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoNotificationSettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: NotificationSettingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoOnlineOperationSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OnlineOperationSettingsVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoOpenTablePrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OpenTablePrintRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoOperationSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OperationSettingsVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPayBillVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PayBillVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPhoneBlacklistVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PhoneBlacklistVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPointsExchangeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PointsExchangeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPosMachineVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PosMachineVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPrintTemplateVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PrintTemplateVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRechargePackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RechargePackageVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRecipeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RecipeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRedemptionRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RedemptionRecordVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRewardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RewardVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoRouterVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RouterVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoSalesPerformanceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SalesPerformanceVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoSequencerVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SequencerVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoServiceRewardSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ServiceRewardSettingsVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoSmsServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SmsServiceVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoStatisticsCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: StatisticsCategoryVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoStatisticsPeriodVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: StatisticsPeriodVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoSubtitleInfoVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SubtitleInfoVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoTurnoverDataVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: TurnoverDataVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoTvScreenActivityVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: TvScreenActivityVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoVodSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VodSettingsVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoVoucherVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VoucherVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoWarehouseVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WarehouseVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoWineStorageSettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WineStorageSettingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultControllerQRLoginResultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: QRLoginResultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultPaysdkPayResult = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PayResult;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultString = {
    attachments?: Record<string, any>;
    code?: number;
    data?: string;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoAppUpgradeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AppUpgradeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoAsExampleVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AsExampleVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoBirthdayGreetingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: BirthdayGreetingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCallMessageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CallMessageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCallTypesVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CallTypesVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCashierMachineBindInfoVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CashierMachineBindInfoVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCashierMachineVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CashierMachineVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCheckoutPrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CheckoutPrintRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCommissionPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CommissionPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCommissionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CommissionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCommonRemarkVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CommonRemarkVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoConstructionAssistanceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ConstructionAssistanceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoConsumptionCashbackVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ConsumptionCashbackVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCreditAccountVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CreditAccountVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCreditUnitVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CreditUnitVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCustomerGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CustomerGroupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCustomerSourceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CustomerSourceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCustomerTagVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CustomerTagVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoDailyPaiedSessionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: DailyPaiedSessionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoDataCleanupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: DataCleanupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoAreaVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AreaVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoHolidayVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: HolidayVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoSessionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SessionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoDouyinGroupBuyingPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: DouyinGroupBuyingPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoFlavorVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: FlavorVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoHistoricalRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: HistoricalRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoIngredientTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: IngredientTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoLoginMinAppResultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: LoginMinAppResultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoLoginResultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: LoginResultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMarketServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MarketServiceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMerchantServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MerchantServiceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoNotificationSettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: NotificationSettingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOnlineOperationSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OnlineOperationSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOpenTablePrintRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OpenTablePrintRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOperationSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OperationSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPhoneBlacklistVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PhoneBlacklistVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPointsExchangeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PointsExchangeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPosMachineVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PosMachineVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPrintTemplateVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PrintTemplateVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductStorageAddResultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStorageAddResultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRechargePackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RechargePackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRecipeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RecipeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRedemptionRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RedemptionRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRewardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RewardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRouterVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RouterVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoSalesPerformanceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SalesPerformanceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoSendSmsCodeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SendSmsCodeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoSequencerVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SequencerVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoServiceRewardSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ServiceRewardSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoSmsServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SmsServiceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoStatisticsCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: StatisticsCategoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoStatisticsPeriodVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: StatisticsPeriodVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoStorageStatisticsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: StorageStatisticsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoSubtitleInfoVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SubtitleInfoVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoTurnoverDataVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: TurnoverDataVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoTvScreenActivityVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: TvScreenActivityVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueCashMachineAuthResultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueCashMachineAuthResultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVodSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VodSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVoucherVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VoucherVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoWarehouseVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WarehouseVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoWineStorageSettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WineStorageSettingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoWithdrawableItemsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WithdrawableItemsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoWXEventVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: WXEventVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type SessionOperationVOVoPayResultVO = {
    data?: PayResultVO;
    jspayInfo?: WechatMiniappJSPayInfo;
    lshSimplePayInfo?: LshSimplePayInfo;
    orderNos?: string[];
    payBills?: PayBillVO[];
    sessionId?: string;
  };

export type VenueCashMachineAuthResultVO = {
    /** 收银机信息 */
    cashierMachine?: CashierMachineVO;
    /** 是否绑定 */
    isBind?: boolean;
    /** 上一次的收银机IP */
    lastIP?: string;
    /** mac地址 */
    mac?: string;
    /** 授权小程序的二维码地址 */
    qrCodeUrl?: string;
    /** 场景str-微信公众号 */
    sceneStr?: string;
    /** 二维码过期时间 */
    urlExpireTime?: number;
    /** 门店信息 */
    venue?: VenueVO;
    /** 门店id */
    venueId?: string;
  };

export type VerifyResultVO = {
    /** 已存在的会员信息 */
    existingMember?: RegisterMemberVO;
    /** 是否有效 */
    isValid?: boolean;
    /** 提示信息 */
    message?: string;
  };
