// venue相关类型定义
import { EmployeeVO } from './employee';

export type ActiveVenueAuthCodeReqDto = {
    /** 授权码 */
    authCode?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddVenueAuthCodeReqDto = {
    /** 授权码 */
    authCode?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 创建人ID */
    creatorId?: string;
    /** 到期时间 */
    expireTime?: number;
    /** 备注 */
    remark?: string;
    /** 生效时间 */
    startTime?: number;
    /** 授权码状态：0-未使用 1-已使用 2-已过期 */
    status?: number;
    /** 有效期(天数) */
    validPeriod?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddVenueForMiniAppReqDto = {
    /** 门店地址 */
    address?: string;
    /** 审核状态：0-待审核 1-已通过 2-已拒绝 */
    auditStatus?: number;
    /** 市 */
    city?: string;
    /** 联系人 */
    contactName?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 门店描述 */
    description?: string;
    /** 区 */
    district?: string;
    /** 营业时间 */
    endHours?: string;
    /** 门店logo URL */
    logo?: string;
    /** 门店名称 */
    name?: string;
    /** 门店照片URL列表 */
    photos?: string;
    /** 省 */
    province?: string;
    /** 场景串 */
    sceneStr?: string;
    /** 营业时间 */
    startHours?: string;
    /** 微信unionid */
    unionid?: string;
    /** 用户ID */
    userId?: string;
    /** 用户名 */
    userName?: string;
    /** 用户手机号 */
    userPhone?: string;
    /** 门店类型 */
    venueType?: number;
  };

export type AddVenuePaySettingReqDto = {
    /** 房间ID */
    ktvid?: string;
    /** 备注 */
    remark?: string;
    /** 乐刷子商户ID */
    subMerchantId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddVenueShouyinAuthReqDto = {
    /** 客户端类型  client/pad */
    clientType?: string;
    /** 授权IP */
    grantIp?: string;
    /** mac地址 */
    mac?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AuditVenueReqDto = {
    /** 审核备注 */
    remark?: string;
    /** 审核状态：1-通过 2-拒绝 */
    status?: number;
    /** 门店ID */
    venue_id?: string;
  };

export type AuthorizeVenueReqDto = {
    /** 授权账号 */
    dogname: string;
    /** 授权密码 */
    pwd: string;
    /** 门店ID */
    venueId: string;
  };

export type BindVenueShouyinReqDto = {
    /** 门店ID */
    bindVenueId: string;
    /** 场景值，用于识别扫码来源 */
    sceneStr: string;
  };

export type DeleteVenueAuthCodeReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteVenuePaySettingReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteVenueReqDto = {
    /** ID */
    id?: string;
  };

export type getApiV1VenueAuthStatusParams = {
    /** 门店ID */
    venue_id?: string;
  };

export type getApiVenueShouyinBindInfoParams = {
    /** 收银机MAC地址 */
    mac?: string;
    /** 收银机ID */
    id?: string;
  };

export type PageVOArrayVoVenueVO = {
    data?: VenueVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type QueryMiniAppVenueBindListReqDto = {
    /** 手机号 */
    phone?: string;
    /** 微信unionid */
    unionid?: string;
  };

export type QueryMyVenueReqDto = {
    /** 手机号 */
    phone?: string;
  };

export type QueryVenueAuthCodeReqDto = {
    /** 授权码 */
    authCode?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 创建人ID */
    creatorId?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页条数 */
    pageSize?: number;
    /** 授权码状态：0-未使用 1-已使用 2-已过期 */
    status?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryVenuePaySettingReqDto = {
    /** ID */
    id?: string;
    /** 房间ID */
    ktvid?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 备注 */
    remark?: string;
    /** 乐刷子商户ID */
    subMerchantId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryVenueReqDto = {
    /** 门店地址 */
    address?: string;
    /** 新增审核状态查询条件 */
    auditStatus?: number;
    /** 营业时间 */
    businessHours?: string;
    /** ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 门店logo URL */
    logo?: string;
    /** mac地址 */
    mac?: string;
    /** 门店名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 手机号 */
    phone?: string;
    /** 门店照片URL列表 */
    photos?: string;
    /** 微信unionid */
    unionid?: string;
  };

export type QueryVenueShouyinGrantLoginQRReqDto = {
    /** IP地址 */
    ip?: string;
    /** mac地址 */
    mac?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryVenueShouyinGrantMyBindedReqDto = {
    /** 手机号 */
    phone?: string;
    /** 场景str-微信公众号 */
    sceneStr?: string;
  };

export type ResultArrayVoVenueAuthCodeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAuthCodeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoVenuePaySettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenuePaySettingVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoVenueVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoVenueVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoVenueVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoSwitchVenueVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: SwitchVenueVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueAuthCodeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAuthCodeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueAuthStatusVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAuthStatusVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenuePaySettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenuePaySettingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueShouyinGrantMyBindedVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueShouyinGrantMyBindedVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type SwitchVenueReqDto = {
    /** 门店ID */
    venueId: string;
  };

export type SwitchVenueVO = {
    /** 员工信息 */
    employee?: EmployeeVO;
    /** 新的 token */
    token?: string;
    /** 门店信息 */
    venue?: VenueVO;
  };

export type UpdateVenueAuthCodeReqDto = {
    /** 授权码 */
    authCode?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 创建人ID */
    creatorId?: string;
    /** 到期时间 */
    expireTime?: number;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 生效时间 */
    startTime?: number;
    /** 授权码状态：0-未使用 1-已使用 2-已过期 */
    status?: number;
    /** 有效期(天数) */
    validPeriod?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateVenuePaySettingReqDto = {
    /** ID */
    id?: string;
    /** 房间ID */
    ktvid?: string;
    /** 备注 */
    remark?: string;
    /** 乐刷子商户ID */
    subMerchantId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateVenueReqDto = {
    /** 门店地址 */
    address?: string;
    /** 市 */
    city?: string;
    /** 联系人 */
    contact?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 门店描述 */
    description?: string;
    /** 区 */
    district?: string;
    /** 营业结束时间 */
    endHours?: string;
    /** ID */
    id?: string;
    /** 门店logo URL */
    logo?: string;
    /** 门店名称 */
    name?: string;
    /** 门店照片URL列表 */
    photos?: string;
    /** 省 */
    province?: string;
    /** 营业开始时间 */
    startHours?: string;
    /** 门店类型 */
    venueType?: number;
  };

export type VenueAuthCodeVO = {
    /** 授权码 */
    authCode?: string;
    /** 合同名称 */
    contractName?: string;
    /** 合同编号 */
    contractNo?: string;
    /** 创建人ID */
    creatorId?: string;
    /** 创建时间 */
    ctime?: number;
    /** 到期时间 */
    expireTime?: number;
    /** ID */
    id?: string;
    /** 备注 */
    remark?: string;
    /** 生效时间 */
    startTime?: number;
    /** 状态 */
    state?: number;
    /** 授权码状态：0-未使用 1-已使用 2-已过期 */
    status?: number;
    /** 更新时间 */
    utime?: number;
    /** 有效期(天数) */
    validPeriod?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type VenueAuthStatusVO = {
    /** 审核备注 */
    audit_remark?: string;
    /** 审核状态：0-待审核 1-已通过 2-已拒绝 */
    audit_status?: number;
    /** 授权状态：0-未授权 1-试用中 2-正式授权 3-已过期 */
    auth_status?: number;
    /** 授权类型：1-试用授权 2-正式授权 */
    auth_type?: number;
    /** 新增合同名称 */
    contract_name?: string;
    /** 新增合同编号 */
    contract_no?: string;
    /** 到期时间 */
    expire_time?: string;
    /** 最近授权时间 */
    last_auth_time?: string;
    /** 剩余天数 */
    remain_days?: number;
    /** 门店ID */
    venue_id?: string;
  };

export type VenuePaySettingVO = {
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 房间ID */
    ktvid?: string;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 乐刷子商户ID */
    subMerchantId?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type VenueShouyinGrantGzhQRVO = {
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** mac地址 */
    mac?: string;
    /** 授权公众号二维码URL */
    qrGzhUrl?: string;
    /** 备注 */
    remark?: string;
    /** 场景str-微信公众号 */
    sceneStr?: string;
    /** 状态 */
    state?: number;
    /** 二维码过期时间 */
    urlExpireTime?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type VenueShouyinGrantMyBindedVO = {
    /** 员工信息 */
    employeeVO?: EmployeeVO;
    /** 门店信息 */
    venueVO?: VenueVO;
  };

export type VenueVO = {
    /** 门店地址 */
    address?: string;
    /** 用于前端的appId，与加密狗绑定 */
    appId?: string;
    /** 用于前端的appKey，与加密狗绑定 */
    appKey?: string;
    /** 审核状态：0-待审核 1-已通过 2-已拒绝 */
    auditStatus?: number;
    /** 市 */
    city?: string;
    /** 联系人 */
    contact?: string;
    /** 联系人电话 */
    contactPhone?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 门店描述 */
    description?: string;
    /** 区 */
    district?: string;
    /** 营业结束时间 */
    endHours?: string;
    /** ID */
    id?: string;
    /** 是否开通乐刷支付 */
    isLeshuaPay?: number;
    /** 是否是雷石VOD点歌系统 */
    isThunderVOD?: number;
    /** 门店logo URL */
    logo?: string;
    /** 门店名称 */
    name?: string;
    /** 门店照片URL列表 */
    photos?: string;
    /** 省 */
    province?: string;
    /** 营业开始时间 */
    startHours?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店类型 */
    venueType?: number;
    /** 版本号 */
    version?: number;
  };
