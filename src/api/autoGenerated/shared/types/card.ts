// card相关类型定义

export type AddElectronicCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
  };

export type AddM1CardReqDto = {
    /** 卡类型 */
    cardType?: string;
    /** 密码 */
    password?: string;
  };

export type AddPhysicalCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
  };

export type AddPrepaidCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 剩余次数 */
    remainingTimes?: number;
    /** 已使用次数 */
    usedTimes?: number;
  };

export type AddPrepaidCardTypeReqDto = {
    /** 适用产品类型 */
    applicableProductTypes?: string;
    /** 名称 */
    name?: string;
    /** 销售金额 */
    saleAmount?: number;
    /** 总次数 */
    totalTimes?: number;
    /** 有效期 */
    validityPeriod?: string;
  };

export type CardLevelInfo = {
    /** 卡类型 */
    cardType?: string;
    /** 等级描述 */
    description?: string;
    /** 等级ID */
    id?: string;
    /** 等级值 */
    level?: string;
    /** 等级logo */
    logo?: string;
    /** 等级名称 */
    name?: string;
    /** 商品折扣 */
    productDiscount?: number;
    /** 房间折扣 */
    roomDiscount?: number;
  };

export type CardLevelsVO = {
    /** 卡等级列表 */
    levels?: CardLevelInfo[];
  };

export type DeleteElectronicCardReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteM1CardReqDto = {
    /** ID */
    id?: string;
  };

export type DeletePhysicalCardReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type DeletePrepaidCardReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeletePrepaidCardTypeReqDto = {
    /** 唯一id */
    id?: string;
  };

export type ElectronicCardVO = {
    /** 卡号 */
    cardNumber?: string;
    /** 创建时间 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type M1CardVO = {
    /** 卡类型 */
    cardType?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 密码 */
    password?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PageVOArrayVoElectronicCardVO = {
    data?: ElectronicCardVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoM1CardVO = {
    data?: M1CardVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPhysicalCardVO = {
    data?: PhysicalCardVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPrepaidCardVO = {
    data?: PrepaidCardVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PhysicalCardRegisterDTO = {
    /** 生日时间戳 */
    birthday?: number;
    /** 卡等级ID */
    cardLevelId?: string;
    /** 实体卡号 */
    cardNumber?: string;
    /** 性别 */
    gender?: string;
    /** 初始余额 */
    initialBalance?: number;
    /** 初始积分 */
    initialPoints?: number;
    /** 会员姓名 */
    name?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 手机号 */
    phone?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type PhysicalCardVO = {
    /** 卡号 */
    cardNumber?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 唯一ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PrepaidCardTypeVO = {
    /** 适用产品类型 */
    applicableProductTypes?: string;
    /** 创建时间 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 销售金额 */
    saleAmount?: number;
    /** 状态 */
    state?: number;
    /** 总次数 */
    totalTimes?: number;
    /** 更新时间 */
    utime?: number;
    /** 有效期 */
    validityPeriod?: string;
    /** 版本号 */
    version?: number;
  };

export type PrepaidCardVO = {
    /** 卡号 */
    cardNumber?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 剩余次数 */
    remainingTimes?: number;
    /** 状态值 */
    state?: number;
    /** 已使用次数 */
    usedTimes?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type QueryElectronicCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 唯一id */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryM1CardReqDto = {
    /** 卡类型 */
    cardType?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 密码 */
    password?: string;
  };

export type QueryPhysicalCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 唯一ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryPrepaidCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 唯一id */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 剩余次数 */
    remainingTimes?: number;
    /** 已使用次数 */
    usedTimes?: number;
  };

export type QueryPrepaidCardTypeReqDto = {
    /** 适用产品类型 */
    applicableProductTypes?: string;
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 销售金额 */
    saleAmount?: number;
    /** 总次数 */
    totalTimes?: number;
    /** 有效期 */
    validityPeriod?: string;
  };

export type ResultArrayVoElectronicCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ElectronicCardVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoM1CardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: M1CardVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPhysicalCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PhysicalCardVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPrepaidCardTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PrepaidCardTypeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPrepaidCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PrepaidCardVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoElectronicCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ElectronicCardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoM1CardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: M1CardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoElectronicCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoElectronicCardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoM1CardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoM1CardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPhysicalCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPhysicalCardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPrepaidCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPrepaidCardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPhysicalCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PhysicalCardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPrepaidCardTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PrepaidCardTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPrepaidCardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PrepaidCardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type UpdateElectronicCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 唯一id */
    id?: string;
  };

export type UpdateM1CardReqDto = {
    /** 卡类型 */
    cardType?: string;
    /** ID */
    id?: string;
    /** 密码 */
    password?: string;
  };

export type UpdatePhysicalCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 唯一ID */
    id?: string;
  };

export type UpdatePrepaidCardReqDto = {
    /** 卡号 */
    cardNumber?: string;
    /** 唯一id */
    id?: string;
    /** 剩余次数 */
    remainingTimes?: number;
    /** 已使用次数 */
    usedTimes?: number;
  };

export type UpdatePrepaidCardTypeReqDto = {
    /** 适用产品类型 */
    applicableProductTypes?: string;
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 销售金额 */
    saleAmount?: number;
    /** 总次数 */
    totalTimes?: number;
    /** 有效期 */
    validityPeriod?: string;
  };

export type V3OpenCardReqDto = {
    /** 生日 */
    birthday?: number;
    /** 会员卡结束时间 */
    cardEndTime?: number;
    /** 会员卡等级 - 普通卡/黄金卡/钻石卡 */
    cardLevel?: string;
    /** 会员卡名称 */
    cardLevelName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 会员卡开始时间 */
    cardStartTime?: number;
    /** 会员卡类型 - 实体卡/虚拟卡/电子卡 */
    cardType?: string;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 性别 male/female */
    gender?: string;
    /** 姓名 */
    name?: string;
    /** 手机号 */
    phone?: string;
    /** 销售员ID */
    sellerId?: string;
    /** 销售员姓名 */
    sellerName?: string;
    /** 门店ID */
    venueId?: string;
  };
