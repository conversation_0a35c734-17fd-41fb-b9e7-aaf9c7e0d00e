// order相关类型定义
import { OrderProductVO, PayRecordVO, PackageSubProductInfo, GiftProductInfo, ExtendedProductInfo } from './product';
import { RoomVO, RoomInfo, RoomPackageInfo } from './room';
import { SessionVO, PayBillVO, ReservationInfo } from './member';
import { OrderPricePlanVO } from './price';
import { PayResultVO } from './result';
import { VenueVO } from './venue';
import { CustomerInfoVO, WechatMiniappJSPayInfo, LshSimplePayInfo } from './other';
import { ProductStorageVO } from './storage';

export type AbnormalPaymentOrderVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 订单状态 */
    orderStatus?: string;
    /** 房间号 */
    roomNumber?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type AddAbnormalPaymentOrderReqDto = {
    /** 订单号 */
    orderNumber?: string;
    /** 订单状态 */
    orderStatus?: string;
    /** 房间号 */
    roomNumber?: string;
  };

export type AddMobileOrderReqDto = {
    /** 订单ID */
    orderId?: string;
    /** 下单时间 */
    orderTime?: number;
    /** 状态 */
    status?: string;
  };

export type AddOrderAdditionalReqDto = {
    /** BShowQR支付方式的BQROneCode */
    bQROneCode?: string;
    /** 找零金额 */
    changeAmount?: number;
    /** 挂账账户ID */
    creditAccountId?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 当前时间 */
    currentTime?: number;
    /** 商品折扣率 */
    discountProductRate?: number;
    /** 房费折扣率 */
    discountRoomRate?: number;
    /** 员工ID */
    employeeId?: string;
    /** 免单金额 */
    freeAmount?: number;
    /** 是否立结 */
    isSettled?: boolean;
    /** 会员金额 */
    memberAmount?: number;
    /** 点单商品信息 */
    orderProductVOs?: OrderProductVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 支付总金额 */
    payAmount?: number;
    /** 支付方式 */
    payType?: string;
    /** 减免商品金额 */
    reduceProductAmount?: number;
    /** 减免房费金额 */
    reduceRoomAmount?: number;
    /** 关联的房间ID */
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddOrderAttachRoomReqDto = {
    employeeId?: string;
    sessionVOIdle?: SessionVO;
    sessionVOOpening?: SessionVO;
    venueId?: string;
  };

export type AddOrderCancelAttachRoomReqDto = {
    sessionVOSlave?: SessionVO;
    venueId?: string;
  };

export type AddOrderCloseRoomReqDto = {
    /** 房间ID */
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddOrderMergeRoomReqDto = {
    sessionVOOpeningA?: SessionVO;
    sessionVOOpeningB?: SessionVO;
    venueId?: string;
  };

export type AddOrderOpenContinueReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 当前时间 */
    currentTime?: number;
    /** 员工ID */
    employeeId?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付总金额 */
    payAmount?: number;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 订单开始时间 */
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddOrderOpenReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 当前时间 */
    currentTime?: number;
    /** 员工ID */
    employeeId?: string;
    /** 支付员工ID */
    employeeIdPay?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付总金额 */
    payAmount?: number;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 订单开始时间 */
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddOrderProductReqDto = {
    /** 口味 */
    flavors?: string;
    /** 套内商品标签 */
    inPackageTag?: string;
    /** 产品显示备注 */
    mark?: string;
    /** 订单ID */
    orderNo?: string;
    /** 支付价格 */
    payPrice?: number;
    /** 支付状态 */
    payStatus?: string;
    /** 原价 */
    price?: number;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 场次ID */
    sessionId?: string;
    /** 套餐来源 */
    src?: string;
    /** 总金额 */
    totalAmount?: number;
    /** 单位 */
    unit?: string;
  };

export type AddOrderReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** 会员ID */
    memberId?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单编号 */
    orderNo?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 订单开始时间 */
    startTime?: number;
    /** 订单状态 */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 订单总金额 */
    totalAmount?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddOrderRoomPlanReqDto = {
    /** 买钟时长 */
    duration?: number;
    /** 结束时间 */
    endTime?: number;
    /** 订单ID */
    orderNo?: string;
    /** 房费 */
    payFee?: number;
    /** 支付状态 */
    payStatus?: string;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 买钟价格类型 */
    pricePlanType?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 开始时间 */
    startTime?: number;
  };

export type AddOrderSwapRoomReqDto = {
    sessionVOOpeningA?: SessionVO;
    sessionVOOpeningB?: SessionVO;
    venueId?: string;
  };

export type AddOrderTimeGiftReqDto = {
    /** 赠送时长 单位：分钟 */
    giftMinute?: number;
    /** 原始金额 */
    originalAmount?: number;
    /** 支付总金额 */
    payAmount?: number;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddOrderTransferRoomReqDto = {
    sessionVOIdle?: SessionVO;
    sessionVOOpening?: SessionVO;
    venueId?: string;
  };

export type AddProductionOrderPlanReqDto = {
    /** 名称 */
    name?: string;
    /** 打印区域 */
    printAreas?: string;
    /** 产品类型 */
    productTypes?: string;
  };

export type DeleteAbnormalPaymentOrderReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteMobileOrderReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteOrderProductReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteOrderReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteOrderRoomPlanReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteProductionOrderPlanReqDto = {
    /** 唯一id */
    id?: string;
  };

export type getApiProductStorageOrderOrderNoParams = {
    /** 存酒单号（主订单号） */
    orderNo: string;
  };

export type MobileOrderVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 订单ID */
    orderId?: string;
    /** 下单时间 */
    orderTime?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type OrderAndPayVO = {
    /** 收款单号 */
    billId?: string;
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 单号 */
    orderNo?: string;
    /** 扩展字段 */
    payBillVO?: PayBillVO;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type OrderAndRefundVO = {
    hasRefund?: boolean;
    orderVO?: OrderVO;
    pOrderVO?: OrderVO;
  };

export type OrderDetailsMap = {
    /** key: orderNo, value: OrderDetailVO */
    orderDetails?: Record<string, any>;
  };

export type OrderDetailVO = {
    /** 订单信息 */
    order?: OrderVO;
    /** 订单关联的产品 */
    orderProducts?: OrderProductVO[];
    /** 订单关联的房间计划 */
    orderRoomPlans?: OrderRoomPlanVO[];
  };

export type OrderExtFeeVO = {
    /** 配置-商品价格会员优惠方式 0:无，1：会员价，2：会员折扣 */
    configProductMemberDiscountType?: number;
    /** 配置-包厢价格会员优惠方式 0:无，1：会员价，2：会员折扣 */
    configRoomMemberDiscountType?: number;
    /** 创建时间 */
    ctime?: number;
    /** 方向 normal:下单、refund:退款 */
    direction?: string;
    /** 员工ID */
    employeeId?: string;
    /** 是否存在多种支付方式 */
    hasMultiPayWay?: boolean;
    /** ID */
    id?: string;
    /** 备注 */
    mark?: string;
    /** 标记类型 normal:正常、cancel:取消  转台时旧订单被标记为cancel，其他均正常 */
    markType?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单编号 */
    orderNo?: string;
    orderProductVOs?: OrderProductVO[];
    orderRoomPlanVOs?: OrderRoomPlanVO[];
    /** 父订单编号-退款时原订单 */
    pOrderNo?: string;
    /** 套餐信息 */
    packageInfo?: OrderPricePlanVO;
    payBillVO?: PayBillVO;
    payResultVOs?: PayResultVO[];
    productInfos?: OrderProductVO[];
    refundOrderProductVOs?: OrderProductVO[];
    /** 退款订单 */
    refundOrders?: OrderVO[];
    /** 退款类型 空为正常 已付退款:paid_refund，未付退款:unpaid_refund */
    refundTag?: string;
    /** 房间ID */
    roomId?: string;
    roomVO?: RoomVO;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 订单状态  unpaid:未结、paid:已结 */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 订单总金额 */
    totalFee?: number;
    /** 订单类型 roomplan/product */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type OrderOpenVO = {
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 当前时间 */
    currentTime?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 支付中间表 */
    orderAndPayVOs?: OrderAndPayVO[];
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 订单信息 */
    orderVOS?: OrderVO[];
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付单 */
    payBillVOs?: PayBillVO[];
    /** 支付记录 */
    payRecordVOs?: PayRecordVO[];
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次信息 */
    sessionVO?: SessionVO;
    /** 订单开始时间 */
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    /** 订单总金额 */
    totalAmount?: number;
    /** 所属门店ID */
    venueId?: string;
  };

export type OrderProductSalesVO = {
    /** 商品或套餐分类ID */
    categoryId?: string;
    /** 商品或套餐分类名称 */
    categoryName?: string;
    /** 员工ID */
    employeeId?: string;
    /** 是否-已赠送 */
    isGift?: boolean;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 应付-原价格*数量 */
    shouldFee?: number;
    /** 总金额-实付 */
    totalFee?: number;
    /** 门店ID */
    venueId?: string;
  };

export type OrderRefundInfoVO = {
    errCode?: number;
    errMsg?: string;
    payRecordVOs?: PayRecordVO[];
    payType?: string;
    refundAmount?: number;
    refundId?: string;
  };

export type OrderRefundViewVO = {
    orderProductVOs?: OrderProductVO[];
    orderRoomPlanVOs?: OrderRoomPlanVO[];
    orderVOs?: OrderVO[];
    roomVO?: RoomVO;
    sessionVO?: SessionVO;
    venueVO?: VenueVO;
  };

export type OrderRefundVO = {
    refundInfos?: OrderRefundInfoVO[];
  };

export type OrderRoomPlanVO = {
    /** --消费模式消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 创建时间 */
    ctime?: number;
    /** 买钟时长 单位：分钟 == BuyMinute */
    duration?: number;
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 是否已打折 */
    isDiscounted?: boolean;
    /** 是否-已免单 */
    isFree?: boolean;
    /** 是否是赠送 */
    isGift?: boolean;
    /** 是否是补差价订单 */
    isPriceDiff?: boolean;
    /** 是否是计时消费 */
    isTimeConsume?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员折扣 */
    memberDiscount?: number;
    /** 是否-会员折扣 */
    memberDiscountable?: boolean;
    /** 会员ID */
    memberId?: string;
    /** 真实原价-会员价格-白金-钻石 */
    memberPrice?: number;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单ID */
    orderNo?: string;
    /** 原支付金额 */
    originalPayAmount?: number;
    /** 退款对应的原始OrderRoomPlan.Id */
    pId?: string;
    /** 房费 */
    payAmount?: number;
    /** 折扣 */
    payRoomDiscount?: number;
    /** 折扣减免 */
    payRoomDiscountAmount?: number;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    refundFee?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** --选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** --选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 开始时间 */
    startTime?: number;
    /** 状态 */
    state?: number;
    /** --买钟-类型 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeMode?: string;
    /** --买钟-类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时 */
    timeChargeType?: string;
    /** 扩展字段2 */
    unitPrice?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type OrderVO = {
    /** 配置-商品价格会员优惠方式 0:无，1：会员价，2：会员折扣 */
    configProductMemberDiscountType?: number;
    /** 配置-包厢价格会员优惠方式 0:无，1：会员价，2：会员折扣 */
    configRoomMemberDiscountType?: number;
    /** 创建时间 */
    ctime?: number;
    /** 方向 normal:下单、refund:退款 */
    direction?: string;
    /** 员工ID */
    employeeId?: string;
    /** 是否存在多种支付方式 */
    hasMultiPayWay?: boolean;
    /** ID */
    id?: string;
    /** 备注 */
    mark?: string;
    /** 标记类型 normal:正常、cancel:取消  转台时旧订单被标记为cancel，其他均正常 */
    markType?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单编号 */
    orderNo?: string;
    orderProductVOs?: OrderProductVO[];
    orderRoomPlanVOs?: OrderRoomPlanVO[];
    /** 父订单编号-退款时原订单 */
    pOrderNo?: string;
    /** 套餐信息 */
    packageInfo?: OrderPricePlanVO;
    payBillVO?: PayBillVO;
    payResultVOs?: PayResultVO[];
    productInfos?: OrderProductVO[];
    refundOrderProductVOs?: OrderProductVO[];
    /** 退款订单 */
    refundOrders?: OrderVO[];
    /** 退款类型 空为正常 已付退款:paid_refund，未付退款:unpaid_refund */
    refundTag?: string;
    /** 房间ID */
    roomId?: string;
    roomVO?: RoomVO;
    /** 场次ID */
    sessionId?: string;
    /** 状态 */
    state?: number;
    /** 订单状态  unpaid:未结、paid:已结 */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 订单类型 roomplan/product */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type PageVOArrayVoAbnormalPaymentOrderVO = {
    data?: AbnormalPaymentOrderVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMobileOrderVO = {
    data?: MobileOrderVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoOrderVO = {
    data?: OrderVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type ProductionOrderDataVO = {
    /** 点单人员工名称 */
    employeeName?: string;
    /** 订单号 */
    orderNo?: string;
    /** 点单时间 */
    orderTime?: string;
    /** 出品人员工名称 */
    producerName?: string;
    /** 出品单号 */
    productionOrderNo?: string;
    /** 出品时间 */
    productionTime?: string;
    /** 出品商品列表 */
    products?: ProductionOrderItem[];
    /** 包厢信息 */
    roomInfo?: RoomInfo;
    /** 开台单号 */
    sessionId?: string;
  };

export type ProductionOrderItem = {
    /** 口味，可选 */
    flavors?: string;
    /** 支付价/现价 */
    payPrice?: number;
    /** 单价 */
    price?: number;
    /** 商品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 套餐子商品 */
    subProducts?: PackageSubProductInfo[];
    /** 小计 */
    totalAmount?: number;
    /** 单位 */
    unit?: string;
  };

export type ProductionOrderPlanVO = {
    /** 创建时间 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 打印区域 */
    printAreas?: string;
    /** 产品类型 */
    productTypes?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type ProductStorageOrderWithItemsVO = {
    customerInfo?: CustomerInfoVO;
    items?: ProductStorageVO[];
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    orderNo?: string;
    remark?: string;
    storageTime?: number;
    totalItems?: number;
    totalQuantity?: number;
  };

export type QueryAbnormalPaymentOrderReqDto = {
    /** 唯一id */
    id?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 订单状态 */
    orderStatus?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间号 */
    roomNumber?: string;
  };

export type QueryMobileOrderReqDto = {
    /** ID */
    id?: string;
    /** 订单ID */
    orderId?: string;
    /** 下单时间 */
    orderTime?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 状态 */
    status?: string;
  };

export type QueryOpenOrderReqDto = {
    endTime?: number;
    memberId?: string;
    pageNum?: number;
    pageSize?: number;
    roomId?: string;
    sessionId?: string;
    startTime?: number;
    status?: string;
    venueId?: string;
  };

export type QueryOrderCleanRoomFinishReqDto = {
    /** 员工ID（可选） */
    employeeId?: string;
    /** 房间ID（可选） */
    roomId?: string;
    /** 场次ID（可选） */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryOrderFaultRoomReqDto = {
    content?: string;
    roomId?: string;
    venueId?: string;
  };

export type QueryOrderLockRoomReqDto = {
    roomId?: string;
    sessionId?: string;
    venueId?: string;
  };

export type QueryOrderPayLeshuaQueryReqDto = {
    /** 实付金额 */
    actualAmount?: number;
    /** 找零金额 */
    changeAmount?: number;
    /** 订单ID */
    orderNos?: string[];
    /** 支付金额 */
    payAmount?: number;
    /** 支付ID */
    payId?: string;
    /** 支付方式 */
    payType?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryOrderPayLeshuaRefundQueryReqDto = {
    /** 实付金额 */
    actualAmount?: number;
    /** 找零金额 */
    changeAmount?: number;
    /** 订单ID */
    orderNos?: string[];
    /** 支付金额 */
    payAmount?: number;
    /** 支付ID */
    payId?: string;
    /** 支付方式 */
    payType?: string;
    /** 退款ID */
    refundId?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryOrderPayReqDto = {
    /** BShowQR支付方式的BQROneCode */
    bQROneCode?: string;
    /** 找零金额 */
    changeAmount?: number;
    /** 挂账账户ID */
    creditAccountId?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 商品折扣率 */
    discountProductRate?: number;
    /** 房费折扣率 */
    discountRoomRate?: number;
    /** 员工ID */
    employeeId?: string;
    /** 免单金额 */
    freeAmount?: number;
    /** 会员金额 */
    memberAmount?: number;
    /** 订单ID */
    orderNos?: string[];
    /** 原始金额 */
    originalFee?: number;
    /** 支付金额 */
    payAmount?: number;
    /** 支付方式 */
    payType?: string;
    /** 减免商品金额 */
    reduceProductAmount?: number;
    /** 减免房费金额 */
    reduceRoomAmount?: number;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryOrderProductReqDto = {
    /** 口味 */
    flavors?: string;
    /** ID */
    id?: string;
    /** ID */
    ids?: string[];
    /** 套内商品标签 */
    inPackageTag?: string;
    /** 产品显示备注 */
    mark?: string;
    /** 订单ID */
    orderNo?: string;
    /** 订单ID */
    orderNos?: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 支付价格 */
    payPrice?: number;
    /** 支付状态 */
    payStatus?: string;
    /** 原价 */
    price?: number;
    /** 产品ID */
    productId?: string;
    /** 产品ID */
    productIds?: string[];
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 场次ID */
    sessionId?: string;
    /** 场次ID */
    sessionIds?: string[];
    /** 套餐来源 */
    src?: string;
    /** 总金额 */
    totalAmount?: number;
    /** 单位 */
    unit?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryOrderRefundLeshuaReqDto = {
    /** 支付账单ID */
    id?: string;
    /** 订单ID */
    orderId?: string;
    /** 支付单号 */
    payId?: string;
    /** 支付类型 */
    payType?: string;
    /** 退款金额 */
    refundAmount?: number;
  };

export type QueryOrderRefundReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 退款商品 */
    orderProductVos?: OrderProductVO[];
    /** 退款金额 */
    refundAmount?: number;
    /** 退款类型 back: 原路径返回 cash: 现金退款 */
    refundWayType?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryOrderRefundViewReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryOrderReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单编号 */
    orderNo?: string;
    /** 订单编号 */
    orderNos?: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 场次ID */
    sessionIds?: string[];
    /** 订单开始时间 */
    startTime?: number;
    /** 订单状态 */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 订单总金额 */
    totalAmount?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryOrderRoomPlanReqDto = {
    /** 买钟时长 */
    duration?: number;
    /** 结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** ID */
    ids?: string[];
    /** 订单ID */
    orderNo?: string;
    /** 订单ID */
    orderNos?: string[];
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房费 */
    payFee?: number;
    /** 支付状态 */
    payStatus?: string;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 买钟价格类型 */
    pricePlanType?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 场次ID */
    sessionIds?: string[];
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryOrdersByNosReqDto = {
    /** 订单编号数组 */
    orderNos?: string[];
    /** 门店ID */
    venueId?: string;
  };

export type QueryOrderSessionReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单编号 */
    orderNo?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 订单开始时间 */
    startTime?: number;
    /** 订单状态 */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 订单总金额 */
    totalAmount?: number;
    /** 门店门店 */
    venueId?: string;
  };

export type QueryOrderWithGuestReqDto = {
    roomId?: string;
    venueId?: string;
  };

export type QueryProductionOrderPlanReqDto = {
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 打印区域 */
    printAreas?: string;
    /** 产品类型 */
    productTypes?: string;
  };

export type ResultArrayVoAbnormalPaymentOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AbnormalPaymentOrderVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoOrderRoomPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderRoomPlanVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMobileOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MobileOrderVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoProductionOrderPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductionOrderPlanVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoAbnormalPaymentOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: AbnormalPaymentOrderVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoOrderRoomPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderRoomPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMobileOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MobileOrderVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOrderDetailsMap = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderDetailsMap;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOrderOpenVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderOpenVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOrderRefundViewVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderRefundViewVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOrderRefundVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderRefundVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: OrderVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoAbnormalPaymentOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoAbnormalPaymentOrderVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMobileOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMobileOrderVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoOrderVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoOrderVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductionOrderPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductionOrderPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoProductStorageOrderWithItemsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ProductStorageOrderWithItemsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type SessionOperationVOVoOrderVO = {
    data?: OrderVO;
    jspayInfo?: WechatMiniappJSPayInfo;
    lshSimplePayInfo?: LshSimplePayInfo;
    orderNos?: string[];
    payBills?: PayBillVO[];
    sessionId?: string;
  };

export type SessionOrderDataVO = {
    /** 收银员 */
    cashierName?: string;
    /** 消费结束时间 */
    endTime?: string;
    /** 赠送小计 */
    giftProductTotal?: number;
    /** 赠送商品 */
    giftProducts?: GiftProductInfo[];
    /** 开台时间 */
    openTime?: string;
    /** 预付余额 */
    prePayBalance?: number;
    /** 打单时间 */
    printTime?: string;
    /** 商品消费总计 */
    productFeeTotal?: number;
    /** 普通消费商品 */
    products?: ExtendedProductInfo[];
    /** 备注 */
    remark?: string;
    /** 预订信息 */
    reservationInfo?: ReservationInfo;
    /** 包厢消费总计 */
    roomFeeTotal?: number;
    /** 包厢信息 */
    roomInfo?: RoomInfo;
    /** 房间方案 */
    roomPackages?: RoomPackageInfo[];
    /** 开台单号 */
    sessionId?: string;
    /** 店铺名称 */
    shopName?: string;
    /** 消费开始时间 */
    startTime?: string;
  };

export type StorageOrderBriefVO = {
    /** 存酒单号 */
    orderNo?: string;
    /** 剩余总数量 */
    remainingQuantity?: number;
    /** 存入时间 */
    storageTime?: number;
    /** 商品总项数 */
    totalItems?: number;
    /** 商品总数量 */
    totalQuantity?: number;
  };

export type UpdateAbnormalPaymentOrderReqDto = {
    /** 唯一id */
    id?: string;
    /** 订单号 */
    orderNumber?: string;
    /** 订单状态 */
    orderStatus?: string;
    /** 房间号 */
    roomNumber?: string;
  };

export type UpdateMobileOrderReqDto = {
    /** ID */
    id?: string;
    /** 订单ID */
    orderId?: string;
    /** 下单时间 */
    orderTime?: number;
    /** 状态 */
    status?: string;
  };

export type UpdateOrderProductReqDto = {
    /** 口味 */
    flavors?: string;
    /** ID */
    id?: string;
    /** 套内商品标签 */
    inPackageTag?: string;
    /** 产品显示备注 */
    mark?: string;
    /** 订单ID */
    orderNo?: string;
    /** 支付价格 */
    payPrice?: number;
    /** 支付状态 */
    payStatus?: string;
    /** 原价 */
    price?: number;
    /** 产品ID */
    productId?: string;
    /** 产品名称 */
    productName?: string;
    /** 数量 */
    quantity?: number;
    /** 场次ID */
    sessionId?: string;
    /** 套餐来源 */
    src?: string;
    /** 总金额 */
    totalAmount?: number;
    /** 单位 */
    unit?: string;
  };

export type UpdateOrderReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 订单结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 最低消费金额 */
    minimumCharge?: number;
    /** 订单编号 */
    orderNo?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 订单开始时间 */
    startTime?: number;
    /** 订单状态 */
    status?: string;
    /** 标签 */
    tag?: string;
    /** 订单总金额 */
    totalAmount?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateOrderRoomPlanReqDto = {
    /** 买钟时长 */
    duration?: number;
    /** 结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 订单ID */
    orderNo?: string;
    /** 房费 */
    payFee?: number;
    /** 支付状态 */
    payStatus?: string;
    /** 方案id */
    pricePlanId?: string;
    /** 价格方案名称 */
    pricePlanName?: string;
    /** 买钟价格类型 */
    pricePlanType?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 开始时间 */
    startTime?: number;
  };

export type UpdateProductionOrderPlanReqDto = {
    /** 唯一id */
    id?: string;
    /** 名称 */
    name?: string;
    /** 打印区域 */
    printAreas?: string;
    /** 产品类型 */
    productTypes?: string;
  };

export type V3AddOrderAdditionalPayReqDto = {
    /** 找零金额 */
    changeAmount?: number;
    /** 挂账账户ID */
    creditAccountId?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 当前时间 */
    currentTime?: number;
    /** 优惠原因 */
    discountReason?: string;
    employeeId?: string;
    /** 是否免单 */
    isFree?: boolean;
    /** 是否赠品 */
    isGift?: boolean;
    /** 会员金额 */
    memberAmount?: number;
    memberCardId?: string;
    memberId?: string;
    /** 点单商品信息 */
    orderProductVOs?: OrderProductVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 原始金额 优惠金额 = 原始金额 - 应付金额 */
    originalFee?: number;
    /** 支付总金额 */
    payAmount?: number;
    /** 支付记录ID */
    payRecords?: PayRecordVO[];
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    roomId?: string;
    sessionId?: string;
    /** 应付金额 = 实付金额 + 抹零金额 */
    shouldFee?: number;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    venueId?: string;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type V3AddOrderAdditionalReqDto = {
    /** 找零金额 */
    changeAmount?: number;
    /** 挂账账户ID */
    creditAccountId?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 当前时间 */
    currentTime?: number;
    employeeId?: string;
    /** 是否赠品 */
    isGift?: boolean;
    /** 会员金额 */
    memberAmount?: number;
    memberCardId?: string;
    memberId?: string;
    /** 点单商品信息 */
    orderProductVOs?: OrderProductVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 支付总金额 */
    payAmount?: number;
    roomId?: string;
    sessionId?: string;
    venueId?: string;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type V3AddOrderCloseRoomReqDto = {
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    sessionId?: string;
    venueId?: string;
  };

export type V3AddOrderOpenContinuePayReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 找零金额 */
    changeAmount?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 当前时间 */
    currentTime?: number;
    /** 优惠原因 */
    discountReason?: string;
    /** 员工ID */
    employeeId?: string;
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否免单 */
    isFree?: boolean;
    /** 是否赠品 */
    isGift?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 原始金额 优惠金额 = 原始金额 - 应付金额 */
    originalFee?: number;
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付总金额 */
    payAmount?: number;
    /** 支付记录ID */
    payRecords?: PayRecordVO[];
    pricePlanId?: string;
    pricePlanName?: string;
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 退款支付记录ID */
    refundPayRecords?: PayRecordVO[];
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    sessionId?: string;
    /** 应付金额 = 实付金额 + 抹零金额 + 找零金额 */
    shouldFee?: number;
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    venueId?: string;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type V3AddOrderOpenContinueReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 当前时间 */
    currentTime?: number;
    /** 员工ID */
    employeeId?: string;
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否赠品 */
    isGift?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付总金额 */
    payAmount?: number;
    pricePlanId?: string;
    pricePlanName?: string;
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    sessionId?: string;
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    venueId?: string;
  };

export type V3AddOrderOpenPayReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 找零金额 */
    changeAmount?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 当前时间 */
    currentTime?: number;
    /** 优惠原因 */
    discountReason?: string;
    /** 员工ID */
    employeeId?: string;
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否免单 */
    isFree?: boolean;
    /** 是否赠品 */
    isGift?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 原始金额 优惠金额 = 原始金额 - 应付金额 */
    originalFee?: number;
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付总金额 */
    payAmount?: number;
    /** 支付记录ID */
    payRecords?: PayRecordVO[];
    pricePlanId?: string;
    pricePlanName?: string;
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    sessionId?: string;
    /** 应付金额 = 实付金额 + 抹零金额 */
    shouldFee?: number;
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    venueId?: string;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type V3AddOrderOpenReqDto = {
    /** 预订ID */
    bookingId?: string;
    /** 买钟时长 单位：分钟 */
    buyMinute?: number;
    /** 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠） */
    consumptionMode?: string;
    /** 当前时间 */
    currentTime?: number;
    /** 员工ID */
    employeeId?: string;
    endTime?: number;
    /** 商品信息-套餐内 */
    inOrderProductInfos?: OrderProductVO[];
    /** 是否赠品 */
    isGift?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 【买断】最低消费金额 */
    minimumCharge?: number;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 商品信息-套餐外 */
    outOrderProductInfos?: OrderProductVO[];
    /** 支付总金额 */
    payAmount?: number;
    pricePlanId?: string;
    pricePlanName?: string;
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    /** 选择的计费方式-套餐区域id */
    selectedAreaId?: string;
    /** 选择的计费方式-房间类型id */
    selectedRoomTypeId?: string;
    sessionId?: string;
    startTime?: number;
    /** 买钟金额 单位：分 */
    timeChargeAmount?: number;
    /** 买钟结束时间 格式：HH:mm */
    timeChargeEndTime?: string;
    /** 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间、countTime:计时 */
    timeChargeMode?: string;
    /** 买钟价格类型 基础价格、区域价格、节假日价格 */
    timeChargeType?: string;
    venueId?: string;
  };

export type V3OrderGiftProductReqDto = {
    employeeId?: string;
    giftEmployeeId?: string;
    giftEmployeeName?: string;
    giftReason?: string;
    memberId?: string;
    orderProductVOs?: OrderProductVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 支付总金额 */
    payAmount?: number;
    roomId?: string;
    sessionId?: string;
    venueId?: string;
  };

export type V3OrderGiftTimeReqDto = {
    employeeId?: string;
    endTime?: number;
    giftEmployeeId?: string;
    giftEmployeeName?: string;
    giftMinute?: number;
    giftReason?: string;
    memberId?: string;
    orderRoomPlanVOs?: OrderRoomPlanVO[];
    roomId?: string;
    sessionId?: string;
    startTime?: number;
    venueId?: string;
  };

export type V3OrderQueryProductSalesReqDto = {
    employeeId?: string;
    endTime?: number;
    startTime?: number;
    venueId?: string;
  };

export type V3OrderReopenReqDto = {
    employeeId?: string;
    roomId?: string;
    venueId?: string;
  };

export type V3QueryOrderCleanRoomFinishReqDto = {
    /** 员工ID（可选） */
    employeeId?: string;
    /** 房间ID（可选） */
    roomId?: string;
    /** 场次ID（可选） */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3QueryOrderPayCalculateReqDto = {
    employeeId?: string;
    orderVOs?: OrderVO[];
    payBillVO?: PayBillVO;
    roomId?: string;
    session?: SessionVO;
    sessionId?: string;
    venueId?: string;
  };

export type V3QueryOrderPayQueryReqDto = {
    billId?: string;
    employeeId?: string;
    roomId?: string;
    sessionId?: string;
    venueId?: string;
  };

export type V3QueryOrderPayReqDto = {
    /** 找零金额 */
    changeAmount?: number;
    /** 挂账账户ID */
    creditAccountId?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 优惠金额 */
    discountAmount?: number;
    /** 优惠原因 */
    discountReason?: string;
    employeeId?: string;
    /** 是否免单 */
    isFree?: boolean;
    /** 会员金额 */
    memberAmount?: number;
    /** 会员卡ID-登录人 */
    memberCardId?: string;
    /** 订单ID */
    orderNos?: string[];
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    /** 原始金额 */
    originalFee?: number;
    /** 支付记录ID */
    payRecords?: PayRecordVO[];
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    roomId?: string;
    sessionId?: string;
    /** 应付金额 */
    shouldFee?: number;
    /** 总金额-实际支付金额或会员卡的本金 */
    totalFee?: number;
    venueId?: string;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type V3QueryOrderRefundReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 退款商品 */
    orderProductVOs?: OrderProductVO[];
    /** 退款金额 */
    refundAmount?: number;
    /** 退款类型 back: 原路径返回 cash: 现金退款 */
    refundWayType?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type V3QueryOrderRefundViewReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type V3QueryOrderRoomFeeRefundReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 退房费 */
    orderNos?: string[];
    /** 退款金额 */
    refundAmount?: number;
    /** 退款类型 back: 原路径返回 cash: 现金退款 */
    refundWayType?: string;
    /** 关联的房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 所属门店ID */
    venueId?: string;
  };
