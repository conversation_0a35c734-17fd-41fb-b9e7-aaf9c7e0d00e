// user相关类型定义

export type AddERPUserAndEmployeeReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ERP用户ID */
    erpUserId?: string;
  };

export type AddERPUserReqDto = {
    /** 头像 */
    avatar?: string;
    /** 性别 */
    gender?: string;
    /** 用户名称 */
    name?: string;
    /** 密码 */
    password?: string;
    /** 电话号码 */
    phone?: string;
    /** 微信unionid */
    unionid?: string;
  };

export type AddWarehouseReqDto = {
    /** 是否为默认酒水仓库 */
    isDefaultLiquorStorage?: boolean;
    /** 仓库名称 */
    name?: string;
    /** 备注 */
    remark?: string;
  };

export type DeleteERPUserAndEmployeeReqDto = {
    /** 关联ID */
    id?: string;
  };

export type DeleteERPUserReqDto = {
    /** 用户ID */
    id?: string;
  };

export type DeleteWarehouseReqDto = {
    /** 仓库ID */
    id?: string;
  };

export type ERPUserVO = {
    /** 头像 */
    avatar?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** 用户名称 */
    name?: string;
    /** 电话号码 */
    phone?: string;
    /** 状态值 */
    state?: number;
    /** 微信unionid */
    unionid?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PageVOArrayVoERPUserVO = {
    data?: ERPUserVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type QueryERPUserAndEmployeeReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ERP用户ID */
    erpUserId?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 新增门店ID查询条件 */
    venueId?: string;
  };

export type QueryERPUserReqDto = {
    /** 头像 */
    avatar?: string;
    /** 性别 */
    gender?: string;
    /** 用户ID */
    id?: string;
    /** 用户名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 电话号码 */
    phone?: string;
    /** 微信unionid */
    unionid?: string;
  };

export type QueryWarehouseReqDto = {
    /** 仓库ID */
    id?: string;
    /** 是否为默认酒水仓库 */
    isDefaultLiquorStorage?: boolean;
    /** 仓库名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 备注 */
    remark?: string;
  };

export type ResultArrayVoERPUserVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ERPUserVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoERPUserVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: ERPUserVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoERPUserVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoERPUserVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type UpdateERPUserAndEmployeeReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ERP用户ID */
    erpUserId?: string;
    /** 关联ID */
    id?: string;
  };

export type UpdateERPUserReqDto = {
    /** 头像 */
    avatar?: string;
    /** 性别 */
    gender?: string;
    /** 用户ID */
    id?: string;
    /** 用户名称 */
    name?: string;
  };

export type UpdateWarehouseReqDto = {
    /** 仓库ID */
    id?: string;
    /** 是否为默认酒水仓库 */
    isDefaultLiquorStorage?: boolean;
    /** 仓库名称 */
    name?: string;
    /** 备注 */
    remark?: string;
  };
