// other相关类型定义
import { VenueVO } from './venue';
import { CheckoutBillDataVO } from './payment';
import { SessionVO, PayBillVO } from './member';
import { SessionOrderDataVO, StorageOrderBriefVO } from './order';
import { EmployeeVO } from './employee';
import { PayRecordVO, WithdrawableItemVO } from './product';
import { PermissionRoleVO } from './permission';

export type AddAreaReqDto = {
    /** 区域容量 */
    capacity?: number;
    /** 区域描述 */
    description?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddAsExampleReqDto = {
    /** 区域容量 */
    capacity?: number;
    /** 区域描述 */
    description?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddBirthdayGreetingReqDto = {
    /** 客户群组 */
    customerGroup?: string;
    /** 提前天数 */
    daysInAdvance?: number;
    /** 短信模板 */
    smsTemplate?: string;
  };

export type AddCallMessageReqDto = {
    /** 呼叫来源 */
    callSrc?: string;
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫类型名称 */
    callTypeName?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 状态 */
    status?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddCallTypesReqDto = {
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫类型名称 */
    callTypeName?: string;
    /** 排序 */
    sortStr?: string;
    /** 类型 */
    type?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddCashierMachineReqDto = {
    /** 授权IP */
    grantIp?: string;
    /** 是否为主收银 */
    isMain?: boolean;
    /** mac地址 */
    mac?: string;
    /** 收银机名称 */
    name?: string;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 备注 */
    remark?: string;
    /** 是否使用网络打印机 */
    useNetworkPrinter?: boolean;
    /** 门店ID */
    venueId?: string;
  };

export type AddCommissionPlanReqDto = {
    /** 允许的预订数量 */
    allowedBookingQuantity?: number;
    /** 允许的房间类型列表 */
    allowedRoomTypes?: string;
    /** 佣金金额 */
    amount?: number;
    /** 计算方法 */
    calculationMethod?: string;
    /** 排除的产品列表 */
    excludedProducts?: string;
    /** 是否包含房间轮换 */
    includesRoomRotation?: boolean;
    /** 最大佣金金额 */
    maximumCommission?: number;
    /** 最低账单金额 */
    minimumBillAmount?: number;
    /** 佣金计划名称 */
    name?: string;
    /** 是否使用分层充值佣金 */
    tieredRechargeCommission?: boolean;
    /** 时间段列表 */
    timePeriods?: string;
    /** 佣金类型 */
    type?: string;
  };

export type AddCommissionReqDto = {
    /** 金额 */
    amount?: number;
    /** 佣金ID */
    commissionId?: string;
    /** 日期 */
    date?: number;
  };

export type AddCommonRemarkReqDto = {
    /** 备注内容 */
    content?: string;
    /** 备注类型 */
    type?: string;
  };

export type AddConstructionAssistanceReqDto = {
    /** 所属店铺ID */
    storeId?: string;
    /** 技术员电话 */
    technicianPhone?: string;
    /** 有效期 */
    validityPeriod?: number;
  };

export type AddConsumptionCashbackReqDto = {
    /** 返现比例 */
    cashbackRate?: number;
    /** 最大返现金额 */
    maximumCashback?: number;
  };

export type AddCreditAccountReqDto = {
    /** 信用额度 */
    creditLimit?: number;
    /** 挂账单位 */
    creditUnit?: string;
    /** 当前余额 */
    currentBalance?: number;
    /** 是否启用 */
    enable?: boolean;
    /** 姓名 */
    name?: string;
    /** 手机号 */
    phone?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddCreditUnitReqDto = {
    /** 联系人姓名 */
    contactName?: string;
    /** 信用额度 */
    creditLimit?: number;
    /** 信用单位名称 */
    name?: string;
  };

export type AddCustomerGroupReqDto = {
    /** 年龄范围 */
    ageRange?: number;
    /** 生日范围 */
    birthdayRange?: string;
    /** 卡余额 */
    cardBalance?: number;
    /** 卡等级列表 */
    cardLevels?: string;
    /** 消费行为 */
    consumptionBehavior?: string;
    /** 性别 */
    gender?: string;
    /** 客户组名称 */
    name?: string;
    /** 积分范围 */
    pointsRange?: number;
    /** 总消费金额 */
    totalConsumptionAmount?: number;
    /** 总消费次数 */
    totalConsumptionTimes?: number;
  };

export type AddCustomerSourceReqDto = {
    /** 客户来源名称 */
    name?: string;
  };

export type AddCustomerTagReqDto = {
    /** 客户标签名称 */
    name?: string;
  };

export type AddDataCleanupReqDto = {
    /** 数据类型 */
    dataType?: string;
    /** 请求时间 */
    requestTime?: number;
    /** 状态 */
    status?: string;
  };

export type AddFlavorReqDto = {
    /** 描述 */
    description?: string;
    /** 口味名称 */
    name?: string;
    /** 排序号 */
    sortNum?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddHistoricalRecordReqDto = {
    /** 结束台数 */
    finishedTables?: number;
    /** 开台数量 */
    openTables?: number;
    /** 结算金额 */
    settlementAmount?: number;
    /** 总开台数量 */
    totalOpenedTables?: number;
  };

export type AddHolidayReqDto = {
    /** 日期 */
    date?: string;
    /** 节假日名称 */
    name?: string;
    /** 节假日类型 1:节假日 2:工作日 */
    type?: string;
    /** 所属店铺ID */
    venueId?: string;
  };

export type AddIngredientTypeReqDto = {
    /** 配料类型描述 */
    description?: string;
    /** 配料类型名称 */
    name?: string;
  };

export type AddMarketServiceReqDto = {
    /** 是否启用 */
    isEnabled?: boolean;
    /** 服务名称 */
    serviceName?: string;
  };

export type AddMerchantServiceReqDto = {
    /** 授权码 */
    authorizationCode?: string;
    /** 过期日期 */
    expirationDate?: number;
  };

export type AddNotificationSettingReqDto = {
    /** 接收人 */
    recipient?: string;
    /** 触发条件 */
    triggerCondition?: string;
    /** 通知类型 */
    type?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type AddOnlineOperationSettingsReqDto = {
    /** 是否允许混合支付 */
    allowMixedPayment?: boolean;
    /** 是否自动连接WiFi */
    autoWifiConnect?: boolean;
    /** 是否允许提前入住补偿 */
    earlyCheckinCompensation?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** 是否需要输入销售员 */
    inputSalesperson?: boolean;
    /** 是否允许手动输入房间号 */
    manualRoomInput?: boolean;
    /** 最小计费时长 */
    minBillingDuration?: number;
    /** 在线营业时间 */
    onlineBusinessHours?: string;
    /** 在线房间计费模式 */
    onlineRoomBillingMode?: string;
    /** 订单模式 */
    orderMode?: string;
    /** 订单支付超时时间 */
    orderPaymentTimeout?: number;
    /** 套餐价格展示方式 */
    packagePriceDisplay?: string;
    /** 是否允许快速注册会员卡 */
    quickCardRegistration?: boolean;
    /** 推荐会员卡 */
    recommendedMemberCard?: string;
    /** 开房是否使用会员折扣 */
    useOpenRoomMemberDiscount?: boolean;
    /** WiFi密码 */
    wifiPassword?: string;
    /** WiFi用户名 */
    wifiUsername?: string;
  };

export type AddOperationSettingsReqDto = {
    /** 是否允许收银员添加员工 */
    allowCashierAddEmployee?: boolean;
    /** 是否允许收银员选择模式 */
    allowCashierSelectMode?: boolean;
    /** 是否允许最低消费差异 */
    allowMinConsumptionDifference?: boolean;
    /** 是否允许超额购买预估清台 */
    allowOverbuyingEstimatedClearance?: boolean;
    /** 是否自动取消次日清台 */
    autoCancelClearanceNextDay?: boolean;
    /** 结账后是否自动锁定房间 */
    autoLockRoomAfterCheckout?: boolean;
    /** 是否自动切换到时间计费 */
    autoSwitchToTimeBilling?: boolean;
    /** 呼叫处理超时时间 */
    callProcessingTimeout?: number;
    /** 取消开房时长 */
    cancelOpenDuration?: number;
    /** 收银员数据保留天数 */
    cashierDataRetentionDays?: number;
    /** 结账方式 */
    checkoutMode?: string;
    /** 柜台自动登出时间 */
    counterAutoLogoutTime?: number;
    /** 柜台点单模式 */
    counterOrderMode?: string;
    /** 是否默认发送预订短信 */
    defaultSendReservationSMS?: boolean;
    /** 折扣是否满足最低消费 */
    discountMeetMinConsumption?: boolean;
    /** 折扣是否仅限超出部分 */
    discountOnlyExcess?: boolean;
    /** 是否启用跨账单调整 */
    enableCrossBillingAdjustment?: boolean;
    /** 是否启用多个销售员 */
    enableMultipleSalespersons?: boolean;
    /** 是否启用退货确认 */
    enableReturnConfirmation?: boolean;
    /** 是否启用拆分计费 */
    enableSplitBilling?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** 客人时长 */
    guestDuration?: number;
    /** 继承会员卡模式 */
    inheritMemberCardMode?: string;
    /** 会员卡支付方式 */
    memberCardPaymentMode?: string;
    /** 会员卡验证方式 */
    memberCardVerificationMethod?: string;
    /** 会员折扣方式 */
    memberDiscountMethod?: string;
    /** 最小续费时长 */
    minRenewalDuration?: number;
    /** 手机点单现金支付方式 */
    mobileOrderCashPaymentMode?: string;
    /** 手机点单结账方式 */
    mobileOrderCheckoutMode?: string;
    /** 手机点单是否只存储已点商品 */
    mobileOrderOnlyStoreOrderedItems?: boolean;
    /** 手机点单酒水取回方式 */
    mobileOrderWineRetrievalMode?: string;
    /** 手机点单酒水寄存方式 */
    mobileOrderWineStorageMode?: string;
    /** 订单屏幕保持模式 */
    orderScreenHoldMode?: string;
    /** 产品价格会员折扣方式 */
    productPriceMemberDiscountMethod?: string;
    /** 退款时长 */
    refundDuration?: number;
    /** 重新开房时长 */
    reopenDuration?: number;
    /** 下单是否需要房间 */
    requireRoomForOrder?: boolean;
    /** 酒水是否需要绑定房间 */
    requireRoomForWine?: boolean;
    /** 预订到期时长 */
    reservationExpirationDuration?: number;
    /** 是否限制所有会员查询 */
    restrictAllMemberQuery?: boolean;
    /** 房间变更规则 */
    roomChangeRule?: string;
    /** 房间清洁到空闲时长 */
    roomCleanToIdleDuration?: number;
    /** 房间价格会员折扣方式 */
    roomPriceMemberDiscountMethod?: string;
    /** 四舍五入类型 */
    roundingType?: string;
    /** 第二次计费时长 */
    secondBillingDuration?: number;
    /** 是否在收银员端显示库存 */
    showInventoryOnCashier?: boolean;
    /** 拆分计费房间百分比 */
    splitBillingRoomPercentage?: number;
    /** 超市结账方式 */
    supermarketCheckoutMode?: string;
    /** 酒水核销方式 */
    wineVerificationMethod?: string;
  };

export type AddPhoneBlacklistReqDto = {
    /** 电话号码 */
    phoneNumber?: string;
    /** 所属店铺ID */
    storeId?: string;
  };

export type AddPosMachineReqDto = {
    /** IP地址 */
    ipAddress?: string;
    /** 状态 */
    status?: string;
    /** 所属店铺ID */
    storeId?: string;
    /** 类型 */
    type?: string;
  };

export type AddPrintTemplateReqDto = {
    /** 打印份数 */
    copies?: number;
    /** 合并产品 */
    mergeProducts?: boolean;
    /** 打印模板名称 */
    name?: string;
    /** 纸张大小 */
    paperSize?: string;
    /** 打印礼品 */
    printGifts?: boolean;
    /** 打印模式 */
    printMode?: string;
    /** 仅打印本地使用 */
    printOnlyLocalUse?: boolean;
    /** 打印支付方式计数 */
    printPaymentMethodCount?: boolean;
    /** 打印产品总金额 */
    printProductTotalAmount?: boolean;
    /** 打印反向账单 */
    printReverseBill?: boolean;
    /** 打印机器人配送二维码 */
    printRobotDeliveryQR?: boolean;
    /** 打印时间 */
    printTime?: string;
    /** 打印酒水退款 */
    printWineRefund?: boolean;
    /** 打印零账单 */
    printZeroBill?: boolean;
    /** 打印机类型 */
    printerType?: string;
    /** 产品分组方式 */
    productGroupingMethod?: string;
    /** 产品类型配置 */
    productTypeConfig?: string;
    /** 生产点份数 */
    productionPointCopies?: number;
    /** 自助服务份数 */
    selfServiceCopies?: number;
    /** 模板内容 */
    templateContent?: string;
    /** 酒水订单打印顺序 */
    wineOrderPrintSequence?: string;
  };

export type AddRechargePackageReqDto = {
    /** 金额 */
    amount?: number;
    /** 金额类型 */
    amountType?: string;
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送酒水 */
    bonusBeverages?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 间隔赠送金额 */
    intervalBonusAmount?: string;
    /** 百分比赠送金额 */
    percentageBonusAmount?: number;
    /** 备注 */
    remark?: string;
  };

export type AddRedemptionRecordReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 记录ID */
    recordId?: string;
    /** 核销时间 */
    redemptionTime?: number;
    /** 凭证ID */
    voucherId?: string;
  };

export type AddRewardReqDto = {
    /** 金额 */
    amount?: number;
    /** 奖励ID */
    rewardId?: string;
    /** 奖励时间 */
    rewardTime?: number;
  };

export type AddRouterReqDto = {
    /** 是否启用DHCP */
    dhcpEnabled?: boolean;
    /** IP地址 */
    ipAddress?: string;
  };

export type AddSalesPerformanceReqDto = {
    /** 销售金额 */
    amount?: number;
    /** 销售员ID */
    employeeId?: string;
    /** 销售产品类型 */
    productType?: string;
    /** 销售数量 */
    quantity?: number;
    /** 销售日期 */
    saleDate?: number;
  };

export type AddSequencerReqDto = {
    /** 延迟时间 */
    delayTime?: number;
    /** 网关 */
    gateway?: string;
    /** IP地址 */
    ipAddress?: string;
    /** MAC地址 */
    macAddress?: string;
    /** 型号 */
    model?: string;
    /** 服务器IP */
    serverIP?: string;
    /** 状态 */
    status?: string;
    /** 温度 */
    temperature?: number;
  };

export type AddServiceRewardSettingsReqDto = {
    /** 是否启用 */
    enabled?: boolean;
    /** 分成比例 */
    splitRatio?: number;
  };

export type AddSessionReqDto = {
    /** 代定人 */
    agentPerson?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 客群标签 */
    customerTag?: string;
    /** 使用时长 */
    duration?: number;
    /** 轮房人 */
    dutyPerson?: string;
    /** 员工ID */
    employeeId?: string;
    /** 支付员工ID */
    employeeIdPay?: string;
    /** 关房时间 */
    endTime?: number;
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 最低消费 */
    minConsume?: number;
    /** 订单来源 */
    orderSource?: string;
    /** 预付余额 */
    prePayBalance?: number;
    /** 排位号码 */
    rankNumber?: string;
    /** 包厢费用 */
    roomFee?: number;
    /** 房间ID */
    roomId?: string;
    /** 开台ID */
    sessionId?: string;
    /** 开台时间 */
    startTime?: number;
    /** 支付状态 */
    status?: string;
    /** 超市费用 */
    supermarketFee?: number;
    /** 总计费用 */
    totalFee?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddSmsServiceReqDto = {
    /** 余额 */
    balance?: number;
    /** 签名 */
    signature?: string;
  };

export type AddStatisticsPeriodReqDto = {
    /** 描述 */
    description?: string;
    /** 名称 */
    name?: string;
  };

export type AddSubtitleInfoReqDto = {
    /** 内容 */
    content?: string;
    /** 是否循环播放 */
    loopPlay?: boolean;
    /** 重复次数 */
    repeatCount?: number;
    /** 滚动速度 */
    scrollSpeed?: number;
    /** 停留时间 */
    stayTime?: number;
  };

export type AddTurnoverDataReqDto = {
    /** 日期 */
    date?: number;
    /** 折扣金额 */
    discountAmount?: number;
    /** 赠送金额 */
    giftAmount?: number;
    /** 会员充值 */
    memberRecharge?: number;
    /** 统计周期 */
    period?: string;
    /** 积分兑换金额 */
    pointsExchangeAmount?: number;
    /** 产品实收 */
    productActualReceived?: number;
    /** 产品应收 */
    productReceivable?: number;
    /** 包厢实收 */
    roomActualReceived?: number;
    /** 包厢应收 */
    roomReceivable?: number;
    /** 总营业额 */
    totalTurnover?: number;
  };

export type AddTvScreenActivityReqDto = {
    /** 活动类型 */
    activityType?: string;
    /** 结束时间 */
    endTime?: number;
    /** 产品信息 */
    productInfo?: string;
    /** 开始时间 */
    startTime?: number;
  };

export type AddVodSettingsReqDto = {
    /** ERP服务器IP地址 */
    erpServerIP?: string;
    /** 控制器型号 */
    sequencerModel?: string;
    /** 点歌屏提醒 */
    songScreenReminder?: boolean;
    /** 门店ID */
    venueId?: string;
    /** VOD服务器IP地址 */
    vodServerIP?: string;
  };

export type AddVoucherReqDto = {
    /** 计划ID */
    planId?: string;
    /** 状态 */
    status?: string;
    /** 凭证码 */
    voucherCode?: string;
    /** 凭证ID */
    voucherId?: string;
  };

export type APIResponse = {
    code?: number;
    data?: any;
    message?: string;
  };

export type AppUpgradeVO = {
    /** 客户端类型 */
    clientType?: string;
    /** 创建时间 */
    createTime?: number;
    /** 下载地址 */
    downloadUrl?: string;
    /** 文件MD5 */
    fileMd5?: string;
    /** 文件大小（字节） */
    fileSize?: number;
    /** 是否强制升级 */
    forceUpgrade?: boolean;
    /** 升级包ID */
    id?: string;
    /** 更新时间 */
    updateTime?: number;
    /** 升级内容 */
    upgradeContent?: string;
    /** 升级标题 */
    upgradeTitle?: string;
    /** 版本号（用于比较） */
    versionCode?: number;
    /** 版本名称（显示给用户） */
    versionName?: string;
  };

export type Area = {
    /** 区域ID */
    id?: string;
    /** 区域名称 */
    name?: string;
  };

export type AreaConfig = {
    /** 区域列表 */
    areas?: Area[];
  };

export type AreaVO = {
    /** 容量 */
    capacity?: number;
    /** 创建时间 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** 区域ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type AsExampleVO = {
    /** 容量 */
    capacity?: number;
    /** 创建时间 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** 区域ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type BatchRequest = {
    action: string;
    employeeId?: string;
    ids: string[];
    payload?: Record<string, any>;
    table: string;
    venueId?: string;
  };

export type BatchWithdrawItem = {
    /** 产品规格 */
    productSpec?: string;
    /** 产品单位 */
    productUnit?: string;
    /** 提取数量 */
    quantity: number;
    /** 存储ID */
    storageId: string;
    /** 存酒单号 */
    storageOrderNo: string;
  };

export type BirthdayGreetingVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 客户群组 */
    customerGroup?: string;
    /** 提前天数 */
    daysInAdvance?: number;
    /** ID */
    id?: string;
    /** 短信模板 */
    smsTemplate?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type BusinessData = {
    /** 账单数 */
    billCount?: number;
    /** 开台数 */
    openCount?: number;
    /** 已结开台数 */
    openCountPaid?: number;
    /** 未结开台数 */
    openCountUnpaid?: number;
    /** 点单数 */
    orderPaidCount?: number;
    /** 待结订单数 */
    orderUnpaidCount?: number;
  };

export type BusinessOverview = {
    /** 员工赠送 */
    employeeGift?: number;
    /** 低消差额 */
    lowConsumptionFee?: number;
    /** 会员优惠 */
    memberDiscount?: number;
    /** 商家优惠 */
    merchantDiscount?: number;
    /** 净收 */
    netFee?: number;
    /** 商品费用 */
    productFee?: number;
    /** 房费 */
    roomFee?: number;
    /** 应收 */
    shouldFee?: number;
    /** 实收 */
    totalFee?: number;
    /** 未结金额 */
    unpaidFee?: number;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type BusinessSummary = {
    /** 营业实收 */
    actualReceived?: number;
    /** 计费抵商品 */
    billDeductionForProducts?: number;
    /** 办卡金额 */
    cardAmount?: number;
    /** 办卡张数 */
    cardCount?: number;
    /** 补卡金额 */
    cardReplacementAmount?: number;
    /** 冲账实收 */
    chargeOffActualReceived?: number;
    /** 冲账应收 */
    chargeOffReceivable?: number;
    /** 优惠券优惠 */
    couponDiscount?: number;
    /** 员工商品赠送 */
    employeeProductGift?: number;
    /** 会员优惠 */
    memberDiscount?: number;
    /** 商家优惠 */
    merchantDiscount?: number;
    /** 营业净收 */
    netReceived?: number;
    /** 商品实收 */
    productActualReceived?: number;
    /** 商品抵房费 */
    productDeductionForRoom?: number;
    /** 营业应收 */
    receivable?: number;
    /** 充值金额 */
    rechargeAmount?: number;
    /** 充值赠送 */
    rechargeBonusAmount?: number;
    /** 续期费 */
    renewalFee?: number;
    /** 包厢实收 */
    roomActualReceived?: number;
    /** 抹零金额 */
    roundingAmount?: number;
  };

export type BuyoutTimeConfig = {
    /** 可用时段结束时间 (HH:mm) */
    availableTimeEnd?: string;
    /** 可用时段开始时间 (HH:mm) */
    availableTimeStart?: string;
    /** 日期配置，TimeType为 "date" 时使用 */
    dateConfig?: DateConfig;
    /** 时间类型: "weekday", "date" */
    timeType?: string;
    /** 星期配置，TimeType为 "weekday" 时使用 */
    weekdayConfig?: WeekdayConfig;
  };

export type CallMessageVO = {
    /** 呼叫来源 小程序、触摸屏 */
    callSrc?: string;
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫类型名称 */
    callTypeName?: string;
    /** 取消人ID */
    cancelId?: string;
    /** 取消人 */
    cancelName?: string;
    /** 取消时间 */
    cancelTime?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** 对应员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 处理人ID */
    optId?: string;
    /** 处理人 */
    optName?: string;
    /** 处理时间 */
    optTime?: number;
    /** 对应房间ID */
    roomId?: string;
    /** 对应房间名称 */
    roomName?: string;
    /** 对应场次ID */
    sessionId?: string;
    /** 状态值 */
    state?: number;
    /** 房间状态(0:未处理,1:已处理，2:已取消) */
    status?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 对应场馆ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type CallTypesVO = {
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫名称-中文 */
    callTypeName?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 排序 */
    sortStr?: string;
    /** 状态值 */
    state?: number;
    /** 类型，0:默认，1：通用 */
    type?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type CashierMachineBindInfoVO = {
    /** 收银机信息 */
    cashierMachine?: CashierMachineVO;
    /** 是否绑定 */
    isBind?: boolean;
    /** 上一次的收银机IP */
    lastIP?: string;
    /** 门店信息 */
    venue?: VenueVO;
  };

export type CashierMachineVO = {
    /** 客户端类型 */
    clientType?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 授权IP */
    grantIp?: string;
    /** ID */
    id?: string;
    /** 是否绑定 */
    isBind?: boolean;
    /** 是否为主收银 */
    isMain?: boolean;
    /** mac地址 */
    mac?: string;
    /** 收银机名称 */
    name?: string;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 备注 */
    remark?: string;
    /** 状态值 */
    state?: number;
    /** 是否使用网络打印机 */
    useNetworkPrinter?: boolean;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type ChangePasswordReqDto = {
    /** 确认密码 */
    confirmPassword?: string;
    /** 新密码 */
    newPassword?: string;
    /** 手机验证码 */
    smsCode?: string;
  };

export type Channel = {
    /** 渠道ID */
    id?: string;
    /** 渠道名称 */
    name?: string;
  };

export type ChannelConfig = {
    /** 渠道列表 */
    channels?: Channel[];
  };

export type CheckoutPrintRecordVO = {
    /** 结账单数据 */
    checkoutBillData?: CheckoutBillDataVO;
    /** 创建时间 */
    createTime?: number;
    /** 设备名称 */
    deviceName?: string;
    /** 错误信息 */
    errorMsg?: string;
    /** 记录ID */
    id?: string;
    /** 操作员ID */
    operatorId?: string;
    /** 操作员姓名 */
    operatorName?: string;
    /** 结账单ID */
    payBillId?: string;
    /** 打印单号 */
    printNo?: string;
    /** 打印时间戳 */
    printTime?: number;
    /** 打印类型 */
    printType?: string;
    /** 备注 */
    remark?: string;
    /** 会话ID */
    sessionId?: string;
    /** 状态 */
    status?: number;
    /** 更新时间 */
    updateTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type CheckUpgradeReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 当前版本号 */
    versionCode?: number;
  };

export type CommissionPlanVO = {
    /** 允许的预订数量 */
    allowedBookingQuantity?: number;
    /** 允许的房间类型列表 */
    allowedRoomTypes?: string;
    /** 佣金金额 */
    amount?: number;
    /** 计算方法 */
    calculationMethod?: string;
    /** 创建时间 */
    ctime?: number;
    /** 排除的产品列表 */
    excludedProducts?: string;
    /** ID */
    id?: string;
    /** 是否包含房间轮换 */
    includesRoomRotation?: boolean;
    /** 最大佣金金额 */
    maximumCommission?: number;
    /** 最低账单金额 */
    minimumBillAmount?: number;
    /** 佣金计划名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 是否使用分层充值佣金 */
    tieredRechargeCommission?: boolean;
    /** 时间段列表 */
    timePeriods?: string;
    /** 佣金类型 */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type CommissionVO = {
    /** 金额 */
    amount?: number;
    /** 佣金ID */
    commissionId?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 日期 */
    date?: number;
    /** ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type CommonRemarkVO = {
    /** 备注内容 */
    content?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 备注类型 */
    type?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type ConstructionAssistanceVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 所属店铺ID */
    storeId?: string;
    /** 技术员电话 */
    technicianPhone?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 有效期 */
    validityPeriod?: number;
    /** 版本号 */
    version?: number;
  };

export type ConsumptionCashbackVO = {
    /** 返现比例 */
    cashbackRate?: number;
    /** 创建时间 */
    ctime?: number;
    /** 唯一id */
    id?: string;
    /** 最大返现金额 */
    maximumCashback?: number;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type CreateAppUpgradeReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 下载地址 */
    downloadUrl?: string;
    /** 文件MD5 */
    fileMd5?: string;
    /** 文件大小（字节） */
    fileSize?: number;
    /** 是否强制升级 */
    forceUpgrade?: boolean;
    /** 升级内容 */
    upgradeContent?: string;
    /** 升级标题 */
    upgradeTitle?: string;
    /** 版本号（用于比较） */
    versionCode?: number;
    /** 版本名称（显示给用户） */
    versionName?: string;
  };

export type CreateCheckoutPrintRecordReqDto = {
    /** 订单编号数组，用于获取订单详情来构建结账单数据 */
    orderNos?: string[];
    /** 结账单ID */
    payBillId: string;
    /** 会话ID */
    sessionId: string;
    /** 门店ID */
    venueId: string;
  };

export type CreateOpenTablePrintRecordReqDto = {
    /** 订单编号数组，用于获取订单详情来构建开台单数据 */
    orderNos?: string[];
    /** 开台单号/场次ID */
    sessionId: string;
    /** 门店ID */
    venueId: string;
  };

export type CreateRequest = {
    employeeId?: string;
    record: Record<string, any>;
    table: string;
    venueId?: string;
  };

export type CreditAccountVO = {
    /** 信用额度 */
    creditLimit?: number;
    /** 挂账单位 */
    creditUnit?: string;
    /** 创建时间 */
    ctime?: number;
    /** 当前余额 */
    currentBalance?: number;
    /** 是否启用 */
    enable?: boolean;
    /** 唯一id */
    id?: string;
    /** 姓名 */
    name?: string;
    /** 手机号 */
    phone?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type CreditUnitVO = {
    /** 联系人姓名 */
    contactName?: string;
    /** 信用额度 */
    creditLimit?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 信用单位名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type CustomerGroupVO = {
    /** 年龄范围 */
    ageRange?: number;
    /** 生日范围 */
    birthdayRange?: string;
    /** 卡余额 */
    cardBalance?: number;
    /** 卡等级列表 */
    cardLevels?: string;
    /** 消费行为 */
    consumptionBehavior?: string;
    /** 创建时间 */
    ctime?: number;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** 客户组名称 */
    name?: string;
    /** 积分范围 */
    pointsRange?: number;
    /** 状态 */
    state?: number;
    /** 总消费金额 */
    totalConsumptionAmount?: number;
    /** 总消费次数 */
    totalConsumptionTimes?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type CustomerInfoVO = {
    customerId?: string;
    customerName?: string;
    memberCardId?: string;
    phoneNumber?: string;
  };

export type CustomerSourceVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 客户来源名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type CustomerTagVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 客户标签名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type DailyPaiedSessionVO = {
    /** 营业结束时间 unix timestamp */
    endTime?: number;
    /** 场次列表 */
    sessionVOs?: SessionVO[];
    /** 营业开始时间 unix timestamp */
    startTime?: number;
  };

export type DataCleanupVO = {
    /** 创建时间 */
    ctime?: number;
    /** 数据类型 */
    dataType?: string;
    /** ID */
    id?: string;
    /** 请求时间 */
    requestTime?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type DateConfig = {
    /** 结束日期 (YYYY-MM-DD 格式字符串) */
    dayEnd?: string;
    /** 开始日期 (YYYY-MM-DD 格式字符串) */
    dayStart?: string;
  };

export type DeleteAreaReqDto = {
    /** 门店ID */
    id?: string;
    /** 门店id */
    venueId?: string;
  };

export type DeleteAsExampleReqDto = {
    /** 区域ID */
    id?: string;
  };

export type DeleteBirthdayGreetingReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCallMessageReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCallTypesReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCashierMachineReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCommissionPlanReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCommissionReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCommonRemarkReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteConstructionAssistanceReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteConsumptionCashbackReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteCreditAccountReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteCreditUnitReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCustomerGroupReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCustomerSourceReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteCustomerTagReqDto = {
    /** 标签ID */
    id?: string;
  };

export type DeleteDataCleanupReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteDouyinGroupBuyingPlanReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteFlavorReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteHistoricalRecordReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteHolidayReqDto = {
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteIngredientTypeReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteMarketServiceReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteMerchantServiceReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteNotificationSettingReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteOnlineOperationSettingsReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteOperationSettingsReqDto = {
    /** ID */
    id?: string;
  };

export type DeletePhoneBlacklistReqDto = {
    /** ID */
    id?: string;
  };

export type DeletePointsExchangeReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type DeletePosMachineReqDto = {
    /** ID */
    id?: string;
  };

export type DeletePrintTemplateReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteRechargePackageReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteRecipeReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteRedemptionRecordReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type DeleteRequest = {
    employeeId?: string;
    id: string;
    table: string;
    venueId?: string;
  };

export type DeleteRewardReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteRouterReqDto = {
    /** 路由器ID */
    id?: string;
  };

export type DeleteSalesPerformanceReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteSequencerReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteServiceRewardSettingsReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteSessionReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type DeleteSmsServiceReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteStatisticsCategoryReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteStatisticsPeriodReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteSubtitleInfoReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteTurnoverDataReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteTvScreenActivityReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteVodSettingsReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type DeleteVoucherReqDto = {
    /** 唯一ID */
    id?: string;
  };

export type FlavorVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 口味名称 */
    name?: string;
    /** 排序号 */
    sortNum?: number;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店id */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type getApiWxEventParams = {
    /** 签名 */
    signature: string;
    /** 时间戳 */
    timestamp: string;
    /** 随机数 */
    nonce: string;
    /** 随机字符串 */
    echostr: string;
  };

export type GetAppUpgradeByVersionNameReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 版本名称 */
    versionName?: string;
  };

export type GetAppUpgradeByVersionReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 版本号 */
    versionCode?: number;
  };

export type GetCheckoutPrintRecordsByPayBillIdsReqDto = {
    /** 账单号数组 */
    payBillIds: string[];
    /** 门店ID */
    venueId: string;
  };

export type GetCheckoutPrintRecordsBySessionIdReqDto = {
    /** 会话ID */
    sessionId: string;
    /** 门店ID */
    venueId: string;
  };

export type GetOpenTablePrintRecordsBySessionIdReqDto = {
    /** 会话ID */
    sessionId: string;
    /** 门店ID */
    venueId: string;
  };

export type GetRequest = {
    employeeId?: string;
    id: string;
    include?: string;
    table: string;
    venueId?: string;
  };

export type getVcTableConfigOptionsParams = {
    /** 表名 */
    table: string;
    /** 值字段名，默认id */
    value?: string;
    /** 标签字段名，默认name */
    label?: string;
    /** 搜索关键词 */
    q?: string;
    /** 场所ID */
    venue_id?: string;
  };

export type HistoricalRecordVO = {
    /** 创建时间 */
    ctime?: number;
    /** 结束台数 */
    finishedTables?: number;
    /** 唯一id */
    id?: string;
    /** 开台数量 */
    openTables?: number;
    /** 结算金额 */
    settlementAmount?: number;
    /** 状态 */
    state?: number;
    /** 总开台数量 */
    totalOpenedTables?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type HolidayVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 日期 */
    date?: string;
    /** ID */
    id?: string;
    /** 节假日名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 节假日类型 1:节假日 2:工作日 */
    type?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 所属店铺ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type IngredientTypeVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 配料类型描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 配料类型名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type LeshuaPayCallbackModel = {
    amount?: string;
    attach?: string;
    bank_type?: string;
    channel_datetime?: string;
    channel_order_id?: string;
    error_code?: string;
    leshua_order_id?: string;
    merchant_id?: string;
    nonce_str?: string;
    openid?: string;
    out_transaction_id?: string;
    pay_time?: string;
    pay_way?: string;
    sign?: string;
    sign_type?: string;
    status?: string;
    sub_merchant_id?: string;
    sub_openid?: string;
    third_order_id?: string;
    trade_type?: string;
  };

export type LeshuaRefundCallbackModel = {
    attach?: string;
    discount_refund_amount?: string;
    error_code?: string;
    failure_reason?: string;
    leshua_order_id?: string;
    leshua_refund_id?: string;
    merchant_id?: string;
    merchant_refund_id?: string;
    nonce_str?: string;
    refund_amount?: string;
    refund_detail?: string;
    refund_time?: string;
    settlement_refund_amount?: string;
    sign?: string;
    sign_type?: string;
    status?: string;
    sub_merchant_id?: string;
    third_order_id?: string;
    total_amount?: string;
  };

export type ListAppUpgradeReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
  };

export type ListRequest = {
    employeeId?: string;
    filters?: any[];
    page?: number;
    pageSize?: number;
    sort?: string[];
    table: string;
    venueId?: string;
  };

export type ListResponse = {
    list?: Record<string, any>[];
    page?: number;
    pageSize?: number;
    total?: number;
  };

export type LshSimplePayInfo = {
    prepayUrl?: string;
  };

export type ManagerPhonePasswordLoginReqDto = {
    /** 密码 */
    password?: string;
    /** 手机号 */
    phone?: string;
    /** 场景字符串 */
    sceneStr?: string;
    /** 门店ID */
    venueId?: string;
  };

export type MarketServiceVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 服务名称 */
    serviceName?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type MerchantServiceVO = {
    /** 授权码 */
    authorizationCode?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 过期日期 */
    expirationDate?: number;
    /** ID */
    id?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type MiniAppGetPhoneNumberReqDto = {
    code?: string;
  };

export type MiniAppGetPhoneNumberRespDto = {
    errcode?: number;
    errmsg?: string;
    phone_info?: MiniAppGetPhoneNumberRespPhoneInfo;
  };

export type MiniAppGetPhoneNumberRespPhoneInfo = {
    countryCode?: string;
    phoneNumber?: string;
    purePhoneNumber?: string;
    watermark?: MiniAppGetPhoneNumberRespPhoneInfoWatermark;
  };

export type MiniAppGetPhoneNumberRespPhoneInfoWatermark = {
    appid?: string;
    timestamp?: number;
  };

export type MiniAppLoginReqDto = {
    /** 授权code */
    code?: string;
  };

export type MiniAppResponseVO = {
    openid?: string;
    unionid?: string;
  };

export type NotificationSettingVO = {
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 接收人 */
    recipient?: string;
    /** 状态 */
    state?: number;
    /** 触发条件 */
    triggerCondition?: string;
    /** 通知类型 */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 所属门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type OnlineOperationSettingsVO = {
    /** 是否允许混合支付 */
    allowMixedPayment?: boolean;
    /** 是否自动连接WiFi */
    autoWifiConnect?: boolean;
    /** 创建时间戳 */
    ctime?: number;
    /** 是否允许提前入住补偿 */
    earlyCheckinCompensation?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** ID */
    id?: string;
    /** 是否需要输入销售员 */
    inputSalesperson?: boolean;
    /** 是否允许手动输入房间号 */
    manualRoomInput?: boolean;
    /** 最小计费时长 */
    minBillingDuration?: number;
    /** 在线营业时间 */
    onlineBusinessHours?: string;
    /** 在线房间计费模式 */
    onlineRoomBillingMode?: string;
    /** 订单模式 */
    orderMode?: string;
    /** 订单支付超时时间 */
    orderPaymentTimeout?: number;
    /** 套餐价格展示方式 */
    packagePriceDisplay?: string;
    /** 是否允许快速注册会员卡 */
    quickCardRegistration?: boolean;
    /** 推荐会员卡 */
    recommendedMemberCard?: string;
    /** 状态值 */
    state?: number;
    /** 开房是否使用会员折扣 */
    useOpenRoomMemberDiscount?: boolean;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
    /** WiFi密码 */
    wifiPassword?: string;
    /** WiFi用户名 */
    wifiUsername?: string;
  };

export type OpenTablePrintRecordVO = {
    /** 创建时间 */
    createTime?: number;
    /** 打印设备名称 */
    deviceName?: string;
    /** 错误信息 */
    errorMsg?: string;
    /** 打印记录ID */
    id?: string;
    /** 操作员ID */
    operatorId?: string;
    /** 操作员姓名 */
    operatorName?: string;
    /** 打印单号 */
    printNo?: string;
    /** 打印时间 */
    printTime?: number;
    /** 打印类型 */
    printType?: string;
    /** 备注 */
    remark?: string;
    /** 场次ID */
    sessionId?: string;
    /** 开台单特有字段 */
    sessionOrderData?: SessionOrderDataVO;
    /** 打印状态 */
    status?: number;
    /** 更新时间 */
    updateTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type OperationSettingsVO = {
    /** 是否允许收银员添加员工 */
    allowCashierAddEmployee?: boolean;
    /** 是否允许收银员选择模式 */
    allowCashierSelectMode?: boolean;
    /** 是否允许最低消费差异 */
    allowMinConsumptionDifference?: boolean;
    /** 是否允许超额购买预估清台 */
    allowOverbuyingEstimatedClearance?: boolean;
    /** 是否自动取消次日清台 */
    autoCancelClearanceNextDay?: boolean;
    /** 结账后是否自动锁定房间 */
    autoLockRoomAfterCheckout?: boolean;
    /** 是否自动切换到时间计费 */
    autoSwitchToTimeBilling?: boolean;
    /** 呼叫处理超时时间 */
    callProcessingTimeout?: number;
    /** 取消开房时长 */
    cancelOpenDuration?: number;
    /** 收银员数据保留天数 */
    cashierDataRetentionDays?: number;
    /** 结账方式 */
    checkoutMode?: string;
    /** 柜台自动登出时间 */
    counterAutoLogoutTime?: number;
    /** 柜台点单模式 */
    counterOrderMode?: string;
    /** 创建时间 */
    ctime?: number;
    /** 是否默认发送预订短信 */
    defaultSendReservationSMS?: boolean;
    /** 折扣是否满足最低消费 */
    discountMeetMinConsumption?: boolean;
    /** 折扣是否仅限超出部分 */
    discountOnlyExcess?: boolean;
    /** 是否启用跨账单调整 */
    enableCrossBillingAdjustment?: boolean;
    /** 是否启用多个销售员 */
    enableMultipleSalespersons?: boolean;
    /** 是否启用退货确认 */
    enableReturnConfirmation?: boolean;
    /** 是否启用拆分计费 */
    enableSplitBilling?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** 客人时长 */
    guestDuration?: number;
    /** ID */
    id?: string;
    /** 继承会员卡模式 */
    inheritMemberCardMode?: string;
    /** 会员卡支付方式 */
    memberCardPaymentMode?: string;
    /** 会员卡验证方式 */
    memberCardVerificationMethod?: string;
    /** 会员折扣方式 */
    memberDiscountMethod?: string;
    /** 最小续费时长 */
    minRenewalDuration?: number;
    /** 手机点单现金支付方式 */
    mobileOrderCashPaymentMode?: string;
    /** 手机点单结账方式 */
    mobileOrderCheckoutMode?: string;
    /** 手机点单是否只存储已点商品 */
    mobileOrderOnlyStoreOrderedItems?: boolean;
    /** 手机点单酒水取回方式 */
    mobileOrderWineRetrievalMode?: string;
    /** 手机点单酒水寄存方式 */
    mobileOrderWineStorageMode?: string;
    /** 订单屏幕保持模式 */
    orderScreenHoldMode?: string;
    /** 产品价格会员折扣方式 */
    productPriceMemberDiscountMethod?: string;
    /** 退款时长 */
    refundDuration?: number;
    /** 重新开房时长 */
    reopenDuration?: number;
    /** 下单是否需要房间 */
    requireRoomForOrder?: boolean;
    /** 酒水是否需要绑定房间 */
    requireRoomForWine?: boolean;
    /** 预订到期时长 */
    reservationExpirationDuration?: number;
    /** 是否限制所有会员查询 */
    restrictAllMemberQuery?: boolean;
    /** 房间变更规则 */
    roomChangeRule?: string;
    /** 房间清洁到空闲时长 */
    roomCleanToIdleDuration?: number;
    /** 房间价格会员折扣方式 */
    roomPriceMemberDiscountMethod?: string;
    /** 四舍五入类型 */
    roundingType?: string;
    /** 第二次计费时长 */
    secondBillingDuration?: number;
    /** 是否在收银员端显示库存 */
    showInventoryOnCashier?: boolean;
    /** 拆分计费房间百分比 */
    splitBillingRoomPercentage?: number;
    /** 状态 */
    state?: number;
    /** 超市结账方式 */
    supermarketCheckoutMode?: string;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
    /** 酒水核销方式 */
    wineVerificationMethod?: string;
  };

export type OptionsResponse = {
    label?: string;
    value?: any;
  };

export type PayBillUnionVO = {
    employeeVOs?: EmployeeVO[];
    payBills?: PayBillVO[];
    payRecords?: PayRecordVO[];
  };

export type PayTypeData = {
    /** 支付宝 */
    alipay?: number;
    /** 银行卡 */
    bank?: number;
    /** 现金 */
    cash?: number;
    /** 口碑 */
    koubei?: number;
    /** 乐刷 */
    leshua?: number;
    /** 美团 */
    meituan?: number;
    /** 其他 */
    other?: number;
    /** 招待券 */
    ticket?: number;
    /** 微信 */
    wechat?: number;
  };

export type PhoneBlacklistVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 状态值 */
    state?: number;
    /** 所属店铺ID */
    storeId?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PhonePasswordLoginReqDto = {
    /** 密码 */
    password?: string;
    /** 手机号 */
    phone?: string;
    /** 场景字符串 */
    scene_str?: string;
  };

export type PosMachineVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 所属店铺ID */
    storeId?: string;
    /** 类型 */
    type?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PrintTemplateVO = {
    /** 打印份数 */
    copies?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 合并产品 */
    mergeProducts?: boolean;
    /** 打印模板名称 */
    name?: string;
    /** 纸张大小 */
    paperSize?: string;
    /** 打印礼品 */
    printGifts?: boolean;
    /** 打印模式 */
    printMode?: string;
    /** 仅打印本地使用 */
    printOnlyLocalUse?: boolean;
    /** 打印支付方式计数 */
    printPaymentMethodCount?: boolean;
    /** 打印产品总金额 */
    printProductTotalAmount?: boolean;
    /** 打印反向账单 */
    printReverseBill?: boolean;
    /** 打印机器人配送二维码 */
    printRobotDeliveryQR?: boolean;
    /** 打印时间 */
    printTime?: string;
    /** 打印酒水退款 */
    printWineRefund?: boolean;
    /** 打印零账单 */
    printZeroBill?: boolean;
    /** 打印机类型 */
    printerType?: string;
    /** 产品分组方式 */
    productGroupingMethod?: string;
    /** 产品类型配置 */
    productTypeConfig?: string;
    /** 生产点份数 */
    productionPointCopies?: number;
    /** 自助服务份数 */
    selfServiceCopies?: number;
    /** 状态值 */
    state?: number;
    /** 模板内容 */
    templateContent?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
    /** 酒水订单打印顺序 */
    wineOrderPrintSequence?: string;
  };

export type QRAutoLoginReqDto = {
    login_key: string;
    scene_str: string;
    user_id: string;
  };

export type QueryAreaReqDto = {
    /** 区域ID */
    id?: string;
    /** ID列表 */
    ids?: string[];
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryAsExampleReqDto = {
    /** 区域容量 */
    capacity?: number;
    /** 区域描述 */
    description?: string;
    /** 区域ID */
    id?: string;
    /** 区域ID列表 */
    ids?: string[];
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页条数 */
    pageSize?: number;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryBirthdayGreetingReqDto = {
    /** 客户群组 */
    customerGroup?: string;
    /** 提前天数 */
    daysInAdvance?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 短信模板 */
    smsTemplate?: string;
  };

export type QueryCallMessageReqDto = {
    /** 呼叫类型 */
    callType?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 房间ID */
    roomId?: string;
    /** 状态 */
    status?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryCallTypesReqDto = {
    /** 呼叫类型 */
    callType?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 类型 */
    type?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryCashierMachineReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 授权IP */
    grantIp?: string;
    /** ID */
    id?: string;
    /** 是否绑定 */
    isBind?: boolean;
    /** 是否为主收银 */
    isMain?: boolean;
    /** mac地址 */
    mac?: string;
    /** 收银机名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 场景值 */
    sceneStr?: string;
    /** 是否使用网络打印机 */
    useNetworkPrinter?: boolean;
    /** 门店ID */
    venueId?: string;
  };

export type QueryCommissionPlanReqDto = {
    /** 计算方法 */
    calculationMethod?: string;
    /** ID */
    id?: string;
    /** 佣金计划名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 佣金类型 */
    type?: string;
  };

export type QueryCommissionReqDto = {
    /** 金额 */
    amount?: number;
    /** 佣金ID */
    commissionId?: string;
    /** 日期 */
    date?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryCommonRemarkReqDto = {
    /** 备注内容 */
    content?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 备注类型 */
    type?: string;
  };

export type QueryConstructionAssistanceReqDto = {
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 所属店铺ID */
    storeId?: string;
    /** 技术员电话 */
    technicianPhone?: string;
    /** 有效期 */
    validityPeriod?: number;
  };

export type QueryConsumptionCashbackReqDto = {
    /** 返现比例 */
    cashbackRate?: number;
    /** 唯一id */
    id?: string;
    /** 最大返现金额 */
    maximumCashback?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryCreditAccountReqDto = {
    /** 账户名称 */
    accountName?: string;
    /** 信用额度 */
    creditLimit?: number;
    /** 当前余额 */
    currentBalance?: number;
    /** 唯一id */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryCreditUnitReqDto = {
    /** 联系人姓名 */
    contactName?: string;
    /** 信用额度 */
    creditLimit?: number;
    /** ID */
    id?: string;
    /** 信用单位名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryCustomerGroupReqDto = {
    /** 年龄范围 */
    ageRange?: number;
    /** 生日范围 */
    birthdayRange?: string;
    /** 卡余额 */
    cardBalance?: number;
    /** 卡等级列表 */
    cardLevels?: string;
    /** 消费行为 */
    consumptionBehavior?: string;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** 客户组名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 积分范围 */
    pointsRange?: number;
    /** 总消费金额 */
    totalConsumptionAmount?: number;
    /** 总消费次数 */
    totalConsumptionTimes?: number;
  };

export type QueryCustomerSourceReqDto = {
    /** ID */
    id?: string;
    /** 客户来源名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryCustomerTagReqDto = {
    /** 标签ID */
    id?: string;
    /** 客户标签名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryDailyPaiedSessionReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 场馆ID */
    venueId: string;
  };

export type QueryDataCleanupReqDto = {
    /** 数据类型 */
    dataType?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 请求时间 */
    requestTime?: number;
    /** 状态 */
    status?: string;
  };

export type QueryFlavorReqDto = {
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 口味名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 排序号 */
    sortNum?: number;
    /** 门店id */
    venueId?: string;
  };

export type QueryHistoricalRecordReqDto = {
    /** 结束台数 */
    finishedTables?: number;
    /** 唯一id */
    id?: string;
    /** 开台数量 */
    openTables?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 结算金额 */
    settlementAmount?: number;
    /** 总开台数量 */
    totalOpenedTables?: number;
  };

export type QueryHolidayReqDto = {
    /** 日期 */
    date?: string;
    /** ID */
    id?: string;
    /** 节假日名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 节假日类型 1:节假日 2:工作日 */
    type?: string;
    /** 所属店铺ID */
    venueId?: string;
  };

export type QueryIngredientTypeReqDto = {
    /** 配料类型描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 配料类型名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryMarketServiceReqDto = {
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 服务名称 */
    serviceName?: string;
  };

export type QueryMerchantServiceReqDto = {
    /** 授权码 */
    authorizationCode?: string;
    /** 过期日期 */
    expirationDate?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QueryNotificationSettingReqDto = {
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 接收人 */
    recipient?: string;
    /** 触发条件 */
    triggerCondition?: string;
    /** 通知类型 */
    type?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type QueryOnlineOperationSettingsReqDto = {
    /** 是否允许混合支付 */
    allowMixedPayment?: boolean;
    /** 是否自动连接WiFi */
    autoWifiConnect?: boolean;
    /** 是否允许提前入住补偿 */
    earlyCheckinCompensation?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** ID */
    id?: string;
    /** 是否需要输入销售员 */
    inputSalesperson?: boolean;
    /** 是否允许手动输入房间号 */
    manualRoomInput?: boolean;
    /** 最小计费时长 */
    minBillingDuration?: number;
    /** 在线营业时间 */
    onlineBusinessHours?: string;
    /** 在线房间计费模式 */
    onlineRoomBillingMode?: string;
    /** 订单模式 */
    orderMode?: string;
    /** 订单支付超时时间 */
    orderPaymentTimeout?: number;
    /** 套餐价格展示方式 */
    packagePriceDisplay?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 是否允许快速注册会员卡 */
    quickCardRegistration?: boolean;
    /** 推荐会员卡 */
    recommendedMemberCard?: string;
    /** 开房是否使用会员折扣 */
    useOpenRoomMemberDiscount?: boolean;
    /** WiFi密码 */
    wifiPassword?: string;
    /** WiFi用户名 */
    wifiUsername?: string;
  };

export type QueryOperationSettingsReqDto = {
    /** 是否允许收银员添加员工 */
    allowCashierAddEmployee?: boolean;
    /** 是否允许收银员选择模式 */
    allowCashierSelectMode?: boolean;
    /** 是否允许最低消费差异 */
    allowMinConsumptionDifference?: boolean;
    /** 是否允许超额购买预估清台 */
    allowOverbuyingEstimatedClearance?: boolean;
    /** 是否自动取消次日清台 */
    autoCancelClearanceNextDay?: boolean;
    /** 结账后是否自动锁定房间 */
    autoLockRoomAfterCheckout?: boolean;
    /** 是否自动切换到时间计费 */
    autoSwitchToTimeBilling?: boolean;
    /** 呼叫处理超时时间 */
    callProcessingTimeout?: number;
    /** 取消开房时长 */
    cancelOpenDuration?: number;
    /** 收银员数据保留天数 */
    cashierDataRetentionDays?: number;
    /** 结账方式 */
    checkoutMode?: string;
    /** 柜台自动登出时间 */
    counterAutoLogoutTime?: number;
    /** 柜台点单模式 */
    counterOrderMode?: string;
    /** 是否默认发送预订短信 */
    defaultSendReservationSMS?: boolean;
    /** 折扣是否满足最低消费 */
    discountMeetMinConsumption?: boolean;
    /** 折扣是否仅限超出部分 */
    discountOnlyExcess?: boolean;
    /** 是否启用跨账单调整 */
    enableCrossBillingAdjustment?: boolean;
    /** 是否启用多个销售员 */
    enableMultipleSalespersons?: boolean;
    /** 是否启用退货确认 */
    enableReturnConfirmation?: boolean;
    /** 是否启用拆分计费 */
    enableSplitBilling?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** 客人时长 */
    guestDuration?: number;
    /** ID */
    id?: string;
    /** 继承会员卡模式 */
    inheritMemberCardMode?: string;
    /** 会员卡支付方式 */
    memberCardPaymentMode?: string;
    /** 会员卡验证方式 */
    memberCardVerificationMethod?: string;
    /** 会员折扣方式 */
    memberDiscountMethod?: string;
    /** 最小续费时长 */
    minRenewalDuration?: number;
    /** 手机点单现金支付方式 */
    mobileOrderCashPaymentMode?: string;
    /** 手机点单结账方式 */
    mobileOrderCheckoutMode?: string;
    /** 手机点单是否只存储已点商品 */
    mobileOrderOnlyStoreOrderedItems?: boolean;
    /** 手机点单酒水取回方式 */
    mobileOrderWineRetrievalMode?: string;
    /** 手机点单酒水寄存方式 */
    mobileOrderWineStorageMode?: string;
    /** 订单屏幕保持模式 */
    orderScreenHoldMode?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 产品价格会员折扣方式 */
    productPriceMemberDiscountMethod?: string;
    /** 退款时长 */
    refundDuration?: number;
    /** 重新开房时长 */
    reopenDuration?: number;
    /** 下单是否需要房间 */
    requireRoomForOrder?: boolean;
    /** 酒水是否需要绑定房间 */
    requireRoomForWine?: boolean;
    /** 预订到期时长 */
    reservationExpirationDuration?: number;
    /** 是否限制所有会员查询 */
    restrictAllMemberQuery?: boolean;
    /** 房间变更规则 */
    roomChangeRule?: string;
    /** 房间清洁到空闲时长 */
    roomCleanToIdleDuration?: number;
    /** 房间价格会员折扣方式 */
    roomPriceMemberDiscountMethod?: string;
    /** 四舍五入类型 */
    roundingType?: string;
    /** 第二次计费时长 */
    secondBillingDuration?: number;
    /** 是否在收银员端显示库存 */
    showInventoryOnCashier?: boolean;
    /** 拆分计费房间百分比 */
    splitBillingRoomPercentage?: number;
    /** 超市结账方式 */
    supermarketCheckoutMode?: string;
    /** 酒水核销方式 */
    wineVerificationMethod?: string;
  };

export type QueryPhoneBlacklistReqDto = {
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 电话号码 */
    phoneNumber?: string;
    /** 所属店铺ID */
    storeId?: string;
  };

export type QueryPosMachineReqDto = {
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 状态 */
    status?: string;
    /** 所属店铺ID */
    storeId?: string;
    /** 类型 */
    type?: string;
  };

export type QueryPrintTemplateReqDto = {
    /** ID */
    id?: string;
    /** 打印模板名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 纸张大小 */
    paperSize?: string;
    /** 打印模式 */
    printMode?: string;
    /** 打印机类型 */
    printerType?: string;
    /** 产品分组方式 */
    productGroupingMethod?: string;
  };

export type QueryRechargePackageReqDto = {
    /** 金额 */
    amount?: number;
    /** 金额类型 */
    amountType?: string;
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送酒水 */
    bonusBeverages?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 唯一id */
    id?: string;
    /** 间隔赠送金额 */
    intervalBonusAmount?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 百分比赠送金额 */
    percentageBonusAmount?: number;
    /** 备注 */
    remark?: string;
  };

export type QueryRedemptionRecordReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 唯一ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 记录ID */
    recordId?: string;
    /** 核销时间 */
    redemptionTime?: number;
    /** 凭证ID */
    voucherId?: string;
  };

export type QueryRewardReqDto = {
    /** 金额 */
    amount?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 奖励ID */
    rewardId?: string;
    /** 奖励时间 */
    rewardTime?: number;
  };

export type QueryRouterReqDto = {
    /** 是否启用DHCP */
    dhcpEnabled?: boolean;
    /** 路由器ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QuerySalesPerformanceReqDto = {
    /** 销售金额 */
    amount?: number;
    /** 销售员ID */
    employeeId?: string;
    /** 唯一id */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 销售产品类型 */
    productType?: string;
    /** 销售数量 */
    quantity?: number;
    /** 销售日期 */
    saleDate?: number;
  };

export type QuerySequencerReqDto = {
    /** 延迟时间 */
    delayTime?: number;
    /** 网关 */
    gateway?: string;
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** MAC地址 */
    macAddress?: string;
    /** 型号 */
    model?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 服务器IP */
    serverIP?: string;
    /** 状态 */
    status?: string;
    /** 温度 */
    temperature?: number;
  };

export type QueryServiceRewardSettingsReqDto = {
    /** 是否启用 */
    enabled?: boolean;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 分成比例 */
    splitRatio?: number;
  };

export type QuerySessionReqDto = {
    /** 代定人 */
    agentPerson?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 客群标签 */
    customerTag?: string;
    /** 轮房人 */
    dutyPerson?: string;
    /** 员工ID */
    employeeId?: string;
    /** 支付员工ID */
    employeeIdPay?: string;
    /** 关房时间 */
    endTime?: number;
    /** 关房时间范围结束 */
    endTimeEnd?: number;
    /** 关房时间范围开始 */
    endTimeStart?: number;
    /** 唯一ID */
    id?: string;
    /** 状态 */
    info?: string;
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 订单来源 */
    orderSource?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 排位号码 */
    rankNumber?: string;
    /** 房间ID */
    roomId?: string;
    /** 开台ID */
    sessionId?: string;
    /** 开台ID */
    sessionIds?: string[];
    /** 开台时间 */
    startTime?: number;
    /** 开台时间范围结束 */
    startTimeEnd?: number;
    /** 开台时间范围开始 */
    startTimeStart?: number;
    /** 支付状态 */
    status?: string;
    /** 支付状态 */
    statusList?: string[];
    /** 门店ID */
    venueId?: string;
  };

export type QuerySmsServiceReqDto = {
    /** 余额 */
    balance?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 签名 */
    signature?: string;
  };

export type QueryStatisticsPeriodReqDto = {
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
  };

export type QuerySubtitleInfoReqDto = {
    /** 内容 */
    content?: string;
    /** ID */
    id?: string;
    /** 是否循环播放 */
    loopPlay?: boolean;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 重复次数 */
    repeatCount?: number;
    /** 滚动速度 */
    scrollSpeed?: number;
    /** 停留时间 */
    stayTime?: number;
  };

export type QueryTurnoverDataReqDto = {
    /** 日期 */
    date?: number;
    /** 折扣金额 */
    discountAmount?: number;
    /** 赠送金额 */
    giftAmount?: number;
    /** ID */
    id?: string;
    /** 会员充值 */
    memberRecharge?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 统计周期 */
    period?: string;
    /** 积分兑换金额 */
    pointsExchangeAmount?: number;
    /** 产品实收 */
    productActualReceived?: number;
    /** 产品应收 */
    productReceivable?: number;
    /** 包厢实收 */
    roomActualReceived?: number;
    /** 包厢应收 */
    roomReceivable?: number;
    /** 总营业额 */
    totalTurnover?: number;
  };

export type QueryTvScreenActivityReqDto = {
    /** 活动类型 */
    activityType?: string;
    /** 结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 产品信息 */
    productInfo?: string;
    /** 开始时间 */
    startTime?: number;
  };

export type QueryVodSettingsReqDto = {
    /** ERP服务器IP地址 */
    erpServerIP?: string;
    /** 唯一ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 控制器型号 */
    sequencerModel?: string;
    /** 点歌屏提醒 */
    songScreenReminder?: boolean;
    /** 门店ID */
    venueId?: string;
    /** VOD服务器IP地址 */
    vodServerIP?: string;
  };

export type QueryVoucherReqDto = {
    /** 唯一ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 计划ID */
    planId?: string;
    /** 状态 */
    status?: string;
    /** 凭证码 */
    voucherCode?: string;
    /** 凭证ID */
    voucherId?: string;
  };

export type QueryWithdrawableItemsReqDto = {
    /** 即将到期的天数定义，默认30天 */
    expireDays?: number;
    /** 是否模糊匹配，默认true */
    fuzzyMatch?: boolean;
    /** 是否包含即将到期的商品，默认true */
    includeExpiringSoon?: boolean;
    /** 搜索类型: phone(手机号)/name(姓名)/id(客户ID)/card(会员卡号) */
    searchType?: string;
    /** 搜索值 */
    searchValue?: string;
    /** 门店ID */
    venueId?: string;
  };

export type RechargePackageVO = {
    /** 金额 */
    amount?: number;
    /** 金额类型 */
    amountType?: string;
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送酒水 */
    bonusBeverages?: string;
    /** 创建时间 */
    ctime?: number;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 唯一id */
    id?: string;
    /** 间隔赠送金额 */
    intervalBonusAmount?: string;
    /** 百分比赠送金额 */
    percentageBonusAmount?: number;
    /** 备注 */
    remark?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本 */
    version?: number;
  };

export type RedemptionRecordVO = {
    /** 创建时间 */
    ctime?: number;
    /** 员工ID */
    employeeId?: string;
    /** 唯一ID */
    id?: string;
    /** 记录ID */
    recordId?: string;
    /** 核销时间 */
    redemptionTime?: number;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
    /** 凭证ID */
    voucherId?: string;
  };

export type RewardVO = {
    /** 金额 */
    amount?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 奖励ID */
    rewardId?: string;
    /** 奖励时间 */
    rewardTime?: number;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type RoleListVO = {
    /** 角色列表 */
    list?: PermissionRoleVO[];
    /** 当前页码 */
    pageNo?: number;
    /** 页大小 */
    pageSize?: number;
    /** 总页数 */
    pages?: number;
    /** 总记录数 */
    total?: number;
  };

export type RouterVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 是否启用DHCP */
    dhcpEnabled?: boolean;
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type SalesPerformanceVO = {
    /** 销售金额 */
    amount?: number;
    /** 创建时间 */
    ctime?: number;
    /** 销售员ID */
    employeeId?: string;
    /** 唯一id */
    id?: string;
    /** 销售产品类型 */
    productType?: string;
    /** 销售数量 */
    quantity?: number;
    /** 销售日期 */
    saleDate?: number;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type SendSmsCodeReqDto = {
    /** 手机号码 */
    phone: string;
    /** 测试模式，可选 */
    testMode?: boolean;
  };

export type SendSmsCodeVO = {
    /** 验证码（测试模式下返回） */
    code?: string;
    /** 返回消息 */
    message?: string;
    /** 手机号码 */
    phone?: string;
    /** 是否成功 */
    success?: boolean;
    /** 是否为测试模式 */
    testMode?: boolean;
  };

export type SequencerVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 延迟时间 */
    delayTime?: number;
    /** 网关 */
    gateway?: string;
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** MAC地址 */
    macAddress?: string;
    /** 型号 */
    model?: string;
    /** 服务器IP */
    serverIP?: string;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 温度 */
    temperature?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type ServiceRewardSettingsVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 是否启用 */
    enabled?: boolean;
    /** ID */
    id?: string;
    /** 分成比例 */
    splitRatio?: number;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type SessionOperationVOVoderpltvvErpManagentApiVoSessionVO = {
    data?: SessionVO;
    jspayInfo?: WechatMiniappJSPayInfo;
    lshSimplePayInfo?: LshSimplePayInfo;
    orderNos?: string[];
    payBills?: PayBillVO[];
    sessionId?: string;
  };

export type ShouyinPhonePasswordLoginReqDto = {
    /** 收银机mac地址 */
    mac?: string;
    /** 密码 */
    password?: string;
    /** 手机号 */
    phone?: string;
    /** 场景值（可选） */
    scene_str?: string;
  };

export type SmsServiceVO = {
    /** 余额 */
    balance?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 签名 */
    signature?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type StatisticsPeriodVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 名称 */
    name?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type SubtitleInfoVO = {
    /** 内容 */
    content?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 是否循环播放 */
    loopPlay?: boolean;
    /** 重复次数 */
    repeatCount?: number;
    /** 滚动速度 */
    scrollSpeed?: number;
    /** 状态值 */
    state?: number;
    /** 停留时间 */
    stayTime?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type TurnoverDataVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 日期 */
    date?: number;
    /** 折扣金额 */
    discountAmount?: number;
    /** 赠送金额 */
    giftAmount?: number;
    /** ID */
    id?: string;
    /** 会员充值 */
    memberRecharge?: number;
    /** 统计周期 */
    period?: string;
    /** 积分兑换金额 */
    pointsExchangeAmount?: number;
    /** 产品实收 */
    productActualReceived?: number;
    /** 产品应收 */
    productReceivable?: number;
    /** 包厢实收 */
    roomActualReceived?: number;
    /** 包厢应收 */
    roomReceivable?: number;
    /** 状态值 */
    state?: number;
    /** 总营业额 */
    totalTurnover?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type TvScreenActivityVO = {
    /** 活动类型 */
    activityType?: string;
    /** 创建时间 */
    ctime?: number;
    /** 结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 产品信息 */
    productInfo?: string;
    /** 开始时间 */
    startTime?: number;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type UnbindCashierMachineReqDto = {
    /** 收银机ID */
    id?: string;
  };

export type UpdateAppUpgradeReqDto = {
    /** 客户端类型 */
    clientType?: string;
    /** 下载地址 */
    downloadUrl?: string;
    /** 文件MD5 */
    fileMd5?: string;
    /** 文件大小（字节） */
    fileSize?: number;
    /** 是否强制升级 */
    forceUpgrade?: boolean;
    /** 升级包ID */
    id?: string;
    /** 升级内容 */
    upgradeContent?: string;
    /** 升级标题 */
    upgradeTitle?: string;
    /** 版本号（用于比较） */
    versionCode?: number;
    /** 版本名称（显示给用户） */
    versionName?: string;
  };

export type UpdateAreaReqDto = {
    /** 区域容量 */
    capacity?: number;
    /** 区域描述 */
    description?: string;
    /** 区域ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateAsExampleReqDto = {
    /** 区域容量 */
    capacity?: number;
    /** 区域描述 */
    description?: string;
    /** 区域ID */
    id?: string;
    /** 是否显示 */
    isDisplayed?: boolean;
    /** 区域名称 */
    name?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateBirthdayGreetingReqDto = {
    /** 客户群组 */
    customerGroup?: string;
    /** 提前天数 */
    daysInAdvance?: number;
    /** ID */
    id?: string;
    /** 短信模板 */
    smsTemplate?: string;
  };

export type UpdateCallMessageReqDto = {
    /** 呼叫来源 */
    callSrc?: string;
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫类型名称 */
    callTypeName?: string;
    /** ID */
    id?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 状态 */
    status?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateCallTypesReqDto = {
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫类型名称 */
    callTypeName?: string;
    /** ID */
    id?: string;
    /** 排序 */
    sortStr?: string;
    /** 类型 */
    type?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateCashierMachineReqDto = {
    /** 授权IP */
    grantIp?: string;
    /** ID */
    id?: string;
    /** 是否为主收银 */
    isMain?: boolean;
    /** mac地址 */
    mac?: string;
    /** 收银机名称 */
    name?: string;
    /** 打印机IP地址 */
    printerIp?: string;
    /** 备注 */
    remark?: string;
    /** 是否使用网络打印机 */
    useNetworkPrinter?: boolean;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateCommissionPlanReqDto = {
    /** 允许的预订数量 */
    allowedBookingQuantity?: number;
    /** 允许的房间类型列表 */
    allowedRoomTypes?: string;
    /** 佣金金额 */
    amount?: number;
    /** 计算方法 */
    calculationMethod?: string;
    /** 排除的产品列表 */
    excludedProducts?: string;
    /** ID */
    id?: string;
    /** 是否包含房间轮换 */
    includesRoomRotation?: boolean;
    /** 最大佣金金额 */
    maximumCommission?: number;
    /** 最低账单金额 */
    minimumBillAmount?: number;
    /** 佣金计划名称 */
    name?: string;
    /** 是否使用分层充值佣金 */
    tieredRechargeCommission?: boolean;
    /** 时间段列表 */
    timePeriods?: string;
    /** 佣金类型 */
    type?: string;
  };

export type UpdateCommissionReqDto = {
    /** 金额 */
    amount?: number;
    /** 佣金ID */
    commissionId?: string;
    /** 日期 */
    date?: number;
    /** ID */
    id?: string;
  };

export type UpdateCommonRemarkReqDto = {
    /** 备注内容 */
    content?: string;
    /** ID */
    id?: string;
    /** 备注类型 */
    type?: string;
  };

export type UpdateConstructionAssistanceReqDto = {
    /** ID */
    id?: string;
    /** 所属店铺ID */
    storeId?: string;
    /** 技术员电话 */
    technicianPhone?: string;
    /** 有效期 */
    validityPeriod?: number;
  };

export type UpdateConsumptionCashbackReqDto = {
    /** 返现比例 */
    cashbackRate?: number;
    /** 唯一id */
    id?: string;
    /** 最大返现金额 */
    maximumCashback?: number;
  };

export type UpdateCreditAccountReqDto = {
    /** 信用额度 */
    creditLimit?: number;
    /** 挂账单位 */
    creditUnit?: string;
    /** 当前余额 */
    currentBalance?: number;
    /** 是否启用 */
    enable?: boolean;
    /** 唯一id */
    id?: string;
    /** 姓名 */
    name?: string;
    /** 手机号 */
    phone?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateCreditUnitReqDto = {
    /** 联系人姓名 */
    contactName?: string;
    /** 信用额度 */
    creditLimit?: number;
    /** ID */
    id?: string;
    /** 信用单位名称 */
    name?: string;
  };

export type UpdateCustomerGroupReqDto = {
    /** 年龄范围 */
    ageRange?: number;
    /** 生日范围 */
    birthdayRange?: string;
    /** 卡余额 */
    cardBalance?: number;
    /** 卡等级列表 */
    cardLevels?: string;
    /** 消费行为 */
    consumptionBehavior?: string;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** 客户组名称 */
    name?: string;
    /** 积分范围 */
    pointsRange?: number;
    /** 总消费金额 */
    totalConsumptionAmount?: number;
    /** 总消费次数 */
    totalConsumptionTimes?: number;
  };

export type UpdateCustomerSourceReqDto = {
    /** ID */
    id?: string;
    /** 客户来源名称 */
    name?: string;
  };

export type UpdateCustomerTagReqDto = {
    /** 标签ID */
    id?: string;
    /** 客户标签名称 */
    name?: string;
  };

export type UpdateDataCleanupReqDto = {
    /** 数据类型 */
    dataType?: string;
    /** ID */
    id?: string;
    /** 请求时间 */
    requestTime?: number;
    /** 状态 */
    status?: string;
  };

export type UpdateFlavorReqDto = {
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 口味名称 */
    name?: string;
    /** 排序号 */
    sortNum?: number;
    /** 门店id */
    venueId?: string;
  };

export type UpdateHistoricalRecordReqDto = {
    /** 结束台数 */
    finishedTables?: number;
    /** 唯一id */
    id?: string;
    /** 开台数量 */
    openTables?: number;
    /** 结算金额 */
    settlementAmount?: number;
    /** 总开台数量 */
    totalOpenedTables?: number;
  };

export type UpdateHolidayReqDto = {
    /** 日期 */
    date?: string;
    /** ID */
    id?: string;
    /** 节假日名称 */
    name?: string;
    /** 节假日类型 1:节假日 2:工作日 */
    type?: string;
    /** 所属店铺ID */
    venueId?: string;
  };

export type UpdateIngredientTypeReqDto = {
    /** 配料类型描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 配料类型名称 */
    name?: string;
  };

export type UpdateMarketServiceReqDto = {
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 服务名称 */
    serviceName?: string;
  };

export type UpdateMerchantServiceReqDto = {
    /** 授权码 */
    authorizationCode?: string;
    /** 过期日期 */
    expirationDate?: number;
    /** ID */
    id?: string;
  };

export type UpdateNotificationSettingReqDto = {
    /** ID */
    id?: string;
    /** 接收人 */
    recipient?: string;
    /** 触发条件 */
    triggerCondition?: string;
    /** 通知类型 */
    type?: string;
    /** 所属门店ID */
    venueId?: string;
  };

export type UpdateOnlineOperationSettingsReqDto = {
    /** 是否允许混合支付 */
    allowMixedPayment?: boolean;
    /** 是否自动连接WiFi */
    autoWifiConnect?: boolean;
    /** 是否允许提前入住补偿 */
    earlyCheckinCompensation?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** ID */
    id?: string;
    /** 是否需要输入销售员 */
    inputSalesperson?: boolean;
    /** 是否允许手动输入房间号 */
    manualRoomInput?: boolean;
    /** 最小计费时长 */
    minBillingDuration?: number;
    /** 在线营业时间 */
    onlineBusinessHours?: string;
    /** 在线房间计费模式 */
    onlineRoomBillingMode?: string;
    /** 订单模式 */
    orderMode?: string;
    /** 订单支付超时时间 */
    orderPaymentTimeout?: number;
    /** 套餐价格展示方式 */
    packagePriceDisplay?: string;
    /** 是否允许快速注册会员卡 */
    quickCardRegistration?: boolean;
    /** 推荐会员卡 */
    recommendedMemberCard?: string;
    /** 开房是否使用会员折扣 */
    useOpenRoomMemberDiscount?: boolean;
    /** WiFi密码 */
    wifiPassword?: string;
    /** WiFi用户名 */
    wifiUsername?: string;
  };

export type UpdateOperationSettingsReqDto = {
    /** 是否允许收银员添加员工 */
    allowCashierAddEmployee?: boolean;
    /** 是否允许收银员选择模式 */
    allowCashierSelectMode?: boolean;
    /** 是否允许最低消费差异 */
    allowMinConsumptionDifference?: boolean;
    /** 是否允许超额购买预估清台 */
    allowOverbuyingEstimatedClearance?: boolean;
    /** 是否自动取消次日清台 */
    autoCancelClearanceNextDay?: boolean;
    /** 结账后是否自动锁定房间 */
    autoLockRoomAfterCheckout?: boolean;
    /** 是否自动切换到时间计费 */
    autoSwitchToTimeBilling?: boolean;
    /** 呼叫处理超时时间 */
    callProcessingTimeout?: number;
    /** 取消开房时长 */
    cancelOpenDuration?: number;
    /** 收银员数据保留天数 */
    cashierDataRetentionDays?: number;
    /** 结账方式 */
    checkoutMode?: string;
    /** 柜台自动登出时间 */
    counterAutoLogoutTime?: number;
    /** 柜台点单模式 */
    counterOrderMode?: string;
    /** 是否默认发送预订短信 */
    defaultSendReservationSMS?: boolean;
    /** 折扣是否满足最低消费 */
    discountMeetMinConsumption?: boolean;
    /** 折扣是否仅限超出部分 */
    discountOnlyExcess?: boolean;
    /** 是否启用跨账单调整 */
    enableCrossBillingAdjustment?: boolean;
    /** 是否启用多个销售员 */
    enableMultipleSalespersons?: boolean;
    /** 是否启用退货确认 */
    enableReturnConfirmation?: boolean;
    /** 是否启用拆分计费 */
    enableSplitBilling?: boolean;
    /** 首次计费时长 */
    firstBillingDuration?: number;
    /** 客人时长 */
    guestDuration?: number;
    /** ID */
    id?: string;
    /** 继承会员卡模式 */
    inheritMemberCardMode?: string;
    /** 会员卡支付方式 */
    memberCardPaymentMode?: string;
    /** 会员卡验证方式 */
    memberCardVerificationMethod?: string;
    /** 会员折扣方式 */
    memberDiscountMethod?: string;
    /** 最小续费时长 */
    minRenewalDuration?: number;
    /** 手机点单现金支付方式 */
    mobileOrderCashPaymentMode?: string;
    /** 手机点单结账方式 */
    mobileOrderCheckoutMode?: string;
    /** 手机点单是否只存储已点商品 */
    mobileOrderOnlyStoreOrderedItems?: boolean;
    /** 手机点单酒水取回方式 */
    mobileOrderWineRetrievalMode?: string;
    /** 手机点单酒水寄存方式 */
    mobileOrderWineStorageMode?: string;
    /** 订单屏幕保持模式 */
    orderScreenHoldMode?: string;
    /** 产品价格会员折扣方式 */
    productPriceMemberDiscountMethod?: string;
    /** 退款时长 */
    refundDuration?: number;
    /** 重新开房时长 */
    reopenDuration?: number;
    /** 下单是否需要房间 */
    requireRoomForOrder?: boolean;
    /** 酒水是否需要绑定房间 */
    requireRoomForWine?: boolean;
    /** 预订到期时长 */
    reservationExpirationDuration?: number;
    /** 是否限制所有会员查询 */
    restrictAllMemberQuery?: boolean;
    /** 房间变更规则 */
    roomChangeRule?: string;
    /** 房间清洁到空闲时长 */
    roomCleanToIdleDuration?: number;
    /** 房间价格会员折扣方式 */
    roomPriceMemberDiscountMethod?: string;
    /** 四舍五入类型 */
    roundingType?: string;
    /** 第二次计费时长 */
    secondBillingDuration?: number;
    /** 是否在收银员端显示库存 */
    showInventoryOnCashier?: boolean;
    /** 拆分计费房间百分比 */
    splitBillingRoomPercentage?: number;
    /** 超市结账方式 */
    supermarketCheckoutMode?: string;
    /** 酒水核销方式 */
    wineVerificationMethod?: string;
  };

export type UpdatePhoneBlacklistReqDto = {
    /** ID */
    id?: string;
    /** 电话号码 */
    phoneNumber?: string;
    /** 所属店铺ID */
    storeId?: string;
  };

export type UpdatePosMachineReqDto = {
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 状态 */
    status?: string;
    /** 所属店铺ID */
    storeId?: string;
    /** 类型 */
    type?: string;
  };

export type UpdatePrintTemplateReqDto = {
    /** 打印份数 */
    copies?: number;
    /** ID */
    id?: string;
    /** 合并产品 */
    mergeProducts?: boolean;
    /** 打印模板名称 */
    name?: string;
    /** 纸张大小 */
    paperSize?: string;
    /** 打印礼品 */
    printGifts?: boolean;
    /** 打印模式 */
    printMode?: string;
    /** 仅打印本地使用 */
    printOnlyLocalUse?: boolean;
    /** 打印支付方式计数 */
    printPaymentMethodCount?: boolean;
    /** 打印产品总金额 */
    printProductTotalAmount?: boolean;
    /** 打印反向账单 */
    printReverseBill?: boolean;
    /** 打印机器人配送二维码 */
    printRobotDeliveryQR?: boolean;
    /** 打印时间 */
    printTime?: string;
    /** 打印酒水退款 */
    printWineRefund?: boolean;
    /** 打印零账单 */
    printZeroBill?: boolean;
    /** 打印机类型 */
    printerType?: string;
    /** 产品分组方式 */
    productGroupingMethod?: string;
    /** 产品类型配置 */
    productTypeConfig?: string;
    /** 生产点份数 */
    productionPointCopies?: number;
    /** 自助服务份数 */
    selfServiceCopies?: number;
    /** 模板内容 */
    templateContent?: string;
    /** 酒水订单打印顺序 */
    wineOrderPrintSequence?: string;
  };

export type UpdateRechargePackageReqDto = {
    /** 金额 */
    amount?: number;
    /** 金额类型 */
    amountType?: string;
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送酒水 */
    bonusBeverages?: string;
    /** 分销渠道 */
    distributionChannels?: string;
    /** 唯一id */
    id?: string;
    /** 间隔赠送金额 */
    intervalBonusAmount?: string;
    /** 百分比赠送金额 */
    percentageBonusAmount?: number;
    /** 备注 */
    remark?: string;
  };

export type UpdateRedemptionRecordReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 唯一ID */
    id?: string;
    /** 记录ID */
    recordId?: string;
    /** 核销时间 */
    redemptionTime?: number;
    /** 凭证ID */
    voucherId?: string;
  };

export type UpdateRequest = {
    employeeId?: string;
    id: string;
    record: Record<string, any>;
    table: string;
    venueId?: string;
  };

export type UpdateRewardReqDto = {
    /** 金额 */
    amount?: number;
    /** ID */
    id?: string;
    /** 奖励ID */
    rewardId?: string;
    /** 奖励时间 */
    rewardTime?: number;
  };

export type UpdateRouterReqDto = {
    /** 是否启用DHCP */
    dhcpEnabled?: boolean;
    /** 路由器ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
  };

export type UpdateSalesPerformanceReqDto = {
    /** 销售金额 */
    amount?: number;
    /** 销售员ID */
    employeeId?: string;
    /** 唯一id */
    id?: string;
    /** 销售产品类型 */
    productType?: string;
    /** 销售数量 */
    quantity?: number;
    /** 销售日期 */
    saleDate?: number;
  };

export type UpdateSequencerReqDto = {
    /** 延迟时间 */
    delayTime?: number;
    /** 网关 */
    gateway?: string;
    /** ID */
    id?: string;
    /** IP地址 */
    ipAddress?: string;
    /** MAC地址 */
    macAddress?: string;
    /** 型号 */
    model?: string;
    /** 服务器IP */
    serverIP?: string;
    /** 状态 */
    status?: string;
    /** 温度 */
    temperature?: number;
  };

export type UpdateServiceRewardSettingsReqDto = {
    /** 是否启用 */
    enabled?: boolean;
    /** ID */
    id?: string;
    /** 分成比例 */
    splitRatio?: number;
  };

export type UpdateSessionReqDto = {
    /** 代定人 */
    agentPerson?: string;
    /** 客户来源 */
    customerSource?: string;
    /** 客群标签 */
    customerTag?: string;
    /** 使用时长 */
    duration?: number;
    /** 轮房人 */
    dutyPerson?: string;
    /** 员工ID */
    employeeId?: string;
    /** 支付员工ID */
    employeeIdPay?: string;
    /** 关房时间 */
    endTime?: number;
    /** 唯一ID */
    id?: string;
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 最低消费 */
    minConsume?: number;
    /** 订单来源 */
    orderSource?: string;
    /** 预付余额 */
    prePayBalance?: number;
    /** 排位号码 */
    rankNumber?: string;
    /** 包厢费用 */
    roomFee?: number;
    /** 房间ID */
    roomId?: string;
    /** 开台ID */
    sessionId?: string;
    /** 开台时间 */
    startTime?: number;
    /** 支付状态 */
    status?: string;
    /** 超市费用 */
    supermarketFee?: number;
    /** 总计费用 */
    totalFee?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateSmsServiceReqDto = {
    /** 余额 */
    balance?: number;
    /** ID */
    id?: string;
    /** 签名 */
    signature?: string;
  };

export type UpdateStatisticsPeriodReqDto = {
    /** 描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 名称 */
    name?: string;
  };

export type UpdateSubtitleInfoReqDto = {
    /** 内容 */
    content?: string;
    /** ID */
    id?: string;
    /** 是否循环播放 */
    loopPlay?: boolean;
    /** 重复次数 */
    repeatCount?: number;
    /** 滚动速度 */
    scrollSpeed?: number;
    /** 停留时间 */
    stayTime?: number;
  };

export type UpdateTurnoverDataReqDto = {
    /** 日期 */
    date?: number;
    /** 折扣金额 */
    discountAmount?: number;
    /** 赠送金额 */
    giftAmount?: number;
    /** ID */
    id?: string;
    /** 会员充值 */
    memberRecharge?: number;
    /** 统计周期 */
    period?: string;
    /** 积分兑换金额 */
    pointsExchangeAmount?: number;
    /** 产品实收 */
    productActualReceived?: number;
    /** 产品应收 */
    productReceivable?: number;
    /** 包厢实收 */
    roomActualReceived?: number;
    /** 包厢应收 */
    roomReceivable?: number;
    /** 总营业额 */
    totalTurnover?: number;
  };

export type UpdateTvScreenActivityReqDto = {
    /** 活动类型 */
    activityType?: string;
    /** 结束时间 */
    endTime?: number;
    /** ID */
    id?: string;
    /** 产品信息 */
    productInfo?: string;
    /** 开始时间 */
    startTime?: number;
  };

export type UpdateVodSettingsReqDto = {
    /** ERP服务器IP地址 */
    erpServerIP?: string;
    /** 唯一ID */
    id?: string;
    /** 控制器型号 */
    sequencerModel?: string;
    /** 点歌屏提醒 */
    songScreenReminder?: boolean;
    /** 门店ID */
    venueId?: string;
    /** VOD服务器IP地址 */
    vodServerIP?: string;
  };

export type UpdateVoucherReqDto = {
    /** 唯一ID */
    id?: string;
    /** 计划ID */
    planId?: string;
    /** 状态 */
    status?: string;
    /** 凭证码 */
    voucherCode?: string;
    /** 凭证ID */
    voucherId?: string;
  };

export type V3AddCallReqDto = {
    /** 呼叫来源 小程序、触摸屏 */
    callSrc?: string;
    /** 呼叫类型 */
    callType?: string;
    /** 呼叫类型名称 */
    callTypeName?: string;
    /** 对应员工ID */
    employeeId?: string;
    /** 对应房间ID */
    roomId?: string;
    /** 对应场次ID */
    sessionId?: string;
    /** 对应场馆ID */
    venueId?: string;
  };

export type V3BillBackReqDto = {
    /** 支付单号列表 */
    billIds?: string[];
    /** 员工ID */
    employeeId?: string;
    /** 房间ID */
    roomId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3BillQueryBySessionReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3BillQueryReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 结束时间 */
    endTime?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 开始时间 */
    startTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type V3CancelCallReqDto = {
    /** 取消备注 */
    remark?: string;
    /** 房间ID */
    roomId?: string;
  };

export type V3DealCallReqDto = {
    /** 处理员工ID */
    employeeId?: string;
    /** 呼叫消息ID */
    id?: string;
    /** 处理备注 */
    remark?: string;
  };

export type V3ListCallLastReqDto = {
    /** 员工ID（可选） */
    employeeId?: string;
    /** 场馆ID */
    venueId?: string;
  };

export type V3ListCallTypesReqDto = {
    /** 页码 */
    pageNum?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 状态（0-启用，1-禁用） */
    state?: number;
    /** 场馆ID */
    venueId?: string;
  };

export type V3ListCallUnprocessedReqDto = {
    /** 员工ID（可选） */
    employeeId?: string;
    /** 场馆ID */
    venueId?: string;
  };

export type V3QueryBillBackViewReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 场次ID */
    sessionId?: string;
    /** 门店ID */
    venueId?: string;
  };

export type VodSettingsVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ERP服务器IP地址 */
    erpServerIP?: string;
    /** 唯一ID */
    id?: string;
    /** 控制器型号 */
    sequencerModel?: string;
    /** 点歌屏提醒 */
    songScreenReminder?: boolean;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
    /** VOD服务器IP地址 */
    vodServerIP?: string;
  };

export type VoucherVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** 唯一ID */
    id?: string;
    /** 计划ID */
    planId?: string;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
    /** 凭证码 */
    voucherCode?: string;
    /** 凭证ID */
    voucherId?: string;
  };

export type WarehouseVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 是否为默认酒水仓库 */
    isDefaultLiquorStorage?: boolean;
    /** 仓库名称 */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type WechatMiniappJSPayInfo = {
    appId?: string;
    nonceStr?: string;
    package?: string;
    paySign?: string;
    signType?: string;
    timeStamp?: string;
  };

export type WeekdayConfig = {
    /** 星期的日子选择数组(1~7) */
    weeks?: number[];
  };

export type WithdrawableItemsVO = {
    /** 客户信息 */
    customerInfo?: CustomerInfoVO;
    /** 存酒单简要信息 */
    storageOrders?: StorageOrderBriefVO[];
    /** 可取商品列表 */
    withdrawableItems?: WithdrawableItemVO[];
  };

export type WXEventArticlesItemVO = {
    items?: WXEventArticlesVO[];
  };

export type WXEventArticlesVO = {
    description?: string;
    picUrl?: string;
    title?: string;
    url?: string;
  };

export type WXEventReqDto = {
    content?: string;
    createTime?: number;
    event?: string;
    eventKey?: string;
    fromUserName?: string;
    msgId?: number;
    msgType?: string;
    ticket?: string;
    toUserName?: string;
  };

export type WXEventVO = {
    articleCount?: string;
    articles?: WXEventArticlesItemVO;
    createTime?: string;
    fromUserName?: string;
    msgType?: string;
    toUserName?: string;
  };
