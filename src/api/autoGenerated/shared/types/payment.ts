// payment相关类型定义
import { GiftProductInfo, ExtendedProductInfo } from './product';
import { RoomInfo, RoomPackageInfo } from './room';

export type AbnormalPaymentData = {
    /** 异常支付流水金额 */
    amount?: number;
    /** 异常支付流水数 */
    count?: number;
  };

export type AddPaymentMethodReqDto = {
    /** 是否启用 */
    isEnabled?: boolean;
    /** 支付方式名称 */
    name?: string;
    /** 排序顺序 */
    sortOrder?: number;
    /** 支付方式类型 */
    type?: string;
  };

export type CheckoutBillDataVO = {
    /** 调整原因 */
    adjustReason?: string;
    /** 收银员 */
    cashierName?: string;
    /** 找零金额 */
    changeAmount?: number;
    /** 结账时间 */
    checkoutTime?: string;
    /** 优惠券金额 */
    couponDiscount?: number;
    /** 优惠人 */
    discountBy?: string;
    /** 消费时长（分钟） */
    duration?: number;
    /** 消费结束时间 */
    endTime?: string;
    /** 计费抵商品金额 */
    feeToProduct?: number;
    /** 赠送小计 */
    giftProductTotal?: number;
    /** 赠送商品 */
    giftProducts?: GiftProductInfo[];
    /** 会员优惠金额 */
    memberDiscount?: number;
    /** 商家优惠金额 */
    merchantDiscount?: number;
    /** 商家赠送金额 */
    merchantGift?: number;
    /** 结账单号 */
    payBillId?: string;
    /** 付款金额 */
    paymentAmount?: number;
    /** 支付方式明细 */
    paymentMethods?: PaymentDetail[];
    /** 打单时间 */
    printTime?: string;
    /** 商品消费总计 */
    productFeeTotal?: number;
    /** 普通消费商品 */
    products?: ExtendedProductInfo[];
    /** 包厢消费总计 */
    roomFeeTotal?: number;
    /** 包厢信息 */
    roomInfo?: RoomInfo;
    /** 房间方案信息 */
    roomPackages?: RoomPackageInfo[];
    /** 包厢类型（小包、中包、大包） */
    roomType?: string;
    /** 轮房人 */
    rotationPerson?: string;
    /** 抹零金额 */
    roundDown?: number;
    /** 开台单号 */
    sessionId?: string;
    /** 店铺名称 */
    shopName?: string;
    /** 消费开始时间 */
    startTime?: string;
    /** 应收金额 */
    totalReceivable?: number;
  };

export type DeletePaymentMethodReqDto = {
    /** ID */
    id?: string;
  };

export type PaymentDetail = {
    /** 金额 */
    amount?: number;
    /** 支付方式名称 */
    method?: string;
  };

export type PaymentMethodVO = {
    /** 创建时间 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 支付方式名称 */
    name?: string;
    /** 排序顺序 */
    sortOrder?: number;
    /** 状态 */
    state?: number;
    /** 支付方式类型 */
    type?: string;
    /** 更新时间 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type QueryPaymentMethodReqDto = {
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 支付方式名称 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 排序顺序 */
    sortOrder?: number;
    /** 支付方式类型 */
    type?: string;
  };

export type ResultArrayVoPaymentMethodVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PaymentMethodVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPaymentMethodVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PaymentMethodVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type UpdatePaymentMethodReqDto = {
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 支付方式名称 */
    name?: string;
    /** 排序顺序 */
    sortOrder?: number;
    /** 支付方式类型 */
    type?: string;
  };
