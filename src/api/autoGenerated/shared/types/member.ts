// member相关类型定义
import { OrderAndPayVO, OrderAndRefundVO, OrderExtFeeVO, OrderVO, OrderRoomPlanVO } from './order';
import { PayRecordVO, OrderProductVO } from './product';
import { OrderPricePlanVO } from './price';
import { PayResultVO } from './result';
import { RoomVO } from './room';

export type AddMemberCardLevelReqDto = {
    /** 酒水折扣 */
    beverageDiscount?: number;
    /** 生日福利 */
    birthdayBenefits?: string;
    /** 消费时段设置 */
    cardConsumptionTimeSettings?: string;
    /** 办卡费用 */
    cardIssueFee?: number;
    /** 续卡费用 */
    cardRenewalFee?: number;
    /** 补卡费用 */
    cardReplacementFee?: number;
    /** 卡类型 */
    cardType?: string;
    /** 消费积分规则 */
    consumptionPointsRule?: string;
    /** 消费时间段 */
    consumptionTimeSlots?: string;
    /** 每日房间限制 */
    dailyRoomLimit?: number;
    /** 默认使用范围 */
    defaultUsageScope?: string;
    /** 分发渠道 */
    distributionChannels?: string;
    /** 降级条件 */
    downgradeConditions?: string;
    /** 员工ID */
    employeeId?: string;
    /** logo */
    logo?: string;
    /** 享受折扣的最低余额 */
    minimumBalanceForDiscount?: number;
    /** 最低消费金额 */
    minimumConsumptionAmount?: number;
    /** 最低充值金额 */
    minimumRechargeAmount?: number;
    /** 每月优惠券 */
    monthlyVouchers?: string;
    /** 等级名称 */
    name?: string;
    /** 支付限制 */
    paymentRestrictions?: string;
    /** 充值积分规则 */
    rechargePointsRule?: string;
    /** 注册升级福利 */
    registrationUpgradeBenefits?: string;
    /** 房间折扣 */
    roomDiscount?: number;
    /** 升级条件 */
    upgradeConditions?: string;
    /** 有效期 */
    validityPeriod?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddMemberCardOperationReqDto = {
    /** 余额 */
    balance?: number;
    /** 会员卡ID */
    memberCardId?: string;
    /** 操作类型 */
    operationType?: string;
  };

export type AddMemberDayReqDto = {
    /** 活动周期 */
    activityCycle?: string;
    /** 会员活动时间，如单独一天，周一，[1]，或者连续几天，如周一到周五，[1,2,3,4,5] */
    dayOfWeek?: number[];
    /** 员工ID */
    employeeId?: string;
    /** 是否启用 */
    isEnabled?: number;
    /** 积分倍数 */
    pointsMultiplier?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddMemberRechargePackageReqDto = {
    /** 活动结束时间 */
    activityEndTime?: number;
    /** 活动开始时间 */
    activityStartTime?: number;
    /** 适用会员卡等级 */
    applicableMemberCardLevels?: string[];
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送金额使用条件 */
    bonusAmountCondition?: string;
    /** 赠送金额当日是否可用 */
    bonusAmountIsAvailable?: boolean;
    /** 赠送金额是否分期返还 */
    bonusAmountIsReturnable?: boolean;
    /** 赠送金额百分比 */
    bonusAmountPercent?: number;
    /** 赠送金额类型 */
    bonusAmountType?: string;
    /** 赠送优惠券ID列表 */
    bonusCouponIds?: string[];
    /** 可选组商品数量 */
    bonusGoodsGroupCount?: number;
    /** 赠送商品可选组名称 */
    bonusGoodsGroupName?: string;
    /** 赠送商品组ID列表 */
    bonusGoodsGroupProductIds?: string[];
    /** 可选组策略类型 */
    bonusGoodsGroupStrategyType?: string;
    /** 赠送商品ID列表 */
    bonusGoodsIds?: string[];
    /** 投放渠道 */
    deliveryChannel?: string;
    /** 员工ID */
    employeeId?: string;
    /** 固定充值金额limit */
    fixedAmountLimit?: number;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 区间金额最大 */
    maxAmount?: number;
    /** 区间金额最小 */
    minAmount?: number;
    /** 充值套餐类型 */
    packageType?: string;
    /** 优惠说明 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type AddMemberReqDto = {
    /** 会员余额 */
    balance?: number;
    /** 生日 */
    birthday?: number;
    /** 赠金余额 */
    bonusBalance?: number;
    /** 卡名称 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical_card, virtual_card, electronic_card */
    cardType?: string;
    /** 员工ID */
    employeeId?: string;
    /** 性别 */
    gender?: string;
    /** 会员姓名 */
    name?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 会员手机号 */
    phone?: string;
    /** 会员积分 */
    points?: number;
    /** 本金余额 */
    principalBalance?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddMemberTransferMoneyReqDto = {
    /** 转账金额 */
    amount?: number;
    /** 转出会员卡号 */
    fromMemberCardNumber?: string;
    /** 转出会员ID */
    fromMemberId?: string;
    /** 转账积分 */
    points?: number;
    /** 备注 */
    remark?: string;
    /** 转账状态 */
    status?: string;
    /** 转入会员卡号 */
    toMemberCardNumber?: string;
    /** 转入会员ID */
    toMemberId?: string;
    /** 转账时间 */
    transferTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type AddVenueAndMemberReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** 会员ID */
    memberId?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteMemberCardLevelReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteMemberCardOperationReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteMemberDayReqDto = {
    /** 唯一id */
    id?: string;
  };

export type DeleteMemberRechargePackageReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteMemberReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type DeleteMemberTransferMoneyReqDto = {
    /** ID */
    id?: string;
  };

export type DeleteVenueAndMemberReqDto = {
    /** ID */
    id?: string;
  };

export type getApiV1MemberCardLevelsParams = {
    /** 门店ID */
    venueId: string;
  };

export type getApiV2MemberCardLevelsParams = {
    /** 门店ID */
    venueId: string;
  };

export type getApiV3MemberCardConsumeIdParams = {
    /** 消费记录ID */
    id: string;
  };

export type MemberCardConsumeUnionVO = {
    /** 收款单号 */
    billId?: string;
    /** 退款单对应的收款单号 */
    billPid?: string;
    /** 业务类型，如：消费/退款/充值 */
    bizType?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 方向 */
    direction?: string;
    /** 是否还原 0: 正常 1: 账单还原 */
    isBack?: boolean;
    /** 会员卡总余额 */
    memberCardBalance?: number;
    /** 通用赠金 */
    memberCommonBonusAmount?: number;
    /** 商品赠金 */
    memberGoodsBonusAmount?: number;
    /** 房间赠金 */
    memberRoomBonusAmount?: number;
    /** 消费 */
    originalFee?: number;
    /** 总金额-实际支付金额或会员卡的本金+3种赠金 */
    payRecordTotalAmout?: number;
    /** 充值/消费中会员卡部分金额 */
    principalAmount?: number;
    /** 应付金额 = 实付金额 + 抹零金额 - 找零金额 */
    shouldFee?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
    /** 版本号 */
    version?: number;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type MemberCardConsumeVO = {
    /** paybill */
    billId?: string;
    /** 退款单对应的收款单号 */
    billPid?: string;
    /** 业务类型，如：消费/退款 */
    bizType?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 方向 */
    direction?: string;
    /** 员工ID */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工电话 */
    employeePhone?: string;
    /** ID */
    id?: string;
    /** 是否还原 0: 正常 1: 账单还原 */
    isBack?: boolean;
    /** membercard */
    memberCardBalance?: number;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员卡号 */
    memberCardNumber?: string;
    /** 会员ID */
    memberId?: string;
    /** 原始金额 优惠金额 = 原始金额 - 应付金额 */
    originalFee?: number;
    /** payrecord */
    payId?: string;
    payPid?: string;
    /** 通用赠金 */
    payRecordCommonBonusAmount?: number;
    /** 商品赠金 */
    payRecordGoodsBonusAmount?: number;
    /** 本金 */
    payRecordPrincipalAmount?: number;
    /** 房费赠金 */
    payRecordRoomBonusAmount?: number;
    /** 总金额-实际支付金额或会员卡的本金+3种赠金 */
    payRecordTotalAmout?: number;
    /** 备注 */
    remark?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 应付金额 = 实付金额 + 抹零金额 - 找零金额 */
    shouldFee?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
    /** 版本号 */
    version?: number;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type MemberCardLevelVO = {
    /** 卡类型 */
    cardType?: string;
    /** 创建时间 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** 卡时长(天) */
    duration?: number;
    /** ID */
    id?: string;
    /** 等级 */
    level?: string;
    /** logo */
    logo?: string;
    /** 最小开卡时长(天) */
    minOpenDuration?: number;
    /** 卡名称 */
    name?: string;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type MemberCardOperationVO = {
    /** 余额 */
    balance?: number;
    /** 账单ID */
    billId?: string;
    /** 账单支付ID */
    billPid?: string;
    /** 会员卡等级 - normal/gold/diamond */
    cardLevel?: string;
    /** 会员卡等级名称 - 普通卡/黄金卡/钻石卡 */
    cardLevelName?: string;
    /** 会员卡名称 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 会员卡手机号 */
    cardPhone?: string;
    /** 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic */
    cardType?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 通用赠金 */
    memberCommonBonusAmount?: number;
    /** 商品赠金 */
    memberGoodsBonusAmount?: number;
    /** 会员ID */
    memberId?: string;
    /** 房间赠金 */
    memberRoomBonusAmount?: number;
    /** 操作类型：open_card-开卡,cancel-注销,renew-续卡,frozen-冻结,unfrozen-解冻,lost-挂失,unlost-解挂 */
    operationType?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 本金 */
    principalAmount?: number;
    /** 销售员ID */
    sellerId?: string;
    /** 销售员姓名 */
    sellerName?: string;
    /** 状态值 */
    state?: number;
    /** 总金额 */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberCardPayData = {
    /** 通用赠金 */
    commonBonusAmount?: number;
    /** 商品赠金 */
    goodsBonusAmount?: number;
    /** 本金支付金额 */
    principalAmount?: number;
    /** 房费赠金 */
    roomBonusAmount?: number;
  };

export type MemberCardRechargeData = {
    /** 充值金额 */
    rechargeAmount?: number;
  };

export type MemberDayVO = {
    /** 活动周期，如每周、每月、每年 */
    activityCycle?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 会员活动日，支持多天如[1,2,3] */
    dayOfWeek?: number[];
    /** 唯一id */
    id?: string;
    /** 是否启用，0: 不启用，1: 启用 */
    isEnabled?: number;
    /** 积分倍数 */
    pointsMultiplier?: number;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 场所ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberRechargeBillExVO = {
    /** 账单日期 */
    billDate?: number;
    /** 充值账单号 */
    billId?: string;
    /** 退款账单号 */
    billPid?: string;
    /** 业务类型，如：充值，消费 */
    bizType?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 通用赠金（都可以用的赠金） */
    commonBonusAmount?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** 充值方向,如: 充值, 退款，消费余额，退余额，枚举：recharge, refund, consume, refund_consume */
    direction?: string;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 完成时间 */
    finishTime?: number;
    /** 用于商品的赠金 */
    goodsBonusAmount?: number;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 用于房费的赠金 */
    roomBonusAmount?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 第三方支付ID-收银支付id */
    thirdPayId?: string;
    /** 充值金额 */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberRechargeBillVO = {
    /** 账单日期 */
    billDate?: number;
    /** 充值账单号 */
    billId?: string;
    /** 退款账单号 */
    billPid?: string;
    /** 业务类型，如：充值，消费 */
    bizType?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 通用赠金（都可以用的赠金） */
    commonBonusAmount?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** 充值方向,如: 充值, 退款，消费余额，退余额，枚举：recharge, refund, consume, refund_consume */
    direction?: string;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 完成时间 */
    finishTime?: number;
    /** 用于商品的赠金 */
    goodsBonusAmount?: number;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 用于房费的赠金 */
    roomBonusAmount?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 第三方支付ID-收银支付id */
    thirdPayId?: string;
    /** 充值金额 */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberRechargePackageVO = {
    /** 活动结束时间 */
    activityEndTime?: number;
    /** 活动开始时间 */
    activityStartTime?: number;
    /** 适用会员卡等级 */
    applicableMemberCardLevels?: string[];
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送金额使用条件 */
    bonusAmountCondition?: string;
    /** 赠送金额当日是否可用 */
    bonusAmountIsAvailable?: boolean;
    /** 赠送金额是否分期返还 */
    bonusAmountIsReturnable?: boolean;
    /** 赠送金额百分比 */
    bonusAmountPercent?: number;
    /** 赠送金额类型 */
    bonusAmountType?: string;
    /** 赠送优惠券ID列表 */
    bonusCouponIds?: string[];
    /** 可选组商品数量 */
    bonusGoodsGroupCount?: number;
    /** 赠送商品可选组名称 */
    bonusGoodsGroupName?: string;
    /** 赠送商品组ID列表 */
    bonusGoodsGroupProductIds?: string[];
    /** 可选组策略类型 */
    bonusGoodsGroupStrategyType?: string;
    /** 赠送商品ID列表 */
    bonusGoodsIds?: string[];
    /** 创建时间戳 */
    ctime?: number;
    /** 投放渠道 */
    deliveryChannel?: string;
    /** 固定充值金额limit */
    fixedAmountLimit?: number;
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 区间金额最大 */
    maxAmount?: number;
    /** 区间金额最小 */
    minAmount?: number;
    /** 充值套餐类型 */
    packageType?: string;
    /** 优惠说明 */
    remark?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberRechargeRecordVO = {
    /** BShowQR支付方式的BQROneCode */
    bQROneCode?: string;
    /** 账单日期-冗余-用于统计 */
    billDate?: number;
    /** 充值账单号 */
    billId?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 完成时间 */
    finishTime?: number;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 支付单ID */
    payId?: string;
    /** 退款单ID */
    payPid?: string;
    /** 支付来源-乐刷等第三方支付方式-微信/支付宝 */
    paySource?: string;
    /** 支付类型-微信 支付宝 找零 挂账 */
    payType?: string;
    /** 商品名称 */
    productName?: string;
    /** 房间ID */
    roomId?: string;
    /** 状态值 */
    state?: number;
    /** 状态 success/refund */
    status?: string;
    /** 第三方支付单号 */
    thirdOrderId?: string;
    /** 总金额-实际支付金额 */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberRegisterDTO = {
    /** 生日时间戳 */
    birthday?: number;
    /** 卡等级ID */
    cardLevelId?: string;
    /** 卡类型 */
    cardType?: string;
    /** 性别 */
    gender?: string;
    /** 初始余额 */
    initialBalance?: number;
    /** 初始积分 */
    initialPoints?: number;
    /** 会员姓名 */
    name?: string;
    /** 操作人ID */
    operatorId?: string;
    /** 操作人姓名 */
    operatorName?: string;
    /** 手机号 */
    phone?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type MemberTransferMoneyVO = {
    /** 转账金额 */
    amount?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** 转出会员卡号 */
    fromMemberCardNumber?: string;
    /** 转出会员ID */
    fromMemberId?: string;
    /** ID */
    id?: string;
    /** 转账积分 */
    points?: number;
    /** 备注 */
    remark?: string;
    /** 状态值 */
    state?: number;
    /** 转账状态 */
    status?: string;
    /** 转入会员卡号 */
    toMemberCardNumber?: string;
    /** 转入会员ID */
    toMemberId?: string;
    /** 转账时间 */
    transferTime?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type MemberVerifyDTO = {
    /** 卡号 */
    cardNumber?: string;
    /** 手机号 */
    phone?: string;
    /** 门店ID */
    venueId?: string;
  };

export type MemberVO = {
    /** 生日 */
    birthday?: number;
    /** 结束时间 */
    cardEndTime?: number;
    /** 会员卡ID - 7979fff126604feaa4a56cb6d29e0a87 */
    cardId?: string;
    /** 会员卡等级 - normal/gold/diamond */
    cardLevel?: string;
    /** 会员卡名称 - 普通卡/黄金卡/钻石卡 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 会员卡号ID-计算用6位正整数 */
    cardNumberId?: number;
    /** 开卡时间 */
    cardStartTime?: number;
    /** 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic */
    cardType?: string;
    /** 通用赠金（都可以用的赠金） */
    commonBonusBalance?: number;
    /** 创建时间戳 */
    ctime?: number;
    /** 员工ID -操作人 */
    employeeId?: string;
    /** 性别 male/female */
    gender?: string;
    /** 用于商品的赠金 */
    goodsBonusBalance?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 会员姓名 */
    name?: string;
    /** 开卡操作人ID - 销售员 */
    operatorId?: string;
    /** 开卡操作人姓名 - 销售员 */
    operatorName?: string;
    /** 会员手机号 */
    phone?: string;
    /** 会员积分 */
    points?: number;
    /** 本金余额 */
    principalBalance?: number;
    /** 用于房费的赠金 */
    roomBonusBalance?: number;
    /** 会员来源,如: 线下实体卡, 线上小程序, 线上APP，枚举：offline_card, mini_program, app */
    source?: string;
    /** 状态值 */
    state?: number;
    /** 会员状态,如: 正常:normal, 挂失:lost, 冻结:frozen, 过期:expired, 注销:cancelled */
    status?: string;
    /** 累计消费金额 */
    totalConsumptionAmount?: number;
    /** 累计消费次数 */
    totalConsumptionTimes?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 版本号 */
    version?: number;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoMemberVO = {
    data?: MemberVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoVenueAndMemberVO = {
    data?: VenueAndMemberVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PayBillExtVO = {
    /** 账单日期 */
    billDate?: number;
    /** 收款单号 */
    billId?: string;
    /** 退款单对应的收款单号 */
    billPid?: string;
    /** 现金-找零-【无用字段】 */
    changeAmount?: number;
    /** 挂账 */
    creditAmount?: number;
    /** 创建时间 */
    ctime?: number;
    /** 方向 */
    direction?: string;
    /** 优惠原因 */
    discountReason?: string;
    /** 对超过低消部分打折还是全部打折 */
    discountType?: number;
    /** 员工ID-交班用 */
    employeeId?: string;
    /** 完成时间 */
    finishTime?: number;
    /** 是否强制满低消 */
    forceMinimumCharge?: boolean;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 是否还原 0: 正常 1: 账单还原 */
    isBack?: boolean;
    /** 是否免单 */
    isFree?: boolean;
    /** 是否赠送 */
    isGift?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 订单支付中间表 */
    orderAndPayVOs?: OrderAndPayVO[];
    /** 退款订单 */
    orderAndRefundVOs?: OrderAndRefundVO[];
    /** 退款订单金额 */
    orderAndRefundVOsSum?: number;
    /** 订单标签 */
    orderTag?: string;
    orderVOs?: OrderExtFeeVO[];
    /** 营业实收=营业应收-(商家优惠+[会员卡支付]+抹零)
  600.5     = 300+100+200+0.5 */
    originalFee?: number;
    /** 超过低消商品减免 - 计算字段 */
    overMinProductDiscountAmount?: number;
    payRecordVOs?: PayRecordVO[];
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 退款金额 */
    refundSum?: number;
    /** 退款方式 */
    refundWay?: string;
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 应收 = 原始 (优惠 = 应收 - 实收 - 抹零) */
    shouldFee?: number;
    /** 状态 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 实收 = 应收 - 优惠 - 抹零 = 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店id */
    venueId?: string;
    /** 版本号 */
    version?: number;
    /** 抹零 */
    zeroFee?: number;
  };

export type PayBillVO = {
    /** 账单日期 */
    billDate?: number;
    /** 收款单号 */
    billId?: string;
    /** 退款单对应的收款单号 */
    billPid?: string;
    /** 现金-找零-【无用字段】 */
    changeAmount?: number;
    /** 挂账 */
    creditAmount?: number;
    /** 创建时间 */
    ctime?: number;
    /** 方向 */
    direction?: string;
    /** 优惠原因 */
    discountReason?: string;
    /** 对超过低消部分打折还是全部打折 */
    discountType?: number;
    /** 员工ID-交班用 */
    employeeId?: string;
    /** 完成时间 */
    finishTime?: number;
    /** 是否强制满低消 */
    forceMinimumCharge?: boolean;
    /** ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 是否还原 0: 正常 1: 账单还原 */
    isBack?: boolean;
    /** 是否免单 */
    isFree?: boolean;
    /** 是否赠送 */
    isGift?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 订单支付中间表 */
    orderAndPayVOs?: OrderAndPayVO[];
    /** 退款订单 */
    orderAndRefundVOs?: OrderAndRefundVO[];
    /** 退款订单金额 */
    orderAndRefundVOsSum?: number;
    /** 订单标签 */
    orderTag?: string;
    /** 订单 */
    orderVOs?: OrderVO[];
    /** 营业实收=营业应收-(商家优惠+[会员卡支付]+抹零)
  600.5     = 300+100+200+0.5 */
    originalFee?: number;
    /** 超过低消商品减免 - 计算字段 */
    overMinProductDiscountAmount?: number;
    /** 支付记录 */
    payRecordVOs?: PayRecordVO[];
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 退款金额 */
    refundSum?: number;
    /** 退款方式 */
    refundWay?: string;
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 应收 = 原始 (优惠 = 应收 - 实收 - 抹零) */
    shouldFee?: number;
    /** 状态 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 实收 = 应收 - 优惠 - 抹零 = 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店id */
    venueId?: string;
    /** 版本号 */
    version?: number;
    /** 抹零 */
    zeroFee?: number;
  };

export type QueryMemberCardConsumeReqDto = {
    /** paybill */
    billId?: string;
    /** 退款单对应的收款单号 */
    billPid?: string;
    /** 创建时间戳 */
    ctime?: number;
    /** 方向 */
    direction?: string;
    /** 员工ID */
    employeeId?: string;
    /** 员工姓名 */
    employeeName?: string;
    /** 员工电话 */
    employeePhone?: string;
    /** ID */
    id?: string;
    /** 是否还原 0: 正常 1: 账单还原 */
    isBack?: boolean;
    /** membercard */
    memberCardBalance?: number;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员卡名称 */
    memberCardName?: string;
    /** 会员卡号 */
    memberCardNumber?: string;
    /** 会员ID */
    memberId?: string;
    /** 原始金额 优惠金额 = 原始金额 - 应付金额 */
    originalFee?: number;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** payrecord */
    payId?: string;
    payPid?: string;
    /** 总金额-实际支付金额或会员卡的本金+3种赠金 */
    payRecordTotalAmout?: number;
    /** 备注 */
    remark?: string;
    /** 房间ID */
    roomId?: string;
    /** 房间名称 */
    roomName?: string;
    /** 场次ID */
    sessionId?: string;
    /** 应付金额 = 实付金额 + 抹零金额 - 找零金额 */
    shouldFee?: number;
    /** 状态值 */
    state?: number;
    /** 状态 */
    status?: string;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 门店名称 */
    venueName?: string;
    /** 版本号 */
    version?: number;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type QueryMemberCardLevelReqDto = {
    /** 会员卡等级ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryMemberCardOperationListReqDto = {
    /** 会员卡ID */
    memberCardId?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryMemberCardOperationReqDto = {
    /** 会员卡名称 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** ID */
    ids?: string[];
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 会员姓名 */
    name?: string;
    /** 操作类型 */
    operationType?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 会员手机号 */
    phone?: string;
    /** 会员手机号模糊查询 */
    phoneLike?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryMemberDayReqDto = {
    /** 唯一id */
    id?: string;
    /** 场所ID */
    venueId?: string;
  };

export type QueryMemberRechargePackageReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryMemberReqDto = {
    /** 会员卡名称 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 员工ID */
    employeeId?: string;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** ID */
    ids?: string[];
    /** 会员姓名 */
    name?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 会员手机号 */
    phone?: string;
    /** 门店ID */
    venueId?: string;
  };

export type QueryMemberTransferMoneyReqDto = {
    /** 转账金额 */
    amount?: number;
    /** 转出会员卡号 */
    fromMemberCardNumber?: string;
    /** 转出会员ID */
    fromMemberId?: string;
    /** ID */
    id?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 转账积分 */
    points?: number;
    /** 转账状态 */
    status?: string;
    /** 转入会员卡号 */
    toMemberCardNumber?: string;
    /** 转入会员ID */
    toMemberId?: string;
    /** 转账时间 */
    transferTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type QueryVenueAndMemberReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 页码 */
    pageNum?: number;
    /** 每页记录数 */
    pageSize?: number;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type RegisterMemberVO = {
    /** 会员余额 */
    balance?: number;
    /** 生日时间戳 */
    birthday?: number;
    /** 赠金余额 */
    bonusBalance?: number;
    /** 会员卡ID */
    cardId?: string;
    /** 卡等级 */
    cardLevel?: string;
    /** 会员卡名称 */
    cardName?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 卡类型 */
    cardType?: string;
    /** 创建时间 */
    ctime?: number;
    /** 性别 */
    gender?: string;
    /** 会员ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 会员姓名 */
    name?: string;
    /** 手机号 */
    phone?: string;
    /** 会员积分 */
    points?: number;
    /** 本金余额 */
    principalBalance?: number;
    /** 会员状态 */
    status?: string;
    /** 累计消费金额 */
    totalConsumptionAmount?: number;
    /** 累计消费次数 */
    totalConsumptionTimes?: number;
    /** 有效期结束时间 */
    validityEndDate?: number;
    /** 有效期开始时间 */
    validityStartDate?: number;
  };

export type ReservationInfo = {
    /** 预订会员卡号 */
    memberCardNo?: string;
    /** 预定会员名称 */
    memberName?: string;
    /** 预定会员手机号 */
    memberPhone?: string;
    /** 代订人 */
    proxyOrderer?: string;
    /** 代订人手机号 */
    proxyOrdererPhone?: string;
  };

export type ResultArrayVoderpltvvErpManagentApiVoMemberVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberCardConsumeUnionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardConsumeUnionVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberCardConsumeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardConsumeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberCardLevelVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardLevelVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberCardOperationVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardOperationVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberDayVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberDayVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberRechargeBillExVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberRechargeBillExVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoMemberTransferMoneyVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberTransferMoneyVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoVenueAndMemberVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAndMemberVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoderpltvvErpManagentApiVoMemberVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMemberCardConsumeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardConsumeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMemberCardLevelVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardLevelVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMemberCardOperationVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberCardOperationVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMemberDayVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberDayVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMemberRechargePackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberRechargePackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoMemberTransferMoneyVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: MemberTransferMoneyVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoMemberVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoMemberVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoVenueAndMemberVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoVenueAndMemberVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoVenueAndMemberVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: VenueAndMemberVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type SessionVO = {
    /** 代定人 */
    agentPerson?: string;
    /** 实际关房时间 */
    closeTime?: number;
    /** 创建时间 */
    ctime?: number;
    /** 客户来源 */
    customerSource?: string;
    /** 客群标签 */
    customerTag?: string;
    /** 废弃-客户端计算-使用时长 */
    duration?: number;
    /** 轮房人 */
    dutyPerson?: string;
    /** 员工ID */
    employeeId?: string;
    /** 预期结束时间 */
    endTime?: number;
    /** 赠品金额 */
    giftAmount?: number;
    /** 唯一ID */
    id?: string;
    /** 备注 */
    info?: string;
    /** 是否开台立结 */
    isOpenTableSettled?: boolean;
    /** 是否是计时消费 */
    isTimeConsume?: boolean;
    /** 会员卡ID */
    memberCardId?: string;
    /** 会员ID */
    memberId?: string;
    /** 最低消费 */
    minConsume?: number;
    /** 扩展字段
价格方案信息 */
    orderPricePlanVO?: OrderPricePlanVO;
    /** 订单来源 */
    orderSource?: string;
    /** 已付金额 */
    paidAmount?: number;
    payBills?: PayBillVO[];
    /** 支付结果 */
    payResultVOs?: PayResultVO[];
    /** 支付状态 */
    payStatus?: string;
    /** 预付余额 */
    prePayBalance?: number;
    /** 排位号码 */
    rankNumber?: string;
    /** 包厢费用 */
    roomFee?: number;
    /** 房间ID */
    roomId?: string;
    /** 房间信息 */
    roomVO?: RoomVO;
    rtOrderNos?: string[];
    /** 开台ID */
    sessionId?: string;
    /** 开台时间 */
    startTime?: number;
    /** 状态 */
    state?: number;
    /** 状态 开台：opening， 关台：ending */
    status?: string;
    /** 超市费用 */
    supermarketFee?: number;
    /** tag: lock/birthday/changyin */
    tag?: string;
    /** 总计费用 */
    totalFee?: number;
    /** 未付金额 */
    unpaidAmount?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };

export type UpdateMemberCardLevelReqDto = {
    /** 酒水折扣 */
    beverageDiscount?: number;
    /** 生日福利 */
    birthdayBenefits?: string;
    /** 办卡费用 */
    cardIssueFee?: number;
    /** 续卡费用 */
    cardRenewalFee?: number;
    /** 补卡费用 */
    cardReplacementFee?: number;
    /** 卡类型 */
    cardType?: string;
    /** 消费积分规则 */
    consumptionPointsRule?: string;
    /** 消费时间段 */
    consumptionTimeSlots?: string;
    /** 每日房间限制 */
    dailyRoomLimit?: number;
    /** 默认使用范围 */
    defaultUsageScope?: string;
    /** 分发渠道 */
    distributionChannels?: string;
    /** 降级条件 */
    downgradeConditions?: string;
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 等级 */
    level?: number;
    /** logo */
    logo?: string;
    /** 享受折扣的最低余额 */
    minimumBalanceForDiscount?: number;
    /** 最低消费金额 */
    minimumConsumptionAmount?: number;
    /** 最低充值金额 */
    minimumRechargeAmount?: number;
    /** 每月优惠券 */
    monthlyVouchers?: string;
    /** 等级名称 */
    name?: string;
    /** 支付限制 */
    paymentRestrictions?: string;
    /** 充值积分规则 */
    rechargePointsRule?: string;
    /** 注册升级福利 */
    registrationUpgradeBenefits?: string;
    /** 房间折扣 */
    roomDiscount?: number;
    /** 升级条件 */
    upgradeConditions?: string;
    /** 有效期 */
    validityPeriod?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateMemberCardOperationReqDto = {
    /** 余额 */
    balance?: number;
    /** ID */
    id?: string;
    /** 会员卡ID */
    memberCardId?: string;
    /** 操作类型 */
    operationType?: string;
  };

export type UpdateMemberDayReqDto = {
    /** 活动周期 */
    activityCycle?: string;
    /** 星期几 */
    dayOfWeek?: number[];
    /** 员工ID */
    employeeId?: string;
    /** 唯一id */
    id?: string;
    /** 是否启用 */
    isEnabled?: number;
    /** 积分倍数 */
    pointsMultiplier?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateMemberRechargePackageReqDto = {
    /** 活动结束时间 */
    activityEndTime?: number;
    /** 活动开始时间 */
    activityStartTime?: number;
    /** 适用会员卡等级 */
    applicableMemberCardLevels?: string[];
    /** 赠送金额 */
    bonusAmount?: number;
    /** 赠送金额使用条件 */
    bonusAmountCondition?: string;
    /** 赠送金额当日是否可用 */
    bonusAmountIsAvailable?: boolean;
    /** 赠送金额是否分期返还 */
    bonusAmountIsReturnable?: boolean;
    /** 赠送金额百分比 */
    bonusAmountPercent?: number;
    /** 赠送金额类型 */
    bonusAmountType?: string;
    /** 赠送优惠券ID列表 */
    bonusCouponIds?: string[];
    /** 可选组商品数量 */
    bonusGoodsGroupCount?: number;
    /** 赠送商品可选组名称 */
    bonusGoodsGroupName?: string;
    /** 赠送商品组ID列表 */
    bonusGoodsGroupProductIds?: string[];
    /** 可选组策略类型 */
    bonusGoodsGroupStrategyType?: string;
    /** 赠送商品ID列表 */
    bonusGoodsIds?: string[];
    /** 投放渠道 */
    deliveryChannel?: string;
    /** 员工ID */
    employeeId?: string;
    /** 固定充值金额limit */
    fixedAmountLimit?: number;
    /** ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 区间金额最大 */
    maxAmount?: number;
    /** 区间金额最小 */
    minAmount?: number;
    /** 充值套餐类型 */
    packageType?: string;
    /** 优惠说明 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateMemberReqDto = {
    /** 会员余额 */
    balance?: number;
    /** 生日 */
    birthday?: number;
    /** 所属会员卡模板ID */
    cardId?: string;
    /** 会员卡号 */
    cardNumber?: string;
    /** 员工ID */
    employeeId?: string;
    /** 性别 */
    gender?: string;
    /** ID */
    id?: string;
    /** 会员等级 */
    level?: string;
    /** 会员姓名 */
    name?: string;
    /** 会员手机号 */
    phone?: string;
    /** 会员积分 */
    points?: number;
    /** 累计消费金额 */
    totalConsumptionAmount?: number;
    /** 累计消费次数 */
    totalConsumptionTimes?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateMemberTransferMoneyReqDto = {
    /** 转账金额 */
    amount?: number;
    /** 转出会员卡号 */
    fromMemberCardNumber?: string;
    /** 转出会员ID */
    fromMemberId?: string;
    /** ID */
    id?: string;
    /** 转账积分 */
    points?: number;
    /** 备注 */
    remark?: string;
    /** 转账状态 */
    status?: string;
    /** 转入会员卡号 */
    toMemberCardNumber?: string;
    /** 转入会员ID */
    toMemberId?: string;
    /** 转账时间 */
    transferTime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdateVenueAndMemberReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 备注 */
    remark?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3EndTimeConsumeReqDto = {
    employeeId?: string;
    memberCardId?: string;
    memberId?: string;
    /** 房费信息 */
    orderRoomPlanVOS?: OrderRoomPlanVO[];
    roomId?: string;
    sessionId?: string;
    venueId?: string;
  };

export type V3FrozenMemberCardReqDto = {
    /** 备注 */
    description?: string;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 会员卡ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3MiniAppPayReqDto = {
    /** 找零金额 */
    changeAmount?: number;
    /** 挂账账户ID */
    creditAccountId?: string;
    /** 挂账金额 */
    creditAmount?: number;
    /** 当前时间 */
    currentTime?: number;
    employeeId?: string;
    /** 是否免单 */
    isFree?: boolean;
    /** 会员金额 */
    memberAmount?: number;
    memberCardId?: string;
    memberId?: string;
    openid?: string;
    /** 点单商品信息 */
    orderProductVOs?: OrderProductVO[];
    /** 原始金额 */
    originalAmount?: number;
    /** 原始金额 优惠金额 = 原始金额 - 应付金额 */
    originalFee?: number;
    /** 支付总金额 */
    payAmount?: number;
    /** 支付记录ID */
    payRecords?: PayRecordVO[];
    /** 商品折扣 */
    productDiscount?: number;
    /** 商品减免 */
    productDiscountAmount?: number;
    /** 房费折扣 */
    roomDiscount?: number;
    /** 房费减免 */
    roomDiscountAmount?: number;
    roomId?: string;
    sessionId?: string;
    /** 应付金额 = 实付金额 + 抹零金额 */
    shouldFee?: number;
    /** 实付金额 金额对应sum(payrecords.totalfee) */
    totalFee?: number;
    venueId?: string;
    /** 抹零金额 */
    zeroFee?: number;
  };

export type V3QueryMemberListPhoneReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 会员ID */
    ids?: string[];
    /** 会员ID */
    memberId?: string[];
    /** 会员手机号模糊查询 */
    phoneLike?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3QueryMemberListReqDto = {
    /** 员工ID */
    employeeId?: string;
    /** ID */
    id?: string;
    /** 会员ID */
    ids?: string[];
    /** 会员ID */
    memberId?: string[];
    /** 门店ID */
    venueId?: string;
  };

export type V3QueryMemberPayQueryReqDto = {
    billId?: string;
    employeeId?: string;
    venueId?: string;
  };

export type V3QueryMemberRechargeReqDto = {
    /** 会员卡号 */
    cardNumber?: string;
    /** 通用赠金（都可以用的赠金） */
    commonBonusAmount?: number;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 用于商品的赠金 */
    goodsBonusAmount?: number;
    /** 会员卡ID-主键 */
    memberCardId?: string;
    /** 会员充值记录 */
    memberRechargeRecordVOs?: MemberRechargeRecordVO[];
    /** 用于房费的赠金 */
    roomBonusAmount?: number;
    /** 充值金额 */
    totalFee?: number;
    /** 门店ID */
    venueId?: string;
  };

export type V3RenewMemberCardReqDto = {
    /** 结束时间 */
    cardEndTime?: number;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 会员卡ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type V3UnfrozenMemberCardReqDto = {
    /** 备注 */
    description?: string;
    /** 员工ID-操作人 */
    employeeId?: string;
    /** 会员卡ID */
    id?: string;
    /** 门店ID */
    venueId?: string;
  };

export type VenueAndMemberVO = {
    /** 创建时间戳 */
    ctime?: number;
    /** ID */
    id?: string;
    /** 会员ID */
    memberId?: string;
    /** 备注 */
    remark?: string;
    /** 状态值 */
    state?: number;
    /** 更新时间戳 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本号 */
    version?: number;
  };
