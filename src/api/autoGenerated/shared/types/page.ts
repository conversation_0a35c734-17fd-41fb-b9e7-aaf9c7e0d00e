// page相关类型定义
import { AppUpgradeVO, BirthdayGreetingVO, CallMessageVO, CallTypesVO, CashierMachineVO, CommissionPlanVO, CommissionVO, CommonRemarkVO, ConstructionAssistanceVO, ConsumptionCashbackVO, CreditAccountVO, CreditUnitVO, CustomerGroupVO, CustomerSourceVO, CustomerTagVO, DataCleanupVO, HolidayVO, FlavorVO, HistoricalRecordVO, IngredientTypeVO, MarketServiceVO, MerchantServiceVO, NotificationSettingVO, OnlineOperationSettingsVO, OperationSettingsVO, PhoneBlacklistVO, PosMachineVO, PrintTemplateVO, RechargePackageVO, RedemptionRecordVO, RewardVO, RouterVO, SalesPerformanceVO, SequencerVO, ServiceRewardSettingsVO, SmsServiceVO, StatisticsPeriodVO, SubtitleInfoVO, TurnoverDataVO, TvScreenActivityVO, VodSettingsVO, VoucherVO, WarehouseVO } from './other';
import { AuthorizationRecordVO } from './auth';
import { CloudPrinterVO } from './printer';
import { CouponClaimVO, CouponUsageVO } from './coupon';
import { OrderRoomPlanVO, ProductionOrderPlanVO } from './order';
import { RoomTypeVO, RoomFaultVO, RoomGreetingVO, RoomThemeVO } from './room';
import { SessionVO, MemberCardConsumeVO, MemberCardOperationVO, MemberRechargePackageVO, MemberTransferMoneyVO } from './member';
import { DouyinGroupBuyingPlanVO, StatisticsCategoryVO } from './price';
import { EmployeeGiftRecordVO, EmployeeGroupVO } from './employee';
import { GiftGroupVO, GiftRecordVO } from './gift';
import { InventoryRecordVO, InventoryTransactionVO } from './inventory';
import { MarketingCampaignVO, MarketingRoleVO } from './marketing';
import { NetworkVO } from './network';
import { PaymentMethodVO } from './payment';
import { PermissionRoleVO } from './permission';
import { PointsExchangeVO, ProductBindingVO, ProductDisplayCategoryVO, ProductMultipleBuyFreeVO, ProductOutTypeVO, ProductPackageTypeVO, ProductPackageVO, ProductSalesTemplateVO, ProductStatisticsCategoryVO, ProductStorageItemStatVO, ProductTimeSlotVO, ProductTypeVO, ProductWithdrawVO, RecipeVO } from './product';
import { PrepaidCardTypeVO } from './card';
import { ProductStorageVO, WineStorageSettingVO, WineStorageVO } from './storage';
import { ReportTypeVO } from './report';
import { ShiftHandoverFormAndPayBillVO, ShiftHandoverFormVO } from './shift';
import { SingingDeviceVO } from './device';
import { VenuePaySettingVO } from './venue';

export type PageVOArrayVoAppUpgradeVO = {
    data?: AppUpgradeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoAuthorizationRecordVO = {
    data?: AuthorizationRecordVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoBirthdayGreetingVO = {
    data?: BirthdayGreetingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCallMessageVO = {
    data?: CallMessageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCallTypesVO = {
    data?: CallTypesVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCashierMachineVO = {
    data?: CashierMachineVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCloudPrinterVO = {
    data?: CloudPrinterVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCommissionPlanVO = {
    data?: CommissionPlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCommissionVO = {
    data?: CommissionVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCommonRemarkVO = {
    data?: CommonRemarkVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoConstructionAssistanceVO = {
    data?: ConstructionAssistanceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoConsumptionCashbackVO = {
    data?: ConsumptionCashbackVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCouponClaimVO = {
    data?: CouponClaimVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCouponUsageVO = {
    data?: CouponUsageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCreditAccountVO = {
    data?: CreditAccountVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCreditUnitVO = {
    data?: CreditUnitVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCustomerGroupVO = {
    data?: CustomerGroupVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCustomerSourceVO = {
    data?: CustomerSourceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoCustomerTagVO = {
    data?: CustomerTagVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoDataCleanupVO = {
    data?: DataCleanupVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoHolidayVO = {
    data?: HolidayVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoOrderRoomPlanVO = {
    data?: OrderRoomPlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoRoomTypeVO = {
    data?: RoomTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoderpltvvErpManagentApiVoSessionVO = {
    data?: SessionVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoDouyinGroupBuyingPlanVO = {
    data?: DouyinGroupBuyingPlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoEmployeeGiftRecordVO = {
    data?: EmployeeGiftRecordVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoEmployeeGroupVO = {
    data?: EmployeeGroupVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoFlavorVO = {
    data?: FlavorVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoGiftGroupVO = {
    data?: GiftGroupVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoGiftRecordVO = {
    data?: GiftRecordVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoHistoricalRecordVO = {
    data?: HistoricalRecordVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoIngredientTypeVO = {
    data?: IngredientTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoInventoryRecordVO = {
    data?: InventoryRecordVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoInventoryTransactionVO = {
    data?: InventoryTransactionVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMarketingCampaignVO = {
    data?: MarketingCampaignVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMarketingRoleVO = {
    data?: MarketingRoleVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMarketServiceVO = {
    data?: MarketServiceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMemberCardConsumeVO = {
    data?: MemberCardConsumeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMemberCardOperationVO = {
    data?: MemberCardOperationVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMemberRechargePackageVO = {
    data?: MemberRechargePackageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMemberTransferMoneyVO = {
    data?: MemberTransferMoneyVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoMerchantServiceVO = {
    data?: MerchantServiceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoNetworkVO = {
    data?: NetworkVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoNotificationSettingVO = {
    data?: NotificationSettingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoOnlineOperationSettingsVO = {
    data?: OnlineOperationSettingsVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoOperationSettingsVO = {
    data?: OperationSettingsVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPaymentMethodVO = {
    data?: PaymentMethodVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPermissionRoleVO = {
    data?: PermissionRoleVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPhoneBlacklistVO = {
    data?: PhoneBlacklistVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPointsExchangeVO = {
    data?: PointsExchangeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPosMachineVO = {
    data?: PosMachineVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPrepaidCardTypeVO = {
    data?: PrepaidCardTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoPrintTemplateVO = {
    data?: PrintTemplateVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductBindingVO = {
    data?: ProductBindingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductDisplayCategoryVO = {
    data?: ProductDisplayCategoryVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductionOrderPlanVO = {
    data?: ProductionOrderPlanVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductMultipleBuyFreeVO = {
    data?: ProductMultipleBuyFreeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductOutTypeVO = {
    data?: ProductOutTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductPackageTypeVO = {
    data?: ProductPackageTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductPackageVO = {
    data?: ProductPackageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductSalesTemplateVO = {
    data?: ProductSalesTemplateVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductStatisticsCategoryVO = {
    data?: ProductStatisticsCategoryVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductStorageItemStatVO = {
    data?: ProductStorageItemStatVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductStorageVO = {
    data?: ProductStorageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductTimeSlotVO = {
    data?: ProductTimeSlotVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductTypeVO = {
    data?: ProductTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoProductWithdrawVO = {
    data?: ProductWithdrawVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRechargePackageVO = {
    data?: RechargePackageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRecipeVO = {
    data?: RecipeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRedemptionRecordVO = {
    data?: RedemptionRecordVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoReportTypeVO = {
    data?: ReportTypeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRewardVO = {
    data?: RewardVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRoomFaultVO = {
    data?: RoomFaultVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRoomGreetingVO = {
    data?: RoomGreetingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRoomThemeVO = {
    data?: RoomThemeVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoRouterVO = {
    data?: RouterVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoSalesPerformanceVO = {
    data?: SalesPerformanceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoSequencerVO = {
    data?: SequencerVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoServiceRewardSettingsVO = {
    data?: ServiceRewardSettingsVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoShiftHandoverFormAndPayBillVO = {
    data?: ShiftHandoverFormAndPayBillVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoShiftHandoverFormVO = {
    data?: ShiftHandoverFormVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoSingingDeviceVO = {
    data?: SingingDeviceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoSmsServiceVO = {
    data?: SmsServiceVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoStatisticsCategoryVO = {
    data?: StatisticsCategoryVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoStatisticsPeriodVO = {
    data?: StatisticsPeriodVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoSubtitleInfoVO = {
    data?: SubtitleInfoVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoTurnoverDataVO = {
    data?: TurnoverDataVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoTvScreenActivityVO = {
    data?: TvScreenActivityVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoVenuePaySettingVO = {
    data?: VenuePaySettingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoVodSettingsVO = {
    data?: VodSettingsVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoVoucherVO = {
    data?: VoucherVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoWarehouseVO = {
    data?: WarehouseVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoWineStorageSettingVO = {
    data?: WineStorageSettingVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type PageVOArrayVoWineStorageVO = {
    data?: WineStorageVO[];
    pageNum?: number;
    pageSize?: number;
    total?: number;
  };

export type ResultVoPageVOArrayVoAppUpgradeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoAppUpgradeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoAuthorizationRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoAuthorizationRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoBirthdayGreetingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoBirthdayGreetingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCallMessageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCallMessageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCallTypesVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCallTypesVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCashierMachineVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCashierMachineVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCloudPrinterVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCloudPrinterVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCommissionPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCommissionPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCommissionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCommissionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCommonRemarkVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCommonRemarkVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoConstructionAssistanceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoConstructionAssistanceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoConsumptionCashbackVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoConsumptionCashbackVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCouponClaimVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCouponClaimVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCouponUsageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCouponUsageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCreditAccountVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCreditAccountVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCreditUnitVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCreditUnitVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCustomerGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCustomerGroupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCustomerSourceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCustomerSourceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoCustomerTagVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoCustomerTagVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoDataCleanupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoDataCleanupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoHolidayVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoHolidayVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoOrderRoomPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoOrderRoomPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoRoomTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoRoomTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoderpltvvErpManagentApiVoSessionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoderpltvvErpManagentApiVoSessionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoDouyinGroupBuyingPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoDouyinGroupBuyingPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoEmployeeGiftRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoEmployeeGiftRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoEmployeeGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoEmployeeGroupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoFlavorVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoFlavorVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoGiftGroupVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoGiftGroupVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoGiftRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoGiftRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoHistoricalRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoHistoricalRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoIngredientTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoIngredientTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoInventoryRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoInventoryRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoInventoryTransactionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoInventoryTransactionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMarketingCampaignVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMarketingCampaignVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMarketingRoleVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMarketingRoleVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMarketServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMarketServiceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMemberCardConsumeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMemberCardConsumeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMemberCardOperationVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMemberCardOperationVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMemberRechargePackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMemberRechargePackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMemberTransferMoneyVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMemberTransferMoneyVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoMerchantServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoMerchantServiceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoNetworkVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoNetworkVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoNotificationSettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoNotificationSettingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoOnlineOperationSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoOnlineOperationSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoOperationSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoOperationSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPaymentMethodVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPaymentMethodVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPermissionRoleVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPermissionRoleVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPhoneBlacklistVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPhoneBlacklistVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPointsExchangeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPointsExchangeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPosMachineVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPosMachineVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPrepaidCardTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPrepaidCardTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoPrintTemplateVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoPrintTemplateVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductBindingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductBindingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductDisplayCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductDisplayCategoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductionOrderPlanVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductionOrderPlanVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductMultipleBuyFreeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductMultipleBuyFreeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductOutTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductOutTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductPackageTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductPackageTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductPackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductPackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductSalesTemplateVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductSalesTemplateVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductStatisticsCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductStatisticsCategoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductStorageItemStatVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductStorageItemStatVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductStorageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductStorageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductTimeSlotVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductTimeSlotVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoProductWithdrawVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoProductWithdrawVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRechargePackageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRechargePackageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRecipeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRecipeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRedemptionRecordVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRedemptionRecordVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoReportTypeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoReportTypeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRewardVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRewardVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRoomFaultVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRoomFaultVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRoomGreetingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRoomGreetingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRoomThemeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRoomThemeVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoRouterVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoRouterVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoSalesPerformanceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoSalesPerformanceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoSequencerVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoSequencerVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoServiceRewardSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoServiceRewardSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoShiftHandoverFormAndPayBillVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoShiftHandoverFormAndPayBillVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoShiftHandoverFormVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoShiftHandoverFormVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoSingingDeviceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoSingingDeviceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoSmsServiceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoSmsServiceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoStatisticsCategoryVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoStatisticsCategoryVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoStatisticsPeriodVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoStatisticsPeriodVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoSubtitleInfoVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoSubtitleInfoVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoTurnoverDataVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoTurnoverDataVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoTvScreenActivityVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoTvScreenActivityVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoVenuePaySettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoVenuePaySettingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoVodSettingsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoVodSettingsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoVoucherVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoVoucherVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoWarehouseVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoWarehouseVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoWineStorageSettingVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoWineStorageSettingVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPageVOArrayVoWineStorageVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PageVOArrayVoWineStorageVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };
