// permission相关类型定义

export type AddPermissionRoleReqDto = {
    /** 角色编码（可选，留空时自动生成） */
    code?: string;
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: 1 | 2;
    /** 角色描述 */
    description?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 角色名称 */
    name: string;
    /** 权限列表 */
    permissions?: string[];
    /** 排序 */
    sortOrder?: number;
    /** 门店ID */
    venueId: string;
  };

export type AssignRolePermissionReqDto = {
    /** 权限列表 */
    permissions: RolePermissionItemReqDto[];
    /** 角色ID */
    roleId: string;
    /** 门店ID */
    venueId: string;
  };

export type CheckPermissionReqDto = {
    /** 操作 */
    action: string;
    /** 员工ID */
    employeeId: string;
    /** 资源 */
    resource: string;
    /** 系统类型 */
    systemType?: string;
    /** 门店ID */
    venueId: string;
  };

export type CheckPermissionVO = {
    /** 是否有权限 */
    hasPermission?: boolean;
  };

export type DeletePermissionRoleReqDto = {
    /** ID */
    id: string;
  };

export type EmployeePermissionVO = {
    /** 所有角色 */
    allRoles?: string[];
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: number;
    /** 员工ID */
    employeeId?: string;
    /** 是否有多角色 */
    hasMultiRole?: boolean;
    /** 权限详细信息列表 */
    permissionDetails?: PermissionDetailVO[];
    /** 权限代码列表（保持兼容性） */
    permissions?: string[];
    /** 主要角色 */
    primaryRole?: string;
    /** 门店ID */
    venueId?: string;
  };

export type getApiPermissionRoleRoleIdPermissionsParams = {
    /** 角色ID */
    roleId: string;
    /** 门店ID（可选） */
    venueId?: string;
  };

export type getApiPermissionRoleStatisticsParams = {
    /** 门店ID（可选） */
    venueId?: string;
    /** 开始时间（可选） */
    startTime?: string;
    /** 结束时间（可选） */
    endTime?: string;
  };

export type GetPermissionResourceDetailReqDto = {
    /** 权限资源ID */
    id: string;
  };

export type GetPermissionResourceTreeReqDto = {
    /** 是否只获取启用的权限资源 */
    enabled?: boolean;
    /** 是否只显示本业务系统相关的权限资源 */
    onlyMine?: boolean;
    /** 权限类型过滤（SYSTEM/PAGE/MODULE/OPERATION/DATA） */
    type?: string;
    /** 门店ID（可选，获取门店特定权限） */
    venueId?: string;
  };

export type GetRolePermissionsReq = {
    /** 角色ID */
    roleId: string;
    /** 门店ID */
    venueId: string;
  };

export type PermissionDetailVO = {
    /** 权限代码 */
    code?: string;
    /** 描述 */
    description?: string;
    /** 图标 */
    icon?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 权限名称 */
    name?: string;
    /** 父级ID */
    parentId?: string;
    /** 资源路径 */
    path?: string;
    /** 排序 */
    sort?: number;
    /** 权限类型：SYSTEM-系统 PAGE-页面 MODULE-模块 OPERATION-操作 DATA-数据 */
    type?: string;
    /** 权限类型名称 */
    typeName?: string;
  };

export type PermissionResourceAddReq = {
    /** 资源编码（唯一） */
    code: string;
    /** 描述 */
    description?: string;
    /** 图标 */
    icon?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 资源名称 */
    name: string;
    /** 父级ID（可选） */
    parentId?: string;
    /** 资源路径 */
    path?: string;
    /** 排序 */
    sort?: number;
    /** 资源类型：SYSTEM-系统 PAGE-页面 MODULE-模块 OPERATION-操作 DATA-数据 */
    type: 'SYSTEM' | 'PAGE' | 'MODULE' | 'OPERATION' | 'DATA';
    /** 门店ID（可选，用于门店特定资源） */
    venueId?: string;
  };

export type PermissionResourceDeleteReq = {
    /** 是否强制删除（删除子权限） */
    force?: boolean;
    /** 权限资源ID */
    id: string;
  };

export type PermissionResourceListVO = {
    /** 权限资源列表 */
    list?: PermissionResourceVO[];
    /** 当前页码 */
    pageNo?: number;
    /** 页大小 */
    pageSize?: number;
    /** 总页数 */
    pages?: number;
    /** 总记录数 */
    total?: number;
  };

export type PermissionResourceQueryReq = {
    /** 资源编码（模糊查询） */
    code?: string;
    /** 是否启用过滤 */
    isEnabled?: boolean;
    /** 资源名称（模糊查询） */
    name?: string;
    /** 页码（默认1） */
    pageNo?: number;
    /** 页大小（默认20，最大100） */
    pageSize?: number;
    /** 父级ID过滤 */
    parentId?: string;
    /** 资源路径（模糊查询） */
    path?: string;
    /** 排序字段 */
    sortField?: string;
    /** 排序方向 */
    sortOrder?: 'asc' | 'desc';
    /** 资源类型过滤 */
    type?: 'SYSTEM' | 'PAGE' | 'MODULE' | 'OPERATION' | 'DATA';
    /** 门店ID过滤 */
    venueId?: string;
  };

export type PermissionResourceTreeVO = {
    /** 子节点列表 */
    children?: PermissionResourceTreeVO[];
    /** 资源编码 */
    code?: string;
    /** 创建时间 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** 是否有子节点 */
    hasChildren?: boolean;
    /** 图标 */
    icon?: string;
    /** 权限资源ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 树层级（1-5） */
    level?: number;
    /** 资源名称 */
    name?: string;
    /** 父级ID */
    parentId?: string;
    /** 资源路径 */
    path?: string;
    /** 排序 */
    sort?: number;
    /** 资源类型 */
    type?: string;
    /** 资源类型名称 */
    typeName?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type PermissionResourceUpdateReq = {
    /** 资源编码 */
    code?: string;
    /** 描述 */
    description?: string;
    /** 图标 */
    icon?: string;
    /** 权限资源ID */
    id: string;
    /** 是否启用（指针类型，支持null） */
    isEnabled?: boolean;
    /** 资源名称 */
    name?: string;
    /** 父级ID */
    parentId?: string;
    /** 资源路径 */
    path?: string;
    /** 排序 */
    sort?: number;
    /** 资源类型 */
    type?: 'SYSTEM' | 'PAGE' | 'MODULE' | 'OPERATION' | 'DATA';
    /** 门店ID */
    venueId?: string;
  };

export type PermissionResourceVO = {
    /** 资源编码 */
    code?: string;
    /** 创建时间 */
    ctime?: number;
    /** 描述 */
    description?: string;
    /** 图标 */
    icon?: string;
    /** 权限资源ID */
    id?: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 资源名称 */
    name?: string;
    /** 父级ID */
    parentId?: string;
    /** 资源路径 */
    path?: string;
    /** 排序 */
    sort?: number;
    /** 资源类型：SYSTEM-系统 PAGE-页面 MODULE-模块 OPERATION-操作 DATA-数据 */
    type?: string;
    /** 资源类型名称 */
    typeName?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type PermissionRoleVO = {
    /** 角色编码 */
    code?: string;
    /** 创建时间 */
    ctime?: number;
    /** 角色描述 */
    description?: string;
    /** ID */
    id?: string;
    /** 是否默认角色 */
    isDefault?: boolean;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 是否系统角色 */
    isSystemRole?: boolean;
    /** 角色名称 */
    name?: string;
    /** 排序 */
    sortOrder?: number;
    /** 状态 */
    state?: number;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
    /** 版本 */
    version?: number;
  };

export type PermissionValidationVO = {
    /** 数据过滤条件 */
    dataFilter?: string;
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: number;
    /** 是否有数据权限 */
    hasDataPermission?: boolean;
    /** 是否有权限 */
    hasPermission?: boolean;
    /** 验证消息 */
    message?: string;
  };

export type QueryPermissionRoleReqDto = {
    /** 角色编码 */
    code?: string;
    /** 员工类型 */
    employeeType?: string;
    /** ID */
    id?: string;
    /** 角色名称 */
    name?: string;
    /** 是否只显示本业务系统相关的角色 */
    onlyMine?: boolean;
    /** 页码 */
    pageNum?: number;
    /** 页大小 */
    pageSize?: number;
    /** 门店ID */
    venueId?: string;
  };

export type ResultArrayVoPermissionResourceTreeVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PermissionResourceTreeVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultArrayVoPermissionRoleVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PermissionRoleVO[];
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoCheckPermissionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: CheckPermissionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoEmployeePermissionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: EmployeePermissionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPermissionResourceListVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PermissionResourceListVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPermissionResourceVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PermissionResourceVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPermissionRoleVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PermissionRoleVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoPermissionValidationVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: PermissionValidationVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRolePermissionListVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RolePermissionListVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRolePermissionStatisticsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RolePermissionStatisticsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoRoleWithPermissionsVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: RoleWithPermissionsVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type ResultVoUserPermissionVO = {
    attachments?: Record<string, any>;
    code?: number;
    data?: UserPermissionVO;
    message?: string;
    requestID?: string;
    serverTime?: number;
    traceId?: string;
  };

export type RolePermissionAssignReq = {
    /** 操作员ID */
    operatorId?: string;
    /** 操作员名称 */
    operatorName?: string;
    /** 权限资源编码列表 */
    permissionCodes: string[];
    /** 操作原因 */
    reason?: string;
    /** 角色编码 */
    roleCode: string;
    /** 门店ID（可选） */
    venueId?: string;
  };

export type RolePermissionCopyReq = {
    /** 操作员ID */
    operatorId?: string;
    /** 操作员名称 */
    operatorName?: string;
    /** 操作原因 */
    reason?: string;
    /** 源角色编码 */
    sourceRoleCode: string;
    /** 目标角色编码 */
    targetRoleCode: string;
    /** 门店ID（可选） */
    venueId?: string;
  };

export type RolePermissionDetailVO = {
    /** 创建时间 */
    ctime?: number;
    /** 数据过滤条件 */
    dataFilter?: string;
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: number;
    /** 是否有数据权限 */
    hasDataPermission?: boolean;
    /** 关联ID */
    id?: string;
    /** 操作员ID */
    operatorId?: string;
    /** 操作员名称 */
    operatorName?: string;
    /** 权限资源编码 */
    resourceCode?: string;
    /** 权限资源ID */
    resourceId?: string;
    /** 权限资源名称 */
    resourceName?: string;
    /** 权限资源类型 */
    resourceType?: string;
    /** 权限资源类型名称 */
    resourceTypeName?: string;
    /** 角色ID */
    roleId?: string;
    /** 角色名称 */
    roleName?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type RolePermissionItemReqDto = {
    /** 操作权限: access,read,write,delete,manage */
    actions: string[];
    /** 数据权限范围(JSON) */
    dataScope?: string;
    /** 资源ID */
    resourceId: string;
  };

export type RolePermissionListVO = {
    /** 角色权限列表 */
    list?: RolePermissionDetailVO[];
    /** 当前页码 */
    pageNo?: number;
    /** 页大小 */
    pageSize?: number;
    /** 总页数 */
    pages?: number;
    /** 总记录数 */
    total?: number;
  };

export type RolePermissionQueryReq = {
    /** 是否有权限过滤 */
    hasPermission?: boolean;
    /** 是否只显示本业务系统相关的权限 */
    onlyMine?: boolean;
    /** 页码（默认1） */
    pageNo?: number;
    /** 页大小（默认20，最大100） */
    pageSize?: number;
    /** 权限资源编码过滤 */
    permissionCode?: string;
    /** 权限资源类型过滤 */
    resourceType?: number;
    /** 角色编码过滤 */
    roleCode?: string;
    /** 排序字段 */
    sortField?: string;
    /** 排序方向 */
    sortOrder?: 'asc' | 'desc';
    /** 门店ID过滤 */
    venueId?: string;
  };

export type RolePermissionRevokeReq = {
    /** 操作员ID */
    operatorId?: string;
    /** 操作员名称 */
    operatorName?: string;
    /** 权限资源编码列表 */
    permissionCodes: string[];
    /** 操作原因 */
    reason?: string;
    /** 角色编码 */
    roleCode: string;
    /** 门店ID（可选） */
    venueId?: string;
  };

export type RolePermissionStatisticsVO = {
    /** 总分配数 */
    totalAssignments?: number;
    /** 门店ID */
    venueId?: string;
  };

export type RolePermissionTreeVO = {
    /** 子节点列表 */
    children?: RolePermissionTreeVO[];
    /** 数据过滤条件 */
    dataFilter?: string;
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: number;
    /** 是否有数据权限 */
    hasDataPermission?: boolean;
    /** 是否有权限 */
    hasPermission?: boolean;
    /** 树层级 */
    level?: number;
    /** 父级ID */
    parentId?: string;
    /** 权限资源编码 */
    resourceCode?: string;
    /** 权限资源ID */
    resourceId?: string;
    /** 权限资源名称 */
    resourceName?: string;
    /** 权限资源类型 */
    resourceType?: string;
    /** 权限资源类型名称 */
    resourceTypeName?: string;
  };

export type RolePermissionValidateReq = {
    /** 数据ID（用于数据权限验证） */
    dataId?: string;
    /** 员工ID */
    employeeId: string;
    /** 权限资源编码 */
    permissionCode: string;
    /** 门店ID（可选） */
    venueId?: string;
  };

export type RoleWithPermissionsVO = {
    /** 创建时间 */
    ctime?: number;
    /** 权限数量 */
    permissionCount?: number;
    /** 权限树 */
    permissionTree?: RolePermissionTreeVO[];
    /** 权限列表 */
    permissions?: RolePermissionDetailVO[];
    /** 角色编码 */
    roleCode?: string;
    /** 角色描述 */
    roleDescription?: string;
    /** 角色ID */
    roleId?: string;
    /** 角色名称 */
    roleName?: string;
    /** 更新时间 */
    utime?: number;
    /** 门店ID */
    venueId?: string;
  };

export type UpdatePermissionRoleReqDto = {
    /** 角色编码 */
    code?: string;
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: 1 | 2;
    /** 角色描述 */
    description?: string;
    /** ID */
    id: string;
    /** 是否启用 */
    isEnabled?: boolean;
    /** 角色名称 */
    name?: string;
    /** 权限列表 */
    permissions?: string[];
    /** 排序 */
    sortOrder?: number;
  };

export type UserPermissionVO = {
    /** 数据权限范围：1-单一门店 2-全局数据 */
    dataScope?: number;
    /** 菜单树 */
    menuTree?: PermissionResourceTreeVO[];
    /** 权限详细信息列表 */
    permissionDetails?: PermissionDetailVO[];
    /** 权限代码列表 */
    permissions?: string[];
    /** 角色列表 */
    roles?: string[];
    /** 系统类型 */
    systemType?: string;
    /** 用户ID */
    userId?: string;
    /** 门店ID */
    venueId?: string;
  };
