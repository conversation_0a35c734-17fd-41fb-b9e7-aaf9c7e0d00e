import request from "@/utils/request";
import * as Types from "../shared/types";

/** 小程序扫码 小程序扫码 POST /api/miniapp/login */
export async function postApiMiniappLogin (body: Types.MiniAppLoginReqDto, options?: { [key: string]: any }) {
  return request<Types.LoginMinAppResultVO>('/api/miniapp/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 小程序创建门店 小程序创建门店 POST /api/miniapp/venue/add */
export async function postApiMiniappVenueAdd (body: Types.AddVenueForMiniAppReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueVO>('/api/miniapp/venue/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询小程序门店绑定列表 查询小程序门店绑定列表 POST /api/miniapp/venue/bind/list */
export async function postApiMiniappVenueBindList (body: Types.QueryMiniAppVenueBindListReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueVO[]>('/api/miniapp/venue/bind/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 修改密码 修改用户密码，需要提供新密码和确认密码，支持短信验证码(可选) POST /api/miniapp/erp/user/change-password */
export async function postApiMiniappErpUserChangePassword (body: Types.ChangePasswordReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/miniapp/erp/user/change-password', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取当前用户信息 从token中获取userId，查询当前用户信息 POST /api/miniapp/erp/user/current */
export async function postApiMiniappErpUserCurrent (options?: { [key: string]: any }) {
  return request<Types.ERPUserVO>('/api/miniapp/erp/user/current', {
    method: 'POST',
    ...(options || {})
  });
}

/** 查询我的门店列表 根据手机号查询用户关联的门店列表 POST /api/miniapp/venue/my */
export async function postApiMiniappVenueMy (body: Types.QueryMyVenueReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueVO[]>('/api/miniapp/venue/my', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 小程序切换门店 小程序用户登录后，如果门店数量大于1个，则需要选择门店进入，进入后需要拿到该门店的员工id，返回门店信息和在该门店的员工信息 POST /api/miniapp/venue/switch */
export async function postApiMiniappVenueOpenApiSwitch (body: Types.SwitchVenueReqDto, options?: { [key: string]: any }) {
  return request<Types.LoginVenueAndEmployeeVO>('/api/miniapp/venue/switch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
