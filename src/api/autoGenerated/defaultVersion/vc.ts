import request from "@/utils/request";
import * as Types from "../shared/types";

/** 批量操作 动态表配置批量操作 POST /vc/table/config/batch */
export async function postVcTableConfigBatch (body: Types.BatchRequest, options?: { [key: string]: any }) {
  return request<any>('/vc/table/config/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除记录 动态表配置删除记录 POST /vc/table/config/delete */
export async function postVcTableConfigOpenApiDelete (body: Types.DeleteRequest, options?: { [key: string]: any }) {
  return request<any>('/vc/table/config/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 列表查询 动态表配置列表查询 POST /vc/table/config/list */
export async function postVcTableConfigList (body: Types.ListRequest, options?: { [key: string]: any }) {
  return request<Types.APIResponse & { data?: Types.ListResponse }>('/vc/table/config/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 下拉选项 动态表配置下拉选项查询 GET /vc/table/config/options */
export async function getVcTableConfigOptions(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getVcTableConfigOptionsParams,
  options?: { [key: string]: any }
) {
  return request<Types.APIResponse & { data?: Types.OptionsResponse[] }>('/vc/table/config/options', {
    method: 'GET',
    params: {
      ...params
    },
    ...(options || {})
  });
}

/** 获取页面配置 获取pages.json配置文件内容 GET /vc/table/config/pages */
export async function getVcTableConfigPages (options?: { [key: string]: any }) {
  return request<Types.APIResponse & { data?: any }>('/vc/table/config/pages', {
    method: 'GET',
    ...(options || {})
  });
}
