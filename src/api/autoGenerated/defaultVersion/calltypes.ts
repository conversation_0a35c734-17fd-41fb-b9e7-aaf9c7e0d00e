import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加呼叫类型 添加呼叫类型 POST /api/call-types/add */
export async function postApiCallTypesAdd (body: Types.AddCallTypesReqDto, options?: { [key: string]: any }) {
  return request<Types.CallTypesVO>('/api/call-types/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除呼叫类型 删除呼叫类型 POST /api/call-types/delete */
export async function postApiCallTypesOpenApiDelete (body: Types.DeleteCallTypesReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/call-types/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询呼叫类型列表 查询呼叫类型列表 POST /api/call-types/list */
export async function postApiCallTypesList (body: Types.QueryCallTypesReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoCallTypesVO>('/api/call-types/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询呼叫类型 查询呼叫类型 POST /api/call-types/query */
export async function postApiCallTypesQuery (body: Types.QueryCallTypesReqDto, options?: { [key: string]: any }) {
  return request<Types.CallTypesVO[]>('/api/call-types/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新呼叫类型 更新呼叫类型 POST /api/call-types/update */
export async function postApiCallTypesUpdate (body: Types.UpdateCallTypesReqDto, options?: { [key: string]: any }) {
  return request<Types.CallTypesVO>('/api/call-types/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
