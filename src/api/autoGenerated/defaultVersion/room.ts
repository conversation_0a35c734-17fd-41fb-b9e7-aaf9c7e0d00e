import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加房间 添加房间 POST /api/room/add */
export async function postApiRoomAdd (body: Types.AddRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomVO>('/api/room/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 强制关闭房间 强制关闭房间 POST /api/room/close/force */
export async function postApiRoomCloseForce (body: Types.CloseRoomReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/room/close/force', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除房间 删除房间 POST /api/room/delete */
export async function postApiRoomOpenApiDelete (body: Types.DeleteRoomReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/room/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间列表 查询房间列表 POST /api/room/list */
export async function postApiRoomList (body: Types.QueryRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoderpltvvErpManagentApiVoRoomVO>('/api/room/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 开台视图 开台视图 POST /api/room/open-view */
export async function postApiRoomOpenView (body: Types.QueryRoomStageReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomVO>('/api/room/open-view', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间 查询房间 POST /api/room/query */
export async function postApiRoomQuery (body: Types.QueryRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomVO[]>('/api/room/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询场次房间 查询场次房间 POST /api/room/stage */
export async function postApiRoomStage (body: Types.QueryRoomStageReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomStageVO[]>('/api/room/stage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新房间 更新房间 POST /api/room/update */
export async function postApiRoomUpdate (body: Types.UpdateRoomReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomVO>('/api/room/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加房间故障 添加房间故障 POST /api/roomFault/add */
export async function postApiRoomFaultAdd (body: Types.AddRoomFaultReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomFaultVO>('/api/roomFault/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除房间故障 删除房间故障 POST /api/roomFault/delete */
export async function postApiRoomFaultOpenApiDelete (body: Types.DeleteRoomFaultReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/roomFault/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间故障列表 查询房间故障列表 POST /api/roomFault/list */
export async function postApiRoomFaultList (body: Types.QueryRoomFaultReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoRoomFaultVO>('/api/roomFault/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间故障 查询房间故障 POST /api/roomFault/query */
export async function postApiRoomFaultQuery (body: Types.QueryRoomFaultReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomFaultVO[]>('/api/roomFault/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新房间故障 更新房间故障 POST /api/roomFault/update */
export async function postApiRoomFaultUpdate (body: Types.UpdateRoomFaultReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomFaultVO>('/api/roomFault/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加房间类型 添加房间类型 POST /api/roomType/add */
export async function postApiRoomTypeAdd (body: Types.AddRoomTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomTypeVO>('/api/roomType/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除房间类型 删除房间类型 POST /api/roomType/delete */
export async function postApiRoomTypeOpenApiDelete (body: Types.DeleteRoomTypeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/roomType/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间类型列表 查询房间类型列表 POST /api/roomType/list */
export async function postApiRoomTypeList (body: Types.QueryRoomTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoderpltvvErpManagentApiVoRoomTypeVO>('/api/roomType/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间类型 查询房间类型 POST /api/roomType/query */
export async function postApiRoomTypeQuery (body: Types.QueryRoomTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomTypeVO[]>('/api/roomType/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新房间类型 更新房间类型 POST /api/roomType/update */
export async function postApiRoomTypeUpdate (body: Types.UpdateRoomTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomTypeVO>('/api/roomType/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加房间问候语 添加房间问候语 POST /api/room-greeting/add */
export async function postApiRoomGreetingAdd (body: Types.AddRoomGreetingReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomGreetingVO>('/api/room-greeting/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除房间问候语 删除房间问候语 POST /api/room-greeting/delete */
export async function postApiRoomGreetingOpenApiDelete (body: Types.DeleteRoomGreetingReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/room-greeting/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间问候语列表 查询房间问候语列表 POST /api/room-greeting/list */
export async function postApiRoomGreetingList (body: Types.QueryRoomGreetingReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoRoomGreetingVO>('/api/room-greeting/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间问候语 查询房间问候语 POST /api/room-greeting/query */
export async function postApiRoomGreetingQuery (body: Types.QueryRoomGreetingReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomGreetingVO[]>('/api/room-greeting/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新房间问候语 更新房间问候语 POST /api/room-greeting/update */
export async function postApiRoomGreetingUpdate (body: Types.UpdateRoomGreetingReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomGreetingVO>('/api/room-greeting/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加房间异常记录 添加房间异常记录 POST /api/roomException/add */
export async function postApiRoomExceptionAdd (body: Types.AddRoomExceptionReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExceptionVO>('/api/roomException/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除房间异常记录 删除房间异常记录 POST /api/roomException/delete */
export async function postApiRoomExceptionOpenApiDelete (body: Types.DeleteRoomExceptionReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/roomException/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间异常记录列表 查询房间异常记录列表 POST /api/roomException/list */
export async function postApiRoomExceptionList (body: Types.QueryRoomExceptionReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExceptionVO[]>('/api/roomException/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间异常记录 查询房间异常记录 POST /api/roomException/query */
export async function postApiRoomExceptionQuery (body: Types.QueryRoomExceptionReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExceptionVO[]>('/api/roomException/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新房间异常记录 更新房间异常记录 POST /api/roomException/update */
export async function postApiRoomExceptionUpdate (body: Types.UpdateRoomExceptionReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomExceptionVO>('/api/roomException/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加房间主题 添加房间主题 POST /api/roomTheme/add */
export async function postApiRoomThemeAdd (body: Types.AddRoomThemeReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomThemeVO>('/api/roomTheme/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除房间主题 删除房间主题 POST /api/roomTheme/delete */
export async function postApiRoomThemeOpenApiDelete (body: Types.DeleteRoomThemeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/roomTheme/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间主题列表 查询房间主题列表 POST /api/roomTheme/list */
export async function postApiRoomThemeList (body: Types.QueryRoomThemeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoRoomThemeVO>('/api/roomTheme/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询房间主题 查询房间主题 POST /api/roomTheme/query */
export async function postApiRoomThemeQuery (body: Types.QueryRoomThemeReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomThemeVO[]>('/api/roomTheme/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新房间主题 更新房间主题 POST /api/roomTheme/update */
export async function postApiRoomThemeUpdate (body: Types.UpdateRoomThemeReqDto, options?: { [key: string]: any }) {
  return request<Types.RoomThemeVO>('/api/roomTheme/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
