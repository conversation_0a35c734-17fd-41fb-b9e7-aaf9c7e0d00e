import request from "@/utils/request";
import * as Types from "../shared/types";

/** 微信验证 微信服务器验证 GET /api/wx/event */
export async function getApiWxEvent(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getApiWxEventParams,
  options?: { [key: string]: any }
) {
  return request<string>('/api/wx/event', {
    method: 'GET',
    params: {
      ...params
    },
    ...(options || {})
  });
}

/** 微信事件 微信事件 POST /api/wx/event */
export async function postApiWxEvent (body: Types.WXEventReqDto, options?: { [key: string]: any }) {
  return request<Types.WXEventVO>('/api/wx/event', {
    method: 'POST',
    headers: {
      'Content-Type': 'text/xml'
    },
    data: body,
    ...(options || {})
  });
}
