import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加存储设置 添加存储设置 POST /api/wineStorageSetting/add */
export async function postApiWineStorageSettingAdd (body: Types.AddWineStorageSettingReqDto, options?: { [key: string]: any }) {
  return request<Types.WineStorageSettingVO>('/api/wineStorageSetting/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除存储设置 删除存储设置 POST /api/wineStorageSetting/delete */
export async function postApiWineStorageSettingOpenApiDelete (body: Types.DeleteWineStorageSettingReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/wineStorageSetting/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询存储设置列表 查询存储设置列表 POST /api/wineStorageSetting/list */
export async function postApiWineStorageSettingList (body: Types.QueryWineStorageSettingReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoWineStorageSettingVO>('/api/wineStorageSetting/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询存储设置 查询存储设置 POST /api/wineStorageSetting/query */
export async function postApiWineStorageSettingQuery (body: Types.QueryWineStorageSettingReqDto, options?: { [key: string]: any }) {
  return request<Types.WineStorageSettingVO[]>('/api/wineStorageSetting/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新存储设置 更新存储设置 POST /api/wineStorageSetting/update */
export async function postApiWineStorageSettingUpdate (body: Types.UpdateWineStorageSettingReqDto, options?: { [key: string]: any }) {
  return request<Types.WineStorageSettingVO>('/api/wineStorageSetting/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
