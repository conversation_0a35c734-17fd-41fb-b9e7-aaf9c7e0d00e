import request from "@/utils/request";
import * as Types from "../shared/types";

/** 删除ERP用户 删除ERP用户 POST /api/erp/user/delete */
export async function postApiErpUserOpenApiDelete (body: Types.DeleteERPUserReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/erp/user/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询ERP用户列表 查询ERP用户列表 POST /api/erp/user/list */
export async function postApiErpUserList (body: Types.QueryERPUserReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoERPUserVO>('/api/erp/user/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询ERP用户 查询ERP用户 POST /api/erp/user/query */
export async function postApiErpUserQuery (body: Types.QueryERPUserReqDto, options?: { [key: string]: any }) {
  return request<Types.ERPUserVO[]>('/api/erp/user/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 用户注册 用户注册接口 POST /api/erp/user/register */
export async function postApiErpUserRegister (body: Types.AddERPUserReqDto, options?: { [key: string]: any }) {
  return request<Types.ERPUserVO>('/api/erp/user/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新ERP用户 更新ERP用户 POST /api/erp/user/update */
export async function postApiErpUserUpdate (body: Types.UpdateERPUserReqDto, options?: { [key: string]: any }) {
  return request<Types.ERPUserVO>('/api/erp/user/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加ERP用户和员工关联 添加ERP用户和员工关联 POST /api/erp/user/employee/add */
export async function postApiErpUserEmployeeAdd (body: Types.AddERPUserAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.ERPUserAndEmployeeVO>('/api/erp/user/employee/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除ERP用户和员工关联 删除ERP用户和员工关联 POST /api/erp/user/employee/delete */
export async function postApiErpUserEmployeeOpenApiDelete (body: Types.DeleteERPUserAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/erp/user/employee/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询ERP用户和员工关联列表 查询ERP用户和员工关联列表 POST /api/erp/user/employee/list */
export async function postApiErpUserEmployeeList (body: Types.QueryERPUserAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoERPUserAndEmployeeVO>('/api/erp/user/employee/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询ERP用户和员工关联 查询ERP用户和员工关联 POST /api/erp/user/employee/query */
export async function postApiErpUserEmployeeQuery (body: Types.QueryERPUserAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.ERPUserAndEmployeeVO[]>('/api/erp/user/employee/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新ERP用户和员工关联 更新ERP用户和员工关联 POST /api/erp/user/employee/update */
export async function postApiErpUserEmployeeUpdate (body: Types.UpdateERPUserAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.ERPUserAndEmployeeVO>('/api/erp/user/employee/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
