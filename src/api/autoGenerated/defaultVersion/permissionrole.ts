import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加权限角色 添加权限角色 POST /api/permission-role/add */
export async function postApiPermissionRoleAdd (body: Types.AddPermissionRoleReqDto, options?: { [key: string]: any }) {
  return request<Types.PermissionRoleVO>('/api/permission-role/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除权限角色 删除权限角色 POST /api/permission-role/delete */
export async function postApiPermissionRoleOpenApiDelete (body: Types.DeletePermissionRoleReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/permission-role/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 分页查询权限角色 分页查询权限角色 POST /api/permission-role/list */
export async function postApiPermissionRoleList (body: Types.QueryPermissionRoleReqDto, options?: { [key: string]: any }) {
  return request<Types.RoleListVO>('/api/permission-role/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询权限角色 查询权限角色 POST /api/permission-role/query */
export async function postApiPermissionRoleQuery (body: Types.QueryPermissionRoleReqDto, options?: { [key: string]: any }) {
  return request<Types.PermissionRoleVO[]>('/api/permission-role/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新权限角色 更新权限角色 POST /api/permission-role/update */
export async function postApiPermissionRoleUpdate (body: Types.UpdatePermissionRoleReqDto, options?: { [key: string]: any }) {
  return request<Types.PermissionRoleVO>('/api/permission-role/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
