import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加员工赠金记录 添加员工赠金记录 POST /api/employee-gift-record/add */
export async function postApiEmployeeGiftRecordAdd (body: Types.AddEmployeeGiftRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.EmployeeGiftRecordVO>('/api/employee-gift-record/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除员工赠金记录 删除员工赠金记录 POST /api/employee-gift-record/delete */
export async function postApiEmployeeGiftRecordOpenApiDelete (body: Types.DeleteEmployeeGiftRecordReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/employee-gift-record/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询员工赠金记录列表 查询员工赠金记录列表 POST /api/employee-gift-record/list */
export async function postApiEmployeeGiftRecordList (body: Types.QueryEmployeeGiftRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoEmployeeGiftRecordVO>('/api/employee-gift-record/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询员工赠金记录 查询员工赠金记录 POST /api/employee-gift-record/query */
export async function postApiEmployeeGiftRecordQuery (body: Types.QueryEmployeeGiftRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.EmployeeGiftRecordVO[]>('/api/employee-gift-record/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新员工赠金记录 更新员工赠金记录 POST /api/employee-gift-record/update */
export async function postApiEmployeeGiftRecordUpdate (body: Types.UpdateEmployeeGiftRecordReqDto, options?: { [key: string]: any }) {
  return request<Types.EmployeeGiftRecordVO>('/api/employee-gift-record/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
