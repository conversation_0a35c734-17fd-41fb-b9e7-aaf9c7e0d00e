import request from "@/utils/request";
import * as Types from "../shared/types";

/** 门店授权 门店授权，获取并更新AppID和AppKey POST /api/venue/authorize */
export async function postApiVenueAuthorize (body: Types.AuthorizeVenueReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venue/authorize', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除门店 删除门店 POST /api/venue/delete */
export async function postApiVenueOpenApiDelete (body: Types.DeleteVenueReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venue/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店列表 查询门店列表 POST /api/venue/list */
export async function postApiVenueList (body: Types.QueryVenueReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoVenueVO>('/api/venue/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店列表 查询门店列表 POST /api/venue/query */
export async function postApiVenueQuery (body: Types.QueryVenueReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueVO>('/api/venue/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新门店 更新门店 POST /api/venue/update */
export async function postApiVenueUpdate (body: Types.UpdateVenueReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueVO>('/api/venue/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加门店和员工关联 添加门店和员工关联 POST /api/venue-employee/add */
export async function postApiVenueEmployeeAdd (body: Types.AddVenueAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAndEmployeeVO>('/api/venue-employee/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除门店和员工关联 删除门店和员工关联 POST /api/venue-employee/delete */
export async function postApiVenueEmployeeOpenApiDelete (body: Types.DeleteVenueAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venue-employee/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店和员工关联列表 查询门店和员工关联列表 POST /api/venue-employee/list */
export async function postApiVenueEmployeeList (body: Types.QueryVenueAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoVenueAndEmployeeVO>('/api/venue-employee/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店和员工关联 查询门店和员工关联 POST /api/venue-employee/query */
export async function postApiVenueEmployeeQuery (body: Types.QueryVenueAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAndEmployeeVO[]>('/api/venue-employee/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新门店和员工关联 更新门店和员工关联 POST /api/venue-employee/update */
export async function postApiVenueEmployeeUpdate (body: Types.UpdateVenueAndEmployeeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAndEmployeeVO>('/api/venue-employee/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加门店会员关联 添加门店会员关联 POST /api/venue-member/add */
export async function postApiVenueMemberAdd (body: Types.AddVenueAndMemberReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAndMemberVO>('/api/venue-member/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除门店会员关联 删除门店会员关联 POST /api/venue-member/delete */
export async function postApiVenueMemberOpenApiDelete (body: Types.DeleteVenueAndMemberReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venue-member/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店会员关联列表 查询门店会员关联列表 POST /api/venue-member/list */
export async function postApiVenueMemberList (body: Types.QueryVenueAndMemberReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoVenueAndMemberVO>('/api/venue-member/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店会员关联 查询门店会员关联 POST /api/venue-member/query */
export async function postApiVenueMemberQuery (body: Types.QueryVenueAndMemberReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAndMemberVO[]>('/api/venue-member/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新门店会员关联 更新门店会员关联 POST /api/venue-member/update */
export async function postApiVenueMemberUpdate (body: Types.UpdateVenueAndMemberReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAndMemberVO>('/api/venue-member/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 激活门店授权码 激活门店授权码 POST /api/venueAuthCode/active */
export async function postApiVenueAuthCodeActive (body: Types.ActiveVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venueAuthCode/active', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加门店授权码 添加门店授权码 POST /api/venueAuthCode/add */
export async function postApiVenueAuthCodeAdd (body: Types.AddVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAuthCodeVO>('/api/venueAuthCode/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除门店授权码 删除门店授权码 POST /api/venueAuthCode/delete */
export async function postApiVenueAuthCodeOpenApiDelete (body: Types.DeleteVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venueAuthCode/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店授权码列表 查询门店授权码列表 POST /api/venueAuthCode/list */
export async function postApiVenueAuthCodeList (body: Types.QueryVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAuthCodeVO[]>('/api/venueAuthCode/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店授权码 查询门店授权码 POST /api/venueAuthCode/query */
export async function postApiVenueAuthCodeQuery (body: Types.QueryVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAuthCodeVO[]>('/api/venueAuthCode/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店授权码 查询门店授权码 POST /api/venueAuthCode/query/active */
export async function postApiVenueAuthCodeQueryActive (body: Types.QueryVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAuthCodeVO[]>('/api/venueAuthCode/query/active', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店授权码 查询门店授权码 POST /api/venueAuthCode/query/new */
export async function postApiVenueAuthCodeQueryOpenApiNew (body: Types.QueryVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAuthCodeVO[]>('/api/venueAuthCode/query/new', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新门店授权码 更新门店授权码 POST /api/venueAuthCode/update */
export async function postApiVenueAuthCodeUpdate (body: Types.UpdateVenueAuthCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueAuthCodeVO>('/api/venueAuthCode/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取门店收银机授权二维码 获取门店收银机授权二维码(小程序码) POST /api/venue-shouyin/auth */
export async function postApiVenueShouyinAuth (body: Types.AddVenueShouyinAuthReqDto, options?: { [key: string]: any }) {
  return request<Types.VenueCashMachineAuthResultVO>('/api/venue-shouyin/auth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加门店支付设置 添加门店支付设置 POST /api/venue-pay-setting/add */
export async function postApiVenuePaySettingAdd (body: Types.AddVenuePaySettingReqDto, options?: { [key: string]: any }) {
  return request<Types.VenuePaySettingVO>('/api/venue-pay-setting/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除门店支付设置 删除门店支付设置 POST /api/venue-pay-setting/delete */
export async function postApiVenuePaySettingOpenApiDelete (body: Types.DeleteVenuePaySettingReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/venue-pay-setting/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店支付设置列表 查询门店支付设置列表 POST /api/venue-pay-setting/list */
export async function postApiVenuePaySettingList (body: Types.QueryVenuePaySettingReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoVenuePaySettingVO>('/api/venue-pay-setting/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询门店支付设置 查询门店支付设置 POST /api/venue-pay-setting/query */
export async function postApiVenuePaySettingQuery (body: Types.QueryVenuePaySettingReqDto, options?: { [key: string]: any }) {
  return request<Types.VenuePaySettingVO[]>('/api/venue-pay-setting/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新门店支付设置 更新门店支付设置 POST /api/venue-pay-setting/update */
export async function postApiVenuePaySettingUpdate (body: Types.UpdateVenuePaySettingReqDto, options?: { [key: string]: any }) {
  return request<Types.VenuePaySettingVO>('/api/venue-pay-setting/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
