import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加酒库存 添加酒库存 POST /api/wine-storage/add */
export async function postApiWineStorageAdd (body: Types.AddWineStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.WineStorageVO>('/api/wine-storage/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除酒库存 删除酒库存 POST /api/wine-storage/delete */
export async function postApiWineStorageOpenApiDelete (body: Types.DeleteWineStorageReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/wine-storage/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询酒库存列表 查询酒库存列表 POST /api/wine-storage/list */
export async function postApiWineStorageList (body: Types.QueryWineStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoWineStorageVO>('/api/wine-storage/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询酒库存 查询酒库存 POST /api/wine-storage/query */
export async function postApiWineStorageQuery (body: Types.QueryWineStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.WineStorageVO[]>('/api/wine-storage/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新酒库存 更新酒库存 POST /api/wine-storage/update */
export async function postApiWineStorageUpdate (body: Types.UpdateWineStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.WineStorageVO>('/api/wine-storage/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
