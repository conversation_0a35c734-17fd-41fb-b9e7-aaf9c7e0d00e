import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加员工赠金记录 添加员工赠金记录 POST /api/asExample/add */
export async function postApiAsExampleAdd (body: Types.AddAsExampleReqDto, options?: { [key: string]: any }) {
  return request<Types.AsExampleVO>('/api/asExample/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除员工赠金记录 删除员工赠金记录 POST /api/asExample/delete */
export async function postApiAsExampleOpenApiDelete (body: Types.DeleteAsExampleReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/asExample/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询员工赠金记录列表 查询员工赠金记录列表 POST /api/asExample/list */
export async function postApiAsExampleList (body: Types.QueryAsExampleReqDto, options?: { [key: string]: any }) {
  return request<Types.AsExampleVO[]>('/api/asExample/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询员工赠金记录 查询员工赠金记录 POST /api/asExample/query */
export async function postApiAsExampleQuery (body: Types.QueryAsExampleReqDto, options?: { [key: string]: any }) {
  return request<Types.AsExampleVO[]>('/api/asExample/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新员工赠金记录 更新员工赠金记录 POST /api/asExample/update */
export async function postApiAsExampleUpdate (body: Types.UpdateAsExampleReqDto, options?: { [key: string]: any }) {
  return request<Types.AsExampleVO>('/api/asExample/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
