import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加产品 添加产品 POST /api/product/add */
export async function postApiProductAdd (body: Types.AddProductReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductVO>('/api/product/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品 删除产品 POST /api/product/delete */
export async function postApiProductOpenApiDelete (body: Types.DeleteProductReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/product/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品列表 查询产品列表 POST /api/product/list */
export async function postApiProductList (body: Types.QueryProductReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductVO>('/api/product/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品类型列表 查询产品类型列表 POST /api/product/list-types */
export async function postApiProductListTypes (options?: { [key: string]: any }) {
  return request<Types.ProductTypeAndPackageVO>('/api/product/list-types', {
    method: 'POST',
    ...(options || {})
  });
}

/** 查询产品类型列表-pad端 查询产品类型列表-pad端 POST /api/product/list-types-pad */
export async function postApiProductListTypesPad (options?: { [key: string]: any }) {
  return request<Types.ProductTypeAndPackageVO>('/api/product/list-types-pad', {
    method: 'POST',
    ...(options || {})
  });
}

/** 查询产品 查询产品 POST /api/product/query */
export async function postApiProductQuery (body: Types.QueryProductReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductVO[]>('/api/product/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品详情 查询产品详情 POST /api/product/query-detail-bytype */
export async function postApiProductQueryDetailBytype (body: Types.QueryProductReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOrPackageRVO>('/api/product/query-detail-bytype', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品详情-pad端 查询产品详情-pad端 POST /api/product/query-detail-bytype-pad */
export async function postApiProductQueryDetailBytypePad (body: Types.QueryProductReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOrPackageRVO>('/api/product/query-detail-bytype-pad', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 产品估清 产品估清 POST /api/product/sold-out */
export async function postApiProductSoldOut (body: Types.UpdateProductSoldOutReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/product/sold-out', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品 更新产品 POST /api/product/update */
export async function postApiProductUpdate (body: Types.UpdateProductReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductVO>('/api/product/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加产品绑定 添加产品绑定 POST /api/product-binding/add */
export async function postApiProductBindingAdd (body: Types.AddProductBindingReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductBindingVO>('/api/product-binding/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品绑定 删除产品绑定 POST /api/product-binding/delete */
export async function postApiProductBindingOpenApiDelete (body: Types.DeleteProductBindingReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/product-binding/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品绑定列表 查询产品绑定列表 POST /api/product-binding/list */
export async function postApiProductBindingList (body: Types.QueryProductBindingReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductBindingVO>('/api/product-binding/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品绑定 查询产品绑定 POST /api/product-binding/query */
export async function postApiProductBindingQuery (body: Types.QueryProductBindingReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductBindingVO[]>('/api/product-binding/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品绑定 更新产品绑定 POST /api/product-binding/update */
export async function postApiProductBindingUpdate (body: Types.UpdateProductBindingReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductBindingVO>('/api/product-binding/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加产品类型 添加产品类型 POST /api/product-type/add */
export async function postApiProductTypeAdd (body: Types.AddProductTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductTypeVO>('/api/product-type/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品类型 删除产品类型 POST /api/product-type/delete */
export async function postApiProductTypeOpenApiDelete (body: Types.DeleteProductTypeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/product-type/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品类型列表 查询产品类型列表 POST /api/product-type/list */
export async function postApiProductTypeList (body: Types.QueryProductTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductTypeVO>('/api/product-type/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品类型 查询产品类型 POST /api/product-type/query */
export async function postApiProductTypeQuery (body: Types.QueryProductTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductTypeVO[]>('/api/product-type/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品类型 更新产品类型 POST /api/product-type/update */
export async function postApiProductTypeUpdate (body: Types.UpdateProductTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductTypeVO>('/api/product-type/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加产品时间段 添加产品时间段 POST /api/productTimeSlot/add */
export async function postApiProductTimeSlotAdd (body: Types.AddProductTimeSlotReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductTimeSlotVO>('/api/productTimeSlot/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品时间段 删除产品时间段 POST /api/productTimeSlot/delete */
export async function postApiProductTimeSlotOpenApiDelete (body: Types.DeleteProductTimeSlotReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productTimeSlot/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品时间段列表 查询产品时间段列表 POST /api/productTimeSlot/list */
export async function postApiProductTimeSlotList (body: Types.QueryProductTimeSlotReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductTimeSlotVO>('/api/productTimeSlot/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品时间段 查询产品时间段 POST /api/productTimeSlot/query */
export async function postApiProductTimeSlotQuery (body: Types.QueryProductTimeSlotReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductTimeSlotVO[]>('/api/productTimeSlot/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品时间段 更新产品时间段 POST /api/productTimeSlot/update */
export async function postApiProductTimeSlotUpdate (body: Types.UpdateProductTimeSlotReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductTimeSlotVO>('/api/productTimeSlot/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加产品套餐 添加产品套餐 POST /api/product-package/add */
export async function postApiProductPackageAdd (body: Types.AddProductPackageReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductPackageVO>('/api/product-package/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品套餐 删除产品套餐 POST /api/product-package/delete */
export async function postApiProductPackageOpenApiDelete (body: Types.DeleteProductPackageReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/product-package/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品套餐列表 查询产品套餐列表 POST /api/product-package/list */
export async function postApiProductPackageList (body: Types.QueryProductPackageReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductPackageVO>('/api/product-package/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品套餐 查询产品套餐 POST /api/product-package/query */
export async function postApiProductPackageQuery (body: Types.QueryProductPackageReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductPackageVO[]>('/api/product-package/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品套餐 更新产品套餐 POST /api/product-package/update */
export async function postApiProductPackageUpdate (body: Types.UpdateProductPackageReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductPackageVO>('/api/product-package/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加产品套餐类型 添加产品套餐类型 POST /api/productPackageType/add */
export async function postApiProductPackageTypeAdd (body: Types.AddProductPackageTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductPackageTypeVO>('/api/productPackageType/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品套餐类型 删除产品套餐类型 POST /api/productPackageType/delete */
export async function postApiProductPackageTypeOpenApiDelete (body: Types.DeleteProductPackageTypeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productPackageType/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品套餐类型列表 查询产品套餐类型列表 POST /api/productPackageType/list */
export async function postApiProductPackageTypeList (body: Types.QueryProductPackageTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductPackageTypeVO>('/api/productPackageType/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品套餐类型 查询产品套餐类型 POST /api/productPackageType/query */
export async function postApiProductPackageTypeQuery (body: Types.QueryProductPackageTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductPackageTypeVO[]>('/api/productPackageType/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品套餐类型 更新产品套餐类型 POST /api/productPackageType/update */
export async function postApiProductPackageTypeUpdate (body: Types.UpdateProductPackageTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductPackageTypeVO>('/api/productPackageType/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加产品销售模板 添加产品销售模板 POST /api/productSalesTemplate/add */
export async function postApiProductSalesTemplateAdd (body: Types.AddProductSalesTemplateReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductSalesTemplateVO>('/api/productSalesTemplate/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除产品销售模板 删除产品销售模板 POST /api/productSalesTemplate/delete */
export async function postApiProductSalesTemplateOpenApiDelete (body: Types.DeleteProductSalesTemplateReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productSalesTemplate/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品销售模板列表 查询产品销售模板列表 POST /api/productSalesTemplate/list */
export async function postApiProductSalesTemplateList (body: Types.QueryProductSalesTemplateReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductSalesTemplateVO>('/api/productSalesTemplate/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询产品销售模板 查询产品销售模板 POST /api/productSalesTemplate/query */
export async function postApiProductSalesTemplateQuery (body: Types.QueryProductSalesTemplateReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductSalesTemplateVO[]>('/api/productSalesTemplate/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新产品销售模板 更新产品销售模板 POST /api/productSalesTemplate/update */
export async function postApiProductSalesTemplateUpdate (body: Types.UpdateProductSalesTemplateReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductSalesTemplateVO>('/api/productSalesTemplate/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 通过订单号查询订单信息 通过订单号查询订单及其关联的房间计划和产品信息 POST /api/productOut/query-by-nos */
export async function postApiProductOutQueryByNos (body: Types.QueryOrdersByNosReqDto, options?: { [key: string]: any }) {
  return request<Types.OrderDetailsMap>('/api/productOut/query-by-nos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加价格方案 添加价格方案 POST /api/productStatisticsCategory/add */
export async function postApiProductStatisticsCategoryAdd (body: Types.AddProductStatisticsCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductStatisticsCategoryVO>('/api/productStatisticsCategory/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除价格方案 删除价格方案 POST /api/productStatisticsCategory/delete */
export async function postApiProductStatisticsCategoryOpenApiDelete (body: Types.DeleteProductStatisticsCategoryReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productStatisticsCategory/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询价格方案列表 查询价格方案列表 POST /api/productStatisticsCategory/list */
export async function postApiProductStatisticsCategoryList (body: Types.QueryProductStatisticsCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductStatisticsCategoryVO>('/api/productStatisticsCategory/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询价格方案 查询价格方案 POST /api/productStatisticsCategory/query */
export async function postApiProductStatisticsCategoryQuery (body: Types.QueryProductStatisticsCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductStatisticsCategoryVO[]>('/api/productStatisticsCategory/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新价格方案 更新价格方案 POST /api/productStatisticsCategory/update */
export async function postApiProductStatisticsCategoryUpdate (body: Types.UpdateProductStatisticsCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductStatisticsCategoryVO>('/api/productStatisticsCategory/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加商品出库类型 添加商品出库类型 POST /api/productOutType/add */
export async function postApiProductOutTypeAdd (body: Types.AddProductOutTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOutTypeVO>('/api/productOutType/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除商品出库类型 删除商品出库类型 POST /api/productOutType/delete */
export async function postApiProductOutTypeOpenApiDelete (body: Types.DeleteProductOutTypeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productOutType/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品出库类型列表 查询商品出库类型列表 POST /api/productOutType/list */
export async function postApiProductOutTypeList (body: Types.QueryProductOutTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductOutTypeVO>('/api/productOutType/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品出库类型 查询商品出库类型 POST /api/productOutType/query */
export async function postApiProductOutTypeQuery (body: Types.QueryProductOutTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOutTypeVO[]>('/api/productOutType/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新商品出库类型 更新商品出库类型 POST /api/productOutType/update */
export async function postApiProductOutTypeUpdate (body: Types.UpdateProductOutTypeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductOutTypeVO>('/api/productOutType/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加商品存储 添加商品存储记录（支持单个商品或多个商品），返回统一的订单信息结构，包含完整的订单详情和商品项列表 POST /api/product-storage/add */
export async function postApiProductStorageAdd (body: Types.UnifiedProductStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductStorageAddResultVO>('/api/product-storage/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取存酒明细详情 获取存酒明细详情，包含基本信息（客户信息、会员卡号、存储位置、存储房间等）和完整的操作历史记录，如无历史记录会生成一条存入记录 GET /api/product-storage/detail/${param0} */
export async function getApiProductStorageDetailId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getApiProductStorageDetailIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<Types.ProductStorageDetailWithHistoryVO>(`/api/product-storage/detail/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {})
  });
}

/** 存酒记录操作 存酒记录操作：支持续存(extend)、报废(discard)、撤销(cancel)、更新(update)、添加商品(addItems)等多种操作类型 POST /api/product-storage/operate */
export async function postApiProductStorageOperate (body: Types.OperateProductStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductStorageVO[]>('/api/product-storage/operate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取存酒单详情 根据单号获取存酒单详情，包含主单信息（客户信息、总数量、处理人等）及所有关联商品明细列表 GET /api/product-storage/order/${param0} */
export async function getApiProductStorageOrderOrderNo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: Types.getApiProductStorageOrderOrderNoParams,
  options?: { [key: string]: any }
) {
  const { orderNo: param0, ...queryParams } = params;
  return request<Types.ProductStorageOrderWithItemsVO>(`/api/product-storage/order/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {})
  });
}

/** 查询商品存储 查询商品存储记录，支持多种条件搜索和分页查询 POST /api/product-storage/query */
export async function postApiProductStorageQuery (body: Types.QueryProductStorageReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductStorageVO>('/api/product-storage/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 获取存酒统计信息 获取指定门店的存酒统计数据，支持两种统计类型：汇总统计(summary)和按商品维度统计(byProduct，默认) POST /api/product-storage/statistics */
export async function postApiProductStorageStatistics (body: Types.GetStorageStatisticsReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductStorageItemStatVO>('/api/product-storage/statistics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加商品提取记录 添加商品提取记录 POST /api/product-withdraw/add */
export async function postApiProductWithdrawAdd (body: Types.AddProductWithdrawReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductWithdrawVO>('/api/product-withdraw/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 批量添加商品提取记录 批量添加商品提取记录，支持一次性从多个存储记录中提取酒水 POST /api/product-withdraw/batch-add */
export async function postApiProductWithdrawBatchAdd (body: Types.BatchAddProductWithdrawReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductWithdrawVO[]>('/api/product-withdraw/batch-add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品提取记录 查询商品提取记录，支持多种条件搜索和分页查询 POST /api/product-withdraw/query */
export async function postApiProductWithdrawQuery (body: Types.QueryProductWithdrawReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductWithdrawVO>('/api/product-withdraw/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询客户可取商品列表 根据客户信息（手机号/姓名/ID）查询可取商品列表 POST /api/product-withdraw/query-withdrawable-items */
export async function postApiProductWithdrawQueryWithdrawableItems (body: Types.QueryWithdrawableItemsReqDto, options?: { [key: string]: any }) {
  return request<Types.WithdrawableItemsVO>('/api/product-withdraw/query-withdrawable-items', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 创建商品单位 创建商品单位（只能创建门店特定单位，不能创建系统内置单位） POST /api/productunit/add */
export async function postApiProductunitAdd (body: Types.AddProductUnitReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductUnitVO>('/api/productunit/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除商品单位 删除商品单位（只能删除门店特定单位，不能删除系统内置单位和全局单位） POST /api/productunit/delete */
export async function postApiProductunitOpenApiDelete (body: Types.DeleteProductUnitReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productunit/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品单位列表 查询商品单位列表（返回系统内置单位 + 门店的单位） POST /api/productunit/list */
export async function postApiProductunitList (body: Types.QueryProductUnitReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductUnitVO[]>('/api/productunit/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新商品单位 更新商品单位（只能修改门店特定单位，不能修改系统内置单位和全局单位） POST /api/productunit/update */
export async function postApiProductunitUpdate (body: Types.UpdateProductUnitReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductUnitVO>('/api/productunit/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加商品多买多送策略 添加商品多买多送策略 POST /api/productMultipleBuyFree/add */
export async function postApiProductMultipleBuyFreeAdd (body: Types.AddProductMultipleBuyFreeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductMultipleBuyFreeVO>('/api/productMultipleBuyFree/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除商品多买多送策略 删除商品多买多送策略 POST /api/productMultipleBuyFree/delete */
export async function postApiProductMultipleBuyFreeOpenApiDelete (body: Types.DeleteProductMultipleBuyFreeReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productMultipleBuyFree/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品多买多送策略列表 查询商品多买多送策略列表 POST /api/productMultipleBuyFree/list */
export async function postApiProductMultipleBuyFreeList (body: Types.QueryProductMultipleBuyFreeReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductMultipleBuyFreeVO>('/api/productMultipleBuyFree/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品多买多送策略 查询商品多买多送策略 POST /api/productMultipleBuyFree/query */
export async function postApiProductMultipleBuyFreeQuery (body: Types.QueryProductMultipleBuyFreeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductMultipleBuyFreeVO[]>('/api/productMultipleBuyFree/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新商品多买多送策略 更新商品多买多送策略 POST /api/productMultipleBuyFree/update */
export async function postApiProductMultipleBuyFreeUpdate (body: Types.UpdateProductMultipleBuyFreeReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductMultipleBuyFreeVO>('/api/productMultipleBuyFree/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加商品收入报表 添加商品收入报表 POST /api/productIncomeReport/add */
export async function postApiProductIncomeReportAdd (body: Types.AddProductIncomeReportReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductIncomeReportVO>('/api/productIncomeReport/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除商品收入报表 删除商品收入报表 POST /api/productIncomeReport/delete */
export async function postApiProductIncomeReportOpenApiDelete (body: Types.DeleteProductIncomeReportReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productIncomeReport/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品收入报表列表 查询商品收入报表列表 POST /api/productIncomeReport/list */
export async function postApiProductIncomeReportList (body: Types.QueryProductIncomeReportReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductIncomeReportVO[]>('/api/productIncomeReport/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品收入报表 查询商品收入报表 POST /api/productIncomeReport/query */
export async function postApiProductIncomeReportQuery (body: Types.QueryProductIncomeReportReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductIncomeReportVO[]>('/api/productIncomeReport/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新商品收入报表 更新商品收入报表 POST /api/productIncomeReport/update */
export async function postApiProductIncomeReportUpdate (body: Types.UpdateProductIncomeReportReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductIncomeReportVO>('/api/productIncomeReport/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加商品展示分类 添加商品展示分类 POST /api/productDisplayCategory/add */
export async function postApiProductDisplayCategoryAdd (body: Types.AddProductDisplayCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductDisplayCategoryVO>('/api/productDisplayCategory/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除商品展示分类 删除商品展示分类 POST /api/productDisplayCategory/delete */
export async function postApiProductDisplayCategoryOpenApiDelete (body: Types.DeleteProductDisplayCategoryReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/productDisplayCategory/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品展示分类列表 查询商品展示分类列表 POST /api/productDisplayCategory/list */
export async function postApiProductDisplayCategoryList (body: Types.QueryProductDisplayCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductDisplayCategoryVO>('/api/productDisplayCategory/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询商品展示分类 查询商品展示分类 POST /api/productDisplayCategory/query */
export async function postApiProductDisplayCategoryQuery (body: Types.QueryProductDisplayCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductDisplayCategoryVO[]>('/api/productDisplayCategory/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新商品展示分类 更新商品展示分类 POST /api/productDisplayCategory/update */
export async function postApiProductDisplayCategoryUpdate (body: Types.UpdateProductDisplayCategoryReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductDisplayCategoryVO>('/api/productDisplayCategory/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 添加生产订单计划 添加生产订单计划 POST /api/production-order-plan/add */
export async function postApiProductionOrderPlanAdd (body: Types.AddProductionOrderPlanReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductionOrderPlanVO>('/api/production-order-plan/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除生产订单计划 删除生产订单计划 POST /api/production-order-plan/delete */
export async function postApiProductionOrderPlanOpenApiDelete (body: Types.DeleteProductionOrderPlanReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/production-order-plan/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询生产订单计划列表 查询生产订单计划列表 POST /api/production-order-plan/list */
export async function postApiProductionOrderPlanList (body: Types.QueryProductionOrderPlanReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoProductionOrderPlanVO>('/api/production-order-plan/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询生产订单计划 查询生产订单计划 POST /api/production-order-plan/query */
export async function postApiProductionOrderPlanQuery (body: Types.QueryProductionOrderPlanReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductionOrderPlanVO[]>('/api/production-order-plan/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新生产订单计划 更新生产订单计划 POST /api/production-order-plan/update */
export async function postApiProductionOrderPlanUpdate (body: Types.UpdateProductionOrderPlanReqDto, options?: { [key: string]: any }) {
  return request<Types.ProductionOrderPlanVO>('/api/production-order-plan/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
