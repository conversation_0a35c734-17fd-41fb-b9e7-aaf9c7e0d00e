import request from "@/utils/request";
import * as Types from "../shared/types";

/** 掌柜系统手机号密码登录 掌柜系统手机号密码登录 POST /manager/api/login/phone */
export async function postManagerApiLoginPhone (body: Types.ManagerPhonePasswordLoginReqDto, options?: { [key: string]: any }) {
  return request<Types.LoginResultVO>('/manager/api/login/phone', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
