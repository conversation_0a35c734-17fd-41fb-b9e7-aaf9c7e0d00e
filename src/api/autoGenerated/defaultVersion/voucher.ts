import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加凭证 添加凭证 POST /api/voucher/add */
export async function postApiVoucherAdd (body: Types.AddVoucherReqDto, options?: { [key: string]: any }) {
  return request<Types.VoucherVO>('/api/voucher/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除凭证 删除凭证 POST /api/voucher/delete */
export async function postApiVoucherOpenApiDelete (body: Types.DeleteVoucherReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/voucher/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询凭证列表 查询凭证列表 POST /api/voucher/list */
export async function postApiVoucherList (body: Types.QueryVoucherReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoVoucherVO>('/api/voucher/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询凭证 查询凭证 POST /api/voucher/query */
export async function postApiVoucherQuery (body: Types.QueryVoucherReqDto, options?: { [key: string]: any }) {
  return request<Types.VoucherVO[]>('/api/voucher/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新凭证 更新凭证 POST /api/voucher/update */
export async function postApiVoucherUpdate (body: Types.UpdateVoucherReqDto, options?: { [key: string]: any }) {
  return request<Types.VoucherVO>('/api/voucher/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
