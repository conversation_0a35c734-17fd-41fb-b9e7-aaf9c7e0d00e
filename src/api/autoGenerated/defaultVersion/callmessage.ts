import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加呼叫消息 添加呼叫消息 POST /api/call-message/add */
export async function postApiCallMessageAdd (body: Types.AddCallMessageReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO>('/api/call-message/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除呼叫消息 删除呼叫消息 POST /api/call-message/delete */
export async function postApiCallMessageOpenApiDelete (body: Types.DeleteCallMessageReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/call-message/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询呼叫消息列表 查询呼叫消息列表 POST /api/call-message/list */
export async function postApiCallMessageList (body: Types.QueryCallMessageReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoCallMessageVO>('/api/call-message/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询呼叫消息 查询呼叫消息 POST /api/call-message/query */
export async function postApiCallMessageQuery (body: Types.QueryCallMessageReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO[]>('/api/call-message/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新呼叫消息 更新呼叫消息 POST /api/call-message/update */
export async function postApiCallMessageUpdate (body: Types.UpdateCallMessageReqDto, options?: { [key: string]: any }) {
  return request<Types.CallMessageVO>('/api/call-message/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
