import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加仓库 添加仓库 POST /api/warehouse/add */
export async function postApiWarehouseAdd (body: Types.AddWarehouseReqDto, options?: { [key: string]: any }) {
  return request<Types.WarehouseVO>('/api/warehouse/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除仓库 删除仓库 POST /api/warehouse/delete */
export async function postApiWarehouseOpenApiDelete (body: Types.DeleteWarehouseReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/warehouse/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询仓库列表 查询仓库列表 POST /api/warehouse/list */
export async function postApiWarehouseList (body: Types.QueryWarehouseReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoWarehouseVO>('/api/warehouse/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询仓库 查询仓库 POST /api/warehouse/query */
export async function postApiWarehouseQuery (body: Types.QueryWarehouseReqDto, options?: { [key: string]: any }) {
  return request<Types.WarehouseVO[]>('/api/warehouse/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新仓库 更新仓库 POST /api/warehouse/update */
export async function postApiWarehouseUpdate (body: Types.UpdateWarehouseReqDto, options?: { [key: string]: any }) {
  return request<Types.WarehouseVO>('/api/warehouse/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
