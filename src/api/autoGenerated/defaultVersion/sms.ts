import request from "@/utils/request";
import * as Types from "../shared/types";

/** 发送短信验证码 发送短信验证码（测试接口） POST /api/sms/send-code */
export async function postApiSmsSendCode (body: Types.SendSmsCodeReqDto, options?: { [key: string]: any }) {
  return request<Types.SendSmsCodeVO>('/api/sms/send-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
