import request from "@/utils/request";
import * as Types from "../shared/types";

/** 添加预订 添加预订 POST /api/booking/add */
export async function postApiBookingAdd (body: Types.AddBookingReqDto, options?: { [key: string]: any }) {
  return request<Types.BookingVO>('/api/booking/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 取消预订 取消预订 POST /api/booking/cancel */
export async function postApiBookingCancel (body: Types.CancelBookingReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/booking/cancel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 删除预订 删除预订 POST /api/booking/delete */
export async function postApiBookingOpenApiDelete (body: Types.DeleteBookingReqDto, options?: { [key: string]: any }) {
  return request<any>('/api/booking/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询预订列表 查询预订列表 POST /api/booking/list */
export async function postApiBookingList (body: Types.QueryBookingReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoBookingVO>('/api/booking/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 搜索预订 搜索预订 POST /api/booking/ls */
export async function postApiBookingLs (body: Types.SearchBookingReqDto, options?: { [key: string]: any }) {
  return request<Types.PageVOArrayVoBookingVO>('/api/booking/ls', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 查询预订 查询预订 POST /api/booking/query */
export async function postApiBookingQuery (body: Types.QueryBookingReqDto, options?: { [key: string]: any }) {
  return request<Types.BookingVO[]>('/api/booking/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

/** 更新预订 更新预订 POST /api/booking/update */
export async function postApiBookingUpdate (body: Types.UpdateBookingReqDto, options?: { [key: string]: any }) {
  return request<Types.BookingVO>('/api/booking/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
