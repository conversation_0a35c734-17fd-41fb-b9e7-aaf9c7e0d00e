import { computed, reactive, ref, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { ILoginViewModel } from './viewmodel';
import { LoginConverter } from './converter';
import { useLoginInteractor } from './interactor';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
// 定义venue类型，确保类型安全
interface VenueInfo {
  id: string;
  code?: string;
  name?: string;
  [key: string]: any;
}

export class LoginPresenter implements ILoginViewModel {
  private router = useRouter();
  private interactor = useLoginInteractor();
  private deviceStore = useDeviceStore();
  private venueStore = useVenueStore();
  private qrcodeTimer: NodeJS.Timeout | null = null;

  // 状态管理
  public state = reactive(LoginConverter.createInitialState());

  // UI引用
  public refs = {
    loginFormRef: undefined as FormInstance | undefined
  };

  // 表单验证规则
  public loginRules = reactive(LoginConverter.createValidationRules());

  // 计算属性
  public computed = {
    isFormValid: computed(() => {
      const phonePattern = /^1[3-9]\d{9}$/;
      return Boolean(
        this.state.loginForm.phone &&
          this.state.loginForm.password &&
          phonePattern.test(this.state.loginForm.phone) &&
          this.state.loginForm.password.length >= 6
      );
    })
  };

  constructor() {
    this.setupLifecycles();
    this.setupWatchers();
  }

  // 生命周期和监听设置
  private setupLifecycles() {
    onMounted(async () => {
      console.log('login onMounted: ', this.venueStore.venueId, this.deviceStore.macAddress, this.deviceStore.ipAddress);

      // 初始化设备信息到视图状态
      this.initDeviceInfo();

      await this.checkCashierMachineBinding();
    });

    onUnmounted(() => {
      this.cleanupResources();
    });
  }

  // 初始化设备信息
  private initDeviceInfo() {
    // 将设备信息从store转换到视图状态
    LoginConverter.updateDeviceInfo(this.state, {
      venue: this.venueStore.venue,
      ipAddress: this.deviceStore.ipAddress
    });
  }

  private setupWatchers() {
    // 监听记住密码变化
    watch(
      () => this.state.rememberPassword,
      newVal => {
        if (!newVal) {
          LoginConverter.saveRememberPassword(this.state);
        }
      }
    );

    // 监听venue变化，更新设备信息
    watch(
      () => this.deviceStore.venue,
      () => {
        this.initDeviceInfo();
      }
    );
  }

  // 业务方法
  private async checkCashierMachineBinding() {
    try {
      const res = await this.interactor.queryCashierMachineBindInfo(this.deviceStore.macAddress);

      if (res.data) {
        // 假设API返回的数据结构中有这些字段
        // 如果API的实际返回类型与此不符，需要调整
        const bindInfo = res.data;
        const isBind = bindInfo && 'isBind' in bindInfo ? bindInfo.isBind : false;
        const venue = bindInfo && 'venue' in bindInfo ? (bindInfo.venue as VenueInfo) : null;
        const cashierMachine = bindInfo && 'cashierMachine' in bindInfo ? bindInfo.cashierMachine : null;

        if (isBind && venue && venue.id) {
          // 收银机已绑定且有门店信息
          this.venueStore.setVenue(venue);

          if (cashierMachine) {
            this.deviceStore.setCashierMachine(cashierMachine);
          }

          // 设置门店ID
          this.venueStore.setVenueId(venue.id);

          // 更新视图状态中的设备信息
          this.initDeviceInfo();
        } else {
          // 收银机未绑定或没有门店信息
          ElMessage.error('收银机未绑定，请先绑定收银机');
          this.router.replace('/auth');
        }
      } else {
        // 没有查询到收银机绑定信息
        ElMessage.error('未查询到收银机绑定信息，请检查设备');
        this.router.replace('/auth');
      }
    } catch (error: any) {
      ElMessage.error('查询收银机绑定信息失败:' + error.message);
      this.router.replace('/auth');
    }
  }

  private async getWxLoginUrl() {
    try {
      this.state.qrcodeUrl = '';
      this.state.qrcodeExpired = false;
      this.state.qrcodeScanned = false;

      // 调用二维码获取接口
      const res = await this.interactor.getWxLoginQrcode({
        venue_id: this.venueStore.venueId,
        mac: this.deviceStore.macAddress
      });

      if (!res.data) {
        throw new Error('获取二维码失败: 无返回数据');
      }

      // 保存二维码信息
      this.state.qrcodeUrl = res.data.qr_code;
      this.state.sceneStr = res.data.scene_str;
      this.state.loginKey = res.data.login_key;

      // 订阅NATS消息
      await this.subscribeLoginStatus();

      // 开始轮询检查状态(作为备用机制)
      this.startQrcodeCheck();
    } catch (error) {
      console.error('获取微信登录二维码失败:', error);
      ElMessage.error('获取二维码失败，请重试');
    }
  }

  private async subscribeLoginStatus() {
    try {
      await this.interactor.connectNats();

      // 订阅主题
      await this.interactor.subscribeNats(`${this.state.sceneStr}`, async data => {
        console.log('收到长连接消息:', data);

        // 判断消息类型
        if (data.type !== 'qr_login_status') {
          console.warn('未知的消息类型:', data.type);
          return;
        }

        const statusData = data.data;
        // 验证场景值
        if (statusData.scene_str !== this.state.sceneStr) {
          console.error('场景值不匹配');
          return;
        }

        switch (statusData.status) {
          case 'EXPIRED':
            this.state.qrcodeExpired = true;
            if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
            break;

          case 'SCANNED':
            this.state.qrcodeScanned = true;
            break;

          case 'CONFIRMED':
            try {
              if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
              // 调用自动登录接口
              const loginResult = await this.interactor.qrAutoLogin({
                login_key: this.state.loginKey,
                user_id: statusData.user_id,
                scene_str: statusData.scene_str
              });
              await this.handleQrLoginSuccess(loginResult);
            } catch (error) {
              console.error('自动登录失败:', error);
              ElMessage.error('登录失败，请重试');
            }
            break;
        }
      });
    } catch (error) {
      console.error('订阅NATS消息失败:', error);
      this.startQrcodeCheck();
    }
  }

  private startQrcodeCheck() {
    if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);

    this.qrcodeTimer = setInterval(async () => {
      try {
        if (!this.state.sceneStr || !this.state.loginKey) return;

        const res = await this.interactor.checkWxLoginStatus({
          scene_str: this.state.sceneStr,
          login_key: this.state.loginKey
        });

        switch (res.data.status) {
          case 'EXPIRED':
            this.state.qrcodeExpired = true;
            if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
            break;
          case 'SCANNED':
            this.state.qrcodeScanned = true;
            break;
          case 'CONFIRMED':
            if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
            const loginResult = await this.interactor.qrAutoLogin({
              login_key: this.state.loginKey,
              user_id: res.data.user_id,
              scene_str: res.data.scene_str
            });
            await this.handleQrLoginSuccess(loginResult);
            break;
        }
      } catch (error) {
        console.error('检查二维码状态失败:', error);
        if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
      }
    }, 3000);
  }

  private async handleQrLoginSuccess(loginResult: any) {
    const success = await this.interactor.handleLoginSuccess(loginResult);
    if (success) {
      this.router.push('/');
    }
  }

  private cleanupResources() {
    if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
    // 取消NATS订阅
    if (this.state.sceneStr) {
      this.interactor.unsubscribeNats(`qr_login.${this.state.sceneStr}`).catch(console.error);
    }
  }

  // 公开操作
  public actions = {
    handleLogin: async () => {
      if (!this.refs.loginFormRef) return;

      await this.refs.loginFormRef.validate(async (valid: boolean) => {
        if (valid) {
          try {
            this.state.loading = true;
            const loginParams = {
              phone: this.state.loginForm.phone,
              password: this.state.loginForm.password,
              mac: this.deviceStore.macAddress
            };
            const res = await this.interactor.phoneLogin(loginParams);

            // 处理记住密码
            LoginConverter.saveRememberPassword(this.state);

            await this.interactor.handleLoginSuccess(res);
            this.router.push('/');
          } catch (error) {
            console.error(error);
            ElMessage.error('登录失败:' + error);
          } finally {
            this.state.loading = false;
          }
        }
      });
    },

    toggleLoginType: () => {
      this.state.loginType = this.state.loginType === 'password' ? 'qrcode' : 'password';
      if (this.state.loginType === 'qrcode') {
        this.getWxLoginUrl();
      } else {
        // 清理定时器和订阅
        if (this.qrcodeTimer) clearInterval(this.qrcodeTimer);
        if (this.state.sceneStr) {
          this.interactor.unsubscribeNats(`qr_login.${this.state.sceneStr}`).catch(console.error);
        }
      }
    },

    refreshQrcode: () => {
      this.getWxLoginUrl();
    }
  };
}

export function useLoginPresenter(): ILoginViewModel {
  return new LoginPresenter();
}
