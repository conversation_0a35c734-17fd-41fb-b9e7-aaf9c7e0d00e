import { ComputedRef, Ref } from 'vue'
import { FormInstance, FormRules } from 'element-plus'

// 登录表单状态
export interface ILoginFormState {
  phone: string
  password: string
}

// 登录状态
export interface ILoginState {
  loginForm: ILoginFormState
  loginType: 'password' | 'qrcode'
  loading: boolean
  rememberPassword: boolean
  qrcodeUrl: string
  qrcodeExpired: boolean
  qrcodeScanned: boolean
  sceneStr: string
  loginKey: string
  // 设备信息状态
  venueCode: string
  venueName: string
  ipAddress: string
}

// 计算属性
export interface ILoginComputed {
  isFormValid: ComputedRef<boolean>
}

// UI引用
export interface ILoginRefs {
  loginFormRef: FormInstance | undefined
}

// 操作方法
export interface ILoginActions {
  handleLogin: () => Promise<void>
  toggleLoginType: () => void
  refreshQrcode: () => void
}

// 组合接口
export interface ILoginViewModel {
  state: ILoginState
  computed: ILoginComputed
  refs: ILoginRefs
  actions: ILoginActions
  loginRules: FormRules
}