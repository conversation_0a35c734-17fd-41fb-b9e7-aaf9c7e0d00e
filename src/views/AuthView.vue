<template>
  <div class="relative min-h-screen w-full overflow-hidden bg-[#F5F5F5]">
    <!-- 背景图片 - 调整层级和样式 -->
    <div class="absolute inset-0 z-0">
      <img src="@/assets/v2/login/bg.webp" alt="背景" class="w-full h-full object-cover opacity-50" style="transform: scaleX(-1)" />
    </div>

    <!-- 授权检查中的遮罩 -->
    <div v-if="authChecking" class="fixed inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center flex-col">
      <el-icon class="animate-spin text-[#000] text-4xl mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 text-lg">正在检查授权状态...</p>
    </div>

    <!-- 顶部IP显示 -->
    <div class="bg-[#B37A51] text-white px-4 py-2 rounded-br-lg absolute top-0 left-0 z-10">当前IP：{{ ipAddress }}</div>

    <!-- 主要内容区域 - 添加z-index确保在背景之上 -->
    <div class="relative z-10 flex-1 flex flex-col items-center justify-center p-8 w-full h-screen">
      <div class="text-center">
        <span class="text-[60px] font-[400]">欢迎使用</span>
        <span class="text-[60px] font-[600] text-[#B37A51] ml-[20px]">汇金Pro</span>
        <span class="text-[60px] font-[400] ml-[20px]">收银系统</span>
      </div>
      <div class="text-center">
        <span class="text-[32px] text-[#000] mt-[28px]">先完成门店创建，再绑定此收银机</span>
      </div>
      <!-- 步骤说明 -->
      <div class="mt-[52px]">
        <div class="flex flex-col items-center text-center rounded-lg shadow-md w-[330px] h-[330px] bg-white items-center justify-center">
          <div class="">
            <div v-if="loading" class="inset-0 flex items-center justify-center bg-white bg-opacity-75">
              <el-icon class="animate-spin text-blue-500 text-xl">
                <Loading />
              </el-icon>
            </div>
            <img v-if="qrCodeData" :src="qrCodeData" alt="QR Code" class="w-[252px] h-[252px] mx-auto" />
            <div v-else class="w-[252px] h-[252px] mx-auto flex items-center justify-center text-gray-400">加载中...</div>
          </div>
          <p class="text-gray-600">打开微信扫一扫</p>
        </div>
      </div>

      <!-- 登录按钮 -->
      <el-button
        type="primary"
        class="mt-[52px] w-[400px] h-[80px] text-[24px] text-[#fff]"
        @click="handleLogin"
        :loading="loginLoading"
        :disabled="!qrCodeData">
        绑定成功,我要营业
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Document, Check, Loading } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { authApi } from '@/api/auth';
import { useUserStore } from '@/stores/userStore';
import { useDeviceStore } from '@/stores/deviceStore';
import { useVenueStore } from '@/stores/venueStore';
import { postApiVenueShouyinAuth } from '@/api/autoGenerated';

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const loginLoading = ref(false);
const authChecking = ref(true);

const userStore = useUserStore();
const deviceStore = useDeviceStore();
const venueStore = useVenueStore();

const qrCodeData = ref('');

// 通过计算属性获取ipAddress
const ipAddress = computed(() => deviceStore.ipAddress);

// 组件名称
const componentName = 'AuthView';

// 检查授权状态
const checkAuth = async () => {
  try {
    authChecking.value = true;
    console.log('checkAuth: ', deviceStore.ipAddress, deviceStore.macAddress);
    const deviceInfo = deviceStore.getDeviceInfo();
    const res = await postApiVenueShouyinAuth({
      grantIp: deviceStore.ipAddress,
      mac: deviceStore.macAddress,
      clientType: 'client',
      deviceType: deviceInfo.deviceType
    });

    console.log('checkAuth res.data: ', res.data);

    if (res.data?.venueId) {
      // 检查IP是否发生变化
      if (res.data.lastIP && res.data.cashierMachine?.grantIp && res.data.lastIP !== res.data.cashierMachine.grantIp) {
        // IP发生变化，询问用户是否继续
        const confirmed = await ElMessageBox.confirm(
          `检测到IP地址已变更\n原IP: ${res.data.lastIP}\n当前IP: ${res.data.cashierMachine.grantIp}\n是否确认使用当前IP继续？`,
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).catch(() => false);

        if (!confirmed) {
          ElMessage.info('操作已取消');
          return false;
        }
      }

      // 保存门店ID和门店信息
      venueStore.setVenueId(res.data.venueId);

      // 保存收银机信息
      if (res.data.cashierMachine) {
        deviceStore.setCashierMachine(res.data.cashierMachine);
      }

      // 保存门店信息
      if (res.data.venue) {
        venueStore.setVenue(res.data.venue);
      }
      console.log('checkAuth res.data.venue: ', res.data.venue);
      // 已授权，检查是否已登录
      if (userStore.isLoggedIn()) {
        console.log('checkAuth userStore.isLoggedIn() true');
        router.push('/room/realtimetable');
      } else {
        console.log('checkAuth userStore.isLoggedIn() false');
        router.push('/login');
      }
      return true;
    } else if (res.data?.qrCodeUrl) {
      // 直接使用返回的base64图片数据
      loading.value = true;
      try {
        qrCodeData.value = res.data.qrCodeUrl;
      } catch (error) {
        console.error('设置二维码失败:', error);
        ElMessage.error('加载二维码失败，请刷新重试');
      } finally {
        loading.value = false;
      }
    }
    return false;
  } catch (error) {
    console.error('检查授权失败:', error);
    ElMessage.error('检查授权失败');
    return false;
  } finally {
    authChecking.value = false;
  }
};

// 处理登录
const handleLogin = async () => {
  loginLoading.value = true;
  try {
    // 直接跳转到登录页面
    router.push('/login');
  } catch (error) {
    console.error('跳转失败:', error);
    ElMessage.error('跳转失败，请重试');
  } finally {
    loginLoading.value = false;
  }
};

// 页面加载完成后检查授权
setTimeout(async () => {
  console.log('AuthView mounted deviceStore: ', deviceStore);
  await checkAuth();
}, 0);

// 页面卸载前清理资源
const beforeUnload = () => {
  qrCodeData.value = '';
};
window.addEventListener('beforeunload', beforeUnload);
</script>
