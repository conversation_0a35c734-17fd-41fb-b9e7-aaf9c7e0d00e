<template>
  <div class="flex flex-col h-screen">
    <!-- 顶部导航栏 -->
    <div class="bg-[#F5F5F5] border-b min-h-[60px] flex items-center justify-between">
      <!-- Logo -->
      <div class="flex-shrink-0 flex items-center">
        <img src="@/assets/logo.svg" alt="logo" class="w-[120px] h-[40px]" />
      </div>

      <!-- 主要功能模块 - 添加最大宽度和居中 -->
      <div class="flex-1 flex">
        <div class="flex items-center max-w-[960px] w-full">
          <div
            v-for="tab in tabs"
            :key="tab.path"
            @click="handleTabClick(tab)"
            class="flex-1 flex flex-col items-center justify-center h-[60px] cursor-pointer hover:bg-white"
            :class="{
              'bg-white text-[#409EFF] border-b-2 border-[#409EFF]': isActiveTab(tab),
              'text-[#606266]': !isActiveTab(tab)
            }">
            <el-icon class="text-[20px]">
              <component :is="tab.icon" />
            </el-icon>
            <span class="mt-1 text-sm">{{ tab.name }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧工具栏 - 添加 flex-shrink-0 确保不被压缩 -->
      <div class="flex-shrink-0 flex items-center px-4 space-x-6 text-[#606266]">
        <PrinterIcon />
        <el-icon class="text-[20px] cursor-pointer hover:text-[#409EFF]">
          <Bell />
        </el-icon>
        <el-icon class="text-[20px] cursor-pointer hover:text-[#409EFF]" @click="handleSettingClick">
          <Setting />
        </el-icon>
        <el-icon class="text-[20px] cursor-pointer hover:text-[#409EFF]">
          <Cloudy />
        </el-icon>

        <!-- 用户信息 -->
        <el-dropdown trigger="click">
          <div class="flex items-center cursor-pointer">
            <el-avatar :size="32" :icon="User" class="bg-gray-200" />
            <span class="ml-2">{{ displayName }}</span>
            <el-icon class="ml-1">
              <ArrowDown />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>个人信息</el-dropdown-item>
              <el-dropdown-item @click="handleCommand('logout')">退出登录</el-dropdown-item>
              <el-dropdown-item @click="handleCommand('settings')">设置</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="flex-grow overflow-auto w-full flex flex-col">
      <BreadcrumbNav v-if="showNav" />
      <div class="flex-1 overflow-auto">
        <router-view v-slot="{ Component }">
          <keep-alive :include="cachedViews">
            <component :is="Component" :key="Component?.type.name" v-if="Component" />
          </keep-alive>
        </router-view>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <footer class="bg-[#F5F5F5] p-2 text-sm text-[#606266]">
      <div class="flex justify-between items-center">
        <!-- 左侧时钟显示 -->
        <div class="flex items-center space-x-2">
          <el-icon>
            <Timer />
          </el-icon>
          <span>{{ timeStore.formattedTime }}</span>
          <ConnectionStatus />
        </div>
        <!-- 右侧版本信息 -->
        <span>雷石ERP量贩版 v5.71.1</span>
      </div>
    </footer>

    <!-- 初始化设置组件 -->
    <InitSettings v-model="showInitSettings" @confirmed="handleInitSettingsConfirmed" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useCache } from '@/stores/routerCacheStore';
import ConnectionStatus from '@/components/customer/KeepLiveStatus.vue';
import BreadcrumbNav from '@/components/customer/BreadcrumbNav.vue';
import PrinterIcon from '@/components/customer/PrinterIcon.vue';
import { clearAuthInfo } from '@/utils/authUtils';
import { useVenueStore } from '@/stores/venueStore';
import {
  Menu,
  List,
  Switch,
  GobletFull,
  User,
  CircleCheck,
  Money,
  Box,
  KnifeFork,
  Bell,
  Setting,
  Cloudy,
  Printer,
  ArrowDown,
  Timer
} from '@element-plus/icons-vue';
import InitSettings from '@/components/system/InitSettings.vue';
import { useTimeStore } from '@/stores/timeStore';
import { useUserStore } from '@/stores/userStore';

defineOptions({
  name: 'HomeView'
});

const router = useRouter();
const route = useRoute();
const { cachedViews, updateCache } = useCache();
const userStore = useUserStore();
const venueStore = useVenueStore();
// 初始化时间存储
const timeStore = useTimeStore();

const tabs = [
  { name: '开台', icon: Menu, path: 'table' },
  { name: '点单', icon: List, path: 'production' },
  { name: '交班', icon: Switch, path: 'shift' },
  { name: '存酒', icon: GobletFull, path: 'wine' },
  { name: '会员', icon: User, path: 'member' },
  // { name: '核销', icon: CircleCheck, path: 'verification' },
  { name: '财务', icon: Money, path: 'finance' },
  { name: '库管', icon: Box, path: 'inventory' }
  // { name: '出品', icon: Printer, path: 'print' }
];

const activeTab = computed(() => {
  return tabs.find(tab => route.path.startsWith(tab.path))?.name || '';
});

const currentTime = ref(new Date().toLocaleString());

const handleTabClick = (tab: (typeof tabs)[0]) => {
  // 根据不同的tab.path设置默认子路由
  let targetPath = `/${tab.path}`;
  switch (tab.path) {
    case 'table':
      targetPath = '/room/table'; // 默认展示实时包厢
      break;
    case 'production':
      targetPath = '/production/index'; // 默认展示商品管理
      break;
    case 'shift':
      targetPath = '/shift/handover'; // 默认展示交班页面
      break;
    case 'wine':
      targetPath = '/wine/index'; // 默认展示存酒管理
      break;
    case 'member':
      targetPath = '/member/index'; // 默认展示会员管理
      break;
    case 'finance':
      targetPath = '/finance/report'; // 默认展示财务报表
      break;
    case 'inventory':
      targetPath = '/inventory/index'; // 默认展示库存报表
      break;
    default:
      targetPath = `/${tab.path}`;
  }
  console.log('targetPath', targetPath);
  // 使用 router.push 替代浏览器默认导航
  if (route.path !== targetPath) {
    router.push({
      path: targetPath,
      replace: true // 使用 replace 来替换当前历史记录，避免返回按钮堆积
    });
  }
};

// 监听完整的路由路径
watch(
  () => route.fullPath,
  () => {
    console.log('keepalive Route changed:', route.fullPath);
    updateCache();
  },
  { immediate: true }
);

onMounted(() => {
  console.log('keepalive HomeView mounted');
  updateCache();

  // 检查是否存在 venueId
  const venueId = venueStore.venueId;
  if (!venueId) {
    showInitSettings.value = true;
    return;
  }

  // 修改路由判断逻辑
  if (route.path === '/') {
    const { deviceId, macAddress } = route.query;
    if (deviceId && macAddress) {
      // 如果有设备参数，跳转到认证页面
      router.push({
        path: '/auth',
        query: route.query
      });
    } else {
      // 没有设备参数，跳转到默认页面
      router.push('/room/table');
    }
  }

  // 更新时间
  setInterval(() => {
    currentTime.value = new Date().toLocaleString();
  }, 1000);

  // 在组件挂载时启动时钟
  timeStore.startTimer();
});

onUnmounted(() => {
  // 不需要在这里定义 timer 和清理逻辑，因为已经在 store 中处理了
});

const isActiveTab = (tab: (typeof tabs)[0]) => {
  // 处理特殊路由映射
  const pathMapping: Record<string, string[]> = {
    table: ['room'], // room 相关的路由都属于 table 模块
    order: ['order', 'product'] // product 和 order 相关的路由都属于 order 模块
  };

  // 获取当前路由的第一段路径
  const currentPath = route.path.split('/')[1];

  // 检查是否有特殊映射
  if (pathMapping[tab.path]) {
    return pathMapping[tab.path].some(path => currentPath === path);
  }

  // 默认匹配逻辑
  return currentPath === tab.path;
};

const showNav = computed(() => {
  // 1. 优先检查 query 参数中是否指定了 showNav
  if (route.query.showNav === 'true') {
    return true;
  }

  // 2. 如果 query 中没有指定，则检查 meta 中的 hideNav
  if (route.meta.showNav) {
    return true;
  }

  // 3. 默认显示
  return false;
});

// 初始化设置相关
const showInitSettings = ref(false);

// 处理初始化设置确认
const handleInitSettingsConfirmed = (settings: any) => {
  console.log('初始化设置已确认:', settings);
  // 重新加载页面以应用新的设置
  window.location.reload();
};

// 在用户信息下拉菜单中添加修改场馆ID的选项
const handleCommand = (command: string) => {
  switch (command) {
    case 'settings':
      router.push('/settings');
      break;
    case 'logout':
      // 清除用户相关信息
      clearAuthInfo();
      // 跳转到登录页
      router.push('/login');
      break;
  }
};

// 添加设置图标点击处理函数
const handleSettingClick = () => {
  router.push('/settings');
};

// 添加用户名显示的计算属性
const displayName = computed(() => {
  return userStore.userInfo.employee?.name || '未登录';
});
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

/* 可以添加一些时钟样式 */
.el-icon {
  font-size: 16px;
  margin-right: 4px;
}

.overflow-auto {
  -webkit-overflow-scrolling: touch;
  /* 流畅滚动 */
  overscroll-behavior: contain;
  /* 防止滚动传递 */
}
</style>
