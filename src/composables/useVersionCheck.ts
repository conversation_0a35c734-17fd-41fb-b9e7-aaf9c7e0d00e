import { ref, onMounted, onUnmounted, computed } from 'vue';
import axios from 'axios'; // 假设你使用 axios

interface VersionCheckOptions {
  /** 检查新版本的轮询间隔时间（毫秒），默认为 30 分钟 */
  checkInterval?: number;
  /** 检测到新版本时的回调函数 */
  onUpdateDetected?: (currentVersion: string, newVersion: string, currentTimestamp?: string, newTimestamp?: string) => void;
  /** 版本文件的 URL，默认为 /version.json */
  versionFileUrl?: string;
  /** 是否在开发环境也启用检查，默认为 false */
  checkInDev?: boolean;
}

// 假设 version.json 的结构是 { "version": "xxx", "releaseTimestamp": "xxx" }
interface VersionData {
  version: string;
  releaseTimestamp?: string;
}

// 在构建时注入的版本号 (需要构建工具配合)
// 读取环境变量 VITE_APP_VERSION
const initialVersion = import.meta.env.VITE_APP_VERSION || 'unknown';
const initialReleaseTimestamp = import.meta.env.VITE_APP_RELEASE_TIMESTAMP || '';

export function useVersionCheck(options: VersionCheckOptions = {}) {
  const {
    checkInterval = 30 * 60 * 1000, // 默认 30 分钟
    onUpdateDetected = () => {},
    versionFileUrl = '/version.json',
    checkInDev = false
  } = options;

  const isUpdateDetected = ref(false);
  const latestVersionData = ref<VersionData | null>(null);
  let intervalId: number | null = null;

  async function checkVersion() {
    if (!checkInDev && import.meta.env.DEV) {
      // 开发环境默认不检查
      console.log('[Version Check] Skipping check in development mode.');
      return;
    }

    try {
      // 添加时间戳防止浏览器缓存
      const response = await axios.get<VersionData>(`${versionFileUrl}?t=${Date.now()}`);
      const data = response.data;
      latestVersionData.value = data;

      console.log(
        `[Version Check] Initial: ${initialVersion} (${initialReleaseTimestamp || 'N/A'}), Latest: ${data.version} (${data.releaseTimestamp || 'N/A'})`
      );

      if (data.version && initialVersion !== 'unknown' && data.version !== initialVersion) {
        isUpdateDetected.value = true;
        if (intervalId) {
          clearInterval(intervalId); // 检测到更新后停止轮询
          intervalId = null;
          console.log('[Version Check] Update detected, polling stopped.');
        }
        onUpdateDetected(initialVersion, data.version, initialReleaseTimestamp, data.releaseTimestamp);
      } else if (data.version && data.version === initialVersion) {
        console.log('[Version Check] Version is up to date.');
      } else {
        console.log('[Version Check] Unable to compare versions or versions match.');
      }
    } catch (error) {
      console.error('[Version Check] Error checking app version:', error);
      // 可以选择在这里也停止轮询，或者忽略单次错误继续检查
      // clearInterval(intervalId);
      // intervalId = null;
    }
  }

  onMounted(() => {
    // 首次延迟检查，避免影响应用启动性能
    setTimeout(checkVersion, 5000); // 延迟 5 秒检查

    // 设置定时检查
    if (!checkInDev && import.meta.env.DEV) return;

    if (intervalId) clearInterval(intervalId); // 清除旧的 interval 以防万一
    intervalId = window.setInterval(checkVersion, checkInterval);
    console.log(`[Version Check] Started polling every ${checkInterval / 1000} seconds.`);
  });

  onUnmounted(() => {
    if (intervalId) {
      clearInterval(intervalId);
      console.log('[Version Check] Polling stopped on unmount.');
    }
  });

  // 使用 computed 属性暴露版本信息
  const currentVersion = computed(() => initialVersion);
  const currentReleaseTimestamp = computed(() => initialReleaseTimestamp);
  const latestVersion = computed(() => latestVersionData.value?.version);
  const latestReleaseTimestamp = computed(() => latestVersionData.value?.releaseTimestamp);

  return {
    isUpdateDetected,
    currentVersion,
    currentReleaseTimestamp,
    latestVersion,
    latestReleaseTimestamp
  };
}
