// nats-service.ts
import { connect, NatsConnection, Subscription, NatsError, Status, ConnectionOptions, Msg } from 'nats.ws';

// 连接状态类型
export type ConnectionStatus = 'connected' | 'disconnected' | 'error' | 'closed';

// 连接监听器类型
type ConnectionListener = (status: ConnectionStatus) => void;

// 消息处理器类型
type MessageHandler<T = any> = (data: T, msg: Msg) => void;

// 定义服务端返回的消息结构
interface NatsMessage<T = any> {
  method: string;
  route: string;
  data: string; // base64编码的数据
}

class NatsService {
  private static instance: NatsService | null = null;
  private nc: NatsConnection | null = null;
  private status: ConnectionStatus = 'disconnected';
  private lastPingTime: number | null = null;
  private heartbeatInterval: ReturnType<typeof setInterval> | null = null;
  private reconnectAttempts: number = 0;
  private readonly maxReconnectAttempts: number = 5;
  private readonly connectionListeners: Set<ConnectionListener> = new Set();
  private readonly subscriptions: Map<string, Subscription> = new Map();

  private constructor() {}

  public static getInstance(): NatsService {
    if (!NatsService.instance) {
      NatsService.instance = new NatsService();
    }
    return NatsService.instance;
  }

  // 建立连接
  public async connect(options?: Partial<ConnectionOptions>): Promise<void> {
    try {
      // console.log('Connecting to NATS...');

      const defaultOptions: ConnectionOptions = {
        servers: import.meta.env.VITE_NATS_SERVER,
        token: import.meta.env.VITE_NATS_TOKEN,
        debug: false,
        pingInterval: 60000,
        timeout: 20000,
        reconnect: true,
        reconnectTimeWait: 5000,
        maxReconnectAttempts: -1
      };

      // console.log('Connection options:', {
      //   server: defaultOptions.servers,
      //   token: '***'
      // });

      this.nc = await connect(defaultOptions);

      // console.log('NATS connected successfully');
      this.updateStatus('connected');

      // 监听连接状态
      (async () => {
        if (!this.nc) return;
        for await (const status of this.nc.status()) {
          this.handleStatus(status);
        }
      })().catch((err: Error) => {
        console.error('Status listener error:', err);
      });
      // 监听连接关闭
      this.nc.closed().then((value: void | Error) => {
        this.handleClosed(value instanceof Error ? value : undefined);
      });
    } catch (err) {
      console.error('NATS connection error:', err);
      this.updateStatus('error');
      throw err;
    }
  }

  // 发送消息
  public async publish<T = any>(subject: string, data: T): Promise<void> {
    if (!this.nc) {
      throw new Error('NATS not connected');
    }

    try {
      const payload = typeof data === 'string' ? data : JSON.stringify(data);
      await this.nc.publish(subject, new TextEncoder().encode(payload));
    } catch (err) {
      console.error('Publish error:', err);
      throw err;
    }
  }

  // 订阅消息
  public async subscribe<T = any>(subject: string, callback: MessageHandler<T>): Promise<Subscription> {
    if (!this.nc) {
      throw new Error('NATS not connected');
    }

    try {
      await this.unsubscribe(subject);
      const sub = this.nc.subscribe(subject);
      this.subscriptions.set(subject, sub);
      console.log('NATS subscribe:', subject);

      // 处理订阅消息
      (async () => {
        for await (const msg of sub) {
          try {
            // 解码消息内容
            if (msg && msg.data) {
              // 心跳消息特殊处理
              if (subject === '_HEARTBEAT_') {
                // 直接调用回调函数，不进行JSON解析
                callback({} as T, msg);
                continue;
              }

              const rawData = new TextDecoder().decode(msg.data);
              console.log('rawData:', rawData);
              // 解析消息结构
              const message = JSON.parse(rawData) as NatsMessage<T>;

              // 日志记录
              console.log('Received message for subject:', subject, {
                method: message.method,
                route: message.route,
                data: message.data?.substring(0, 50) + '...' // 只显示部分数据
              });

              let decodedData: T;
              try {
                // 解码 base64 数据
                const decodedStr = atob(message.data);
                console.log('decodedStr:', decodedStr);
                decodedData = JSON.parse(decodedStr) as T;
              } catch (decodeErr) {
                console.error('Failed to decode message data:', decodeErr);
                decodedData = message.data as T;
              }

              // 调用回调函数,传入解码后的数据
              callback(decodedData, msg);
            }
          } catch (err) {
            console.error(`Message processing error for ${subject}:`, err);
            // 可以选择继续处理其他消息
            continue;
          }
        }
      })().catch((err: Error) => {
        console.error(`Subscription error for ${subject}:`, err);
      });

      return sub;
    } catch (err) {
      console.error('Subscribe error:', err);
      throw err;
    }
  }

  // 请求-响应模式
  public async request<T = any, R = any>(subject: string, data: T, timeout: number = 2000): Promise<R> {
    if (!this.nc) {
      throw new Error('NATS not connected');
    }

    try {
      const payload = typeof data === 'string' ? data : JSON.stringify(data);
      const msg = await this.nc.request(subject, new TextEncoder().encode(payload), { timeout });
      return JSON.parse(new TextDecoder().decode(msg.data)) as R;
    } catch (err) {
      console.error('Request error:', err);
      throw err;
    }
  }

  // 取消订阅
  public async unsubscribe(subject: string): Promise<void> {
    const sub = this.subscriptions.get(subject);
    if (sub) {
      await sub.unsubscribe();
      this.subscriptions.delete(subject);
    }
  }

  // 关闭连接
  public async close(): Promise<void> {
    if (this.nc) {
      await this.nc.close();
      this.updateStatus('disconnected');
      this.nc = null;
    }
  }

  // 启动心跳检测
  public async startHeartbeat(): Promise<void> {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.heartbeatInterval = setInterval(async () => {
      try {
        if (this.nc) {
          // console.log('NATS PING >>>>>');
          await this.publish('_HEARTBEAT_', '');
          this.lastPingTime = Date.now();
          this.updateStatus('connected');
        }
      } catch (err) {
        console.error('Heartbeat failed:', err);
        this.updateStatus('disconnected');
        this.handleDisconnect();
      }
    }, 180000);
  }

  // 可以添加一个心跳响应检测
  public async initHeartbeatCheck(): Promise<void> {
    try {
      await this.subscribe('_HEARTBEAT_', () => {
        // console.log('<<<<< NATS PONG');
        this.lastPingTime = Date.now();
      });
    } catch (err) {
      console.error('Failed to initialize heartbeat check:', err);
    }
  }

  // 处理断开连接
  private async handleDisconnect(): Promise<void> {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      try {
        await this.connect();
        this.reconnectAttempts = 0;
      } catch (err) {
        console.error('Reconnection failed:', err);
      }
    }
  }

  // 处理连接状态
  private handleStatus(status: Status): void {
    // console.log('NATS status: type:', status.type, '  data:', status.data);

    // 根据不同的状态类型更新连接状态
    switch (status.type) {
      case 'pingTimer':
        // pingTimer 表示心跳正常
        this.updateStatus('connected');
        break;
      case 'disconnect':
        this.updateStatus('disconnected');
        break;
      case 'error':
        this.updateStatus('error');
        break;
      case 'reconnecting':
        this.updateStatus('disconnected');
        break;
      //@ts-ignore
      case 'connect':
        this.updateStatus('connected');
        break;
      default:
        // 其他状态保持当前状态不变
        break;
    }
  }

  // 处理连接关闭
  private handleClosed(err?: Error): void {
    this.updateStatus('closed');
    if (err) {
      console.error('NATS connection closed with error:', err);
    }
  }

  // 更新状态
  private updateStatus(newStatus: ConnectionStatus): void {
    // console.log('Updating NATS status:', {
    //   currentStatus: this.status,
    //   newStatus: newStatus,
    //   listenersCount: this.connectionListeners.size
    // });

    if (this.status !== newStatus) {
      this.status = newStatus;
      // 确保立即通知所有监听器
      setTimeout(() => {
        this.notifyConnectionListeners(newStatus);
      }, 0);
    }
  }

  // 手动重连
  public async manualReconnect(): Promise<boolean> {
    this.reconnectAttempts = 0;
    try {
      await this.connect();
      return true;
    } catch (err) {
      console.error('Manual reconnection failed:', err);
      return false;
    }
  }

  // 获取当前状态
  public getStatus(): ConnectionStatus {
    return this.status;
  }

  // 添加连接监听器
  public addConnectionListener(listener: ConnectionListener): void {
    this.connectionListeners.add(listener);
  }

  // 移除连接监听器
  public removeConnectionListener(listener: ConnectionListener): void {
    this.connectionListeners.delete(listener);
  }

  // 通知所有监听器
  private notifyConnectionListeners(status: ConnectionStatus): void {
    console.log('Notifying listeners of status:', status);
    this.connectionListeners.forEach(listener => {
      try {
        listener(status);
      } catch (err) {
        console.error('Error in connection listener:', err);
      }
    });
  }

  // 清理资源
  public cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
}

export default NatsService;
