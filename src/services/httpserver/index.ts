/**
 * HTTP服务器模块入口
 */

import { HttpRouteHandlers } from './routeHandlers'

// 全局HTTP路由处理器实例
let httpRouteHandlers: HttpRouteHandlers | null = null

/**
 * 初始化HTTP服务器模块
 */
export function initHttpServerModule(): void {
  try {
    // 确保在浏览器环境中运行
    if (typeof window === 'undefined') {
      console.warn('[HTTP-SERVER] 非浏览器环境，跳过HTTP服务器模块初始化')
      return
    }

    // 等待HttpBridge可用
    if (!(window as any).HttpBridge) {
      console.warn('[HTTP-SERVER] HttpBridge未就绪，延迟初始化')
      waitForHttpBridge()
      return
    }

    // 创建并注册路由
    httpRouteHandlers = new HttpRouteHandlers()
    httpRouteHandlers.registerAllRoutes()
    
    console.log('[HTTP-SERVER] HTTP服务器模块初始化完成')
  } catch (error) {
    console.error('[HTTP-SERVER] HTTP服务器模块初始化失败:', error)
  }
}

/**
 * 等待HttpBridge就绪
 */
function waitForHttpBridge(): void {
  const checkInterval = setInterval(() => {
    if ((window as any).HttpBridge) {
      clearInterval(checkInterval)
      initHttpServerModule()
    }
  }, 100)

  // 10秒后超时
  setTimeout(() => {
    clearInterval(checkInterval)
    console.error('[HTTP-SERVER] HttpBridge等待超时')
  }, 10000)
}

/**
 * 销毁HTTP服务器模块
 */
export function destroyHttpServerModule(): void {
  try {
    if (httpRouteHandlers) {
      httpRouteHandlers.clearAllRoutes()
      httpRouteHandlers = null
      console.log('[HTTP-SERVER] HTTP服务器模块已销毁')
    }
  } catch (error) {
    console.error('[HTTP-SERVER] HTTP服务器模块销毁失败:', error)
  }
}

/**
 * 获取HTTP路由处理器实例
 */
export function getHttpRouteHandlers(): HttpRouteHandlers | null {
  return httpRouteHandlers
}

/**
 * 检查HTTP服务器模块是否已初始化
 */
export function isHttpServerModuleInitialized(): boolean {
  return httpRouteHandlers !== null
}

// 导出类型和服务
export * from './types'
export { VodCallService } from './vodCallService'
export { HttpRouteHandlers } from './routeHandlers' 