/**
 * HTTP服务器相关类型定义
 */

// VOD呼叫系统类型定义
export namespace VodCall {
  // 通用请求基础接口
  export interface BaseRequest {
    boxip: string
  }

  // 发送呼叫请求
  export interface SendRequest extends BaseRequest {
    calltpye: string
  }

  // 获取呼叫类型请求
  export interface GetTypeRequest extends BaseRequest {}

  // 取消呼叫请求
  export interface CancelRequest extends BaseRequest {}

  // 通用响应基础接口
  export interface BaseResponse {
    errcode: number
    errmsg: string
  }

  // 获取呼叫类型响应
  export interface GetTypeResponse extends BaseResponse {
    ret: string[]
  }

  // 发送呼叫响应
  export interface SendResponse extends BaseResponse {}

  // 取消呼叫响应
  export interface CancelResponse extends BaseResponse {}
}

// 呼叫类型常量
export const CALL_TYPES = {
  NEED_UTENSILS: '需要餐具',
  NEED_SIFTER: '要筛盅', 
  RUSH_ORDER: '催单',
  CALL_WAITER: '呼叫服务员',
  TECHNICAL_SUPPORT: '技术支持'
} as const

// 错误码常量
export const ERROR_CODES = {
  SUCCESS: 0,
  INVALID_REQUEST: 1001,
  BOX_NOT_FOUND: 1002,
  SERVICE_ERROR: 1003
} as const

// 响应消息常量
export const ERROR_MESSAGES = {
  [ERROR_CODES.SUCCESS]: 'success',
  [ERROR_CODES.INVALID_REQUEST]: '请求参数无效',
  [ERROR_CODES.BOX_NOT_FOUND]: '包房不存在',
  [ERROR_CODES.SERVICE_ERROR]: '服务异常'
} as const

// API路由常量
export const API_ROUTES = {
  VOD_GET_CALL_TYPE: '/api/v1/box/calltype',
  VOD_SEND_CALL: '/api/v1/box/call',
  VOD_CANCEL_CALL: '/api/v1/box/cancelcall'
} as const 