/**
 * HTTP服务器路由处理器
 */

import { VodCallService } from './vodCallService'
import { VodCall, API_ROUTES } from './types'

/**
 * HTTP路由处理器类
 */
export class HttpRouteHandlers {
  private vodCallService: VodCallService

  constructor() {
    this.vodCallService = new VodCallService()
  }

  /**
   * 注册所有HTTP路由
   */
  registerAllRoutes(): void {
    if (this.isHttpBridgeAvailable()) {
      this.registerVodCallRoutes()
      console.log('[HTTP-ROUTES] 所有HTTP路由注册完成')
    } else {
      console.warn('[HTTP-ROUTES] HttpBridge 不可用，跳过路由注册')
    }
  }

  /**
   * 检查HttpBridge是否可用
   */
  private isHttpBridgeAvailable(): boolean {
    return typeof window !== 'undefined' && !!(window as any).HttpBridge
  }

  /**
   * 获取HttpBridge实例
   */
  private getHttpBridge(): any {
    return (window as any).HttpBridge
  }

  /**
   * 注册VOD呼叫相关路由
   */
  private registerVodCallRoutes(): void {
    const httpBridge = this.getHttpBridge()

    // 注册获取呼叫类型路由
    httpBridge.post(API_ROUTES.VOD_GET_CALL_TYPE, this.createVodGetCallTypeHandler())
    
    // 注册发送呼叫路由
    httpBridge.post(API_ROUTES.VOD_SEND_CALL, this.createVodSendCallHandler())
    
    // 注册取消呼叫路由
    httpBridge.post(API_ROUTES.VOD_CANCEL_CALL, this.createVodCancelCallHandler())

    console.log('[HTTP-ROUTES] VOD呼叫路由注册完成')
  }

  /**
   * 创建获取呼叫类型处理器
   */
  private createVodGetCallTypeHandler() {
    return async (request: any, response: any) => {
      try {
        const requestData = this.parseRequestBody<VodCall.GetTypeRequest>(request.body)
        
        if (!this.validateGetCallTypeRequest(requestData)) {
          this.sendErrorResponse(response, 400, 1001, '请求参数无效：缺少必要的boxip字段')
          return
        }

        const result = await this.vodCallService.getCallTypes(requestData)
        response.status(200).json(result)
        
      } catch (error) {
        console.error('[HTTP-ROUTES] 获取呼叫类型失败:', error)
        this.sendErrorResponse(response, 500, 1003, '服务异常')
      }
    }
  }

  /**
   * 创建发送呼叫处理器
   */
  private createVodSendCallHandler() {
    return async (request: any, response: any) => {
      try {
        const requestData = this.parseRequestBody<VodCall.SendRequest>(request.body)
        
        if (!this.validateSendCallRequest(requestData)) {
          this.sendErrorResponse(response, 400, 1001, '请求参数无效：缺少必要的boxip或calltpye字段')
          return
        }

        const result = await this.vodCallService.sendCall(requestData)
        response.status(200).json(result)
        
      } catch (error) {
        console.error('[HTTP-ROUTES] 发送呼叫失败:', error)
        this.sendErrorResponse(response, 500, 1003, '服务异常')
      }
    }
  }

  /**
   * 创建取消呼叫处理器
   */
  private createVodCancelCallHandler() {
    return async (request: any, response: any) => {
      try {
        const requestData = this.parseRequestBody<VodCall.CancelRequest>(request.body)
        
        if (!this.validateCancelCallRequest(requestData)) {
          this.sendErrorResponse(response, 400, 1001, '请求参数无效：缺少必要的boxip字段')
          return
        }

        const result = await this.vodCallService.cancelCall(requestData)
        response.status(200).json(result)
        
      } catch (error) {
        console.error('[HTTP-ROUTES] 取消呼叫失败:', error)
        this.sendErrorResponse(response, 500, 1003, '服务异常')
      }
    }
  }

  /**
   * 解析请求体
   */
  private parseRequestBody<T>(body: any): T {
    if (typeof body === 'string') {
      try {
        return JSON.parse(body)
      } catch (error) {
        throw new Error('Invalid JSON format')
      }
    }
    return body as T
  }

  /**
   * 发送错误响应
   */
  private sendErrorResponse(response: any, statusCode: number, errcode: number, errmsg: string): void {
    response.status(statusCode).json({
      errcode,
      errmsg
    })
  }

  /**
   * 验证获取呼叫类型请求参数
   */
  private validateGetCallTypeRequest(data: any): data is VodCall.GetTypeRequest {
    return data && typeof data.boxip === 'string' && data.boxip.trim() !== ''
  }

  /**
   * 验证发送呼叫请求参数
   */
  private validateSendCallRequest(data: any): data is VodCall.SendRequest {
    return data && 
           typeof data.boxip === 'string' && data.boxip.trim() !== '' &&
           typeof data.calltpye === 'string' && data.calltpye.trim() !== ''
  }

  /**
   * 验证取消呼叫请求参数
   */
  private validateCancelCallRequest(data: any): data is VodCall.CancelRequest {
    return data && typeof data.boxip === 'string' && data.boxip.trim() !== ''
  }

  /**
   * 清理所有注册的路由
   */
  clearAllRoutes(): void {
    if (this.isHttpBridgeAvailable()) {
      const httpBridge = this.getHttpBridge()
      
      // 清理VOD路由
      httpBridge.removeRoute('POST', API_ROUTES.VOD_GET_CALL_TYPE)
      httpBridge.removeRoute('POST', API_ROUTES.VOD_SEND_CALL)
      httpBridge.removeRoute('POST', API_ROUTES.VOD_CANCEL_CALL)
      
      console.log('[HTTP-ROUTES] 所有路由已清理')
    }
  }
} 