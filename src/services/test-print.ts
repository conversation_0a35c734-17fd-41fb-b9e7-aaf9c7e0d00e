import { SessionOrderData, ReservationInfo, RoomPackageInfo } from '@/domains/prints/session-order/models/session-order-data';
import { RoomInfo, ExtendedProductInfo, GiftProductInfo, PackageSubProductInfo } from '@/domains/prints/shared/models/base-models';
import { printingService } from '@/application/printingService';
import { formatDateTime } from '@/utils/dateUtils';

/**
 * 测试打印服务
 * 用于生成测试数据并执行打印测试
 */
export class TestPrintService {
  
  /**
   * 创建测试用的包厢信息
   */
  private createTestRoomInfo(): RoomInfo {
    return {
      name: '豪华包厢A001'
    };
  }

  /**
   * 创建测试用的预订信息
   */
  private createTestReservationInfo(): ReservationInfo {
    return {
      memberCardNo: 'VIP001122',
      memberName: '张三',
      memberPhone: '13800138000',
      proxyOrderer: '李四',
      proxyOrdererPhone: '13900139000'
    };
  }

  /**
   * 创建测试用的房间方案信息
   */
  private createTestRoomPackages(): RoomPackageInfo[] {
    return [
      {
        planName: '豪华包厢套餐A',
        duration: 240, // 4小时
        originalPrice: 58800, // 588.00元 (分)
        actualPrice: 52800   // 528.00元 (分)
      },
      {
        planName: '延时费用',
        duration: 60, // 1小时
        originalPrice: 18800, // 188.00元 (分)
        actualPrice: 16800   // 168.00元 (分)
      }
    ];
  }

  /**
   * 创建测试用的套餐子商品
   */
  private createTestSubProducts(): PackageSubProductInfo[] {
    return [
      { productName: '青岛啤酒', quantity: 6, unit: '瓶' },
      { productName: '薯片', quantity: 2, unit: '包' },
      { productName: '果盘', quantity: 1, unit: '份' }
    ];
  }

  /**
   * 创建测试用的消费商品信息
   */
  private createTestProducts(): ExtendedProductInfo[] {
    return [
      {
        productName: '豪华果盘',
        flavors: '时令水果',
        price: 8800,      // 88.00元 (分)
        payPrice: 7800,   // 78.00元 (分)
        quantity: 2,
        unit: '份',
        totalAmount: 15600 // 156.00元 (分)
      },
      {
        productName: '威士忌套餐',
        price: 128800,     // 1288.00元 (分)
        payPrice: 118800,  // 1188.00元 (分)
        quantity: 1,
        unit: '套',
        totalAmount: 118800, // 1188.00元 (分)
        subProducts: this.createTestSubProducts()
      }
    ];
  }

  /**
   * 创建测试用的赠送商品信息
   */
  private createTestGiftProducts(): GiftProductInfo[] {
    return [
      {
        productName: '迎宾果盘',
        quantity: 1,
        unit: '份',
        price: 3800,    // 38.00元 (分)
        remark: '会员赠送'
      },
      {
        productName: '花生米',
        quantity: 2,
        unit: '碟',
        price: 1200,    // 12.00元 (分)
        remark: '店家赠送'
      }
    ];
  }

  /**
   * 生成测试用的开台单数据
   */
  public generateTestSessionOrderData(): SessionOrderData {
    const now = new Date();
    const startTime = new Date(now.getTime() - 4 * 60 * 60 * 1000); // 4小时前开台
    const endTime = new Date(now.getTime() + 1 * 60 * 60 * 1000);   // 预计1小时后结束
    
    const products = this.createTestProducts();
    const giftProducts = this.createTestGiftProducts();
    
    return {
      shopName: '雷石KTV旗舰店',
      roomInfo: this.createTestRoomInfo(),
      sessionId: 'KTV' + now.getTime().toString().slice(-8),
      reservationInfo: this.createTestReservationInfo(),
      openTime: formatDateTime(startTime.toISOString()),
      startTime: formatDateTime(startTime.toISOString()),
      endTime: formatDateTime(endTime.toISOString()),
      roomPackages: this.createTestRoomPackages(),
      roomFeeTotal: 69600, // 696.00元 (分) = 528 + 168
      products,
      productFeeTotal: products.reduce((sum, product) => sum + product.totalAmount, 0),
      giftProducts,
      giftProductTotal: giftProducts.reduce((sum, gift) => sum + (gift.price * gift.quantity), 0),
      prePayBalance: 20000, // 200.00元 (分)
      cashierName: '王小二',
      printTime: formatDateTime(now.toISOString()),
      remark: '生日包厢，请准备生日帽和蛋糕'
    };
  }

  /**
   * 执行测试打印
   */
  public async executeTestPrint(): Promise<boolean> {
    try {
      const testData = this.generateTestSessionOrderData();
      
      console.log('生成测试开台单数据:', testData);
      
      // 直接使用测试数据进行打印
      const success = await printingService.printTestSessionOrder(testData);
      
      return success;
    } catch (error) {
      console.error('测试打印失败:', error);
      throw error;
    }
  }

  /**
   * 创建详细的测试数据说明
   */
  public getTestDataDescription(): string {
    const testData = this.generateTestSessionOrderData();
    
    return `
测试打印数据说明：
=================

基本信息：
- 店铺名称: ${testData.shopName}
- 包厢名称: ${testData.roomInfo?.name}
- 开台单号: ${testData.sessionId}
- 开台时间: ${testData.openTime}
- 收银员: ${testData.cashierName}

包厢消费：
${testData.roomPackages?.map(pkg => 
  `- ${pkg.planName}: ${pkg.duration}分钟, 原价:${(pkg.originalPrice/100).toFixed(2)}元, 现价:${(pkg.actualPrice/100).toFixed(2)}元`
).join('\n')}
- 包厢消费总计: ${(testData.roomFeeTotal/100).toFixed(2)}元

商品消费：
${testData.products?.map(product => 
  `- ${product.productName}${product.flavors ? `(${product.flavors})` : ''}: ${product.quantity}${product.unit} × ${(product.payPrice/100).toFixed(2)}元 = ${(product.totalAmount/100).toFixed(2)}元`
).join('\n')}
- 商品消费总计: ${(testData.productFeeTotal/100).toFixed(2)}元

赠送商品：
${testData.giftProducts?.map(gift => 
  `- ${gift.productName}: ${gift.quantity}${gift.unit} (${gift.remark})`
).join('\n')}

预订信息：
- 会员卡号: ${testData.reservationInfo?.memberCardNo}
- 会员姓名: ${testData.reservationInfo?.memberName}
- 会员电话: ${testData.reservationInfo?.memberPhone}

备注: ${testData.remark}
    `.trim();
  }
}

// 导出服务实例
export const testPrintService = new TestPrintService(); 