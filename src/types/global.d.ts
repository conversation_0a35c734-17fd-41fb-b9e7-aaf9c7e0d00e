// 全局类型声明
import type { DialogManager as DialogManagerType } from '@/utils/dialog';

// 基础响应类型
interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
  traceId?: string;
}

// 常用实体类型
interface ProductVO {
  id: string;
  name: string;
  type?: string;
  currentPrice: number;
  unit: string;
  barcode?: string;
  isDisplayed?: boolean;
  isPromotion?: boolean;
  isSoldOut?: boolean;
  calculateInventory?: boolean;
  lowStockThreshold?: number;
  description?: string;
  image?: string;
  [key: string]: any; // 允许额外的属性
}

interface ProductTypeVO {
  id: string;
  name: string;
  ctime?: number;
  customStorageConfig?: string;
  isIncludedInDrinkAnalysis?: boolean;
  isKitchenMonitoring?: boolean;
  distributionChannels?: string;
  isDisplayed?: boolean;
  deliveryTimeout?: number;
  supportsPoints?: boolean;
  utime?: number;
  venueId?: string;
  version?: number;
  [key: string]: any; // 允许额外的属性
}

interface OrderProductVO {
  id: string;
  venueId: string;
  roomId: string;
  sessionId: string;
  orderNo: string;
  productId: string;
  productName: string;
  flavors?: string;
  unit: string;
  quantity: number;
  payPrice: number;
  originalPrice: number;
  payAmount: number;
  originalAmount: number;
  payStatus?: string;
  inPackageTag?: string;
  totalAmount?: number;
  [key: string]: any; // 允许额外的属性
}

interface BookingVO {
  arrivalTime: number;
  ctime: number;
  customerName: string;
  customerPhone: string;
  customerSource: string;
  gender: string;
  id: string;
  memberCard?: string;
  memberCardId?: string;
  openTablePlan?: string;
  remark?: string;
  state?: number;
  utime?: number;
  venueId?: string;
  version?: number;
  consumptionMode?: string;
  [key: string]: any; // 允许额外的属性
}

interface RoomStageVO {
  roomVO: any;
  roomTypeVO: any;
  areaVO: any;
  sessionVO?: any;
  bookingVOs?: any[];
  pricePlanVOs?: any[];
  roomThemeVO?: any;
  unionRoomId?: string;
  unionRoomName?: string;
  combinedStatus?: string;
  mark?: string;
  [key: string]: any; // 允许额外的属性
}

// 扩展 Window 接口
declare interface Window {
  useRoomStore?: any;
  DialogManager: typeof DialogManagerType;
  HttpBridge: HttpBridge;
  [key: string]: any;
}

// 声明模块
declare module '@/entity/projectobj' {
  export const ProductVO: any;
  export const ProductTypeVO: any;
  export const ProductTypeAndPackageVO: any;
  export const ProductOrPackageRVO: any;
  export const AreaVO: any;
  export const RoomTypeVO: any;
  export const OrderProductVO: any;
  export const BookingVO: any;
  export const RoomStageVO: any;
  export const SessionVO: any;
  export const OrderRoomPlanVO: any;
  export const PricePlanVO: any;
  export const HolidayVO: any;
  export const RoomVO: any;
  export const ShiftReportGetIncomeVO: any;
  export const PayBillVO: any;
  export const QueryShiftReportReqDto: any;
}

declare module '@/entity/baseResponse' {
  export const BaseResponse: any;
}

declare module '@/entity/vodSettings' {
  export const VodSettingsEntity: any;
}

// 声明一些缺失的类型
declare type ConsumeDetailForm = any;
declare type ConsumeItem = any;
declare type InstantPaymentParams = any;
declare type OrderRoomPlan = any;
declare type ICartItem = any;
declare type DailyPaiedSessionVO = any;
declare type Room = any;
declare type StageVO = any;
declare type Story = any;

declare module 'vue' {
  interface ComponentCustomProperties {
    $dialog: typeof DialogManagerType;
  }
}

// HttpBridge请求对象类型
interface HttpRequest {
  id: string
  method: string
  url: string
  pathname: string
  search: string
  query: Record<string, string>
  headers: Record<string, string>
  body: any
  timestamp: number
}

// HttpBridge响应对象类型
interface HttpResponse {
  _requestId: string
  _statusCode: number
  _headers: Record<string, string>
  _body: any
  _sent: boolean
  
  status(code: number): HttpResponse
  header(name: string, value: string): HttpResponse
  headers(obj: Record<string, string>): HttpResponse
  json(data: any): HttpResponse
  text(data: string): HttpResponse
  html(data: string): HttpResponse
  send(data: any): HttpResponse
  error(statusCode: number, message: string, details?: Record<string, any>): HttpResponse
}

// HttpBridge处理器函数类型
type HttpHandler = (request: HttpRequest, response: HttpResponse) => Promise<void> | void

// HttpBridge接口
interface HttpBridge {
  route(method: string, path: string, handler: HttpHandler): void
  get(path: string, handler: HttpHandler): void
  post(path: string, handler: HttpHandler): void
  put(path: string, handler: HttpHandler): void
  delete(path: string, handler: HttpHandler): void
  patch(path: string, handler: HttpHandler): void
  all(path: string, handler: HttpHandler): void
  setDefaultHandler(handler: HttpHandler): void
  use(middleware: HttpHandler): void
  removeRoute(method: string, path: string): boolean
  clearRoutes(): void
  clearMiddlewares(): void
  getRoutes(): string[]
}

export {}; 