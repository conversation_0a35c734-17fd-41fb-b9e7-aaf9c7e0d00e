export interface AbnormalPaymentOrderVO {
  ctime: number; // 创建时间戳
  id: string; // 唯一id
  orderNumber: string; // 订单号
  orderStatus: string; // 订单状态
  roomNumber: string; // 房间号
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface AreaVO {
  capacity: number; // 容量
  ctime: number; // 创建时间
  description: string; // 描述
  id: string; // 区域ID
  isDisplayed: boolean; // 是否显示
  name: string; // 区域名称
  state: number; // 状态
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface AuthorizationRecordVO {
  authorizationTime: number; // 授权时间
  authorizationType: string; // 授权类型
  ctime: number; // 创建时间戳
  employeeId: string; // 操作员工ID
  giftRecordId: string; // 赠送记录ID
  id: string; // ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface BillVO {
  amount: number; // 金额
  billNumber: string; // 账单号
  ctime: number; // 创建时间
  id: string; // 唯一id
  state: number; // 状态
  status: string; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface BirthdayGreetingVO {
  ctime: number; // 创建时间戳
  customerGroup: string; // 客户群组
  daysInAdvance: number; // 提前天数
  id: string; // ID
  smsTemplate: string; // 短信模板
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface BookingVO {
  arrivalTime: number; // 预抵时间
  ctime: number; // 创建时间
  customerName: string; // 客户名称
  customerPhone: string; // 客户电话
  customerSource: string; // 客户来源
  gender: string; // 性别 0 男 1 女 3 未知
  id: string; // ID
  memberCard: string; // 会员卡
  memberCardId: string; // 会员卡id
  openTablePlan: string; // 开台方案
  remark: string; // 备注
  roomId: string; // 房间ID
  roomName: string; // 房间名称
  state: number; // 状态
  status: number; // 状态 0 未使用 1 已使用 2 已取消
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface BuyoutPlanVO {
  ctime: number; // 创建时间
  douyinPlanId: string; // 抖音计划ID
  id: string; // 唯一id
  planId: string; // 计划ID
  planName: string; // 计划名称
  price: number; // 价格
  roomTypes: string; // 包含的房间类型(JSON)
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface CashierMachineVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  ipAddress: string; // IP地址
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
  vodServerIP: string; // 点歌服务器IP
}

export interface CashierSystemVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  state: number; // 状态值
  systemSettings: string; // 系统设置
  utime: number; // 更新时间戳
  version: number; // 版本号
  version1: string; // 版本
}

export interface CloudPrinterVO {
  code: string; // 云打印机编码
  ctime: number; // 创建时间戳
  id: string; // ID
  name: string; // 云打印机名称
  secretKey: string; // 云打印机密钥
  specification: string; // 云打印机规格
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface CommissionPlanVO {
  allowedBookingQuantity: number; // 允许的预订数量
  allowedRoomTypes: string; // 允许的房间类型列表
  amount: number; // 佣金金额
  calculationMethod: string; // 计算方法
  ctime: number; // 创建时间
  excludedProducts: string; // 排除的产品列表
  id: string; // ID
  includesRoomRotation: boolean; // 是否包含房间轮换
  maximumCommission: number; // 最大佣金金额
  minimumBillAmount: number; // 最低账单金额
  name: string; // 佣金计划名称
  state: number; // 状态
  tieredRechargeCommission: boolean; // 是否使用分层充值佣金
  timePeriods: Date; // 时间段列表
  type: string; // 佣金类型
  utime: number; // 更新时间
  version: number; // 版本
}

export interface CommissionVO {
  amount: number; // 金额
  commissionId: string; // 佣金ID
  ctime: number; // 创建时间戳
  date: number; // 日期
  id: string; // ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface CommonRemarkVO {
  content: string; // 备注内容
  ctime: number; // 创建时间戳
  id: string; // ID
  state: number; // 状态值
  type: string; // 备注类型
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface ConstructionAssistanceVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  state: number; // 状态值
  storeId: string; // 所属店铺ID
  technicianPhone: string; // 技术员电话
  utime: number; // 更新时间戳
  validityPeriod: number; // 有效期
  version: number; // 版本号
}

export interface ConsumptionCashbackVO {
  cashbackRate: number; // 返现比例
  ctime: number; // 创建时间
  id: string; // 唯一id
  maximumCashback: number; // 最大返现金额
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface CouponClaimVO {
  claimTime: number; // 领取时间
  couponId: string; // 优惠券ID
  ctime: number; // 创建时间戳
  id: string; // ID
  memberId: string; // 会员ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface CouponUsageVO {
  couponId: string; // 优惠券ID
  ctime: number; // 创建时间戳
  id: string; // ID
  memberId: string; // 会员ID
  state: number; // 状态值
  usageAmount: number; // 使用金额
  usageTime: number; // 使用时间
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface CouponVO {
  campaignId: string; // 关联的营销活动ID
  code: string; // 优惠券代码
  ctime: number; // 创建时间戳
  id: string; // ID
  state: number; // 状态值
  type: string; // 优惠券类型
  utime: number; // 更新时间戳
  validFrom: number; // 有效期开始日期
  validTo: number; // 有效期结束日期
  value: number; // 优惠券面值
  version: number; // 版本号
}

export interface CreditAccountVO {
  accountName: string; // 账户名称
  creditLimit: number; // 信用额度
  ctime: number; // 创建时间
  currentBalance: number; // 当前余额
  id: string; // 唯一id
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface CreditUnitVO {
  contactName: string; // 联系人姓名
  creditLimit: number; // 信用额度
  ctime: number; // 创建时间戳
  id: string; // ID
  name: string; // 信用单位名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface CustomerGroupVO {
  ageRange: number; // 年龄范围
  birthdayRange: string; // 生日范围
  cardBalance: number; // 卡余额
  cardLevels: string; // 卡等级列表
  consumptionBehavior: string; // 消费行为
  ctime: number; // 创建时间
  gender: string; // 性别
  id: string; // ID
  name: string; // 客户组名称
  pointsRange: number; // 积分范围
  state: number; // 状态
  totalConsumptionAmount: number; // 总消费金额
  totalConsumptionTimes: number; // 总消费次数
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface CustomerSourceVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  name: string; // 客户来源名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface CustomerTagVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  name: string; // 客户标签名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface DataCleanupVO {
  ctime: number; // 创建时间
  dataType: string; // 数据类型
  id: string; // ID
  requestTime: number; // 请求时间
  state: number; // 状态值
  status: string; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface DouyinGroupBuyingPlanVO {
  bindingStatus: string; // 绑定状态
  ctime: number; // 创建时间
  id: string; // 唯一id
  planId: string; // 计划ID
  planName: string; // 计划名称
  price: number; // 价格
  productInfo: string; // 产品信息(JSON)
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface ElectronicCardVO {
  cardNumber: string; // 卡号
  ctime: number; // 创建时间
  id: string; // 唯一id
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface EmployeeGroupVO {
  ctime: number; // 创建时间戳
  directMembers: string; // 直属成员
  id: string; // ID
  name: string; // 员工组名称
  state: number; // 状态值
  supervisor: string; // 主管
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface EmployeeVO {
  businessPermissions: string; // 业务权限
  canManageGroups: boolean; // 是否可以管理组
  canViewCommissionPerformance: boolean; // 是否可以查看佣金业绩
  ctime: number; // 创建时间戳
  employeeCardId: string; // 员工卡ID
  employeeGroup: string; // 员工组
  employeeNumber: string; // 员工编号
  giftPermissions: string; // 礼品权限
  hasMobileOrderingPermission: boolean; // 是否有移动下单权限
  id: string; // ID
  marketingRole: string; // 营销角色
  modulePermissions: string; // 模块权限
  name: string; // 员工姓名
  permissionRole: string; // 权限角色
  phone: string; // 电话号码
  salesPerformance: number; // 销售业绩
  state: number; // 状态值
  type: string; // 员工类型
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
  wechatBinding: string; // 微信绑定
}

export interface FinancialReportVO {
  ctime: number; // 创建时间
  giftAmount: string; // 赠品金额(JSON)
  giftQuantity: string; // 赠品数量(JSON)
  id: string; // 唯一id
  pointsExchangeAmount: string; // 积分兑换金额(JSON)
  pointsExchangeQuantity: string; // 积分兑换数量(JSON)
  productActualReceived: string; // 产品实收款(JSON)
  productReceivable: string; // 产品应收款(JSON)
  productSalesAmount: string; // 产品销售额(JSON)
  productSalesQuantity: string; // 产品销售数量(JSON)
  rechargeIncome: number; // 充值收入
  rechargeRefund: number; // 充值退款
  redemptionDetails: string; // 核销明细(JSON)
  reportDate: number; // 报表日期
  reportId: string; // 报表ID
  reportType: string; // 报表类型
  reservationIncome: number; // 预订收入
  reservationRefund: number; // 预订退款
  roomActualReceived: string; // 房间实收款(JSON)
  roomReceivable: string; // 房间应收款(JSON)
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface FlavorVO {
  ctime: number; // 创建时间戳
  description: string; // 描述
  id: string; // ID
  name: string; // 口味名称
  sortNum: number; // 排序号
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 场馆ID
  version: number; // 版本号
}

export interface GiftGroupVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  isDisplayed: boolean; // 是否显示
  name: string; // 赠品组名称
  products: string; // 产品类型
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface GiftRecordVO {
  ctime: number; // 创建时间戳
  employeeId: string; // 操作员工ID
  giftTime: number; // 赠送时间
  id: string; // ID
  requiresAuthorization: boolean; // 是否需要授权
  state: number; // 状态值
  type: string; // 赠送类型
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface HistoricalRecordVO {
  ctime: number; // 创建时间
  finishedTables: number; // 结束台数
  id: string; // 唯一id
  openTables: number; // 开台数量
  settlementAmount: number; // 结算金额
  state: number; // 状态
  totalOpenedTables: number; // 总开台数量
  utime: number; // 更新时间
  version: number; // 版本
}

export interface HolidayVO {
  ctime: number; // 创建时间戳
  date: Date; // 日期
  id: string; // ID
  name: string; // 节假日名称
  state: number; // 状态值
  type: string; // 节假日类型 1:节假日 2:工作日
  utime: number; // 更新时间戳
  venueId: string; // 所属店铺ID
  version: number; // 版本号
}

export interface IngredientTypeVO {
  ctime: number; // 创建时间戳
  description: string; // 配料类型描述
  id: string; // ID
  name: string; // 配料类型名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface InventoryRecordVO {
  ctime: number; // 创建时间
  handler: string; // 操作人
  id: string; // ID
  inboundNumber: string; // 入库单号
  products: string; // 产品信息列表
  state: number; // 状态
  time: number; // 操作时间
  type: string; // 记录类型
  utime: number; // 更新时间
  venueId: string; // 所属场地ID
  version: number; // 版本号
  warehouse: string; // 仓库名称
}

export interface InventoryTransactionVO {
  ctime: number; // 创建时间
  handlerId: string; // 处理人ID
  id: string; // ID
  productId: string; // 产品ID
  quantity: number; // 数量
  relatedDocumentNumber: string; // 相关文档编号
  state: number; // 状态
  transactionTime: number; // 交易时间
  type: string; // 交易类型
  utime: number; // 更新时间
  version: number; // 版本号
  warehouseId: string; // 仓库ID
}

export interface M1CardVO {
  cardType: string; // 卡类型
  ctime: number; // 创建时间戳
  id: string; // ID
  password: string; // 密码
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface MarketServiceVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  isEnabled: boolean; // 是否启用
  serviceName: string; // 服务名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface MarketingCampaignVO {
  ctime: number; // 创建时间
  discountDetails: string; // 折扣详情
  distributionChannels: string; // 分销渠道
  endDate: number; // 活动结束日期
  id: string; // ID
  name: string; // 活动名称
  startDate: number; // 活动开始日期
  state: number; // 状态
  targetAudience: string; // 目标受众
  type: string; // 活动类型
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface MarketingRoleVO {
  ctime: number; // 创建时间戳
  description: string; // 角色描述
  id: string; // ID
  name: string; // 营销角色名称
  permissions: string; // 角色权限
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export type MemberCardLevelVO = {
  /** 本金使用范围 */
  balanceUsageScope?: string;
  /** 会员礼遇 */
  benefitSettings?: MemberCardLevelBenefits;
  /** 卡消费时间限制 */
  consumptionTimeSlots?: MemberCardLevelConsumptionTimeSlots;
  /** 创建时间戳 */
  ctime?: number;
  /** 折扣设置 */
  discountSettings?: MemberCardLevelDiscount;
  /** 分发渠道 */
  distributionChannels?: string;
  /** 降级设置 */
  downgradeSettings?: MemberCardLevelDowngradeConditions;
  /** ID */
  id?: string;
  /** 是否支持储值 */
  isSupportBalance?: boolean;
  /** 是否支持线上领卡 */
  isSupportOnlineCard?: boolean;
  /** 等级 */
  level?: number;
  /** logo */
  logo?: string;
  /** 卡费用 */
  memberCardLevelFeeSettings?: MemberCardLevelFee;
  /** 等级名称 */
  name?: string;
  /** 结账限制 */
  paymentRestrictionSettings?: MemberCardLevelPaymentRestrictions;
  /** 状态值 */
  state?: number;
  /** 升级设置 */
  upgradeSettings?: MemberCardLevelUpgradeConditions;
  /** 更新时间戳 */
  utime?: number;
  /** 有效期设置 */
  validityPeriodSettings?: MemberCardLevelValidityPeriod;
  /** 门店ID */
  venueId?: string;
  /** 版本号 */
  version?: number;
};

export type MemberCardLevelDiscount = {
  /** 消费积分基数 */
  consumptionPointsBase?: number;
  /** 每满基数获得积分数 */
  consumptionPointsPerBase?: number;
  /** 商品折扣 */
  productDiscount?: number;
  /** 房间折扣 */
  roomDiscount?: number;
  /** 服务费折扣 */
  serviceFeeDiscount?: number;
};

export type MemberCardLevelFee = {
  /** 办卡费用 */
  cardIssueFee?: number;
  /** 续卡费用 */
  cardRenewalFee?: number;
  /** 补卡费用 */
  cardReplacementFee?: number;
  /** 是否需要制卡费 */
  isNeedCardIssueFee?: boolean;
  /** 最低充值金额 */
  minimumRechargeAmount?: number;
};

export type MemberCardLevelUpgradeConditions = {
  /** 累计消费金额 */
  consumptionAmount?: number;
  /** 年累计积分 */
  yearlyConsumptionPoints?: number;
};

export type MemberCardLevelValidityPeriod = {
  /** 续期周期单位 */
  renewalPeriodUnit?: string;
  /** 续期周期值 */
  renewalPeriodValue?: number;
  /** 有效期结束时间戳 */
  validityEndDate?: number;
  /** 有效期类型 */
  validityPeriodType?: string;
  /** 有效期单位 */
  validityPeriodUnit?: string;
  /** 有效期时长值 */
  validityPeriodValue?: number;
  /** 有效期开始时间戳 */
  validityStartDate?: number;
};

export type MemberCardLevelBenefits = {
  /** 生日福利 */
  birthdayBenefits?: MemberCardLevelSingleBenefit[];
  /** 每月优惠券 */
  monthlyVouchers?: MemberCardLevelSingleBenefit[];
  /** 注册升级福利 */
  registrationUpgradeBenefits?: MemberCardLevelSingleBenefit[];
};

export type MemberCardLevelConsumptionTimeSlots = {
  periods?: MemberCardLevelPeriod[];
};

export type MemberCardLevelPeriod = {
  dateRange?: MemberCardLevelDateRange;
  timeRanges?: MemberCardLevelTimeSlot[];
  type?: string;
  weekDays?: number[];
};

export type MemberCardLevelTimeSlot = {
  end?: string;
  start?: string;
};

export type MemberCardLevelDateRange = {
  end?: number;
  start?: number;
};

export type MemberCardLevelSingleBenefit = {
  /** 数量 */
  count?: number;
  /** 优惠券 */
  coupon?: CouponVO;
};

export type MemberCardLevelPaymentRestrictions = {
  /** 结账限制 */
  paymentRestrictions?: MemberCardLevelSinglePaymentRestriction[];
};

export type MemberCardLevelSinglePaymentRestriction = {
  /** 商品限制 */
  productRestrictions?: SingleProductRestriction[];
  /** 商品类型 */
  productType?: string;
};

export type SingleProductRestriction = {
  /** 可结账类型，1. 本金、赠金可结账 2. 本金可结账 3. 赠金可结账 4. 会员卡不可结账 */
  canSettleType?: number;
  id?: string;
};

export type MemberCardLevelDowngradeConditions = {
  /** 年等级累计积分（以等级切换时间计算） */
  yearlyConsumptionPoints?: number;
};

export interface MemberCardTemplateVO {
  balanceUsageScope: string; // 余额使用范围
  birthdayPerks: string; // 生日特权
  cardFee: number; // 办卡费用
  consumptionPeriods: string; // 消费周期
  consumptionPointsRatio: number; // 消费积分比例
  ctime: number; // 创建时间
  distributionChannels: string; // 分发渠道
  downgradeConditions: string; // 降级条件
  id: string; // ID
  logo: string; // 卡片logo
  minimumRechargeAmount: number; // 最低充值金额
  monthlyCoupons: string; // 每月优惠券
  name: string; // 模板名称
  paymentRestrictions: string; // 支付限制
  productDiscount: number; // 商品折扣
  rechargePointsRatio: number; // 充值积分比例
  renewalFee: number; // 续费费用
  replacementFee: number; // 补卡费用
  roomDiscount: number; // 房间折扣
  signupUpgradePerks: string; // 注册升级特权
  state: number; // 状态
  upgradeConditions: string; // 升级条件
  utime: number; // 更新时间
  validityPeriod: string; // 有效期
  venueId: string; // 场地ID
  version: number; // 版本
}

export interface MemberCardVO {
  cardType: string; // 卡类型
  ctime: number; // 创建时间戳
  discount: number; // 折扣
  id: string; // ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface MemberDayVO {
  ctime: number; // 创建时间
  dayOfWeek: string; // 星期几
  id: string; // 唯一id
  pointsMultiplier: number; // 积分倍数
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export type MemberVO = {
  /** 生日 */
  birthday?: number;
  /** 结束时间 */
  cardEndTime?: number;
  /** 会员卡ID - 7979fff126604feaa4a56cb6d29e0a87 */
  cardId?: string;
  /** 会员卡等级 - normal/gold/diamond */
  cardLevel?: string;
  /** 会员卡名称 - 普通卡/黄金卡/钻石卡 */
  cardName?: string;
  /** 会员卡号 */
  cardNumber?: string;
  /** 会员卡号ID-计算用6位正整数 */
  cardNumberId?: number;
  /** 开卡时间 */
  cardStartTime?: number;
  /** 卡类型,如: 实体卡, 虚拟卡, 电子卡，枚举：physical, virtual, electronic */
  cardType?: string;
  /** 通用赠金（都可以用的赠金） */
  commonBonusBalance?: number;
  /** 创建时间戳 */
  ctime?: number;
  /** 员工ID -操作人 */
  employeeId?: string;
  /** 性别 male/female */
  gender?: string;
  /** 用于商品的赠金 */
  goodsBonusBalance?: number;
  /** ID */
  id?: string;
  /** 是否启用 */
  isEnabled?: boolean;
  /** 会员姓名 */
  name?: string;
  /** 开卡操作人ID - 销售员 */
  operatorId?: string;
  /** 开卡操作人姓名 - 销售员 */
  operatorName?: string;
  /** 会员手机号 */
  phone?: string;
  /** 会员积分 */
  points?: number;
  /** 本金余额 */
  principalBalance?: number;
  /** 用于房费的赠金 */
  roomBonusBalance?: number;
  /** 会员来源,如: 线下实体卡, 线上小程序, 线上APP，枚举：offline_card, mini_program, app */
  source?: string;
  /** 状态值 */
  state?: number;
  /** 会员状态,如: 正常:normal, 挂失:lost, 冻结:frozen, 过期:expired, 注销:cancelled */
  status?: string;
  /** 累计消费金额 */
  totalConsumptionAmount?: number;
  /** 累计消费次数 */
  totalConsumptionTimes?: number;
  /** 更新时间戳 */
  utime?: number;
  /** 版本号 */
  version?: number;
};

export interface MerchantServiceVO {
  authorizationCode: string; // 授权码
  ctime: number; // 创建时间戳
  expirationDate: number; // 过期日期
  id: string; // ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface MobileOrderVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  orderId: string; // 订单ID
  orderTime: number; // 下单时间
  state: number; // 状态值
  status: string; // 状态
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface MonthlyGiftCouponVO {
  ctime: number; // 创建时间戳
  giftQuantity: number; // 赠送数量
  id: string; // ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface NetworkVO {
  connectionStatus: string; // 连接状态
  ctime: number; // 创建时间戳
  id: string; // ID
  state: number; // 状态值
  subnet: string; // 子网
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface NotificationSettingVO {
  ctime: number; // 创建时间
  id: string; // ID
  recipient: string; // 接收人
  state: number; // 状态
  triggerCondition: string; // 触发条件
  type: string; // 通知类型
  utime: number; // 更新时间
  venueId: string; // 所属场地ID
  version: number; // 版本号
}

export interface OnlineOperationSettingsVO {
  allowMixedPayment: boolean; // 是否允许混合支付
  autoWifiConnect: boolean; // 是否自动连接WiFi
  ctime: number; // 创建时间戳
  earlyCheckinCompensation: boolean; // 是否允许提前入住补偿
  firstBillingDuration: number; // 首次计费时长
  id: string; // ID
  inputSalesperson: boolean; // 是否需要输入销售员
  manualRoomInput: boolean; // 是否允许手动输入房间号
  minBillingDuration: number; // 最小计费时长
  onlineBusinessHours: Date; // 在线营业时间
  onlineRoomBillingMode: string; // 在线房间计费模式
  orderMode: string; // 订单模式
  orderPaymentTimeout: number; // 订单支付超时时间
  packagePriceDisplay: string; // 套餐价格展示方式
  quickCardRegistration: boolean; // 是否允许快速注册会员卡
  recommendedMemberCard: string; // 推荐会员卡
  state: number; // 状态值
  useOpenRoomMemberDiscount: boolean; // 开房是否使用会员折扣
  utime: number; // 更新时间戳
  version: number; // 版本号
  wifiPassword: string; // WiFi密码
  wifiUsername: string; // WiFi用户名
}

export interface OperationSettingsVO {
  allowCashierAddEmployee: boolean; // 是否允许收银员添加员工
  allowCashierSelectMode: boolean; // 是否允许收银员选择模式
  allowMinConsumptionDifference: boolean; // 是否允许最低消费差异
  allowOverbuyingEstimatedClearance: boolean; // 是否允许超额购买预估清台
  autoCancelClearanceNextDay: boolean; // 是否自动取消次日清台
  autoLockRoomAfterCheckout: boolean; // 结账后是否自动锁定房间
  autoSwitchToTimeBilling: boolean; // 是否自动切换到时间计费
  callProcessingTimeout: number; // 呼叫处理超时时间
  cancelOpenDuration: number; // 取消开房时长
  cashierDataRetentionDays: number; // 收银员数据保留天数
  checkoutMode: string; // 结账方式
  counterAutoLogoutTime: number; // 柜台自动登出时间
  counterOrderMode: string; // 柜台点单模式
  ctime: number; // 创建时间
  defaultSendReservationSMS: boolean; // 是否默认发送预订短信
  discountMeetMinConsumption: boolean; // 折扣是否满足最低消费
  discountOnlyExcess: boolean; // 折扣是否仅限超出部分
  enableCrossBillingAdjustment: boolean; // 是否启用跨账单调整
  enableMultipleSalespersons: boolean; // 是否启用多个销售员
  enableReturnConfirmation: boolean; // 是否启用退货确认
  enableSplitBilling: boolean; // 是否启用拆分计费
  firstBillingDuration: number; // 首次计费时长
  guestDuration: number; // 客人时长
  id: string; // ID
  inheritMemberCardMode: string; // 继承会员卡模式
  memberCardPaymentMode: string; // 会员卡支付方式
  memberCardVerificationMethod: string; // 会员卡验证方式
  memberDiscountMethod: string; // 会员折扣方式
  minRenewalDuration: number; // 最小续费时长
  mobileOrderCashPaymentMode: string; // 手机点单现金支付方式
  mobileOrderCheckoutMode: string; // 手机点单结账方式
  mobileOrderOnlyStoreOrderedItems: boolean; // 手机点单是否只存储已点商品
  mobileOrderWineRetrievalMode: string; // 手机点单酒水取回方式
  mobileOrderWineStorageMode: string; // 手机点单酒水寄存方式
  orderScreenHoldMode: string; // 订单屏幕保持模式
  productPriceMemberDiscountMethod: string; // 产品价格会员折扣方式
  refundDuration: number; // 退款时长
  reopenDuration: number; // 重新开房时长
  requireRoomForOrder: boolean; // 下单是否需要房间
  requireRoomForWine: boolean; // 酒水是否需要绑定房间
  reservationExpirationDuration: number; // 预订到期时长
  restrictAllMemberQuery: boolean; // 是否限制所有会员查询
  roomChangeRule: string; // 房间变更规则
  roomCleanToIdleDuration: number; // 房间清洁到空闲时长
  roomPriceMemberDiscountMethod: string; // 房间价格会员折扣方式
  roundingType: string; // 四舍五入类型
  secondBillingDuration: number; // 第二次计费时长
  showInventoryOnCashier: boolean; // 是否在收银员端显示库存
  splitBillingRoomPercentage: number; // 拆分计费房间百分比
  state: number; // 状态
  supermarketCheckoutMode: string; // 超市结账方式
  utime: number; // 更新时间
  version: number; // 版本
  wineVerificationMethod: string; // 酒水核销方式
}

export interface OrderOpenVO {
  buyMinute: number; // 买钟时长 单位：分钟
  consumptionMode: string; // 消费模式 消费模式（'buyout'买断, 'timeCharge'买钟, 'timeChargeDiscount'买钟优惠）
  currentTime: Date; // 当前时间
  endTime: number; // 订单结束时间
  inOrderProductInfos: OrderProductVO[]; // 商品信息-套餐内
  isOpenTableSettled: boolean; // 是否开台立结
  minimumCharge: number; // 【买断】最低消费金额
  orderRoomPlanVOS: OrderRoomPlanVO[]; // 房费信息
  orderVOS: OrderVO[]; // 订单信息
  outOrderProductInfos: OrderProductVO[]; // 商品信息-套餐外
  payBillVOs: PayBillVO[]; // 支付单信息
  payRecordVOs: PayRecordVO[]; // 支付记录信息
  pricePlanId: string; // 方案id
  pricePlanName: string; // 价格方案名称
  roomId: string; //
  roomVO: RoomVO; // 房间信息
  selectedAreaId: string; // 选择的计费方式-套餐区域id
  selectedRoomTypeId: string; // 选择的计费方式-房间类型id
  sessionVO: SessionVO; // 会话信息
  startTime: number; // 订单开始时间
  timeChargeAmount: number; // 买钟金额 单位：分
  timeChargeEndTime: Date; // 买钟结束时间 格式：HH:mm
  timeChargeMode: Date; // 买钟类型 minute:按时长、amount:按金额、endTime:按结束时间
  timeChargeType: string; // 买钟价格类型 基础价格、区域价格、节假日价格
  payAmount: number; // 订单总金额
  originalAmount: number; // 原始金额
  venueId: string; // 所属场地ID ktvid
}

export interface OrderPriceBaseVO {
  activityFee: number; //
  baseRoomFee: number; //
  birthdayFee: number; //
  groupBuyFee: number; //
  id: string; //
  minimumCharge: number; //
}

export interface OrderPricePlanVO {
  buyMinute: number; // 买钟时长
  consumptionMode: string; // 消费模式
  ctime: number; // 创建时间
  endTime: number; // 结束时间-真实结束时间
  id: string; // ID
  minimumCharge: number; // 最低消费金额
  orderNo: string; // 订单ID
  pricePlanId: string; // 方案id
  pricePlanName: string; // 价格方案名称
  roomId: string; // 房间ID
  selectedAreaId: string; // 选择的计费方式-套餐区域id
  selectedRoomTypeId: string; // 选择的计费方式-房间类型id
  sessionId: string; // 会话ID
  startTime: number; // 开始时间-真实开始时间
  state: number; // 状态
  timeChargeMode: string; // 买钟类型
  timeChargeType: string; // 买钟价格类型
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本
}

export interface OrderProductVO {
  id: string; // ID
  venueId: string; // 门店ID
  roomId: string; // 房间ID
  sessionId: string; // 场次ID
  orderNo: string; // 订单ID
  productId: string; // 产品ID
  productName: string; // 产品名称
  flavors: string; // 口味
  unit: string; // 单位
  quantity: number; // 数量
  payPrice: number; // 支付价格
  originalPrice: number; // 原价价格
  payAmount: number; // 支付总金额
  originalAmount: number; // 原始总额
  discountRate: number; // 折扣率
  reduceAmount: number; // 减免金额
  freeAmount: number; // 免单金额
  mark: string; // 产品显示备注
  inPackageTag: string; // 套内商品标签
  src: string; // 套餐来源
  ctime: number; // 创建时间
  utime: number; // 更新时间
  state: number; // 状态
  version: number; // 版本号
  statusInOrder?: string; // 订单状态
  pId?: string; // 退款订单ID
  isProductDiscount?: boolean; // 是否商品折扣
  productDiscountRate?: number; // 商品折扣率
}

export interface OrderRoomPlanVO {
  ctime: number; // 创建时间
  duration: number; // 买钟时长
  endTime: number; // 结束时间
  id: string; // ID
  orderNo: string; // 订单ID
  payAmount: number; // 房费
  originalAmount: number; // 原始金额
  pricePlanId: string; // 方案id
  pricePlanName: string; // 价格方案名称
  pricePlanType: string; // 买钟价格类型
  isTimeConsume: boolean; // 是否计时消费
  roomId: string; // 房间ID
  roomName: string; // 房间名称
  sessionId: string; // 会话ID
  startTime: number; // 开始时间
  state: number; // 状态
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface OrderVO {
  ctime: number; // 创建时间
  employeeId: string; // 员工ID
  endTime: number; // 订单结束时间
  id: string; // ID
  mark: string; // 备注
  memberId: string; // 会员ID
  minimumCharge: number; // 最低消费金额
  orderNo: string; // 订单编号
  pOrderNo: string; // 父订单编号 退款中使用
  packageInfo: OrderPricePlanVO; // 套餐信息
  productInfos: OrderProductVO[]; //
  roomId: string; // 房间ID
  roomVO: RoomVO; //
  sessionId: string; // 场次ID
  startTime: number; // 订单开始时间
  state: number; // 状态
  status: string; // 订单状态
  tag: string; // 标签
  type: string; // 订单类型 product、roomplain
  payAmount: number; // 订单总金额
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
  direction: 'normal' | 'refund'; // 方向 normal:下单、refund:退款
}

export interface PaymentMethodVO {
  ctime: number; // 创建时间
  id: string; // ID
  isEnabled: boolean; // 是否启用
  name: string; // 支付方式名称
  sortOrder: number; // 排序顺序
  state: number; // 状态
  type: string; // 支付方式类型
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface PermissionRoleVO {
  allowedProductTypes: string; // 允许的产品类型列表
  businessPermissions: string; // 业务权限列表
  cashierPermissions: string; // 收银权限列表
  ctime: number; // 创建时间戳
  employeeType: string; // 员工类型
  giftPermissions: string; // 礼品权限列表
  id: string; // ID
  modulePermissions: string; // 模块权限列表
  name: string; // 角色名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface PhoneBlacklistVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  phoneNumber: string; // 电话号码
  state: number; // 状态值
  storeId: string; // 所属店铺ID
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface PhysicalCardVO {
  cardNumber: string; // 卡号
  ctime: number; // 创建时间戳
  id: string; // 唯一ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface PointsExchangeVO {
  ctime: number; // 创建时间戳
  id: string; // 唯一ID
  productName: string; // 产品名称
  quantity: number; // 数量
  requiredPoints: number; // 所需积分
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface PosMachineVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  ipAddress: string; // IP地址
  state: number; // 状态值
  status: string; // 状态
  storeId: string; // 所属店铺ID
  type: string; // 类型
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface PrepaidCardTypeVO {
  applicableProductTypes: string; // 适用产品类型
  ctime: number; // 创建时间
  id: string; // 唯一id
  name: string; // 名称
  saleAmount: number; // 销售金额
  state: number; // 状态
  totalTimes: number; // 总次数
  utime: number; // 更新时间
  validityPeriod: string; // 有效期
  version: number; // 版本号
}

export interface PrepaidCardVO {
  cardNumber: string; // 卡号
  ctime: number; // 创建时间戳
  id: string; // 唯一id
  remainingTimes: number; // 剩余次数
  state: number; // 状态值
  usedTimes: number; // 已使用次数
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface PricePlanUnionProductVO {
  count: number; // 商品数量
  id: string; // 商品id
  packageId: string; // 套餐id
  type: string; // 商品类型 package、standard(商品)
}

export interface PricePlanUnionVO {
  billType: string; // 账单类型
  name: string; // 商品名称
  optionCount: number; // 选项数量
  optionType: string; // 选项类型
  products: PricePlanUnionProductVO[]; // 商品列表
}

export interface PricePlanVO {
  activityFee: number; // 活动价格
  advanceDisableDuration: number; // 提前禁用时长
  areaMemberPrices: string; // 区域会员价格
  areaPrices: string; // 区域价格
  baseRoomFee: number; // 基础房费
  birthdayFee: number; // 生日价格
  buyGiftPlan: string; // 买赠方案
  consumptionMode: string; // 消费模式
  consumptionTimeSlots: Date; // 消费时间段
  ctime: number; // 创建时间
  dayEnd: Date; // 结束日期
  dayStart: Date; // 开始日期
  discountDuration: number; // 买钟优惠时长
  discountMode: string; // 优惠模式
  distributionChannel: string; // 分销渠道
  duration: number; // 买断持续时长
  exampleProducts: string; // 示例产品
  freeProducts: string; // 免费产品
  giftDuration: number; // 赠送时长
  groupBuyFee: number; // 团购价格
  hasMinimumCharge: boolean; // 是否有最低消费
  holidayPrices: string; // 节假日价格
  hourMinuteEnd: Date; // 结束时间
  hourMinuteStart: Date; // 开始时间
  id: string; // ID
  isAreaSpecified: boolean; // 是否指定投放区域
  isEnabled: boolean; // 是否启用
  isExcessIncluded: boolean; // 多余部分是否计入房费
  maxDeductibleAmount: number; // 累计最高可抵扣金额
  memberDiscount: number; // 会员折扣
  memberPrice: number; // 会员价格
  minimumCharge: number; // 最低消费金额
  minimumConsumption: number; // 超市消费最低金额
  name: string; // 价格方案名称
  optionalProducts: string; // 可选产品
  orderPriceBaseVO: OrderPriceBaseVO; //
  planPic: string; // 方案图片
  planProducts: string; // 方案内商品
  planProductsPricePlanUnionVOs: PricePlanUnionVO[]; //
  pricePlanSubProductVO: ProductVO[]; // 套餐子产品
  productPackageVOs: ProductPackageVO[]; // 套餐
  roomChargeGoods: string; // 房费可抵扣的商品分类
  roomType: string; // 房间类型
  selectedAreas: string; // 选中的投放区域
  state: number; // 状态
  statisticsCategory: string; // 统计分类
  supportsPoints: boolean; // 是否支持积分
  timeType: Date; // 时间类型
  utime: number; // 更新时间
  venueId: string; // ktvid
  version: number; // 版本
  weeks: string; // 星期
}

export interface PriceSchemeVO {
  areaMemberPrice: number; // 区域会员价格
  areaPrice: number; // 区域价格
  buyAndGiftScheme: string; // 买赠方案
  consumptionMode: string; // 消费模式
  consumptionTimeSlot: string; // 消费时段
  ctime: number; // 创建时间
  defaultItems: string; // 默认包含项目
  distributionChannel: string; // 分销渠道
  freeItems: string; // 免费项目
  hasMinimumCharge: boolean; // 是否有最低消费
  holidayPrice: number; // 节假日价格
  id: string; // ID
  isEnabled: boolean; // 是否启用
  memberDiscount: number; // 会员折扣
  memberPrice: number; // 会员价格
  minimumCharge: number; // 最低消费金额
  name: string; // 价格方案名称
  optionalItems: string; // 可选项目
  roomType: string; // 房间类型
  state: number; // 状态
  supportsPoints: boolean; // 是否支持积分
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface PrintTemplateVO {
  copies: number; // 打印份数
  ctime: number; // 创建时间戳
  id: string; // ID
  mergeProducts: boolean; // 合并产品
  name: string; // 打印模板名称
  paperSize: string; // 纸张大小
  printGifts: boolean; // 打印礼品
  printMode: string; // 打印模式
  printOnlyLocalUse: boolean; // 仅打印本地使用
  printPaymentMethodCount: boolean; // 打印支付方式计数
  printProductTotalAmount: boolean; // 打印产品总金额
  printReverseBill: boolean; // 打印反向账单
  printRobotDeliveryQR: boolean; // 打印机器人配送二维码
  printTime: Date; // 打印时间
  printWineRefund: boolean; // 打印酒水退款
  printZeroBill: boolean; // 打印零账单
  printerType: string; // 打印机类型
  productGroupingMethod: string; // 产品分组方式
  productTypeConfig: string; // 产品类型配置
  productionPointCopies: number; // 生产点份数
  selfServiceCopies: number; // 自助服务份数
  state: number; // 状态值
  templateContent: string; // 模板内容
  utime: number; // 更新时间戳
  version: number; // 版本号
  wineOrderPrintSequence: string; // 酒水订单打印顺序
}

export interface ProductBindingVO {
  ctime: number; // 创建时间戳
  description: string; // 描述
  id: string; // ID
  name: string; // 产品绑定名称
  productList: string; // 产品列表
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 场馆ID
  version: number; // 版本号
}

export interface ProductDisplayCategoryVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  isDisplaySecond: boolean; // 是否显示二级分类
  name: string; // 分类名称
  productTypes: string; // 关联的商品类型
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface ProductMultipleBuyFreeVO {
  buyCount: number; // 购买数量
  buyProducts: string; // 购买商品列表
  ctime: number; // 创建时间
  exampleGiftProduct: string; // 赠送商品示例
  giftPolicy: string; // 赠送策略
  id: string; // 唯一id
  isCanRepeatBuy: boolean; // 是否可以重复购买
  name: string; // 策略名称
  state: number; // 状态
  timeSlots: Date; // 适用时间段
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本
}

export interface ProductOrPackageRVO {
  productPackageVOs: ProductPackageVO[]; //
  productVOs: ProductVO[]; //
}

export interface ProductOutTypeVO {
  area: string; // 适用区域，JSON格式
  ctime: number; // 创建时间戳
  id: string; // 唯一id
  name: string; // 出库类型名称
  productTypes: string; // 适用商品类型，JSON格式
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
  warehouse: string; // 出库仓库，JSON格式
}

export interface ProductPackageTypeVO {
  ctime: number; // 创建时间
  distributionChannels: string; // 分销渠道
  id: string; // ID
  isDisplayed: boolean; // 是否显示
  name: string; // 套餐类型名称
  state: number; // 状态
  supportPoints: boolean; // 是否支持积分
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface ProductPackageVO {
  areaPrices: string; // 区域价格
  availableAfterMinimumConsumption: boolean; // 最低消费后可用
  barcode: string; // 条形码
  calculatePerformance: boolean; // 是否计算业绩
  category: string; // 类别
  consumptionGiftCoupon: string; // 消费赠送优惠券
  countInMinimumConsumption: boolean; // 是否计入最低消费
  ctime: number; // 创建时间戳
  currentPrice: number; // 当前价格
  deploymentAreas: string; // 部署区域
  deploymentChannels: string; // 部署渠道
  deploymentRoomTypes: string; // 部署包厢类型
  description: string; // 描述
  freeDrinkMode: boolean; // 免费酒水模式
  id: string; // ID
  image: string; // 图片
  isOnShelf: boolean; // 是否上架
  isPromoted: boolean; // 是否促销
  memberCardPaymentLimit: string; // 会员卡支付限制
  name: string; // 名称
  optionalGroups: string; // 可选组
  optionalGroupsPricePlanUnionVOs: PricePlanUnionVO[]; // 可选组
  orderQuantityLimit: number; // 订单数量限制
  packageProducts: string; // 套餐产品
  packageProductsPricePlanUnionProductVOs: PricePlanUnionProductVO[]; // 套餐产品
  productVOList: ProductVO[]; // 套餐产品
  series: string; // 系列
  shelfTimeSlots: string; // 上架时段
  staffGift: boolean; // 员工赠送
  state: number; // 状态值
  supportsDiscount: boolean; // 是否支持折扣
  timeSlotPrices: string; // 时段价格
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
  isSoldOut: boolean; // 是否售罄
}

export interface ProductSalesTemplateVO {
  ctime: number; // 创建时间
  id: string; // 唯一id
  name: string; // 名称
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface ProductStatisticsCategoryVO {
  ctime: number; // 创建时间
  id: string; // ID
  name: string; // 统计分类名称
  productIds: string; // 绑定商品类型
  state: number; // 状态
  utime: number; // 更新时间
  venueId: string; // 所属场地ID
  version: number; // 版本号
}

export interface ProductTimeSlotVO {
  ctime: number; // 创建时间
  days: string; // 星期几
  id: string; // ID
  name: string; // 名称
  state: number; // 状态
  timerange: Date; // 时间范围
  type: string; // 类型
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface ProductTypeAndPackageVO {
  productPackageTypeVOs: ProductPackageTypeVO[]; // 套餐类型
  productTypeVOs: ProductTypeVO[]; // 商品类型
  venueId: string; // 所属场地ID ktvid
}

export interface ProductTypeVO {
  ctime: number; // 创建时间
  customStorageConfig: string; // 自定义存储配置
  deliveryTimeout: number; // 配送超时时间
  distributionChannels: string; // 分销渠道列表
  id: string; // ID
  isDisplayed: boolean; // 是否显示
  isIncludedInDrinkAnalysis: boolean; // 是否计入酒水分析
  isKitchenMonitoring: boolean; // 是否启用后厨监控
  name: string; // 产品类型名称
  state: number; // 状态
  supportsPoints: boolean; // 是否支持积分
  utime: number; // 更新时间
  venueId: string; // 所属场地ID
  version: number; // 版本号
}

export interface ProductVO {
  allowRepeatBuy: boolean; // 支持重复购买
  allowStaffGift: boolean; // 支持员工赠送
  allowWineStorage: boolean; // 支持存酒
  areaPrices: string; // 不同区域价格
  auxiliaryFormula: string; // 辅助公式
  barcode: string; // 条形码
  buyGiftPlan: string; // 买赠方案
  calculateInventory: boolean; // 计算库存
  category: string; // 商品分类
  countToMinCharge: boolean; // 计入低消
  countToPerformance: boolean; // 计算业绩
  ctime: number; // 创建时间
  price: number; // 原价
  currentPrice: number; // 当前价格
  deliveryTimeout: number; // 送达超时时间
  description: string; // 商品介绍
  discounts: string; // 支持折扣
  distributionChannels: string; // 分销渠道
  endTime: Date; // 投放结束时间
  externalDeliveryPrice: number; // 外送价格
  flavors: string; // 商品口味
  giftVoucher: string; // 消费赠券
  id: string; // ID
  image: string; // 商品图片
  ingredients: string; // 辅料配方
  isAreaSpecified: boolean; // 指定投放区域
  isDisplayed: boolean; // 是否上架展示
  isPromotion: boolean; // 推广
  isRealPriceProduct: boolean; // 是否为实价产品
  isRoomTypeSpecified: boolean; // 指定投放包厢类型
  isSoldOut: boolean; // 是否洁清
  lowStockThreshold: number; // 低库存数
  memberCardLimits: string; // 会员卡结账限制
  memberCardPaymentRestrictions: string; // 会员卡支付限制
  minimumSaleQuantity: number; // 最小销售数量
  name: string; // 产品名称
  payMark: string; // 支付标签
  recommendCombos: string; // 推荐搭配
  selectedAreas: string; // 指定的投放区域
  selectedRoomTypes: string; // 指定的投放包厢类型
  soldOutTime: number; // 洁清时间
  startTime: Date; // 投放开始时间
  state: number; // 状态
  supportsExternalDelivery: boolean; // 是否支持外送
  timeSlotPrices: string; // 时段价格
  type: string; // 产品类型
  unit: string; // 单位
  utime: number; // 更新时间
  venueId: string; // 所属场地ID
  version: number; // 版本
}

export interface ProductionOrderPlanVO {
  ctime: number; // 创建时间
  id: string; // 唯一id
  name: string; // 名称
  printAreas: string; // 打印区域
  productTypes: string; // 产品类型
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface RechargePackageVO {
  amount: number; // 金额
  amountType: string; // 金额类型
  bonusAmount: number; // 赠送金额
  bonusBeverages: string; // 赠送酒水
  ctime: number; // 创建时间
  distributionChannels: string; // 分销渠道
  id: string; // 唯一id
  intervalBonusAmount: string; // 间隔赠送金额
  percentageBonusAmount: number; // 百分比赠送金额
  remark: string; // 备注
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface RecipeVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  ingredientId: string; // 配料ID
  productId: string; // 产品ID
  quantity: number; // 配料数量
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface RedemptionRecordVO {
  ctime: number; // 创建时间
  employeeId: string; // 员工ID
  id: string; // 唯一ID
  recordId: string; // 记录ID
  redemptionTime: number; // 核销时间
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
  voucherId: string; // 凭证ID
}

export interface ReportTypeVO {
  ctime: number; // 创建时间
  description: string; // 描述
  frequency: string; // 频率
  id: string; // 唯一id
  name: string; // 报表类型名称
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface ReportVO {
  ctime: number; // 创建时间戳
  generationDate: number; // 生成日期
  id: string; // ID
  reportId: string; // 报表ID
  state: number; // 状态值
  type: string; // 类型
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface ReservationOrderVO {
  accountingAccount: string; // 会计科目
  bookingEmployee: string; // 预订员工
  ctime: number; // 创建时间戳
  customerName: string; // 客户姓名
  customerPhone: string; // 客户电话
  expectedArrivalTime: number; // 预计到店时间
  id: string; // ID
  paymentStatus: string; // 支付状态
  refundLevel: string; // 退款等级
  reservationChannel: string; // 预约渠道
  reservationTime: number; // 预约时间
  roomNumber: string; // 房间号
  state: number; // 状态值
  status: string; // 状态
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface ReservationVO {
  accountingAccount: string; // 会计科目
  bookingEmployeeId: string; // 预订员工
  channel: string; // 预订渠道
  ctime: number; // 创建时间
  customerName: string; // 客户姓名
  customerPhone: string; // 客户电话
  estimatedArrivalTime: number; // 预计到达时间
  id: string; // ID
  paymentStatus: string; // 支付状态
  refundTier: string; // 退款等级
  reservationTime: number; // 预订时间
  roomId: string; // 预订房间
  state: number; // 状态
  status: string; // 预订状态
  utime: number; // 更新时间
  version: number; // 版本
}

export interface RewardVO {
  amount: number; // 金额
  ctime: number; // 创建时间戳
  id: string; // ID
  rewardId: string; // 奖励ID
  rewardTime: number; // 奖励时间
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface RoomFaultVO {
  ctime: number; // 创建时间
  faultDescription: string; // 故障描述
  faultTime: number; // 故障时间
  faultType: string; // 故障类型
  id: string; // ID
  roomId: string; // 房间ID
  state: number; // 状态
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface RoomGreetingVO {
  applicableRoomTypes: string; // 适用房型
  content: string; // 问候内容
  ctime: number; // 创建时间戳
  id: string; // ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface RoomStageVO {
  areaVO: AreaVO; // 区域信息
  bookingVOs: BookingVO[]; // 预定信息
  pricePlanVOs: PricePlanVO[]; // 价格计划信息
  roomThemeVO: RoomThemeVO; // 主题信息
  roomTypeVO: RoomTypeVO; // 房间类型信息
  roomVO: RoomVO; // 房间信息
  sessionVO: SessionVO; // 会话信息
  unionRoomId: string; // 会话联合ID 联房时用-联房的主房间ID
  unionRoomName: string; //
}

export interface RoomThemeVO {
  ctime: number; // 创建时间戳
  description: string; // 主题描述
  id: string; // ID
  imageUrl: string; // 主题图片URL
  isDisplayed: boolean; // 是否显示该主题
  name: string; // 房间主题名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 所属场地ID
  version: number; // 版本号
}

export interface RoomTypeVO {
  consumptionMode: string; // 消费模式
  ctime: number; // 创建时间戳
  distributionChannel: string; // 分销渠道
  highConsumptionAlert: number; // 高消费警报金额
  id: string; // ID
  isDisplayed: boolean; // 是否显示
  name: string; // 房间类型名称
  photo: string; // 房间类型照片
  remark: string; // 备注
  state: number; // 状态值
  timeChargeBasePlan: string; // 买钟基础价格方案
  utime: number; // 更新时间戳
  venueId: string; // 所属场地ID
  version: number; // 版本号
}

export interface RoomVO {
  areaId: string; // 区域ID
  areaVO: AreaVO; // 区域信息
  bookingVOs: BookingVO[]; // 预定信息
  closeTime: number; // 关闭时间
  color: string; // 颜色
  consumptionMode: string; // 消费模式
  ctime: number; // 创建时间
  currentTime: number; // 当前时间
  displayItems: string; // 显示项目
  highConsumptionAlert: number; // 高消费警报阈值
  holidayVOs: HolidayVO[]; // 节假日信息
  id: string; // ID
  interiorPhoto: string; // 室内照片
  isDisplayed: boolean; // 是否显示
  name: string; // 房间名称
  openTime: number; // 开放时间
  pricePlanId: string; // 价格方案ID
  pricePlanVOs: PricePlanVO[]; // 价格计划信息
  qrCode: string; // 二维码
  roomThemeVO?: RoomThemeVO; // 主题信息
  roomTypeVO?: RoomTypeVO; // 房间类型信息
  sessionId: string; // 场次ID
  state: number; // 状态
  status: string; // 房间状态
  tag: string; // 标签
  themeId: string; // 主题ID
  typeId: string; // 房间类型ID
  deviceIp: string; // 设备IP
  sequencerIp: string; // 时序器IP
  utime: number; // 更新时间
  venueId: string; // 所属场地ID
  version: number; // 版本
}

export interface RouterVO {
  ctime: number; // 创建时间戳
  dhcpEnabled: boolean; // 是否启用DHCP
  id: string; // ID
  ipAddress: string; // IP地址
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface SalesPerformanceVO {
  amount: number; // 销售金额
  ctime: number; // 创建时间
  employeeId: string; // 销售员ID
  id: string; // 唯一id
  productType: string; // 销售产品类型
  quantity: number; // 销售数量
  saleDate: number; // 销售日期
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface SequencerVO {
  ctime: number; // 创建时间戳
  delayTime: number; // 延迟时间
  gateway: string; // 网关
  id: string; // ID
  ipAddress: string; // IP地址
  macAddress: string; // MAC地址
  model: string; // 型号
  serverIP: string; // 服务器IP
  state: number; // 状态值
  status: string; // 状态
  temperature: number; // 温度
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface ServiceRewardSettingsVO {
  ctime: number; // 创建时间戳
  enabled: boolean; // 是否启用
  id: string; // ID
  splitRatio: number; // 分成比例
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface SessionUnionVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  remark: string; // 备注
  roomFlag: string; // 房间标识
  roomId: string; // 房间ID
  sessionId: string; // 会话ID
  state: number; // 状态值
  unionId: string; // 联房ID
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface SessionVO {
  agentPerson: string; // 代定人
  ctime: number; // 创建时间
  customerSource: string; // 客户来源
  customerTag: string; // 客群标签
  duration: number; // 使用时长
  dutyPerson: string; // 轮房人
  closeTime: number; // 实际关房时间
  endTime: number; // 计划关房时间
  id: string; // 唯一ID
  info: string; // 备注 attach：联房，union：并房，transfer：转房, opening:开台, swap:互换
  isOpenTableSettled: boolean; // 是否开台立结
  minConsume: number; // 最低消费
  orderPricePlanVO: OrderPricePlanVO; // 价格方案信息
  orderSource: string; // 订单来源
  prePayBalance: number; // 预付余额
  rankNumber: string; // 排位号码
  roomFee: number; // 包厢费用
  roomId: string; // 房间ID
  roomVO: RoomVO; // 房间信息
  giftAmount: number; // 赠送金额
  sessionId: string; // 开台ID
  isTimeConsume: boolean; // 是否计时消费
  startTime: number; // 开台时间
  state: number; // 状态
  status: string; // 支付状态
  supermarketFee: number; // 超市费用
  totalFee: number; // 总计费用
  unpaidAmount: number; // 未付金额
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
  payStatus: string; // 支付状态  UNPAID:未结, PAID:已结
}

export interface ShiftReportVO {
  ctime: number; // 创建时间
  giftedProducts: string; // 赠送产品列表(JSON)
  giftedProductsAmount: number; // 赠送产品总金额
  id: string; // 唯一ID
  memberConsumption: number; // 会员消费金额
  memberRecharge: number; // 会员充值金额
  openedAmount: number; // 开台金额
  openedTables: number; // 开台数量
  paymentMethodStats: string; // 支付方式统计(JSON)
  returnedProducts: string; // 退货产品列表(JSON)
  returnedProductsAmount: number; // 退货产品总金额
  settledAmount: number; // 已结算金额
  settledTables: number; // 已结算台数
  shiftTime: number; // 班次时间
  soldProducts: string; // 销售产品列表(JSON)
  soldProductsAmount: number; // 销售产品总金额
  state: number; // 状态
  unsettledAmount: number; // 未结算金额
  unsettledTables: number; // 未结算台数
  utime: number; // 更新时间
  version: number; // 版本
}

export interface SingingDeviceVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  ipAddress: string; // IP地址
  state: number; // 状态值
  type: string; // 设备类型
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface SmsServiceVO {
  balance: number; // 余额
  ctime: number; // 创建时间戳
  id: string; // ID
  signature: string; // 签名
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface StatisticsCategoryVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  name: string; // 统计分类名称
  pricePlanIds: string; // 价格方案ids
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 所属场地ID
  version: number; // 版本号
}

export interface StatisticsPeriodVO {
  ctime: number; // 创建时间戳
  description: string; // 描述
  id: string; // ID
  name: string; // 名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface SubtitleInfoVO {
  content: string; // 内容
  ctime: number; // 创建时间戳
  id: string; // ID
  loopPlay: boolean; // 是否循环播放
  repeatCount: number; // 重复次数
  scrollSpeed: number; // 滚动速度
  state: number; // 状态值
  stayTime: number; // 停留时间
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface ThemeVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  isDisplayed: boolean; // 是否显示
  name: string; // 主题名称
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface TurnoverDataVO {
  ctime: number; // 创建时间戳
  date: number; // 日期
  discountAmount: number; // 折扣金额
  giftAmount: number; // 赠送金额
  id: string; // ID
  memberRecharge: number; // 会员充值
  period: string; // 统计周期
  pointsExchangeAmount: number; // 积分兑换金额
  productActualReceived: number; // 产品实收
  productReceivable: number; // 产品应收
  roomActualReceived: number; // 包厢实收
  roomReceivable: number; // 包厢应收
  state: number; // 状态值
  totalTurnover: number; // 总营业额
  utime: number; // 更新时间戳
  version: number; // 版本号
}

export interface TvScreenActivityVO {
  activityType: string; // 活动类型
  ctime: number; // 创建时间
  endTime: number; // 结束时间
  id: string; // ID
  productInfo: string; // 产品信息
  startTime: number; // 开始时间
  state: number; // 状态
  utime: number; // 更新时间
  version: number; // 版本号
}

export interface VenueVO {
  address: string; // 门店地址
  ctime: number; // 创建时间戳
  endHours: string; // 营业结束时间
  id: string; // ID
  loginEmployeeVO: EmployeeVO; // 登录员工信息
  logo: string; // 门店logo URL
  name: string; // 门店名称
  photos: string; // 门店照片URL列表
  startHours: string; // 营业开始时间
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
  appId: string; // 应用ID
  appKey: string; // 应用密钥
}

export interface VodSettingsVO {
  ctime: number; // 创建时间戳
  erpServerIP: string; // ERP服务器IP地址
  id: string; // 唯一ID
  sequencerModel: string; // 控制器型号
  songScreenReminder: boolean; // 点歌屏提醒
  state: number; // 状态值
  utime: number; // 更新时间戳
  version: number; // 版本号
  vodServerIP: string; // VOD服务器IP地址
}

export interface VoucherVO {
  ctime: number; // 创建时间戳
  id: string; // 唯一ID
  planId: string; // 计划ID
  state: number; // 状态值
  status: string; // 状态
  utime: number; // 更新时间戳
  version: number; // 版本号
  voucherCode: string; // 凭证码
  voucherId: string; // 凭证ID
}

export interface WarehouseVO {
  ctime: number; // 创建时间戳
  id: string; // ID
  isDefaultLiquorStorage: boolean; // 是否为默认酒水仓库
  name: string; // 仓库名称
  remark: string; // 备注
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface WineStorageSettingVO {
  agentExpirationReminder: string; // 代理商到期提醒
  autoConfiscate: boolean; // 是否自动没收
  autoConfiscateDays: number; // 过期多少天后自动充公
  confiscateWarehouse: string; // 充公仓库
  ctime: number; // 创建时间
  customerNotificationDays: number; // 客户通知天数
  id: string; // ID
  merchantExpirationReminder: string; // 商户到期提醒
  merchantNotificationDays: number; // 商户通知天数
  overdueWithdrawalLimit: number; // 逾期取酒限制
  renewalDays: number; // 续期天数
  renewalTimes: number; // 续期次数
  state: number; // 状态
  storageDays: number; // 存储天数
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本
}

export interface WineStorageVO {
  ctime: number; // 创建时间戳
  expirationDate: number; // 过期日期
  handler: string; // 处理人
  id: string; // ID
  orderNumber: string; // 订单号
  phoneNumber: string; // 电话号码
  roomId: string; // 房间ID
  state: number; // 状态值
  utime: number; // 更新时间戳
  venueId: string; // 场地ID
  version: number; // 版本号
}

export interface PayBillVO {
  actualAmount: number; // 实际支付金额
  billDate: number; // 账单日期
  changeAmount: number; // 找零金额
  creditAccountId: string; // 信用账户ID
  creditAmount: number; // 信用金额
  ctime: number; // 创建时间
  employeeId: string; // 员工ID
  finishTime: number; // 完成时间
  id: string; // ID
  orderTag: string; // 订单标签
  billPid: string; // 父支付ID
  billId: string; // 账单ID
  payType: string; // 支付类型
  productName: string; // 产品名称
  roomId: string; // 房间ID
  sessionId: string; // 会话ID
  state: number; // 状态值
  status: string; // 状态
  thirdOrderId: string; // 第三方订单ID
  totalFee: number; // 总费用
  utime: number; // 更新时间
  venueId: string; // 场地ID
  version: number; // 版本号
  roomName: string; // 房间名称
}

export interface PayRecordVO {
  bQROneCode: string; // 扩展字段
  billDate: number; // 账单日期-冗余-用于统计
  billId: string; // payBill.BillId
  ctime: number; // 创建时间
  employeeId: string; // 支付员工ID-冗余-用于统计
  finishTime: number; // 完成时间
  id: string; // ID
  info: string; // 备注
  payId: string; // 支付单ID
  payPid: string; // 只有三方支付时此处才有值
  payType: string; // 支付类型-微信 支付宝 找零 挂账
  productName: string; // 商品名称
  roomId: string; // 房间ID
  sessionId: string; // 场次ID
  state: number; // 状态
  status: string; // 状态 success/refund
  thirdOrderId: string; // 第三方支付单号
  totalFee: number; // 总金额-实际支付金额
  utime: number; // 更新时间
  venueId: string; // 门店id
  version: number; // 版本号
}

export interface ERPUserVO {
  id: string; // ID
  phone: string; // 电话号码
  unionid: string; // 微信unionid
  name: string; // 用户名称
  ctime: number; // 创建时间戳
  utime: number; // 更新时间戳
  state: number; // 状态值
  version: number; // 版本号
}

export interface ShiftReportGetIncomeVO {
  alipayPayAmount?: number; // 支付宝支付金额
  bankCardPayAmount?: number; // 银行卡支付金额
  businessActual?: number; // 营业实收
  businessNet?: number; // 营业净收入
  businessReceivable?: number; // 营业应收
  cashPayAmount?: number; // 现金支付金额
  couponPayAmount?: number; // 优惠券支付金额
  creditAmount?: number; // 信用金额
  customerBatches?: number; // 客户批次数
  generalGiftAmount?: number; // 一般赠送金额
  koubeiPayAmount?: number; // 口碑支付金额
  meituanPayAmount?: number; // 美团支付金额
  memberCardPay?: number; // 会员卡支付
  memberCardPrincipalAmount?: number; // 会员卡本金金额
  memberDiscount?: number; // 会员折扣
  merchantDiscount?: number; // 商家折扣
  minimumAdjustment?: number; // 最低消费调整
  productGiftAmount?: number; // 商品赠送金额
  rechargeAmount?: number; // 充值金额
  rechargeGift?: number; // 充值赠送
  roomGiftAmount?: number; // 包厢赠送金额
  staffGifts?: number; // 员工赠送
  venueId?: string; // 场地ID
  wechatPayAmount?: number; // 微信支付金额
  writeOffActual?: number; // 核销实收
  writeOffDiscount?: number; // 核销折扣
  writeOffReceivable?: number; // 核销应收
}

export interface BaseProductInfo {
  id: string; //选中商品ID
  count: number; // 规格数量
  name: string; // 选中商品名称
  price: number; // 选中商品价格
  quantity: number; // 已选数量
  unit: string; // 规格单位
}
