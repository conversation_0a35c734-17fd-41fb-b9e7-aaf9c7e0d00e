{"name": "thunder_erp_client", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=18.0.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": ">=9.0.0"}, "packageManager": "pnpm@9.15.0", "scripts": {"dev": "cross-env APP_TYPE=main vite --host --port 5173", "dev:client-pad": "cross-env APP_TYPE=client-pad vite --config vite.client-pad.config.ts --host --port 5174 --force", "build:stage": "cross-env NODE_OPTIONS=--max-old-space-size=2048 BUILD_ENV=stage node scripts/generateVersion.js && vite build --mode stage", "build:prod": "cross-env NODE_OPTIONS=--max-old-space-size=2048 BUILD_ENV=production node scripts/generateVersion.js && vite build --mode production && node scripts/compress-build.js", "build": "cross-env APP_TYPE=main vite build", "build:with-type-check": "vue-tsc --noEmit && cross-env APP_TYPE=main vite build", "build:client-pad": "cross-env APP_TYPE=client-pad vite build --config vite.client-pad.config.ts", "build:client-pad:with-type-check": "vue-tsc --noEmit && cross-env APP_TYPE=client-pad vite build --config vite.client-pad.config.ts", "build:client-pad:stage": "cross-env APP_TYPE=client-pad NODE_OPTIONS=--max-old-space-size=4608 BUILD_ENV=stage node scripts/generateVersion.js && vite build --config vite.client-pad.config.ts --mode stage", "android:dev": "npm run build:dev && npm run copy:android", "preview": "vite preview", "preview:client-pad": "cross-env APP_TYPE=client-pad vite preview --config vite.client-pad.config.ts", "test:unit": "vitest", "test": "vitest", "coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "type-check": "vue-tsc --noEmit", "type-check:dev": "vue-tsc --noEmit -p tsconfig.dev.json", "generate-api": "cd scripts/api-generator && ./generate-api.sh", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write ."}, "pnpm": {"overrides": {"pinia": "3.0.1"}}, "dependencies": {"@arms/rum-browser": "^0.0.37", "@element-plus/icons-vue": "^2.3.1", "@formkit/auto-animate": "^0.8.2", "@tanstack/vue-table": "^8.21.2", "@types/crypto-js": "^4.2.2", "@vee-validate/zod": "^4.14.6", "@vueuse/core": "^11.1.0", "axios": "^1.8.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "element-plus": "^2.9.5", "fs": "0.0.1-security", "jsprintmanager": "^7.0.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "nanoid": "^4", "nats": "^2.29.2", "nats.ws": "^1.30.2", "pinia": "3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "qrcode": "^1.5.4", "vconsole": "^3.15.1", "vee-validate": "^4.14.6", "vue": "3.5.12", "vue-hooks-plus": "^2.2.4", "vue-router": "^4.4.5", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^1.9.0", "@mdx-js/mdx": "^2.3.0", "@mdx-js/react": "^2.3.0", "@stagewise/toolbar": "^0.2.1", "@stagewise/toolbar-vue": "^0.1.2", "@storybook/addon-essentials": "^8.6.3", "@storybook/addon-interactions": "^8.6.3", "@storybook/addon-links": "^8.6.3", "@storybook/addon-onboarding": "^8.6.3", "@storybook/blocks": "^8.6.3", "@storybook/test": "^8.6.3", "@storybook/vue3": "^8.6.3", "@storybook/vue3-vite": "^8.6.3", "@types/lodash": "^4.17.17", "@types/node": "^22.8.1", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@umijs/openapi": "^1.13.0", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "5.1.4", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/runtime-core": "^3.5.13", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.0", "happy-dom": "^15.7.4", "jsdom": "^25.0.1", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4", "node-modules-inspector": "^0.2.8", "nprogress": "^0.2.0", "postcss": "^8.4.47", "postcss-nesting": "^13.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^6.1.0", "prettier": "^3.5.3", "remark-gfm": "^3.0.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.86.0", "sass-embedded": "^1.85.1", "sass-loader": "^16.0.5", "storybook": "^8.6.3", "storybook-vue3-router": "^5.0.0", "tailwindcss": "^3.4.14", "terser": "^5.39.0", "typescript": "^5.6.3", "vite": "5.4.10", "vite-plugin-inspect": "^0.8.9", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vue-mcp": "^0.3.1", "vitest": "^2.1.4", "vue-tsc": "^2.2.10"}, "msw": {"workerDirectory": ["public"]}}