package com.thunder.erp.jsinterface;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;

import com.thunder.erp.utils.NetworkPrinter;
import com.thunder.erp.utils.NetworkPrinter.Command;
import com.thunder.erp.print.PrintQueueManager;
import com.thunder.erp.print.PrintConsumer;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class PrinterJSInterface {
    private static final String TAG = "PrinterJSInterface";
    private final Context context;
    private final NetworkPrinter printer;
    private final PrintQueueManager queueManager;
    private final PrintConsumer printConsumer;
    private final ExecutorService executorService;
    private final Handler mainHandler;
    private WebView webView;

    public PrinterJSInterface(Context context, WebView webView) {
        this.context = context;
        this.webView = webView;
        this.printer = new NetworkPrinter(context);
        this.queueManager = PrintQueueManager.getInstance();
        this.printConsumer = PrintConsumer.getInstance(context);
        this.executorService = Executors.newCachedThreadPool();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        // 设置WebView引用并启动打印消费者
        this.printConsumer.setWebView(webView);
        this.printConsumer.start();
        
        Log.i(TAG, "PrinterJSInterface初始化完成，打印队列服务已启动");
    }

    @JavascriptInterface
    public String scanPrinters() {
        try {
            List<NetworkPrinter.PrinterInfo> printers = printer.scanPrinters();
            JSONArray jsonArray = new JSONArray();
            for (NetworkPrinter.PrinterInfo printerInfo : printers) {
                JSONObject json = new JSONObject();
                json.put("ip", printerInfo.ip);
                json.put("port", printerInfo.port);
                jsonArray.put(json);
            }
            return jsonArray.toString();
        } catch (Exception e) {
            Log.e(TAG, "扫描打印机失败", e);
            return "[]";
        }
    }

    @JavascriptInterface
    public boolean rawPrint(String ip, int port, String commandsJson) {
        try {
            JSONArray jsonCommands = new JSONArray(commandsJson);
            List<NetworkPrinter.Command> commands = new ArrayList<>();

            for (int i = 0; i < jsonCommands.length(); i++) {
                JSONObject cmdObj = jsonCommands.getJSONObject(i);
                String type = cmdObj.getString("type");
                
                switch (type) {
                    case "text":
                        // 文本类型
                        String text = cmdObj.getString("data");
                        commands.add(NetworkPrinter.Command.text(text));
                        break;
                    case "command":
                        // 命令类型
                        JSONArray cmdArray = cmdObj.getJSONArray("data");
                        byte[] cmdData = new byte[cmdArray.length()];
                        for (int j = 0; j < cmdArray.length(); j++) {
                            cmdData[j] = (byte) cmdArray.getInt(j);
                        }
                        commands.add(NetworkPrinter.Command.command(cmdData));
                        break;
                    case "raw":
                        // 原始数据类型
                        JSONArray rawArray = cmdObj.getJSONArray("data");
                        byte[] rawData = new byte[rawArray.length()];
                        for (int j = 0; j < rawArray.length(); j++) {
                            rawData[j] = (byte) rawArray.getInt(j);
                        }
                        commands.add(NetworkPrinter.Command.raw(rawData));
                        break;
                }
            }

            return printer.rawPrint(ip, port, commands);
        } catch (Exception e) {
            Log.e(TAG, "打印失败", e);
            return false;
        }
    }

    /**
     * 异步打印方法 - 使用队列模式
     * @param ip 打印机IP
     * @param port 打印机端口
     * @param commandsJson 打印命令JSON
     * @param callbackId 回调ID
     */
    @JavascriptInterface
    public void rawPrintAsync(String ip, int port, String commandsJson, String callbackId) {
        Log.d(TAG, "异步打印请求: " + ip + ":" + port + ", callbackId: " + callbackId);
        
        executorService.execute(() -> {
            try {
                // 验证参数
                if (ip == null || ip.trim().isEmpty() || port <= 0 || 
                    commandsJson == null || callbackId == null) {
                    sendErrorCallback(callbackId, "打印参数无效");
                    return;
                }

                // 解析命令
                List<NetworkPrinter.Command> commands = parseCommands(commandsJson);
                if (commands.isEmpty()) {
                    sendErrorCallback(callbackId, "解析打印命令失败或命令为空");
                    return;
                }

                // 添加到打印队列
                String taskId = queueManager.addTask(ip.trim(), port, commands, callbackId);
                Log.i(TAG, "打印任务已添加到队列: " + taskId + ", callbackId: " + callbackId);

            } catch (Exception e) {
                Log.e(TAG, "添加打印任务失败", e);
                sendErrorCallback(callbackId, "添加打印任务失败: " + e.getMessage());
            }
        });
    }

    /**
     * 解析打印命令
     */
    private List<NetworkPrinter.Command> parseCommands(String commandsJson) throws Exception {
        JSONArray jsonCommands = new JSONArray(commandsJson);
        List<NetworkPrinter.Command> commands = new ArrayList<>();

        for (int i = 0; i < jsonCommands.length(); i++) {
            JSONObject cmdObj = jsonCommands.getJSONObject(i);
            String type = cmdObj.getString("type");
            
            switch (type) {
                case "text":
                    // 文本类型
                    String text = cmdObj.getString("data");
                    commands.add(NetworkPrinter.Command.text(text));
                    break;
                case "command":
                    // 命令类型
                    JSONArray cmdArray = cmdObj.getJSONArray("data");
                    byte[] cmdData = new byte[cmdArray.length()];
                    for (int j = 0; j < cmdArray.length(); j++) {
                        cmdData[j] = (byte) cmdArray.getInt(j);
                    }
                    commands.add(NetworkPrinter.Command.command(cmdData));
                    break;
                case "raw":
                    // 原始数据类型
                    JSONArray rawArray = cmdObj.getJSONArray("data");
                    byte[] rawData = new byte[rawArray.length()];
                    for (int j = 0; j < rawArray.length(); j++) {
                        rawData[j] = (byte) rawArray.getInt(j);
                    }
                    commands.add(NetworkPrinter.Command.raw(rawData));
                    break;
            }
        }

        return commands;
    }

    /**
     * 发送错误回调
     */
    private void sendErrorCallback(String callbackId, String error) {
        if (webView == null || callbackId == null) {
            return;
        }

        String result = String.format("{\"success\": false, \"error\": \"%s\"}", 
            error.replace("\"", "\\\""));
        
        Log.d(TAG, "发送错误回调: " + callbackId + ", 错误: " + error);
        
        mainHandler.post(() -> {
            try {
                String js = String.format("javascript:window.printBridgeCallback('%s', '%s')", 
                    callbackId, 
                    result.replace("'", "\\'"));
                Log.d(TAG, "执行错误回调JS: " + js);
                webView.evaluateJavascript(js, null);
            } catch (Exception e) {
                Log.e(TAG, "执行错误回调失败", e);
            }
        });
    }

    // 保留测试打印方法，方便调试
    @JavascriptInterface
    public boolean printTest(String ip, int port) {
        try {
            List<NetworkPrinter.Command> commands = new ArrayList<>();
            
            // 初始化打印机
            commands.add(Command.command(NetworkPrinter.PrintCmd.INIT));
            
            // 设置中文模式
            commands.add(Command.command(NetworkPrinter.PrintCmd.CHINESE_MODE));
            
            // 居中对齐
            commands.add(Command.command(NetworkPrinter.PrintCmd.ALIGN_CENTER));
            
            // 测试内容
            String content = "\n打印机测试页\n" +
                           "----------------\n" +
                           "IP: " + ip + "\n" +
                           "端口: " + port + "\n" +
                           "时间: " + java.time.LocalDateTime.now() + "\n" +
                           "----------------\n\n\n";
            
            commands.add(Command.text(content));
            
            // 切纸
            commands.add(Command.command(NetworkPrinter.PrintCmd.CUT_PAPER));
            
            return printer.rawPrint(ip, port, commands);
        } catch (Exception e) {
            Log.e(TAG, "打印测试失败", e);
            return false;
        }
    }
}
