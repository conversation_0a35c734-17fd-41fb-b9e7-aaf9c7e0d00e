package com.thunder.erp.print;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;
import android.webkit.WebView;
import com.thunder.erp.utils.NetworkPrinter;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 打印消费者 - 负责处理队列中的打印任务
 */
public class PrintConsumer {
    private static final String TAG = "PrintConsumer";
    private static PrintConsumer instance;
    private final PrintQueueManager queueManager;
    private final NetworkPrinter printer;
    private final ExecutorService executorService;
    private final Handler mainHandler;
    private final AtomicBoolean isRunning;
    private WebView webView;
    
    private PrintConsumer(Context context) {
        this.queueManager = PrintQueueManager.getInstance();
        this.printer = new NetworkPrinter(context);
        this.executorService = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.isRunning = new AtomicBoolean(false);
    }
    
    public static synchronized PrintConsumer getInstance(Context context) {
        if (instance == null) {
            instance = new PrintConsumer(context);
        }
        return instance;
    }
    
    /**
     * 设置WebView引用，用于回调
     */
    public void setWebView(WebView webView) {
        this.webView = webView;
    }
    
    /**
     * 启动消费者服务
     */
    public void start() {
        if (isRunning.compareAndSet(false, true)) {
            Log.i(TAG, "启动打印消费者服务");
            executorService.execute(this::consumeLoop);
        }
    }
    
    /**
     * 停止消费者服务
     */
    public void stop() {
        if (isRunning.compareAndSet(true, false)) {
            Log.i(TAG, "停止打印消费者服务");
            executorService.shutdown();
        }
    }
    
    /**
     * 消费循环 - 持续处理队列中的任务
     */
    private void consumeLoop() {
        Log.i(TAG, "打印消费者开始工作");
        
        while (isRunning.get()) {
            try {
                // 阻塞获取下一个任务
                PrintTask task = queueManager.getNextTask();
                
                if (task != null) {
                    processTask(task);
                }
            } catch (InterruptedException e) {
                Log.i(TAG, "打印消费者被中断");
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                Log.e(TAG, "处理打印任务时发生异常", e);
            }
        }
        
        Log.i(TAG, "打印消费者停止工作");
    }
    
    /**
     * 处理单个打印任务
     */
    private void processTask(PrintTask task) {
        String taskId = task.getId();
        String callbackId = task.getCallbackId();
        
        Log.i(TAG, "开始处理打印任务: " + taskId);
        
        // 更新任务状态为处理中
        queueManager.updateTaskStatus(taskId, PrintTask.TaskStatus.PROCESSING, null);
        
        try {
            // 执行打印
            boolean success = printer.rawPrint(task.getIp(), task.getPort(), task.getCommands());
            
            if (success) {
                // 打印成功
                queueManager.updateTaskStatus(taskId, PrintTask.TaskStatus.COMPLETED, null);
                sendCallback(callbackId, true, null);
                Log.i(TAG, "打印任务成功: " + taskId);
                SystemClock.sleep(200);
            } else {
                // 打印失败
                String error = "打印机连接失败或打印错误";
                queueManager.updateTaskStatus(taskId, PrintTask.TaskStatus.FAILED, error);
                
                // 检查是否已达到最大重试次数
                PrintTask updatedTask = queueManager.getTask(taskId);
                if (updatedTask != null && updatedTask.getRetries() >= 3) {
                    sendCallback(callbackId, false, error);
                }
                Log.w(TAG, "打印任务失败: " + taskId + ", 错误: " + error);
            }
        } catch (Exception e) {
            // 处理异常
            String error = "打印异常: " + e.getMessage();
            queueManager.updateTaskStatus(taskId, PrintTask.TaskStatus.FAILED, error);
            
            // 检查是否已达到最大重试次数
            PrintTask updatedTask = queueManager.getTask(taskId);
            if (updatedTask != null && updatedTask.getRetries() >= 3) {
                sendCallback(callbackId, false, error);
            }
            Log.e(TAG, "打印任务异常: " + taskId, e);
        }
    }
    
    /**
     * 发送回调到H5
     */
    private void sendCallback(String callbackId, boolean success, String error) {
        if (webView == null || callbackId == null) {
            return;
        }
        
        // 构建回调结果
        String result = String.format("{\"success\": %s, \"error\": %s}", 
            success, 
            error != null ? "\"" + error.replace("\"", "\\\"") + "\"" : "null");
        
        Log.d(TAG, "发送打印回调: " + callbackId + ", 结果: " + result);
        
        // 在主线程中执行回调
        mainHandler.post(() -> {
            try {
                String js = String.format("javascript:window.printBridgeCallback('%s', '%s')", 
                    callbackId, 
                    result.replace("'", "\\'"));
                Log.d(TAG, "执行回调JS: " + js);
                webView.evaluateJavascript(js, null);
            } catch (Exception e) {
                Log.e(TAG, "执行打印回调失败", e);
            }
        });
    }
} 