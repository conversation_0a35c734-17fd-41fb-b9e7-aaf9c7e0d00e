package com.thunder.erp.print;

import android.util.Log;
import com.thunder.erp.utils.NetworkPrinter;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 打印队列管理器 - 实现生产者消费者模式
 */
public class PrintQueueManager {
    private static final String TAG = "PrintQueueManager";
    private static PrintQueueManager instance;
    private final BlockingQueue<PrintTask> taskQueue;
    private final ConcurrentHashMap<String, PrintTask> allTasks;
    private final AtomicLong taskIdCounter;
    private final int maxRetries;
    
    private PrintQueueManager() {
        this.taskQueue = new LinkedBlockingQueue<>();
        this.allTasks = new ConcurrentHashMap<>();
        this.taskIdCounter = new AtomicLong(0);
        this.maxRetries = 3;
    }
    
    public static synchronized PrintQueueManager getInstance() {
        if (instance == null) {
            instance = new PrintQueueManager();
        }
        return instance;
    }
    
    /**
     * 添加打印任务到队列
     * @param ip 打印机IP
     * @param port 打印机端口
     * @param commands 打印命令
     * @param callbackId 回调ID
     * @return 任务ID
     */
    public String addTask(String ip, int port, List<NetworkPrinter.Command> commands, String callbackId) {
        String taskId = "print_" + System.currentTimeMillis() + "_" + taskIdCounter.incrementAndGet();
        PrintTask task = new PrintTask(taskId, ip, port, commands, callbackId);
        
        // 添加到队列和任务映射表
        taskQueue.offer(task);
        allTasks.put(taskId, task);
        
        Log.i(TAG, "添加打印任务: " + task + ", 队列长度: " + taskQueue.size());
        return taskId;
    }
    
    /**
     * 获取下一个待处理的任务（阻塞方法）
     * @return 打印任务
     */
    public PrintTask getNextTask() throws InterruptedException {
        return taskQueue.take();
    }
    
    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @param error 错误信息（可选）
     */
    public void updateTaskStatus(String taskId, PrintTask.TaskStatus status, String error) {
        PrintTask task = allTasks.get(taskId);
        if (task == null) {
            Log.w(TAG, "未找到任务: " + taskId);
            return;
        }
        
        PrintTask.TaskStatus oldStatus = task.getStatus();
        task.setStatus(status);
        
        switch (status) {
            case PROCESSING:
                task.setStartedAt(System.currentTimeMillis());
                Log.d(TAG, "任务开始处理: " + taskId);
                break;
                
            case COMPLETED:
                task.setCompletedAt(System.currentTimeMillis());
                long duration = task.getCompletedAt() - task.getStartedAt();
                Log.i(TAG, "任务完成: " + taskId + ", 耗时: " + duration + "ms");
                // 完成后可以选择从映射表中移除，或保留一段时间供查询
                break;
                
            case FAILED:
                task.incrementRetries();
                task.setError(error);
                
                if (task.getRetries() < maxRetries) {
                    // 重新加入队列进行重试
                    task.setStatus(PrintTask.TaskStatus.PENDING);
                    taskQueue.offer(task);
                    Log.w(TAG, "任务失败，将重试: " + taskId + ", 重试次数: " + task.getRetries());
                } else {
                    Log.e(TAG, "任务最终失败: " + taskId + ", 错误: " + error);
                }
                break;
        }
    }
    
    /**
     * 获取任务
     * @param taskId 任务ID
     * @return 打印任务
     */
    public PrintTask getTask(String taskId) {
        return allTasks.get(taskId);
    }
    
    /**
     * 获取队列状态
     * @return 队列大小
     */
    public int getQueueSize() {
        return taskQueue.size();
    }
    
    /**
     * 清理已完成的任务（可选实现）
     */
    public void cleanupCompletedTasks() {
        long cutoffTime = System.currentTimeMillis() - 3600000; // 1小时前
        allTasks.entrySet().removeIf(entry -> {
            PrintTask task = entry.getValue();
            return (task.getStatus() == PrintTask.TaskStatus.COMPLETED || 
                   task.getStatus() == PrintTask.TaskStatus.FAILED) &&
                   task.getCreatedAt() < cutoffTime;
        });
    }
} 