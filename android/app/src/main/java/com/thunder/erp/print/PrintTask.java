package com.thunder.erp.print;

import com.thunder.erp.utils.NetworkPrinter;
import java.util.List;

/**
 * 打印任务数据类
 */
public class PrintTask {
    private String id;
    private String ip;
    private int port;
    private List<NetworkPrinter.Command> commands;
    private TaskStatus status;
    private long createdAt;
    private long startedAt;
    private long completedAt;
    private int retries;
    private String error;
    private String callbackId;
    
    public enum TaskStatus {
        PENDING,       // 待处理
        PROCESSING,    // 处理中
        COMPLETED,     // 已完成
        FAILED         // 失败
    }
    
    public PrintTask(String id, String ip, int port, List<NetworkPrinter.Command> commands, String callbackId) {
        this.id = id;
        this.ip = ip;
        this.port = port;
        this.commands = commands;
        this.callbackId = callbackId;
        this.status = TaskStatus.PENDING;
        this.createdAt = System.currentTimeMillis();
        this.retries = 0;
    }
    
    // Getters and Setters
    public String getId() { return id; }
    
    public String getIp() { return ip; }
    
    public int getPort() { return port; }
    
    public List<NetworkPrinter.Command> getCommands() { return commands; }
    
    public TaskStatus getStatus() { return status; }
    public void setStatus(TaskStatus status) { this.status = status; }
    
    public long getCreatedAt() { return createdAt; }
    
    public long getStartedAt() { return startedAt; }
    public void setStartedAt(long startedAt) { this.startedAt = startedAt; }
    
    public long getCompletedAt() { return completedAt; }
    public void setCompletedAt(long completedAt) { this.completedAt = completedAt; }
    
    public int getRetries() { return retries; }
    public void incrementRetries() { this.retries++; }
    
    public String getError() { return error; }
    public void setError(String error) { this.error = error; }
    
    public String getCallbackId() { return callbackId; }
    
    @Override
    public String toString() {
        return "PrintTask{" +
                "id='" + id + '\'' +
                ", ip='" + ip + '\'' +
                ", port=" + port +
                ", status=" + status +
                ", retries=" + retries +
                '}';
    }
} 