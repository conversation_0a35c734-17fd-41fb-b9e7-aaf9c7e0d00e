package com.thunder.erp.jsinterface;

import android.app.Activity;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.net.Uri;
import com.thunder.erp.utils.DownloadManager;

/**
 * WebView控制的JavaScript接口
 * 提供重新加载页面和退出应用程序的功能
 */
public class WebViewJSInterface {
    private static final String TAG = "WebViewJSInterface";
    private final Activity mActivity;
    private final WebView mWebView;
    private final String mBaseUrl;
    private final String mDeviceId;
    private final String mIpAddress;

    public WebViewJSInterface(Activity activity, WebView webView, String baseUrl, String deviceId, String ipAddress) {
        this.mActivity = activity;
        this.mWebView = webView;
        this.mBaseUrl = baseUrl;
        this.mDeviceId = deviceId;
        this.mIpAddress = ipAddress;
    }

    /**
     * 重新加载WebView
     * JS调用示例: window.webViewBridge.reloadWebView();
     */
    @JavascriptInterface
    public void reloadWebView() {
        Log.d(TAG, "JS调用reloadWebView方法，准备重新加载页面");
        
        mActivity.runOnUiThread(() -> {
            Uri.Builder builder = Uri.parse(mBaseUrl).buildUpon();
            builder.appendQueryParameter("deviceId", mDeviceId)
                   .appendQueryParameter("macAddress", mDeviceId)
                   .appendQueryParameter("ipAddress", mIpAddress);
            String fullUrl = builder.build().toString();

            Log.d(TAG, "重新加载URL: " + fullUrl);
            mWebView.loadUrl(fullUrl);
        });
    }

    /**
     * 退出应用程序
     * JS调用示例: window.webViewBridge.exitApp();
     */
    @JavascriptInterface
    public void exitApp() {
        Log.d(TAG, "JS调用exitApp方法，准备退出应用");
        
        mActivity.runOnUiThread(() -> {
            Log.d(TAG, "执行退出应用");
            mActivity.finish();
        });
    }

    /**
     * 重启应用程序
     * JS调用示例: window.webViewBridge.restartApp();
     */
    @JavascriptInterface
    public void restartApp() {
        Log.d(TAG, "JS调用restartApp方法，准备重启应用");
        
        mActivity.runOnUiThread(() -> {
            Log.d(TAG, "执行重启应用");
            // 通过重启MainActivity来实现重启效果
            android.content.Intent intent = mActivity.getIntent();
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP | android.content.Intent.FLAG_ACTIVITY_NEW_TASK);
            mActivity.startActivity(intent);
            mActivity.finish();
        });
    }

    /**
     * 下载并安装APK
     * JS调用示例: window.webViewBridge.downloadAndInstallApk('https://example.com/app.apk', 'md5hash');
     */
    @JavascriptInterface
    public void downloadAndInstallApk(String downloadUrl, String fileMd5) {
        Log.d(TAG, "JS调用downloadAndInstallApk方法");
        Log.d(TAG, "下载地址: " + downloadUrl);
        Log.d(TAG, "文件MD5: " + fileMd5);
        
        if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
            Log.e(TAG, "下载地址为空，无法下载");
            return;
        }
        
        mActivity.runOnUiThread(() -> {
            try {
                Log.d(TAG, "开始下载APK文件");
                DownloadManager downloadManager = new DownloadManager(mActivity);
                downloadManager.downloadAndInstallApk(downloadUrl, fileMd5);
            } catch (Exception e) {
                Log.e(TAG, "下载安装APK失败: " + e.getMessage(), e);
            }
        });
    }
} 