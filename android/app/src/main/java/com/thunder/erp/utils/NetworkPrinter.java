package com.thunder.erp.utils;

import android.content.Context;
import android.net.wifi.WifiManager;
import android.text.format.Formatter;
import android.util.Log;

import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

public class NetworkPrinter {
    private static final String TAG = "NetworkPrinter";
    private final Context context;
    private static final int[] PRINTER_PORTS = {9100, 515, 631};
    private static final int TIMEOUT = 3000;

    // 打印机信息类
    public static class PrinterInfo {
        public String ip;
        public int port;

        public PrinterInfo(String ip, int port) {
            this.ip = ip;
            this.port = port;
        }

        @Override
        public String toString() {
            return "PrinterInfo{ip='" + ip + "', port=" + port + "}";
        }
    }

    // 打印命令常量
    public static class PrintCmd {
        // 基础命令
        public static final byte[] INIT = {0x1B, 0x40};          // 初始化打印机
        public static final byte[] CHINESE_MODE = {0x1C, 0x26};  // 中文模式
        public static final byte[] CUT_PAPER = {0x1D, 0x56, 0x41, 0x00}; // 切纸
        
        // 文本格式
        public static final byte[] BOLD_ON = {0x1B, 0x45, 0x01}; // 加粗开
        public static final byte[] BOLD_OFF = {0x1B, 0x45, 0x00}; // 加粗关
        public static final byte[] ALIGN_LEFT = {0x1B, 0x61, 0x00}; // 左对齐
        public static final byte[] ALIGN_CENTER = {0x1B, 0x61, 0x01}; // 居中
        public static final byte[] ALIGN_RIGHT = {0x1B, 0x61, 0x02}; // 右对齐
        
        // 字符编码
        public static final byte[] CHARSET_GBK = {0x1B, 0x74, 0x0F}; // GBK编码
        
        // 行间距
        public static final byte[] LINE_SPACING_DEFAULT = {0x1B, 0x32}; // 默认行间距
        public static final byte[] LINE_SPACING_CUSTOM = {0x1B, 0x33}; // 自定义行间距
    }

    // 添加一个内部类用于处理命令类型
    public static class Command {
        public enum Type {
            RAW,        // 原始字节数组
            TEXT,       // 文本，需要编码转换
            COMMAND     // 打印机指令
        }

        private final Type type;
        private final Object data;

        private Command(Type type, Object data) {
            this.type = type;
            this.data = data;
        }

        public static Command raw(byte[] data) {
            return new Command(Type.RAW, data);
        }

        public static Command text(String text) {
            return new Command(Type.TEXT, text);
        }

        public static Command command(byte[] cmd) {
            return new Command(Type.COMMAND, cmd);
        }
    }

    public NetworkPrinter(Context context) {
        this.context = context;
    }

    // 主打印方法 - 处理Command对象
    public boolean rawPrint(String ip, int port, List<Command> commands) {
        Socket socket = null;
        OutputStream outputStream = null;
        try {
            Log.d(TAG, "开始打印到: " + ip + ":" + port);
            
            socket = new Socket();
            socket.connect(new InetSocketAddress(ip, port), TIMEOUT);
            outputStream = socket.getOutputStream();

            // 初始化打印机和设置编码
            outputStream.write(PrintCmd.INIT);
            outputStream.write(PrintCmd.CHARSET_GBK);
            outputStream.write(PrintCmd.CHINESE_MODE);

            // 处理每个打印命令
            for (Command cmd : commands) {
                if (cmd != null) {
                    byte[] data;
                    switch (cmd.type) {
                        case TEXT:
                            // 文本类型，进行GBK编码转换
                            try {
                                data = ((String) cmd.data).getBytes("GBK");
                            } catch (Exception e) {
                                Log.e(TAG, "文本编码转换失败", e);
                                continue;
                            }
                            break;
                        case COMMAND:
                        case RAW:
                            // 命令和原始数据类型，直接使用
                            data = (byte[]) cmd.data;
                            break;
                        default:
                            continue;
                    }
                    
                    if (data != null && data.length > 0) {
                        outputStream.write(data);
                        outputStream.flush();
                    }
                }
            }

            Log.d(TAG, "打印完成");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "打印失败", e);
            return false;
        } finally {
            try {
                if (outputStream != null) outputStream.close();
                if (socket != null) socket.close();
            } catch (Exception e) {
                Log.e(TAG, "关闭资源失败", e);
            }
        }
    }

    // 简单打印方法 - 直接处理字节数组
    public boolean printRawBytes(String ip, int port, List<byte[]> rawCommands) {
        List<Command> commands = new ArrayList<>();
        for (byte[] cmd : rawCommands) {
            commands.add(Command.raw(cmd));
        }
        return rawPrint(ip, port, commands);
    }

    // 扫描打印机方法
    public List<PrinterInfo> scanPrinters() {
        List<PrinterInfo> printers = new ArrayList<>();
        String subnet = getSubnet();
        Log.d(TAG, "开始扫描子网: " + subnet);

        for (int i = 1; i <= 255; i++) {
            String ip = subnet + "." + i;
            PrinterInfo printer = checkPrinterPorts(ip);
            if (printer != null) {
                printers.add(printer);
                Log.d(TAG, "发现打印机: " + printer);
            }
        }

        Log.d(TAG, "扫描完成，共发现 " + printers.size() + " 台打印机");
        return printers;
    }

    // 检查单个IP的打印机端口
    private PrinterInfo checkPrinterPorts(String ip) {
        for (int port : PRINTER_PORTS) {
            try {
                Socket socket = new Socket();
                socket.connect(new InetSocketAddress(ip, port), TIMEOUT);
                socket.close();
                Log.d(TAG, "发现打印机端口: " + ip + ":" + port);
                return new PrinterInfo(ip, port);
            } catch (Exception e) {
                Log.d(TAG, "端口检查失败 " + ip + ":" + port);
            }
        }
        return null;
    }

    // 获取本机所在子网
    private String getSubnet() {
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        String ipAddress = Formatter.formatIpAddress(wifiManager.getConnectionInfo().getIpAddress());
        return ipAddress.substring(0, ipAddress.lastIndexOf("."));
    }
}