package com.thunder.erp.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;
import androidx.core.content.FileProvider;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * 文件下载管理器
 * 封装OkHttp实现文件下载功能，提供下载进度回调
 */
public class DownloadManager {
    private static final String TAG = "DownloadManager";
    private static final int DEFAULT_TIMEOUT = 30; // 默认超时时间(秒)
    private static final int REQUEST_INSTALL_PERMISSION = 1001;
    
    private OkHttpClient okHttpClient;
    private Handler mainHandler;
    private Call currentCall;
    private Activity activity;
    private ProgressDialog progressDialog;
    
    /**
     * 下载监听器
     */
    public interface DownloadListener {
        /**
         * 下载开始
         */
        void onStart();
        
        /**
         * 下载进度更新
         * @param progress 当前进度(0-100)
         * @param downloadedBytes 已下载字节数
         * @param totalBytes 文件总字节数
         */
        void onProgress(int progress, long downloadedBytes, long totalBytes);
        
        /**
         * 下载完成
         * @param file 下载的文件
         */
        void onSuccess(File file);
        
        /**
         * 下载失败
         * @param e 错误原因
         */
        void onFailure(Exception e);
    }
    
    public DownloadManager() {
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .build();
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public DownloadManager(Activity activity) {
        this();
        this.activity = activity;
    }
    
    /**
     * 设置自定义的OkHttpClient
     * @param okHttpClient 自定义的OkHttpClient实例
     */
    public void setOkHttpClient(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }
    
    /**
     * 下载文件
     * @param url 下载链接
     * @param saveDir 保存目录
     * @param fileName 保存的文件名
     * @param listener 下载监听器
     */
    public void download(String url, String saveDir, String fileName, DownloadListener listener) {
        // 检查参数
        if (url == null || url.trim().isEmpty()) {
            if (listener != null) {
                listener.onFailure(new IllegalArgumentException("Download URL cannot be empty"));
            }
            return;
        }
        
        if (saveDir == null || saveDir.trim().isEmpty()) {
            if (listener != null) {
                listener.onFailure(new IllegalArgumentException("Save directory cannot be empty"));
            }
            return;
        }
        
        // 如果文件名为空，从URL中提取
        if (fileName == null || fileName.trim().isEmpty()) {
            fileName = getFileNameFromUrl(url);
        }
        
        // 创建保存目录
        File dir = new File(saveDir);
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                if (listener != null) {
                    mainHandler.post(() -> listener.onFailure(
                            new IOException("Failed to create directory: " + saveDir)));
                }
                return;
            }
        }
        
        // 创建下载文件
        File file = new File(dir, fileName);
        
        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();
        
        // 通知开始下载
        if (listener != null) {
            mainHandler.post(listener::onStart);
        }
        
        // 执行请求
        currentCall = okHttpClient.newCall(request);
        currentCall.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (listener != null) {
                    mainHandler.post(() -> listener.onFailure(e));
                }
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    if (listener != null) {
                        mainHandler.post(() -> listener.onFailure(
                                new IOException("Unexpected response code: " + response.code())));
                    }
                    return;
                }
                
                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    if (listener != null) {
                        mainHandler.post(() -> listener.onFailure(
                                new IOException("Response body is null")));
                    }
                    return;
                }
                
                InputStream inputStream = null;
                FileOutputStream outputStream = null;
                
                try {
                    // 获取总字节数
                    long totalBytes = responseBody.contentLength();
                    if (totalBytes <= 0) {
                        totalBytes = -1; // 未知大小
                    }
                    
                    inputStream = responseBody.byteStream();
                    outputStream = new FileOutputStream(file);
                    
                    byte[] buffer = new byte[8192];
                    long downloadedBytes = 0;
                    long lastProgressUpdateTime = 0;
                    int bufferSize;
                    
                    while ((bufferSize = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bufferSize);
                        downloadedBytes += bufferSize;
                        
                        // 避免过于频繁的进度回调，每100ms更新一次
                        long currentTime = System.currentTimeMillis();
                        if (currentTime - lastProgressUpdateTime >= 100 || downloadedBytes == totalBytes) {
                            lastProgressUpdateTime = currentTime;
                            
                            // 计算下载进度
                            final int progress = totalBytes > 0 
                                    ? (int) (downloadedBytes * 100 / totalBytes) 
                                    : -1;
                            
                            // 通知进度更新
                            if (listener != null) {
                                final long finalDownloadedBytes = downloadedBytes;
                                final long finalTotalBytes = totalBytes;
                                mainHandler.post(() -> listener.onProgress(
                                        progress, finalDownloadedBytes, finalTotalBytes));
                            }
                        }
                    }
                    
                    outputStream.flush();
                    
                    // 通知下载成功
                    if (listener != null) {
                        mainHandler.post(() -> listener.onSuccess(file));
                    }
                } catch (Exception e) {
                    // 下载过程中出现异常
                    if (listener != null) {
                        mainHandler.post(() -> listener.onFailure(e));
                    }
                    
                    // 删除下载失败的文件
                    if (file.exists()) {
                        file.delete();
                    }
                } finally {
                    // 关闭流
                    try {
                        if (outputStream != null) {
                            outputStream.close();
                        }
                        if (inputStream != null) {
                            inputStream.close();
                        }
                    } catch (IOException e) {
                        Log.e(TAG, "Failed to close streams", e);
                    }
                }
            }
        });
    }
    
    /**
     * 取消当前下载任务
     */
    public void cancel() {
        if (currentCall != null && !currentCall.isCanceled()) {
            currentCall.cancel();
        }
    }
    
    /**
     * 下载并安装APK
     * @param downloadUrl APK下载地址
     * @param expectedMd5 期望的MD5值，用于校验文件完整性（可为空）
     */
    public void downloadAndInstallApk(String downloadUrl, String expectedMd5) {
        if (activity == null) {
            Log.e(TAG, "Activity为空，无法下载安装APK");
            return;
        }
        
        Log.d(TAG, "开始下载APK: " + downloadUrl);
        
        // 显示下载进度对话框
        showProgressDialog();
        
        // 获取应用下载目录
        File downloadDir = new File(activity.getExternalFilesDir(null), "downloads");
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }
        
        // 生成APK文件名
        String fileName = "app_update_" + System.currentTimeMillis() + ".apk";
        
        // 开始下载
        download(downloadUrl, downloadDir.getAbsolutePath(), fileName, new DownloadListener() {
            @Override
            public void onStart() {
                Log.d(TAG, "APK下载开始");
                updateProgressDialog("正在下载更新包...", 0);
            }
            
            @Override
            public void onProgress(int progress, long downloadedBytes, long totalBytes) {
                Log.d(TAG, "APK下载进度: " + progress + "%");
                String message = String.format("正在下载更新包... %d%%", progress);
                if (totalBytes > 0) {
                    message += String.format(" (%.1fMB/%.1fMB)", 
                        downloadedBytes / 1024.0 / 1024.0, 
                        totalBytes / 1024.0 / 1024.0);
                }
                updateProgressDialog(message, progress);
            }
            
            @Override
            public void onSuccess(File file) {
                Log.d(TAG, "APK下载成功: " + file.getAbsolutePath());
                hideProgressDialog();
                
                // 验证MD5（如果提供了）
                if (expectedMd5 != null && !expectedMd5.trim().isEmpty()) {
                    updateProgressDialog("正在验证文件完整性...", -1);
                    
                    new Thread(() -> {
                        try {
                            String actualMd5 = calculateMD5(file);
                            mainHandler.post(() -> {
                                hideProgressDialog();
                                if (expectedMd5.equalsIgnoreCase(actualMd5)) {
                                    Log.d(TAG, "MD5校验成功");
                                    installApk(file);
                                } else {
                                    Log.e(TAG, "MD5校验失败，期望: " + expectedMd5 + ", 实际: " + actualMd5);
                                    showError("文件校验失败，请重新下载");
                                    file.delete(); // 删除损坏的文件
                                }
                            });
                        } catch (Exception e) {
                            Log.e(TAG, "MD5校验出错", e);
                            mainHandler.post(() -> {
                                hideProgressDialog();
                                showError("文件校验出错: " + e.getMessage());
                            });
                        }
                    }).start();
                } else {
                    // 直接安装
                    installApk(file);
                }
            }
            
            @Override
            public void onFailure(Exception e) {
                Log.e(TAG, "APK下载失败", e);
                hideProgressDialog();
                showError("下载失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 安装APK
     */
    private void installApk(File apkFile) {
        if (activity == null || apkFile == null || !apkFile.exists()) {
            Log.e(TAG, "安装参数无效");
            return;
        }
        
        Log.d(TAG, "准备安装APK: " + apkFile.getAbsolutePath());
        
        try {
            // Android 8.0以上需要请求安装权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (!activity.getPackageManager().canRequestPackageInstalls()) {
                    Log.d(TAG, "需要请求安装权限");
                    requestInstallPermission(apkFile);
                    return;
                }
            }
            
            // 执行安装
            Intent installIntent = new Intent(Intent.ACTION_VIEW);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0以上使用FileProvider
                Uri apkUri = FileProvider.getUriForFile(
                    activity, 
                    activity.getPackageName() + ".fileprovider", 
                    apkFile
                );
                installIntent.setDataAndType(apkUri, "application/vnd.android.package-archive");
                installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                // Android 7.0以下直接使用文件URI
                installIntent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
            }
            
            installIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            activity.startActivity(installIntent);
            
            Log.d(TAG, "已启动APK安装");
            
        } catch (Exception e) {
            Log.e(TAG, "安装APK失败", e);
            showError("安装失败: " + e.getMessage());
        }
    }
    
    /**
     * 请求安装权限（Android 8.0+）
     */
    private void requestInstallPermission(File apkFile) {
        new AlertDialog.Builder(activity)
            .setTitle("需要安装权限")
            .setMessage("为了安装应用更新，需要授予安装权限")
            .setPositiveButton("去设置", (dialog, which) -> {
                Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivityForResult(intent, REQUEST_INSTALL_PERMISSION);
                
                // 保存APK文件路径，权限获取后继续安装
                activity.getSharedPreferences("download_manager", Activity.MODE_PRIVATE)
                    .edit()
                    .putString("pending_install_apk", apkFile.getAbsolutePath())
                    .apply();
            })
            .setNegativeButton("取消", null)
            .show();
    }
    
    /**
     * 权限请求结果处理（需要在Activity中调用）
     */
    public void onInstallPermissionResult(int requestCode, int resultCode) {
        if (requestCode == REQUEST_INSTALL_PERMISSION) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && 
                activity.getPackageManager().canRequestPackageInstalls()) {
                
                // 获取之前保存的APK文件路径
                String apkPath = activity.getSharedPreferences("download_manager", Activity.MODE_PRIVATE)
                    .getString("pending_install_apk", null);
                
                if (apkPath != null) {
                    File apkFile = new File(apkPath);
                    if (apkFile.exists()) {
                        installApk(apkFile);
                    }
                    
                    // 清除保存的路径
                    activity.getSharedPreferences("download_manager", Activity.MODE_PRIVATE)
                        .edit()
                        .remove("pending_install_apk")
                        .apply();
                }
            } else {
                showError("未获得安装权限，无法安装更新");
            }
        }
    }
    
    /**
     * 显示进度对话框
     */
    private void showProgressDialog() {
        if (activity == null) return;
        
        progressDialog = new ProgressDialog(activity);
        progressDialog.setTitle("应用更新");
        progressDialog.setMessage("正在准备下载...");
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setMax(100);
        progressDialog.setCancelable(false);
        progressDialog.show();
    }
    
    /**
     * 更新进度对话框
     */
    private void updateProgressDialog(String message, int progress) {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.setMessage(message);
            if (progress >= 0) {
                progressDialog.setProgress(progress);
            }
        }
    }
    
    /**
     * 隐藏进度对话框
     */
    private void hideProgressDialog() {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
            progressDialog = null;
        }
    }
    
    /**
     * 显示错误提示
     */
    private void showError(String message) {
        if (activity == null) return;
        
        Toast.makeText(activity, message, Toast.LENGTH_LONG).show();
        Log.e(TAG, message);
    }
    
    /**
     * 计算文件MD5值
     */
    private String calculateMD5(File file) throws IOException, NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        FileInputStream fis = new FileInputStream(file);
        byte[] buffer = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = fis.read(buffer)) != -1) {
            md5.update(buffer, 0, bytesRead);
        }
        
        fis.close();
        
        byte[] digest = md5.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        
        return sb.toString();
    }
    
    /**
     * 从URL中提取文件名
     */
    private String getFileNameFromUrl(String url) {
        int lastPathSeparatorIndex = url.lastIndexOf('/');
        if (lastPathSeparatorIndex != -1) {
            String fileName = url.substring(lastPathSeparatorIndex + 1);
            // 处理可能存在的URL参数
            int queryIndex = fileName.indexOf('?');
            if (queryIndex != -1) {
                fileName = fileName.substring(0, queryIndex);
            }
            
            if (!fileName.isEmpty()) {
                return fileName;
            }
        }
        
        // 如果无法从URL中提取有效文件名，使用时间戳作为文件名
        return "download_" + System.currentTimeMillis();
    }
} 