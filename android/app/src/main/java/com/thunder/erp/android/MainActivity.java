package com.thunder.erp.android;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.webkit.*;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import android.view.Window;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.Toast;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.thunder.erp.jsinterface.AppUpgradeJSInterface;
import com.thunder.erp.jsinterface.JSBridge;
import com.thunder.erp.jsinterface.PrinterJSInterface;
import com.thunder.erp.jsinterface.VodJSInterface;
import com.thunder.erp.jsinterface.WebViewJSInterface;
import com.thunder.erp.utils.UniqueIdUtil;
import com.thunder.erp.utils.NetworkUtil;
import com.thunder.erp.utils.AppUpgradeManager;
import com.thunder.erp.entity.AppUpgradeInfo;

public class MainActivity extends AppCompatActivity {
    private static final boolean DEBUG = BuildConfig.DEBUG;
    private WebView webView;
    
    // 从BuildConfig中获取环境和URL配置
    private static final String BASE_URL = BuildConfig.BASE_URL; // 服务端API地址
    private static final boolean IS_PRODUCTION = BuildConfig.IS_PRODUCTION;
    private static final String CLIENT_TYPE = BuildConfig.CLIENT_TYPE;
    
    // 前端Web页面URL
    private static final String FRONTEND_DEV_URL = BuildConfig.FRONTEND_DEV_URL; // 开发环境前端URL
    private static final String FRONTEND_PROD_URL = BuildConfig.FRONTEND_PROD_URL; // 生产环境前端URL

    private boolean isFirstLoad = true;
    // 添加网络统计
    private int totalResourceRequests = 0;
    private int jsResourceRequests = 0;
    private int failedJsRequests = 0;
    private Map<String, String> resourceErrors = new HashMap<>();
    
    // 欢迎页面相关
    private FrameLayout welcomeScreen;
    private ImageView loadingIndicator;
    
    @SuppressLint({"HardwareIds", "MissingPermission"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 隐藏标题栏
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        
        // 获取设备信息
        String deviceId = UniqueIdUtil.getUniqueId(this);
        // 使用NetworkUtil获取IP地址
        String ipAddress = NetworkUtil.getIPAddress(getApplicationContext());
        
        // 打印日志
        Log.d("MainActivity", "设备ID: " + deviceId);
        Log.d("MainActivity", "MAC地址（AndroidId): " + deviceId);
        Log.d("MainActivity", "IP地址: " + ipAddress);
        Log.d("MainActivity", "客户端类型: " + CLIENT_TYPE);
        Log.d("MainActivity", "服务端API地址: " + BASE_URL);
        Log.d("MainActivity", "前端开发环境URL: " + FRONTEND_DEV_URL);
        Log.d("MainActivity", "前端生产环境URL: " + FRONTEND_PROD_URL);
        Log.d("MainActivity", "应用包名: " + BuildConfig.APPLICATION_ID);
        Log.d("MainActivity", "App版本: " + BuildConfig.VERSION_NAME + " (" + BuildConfig.VERSION_CODE + ")");
        Log.d("MainActivity", "是否生产环境: " + IS_PRODUCTION);
        
        // 显示一个 Toast 以便验证当前使用的URL
        String currentUrl = IS_PRODUCTION ? FRONTEND_PROD_URL : FRONTEND_DEV_URL;
        Toast.makeText(this, "当前前端URL: " + currentUrl, Toast.LENGTH_LONG).show();
        
        setContentView(R.layout.activity_main);
        
        // 初始化欢迎页面
        welcomeScreen = findViewById(R.id.welcome_screen);
        loadingIndicator = findViewById(R.id.loading_indicator);
        // 设置loading图片旋转动画
        setupLoadingAnimation(loadingIndicator);
        
        // 初始化 WebView
        webView = findViewById(R.id.webview);
        setupWebView(webView, deviceId, ipAddress);
    }

    /**
     * 设置加载图标的旋转动画
     */
    private void setupLoadingAnimation(ImageView loadingView) {
        RotateAnimation rotateAnimation = new RotateAnimation(
            0, 360,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f
        );
        rotateAnimation.setDuration(1000);
        rotateAnimation.setRepeatCount(Animation.INFINITE);
        loadingView.startAnimation(rotateAnimation);
    }

    @Override
    public void onBackPressed() {
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // 清理WebView和JSBridge
        if (webView != null) {
            webView.destroy();
            webView = null;
            // 清理JSBridge
            JSBridge.init(null);
        }
        
        // 停止动画
        if (loadingIndicator != null) {
            loadingIndicator.clearAnimation();
        }
    }

    private void setupWebView(WebView webView, String deviceId, String ipAddress) {
        WebSettings settings = webView.getSettings();
        
        // 基本设置
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setAllowFileAccess(true);
        settings.setAllowFileAccessFromFileURLs(true);
        settings.setAllowUniversalAccessFromFileURLs(true);
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // 启用缓存
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        settings.setDatabaseEnabled(true);
        
        // 添加MIME类型映射，解决JS模块加载问题
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        settings.setAllowContentAccess(true);
        
        // 设置默认文本编码
        settings.setDefaultTextEncodingName("UTF-8");
        
        // 启用调试
        if (DEBUG) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        
        // 初始化JSBridge
        JSBridge.init(webView);
        
        // 添加WebChromeClient来处理JavaScript Console消息
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                String message = String.format("JavaScript %s: %s (at %s:%d)",
                        consoleMessage.messageLevel(),
                        consoleMessage.message(),
                        consoleMessage.sourceId(),
                        consoleMessage.lineNumber());
                
                switch (consoleMessage.messageLevel()) {
                    case ERROR:
                        Log.e("WebView", message);
                        break;
                    case WARNING:
                        Log.w("WebView", message);
                        break;
                    case DEBUG:
                        Log.d("WebView", message);
                        break;
                    default:
                        Log.i("WebView", message);
                        break;
                }
                return true;
            }
            
            @Override
            public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
                // 记录JavaScript警告
                Log.w("WebView", "JavaScript警告: " + message + " (来自 " + url + ")");
                return super.onJsAlert(view, url, message, result);
            }
        });

        // 简化 WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d("WebView", "开始加载页面: " + url);
                
                // 确保欢迎页面可见
                showWelcomeScreen();
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d("WebView", "页面加载完成: " + url);
                injectDeviceInfo(view, deviceId, ipAddress);
                
                // 记录资源统计信息
                logResourceStats();
                
                // 分析JS模块
                if (DEBUG) {
                    analyzeJsModules(view);
                }
                
                // 首次加载时清除token
                if (isFirstLoad) {
                    clearUserToken(view);
                    isFirstLoad = false;
                    
                    // 首次加载完成后，检查应用更新
                    checkAppUpgrade();
                }
                
                // 延迟一小段时间后隐藏欢迎页面，确保WebView已经渲染完成
                welcomeScreen.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        hideWelcomeScreen();
                    }
                }, 500); // 延迟500毫秒
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                Log.e("WebView", String.format("加载错误: %s\nURL: %s\n错误码: %d", 
                    error.getDescription(),
                    request.getUrl(),
                    error.getErrorCode()
                ));
                
                if (error.getErrorCode() == ERROR_CONNECT) {
                    view.loadUrl("javascript:alert('网络连接失败，请检查服务器是否正常运行')");
                }
            }
            
            @Override
            public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
                super.onReceivedHttpError(view, request, errorResponse);
                String url = request.getUrl().toString();
                Log.e("WebView", String.format("HTTP错误: URL=%s, 状态码=%d, 原因=%s", 
                    url, 
                    errorResponse.getStatusCode(), 
                    errorResponse.getReasonPhrase()
                ));
                
                // 特别记录JavaScript模块加载错误
                if (url.endsWith(".js")) {
                    failedJsRequests++;
                    String errorInfo = String.format("状态码=%d, 原因=%s", 
                        errorResponse.getStatusCode(), 
                        errorResponse.getReasonPhrase());
                    resourceErrors.put(url, errorInfo);
                    
                    Log.e("WebView", String.format("JavaScript模块加载失败: URL=%s, MIME类型=%s, 编码=%s", 
                        url, 
                        errorResponse.getMimeType(), 
                        errorResponse.getEncoding()
                    ));
                }
            }
            
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                String url = request.getUrl().toString();
                // 特别记录JavaScript模块加载请求
                if (url.endsWith(".js")) {
                    jsResourceRequests++;
                    totalResourceRequests++;
                    
                    Log.d("WebView", "加载JavaScript模块: " + url);
                    
                    // 记录请求头，可能包含重要信息
                    Map<String, String> headers = request.getRequestHeaders();
                    if (headers != null && !headers.isEmpty()) {
                        StringBuilder headerLog = new StringBuilder("请求头信息:\n");
                        for (Map.Entry<String, String> entry : headers.entrySet()) {
                            headerLog.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
                        }
                        Log.d("WebView", headerLog.toString());
                    }
                } else {
                    totalResourceRequests++;
                }
                return super.shouldInterceptRequest(view, request);
            }
            
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                Log.e("WebView", "SSL错误: " + error.toString());
                // 在开发环境中可以选择继续加载
                if (DEBUG) {
                    handler.proceed();
                    Log.w("WebView", "忽略SSL错误并继续加载");
                } else {
                    super.onReceivedSslError(view, handler, error);
                }
            }

            @Override
            public void onLoadResource(WebView view, String url) {
                super.onLoadResource(view, url);
                
                // 分析JavaScript请求的MIME类型问题
                if (url.endsWith(".js")) {
                    Log.d("WebView", "正在加载JavaScript资源: " + url);
                    
                    // 尝试诊断MIME类型问题
                    if (url.contains("?") || url.contains("#")) {
                        Log.w("WebView", "注意: JS URL包含查询参数或片段，可能影响MIME类型判断: " + url);
                    }
                }
            }
        });

        // 注册JavaScript接口
        webView.addJavascriptInterface(new PrinterJSInterface(this), "AndroidPrinter");
        // 添加VOD接口
        webView.addJavascriptInterface(new VodJSInterface(this, webView), "vodBridge");
        // 添加WebView控制接口 - 根据环境选择前端URL
        String frontendUrl = IS_PRODUCTION ? FRONTEND_PROD_URL : FRONTEND_DEV_URL;
        webView.addJavascriptInterface(new WebViewJSInterface(this, webView, frontendUrl, deviceId, ipAddress), "webViewBridge");
        // 添加应用升级接口 - 使用服务端API地址
        webView.addJavascriptInterface(new AppUpgradeJSInterface(this, BASE_URL), "appUpgradeBridge");
        
        // 添加JavaScript资源分析器
        if (DEBUG) {
            injectJsResourceAnalyzer(webView);
        }
        
        // 加载远程URL
        loadRemoteUrl(webView, deviceId, ipAddress);
    }

    /**
     * 显示欢迎页面
     */
    private void showWelcomeScreen() {
        if (welcomeScreen != null) {
            welcomeScreen.setVisibility(View.VISIBLE);
            // 确保动画在显示时启动
            if (loadingIndicator != null && loadingIndicator.getAnimation() == null) {
                setupLoadingAnimation(loadingIndicator);
            }
        }
    }
    
    /**
     * 隐藏欢迎页面
     */
    private void hideWelcomeScreen() {
        if (welcomeScreen != null) {
            welcomeScreen.setVisibility(View.GONE);
            // 停止动画
            if (loadingIndicator != null) {
                loadingIndicator.clearAnimation();
            }
        }
    }

    private void loadRemoteUrl(WebView webView, String deviceId, String ipAddress) {
        if (!IS_PRODUCTION) {
            // 在开发环境中，显示环境选择对话框
            showEnvironmentSelectionDialog(webView, deviceId, ipAddress);
        } else {
            // 在生产环境中，直接使用生产环境前端URL
            loadUrl(webView, deviceId, ipAddress, FRONTEND_PROD_URL);
        }
    }
    
    /**
     * 显示环境选择对话框
     * 在开发环境下可以选择不同的服务器地址
     */
    private void showEnvironmentSelectionDialog(final WebView webView, final String deviceId, final String ipAddress) {
        // 选项列表
        final String[] urls = new String[] { FRONTEND_DEV_URL, FRONTEND_PROD_URL };
        final String[] urlNames = new String[] {
            "开发环境 (" + getDomainFromUrl(FRONTEND_DEV_URL) + ")",
            "生产环境 (" + getDomainFromUrl(FRONTEND_PROD_URL) + ")"
        };
        
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择环境")
               .setItems(urlNames, (dialog, which) -> {
                    String selectedUrl = urls[which];
                    Toast.makeText(MainActivity.this, "已选择: " + urlNames[which], Toast.LENGTH_SHORT).show();

                    // 重新设置WebViewJSInterface，确保使用最新选择的URL
                    webView.removeJavascriptInterface("webViewBridge");
                    webView.addJavascriptInterface(new WebViewJSInterface(this, webView, selectedUrl, deviceId, ipAddress), "webViewBridge");
                    
                    loadUrl(webView, deviceId, ipAddress, selectedUrl);
               })
               .setCancelable(false) // 强制用户选择
               .show();
    }
    
    /**
     * 从URL中提取域名部分
     */
    private String getDomainFromUrl(String url) {
        Uri uri = Uri.parse(url);
        return uri.getHost();
    }
    
    /**
     * 加载指定URL
     */
    private void loadUrl(WebView webView, String deviceId, String ipAddress, String targetUrl) {
        final String clientType = com.thunder.erp.android.BuildConfig.CLIENT_TYPE;
        // 构建并加载URL
        Uri.Builder builder = Uri.parse(targetUrl).buildUpon();
        builder.appendQueryParameter("deviceId", deviceId)
               .appendQueryParameter("macAddress", deviceId)
               .appendQueryParameter("ipAddress", ipAddress)
               .appendQueryParameter("deviceType", clientType)
               .appendQueryParameter("appVersion", BuildConfig.VERSION_NAME)
               .appendQueryParameter("appVersionCode", String.valueOf(BuildConfig.VERSION_CODE));
        String fullUrl = builder.build().toString();

        Log.d("WebView", "加载远程URL: " + fullUrl);
        webView.loadUrl(fullUrl);
    }

    private void injectDeviceInfo(WebView view, String deviceId, String ipAddress) {
        String script = "javascript:" +
            "window.deviceInfo = {" +
            "  deviceId: '" + deviceId + "'," +
            "  ipAddress: '" + ipAddress + "'" +
            "};";
        view.evaluateJavascript(script, null);
    }
    
    private void clearUserToken(WebView view) {
        Log.d("WebView", "首次加载，清除用户token");
        String clearTokenScript = "javascript:" +
            "if (window.localStorage) {" +
            "  localStorage.removeItem('userToken');" +
            "  console.log('Android WebView 已清除 userToken');" +
            "}" +
            "if (typeof window.userStore !== 'undefined' && window.userStore.clearToken) {" +
            "  window.userStore.clearToken();" +
            "  console.log('Android WebView 已调用 userStore.clearToken()');" +
            "}";
        view.evaluateJavascript(clearTokenScript, value -> {
            Log.d("WebView", "清除token脚本执行完成: " + value);
        });
    }

    /**
     * 检查应用更新
     * 如果有强制更新，将自动弹出原生升级提示
     * 如果是普通更新，将由前端Vue代码触发
     */
    private void checkAppUpgrade() {
        Log.d("MainActivity", "自动检查应用更新");
        
        // 创建升级管理器，使用服务端API地址
        final AppUpgradeManager upgradeManager = new AppUpgradeManager(this, BASE_URL);
        
        // 设置升级回调
        upgradeManager.setUpgradeCallback(new AppUpgradeManager.UpgradeCallback() {
            @Override
            public void onCheckResult(boolean hasUpdate, AppUpgradeInfo upgradeInfo) {
                boolean isForceUpgrade = false;
                if (hasUpdate && upgradeInfo != null) {
                    isForceUpgrade = upgradeInfo.isForceUpgrade();
                }
                
                if (hasUpdate && isForceUpgrade) {
                    // 如果有强制更新，自动弹出升级提示
                    Log.d("MainActivity", "发现强制更新，自动弹出更新提示");
                    upgradeManager.startUpgradeProcess();
                } else if (hasUpdate) {
                    // 如果是普通更新，通知前端
                    Log.d("MainActivity", "发现普通更新，等待前端主动触发");
                    webView.post(() -> {
                        webView.evaluateJavascript(
                            "javascript:if(typeof window.onNativeUpdateAvailable === 'function'){" +
                            "window.onNativeUpdateAvailable(true);" +
                            "}else{" +
                            "console.log('前端未定义onNativeUpdateAvailable回调');" +
                            "}",
                            null
                        );
                    });
                } else {
                    Log.d("MainActivity", "没有可用更新");
                }
            }

            @Override
            public void onUpgradeStarted() {
                Log.d("MainActivity", "升级流程已开始");
            }

            @Override
            public void onUpgradeComplete() {
                Log.d("MainActivity", "升级流程已完成");
            }

            @Override
            public void onUpgradeFailed(String errorMessage) {
                Log.e("MainActivity", "升级失败: " + errorMessage);
            }
        });
        
        // 执行更新检查
        upgradeManager.checkUpgrade(false);
    }

    /**
     * 记录资源加载统计信息
     */
    private void logResourceStats() {
        Log.i("WebView", "======== 资源加载统计 ========");
        Log.i("WebView", "总请求数: " + totalResourceRequests);
        Log.i("WebView", "JS请求数: " + jsResourceRequests);
        Log.i("WebView", "JS失败数: " + failedJsRequests);
        
        if (!resourceErrors.isEmpty()) {
            Log.e("WebView", "资源加载错误列表:");
            for (Map.Entry<String, String> entry : resourceErrors.entrySet()) {
                Log.e("WebView", entry.getKey() + " => " + entry.getValue());
            }
        }
        
        // MIME类型相关分析
        if (failedJsRequests > 0) {
            Log.e("WebView", "JavaScript模块加载失败可能原因分析:");
            Log.e("WebView", "1. 服务器未正确设置.js文件的MIME类型（应为application/javascript）");
            Log.e("WebView", "2. 服务器返回了非JS内容（如HTML错误页或登录页）");
            Log.e("WebView", "3. 会话已过期或认证失败，导致重定向");
            Log.e("WebView", "4. 网络问题导致内容损坏");
        }
    }

    /**
     * 注入JavaScript资源分析器
     * 用于分析JavaScript模块加载问题
     */
    private void injectJsResourceAnalyzer(WebView webView) {
        String analyzerScript = "javascript:" +
            // 创建分析器对象
            "window.jsResourceAnalyzer = {" +
            "  loadedModules: {}," +
            "  failedModules: {}," +
            "  originalImport: window.import," +
            
            // 监控动态导入
            "  monitorImport: function() {" +
            "    if (!this.isMonitoring) {" +
            "      var self = this;" +
            "      window.import = function(moduleUrl) {" +
            "        console.log('正在动态导入模块: ' + moduleUrl);" +
            "        return self.originalImport.apply(window, arguments)" +
            "          .then(function(module) {" +
            "            self.loadedModules[moduleUrl] = true;" +
            "            console.log('模块加载成功: ' + moduleUrl);" +
            "            return module;" +
            "          })" +
            "          .catch(function(error) {" +
            "            self.failedModules[moduleUrl] = error.message;" +
            "            console.error('模块加载失败: ' + moduleUrl + ', 错误: ' + error.message);" +
            "            throw error;" +
            "          });" +
            "      };" +
            "      this.isMonitoring = true;" +
            "      console.log('JavaScript模块加载监控已启动');" +
            "    }" +
            "  }," +
            
            // 获取MIME类型分析
            "  detectMimeTypeIssues: function() {" +
            "    var scripts = document.querySelectorAll('script[type=\"module\"]');" +
            "    console.log('检测到 ' + scripts.length + ' 个ES模块脚本');" +
            "    for (var i = 0; i < scripts.length; i++) {" +
            "      var src = scripts[i].src;" +
            "      if (src) {" +
            "        console.log('模块脚本 #' + i + ': ' + src);" +
            "        fetch(src)" +
            "          .then(function(response) {" +
            "            console.log('模块响应: ' + src + ', 状态: ' + response.status + ', 类型: ' + response.headers.get('content-type'));" +
            "          })" +
            "          .catch(function(error) {" +
            "            console.error('模块获取失败: ' + src + ', 错误: ' + error.message);" +
            "          });" +
            "      }" +
            "    }" +
            "  }," +
            
            // 启动分析
            "  start: function() {" +
            "    this.monitorImport();" +
            "    // 页面加载完成后分析MIME类型问题" +
            "    var self = this;" +
            "    if (document.readyState === 'complete') {" +
            "      self.detectMimeTypeIssues();" +
            "    } else {" +
            "      window.addEventListener('load', function() {" +
            "        setTimeout(function() { self.detectMimeTypeIssues(); }, 500);" +
            "      });" +
            "    }" +
            "  }" +
            "};" +
            
            // 自动启动分析器
            "window.jsResourceAnalyzer.start();";
            
        webView.evaluateJavascript(analyzerScript, null);
        Log.d("WebView", "注入JS资源分析器");
    }
    
    /**
     * 在页面加载完成后启动JS模块分析
     */
    private void analyzeJsModules(WebView webView) {
        String analyzeScript = "javascript:" +
            "if (window.jsResourceAnalyzer) {" +
            "  window.jsResourceAnalyzer.detectMimeTypeIssues();" +
            "  var stats = {" +
            "    loadedModules: Object.keys(window.jsResourceAnalyzer.loadedModules).length," +
            "    failedModules: Object.keys(window.jsResourceAnalyzer.failedModules).length" +
            "  };" +
            "  JSON.stringify(stats);" +
            "} else {" +
            "  JSON.stringify({error: 'JS分析器未加载'});" +
            "}";
            
        webView.evaluateJavascript(analyzeScript, result -> {
            Log.i("WebView", "JS模块分析结果: " + result);
        });
    }
}