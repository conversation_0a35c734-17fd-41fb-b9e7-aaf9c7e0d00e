package com.thunder.erp.android;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.webkit.*;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import android.view.Window;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.Toast;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.thunder.erp.jsinterface.AppUpgradeJSInterface;
import com.thunder.erp.jsinterface.JSBridge;
import com.thunder.erp.jsinterface.PrinterJSInterface;
import com.thunder.erp.jsinterface.VodJSInterface;
import com.thunder.erp.jsinterface.WebViewJSInterface;
import com.thunder.erp.jsinterface.HttpBridgeJSInterface;
import com.thunder.erp.utils.UniqueIdUtil;
import com.thunder.erp.utils.NetworkUtil;
import com.thunder.erp.utils.AppUpgradeManager;
import com.thunder.erp.entity.AppUpgradeInfo;
import com.thunder.erp.http.ThunderHttpServer;

public class MainActivity extends AppCompatActivity {
    private static final boolean DEBUG = BuildConfig.DEBUG;
    private WebView webView;

    // HTTP服务器相关
    private ThunderHttpServer httpServer;
    private HttpBridgeJSInterface httpBridgeInterface;
    
    // 下载管理器
    private DownloadManager downloadManager;
    
    // 从BuildConfig中获取环境和URL配置
    private static final String BASE_URL = BuildConfig.BASE_URL; // 服务端API地址
    private static final boolean IS_PRODUCTION = BuildConfig.IS_PRODUCTION;
    private static final String CLIENT_TYPE = BuildConfig.CLIENT_TYPE;
    
    // 前端Web页面URL
    private static final String FRONTEND_DEV_URL = BuildConfig.FRONTEND_DEV_URL; // 开发环境前端URL
    private static final String FRONTEND_PROD_URL = BuildConfig.FRONTEND_PROD_URL; // 生产环境前端URL

    private boolean isFirstLoad = true;
    // 添加网络统计
    private int totalResourceRequests = 0;
    private int jsResourceRequests = 0;
    private int failedJsRequests = 0;
    private Map<String, String> resourceErrors = new HashMap<>();
    
    // 欢迎页面相关
    private FrameLayout welcomeScreen;
    private ImageView loadingIndicator;
    
    @SuppressLint({"HardwareIds", "MissingPermission"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 隐藏标题栏
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE);
        
        // 获取设备信息
        String deviceId = UniqueIdUtil.getUniqueId(this);
        // 使用NetworkUtil获取IP地址
        String ipAddress = NetworkUtil.getIPAddress(getApplicationContext());
        
        // 打印日志
        Log.d("MainActivity", "设备ID: " + deviceId);
        Log.d("MainActivity", "MAC地址（AndroidId): " + deviceId);
        Log.d("MainActivity", "IP地址: " + ipAddress);
        Log.d("MainActivity", "客户端类型: " + CLIENT_TYPE);
        Log.d("MainActivity", "服务端API地址: " + BASE_URL);
        Log.d("MainActivity", "前端开发环境URL: " + FRONTEND_DEV_URL);
        Log.d("MainActivity", "前端生产环境URL: " + FRONTEND_PROD_URL);
        Log.d("MainActivity", "应用包名: " + BuildConfig.APPLICATION_ID);
        Log.d("MainActivity", "App版本: " + BuildConfig.VERSION_NAME + " (" + BuildConfig.VERSION_CODE + ")");
        Log.d("MainActivity", "是否生产环境: " + IS_PRODUCTION);
        
        // 显示一个 Toast 以便验证当前使用的URL
        String currentUrl = IS_PRODUCTION ? FRONTEND_PROD_URL : FRONTEND_DEV_URL;
        Toast.makeText(this, "当前前端URL: " + currentUrl, Toast.LENGTH_LONG).show();
        
        setContentView(R.layout.activity_main);
        
        // 初始化欢迎页面
        welcomeScreen = findViewById(R.id.welcome_screen);
        loadingIndicator = findViewById(R.id.loading_indicator);
        // 设置loading图片旋转动画
        setupLoadingAnimation(loadingIndicator);
        
        // 初始化 WebView
        webView = findViewById(R.id.webview);
        setupWebView(webView, deviceId, ipAddress);
    }

    /**
     * 设置加载图标的旋转动画
     */
    private void setupLoadingAnimation(ImageView loadingView) {
        RotateAnimation rotateAnimation = new RotateAnimation(
            0, 360,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f
        );
        rotateAnimation.setDuration(1000);
        rotateAnimation.setRepeatCount(Animation.INFINITE);
        loadingView.startAnimation(rotateAnimation);
    }

    @Override
    public void onBackPressed() {
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 停止HTTP服务器
        stopHttpServer();

        // 清理WebView和JSBridge
        if (webView != null) {
            webView.destroy();
            webView = null;
            // 清理JSBridge
            JSBridge.init(null);
        }

        // 停止动画
        if (loadingIndicator != null) {
            loadingIndicator.clearAnimation();
        }
    }

    private void setupWebView(WebView webView, String deviceId, String ipAddress) {
        WebSettings settings = webView.getSettings();
        
        // 基本设置
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setAllowFileAccess(true);
        settings.setAllowFileAccessFromFileURLs(true);
        settings.setAllowUniversalAccessFromFileURLs(true);
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // 启用缓存
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        settings.setDatabaseEnabled(true);
        
        // 添加MIME类型映射，解决JS模块加载问题
        settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        settings.setAllowContentAccess(true);
        
        // 设置默认文本编码
        settings.setDefaultTextEncodingName("UTF-8");
        
        // 启用调试
        if (DEBUG) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        
        // 初始化JSBridge
        JSBridge.init(webView);
        
        // 添加WebChromeClient来处理JavaScript Console消息
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                String message = String.format("JavaScript %s: %s (at %s:%d)",
                        consoleMessage.messageLevel(),
                        consoleMessage.message(),
                        consoleMessage.sourceId(),
                        consoleMessage.lineNumber());
                
                switch (consoleMessage.messageLevel()) {
                    case ERROR:
                        Log.e("WebView", message);
                        break;
                    case WARNING:
                        Log.w("WebView", message);
                        break;
                    case DEBUG:
                        Log.d("WebView", message);
                        break;
                    default:
                        Log.i("WebView", message);
                        break;
                }
                return true;
            }
            
            @Override
            public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
                // 记录JavaScript警告
                Log.w("WebView", "JavaScript警告: " + message + " (来自 " + url + ")");
                return super.onJsAlert(view, url, message, result);
            }
        });

        // 简化 WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d("WebView", "开始加载页面: " + url);
                
                // 确保欢迎页面可见
                showWelcomeScreen();
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d("WebView", "页面加载完成: " + url);
                injectDeviceInfo(view, deviceId, ipAddress);
                
                // 每次页面加载完成后重新注入HTTP桥接脚本，确保功能可用
                Log.d("WebView", "重新注入HTTP桥接脚本");
                injectHttpBridgeScript(view);
                
                // 延迟测试HTTP桥接功能，确保脚本已执行完毕
                view.postDelayed(() -> {
                    Log.d("WebView", "测试HTTP桥接功能");
                    testHttpBridge(view);
                }, 1000);
                
                // 强制确保HttpBridge可用 - 多次重试机制
                view.postDelayed(() -> {
                    ensureHttpBridgeAvailable(view);
                }, 2000);
                
                // 记录资源统计信息
                logResourceStats();
                
                // 分析JS模块
                if (DEBUG) {
                    analyzeJsModules(view);
                }
                
                // 首次加载时清除token
                if (isFirstLoad) {
                    clearUserToken(view);
                    isFirstLoad = false;
                    
                    // 首次加载完成后，检查应用更新
                    checkAppUpgrade();
                }
                
                // 延迟一小段时间后隐藏欢迎页面，确保WebView已经渲染完成
                welcomeScreen.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        hideWelcomeScreen();
                    }
                }, 500); // 延迟500毫秒
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                Log.e("WebView", String.format("加载错误: %s\nURL: %s\n错误码: %d", 
                    error.getDescription(),
                    request.getUrl(),
                    error.getErrorCode()
                ));
                
                if (error.getErrorCode() == ERROR_CONNECT) {
                    view.loadUrl("javascript:alert('网络连接失败，请检查服务器是否正常运行')");
                }
            }
            
            @Override
            public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
                super.onReceivedHttpError(view, request, errorResponse);
                String url = request.getUrl().toString();
                Log.e("WebView", String.format("HTTP错误: URL=%s, 状态码=%d, 原因=%s", 
                    url, 
                    errorResponse.getStatusCode(), 
                    errorResponse.getReasonPhrase()
                ));
                
                // 特别记录JavaScript模块加载错误
                if (url.endsWith(".js")) {
                    failedJsRequests++;
                    String errorInfo = String.format("状态码=%d, 原因=%s", 
                        errorResponse.getStatusCode(), 
                        errorResponse.getReasonPhrase());
                    resourceErrors.put(url, errorInfo);
                    
                    Log.e("WebView", String.format("JavaScript模块加载失败: URL=%s, MIME类型=%s, 编码=%s", 
                        url, 
                        errorResponse.getMimeType(), 
                        errorResponse.getEncoding()
                    ));
                }
            }
            
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                String url = request.getUrl().toString();
                // 特别记录JavaScript模块加载请求
                if (url.endsWith(".js")) {
                    jsResourceRequests++;
                    totalResourceRequests++;
                    
                    Log.d("WebView", "加载JavaScript模块: " + url);
                    
                    // 记录请求头，可能包含重要信息
                    Map<String, String> headers = request.getRequestHeaders();
                    if (headers != null && !headers.isEmpty()) {
                        StringBuilder headerLog = new StringBuilder("请求头信息:\n");
                        for (Map.Entry<String, String> entry : headers.entrySet()) {
                            headerLog.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
                        }
                        Log.d("WebView", headerLog.toString());
                    }
                } else {
                    totalResourceRequests++;
                }
                return super.shouldInterceptRequest(view, request);
            }
            
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                Log.e("WebView", "SSL错误: " + error.toString());
                // 在开发环境中可以选择继续加载
                if (DEBUG) {
                    handler.proceed();
                    Log.w("WebView", "忽略SSL错误并继续加载");
                } else {
                    super.onReceivedSslError(view, handler, error);
                }
            }

            @Override
            public void onLoadResource(WebView view, String url) {
                super.onLoadResource(view, url);
                
                // 分析JavaScript请求的MIME类型问题
                if (url.endsWith(".js")) {
                    Log.d("WebView", "正在加载JavaScript资源: " + url);
                    
                    // 尝试诊断MIME类型问题
                    if (url.contains("?") || url.contains("#")) {
                        Log.w("WebView", "注意: JS URL包含查询参数或片段，可能影响MIME类型判断: " + url);
                    }
                }
            }
        });

        // 注册JavaScript接口
        webView.addJavascriptInterface(new PrinterJSInterface(this, webView), "AndroidPrinter");
        // 添加VOD接口
        webView.addJavascriptInterface(new VodJSInterface(this, webView), "vodBridge");
        // 添加WebView控制接口 - 根据环境选择前端URL
        String frontendUrl = IS_PRODUCTION ? FRONTEND_PROD_URL : FRONTEND_DEV_URL;
        webView.addJavascriptInterface(new WebViewJSInterface(this, webView, frontendUrl, deviceId, ipAddress), "webViewBridge");
        // 添加应用升级接口 - 使用服务端API地址
        webView.addJavascriptInterface(new AppUpgradeJSInterface(this, BASE_URL), "appUpgradeBridge");

        // 确保HTTP服务器已启动并创建接口
        if (httpServer == null) {
            startHttpServer();
        }
        
        // 添加HTTP桥接接口 - 确保服务器已启动
        if (httpBridgeInterface != null) {
            webView.addJavascriptInterface(httpBridgeInterface, "AndroidHttpBridge");
            Log.d("MainActivity", "AndroidHttpBridge接口已注册");
        } else {
            Log.e("MainActivity", "httpBridgeInterface为null，无法注册AndroidHttpBridge");
        }

        // 立即测试接口是否可用
        testAndroidHttpBridgeInterface(webView);

        // 强制创建基本的HttpBridge，确保至少有默认功能
        createBasicHttpBridge(webView);

        // 注入HTTP桥接脚本
        injectHttpBridgeScript(webView);

        // 添加JavaScript资源分析器
        if (DEBUG) {
            injectJsResourceAnalyzer(webView);
        }
        
        // 加载远程URL
        loadRemoteUrl(webView, deviceId, ipAddress);
    }

    /**
     * 显示欢迎页面
     */
    private void showWelcomeScreen() {
        if (welcomeScreen != null) {
            welcomeScreen.setVisibility(View.VISIBLE);
            // 确保动画在显示时启动
            if (loadingIndicator != null && loadingIndicator.getAnimation() == null) {
                setupLoadingAnimation(loadingIndicator);
            }
        }
    }
    
    /**
     * 隐藏欢迎页面
     */
    private void hideWelcomeScreen() {
        if (welcomeScreen != null) {
            welcomeScreen.setVisibility(View.GONE);
            // 停止动画
            if (loadingIndicator != null) {
                loadingIndicator.clearAnimation();
            }
        }
    }

    private void loadRemoteUrl(WebView webView, String deviceId, String ipAddress) {
        if (!IS_PRODUCTION) {
            // 在开发环境中，显示环境选择对话框
            showEnvironmentSelectionDialog(webView, deviceId, ipAddress);
        } else {
            // 在生产环境中，直接使用生产环境前端URL
            loadUrl(webView, deviceId, ipAddress, FRONTEND_PROD_URL);
        }
    }
    
    /**
     * 显示环境选择对话框
     * 在开发环境下可以选择不同的服务器地址
     */
    private void showEnvironmentSelectionDialog(final WebView webView, final String deviceId, final String ipAddress) {
        // 选项列表
        final String[] urls = new String[] { FRONTEND_DEV_URL, FRONTEND_PROD_URL };
        final String[] urlNames = new String[] {
            "开发环境 (" + getDomainFromUrl(FRONTEND_DEV_URL) + ")",
            "生产环境 (" + getDomainFromUrl(FRONTEND_PROD_URL) + ")"
        };
        
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择环境")
               .setItems(urlNames, (dialog, which) -> {
                    String selectedUrl = urls[which];
                    Toast.makeText(MainActivity.this, "已选择: " + urlNames[which], Toast.LENGTH_SHORT).show();

                    // 重新设置WebViewJSInterface，确保使用最新选择的URL
                    webView.removeJavascriptInterface("webViewBridge");
                    webView.addJavascriptInterface(new WebViewJSInterface(this, webView, selectedUrl, deviceId, ipAddress), "webViewBridge");
                    
                    loadUrl(webView, deviceId, ipAddress, selectedUrl);
               })
               .setCancelable(false) // 强制用户选择
               .show();
    }
    
    /**
     * 从URL中提取域名部分
     */
    private String getDomainFromUrl(String url) {
        Uri uri = Uri.parse(url);
        return uri.getHost();
    }
    
    /**
     * 加载指定URL
     */
    private void loadUrl(WebView webView, String deviceId, String ipAddress, String targetUrl) {
        final String clientType = com.thunder.erp.android.BuildConfig.CLIENT_TYPE;
        // 构建并加载URL
        Uri.Builder builder = Uri.parse(targetUrl).buildUpon();
        builder.appendQueryParameter("deviceId", deviceId)
               .appendQueryParameter("macAddress", deviceId)
               .appendQueryParameter("ipAddress", ipAddress)
               .appendQueryParameter("deviceType", clientType)
               .appendQueryParameter("clientType", clientType)
               .appendQueryParameter("appVersion", BuildConfig.VERSION_NAME)
               .appendQueryParameter("appVersionCode", String.valueOf(BuildConfig.VERSION_CODE));
        String fullUrl = builder.build().toString();

        Log.d("WebView", "加载远程URL: " + fullUrl);
        webView.loadUrl(fullUrl);
    }

    private void injectDeviceInfo(WebView view, String deviceId, String ipAddress) {
        String script = "javascript:" +
            "window.deviceInfo = {" +
            "  deviceId: '" + deviceId + "'," +
            "  ipAddress: '" + ipAddress + "'" +
            "};";
        view.evaluateJavascript(script, null);
    }
    
    private void clearUserToken(WebView view) {
        Log.d("WebView", "首次加载，清除用户token");
        String clearTokenScript = "javascript:" +
            "if (window.localStorage) {" +
            "  localStorage.removeItem('userToken');" +
            "  console.log('Android WebView 已清除 userToken');" +
            "}" +
            "if (typeof window.userStore !== 'undefined' && window.userStore.clearToken) {" +
            "  window.userStore.clearToken();" +
            "  console.log('Android WebView 已调用 userStore.clearToken()');" +
            "}";
        view.evaluateJavascript(clearTokenScript, value -> {
            Log.d("WebView", "清除token脚本执行完成: " + value);
        });
    }

    /**
     * 检查应用更新
     * 如果有强制更新，将自动弹出原生升级提示
     * 如果是普通更新，将由前端Vue代码触发
     */
    private void checkAppUpgrade() {
        Log.d("MainActivity", "自动检查应用更新");
        
        // 创建升级管理器，使用服务端API地址
        final AppUpgradeManager upgradeManager = new AppUpgradeManager(this, BASE_URL);
        
        // 设置升级回调
        upgradeManager.setUpgradeCallback(new AppUpgradeManager.UpgradeCallback() {
            @Override
            public void onCheckResult(boolean hasUpdate, AppUpgradeInfo upgradeInfo) {
                boolean isForceUpgrade = false;
                if (hasUpdate && upgradeInfo != null) {
                    isForceUpgrade = upgradeInfo.isForceUpgrade();
                }
                
                if (hasUpdate && isForceUpgrade) {
                    // 如果有强制更新，自动弹出升级提示
                    Log.d("MainActivity", "发现强制更新，自动弹出更新提示");
                    upgradeManager.startUpgradeProcess();
                } else if (hasUpdate) {
                    // 如果是普通更新，通知前端
                    Log.d("MainActivity", "发现普通更新，等待前端主动触发");
                    webView.post(() -> {
                        webView.evaluateJavascript(
                            "javascript:if(typeof window.onNativeUpdateAvailable === 'function'){" +
                            "window.onNativeUpdateAvailable(true);" +
                            "}else{" +
                            "console.log('前端未定义onNativeUpdateAvailable回调');" +
                            "}",
                            null
                        );
                    });
                } else {
                    Log.d("MainActivity", "没有可用更新");
                }
            }

            @Override
            public void onUpgradeStarted() {
                Log.d("MainActivity", "升级流程已开始");
            }

            @Override
            public void onUpgradeComplete() {
                Log.d("MainActivity", "升级流程已完成");
            }

            @Override
            public void onUpgradeFailed(String errorMessage) {
                Log.e("MainActivity", "升级失败: " + errorMessage);
            }
        });
        
        // 执行更新检查
        upgradeManager.checkUpgrade(false);
    }

    /**
     * 记录资源加载统计信息
     */
    private void logResourceStats() {
        Log.i("WebView", "======== 资源加载统计 ========");
        Log.i("WebView", "总请求数: " + totalResourceRequests);
        Log.i("WebView", "JS请求数: " + jsResourceRequests);
        Log.i("WebView", "JS失败数: " + failedJsRequests);
        
        if (!resourceErrors.isEmpty()) {
            Log.e("WebView", "资源加载错误列表:");
            for (Map.Entry<String, String> entry : resourceErrors.entrySet()) {
                Log.e("WebView", entry.getKey() + " => " + entry.getValue());
            }
        }
        
        // MIME类型相关分析
        if (failedJsRequests > 0) {
            Log.e("WebView", "JavaScript模块加载失败可能原因分析:");
            Log.e("WebView", "1. 服务器未正确设置.js文件的MIME类型（应为application/javascript）");
            Log.e("WebView", "2. 服务器返回了非JS内容（如HTML错误页或登录页）");
            Log.e("WebView", "3. 会话已过期或认证失败，导致重定向");
            Log.e("WebView", "4. 网络问题导致内容损坏");
        }
    }

    /**
     * 注入JavaScript资源分析器
     * 用于分析JavaScript模块加载问题
     */
    private void injectJsResourceAnalyzer(WebView webView) {
        String analyzerScript = "javascript:" +
            // 创建分析器对象
            "window.jsResourceAnalyzer = {" +
            "  loadedModules: {}," +
            "  failedModules: {}," +
            "  originalImport: window.import," +
            
            // 监控动态导入
            "  monitorImport: function() {" +
            "    if (!this.isMonitoring) {" +
            "      var self = this;" +
            "      window.import = function(moduleUrl) {" +
            "        console.log('正在动态导入模块: ' + moduleUrl);" +
            "        return self.originalImport.apply(window, arguments)" +
            "          .then(function(module) {" +
            "            self.loadedModules[moduleUrl] = true;" +
            "            console.log('模块加载成功: ' + moduleUrl);" +
            "            return module;" +
            "          })" +
            "          .catch(function(error) {" +
            "            self.failedModules[moduleUrl] = error.message;" +
            "            console.error('模块加载失败: ' + moduleUrl + ', 错误: ' + error.message);" +
            "            throw error;" +
            "          });" +
            "      };" +
            "      this.isMonitoring = true;" +
            "      console.log('JavaScript模块加载监控已启动');" +
            "    }" +
            "  }," +
            
            // 获取MIME类型分析
            "  detectMimeTypeIssues: function() {" +
            "    var scripts = document.querySelectorAll('script[type=\"module\"]');" +
            "    console.log('检测到 ' + scripts.length + ' 个ES模块脚本');" +
            "    for (var i = 0; i < scripts.length; i++) {" +
            "      var src = scripts[i].src;" +
            "      if (src) {" +
            "        console.log('模块脚本 #' + i + ': ' + src);" +
            "        fetch(src)" +
            "          .then(function(response) {" +
            "            console.log('模块响应: ' + src + ', 状态: ' + response.status + ', 类型: ' + response.headers.get('content-type'));" +
            "          })" +
            "          .catch(function(error) {" +
            "            console.error('模块获取失败: ' + src + ', 错误: ' + error.message);" +
            "          });" +
            "      }" +
            "    }" +
            "  }," +
            
            // 启动分析
            "  start: function() {" +
            "    this.monitorImport();" +
            "    // 页面加载完成后分析MIME类型问题" +
            "    var self = this;" +
            "    if (document.readyState === 'complete') {" +
            "      self.detectMimeTypeIssues();" +
            "    } else {" +
            "      window.addEventListener('load', function() {" +
            "        setTimeout(function() { self.detectMimeTypeIssues(); }, 500);" +
            "      });" +
            "    }" +
            "  }" +
            "};" +
            
            // 自动启动分析器
            "window.jsResourceAnalyzer.start();";
            
        webView.evaluateJavascript(analyzerScript, null);
        Log.d("WebView", "注入JS资源分析器");
    }
    
    /**
     * 在页面加载完成后启动JS模块分析
     */
    private void analyzeJsModules(WebView webView) {
        String analyzeScript = "javascript:" +
            "if (window.jsResourceAnalyzer) {" +
            "  window.jsResourceAnalyzer.detectMimeTypeIssues();" +
            "  var stats = {" +
            "    loadedModules: Object.keys(window.jsResourceAnalyzer.loadedModules).length," +
            "    failedModules: Object.keys(window.jsResourceAnalyzer.failedModules).length" +
            "  };" +
            "  JSON.stringify(stats);" +
            "} else {" +
            "  JSON.stringify({error: 'JS分析器未加载'});" +
            "}";
            
        webView.evaluateJavascript(analyzeScript, result -> {
            Log.i("WebView", "JS模块分析结果: " + result);
        });
    }

    /**
     * 启动HTTP服务器
     */
    private void startHttpServer() {
        try {
            Log.i("MainActivity", "正在启动HTTP服务器...");

            // 创建HTTP服务器实例
            httpServer = new ThunderHttpServer(8080);

            // 创建HTTP桥接接口
            httpBridgeInterface = new HttpBridgeJSInterface(httpServer);

            // 启动服务器
            httpServer.startServer();

            // 设置WebView引用（在WebView初始化后）
            if (webView != null) {
                httpServer.setWebView(webView);
            }

            Log.i("MainActivity", "HTTP服务器启动成功: http://localhost:8080");

            // 显示启动成功提示
            Toast.makeText(this, "HTTP服务器已启动: http://localhost:8080", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Log.e("MainActivity", "HTTP服务器启动失败", e);
            Toast.makeText(this, "HTTP服务器启动失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 停止HTTP服务器
     */
    private void stopHttpServer() {
        if (httpServer != null) {
            Log.i("MainActivity", "正在停止HTTP服务器...");

            try {
                httpServer.stopServer();
                Log.i("MainActivity", "HTTP服务器已停止");
            } catch (Exception e) {
                Log.e("MainActivity", "停止HTTP服务器时出错", e);
            } finally {
                httpServer = null;
                httpBridgeInterface = null;
            }
        }
    }

    /**
     * 注入HTTP桥接脚本
     */
    private void injectHttpBridgeScript(WebView webView) {
        Log.d("MainActivity", "开始注入HTTP桥接脚本");
        
        // 首先验证AndroidHttpBridge接口是否可用
        String checkScript = 
            "javascript:(function(){" +
            "try {" +
            "  if (typeof window.AndroidHttpBridge !== 'undefined') {" +
            "    console.log('[MainActivity] AndroidHttpBridge接口可用');" +
            "    return 'available';" +
            "  } else {" +
            "    console.error('[MainActivity] AndroidHttpBridge接口不可用');" +
            "    return 'not_available';" +
            "  }" +
            "} catch(e) {" +
            "  console.error('[MainActivity] 检查AndroidHttpBridge失败:', e);" +
            "  return 'error';" +
            "}" +
            "})();";
        
        webView.evaluateJavascript(checkScript, result -> {
            Log.d("MainActivity", "AndroidHttpBridge检查结果: " + result);
            
            if (result != null && result.contains("available")) {
                // 接口可用，注入完整脚本
                injectFullHttpBridgeScript(webView);
            } else {
                // 接口不可用，等待一下再重试
                Log.w("MainActivity", "AndroidHttpBridge不可用，500ms后重试");
                webView.postDelayed(() -> {
                    injectHttpBridgeScript(webView);
                }, 500);
            }
        });
    }
    
    /**
     * 注入完整的HTTP桥接脚本
     */
    private void injectFullHttpBridgeScript(WebView webView) {
        try {
            // 从assets读取HTTP桥接脚本
            InputStream inputStream = getAssets().open("android_http_bridge.js");
            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();

            String script = new String(buffer, "UTF-8");

            // 注入脚本
            webView.evaluateJavascript(script, result -> {
                Log.d("MainActivity", "HTTP桥接脚本注入完成，结果: " + result);
                
                // 延迟验证HttpBridge是否正确初始化
                webView.postDelayed(() -> {
                    verifyHttpBridge(webView);
                }, 500);
            });

        } catch (Exception e) {
            Log.e("MainActivity", "注入HTTP桥接脚本失败", e);
            
            // 如果脚本注入失败，尝试注入备用桥接脚本
            injectFallbackHttpBridge(webView);
        }
    }
    
    /**
     * 验证 HttpBridge 是否正确初始化
     */
    private void verifyHttpBridge(WebView webView) {
        String verifyScript = 
            "javascript:(function(){" +
            "try {" +
            "  console.log('[MainActivity] 开始验证 HttpBridge');" +
            "  var result = {" +
            "    exists: typeof window.HttpBridge !== 'undefined'," +
            "    hasHandleRequest: typeof window.HttpBridge?.handleRequest === 'function'," +
            "    status: window.HttpBridge?._checkStatus ? window.HttpBridge._checkStatus() : null" +
            "  };" +
            "  console.log('[MainActivity] HttpBridge验证结果:', result);" +
            "  JSON.stringify(result);" +
            "} catch(e) {" +
            "  console.error('[MainActivity] HttpBridge验证失败:', e);" +
            "  JSON.stringify({error: e.message});" +
            "}" +
            "})();";
        
        webView.evaluateJavascript(verifyScript, result -> {
            Log.d("MainActivity", "HttpBridge验证结果: " + result);
            
            try {
                // 如果验证失败，进行修复
                if (result != null && (result.contains("\"exists\":false") || result.contains("error"))) {
                    Log.w("MainActivity", "HttpBridge验证失败，尝试修复");
                    repairHttpBridge(webView);
                }
            } catch (Exception e) {
                Log.e("MainActivity", "处理验证结果失败", e);
            }
        });
    }
    
    /**
     * 修复 HttpBridge
     */
    private void repairHttpBridge(WebView webView) {
        Log.w("MainActivity", "开始修复 HttpBridge");
        
        // 先尝试重新注入完整脚本
        injectHttpBridgeScript(webView);
        
        // 如果还是失败，使用备用方案
        webView.postDelayed(() -> {
            injectFallbackHttpBridge(webView);
        }, 1000);
    }
    
    /**
     * 注入备用HTTP桥接脚本
     */
    private void injectFallbackHttpBridge(WebView webView) {
        Log.w("MainActivity", "使用备用HTTP桥接脚本");
        
        String fallbackScript = 
            "javascript:(function(){" +
            "console.log('[HTTP-BRIDGE-FALLBACK] 开始初始化备用桥接');" +
            "" +
            "if (typeof window.AndroidHttpBridge === 'undefined') {" +
            "  console.error('[HTTP-BRIDGE-FALLBACK] AndroidHttpBridge不可用');" +
            "  return;" +
            "}" +
            "" +
            "var fallbackRoutes = new Map();" +
            "" +
            "function sendResponse(requestId, statusCode, body) {" +
            "  try {" +
            "    var responseData = {" +
            "      requestId: requestId," +
            "      statusCode: statusCode || 200," +
            "      headers: {'Content-Type': 'application/json'}," +
            "      body: body || {message: 'HTTP桥接工作正常(备用模式)'}" +
            "    };" +
            "    window.AndroidHttpBridge.sendHttpResponse(requestId, JSON.stringify(responseData));" +
            "    console.log('[HTTP-BRIDGE-FALLBACK] 响应已发送:', requestId);" +
            "  } catch(error) {" +
            "    console.error('[HTTP-BRIDGE-FALLBACK] 发送响应失败:', error);" +
            "  }" +
            "}" +
            "" +
            "function handleFallbackRequest(requestData) {" +
            "  console.log('[HTTP-BRIDGE-FALLBACK] 收到请求:', requestData);" +
            "  " +
            "  try {" +
            "    var key = requestData.method + ':' + requestData.pathname;" +
            "    var handler = fallbackRoutes.get(key);" +
            "    " +
            "    if (handler) {" +
            "      console.log('[HTTP-BRIDGE-FALLBACK] 找到路由处理器:', key);" +
            "      try {" +
            "        var mockResponse = {" +
            "          json: function(data) { sendResponse(requestData.id, 200, data); }," +
            "          text: function(data) { sendResponse(requestData.id, 200, {text: data}); }," +
            "          error: function(code, msg) { sendResponse(requestData.id, code, {error: msg}); }" +
            "        };" +
            "        handler(requestData, mockResponse);" +
            "      } catch(e) {" +
            "        console.error('[HTTP-BRIDGE-FALLBACK] 处理器执行失败:', e);" +
            "        sendResponse(requestData.id, 500, {error: '处理器执行失败: ' + e.message});" +
            "      }" +
            "    } else {" +
            "      console.log('[HTTP-BRIDGE-FALLBACK] 路由未找到，使用默认响应');" +
            "      sendResponse(requestData.id, 200, {" +
            "        message: 'HTTP桥接工作正常(备用模式)'," +
            "        path: requestData.pathname," +
            "        method: requestData.method," +
            "        timestamp: Date.now()" +
            "      });" +
            "    }" +
            "  } catch(error) {" +
            "    console.error('[HTTP-BRIDGE-FALLBACK] 请求处理失败:', error);" +
            "    sendResponse(requestData.id, 500, {error: '请求处理失败: ' + error.message});" +
            "  }" +
            "}" +
            "" +
            "window.HttpBridge = {" +
            "  handleRequest: handleFallbackRequest," +
            "  post: function(path, handler) {" +
            "    fallbackRoutes.set('POST:' + path, handler);" +
            "    console.log('[HTTP-BRIDGE-FALLBACK] 注册POST路由:', path);" +
            "  }," +
            "  get: function(path, handler) {" +
            "    fallbackRoutes.set('GET:' + path, handler);" +
            "    console.log('[HTTP-BRIDGE-FALLBACK] 注册GET路由:', path);" +
            "  }," +
            "  route: function(method, path, handler) {" +
            "    fallbackRoutes.set(method.toUpperCase() + ':' + path, handler);" +
            "    console.log('[HTTP-BRIDGE-FALLBACK] 注册路由:', method, path);" +
            "  }," +
            "  _isFallback: true" +
            "};" +
            "" +
            "console.log('[HTTP-BRIDGE-FALLBACK] 备用桥接初始化完成');" +
            "})();";

        webView.evaluateJavascript(fallbackScript, result -> {
            Log.d("MainActivity", "备用HTTP桥接脚本注入完成: " + result);
            
            // 验证备用脚本
            webView.postDelayed(() -> {
                verifyHttpBridge(webView);
            }, 200);
        });
    }
    
    /**
     * 测试HTTP桥接功能
     */
    private void testHttpBridge(WebView webView) {
        Log.d("MainActivity", "开始测试HTTP桥接功能");
        
        String testScript = 
            "javascript:(function(){" +
            "try {" +
            "  if (window.HttpBridge && typeof window.HttpBridge.handleRequest === 'function') {" +
            "    console.log('[HTTP-BRIDGE-TEST] HttpBridge可用，开始测试');" +
            "    var testRequest = {" +
            "      id: 'test-' + Date.now()," +
            "      method: 'POST'," +
            "      pathname: '/test'," +
            "      headers: {}," +
            "      body: 'test data'" +
            "    };" +
            "    window.HttpBridge.handleRequest(testRequest);" +
            "    'HTTP桥接测试已发送';" +
            "  } else {" +
            "    console.error('[HTTP-BRIDGE-TEST] HttpBridge不可用');" +
            "    'HTTP桥接不可用';" +
            "  }" +
            "} catch(e) {" +
            "  console.error('[HTTP-BRIDGE-TEST] 测试失败:', e);" +
            "  'HTTP桥接测试失败: ' + e.message;" +
            "}" +
            "})();";
        
        webView.evaluateJavascript(testScript, result -> {
            Log.d("MainActivity", "HTTP桥接测试结果: " + result);
        });
    }

    /**
     * 强制确保HttpBridge可用 - 多次重试机制
     */
    private void ensureHttpBridgeAvailable(WebView webView) {
        ensureHttpBridgeAvailable(webView, 0);
    }
    
    private void ensureHttpBridgeAvailable(WebView webView, int retryCount) {
        if (retryCount >= 5) {
            Log.w("MainActivity", "HttpBridge重试5次仍不可用，停止重试");
            return;
        }
        
        Log.d("MainActivity", "HttpBridge确保可用性检查，第 " + (retryCount + 1) + " 次");
        
        String checkScript = 
            "javascript:(function(){" +
            "try {" +
            "  if (typeof window.HttpBridge !== 'undefined' && " +
            "      typeof window.HttpBridge.handleRequest === 'function') {" +
            "    return 'available';" +
            "  } else {" +
            "    return 'not_available';" +
            "  }" +
            "} catch(e) {" +
            "  return 'error: ' + e.message;" +
            "}" +
            "})();";
        
        webView.evaluateJavascript(checkScript, result -> {
            Log.d("MainActivity", "HttpBridge检查结果: " + result);
            
            if (result != null && result.contains("available")) {
                Log.i("MainActivity", "HttpBridge已可用");
            } else {
                Log.w("MainActivity", "HttpBridge不可用，尝试修复");
                
                // 强制注入备用脚本
                injectFallbackHttpBridge(webView);
                
                // 延迟后再次检查
                webView.postDelayed(() -> {
                    ensureHttpBridgeAvailable(webView, retryCount + 1);
                }, 1000);
            }
        });
    }

    /**
     * 测试AndroidHttpBridge接口是否正确注册
     */
    private void testAndroidHttpBridgeInterface(WebView webView) {
        Log.d("MainActivity", "测试AndroidHttpBridge接口");
        
        // 延迟执行测试，确保接口已经注册完成
        webView.postDelayed(() -> {
            String testScript = 
                "javascript:(function(){" +
                "try {" +
                "  console.log('[Interface-Test] 开始测试AndroidHttpBridge接口');" +
                "  if (typeof window.AndroidHttpBridge !== 'undefined') {" +
                "    console.log('[Interface-Test] AndroidHttpBridge接口存在');" +
                "    if (typeof window.AndroidHttpBridge.testConnection === 'function') {" +
                "      var result = window.AndroidHttpBridge.testConnection();" +
                "      console.log('[Interface-Test] testConnection结果:', result);" +
                "      return 'interface_available';" +
                "    } else {" +
                "      console.log('[Interface-Test] testConnection方法不存在');" +
                "      return 'method_missing';" +
                "    }" +
                "  } else {" +
                "    console.error('[Interface-Test] AndroidHttpBridge接口不存在');" +
                "    return 'interface_missing';" +
                "  }" +
                "} catch(e) {" +
                "  console.error('[Interface-Test] 测试失败:', e);" +
                "  return 'test_error: ' + e.message;" +
                "}" +
                "})();";
            
            webView.evaluateJavascript(testScript, result -> {
                Log.d("MainActivity", "AndroidHttpBridge接口测试结果: " + result);
                
                if (result != null && result.contains("interface_available")) {
                    Log.i("MainActivity", "AndroidHttpBridge接口正常");
                } else {
                    Log.e("MainActivity", "AndroidHttpBridge接口异常: " + result);
                    
                    // 如果接口有问题，尝试重新注册
                    retryRegisterHttpBridgeInterface(webView);
                }
            });
        }, 100);
    }
    
    /**
     * 重新注册HttpBridge接口
     */
    private void retryRegisterHttpBridgeInterface(WebView webView) {
        Log.w("MainActivity", "重新注册HttpBridge接口");
        
        // 移除旧接口（如果存在）
        try {
            webView.removeJavascriptInterface("AndroidHttpBridge");
        } catch (Exception e) {
            Log.d("MainActivity", "移除旧接口时出错: " + e.getMessage());
        }
        
        // 重新创建和注册接口
        if (httpServer != null) {
            httpBridgeInterface = new HttpBridgeJSInterface(httpServer);
            webView.addJavascriptInterface(httpBridgeInterface, "AndroidHttpBridge");
            Log.d("MainActivity", "AndroidHttpBridge接口已重新注册");
            
            // 再次测试
            webView.postDelayed(() -> {
                testAndroidHttpBridgeInterface(webView);
            }, 200);
        } else {
            Log.e("MainActivity", "httpServer为null，无法重新注册接口");
        }
    }

    /**
     * 创建基本的HttpBridge，确保即使脚本注入失败也有基本功能
     */
    private void createBasicHttpBridge(WebView webView) {
        Log.d("MainActivity", "创建基本HttpBridge");
        
        String basicBridgeScript = 
            "javascript:(function(){" +
            "console.log('[MainActivity] 创建基本HttpBridge');" +
            "" +
            "// 等待AndroidHttpBridge可用的函数" +
            "function waitForAndroidBridge(callback, retries) {" +
            "  retries = retries || 10;" +
            "  if (typeof window.AndroidHttpBridge !== 'undefined') {" +
            "    console.log('[MainActivity] AndroidHttpBridge已可用');" +
            "    callback();" +
            "  } else if (retries > 0) {" +
            "    console.log('[MainActivity] 等待AndroidHttpBridge，剩余重试次数:', retries);" +
            "    setTimeout(function() {" +
            "      waitForAndroidBridge(callback, retries - 1);" +
            "    }, 100);" +
            "  } else {" +
            "    console.error('[MainActivity] AndroidHttpBridge等待超时');" +
            "  }" +
            "}" +
            "" +
            "// 创建基本HttpBridge" +
            "function createHttpBridge() {" +
            "  if (typeof window.AndroidHttpBridge === 'undefined') {" +
            "    console.error('[MainActivity] AndroidHttpBridge不可用，无法创建HttpBridge');" +
            "    return;" +
            "  }" +
            "  " +
            "  window.HttpBridge = {" +
            "    handleRequest: function(requestData) {" +
            "      console.log('[MainActivity] BasicHttpBridge收到请求:', requestData);" +
            "      try {" +
            "        var response = {" +
            "          requestId: requestData.id," +
            "          statusCode: 200," +
            "          headers: {'Content-Type': 'application/json'}," +
            "          body: {" +
            "            message: 'HTTP桥接工作正常(基础模式)'," +
            "            path: requestData.pathname," +
            "            method: requestData.method," +
            "            timestamp: Date.now()" +
            "          }" +
            "        };" +
            "        window.AndroidHttpBridge.sendHttpResponse(" +
            "          requestData.id, JSON.stringify(response)" +
            "        );" +
            "        console.log('[MainActivity] BasicHttpBridge响应已发送:', requestData.id);" +
            "      } catch(error) {" +
            "        console.error('[MainActivity] BasicHttpBridge处理失败:', error);" +
            "      }" +
            "    }," +
            "    get: function(path, handler) { console.log('[BasicBridge] 注册GET:', path); }," +
            "    post: function(path, handler) { console.log('[BasicBridge] 注册POST:', path); }," +
            "    _isBasic: true" +
            "  };" +
            "  console.log('[MainActivity] 基本HttpBridge已创建');" +
            "}" +
            "" +
            "// 等待AndroidHttpBridge并创建HttpBridge" +
            "waitForAndroidBridge(createHttpBridge);" +
            "" +
            "// 返回状态信息" +
            "return 'BasicHttpBridge_Initialized';" +
            "})();";
        
        webView.evaluateJavascript(basicBridgeScript, result -> {
            Log.d("MainActivity", "基本HttpBridge创建完成: " + result);
            
            // 延迟验证是否创建成功
            webView.postDelayed(() -> {
                verifyBasicHttpBridge(webView);
            }, 300);
        });
    }

    /**
     * 验证基础HttpBridge是否创建成功
     */
    private void verifyBasicHttpBridge(WebView webView) {
        Log.d("MainActivity", "验证基础HttpBridge");
        
        String verifyScript = 
            "javascript:(function(){" +
            "try {" +
            "  console.log('[BasicBridge-Verify] 开始验证基础HttpBridge');" +
            "  if (typeof window.HttpBridge !== 'undefined') {" +
            "    console.log('[BasicBridge-Verify] HttpBridge存在');" +
            "    if (typeof window.HttpBridge.handleRequest === 'function') {" +
            "      console.log('[BasicBridge-Verify] handleRequest方法存在');" +
            "      if (window.HttpBridge._isBasic) {" +
            "        console.log('[BasicBridge-Verify] 这是基础HttpBridge');" +
            "        return 'basic_bridge_ok';" +
            "      } else {" +
            "        console.log('[BasicBridge-Verify] 这不是基础HttpBridge');" +
            "        return 'not_basic_bridge';" +
            "      }" +
            "    } else {" +
            "      console.log('[BasicBridge-Verify] handleRequest方法不存在');" +
            "      return 'no_handle_request';" +
            "    }" +
            "  } else {" +
            "    console.error('[BasicBridge-Verify] HttpBridge不存在');" +
            "    return 'no_http_bridge';" +
            "  }" +
            "} catch(e) {" +
            "  console.error('[BasicBridge-Verify] 验证失败:', e);" +
            "  return 'verify_error: ' + e.message;" +
            "}" +
            "})();";
        
        webView.evaluateJavascript(verifyScript, result -> {
            Log.d("MainActivity", "基础HttpBridge验证结果: " + result);
            
            if (result != null && result.contains("basic_bridge_ok")) {
                Log.i("MainActivity", "基础HttpBridge创建成功");
            } else {
                Log.w("MainActivity", "基础HttpBridge创建失败: " + result);
            }
        });
    }
}