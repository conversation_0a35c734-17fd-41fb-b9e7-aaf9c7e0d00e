plugins {
    id 'com.android.application'
}

// 添加本地属性文件读取
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

// 生成时间格式化函数，格式为年月日时分 (yyyyMMddHHmm)
def getTimestamp() {
    def date = new Date()
    def formattedDate = date.format('yyyyMMddHHmm')
    return formattedDate
}

android {
    namespace 'com.thunder.erp.android'
    compileSdk 34

    // 添加签名配置
    signingConfigs {
        release {
            keyAlias localProperties.getProperty('signing.keyAlias')
            keyPassword localProperties.getProperty('signing.keyPassword')
            storeFile localProperties.getProperty('signing.storeFile') ? file(localProperties.getProperty('signing.storeFile')) : null
            storePassword localProperties.getProperty('signing.storePassword')
        }
    }

    defaultConfig {
        applicationId "com.thunder.erp.android"
        minSdk 24
        targetSdk 34

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // 默认值，防止有些变体组合没有定义
        buildConfigField "String", "CLIENT_TYPE", "\"CASHIER_ANDROID\""
        buildConfigField "String", "BASE_URL", "\"https://me.ktvsky.com\"" // 服务端API地址
        buildConfigField "boolean", "IS_PRODUCTION", "false"
        buildConfigField "String", "FRONTEND_DEV_URL", "\"https://merpdev-stage.ktvsky.com\"" // 前端开发环境URL
        buildConfigField "String", "FRONTEND_PROD_URL", "\"https://merp.ktvsky.com\"" // 前端生产环境URL
    }

    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    buildFeatures {
        buildConfig true
    }
    
    flavorDimensions = ['clientType', 'environment']
    
    productFlavors {
        // 客户端类型维度
        cashier {
            dimension 'clientType'
            applicationId "com.thunder.erp.android"
            manifestPlaceholders = [
                APP_NAME: "ERP收银端"
            ]
            buildConfigField "String", "CLIENT_TYPE", "\"CASHIER_ANDROID\""
            buildConfigField "String", "BASE_URL", "\"https://me.ktvsky.com\"" // 服务端API地址
            // 前端URL配置
            buildConfigField "String", "FRONTEND_DEV_URL", "\"https://merpdev-stage.ktvsky.com\""
            buildConfigField "String", "FRONTEND_PROD_URL", "\"https://merp.ktvsky.com\""
            
            // 版本信息配置
            versionCode = 4
            versionName = "1.0." + getTimestamp()
        }
        
        mobile {
            dimension 'clientType'
            applicationId "com.thunder.erp.mobileorder"
            manifestPlaceholders = [
                APP_NAME: "ERP移动点单"
            ]
            buildConfigField "String", "CLIENT_TYPE", "\"MOBILE_ORDER\""
            buildConfigField "String", "BASE_URL", "\"https://me.ktvsky.com\"" // 服务端API地址
            // 前端URL配置
            buildConfigField "String", "FRONTEND_DEV_URL", "\"https://merp-pad-pre.ktvsky.com\""
            buildConfigField "String", "FRONTEND_PROD_URL", "\"https://merp-pad-prod.ktvsky.com\""
            
            // 版本信息配置
            versionCode = 4
            versionName = "1.0." + getTimestamp()
        }
        
        // 环境维度 - 只区分开发版和正式版
        dev {
            dimension 'environment'
            buildConfigField "boolean", "IS_PRODUCTION", "false"
            signingConfig signingConfigs.release
            
            // 为开发版添加版本名后缀
            versionNameSuffix = "-dev"
            
            // 在MainActivity中根据IS_PRODUCTION判断使用哪个前端URL
        }
        
        prod {
            dimension 'environment'
            buildConfigField "boolean", "IS_PRODUCTION", "true"
            signingConfig signingConfigs.release
            
            // 在MainActivity中根据IS_PRODUCTION判断使用哪个前端URL
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.androidx.core
    implementation libs.android.material
    implementation libs.androidx.constraintlayout
    implementation libs.androidx.webkit
    implementation libs.squareup.okhttp
    implementation libs.google.gson
    
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
} 